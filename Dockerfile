FROM docker.intuit.com/oicp/standard/maven/amzn-maven-corretto11:latest AS build
# The following ARG and 2 LABEL are used by Jenkinsfile command
# to identify this intermediate container, for extraction of
# code coverage and other reported values.
ARG build
LABEL build=${build}
LABEL image=build
ARG MVN_SETTINGS=settings.xml
COPY .mvn /usr/src/.mvn
COPY aop /usr/src/aop
COPY app /usr/src/app
COPY batch /usr/src/batch
COPY package /usr/src/package
COPY common /usr/src/common
COPY entity /usr/src/entity
COPY dataaccess /usr/src/dataaccess
COPY worker /usr/src/worker
COPY provideradapter /usr/src/provideradapter
COPY core /usr/src/core
COPY report /usr/src/report
COPY eventconsumer /usr/src/eventconsumer
COPY eventpublisher /usr/src/eventpublisher
COPY project.properties /usr/src/project.properties
COPY rds /usr/src/rds
COPY integrations /usr/src/integrations
COPY telemetry /usr/src/telemetry
COPY localisation /usr/src/localisation
COPY connector /usr/src/connector
COPY graphql-client /usr/src/graphql-client
COPY pom.xml /usr/src/pom.xml
COPY ${MVN_SETTINGS} /usr/src/settings.xml
COPY idps_config /usr/src/idps_config
ENV CI=true
RUN sudo mvn -T 1C -f /usr/src/pom.xml -s /usr/src/settings.xml -e clean install -U -DexcludedGroups=componentTest

FROM docker.intuit.com/oicp/standard/java/amzn-corretto-jdk11:latest
ARG DOCKER_TAGS=latest
# ARG JIRA_PROJECT=https://jira.intuit.com/projects/<CHANGE_ME>
ARG DOCKER_IMAGE_NAME=docker.artifactory.a.intuit.com/appintgwkflw/wkflautomate/wkflatmnsvc/service/wkflatmnsvc:${DOCKER_TAGS}
ARG SERVICE_LINK=https://devportal.intuit.com/app/dp/resource/7065039507767760447

LABEL maintainer=<EMAIL> \
      app=wkflatmnsvc \
      app-scope=runtime \
      build=${build}

# Switch to root for installation and some other operations
USER root

COPY --from=build /usr/src/app/target/was-app.jar /app/was-app.jar
RUN chmod 644 /app/was-app.jar

# Download latest contrast.jar
RUN curl -o /app/contrast/javaagent/contrast.jar https://artifact.intuit.com/artifactory/generic-local/dev/security/ssdlc/contrast/java/3e8e49de-a568-4847-82ac-9ddfc2b50036_latest/contrast.jar

COPY --from=build /usr/src/package/target/build.params.json /build.params.json
RUN chmod 644 /build.params.json

COPY --from=build /usr/src/package/entry.sh /app/entry.sh
RUN chmod 644 /app/entry.sh
# Import RDS certificate
COPY --from=build /usr/src/rds/rds-ca-2019-root.pem /etc/sslcerts/was-rds-ssl-cert
RUN chmod 644 /etc/sslcerts/was-rds-ssl-cert

COPY --from=build /usr/src/rds/AmazonRootCA1.pem /etc/sslcerts/was-rds-proxy-ssl-cert
RUN chmod 644 /etc/sslcerts/was-rds-proxy-ssl-cert

# This is a combined certificate made by concatenating AWS RDS CA global-bundle cert and AWS RDS Proxy AmazonRootCAs certificates.
# https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html
# https://www.amazontrust.com/repository/
# This certificates work for both connections RDS and via RDS Proxy as well.
COPY --from=build /usr/src/rds/combined-ssl-cert_rds_rds-proxy.pem /etc/sslcerts/combined-ssl-cert_rds_rds-proxy
RUN chmod 644 /etc/sslcerts/combined-ssl-cert_rds_rds-proxy

COPY --from=build /usr/src/idps_config /app/idps_config
RUN chmod 644 /app/idps_config/*

RUN mkdir /app/tmp
RUN chown appuser:appuser /app -R

# Remove unnecessary tools
RUN ["/home/<USER>/post_harden.sh"]

USER appuser
CMD ["/bin/sh", "/app/entry.sh"]
