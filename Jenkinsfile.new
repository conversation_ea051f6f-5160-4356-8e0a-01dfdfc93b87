#!groovy
// The library version is controlled from the Jenkins configuration
// To force a version add after lib '@' followed by the version.
@Library('msaas-shared-lib') _
import java.util.regex.Matcher
import java.util.regex.Pattern
import groovy.json.JsonOutput

automationBranch = "develop";
PR_OWNERS = new java.util.HashSet<String>();
PRs = new java.util.HashSet<String>()

node {
    // setup the global static configuration
    config = setupMsaasPipeline('msaas-config.yaml')
    config['alternate_prod_env_order'] = "migrate_env_order"
}

def sendEmail(subject,emailBody,to){
    emailext (
            subject: "${subject}",
            body: """
                      ${emailBody}
                """,
            to: "${to}"
    )
}

String envValue(){
   if("${params.environment}".isEmpty()){
        return "qal";
   }
   return "${params.environment}";
}

def deployCode(String environment, Integer gitTimeout=2, Integer appWaitTimeout=1700) {
    def envSla = environment + '-usw2-eks'
    def envVarEast = environment + 'b-use2-eks'
    def envVarWest = environment + 'b-usw2-eks'

    def message = "Workflow Automation Service Build deployed successfully"
    def messageFailed = "Workflow Automation Service Build deployment failed"
    def messageBody = "Environment:${environment} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
    def slackMsg = "${message} \n${messageBody}"

    if(environment == 'qal'){
           def envPrf = 'prf-usw2-eks'
           def envPrfb = 'prfb-usw2-eks'
           return [
                  'PRF-WEST': {},
                  'PRFB-WEST': {},
                  'QAL-WEST': {
                        stage('QAL-WEST-DEPLOY') {
                            if (getEnv(config, envSla).usw2.deploy) {
                             try{
                                    container('cdtools') {
                                        gitOpsDeploy(config, envSla, config.image_full_name, gitTimeout, appWaitTimeout)
                                    }
                                    messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                    slackMsg = "${message} \n${messageBody}"
                                    sendSlackNotification(slackMsg,"good")
                                }
                                catch(e){
                                    messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                    slackMsg = "${messageFailed} \n${messageBody}"
                                    sendSlackNotification(slackMsg,"bad")
                                    throw e
                                }
                            }
                        }
                    },
                  'QALB-EAST': {
                      stage('QALB-EAST-DEPLOY') {
                          if (getEnv(config, envVarEast).use2.deploy) {
                               try{
                                      container('cdtools') {
                                          gitOpsDeploy(config, envVarEast, config.image_full_name, gitTimeout, appWaitTimeout)
                                      }
                                      messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                      slackMsg = "${message} \n${messageBody}"
                                      sendSlackNotification(slackMsg,"good")
                                  }
                                  catch(e){
                                      messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                      slackMsg = "${messageFailed} \n${messageBody}"
                                      sendSlackNotification(slackMsg,"bad")
                                      throw e
                                  }
                          }
                      }
                  },
                  'QALB-WEST': {
                         stage('QALB-WEST-DEPLOY') {
                             if (getEnv(config, envVarWest).usw2.deploy) {
                                  try{
                                         container('cdtools') {
                                             gitOpsDeploy(config, envVarWest, config.image_full_name, gitTimeout, appWaitTimeout)
                                         }
                                         messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                         slackMsg = "${message} \n${messageBody}"
                                         sendSlackNotification(slackMsg,"good")
                                     }
                                     catch(e){
                                         messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                         slackMsg = "${messageFailed} \n${messageBody}"
                                         sendSlackNotification(slackMsg,"bad")
                                         throw e
                                     }
                             }
                         }
                     }
                  ]
           }
        // E2E and Prod Deployments
        return [
               'SLA-WEST': {
                     stage('SLA-WEST-DEPLOY') {
                         if (getEnv(config, envSla).usw2.deploy) {
                          try{
                                 container('cdtools') {
                                     gitOpsDeploy(config, envSla, config.image_full_name, gitTimeout, appWaitTimeout)
                                 }
                                 messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                 slackMsg = "${message} \n${messageBody}"
                                 sendSlackNotification(slackMsg,"good")
                             }
                             catch(e){
                                 message = "Workflow Automation Service Build deployment failed"
                                 messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                 slackMsg = "${message} \n${messageBody}"
                                 sendSlackNotification(slackMsg,"bad")
                                 throw e
                             }
                         }
                     }
                 },
               'SLB-EAST': {
                   stage('SLB-EAST-DEPLOY') {
                       if (getEnv(config, envVarEast).use2.deploy) {
                            try{
                                   container('cdtools') {
                                       gitOpsDeploy(config, envVarEast, config.image_full_name, gitTimeout, appWaitTimeout)
                                   }
                                   messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                   slackMsg = "${message} \n${messageBody}"
                                   sendSlackNotification(slackMsg,"good")
                               }
                               catch(e){
                                   messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                   slackMsg = "${messageFailed} \n${messageBody}"
                                   sendSlackNotification(slackMsg,"bad")
                                   throw e
                               }
                       }
                   }
               },
               'SLB-WEST': {
                      stage('SLB-WEST-DEPLOY') {
                          if (getEnv(config, envVarWest).usw2.deploy) {
                               try{
                                      container('cdtools') {
                                          gitOpsDeploy(config, envVarWest, config.image_full_name, gitTimeout, appWaitTimeout)
                                      }
                                      messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                      slackMsg = "${message} \n${messageBody}"
                                      sendSlackNotification(slackMsg,"good")
                                  }
                                  catch(e){
                                      messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                                      slackMsg = "${messageFailed} \n${messageBody}"
                                      sendSlackNotification(slackMsg,"bad")
                                      throw e
                                  }
                          }
                      }
                  }
               ]

}

def promoteCode(String envVar, Integer appWaitTimeout=1700) {
    def envSla = envVar + '-usw2-eks'
    def envVarEast = envVar + 'b-use2-eks'
    def envVarWest = envVar + 'b-usw2-eks'
    def message = "Workflow Automation Service Build Promoted successfully"
    def messageBody = "Environment:${envVar} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
    def slackMsg = "${message} \n${messageBody}"

    if(envVar == 'qal'){
       def envPrf = 'prf-usw2-eks'
       def envPrfb = 'prfb-usw2-eks'
       return [
           'PRF-PROMOTE': {},
           'PRFB-PROMOTE': {},
           'SLA-WEST-PROMOTE': {
                stage('SLA-WEST') {
                    if (getEnv(config, envSla).usw2.deploy) {
                        container('cdtools') {
                            gitOpsSync(config, envSla , appWaitTimeout)
                            gitOpsPromote(config, envSla, appWaitTimeout)
                            messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                            slackMsg = "${message} \n${messageBody}"
                            sendSlackNotification(slackMsg,"good")
                        }
                    }
                }
            },
           'SLB-EAST-PROMOTE': {
               stage('SLB-EAST') {
                   if (getEnv(config, envVarEast).use2.deploy) {
                       container('cdtools') {
                           gitOpsSync(config, envVarEast , appWaitTimeout)
                           gitOpsPromote(config, envVarEast, appWaitTimeout)
                           messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                           slackMsg = "${message} \n${messageBody}"
                           sendSlackNotification(slackMsg,"good")
                       }
                   }
               }
           },
              'SLB-WEST-PROMOTE': {
                stage('SLB-WEST') {
                    if (getEnv(config, envVarWest).usw2.deploy) {
                        container('cdtools') {
                            gitOpsSync(config, envVarWest , appWaitTimeout)
                            gitOpsPromote(config, envVarWest, appWaitTimeout)
                            messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                            slackMsg = "${message} \n${messageBody}"
                            sendSlackNotification(slackMsg,"good")
                        }
                    }
                }
            }
        ]
    }
    // For E2E and Prod Promotions
    return [
        'SLA-WEST-PROMOTE': {
             stage('SLA-WEST') {
                 if (getEnv(config, envSla).usw2.deploy) {
                     container('cdtools') {
                         gitOpsSync(config, envSla , appWaitTimeout)
                         gitOpsPromote(config, envSla, appWaitTimeout)
                         messageBody = "Environment:${envSla} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                         slackMsg = "${message} \n${messageBody}"
                         sendSlackNotification(slackMsg,"good")
                     }
                 }
             }
         },
        'SLB-EAST-PROMOTE': {
            stage('SLB-EAST') {
                if (getEnv(config, envVarEast).use2.deploy) {
                    container('cdtools') {
                        gitOpsSync(config, envVarEast , appWaitTimeout)
                        gitOpsPromote(config, envVarEast, appWaitTimeout)
                        messageBody = "Environment:${envVarEast} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                        slackMsg = "${message} \n${messageBody}"
                        sendSlackNotification(slackMsg,"good")
                    }
                }
            }
        },
           'SLB-WEST-PROMOTE': {
             stage('SLB-WEST') {
                 if (getEnv(config, envVarWest).usw2.deploy) {
                     container('cdtools') {
                         gitOpsSync(config, envVarWest , appWaitTimeout)
                         gitOpsPromote(config, envVarWest, appWaitTimeout)
                         messageBody = "Environment:${envVarWest} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
                         slackMsg = "${message} \n${messageBody}"
                         sendSlackNotification(slackMsg,"good")
                     }
                 }
             }
         }
     ]
}

// Checking if deployment is complete or not
def canaryCompleteStatus(String env, Integer appTimeout=3600){
    def envSla = env + '-usw2-eks'
    def envVarEast = env + 'b-use2-eks'
    def envVarWest = env + 'b-usw2-eks'

    return [
            'SLA-WEST': {
                stage('SLA-WEST') {
                    if (getEnv(config, envSla).usw2.deploy) {
                        sh 'echo Deploy status for SLA West'
                        withCredentials([
                                [
                                    $class       : 'StringBinding',
                                    credentialsId: "argocd-${config.service_name}",
                                    variable     : 'ARGOCD_AUTH_TOKEN'
                                ]
                        ]) {
                            withEnv([
                                    "ARGOCD_SERVER=${config.argocd_server}",
                                    'ARGOCD_OPTS= --insecure'
                            ]) {
                                script {
                                    container('cdtools') {
                                        gitOpsSync(config, envSla , appTimeout)
                                        def argoCdApplicationName = config.environments[envSla].namespace
                                        sh label: 'completed status SLA west', script: "argocd app wait ${argoCdApplicationName} --timeout ${appTimeout} --health --grpc-web"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            'SLB-WEST': {
                stage('SLB-WEST') {
                    if (getEnv(config, envVarWest).usw2.deploy) {
                        sh 'echo Deploy status for SLB-West'
                        withCredentials([
                                [
                                    $class       : 'StringBinding',
                                    credentialsId: "argocd-${config.service_name}",
                                    variable     : 'ARGOCD_AUTH_TOKEN'
                                ]
                        ]) {
                            withEnv([
                                    "ARGOCD_SERVER=${config.argocd_server}",
                                    'ARGOCD_OPTS= --insecure'
                            ]) {
                                script {
                                    container('cdtools') {
                                        gitOpsSync(config, envVarWest , appTimeout)
                                        def argoCdApplicationName = config.environments[envVarWest].namespace
                                        sh label: 'completed status SLB-west', script: "argocd app wait ${argoCdApplicationName} --timeout ${appTimeout} --health --grpc-web"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            'SLB-EAST': {
                stage('SLB-EAST') {
                    if (getEnv(config, envVarEast).use2.deploy) {
                        sh 'echo Deploy status for SLB-East'
                        withCredentials([
                                [
                                    $class       : 'StringBinding',
                                    credentialsId: "argocd-${config.service_name}",
                                    variable     : 'ARGOCD_AUTH_TOKEN'
                                ]
                        ]) {
                            withEnv([
                                    "ARGOCD_SERVER=${config.argocd_server}",
                                    'ARGOCD_OPTS= --insecure'
                            ]) {
                                script {
                                    container('cdtools') {
                                        gitOpsSync(config, envVarEast , appTimeout)
                                        def argoCdApplicationName = config.environments[envVarEast].namespace
                                        sh label: 'completed status SLB-east', script: "argocd app wait ${argoCdApplicationName} --timeout ${appTimeout} --health --grpc-web"
                                    }
                                }
                            }
                        }
                    }
                }
            }
    ]
}



// Signalling Argo-cd to Abort Build on Automation Failure
def abortBuild(String envVar, Integer appTimeout=300){
    def message = "Workflow Automation Service Build Aborted successfully"
    def messageBody = "Environment:${envVar} \nBuild Url: ${env.BUILD_URL} \nBRANCH: ${env.BRANCH_NAME} \nAUTHOR: ${AUTHOR} AUTHOR_EMAIL: ${AUTHOR_EMAIL}"
    def slackMsg = "${message} \n${messageBody}"
   try{
        withCredentials([
                [
                    $class       : 'StringBinding',
                    credentialsId: "argocd-${config.service_name}",
                    variable     : 'ARGOCD_AUTH_TOKEN'
                ]
        ]) {
            withEnv([
                    "ARGOCD_SERVER=${config.argocd_server}",
                    'ARGOCD_OPTS= --insecure'
            ]) {
                script {
                    container('cdtools') {
                        def argoCdApplicationName = config.environments[envVar].namespace
                        sh label: 'Abort Signalled', script: "argocd app actions run ${argoCdApplicationName} abort --kind Rollout --all"
                    }
                }
            }
        }

    }catch(Exception e){
        print "Error in Signalling Abort: ${e}"
        sendSlackNotification(slackMsg,"bad")
    }
}

/**
 *  Check if isOnlyPipelineChange.
 */
boolean isOnlyPipelineChange() {
    def getFileDetails = getFiles("${env.CHANGE_ID}","${WAS_REPO_API_URL}")
    if(null == getFileDetails){
        return false;
    }
    def otherChange = getFileDetails.collect{val -> val.get("filename")}
                 .findAll{ val -> (!val.contains('Jenkinsfile') && !val.contains('msaas-config')) };


    def jenkinsPipelineChange = getFileDetails.collect{val -> val.get("filename")}
                  .findAll{ val -> (val.contains('Jenkinsfile') || val.contains('msaas-config')) };

    echo("ignoreAutomationPRCheck:: Filtered List:: ${jenkinsPipelineChange}");
    return otherChange.size() == 0  && jenkinsPipelineChange.size() > 0;
}


// Method to run karate tests.
def karateTests(String envVar) {
    println env.CHANGE_TARGET
    if(envVar == 'pr-build'  && env.CHANGE_TARGET != 'master' && isOnlyPipelineChange()) {
        echo "skipping automation run."
        return [
            'SLA Tests': {
                stage('SLA Tests') {
                   echo "Skipping Automation - SLA"
                }
            },
            'SLB Tests': {
                stage('SLB Tests') {
                    echo "Skipping Automation - SLB"
                }
            }
        ]
    }

    checkoutTestsRepo(envVar)

    echo "Running karate tests!!"
    String slaEnv = envVar + "-usw2-eks"
    String slbEnv = envVar + "b-usw2-eks"

    if(envVar == 'pr-build'){
    def company_details = getCompanyDetails();
       // For Running Automation on PR build
        return [
                'SLA Tests': {
                    stage('SLA Tests') {
                           overwatchTests('maven', envVar, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -Plocal -Dcucumber.options='--tags ~@ignore-pr src/test/java/com/intuit/overwatch/tests/orchestrator-pr-build.feature' -Dusername=${company_details.username} -Dpassword=${company_details.password} -Dintuit_realmid=${company_details.companyId} -Dwas_appId=${WAS_SECRET_PRE_PROD_USR} -Dwas_appSecret=${WAS_SECRET_PRE_PROD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRE_PROD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRE_PROD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PRE_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PRE_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PRE_PROD}")
                    }
                },
                'SLB Tests': {
                    stage('SLB Tests') {
                           //sleep(time: 2, unit: "MINUTES")
                           overwatchTests('maven', envVar, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -Plocal -Dcucumber.options='src/test/java/com/intuit/overwatch/tests/orchestrator-slb.feature' -Dusername=${company_details.username} -Dpassword=${company_details.password} -Dintuit_realmid=${company_details.companyId} -Dwas_appId=${WAS_SECRET_PRE_PROD_USR} -Dwas_appSecret=${WAS_SECRET_PRE_PROD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRE_PROD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRE_PROD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PRE_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PRE_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PRE_PROD}")
                    }
                }
        ]
    }
    else if(envVar == 'qal' || envVar == 'e2e'){
       // For Running QAL and E2E Automations
        return [
                'SLA Tests': {
                    stage('SLA Tests') {
                           // running it only on e2e env
                           if(getEnv(config, slaEnv).usw2.karate){
                           overwatchTests('maven', slaEnv, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -P${envVar} -Dcucumber.options='src/test/java/com/intuit/overwatch/tests/orchestrator-sla.feature' -Dusername=${AUTOMATION_CREDS_USR} -Dpassword=${AUTOMATION_CREDS_PSW} -Dwas_appId=${WAS_SECRET_PRE_PROD_USR} -Dwas_appSecret=${WAS_SECRET_PRE_PROD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRE_PROD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRE_PROD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PRE_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PRE_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PRE_PROD}")
                        }
                    }
                },
                'SLB Tests': {
                    stage('SLB Tests') {
                       if(getEnv(config, slbEnv).usw2.karate){
                           sleep(time: 2, unit: "MINUTES")
                           overwatchTests('maven', slbEnv, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -P${envVar}b -Dcucumber.options='src/test/java/com/intuit/overwatch/tests/orchestrator-slb.feature' -Dusername=${AUTOMATION_CREDS_USR} -Dpassword=${AUTOMATION_CREDS_PSW} -Dwas_appId=${WAS_SECRET_PRE_PROD_USR} -Dwas_appSecret=${WAS_SECRET_PRE_PROD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRE_PROD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRE_PROD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PRE_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PRE_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PRE_PROD}")
                       }
                    }
                }
        ]
    }

    // For Running Prod Automations
     return [
            'SLA Tests': {
                stage('SLA Tests') {
                       if(getEnv(config, slaEnv).usw2.karate){
                       overwatchTests('maven', slaEnv, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -P${envVar} -Dcucumber.options='src/test/java/com/intuit/overwatch/tests/orchestrator-prd-sla.feature' -Dusername=${AUTOMATION_CREDS_PRD_USR} -Dpassword=${AUTOMATION_CREDS_PRD_PSW} -Dwas_appId=${WAS_SECRET_PRD_USR} -Dwas_appSecret=${WAS_SECRET_PRD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PROD}")
                    }
                }
            },
            'SLB Tests': {
                stage('SLB Tests') {
                   if(getEnv(config, slbEnv).usw2.karate){
                       //sleep(time: 2, unit: "MINUTES")
                       overwatchTests('maven', slbEnv, "mvn -s settings.xml -f workflows-automation-tests/pom.xml verify -P${envVar}b -Dcucumber.options='src/test/java/com/intuit/overwatch/tests/orchestrator-prd-slb.feature' -Dusername=${AUTOMATION_CREDS_PRD_USR} -Dpassword=${AUTOMATION_CREDS_PRD_PSW} -Dwas_appId=${WAS_SECRET_PRD_USR} -Dwas_appSecret=${WAS_SECRET_PRD_PSW} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PROD}")
                   }
                }
            }
        ]
}


/**
 * Validates Release Branch.
 * After validation, sets for pre-release and post-release config branches for raising and merging PRs.
 *
 **/
void validateAndSetReleaseBranch(){
    getVariables();
    def wasPrResponse  = getMergeDetails("${WAS_REPO_API_URL}","master");
    println wasPrResponse.head.ref
    def releaseBranch = validateConfigReleaseBranch("${WAS_CONFIG_API_REPO}", wasPrResponse.head.ref[0])
    if(null != releaseBranch) {
        /**
         * After fetching last release branch(wasPrResponse.head.ref[0]), "master-<date_time>"" merged on WAS master branch.
         * A validation is done by adding suffix "-pre-release-prd" config branch exists or not.
         * After validation,
         * workflowConfPreReleasePrd is set with releaseBranch.name, already suffixed with "-pre-release-prd"
         * workflowConfPostReleasePrd is set as wasPrResponse.head.ref[0] suffixed with "-post-release-prd"
         **/
         workflowConfPreReleasePrd = releaseBranch.name
         workflowConfPostReleasePrd = wasPrResponse.head.ref[0] + "-post-release-prd"
         echo "${workflowConfPreReleasePrd}"
         echo "${workflowConfPostReleasePrd}"
    } else {
        error "Release Branch not found."
    }
    saveAndBackupVariables("workflowConfPreReleasePrd", workflowConfPreReleasePrd);
    saveAndBackupVariables("workflowConfPostReleasePrd", workflowConfPostReleasePrd);
}

/**
 *  Calls Github Pulls GET API for closed state.
 */
def getMergeDetails(String repo, String branch){
    String getPRsUrl = repo + "/pulls?base="+branch+"&state=closed";
    return gitApiCall(getPRsUrl)
}

def sendSlackNotification(msg, msgColor) {
    sendSlackNotification(msg, msgColor,"${config.slack_channel}")
}

def sendSlackNotification(msg, msgColor,channelName) {
    def colorHex = '#000000'
    switch (msgColor) {
        case 'good':
            colorHex = '#00BB8A'
            break
        case 'warn':
            colorHex = '#FDAF2C'
            break
        case 'bad':
            colorHex = '#A80000'
            break
        case 'info':
            colorHex = '#0086FA'
            break
    }

    slackSend channel: "${channelName}", color: colorHex, failOnError: 'false',
            baseUrl: 'https://intuit-teams.slack.com/services/hooks/jenkins-ci/',
            message: "${msg}",
            tokenCredentialId: ' ',
            token: "${SLACK_TOKEN}"
}

def sendNotificationToAuthor(msg,color){
    def subject = "Workflow Automation Service ${msg}"
    def emailBody = "Job Status for '${env.JOB_NAME} [${env.BUILD_NUMBER}]': ${currentBuild.result}\n\nCheck console output at ${env.BUILD_URL}"
    def to = "${AUTHOR_EMAIL}"
    if("${currentBuild.result}" == 'null') {
        emailBody = "Job Status for '${env.JOB_NAME} [${env.BUILD_NUMBER}]': Start\n\nCheck console output at ${env.BUILD_URL}"
    }
    if (env.BRANCH_NAME == 'master' || env.BRANCH_NAME == 'develop' || changeRequest()) {
        if(env.BRANCH_NAME == 'master' && "${PR_OWNERS}" != null){
            to = "${PR_OWNERS}"
            echo "PR Owners: ${PR_OWNERS}"
            emailBody = emailBody + "\n\n PR Owners: ${PR_OWNERS}"
        }
        sendEmail(subject,emailBody,to)
    }
}


void sendFailureSlackNotification(msg, color){
    slackMsg = "Workflow Automation Service - ${msg}"
    slackMsg = slackMsg + "\nJob Status for '${env.JOB_NAME} [${env.BUILD_NUMBER}]': ${currentBuild.result}\n\nCheck console output at ${env.BUILD_URL}"
    slackMsg = slackMsg + "\nPR Owners: ${PR_OWNERS}"
    slackMsg = slackMsg + "\ncc: @workflow-oncall @nsehgal"
    sendSlackNotification(slackMsg, color)
}


void generateReport(String envName,String status) {

    def sendTo = "${config.ow_reports_sendTo};${AUTHOR_EMAIL}"
    def messageSuccess = "Overwatch Automation Tests passed successfully"
    def messageFailure = "Overwatch Automation Tests failed"
    def messageBody = "Environment :${envName} \nBuild Url : ${env.BUILD_URL} \nBRANCH : ${env.BRANCH_NAME} \nAUTHOR : ${AUTHOR} AUTHOR_EMAIL : ${AUTHOR_EMAIL} \n"
    def reportFile = "overview-features.html"

    def slackReport = "${messageBody} Find your reports here : ${env.BUILD_URL}cucumber-html-reports/${reportFile}"

    // Stop sending slack notifications on pr-build
    if(envName.contains("pr-build")){
         echo "Report Generated Successfully"
         return;
    }

    if ( status == 'good' ){
        sendSlackNotification("${messageSuccess} \n${slackReport}",status)
    } else if ( status == 'bad' ){
        sendSlackNotification("${messageFailure} \n${slackReport}",status)
        sendEmail("AUTOMATIONS FAILURE REPORT - WAS","${messageFailure} \n${slackReport}",sendTo)
    }
}

void checkoutTestsRepo( envVar) {
    def branchOW = "master"
    if ( envVar.contains("qal")){
        branchOW = "develop"
    }
    else if (envVar.contains("pr-build")){
        branchOW = "${automationBranch}"
    }

    container('maven') {
        withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', usernameVariable: 'GITHUB_USER', passwordVariable: 'GITHUB_TOKEN')]) {
            sh script: '''
                                        git config --global user.name "$GITHUB_USER"
                                        git config --global user.password "$GITHUB_TOKEN"
                                        git config --global user.email "$<EMAIL>"
                                        echo "https://$GITHUB_USER:$<EMAIL>" >> /tmp/gitcredfile
                                        git config --global credential.helper "store --file=/tmp/gitcredfile"
                                        ''', label: 'write git credentials'

            sh script: """#!/bin/bash
                                        echo "Automation Branch Used for running automation tests:: ${branchOW}"
                                        git clone https://github.intuit.com/appintgwkflw-wkflautomate/workflows-automation-tests --branch "${branchOW}" --single-branch workflows-automation-tests
                                        
                                        ls -ltrh workflows-automation-tests
                                        """, label: 'Clone workflows-automation-tests'
        }
    }
}


void overwatchTests(containerName, envName, command) {
    echo "ContainerName: ${containerName}; envName: ${envName}; command: ${command}"
    boolean hasOverwatchFailed = false;
    def newCommand = "${command}";
    sh "mkdir -p workflows-automation-tests/target/cucumber-html-reports"
    def cucumberReportDir = "workflows-automation-tests/target/cucumber-html-reports"
    container("${containerName}") {
        try {
             sh "${newCommand}"
             generateReport(envName,"good")
        } catch (err) {
            hasOverwatchFailed = true;
            echo "Overwatch Execution Failed: ${err}"
            generateReport(envName,"bad")
        } finally {
            if (fileExists("${cucumberReportDir}")) {
                echo "${cucumberReportDir} exist."
                def reportDir = "${env.WORKSPACE}/test/reports/${envName}"
                sh "mkdir -p ${reportDir} && cp -r ${cucumberReportDir} ${reportDir}"

                // Upload test reports to s3 instead of archiving in Jenkins to save space
                sh "sh ${WORKSPACE}/ibp_publish_to_s3.sh ${envName}"
                archiveArtifacts artifacts: "html_reports_in_s3.html", allowEmptyArchive: true
            }else {
                echo "${cucumberReportDir} does not exist. "
            }
            if (hasOverwatchFailed) {
                // Skipped Calling Abort for QAL env or local env(pr-build)
                if(!envName.contains("qal") && !envName.contains("pr-build")){
                    abortBuild(envName)
                }
                error("Overwatch Tests have Failed")
            }
            // Code below is archiving a cucumber report that has already been added to the build using other means above.
            // This report can be 1.6gb in size and is consuming the majority of available disk space within Jenkins.
            // There is no use-case for archiving the raw report files as they are not browsable in Jenkins other than in a raw, not rendered format.
        //archiveArtifacts artifacts: 'workflows-automation-tests/target/cucumber-html-reports/**/*',fingerprint: true
        }
    }
}

void fmeaTests(containerName,env,envName){

    def msgSuccess = "FMEA Automation Tests passed successfully"
    def msgFailure = "FMEA Automation Tests failed"
    def msgBody = "Environment :${envName} \nBuild URL : ${BUILD_URL} \nBRANCH : develop \nAUTHOR : ${AUTHOR} AUTHOR_EMAIL : ${AUTHOR_EMAIL}"
    def slackMessageSuccess = "${msgSuccess} \n${msgBody}"
    def slackMessageFailure = "${msgFailure} \n${msgBody}"
    boolean hasFmeaFailed = false;
    def branchFMEA = "develop"
    def flagFmea = "true"
    def execFile = "src/test/java/com/intuit/fmea/tests/orchestrator-fmea.feature"

    sh "curl -XPOST 'https://appintgwkflw-${env}-wkflatmnsvc-chaos.sbgqboppdusw2.iks2.a.intuit.com/actuator/chaosmonkey/enable'"

    container("${containerName}") {
        dir(envName) {
            git(url: 'https://github.intuit.com/appintgwkflw-wkflautomate/workflows-automation-tests.git', credentialsId: 'github-svc-sbseg-ci', branch: branchFMEA)

            try {
                sh "mvn -s settings.xml clean test -P${env} -Dcucumber.options=${execFile} -Dkarate.env=${env} -Dusername=${FMEA_CREDS_USR} -Dpassword=${FMEA_CREDS_PSW} -Dfmea_flag=${flagFmea} -Dservice_appId=${WAS_CLIENT_APP_SECRET_PRE_PROD_USR} -Dservice_appSecret=${WAS_CLIENT_APP_SECRET_PRE_PROD_PSW} -Doffline_ticket_username=${WAS_OFFLINE_SECRET_PRE_PROD_USR} -Doffline_ticket_password=${WAS_OFFLINE_SECRET_PRE_PROD_PSW} -Doffline_ticket_relamId=${WAS_OFFLINE_SECRET_REALM_PRE_PROD}"
                sendSlackNotification(slackMessageSuccess, "good")
            } catch (err) {
                hasFmeaFailed = true;
                echo "FMEA Execution Failed: ${err}"
                sendSlackNotification(slackMessageFailure, "bad")
            } finally {
                if (hasFmeaFailed) {
                    error("FMEA Tests have Failed")
                }
            }
        }
    }
}

// create release tag by calling github api.
def createRealeaseTag(repo,tag,tagName,tagBody) {
    echo "createRealeaseTag():: tag: ${tag}, tagName: ${tagName}, tagBody: ${tagBody}"
    def releaseTagUrl = repo + '/releases'
    def body = [
            tag_name: "${tag}",
            name: "${tagName}",
            target_commitish: "master",
            body: "${tagBody}"
    ]
    def bodyJson = JsonOutput.toJson(body)
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            createReleaseScript = '''
              curl --max-time 120 -X POST \
                -u $GITHUB_USER:$GITHUB_TOKEN \
                "''' + releaseTagUrl + '''" \
                -d \'''' + bodyJson + '''\'
            '''
            try {
                def response = sh(returnStdout: true, script: "${createReleaseScript}").trim()
                responseJson = readJSON text: response
                echo "TagUrl: ${responseJson.url}, TagId: ${responseJson.id}"
                if("${responseJson.url}".isEmpty() || "${responseJson.url}" == "null"){
                    error "Tag Creation Failed: ${responseJson}"
                }
                return responseJson
            }catch (Exception e) {
                error "Tag Creation Failed: ${e}"
            }
            return null
        }
    }
}

// Send Executing CR Message on #sbseg-adv-sitroom
def postCRDetails(namespace){
    try{
        if(this.fileExists("cr-${namespace}.json")) {
            sh "cat cr-${namespace}.json"
            def crMap = readJSON file: "cr-${namespace}.json"
            def changeId = "${crMap.result.change_sysid}"

            if("${changeId}" == null || "${changeId}".isEmpty()){
                changeId = "${crMap.result.crID}"
            }
            def CR_URL = "https://intuit.service-now.com/nav_to.do?uri=%2Fchange_request.do%3Fsys_id%3D${changeId}%26sysparm_stack%3D%26sysparm_view%3D"
            def CR_NUMBER = crMap.result.change_number
            def executingCRMsg = "Executing WAS release CR: <${CR_URL}|${CR_NUMBER}> \n\nPR Owners: ${PR_OWNERS}"
            sendSlackNotification(executingCRMsg,"good","#sbseg-adv-sitroom")
            sendSlackNotification(executingCRMsg + " \ncc: @workflow-oncall @bagarwal5 @nsehgal","good","#sbseg-qboav-dev")
            saveAndBackupVariables("CR_NUMBER",CR_NUMBER)
            saveAndBackupVariables("CR_URL", CR_URL)
        }
    }
    catch (Exception e) {
        print "Error in sending cr notification in slack channel: ${e}"
    }
}

/**
 *  validates the PR build to check if it contains Automation PR. If Automation PR is not found it fails the build.
    1. Fetches details of current PR.
    2. Checks if PR needs to be whitelisted from Automation Check.
    3. Fetches Automation LInk from PR description.
    4. Fetches the file details for Automation PR.
    5. If valid Automation passes the build.
 */

void validatePRBuild() {
    getVariables();
    whitelistCheck = false;
    automationBranch = "develop";
    def wasPrResponse = getPRDetails("${env.CHANGE_ID}","${WAS_REPO_API_URL}");
    if(null == wasPrResponse){
        error("validatePRBuild:: Error while fetch PR details")
        githubNotify context: 'Automation PR', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
    }
    automationPRLink = getPrLinkFromPRDescription(wasPrResponse.body);
    if (ignoreAutomationPRCheck() && null == automationPRLink) {
        echo "validatePRBuild:: Automation PR not needed"
        githubNotify context: 'Automation check', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Whitelisted', status: 'SUCCESS', targetUrl: env.RUN_DISPLAY_URL
        return;
    }
    else {
        echo "automationPRLink :: ${automationPRLink}"
        if(null != automationPRLink){
            validateAutomationPR(automationPRLink);
            saveAndBackupVariables('automationPRLink', automationPRLink);
        } else {
            whitelistCheck = true;
        }
    }
    saveAndBackupVariables('whitelistCheck', whitelistCheck);
    saveAndBackupVariables('automationBranch', automationBranch);
}

void whitelistCheck(){
    getVariables();
    echo("whitelistCheck :: ${whitelistCheck}");
    echo("automationBranch :: ${automationBranch}");
    echo("automationPRLink :: ${automationPRLink}");
    if(whitelistCheck && null==automationPRLink){
        try {
            whitelistJustification();
        }
        catch(err) {
            println err
            error("Whitelist approval denied");
        }
        try {
            whitelistApproval();
        }
        catch(err) {
            error("Justification Insufficient");
        }
    }
}

void whitelistJustification() {
    def justification = input(
        message: 'Do you want to proceed for whitelisting approval? ',
        parameters: [
            text(defaultValue: '', description: 'Give Justification', name: 'justification')
        ]
    )
    echo "Justification: ${justification}"
    def slackMessage = 'Please approve whitelisting of PR '
    def sendTo = "${config.whitelist_approval_sendTo}"
    def PR = "${REPO_PREFIX}/workflow-automation-service/pull/${env.CHANGE_ID}"
    slackMessage = slackMessage + "\n\nBuild URL : ${env.BUILD_URL}" + "\n\nPR : ${PR}" + "\n\nAUTHOR: ${AUTHOR_EMAIL}" + "\n\nJustification: ${justification}"
    sendEmail("Need whitelist approval", slackMessage, sendTo)
    sendSlackNotification(slackMessage + "\n\n cc: "+ config["whitelist_approval_notifyTo"],"good")
}

void whitelistApproval() {
    def userInput = input(
        message: 'Should this PR be whitelisted?',
        submitter: "${config.submitters}"
    )
}

/**
 * validates and checks the limit for No. of Files change.
 **/
void validatePRFilesLimit() {
    getVariables();
    def getFileDetails = getFiles("${env.CHANGE_ID}","${WAS_REPO_API_URL}")
    if(null == getFileDetails){
        error "Files not found."
    }
    def files_changed = getFileDetails.collect{val -> val.get("filename")}
                        .findAll{ val -> !(val.contains('/entity/') || val.contains('/test/') || val.contains('/exception/') || val.contains('/config/') || val.contains('/dataaccess/'))}
                        .findAll{ val -> val.contains('src/main/java/')}
                        .findAll{val -> val.endsWith(".java")};
    echo "files changed: ${files_changed.size()}"
    if(files_changed.size() <= config['PR_FILE_LIMIT']) {
        githubNotify context: 'Number of files changed', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Available', status: 'SUCCESS', targetUrl: env.RUN_DISPLAY_URL
    }else{
    githubNotify context: 'Number of files changed', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Invalid', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
        error("validatePRFiles:: File Limit Violation")
    }
}

void validateMasterPRBuild(){
    getVariables();
    def wasPrResponse = getPRDetails("${env.CHANGE_ID}","${WAS_REPO_API_URL}");
    if(null == wasPrResponse){
        error("validatePRBuild:: Error while fetch PR details")
        githubNotify context: 'Validate Release Branch', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
    }
    validateConfigReleaseBranch("${WAS_CONFIG_API_REPO}", wasPrResponse.head.ref)
}

@NonCPS
String validateReleaseBranchPattern(String headBranch){
    int CASE_INSENSITIVE = 0x02;
    Pattern releaseBranchPattern = Pattern.compile("master-([0-2][0-9]|3[0-1])-(0[1-9]|1[0-2])-2[3-9]_([0-1][0-9]|2[0-3])-([0-5][0-9])-([0-5][0-9])", CASE_INSENSITIVE);
    Matcher matcher = releaseBranchPattern.matcher(headBranch);
    return matcher.matches();
}


String validateConfigReleaseBranch(String configRepo, String headBranch){
    if(!validateReleaseBranchPattern(headBranch)){
        githubNotify context: 'Validate Release Branch', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Invalid PR, Cherrypick on Release Branch.', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
        error "PR not raised on release branch. Config Branch not found."
        return null;
    }
    container('maven') {
        def configBranchRepoUrl = configRepo +"/branches/" + headBranch +"-pre-release-prd"
        def responseJson
        configBranchRepoScript = '''
            curl --max-time 120 -X GET \
                  -u $GITHUB_USER:$GITHUB_TOKEN \
                  "''' + configBranchRepoUrl + '''"
        '''
        try {
            def response = sh(returnStdout: true, script: "${configBranchRepoScript}").trim()
            responseJson = readJSON text: response
            println responseJson
            if("${responseJson.name}".isEmpty() || "${responseJson.name}" == "null"){
                githubNotify context: 'Validate Release Branch', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Branch Not Found', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
                error "Config Branch Not Found: ${responseJson}"
            }
            githubNotify context: 'Validate Release Branch', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'SUCCESS.', status: 'SUCCESS', targetUrl: env.RUN_DISPLAY_URL
            return responseJson
        }catch (Exception e) {
            githubNotify context: 'Validate Release Branch', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: '${e}', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
            error "Config Branch Validation Failed: ${e}"
        }
        return null
    }
}


/**
 *  Get PR link from description.
 */
String getPrLinkFromPRDescription(String prDescription) {
    def url = null;
    int CASE_INSENSITIVE = 0x02;
    Pattern urlPattern = Pattern.compile("\\b(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]", CASE_INSENSITIVE);
    Matcher matcher = urlPattern.matcher(prDescription);
    while (matcher.find()) {
        url = matcher.group();
        if (url.startsWith("https://github.intuit.com/appintgwkflw-wkflautomate/workflows-automation-tests/pull/")) {
            echo "getPrLinkFromPRDescription :: ${url}"
            return url;
        }
    }
    return null;
}

/**
 *  Ignore Automation check if change is there only in exception,entity,config modules etc.
 */
boolean ignoreAutomationPRCheck() {
    def getFileDetails = getFiles("${env.CHANGE_ID}","${WAS_REPO_API_URL}")
    if(null == getFileDetails){
        return false;
    }
    def newList = getFileDetails.collect{val -> val.get("filename")}
                  .findAll{ val -> !(val.contains('/entity/') || val.contains('/test/') || val.contains('/exception/') || val.contains('/config/') ) }
                  .findAll{val -> val.endsWith(".java")};

    echo("ignoreAutomationPRCheck:: Filtered List:: ${newList}");
    return newList.size() == 0;
}

/**
 *  whitelist Automation PR by label.
 */
boolean whiteListPR(def prResponse) {
    boolean whitelist = false;
    prResponse.labels.each
            {
                 val ->
                      println val;
                      if(val.containsKey('name') && val.get('name') == "whitelist"){
                         whitelist = true;
                         return;
                        }
             }
    return whitelist;
}


/**
 *  Validate Automation PR.
 */
void validateAutomationPR(String prLink) {
    echo "prlink :: ${prLink}"
    if(null == prLink){
        githubNotify context: 'Automation Tests', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Missing', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
        error("validateAutomationPR:: Automation PR missing in the build")
    }

    def prNumber = prLink.tokenize('/').last();
    def automationPRresponse = getPRDetails(prNumber,"${WAS_AUTOMATION_REPO_API_URL}");
    if ("open".equals(automationPRresponse.state) && automationPRresponse.changed_files > 0
        && env.CHANGE_AUTHOR.equals(automationPRresponse.user.login)) {
            echo "validateAutomationPR:: Valid Automation PR Found.Proceeding with the build."
            echo "Automation Branch :: ${automationPRresponse.head.ref}"
            automationBranch = automationPRresponse.head.ref
            githubNotify context: 'Automation Tests', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Available', status: 'SUCCESS', targetUrl: env.RUN_DISPLAY_URL
            return;
    }
    githubNotify context: 'Automation Tests', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Invalid', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
    error("validateAutomationPR:: Automation PR Not Valid")
}

/**
 *  Get PR details by calling github api.
 */
def getPRDetails(String prNumber,String repo) {
    def getPRDetails = repo + '/pulls/'+ prNumber;
    echo "${getPRDetails}";
    return getDetails(getPRDetails);
}

/**
 *  Get File details of the PR via calling github API.
 */
def getFiles(prNumber, repo) {
    def getFilesUrl = repo + '/pulls/'+ prNumber + '/files?per_page=100';
    echo "${getFilesUrl}";
    return getDetails(getFilesUrl);
}

/**
 *  Calls Github GET API.
 */
def getDetails(String url){
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            getScript = '''
                curl --max-time 120 -X GET \
                       -H 'Content-Type: application/json' \
                       -H 'cache-control: no-cache' \
                       -u $GITHUB_USER:$GITHUB_TOKEN \
                       "''' + url + '''" \
            '''
            try {
                def response = sh(returnStdout: true, script: "${getScript}").trim()
                responseJson = readJSON text: response
                print "responseJson\n" + responseJson
                return responseJson
            }catch (Exception e) {
                print "getFiles:: Error in getting details: ${e}"
            }
            return null
        }
    }
}

/**
 *  Get company details per PR build.
 */
def getCompanyDetails() {
    def company_details = readJSON text:readFile('company_details.json').trim()
    def prHash= getPRHash();
    def company_detail = company_details[prHash];
    echo "prHash: ${prHash} ,RealmId: ${company_detail.companyId} ,Username: ${company_detail.username}"
    return company_detail;
}

/**
 *  Get PR Hash based on PR-number.
 */
def getPRHash() {
    def PrNumber = "${env.CHANGE_ID}";
    def number = PrNumber[-2..-1] as Integer;
    return number % 50;
}

def raiseAndMergePR(repoUrl,sourceBranch, destinationBranch, prTitle, prBody, merge, squash) {
    def prResponse = null
    try {
        prResponse = createPullRequest(sourceBranch, destinationBranch,repoUrl, prTitle, prBody)
        if ("${prResponse}" == 'null' || "${prResponse.number}" == 'null') {
            echo "Some exception occurred while raising PR}"
            error('PR creation stage - failed')
        }
        def fileSize = getFiles("${prResponse.number}",repoUrl);
        if (fileSize.size() == 0) {
            closePR("${prResponse.number}",repoUrl);
        }
        if("${merge}" == 'true') {
           echo "Merge PR."
           def mergeResponse = mergePullRequest(repoUrl, "${prResponse.number}", prTitle, prBody, squash)
            if ("${mergeResponse}" == 'null' || mergeResponse.merged == false) {
                echo "Some exception occurred while merging PR}"
                error('PR Merge failed')
            }
        }
        return prResponse.html_url
    }
    catch (Exception e) {
        echo "Exception occurred while raising PR : ${e}"
        if ("${prResponse}" != 'null' && "${prResponse.number}" != 'null') {
            closePR("${prResponse.number}",repoUrl);
        }
        error('Raise & Merge PR Operation failed')
    }
}

def createPullRequest(sourceBranch, destinationBranch, repo, prTitle, prBody) {
    echo "createPullRequest():: sourceBranch: ${sourceBranch}, destinationBranch: ${destinationBranch}"
    def pullUrl = repo + '/pulls'
    echo pullUrl
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            createPrScript = '''
                curl --max-time 1000 -X POST \
                      -H "Accept: application/vnd.github.v3+json" \
                      -H 'cache-control: no-cache' \
                      -u $GITHUB_USER:$GITHUB_TOKEN \
                      "''' + pullUrl + '''" \
                      -d '{
                            "title": "''' +  prTitle + '''",
                            "body": "''' +  prBody + '''",
                            "head": "''' +  sourceBranch + '''",
                            "base": "''' + destinationBranch + '''"
                        }'
            '''
            try {
                def response = sh(returnStdout: true, script: "${createPrScript}").trim()
                echo response
                responseJson = readJSON text: response
                echo "response.state: ${responseJson.number}, PR: ${responseJson.html_url}"
                return responseJson
            }catch (Exception e) {
                print "Error in creating pull request: ${e}"
            }
            return null
        }
    }
}

def mergePullRequest(repo, prNumber, prTitle, prBody, squash) {
    echo "mergePullRequest():: repo: ${repo}, prNumber: ${prNumber}"
    def mergePullUrl = repo + '/pulls/' + prNumber +"/merge"
    echo 'merge URl' + mergePullUrl
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            if (squash) {
                mergePrScript = '''
                    curl --max-time 120 -X PUT \
                      -H "Accept: application/vnd.github.v3+json" \
                      -H 'cache-control: no-cache' \
                      -u $GITHUB_USER:$GITHUB_TOKEN \
                      "''' + mergePullUrl + '''" \
                      -d '{
                            "commit_title": "''' +  prTitle + '''",
                            "commit_message": "''' +  prBody + '''",
                            "merge_method": "squash"
                       }'
                '''
            } else {
                mergePrScript = '''
                    curl --max-time 120 -X PUT \
                      -H "Accept: application/vnd.github.v3+json" \
                      -H 'cache-control: no-cache' \
                      -u $GITHUB_USER:$GITHUB_TOKEN \
                      "''' + mergePullUrl + '''" \
                      -d '{
                            "commit_title": "''' +  prTitle + '''",
                            "commit_message": "''' +  prBody + '''"
                       }'
                '''
            }

            try {
                def response = sh(returnStdout: true, script: "${mergePrScript}").trim()
                responseJson = readJSON text: response
                echo "response.state: ${responseJson.merged}, PR: ${responseJson.message}"
                return responseJson
            }catch (Exception e) {
                print "Error in creating pull request: ${e}"
                error "Merge PR failed."
            }
            return null
        }
    }
}

     void printTime() {
         def  prod_start_time = sh(script:'date +"%s"', returnStdout: true).trim()
         echo "${prod_start_time}"
         echo "print it."
     }


     void sleepTime() {
        getVariables();
        echo "prod_startTime :: ${prod_startTime}"
        echo "e2e_startTime :: ${e2e_startTime}"
        container('jnlp') {
            script{
                // E2E Start Time = Today 3.30 pm
                // PROD_E2E_GAP = 77400 = 21h 30m from E2E start time.
                // Prod Start Time = (E2E Start Time) + (PROD_E2E_GAP) = Next Day 1pm
                def e2e_complete_time = sh(script:'''date +"%s"''', returnStdout: true).trim()
                def e2e_time_taken = sh(script:"""expr ${e2e_complete_time} - ${e2e_startTime}""", returnStdout: true).trim()

                //There are 2 ways to find sleep time.
                //Approach 1:: Sleep Time = (Prod Start Time) - (E2E Complete Time)
                sleep_time = sh(script:"""expr ${prod_startTime} - ${e2e_complete_time}""", returnStdout: true).trim()
                //Approach 2:: Sleep Time = PROD_E2E_GAP - ((E2E Complete Time) - (E2E Start Time))
                // sleep_time = sh(script:"""expr ${PROD_E2E_GAP} - ${e2e_time_taken}""", returnStdout: true).trim()
            }
            saveAndBackupVariables("sleep_time", sleep_time)
            echo "sleep time: ${sleep_time}"
        }
    }

    void approvalOrWait(sleeptime) {
        getVariables();
        if (sleeptime.toInteger() > 0) {
            try {
                timeout(time: sleeptime, unit: "SECONDS") {
                    input 'Do you want to proceed to the deployment without wait?'
                }
            } catch(ex) {
                if (!'SYSTEM'.equals(ex.getCauses().get(0).getUser().getId())) {
                    println "Aborted by user, " + ex.getCauses().get(0).getUser()
                    throw ex;
                }
                println "userId :: " + ex.getCauses().get(0).getUser().getId()
                echo "Timeout expired, moving to Prod deployment."
            }
        }
    }

void configureBranch(){
   getVariables();
   branch = 'master'
   saveAndBackupVariables("branch", branch);
}

def saveVariable(key, value) {
    dir('backup') {
        sh "echo ${key}=\\\'${value}\\\' >> backup.groovy"
    }
}

def backupVariables() {
    dir('backup') {
        stash(name: 'backupStash', includes: 'backup.groovy')
    }
}

def saveAndBackupVariables(key, value) {
    saveVariable(key, value)
    backupVariables()
}

def getVariables() {
    try{
        dir('backup') {
            unstash 'backupStash'
            sh "cat backup.groovy"
            load "backup.groovy"
        }
        backupVariables();
    } catch(Exception ex) {
        println ex
    }
}


void configMerge(configBranch) {
    getVariables();
    echo "configBranch :: ${configBranch}"
    echo "workflowConfPreReleasePrd :: ${workflowConfPreReleasePrd}"
    echo "workflowConfPostReleasePrd :: ${workflowConfPostReleasePrd}"
    script{
        try{
            container('jnlp') {
                dir('config') {
                    withCredentials([gitUsernamePassword(credentialsId: 'github-svc-sbseg-ci')]) {
                        git(url: "${WAS_CONFIG_REPO}",credentialsId: "github-svc-sbseg-ci", branch: "${branch}")
                        sh '''
                            git config user.email "'"${GIT_USER_EMAIL}"'"
                            git config user.name "'"${GITHUB_USER}"'"
                        '''
                        sh "git branch --set-upstream-to=origin/${branch} ${branch}"
                        sh "git pull --rebase"
                        sh "git checkout ${configBranch}"
                        sh "git pull --rebase"
                        sh "git merge --no-commit --no-ff origin/${branch} || true"
                        sh '''
                            if [ "$(grep -lr '<<<<<<<' . | wc -l )" -gt 0 ]; then exit 42; fi
                        '''
                        sh "git commit -m '${jiraId} Merge Commit' || true"
                        sh "git push origin ${configBranch} || true"
                        sh "git pull --rebase"
                        script {
                            def diffFiles = sh(returnStdout: true, script: "git diff ${branch}..${configBranch} | grep \"^diff\" | wc -l").trim().toInteger()
                            println "diffFiles : ${diffFiles}"
                            if(diffFiles > 0) {
                                def prTitle = jiraId + " WAS Automation Release - " + date
                                def prBody = jiraId + " WAS Automation Release - " + date
                                configProdPR = raiseAndMergePR("${WAS_CONFIG_API_REPO}","${configBranch}","${branch}", prTitle, prBody, true, true)
                            }
                            else {
                                configProdPR = NO_CHANGES
                            }
                        }
                    }
                }
            }
        } catch(Exception ex){
            sendConflictNotification("${configBranch}");
            throw ex;
        }
    }
}

void prOwners() {
    getVariables();
    PR_OWNERS = new java.util.HashSet<String>();
    PRs = new java.util.HashSet<String>();

    container('jnlp') {
        script {
            def wasPrResponse  = getMergeDetails("${WAS_REPO_API_URL}","master");
            def releaseBranch = wasPrResponse.head.ref[0];
            println "releaseBranch :: " + releaseBranch;
            for (int i=0; i<wasPrResponse.head.ref.size();i++){
                String ref = wasPrResponse.head.ref[i];
                if(ref.equals(releaseBranch)){
                    PRs.add(wasPrResponse.html_url[i])
                    for (int j=1;j<250;j++){
                        def commitDetails = gitApiCall(wasPrResponse.commits_url[i], j);
                        def committers = commitDetails.commit.author.email
                        PR_OWNERS.addAll(committers);
                        if(committers.size() < 50) {
                            break;
                        }
                    }
                }
            }
            PR_OWNERS.remove("<EMAIL>");
            println "PR :: ${PRs}"
            println "PR Owners :: ${PR_OWNERS}"
        }
        saveAndBackupVariables("PR_OWNERS", PR_OWNERS);
        saveAndBackupVariables("PRs", PRs);

    }
}

void jiraTransition(){
    getVariables();
    script {
        jiraId = sh(script: "echo `git log -1 --pretty=oneline  | grep -oE '[A-Z][A-Z0-9]+-[0-9]+'`", returnStdout: true).trim()
        if (!"${jiraId}".isEmpty()) {
            writeFile file: 'jira-file.txt', text: "${jiraId}"
            transitionJiraTickets(config, "In Progress", "jira-file.txt")
        }
        else{
            print "JIRA ID not found in latest commit, skipping JIRA transition"
        }
    }
    saveAndBackupVariables("jiraId",jiraId);
}

void closeJira(){
    getVariables();
    script {
        jiraId = sh(script: "echo `git log -1 --pretty=oneline  | grep -oE '[A-Z][A-Z0-9]+-[0-9]+'`", returnStdout: true).trim()
        if (!"${jiraId}".isEmpty()) {
            writeFile file: 'jira-file.txt', text: "${jiraId}"
            transitionJiraTickets(config, "Closed", "jira-file.txt")
        }
        else{
            print "JIRA ID not found in latest commit, skipping JIRA transition"
        }
    }
}


def closePR(prNumber, repo) {
    def closePrUrl = repo + '/pulls/'+ prNumber
    def prTitle = jiraId + " Auto closed PR - " + date
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            def closed_status
            closePrScript = '''
                curl --max-time 120 -X PATCH \
                      -u $GITHUB_USER:$GITHUB_TOKEN \
                      "''' + closePrUrl + '''" \
                      -d '{
                             "title": "''' +  prTitle + '''",
                             "body": "PR Closed as no file change found",
                             "state": "closed"
                         }'
            '''
            try {
                def response = sh(returnStdout: true, script: "${closePrScript}").trim()
                responseJson = readJSON text: response
                if ("${responseJson.state}" == 'closed') {
                    echo "Auto PR closed ${prNumber}"
                    closed_status = true
                }
                else {
                    print "Auto PR not closed: ${prNumber} ${e}"
                    closed_status = false
                }
            }catch (Exception e) {
                print "Error in closing PR:${prNumber} ${e}"
                closed_status = false
            }
            return closed_status
        }
    }
}

void evaluateProdStartTime() {
    getVariables();
     container('jnlp') {
        script {
            //86400 = 24 * 60 * 60 = 1 day in seconds.
            //19800 = 5.30 hrs in sec. (GMT+5.30)
            prod_startTime = sh(script:'''
                    date_0h=`date +"%a %b %d 00:00:00 %Z %Y"`
                    epoch_0h=`date --date="\\`echo $date_0h\\`" +"%s"`
                    date_release_time=`date +"%a %b %d ${PROD_RELEASE_TIME}:00 %Z %Y"`
                    epoch_releaseTime_GMT0h=`date --date="\\`echo $date_release_time\\`" +"%s"`
                    epoch_releaseTime=`expr $epoch_releaseTime_GMT0h - $epoch_0h - 19800`
                    baking_period_days_in_sec=`expr ${PROD_E2E_BAKING_DAYS} \\* 86400`
                    epoch_prod_releaseTime=`expr $epoch_0h + $baking_period_days_in_sec + $epoch_releaseTime`
                    echo $epoch_prod_releaseTime
                ''', returnStdout: true).trim()

            release_Day = sh(script:"""
                echo `date -d @${prod_startTime} +"%a"`
                """, returnStdout: true).trim()

            prod_startTime_extend =  sh(script:"""
                #!/bin/bash
                if [ '${release_Day}' = 'Sat' ]; then
                    echo `expr 86400 \\* 2`
                elif [ '${release_Day}' = 'Sun' ]; then
                    echo 86400
                else
                    echo 0
                fi
            """, returnStdout: true).trim()


            prod_startTime =  sh(script:"""
                echo `expr ${prod_startTime} + ${prod_startTime_extend}`
            """, returnStdout: true).trim()

            sh """
                echo `date -d @${prod_startTime} +"%a %b %d %H:%M:%S %Z %Y"`
                echo ${prod_startTime}
            """

             e2e_startTime = sh(script:'''
                    echo `date +"%s"`
                ''', returnStdout: true).trim()
         }
         saveAndBackupVariables("prod_startTime", prod_startTime)
         saveAndBackupVariables("e2e_startTime", e2e_startTime)
     }
}

void evaluateNextProdStartTime() {
     getVariables();
     container('jnlp') {
        script {
            //86400 = 24 * 60 * 60 = 1 day in seconds.
            //19800 = 5.30 hrs in sec. (GMT+5.30)
            // Current datetime in epoch
            epoch_curr = sh(script:'''echo `date +"%s"`''', returnStdout: true).trim()
            echo "${epoch_curr}"
            // Current Day. eg, Mon, Tue, ... Fri
            curr_day = sh(script:'''echo `date +"%a"`''', returnStdout: true).trim()
            echo "${curr_day}"
            //No. of days in sec to increase from current date.
            deadline_extend = sh(script:"""
                #!/bin/bash
                if [ '${curr_day}' = 'Fri' ]; then
                    echo `expr 86400 \\* 3`
                elif [ '${curr_day}' = 'Sat' ]; then
                    echo `expr 86400 \\* 2`
                else
                    echo 86400
                fi
            """, returnStdout: true).trim()
            echo "${deadline_extend}"

           // Cut-off time post which no release will go for the day. Subtracted with 19800 (5.30 hr) to make UTC time equivalent to IST.
            date_deadline_time_IST = sh(script:"""
                deadline_time=`date +"%a %b %d ${DEADLINE_RELEASE_TIME}:00 %Z %Y"`
                epoch_deadline_time_UTC=`date --date="\\`echo \$deadline_time\\`" +"%s"`
                date_deadline_time_IST=`expr \$epoch_deadline_time_UTC - 19800`
                echo \$date_deadline_time_IST
            """, returnStdout: true).trim()
            echo "${date_deadline_time_IST}"

            // Calculated sleep time based on new release time evaluated and current date time in epoch.
            deadline_sleep_time = sh(script:"""
                if [ ${epoch_curr} -ge ${date_deadline_time_IST} ]; then
                    next_release_time=`date +"%a %b %d ${NEXT_RELEASE_TIME}:00 %Z %Y"`
                    next_release_time_epoch=`date --date="\\`echo \$next_release_time\\`" +"%s"`
                    release_time_epoch_next=`expr \$next_release_time_epoch + ${deadline_extend} - 19800`
                    deadline_sleep_time=`expr \$release_time_epoch_next - ${epoch_curr}`
                    echo \$deadline_sleep_time
                else
                    echo 0
                fi        
            """, returnStdout: true).trim()
            echo "test deadline"
            echo "${deadline_sleep_time}"
            saveAndBackupVariables("deadline_sleep_time", deadline_sleep_time)

        }
    }
}

def getOpenPRDetails(String repo, String destBranch, String sourceBranch){
    def getPRsUrl = repo + '/pulls'+"?base="+destBranch+"&head="+sourceBranch+"&state=open";
    return gitApiCall(getPRsUrl)
}

def closeExistingPR(String repo, String destBranch, String sourceBranch){
    def openPRs = getOpenPRDetails(repo, destBranch, sourceBranch)
    if(openPRs.size()>0){
        def pr = openPRs[0]
        echo "A release branch PR is still open\n" +
                "Auto closing the PR: ${pr.url}\n"
        closePR(pr.number, repo)
    }
}

void sendConflictNotification(configRepo){
    def playbookUrl = "https://wiki.intuit.com/display/QBOEN/Daily+Release+-+8th+May%2C+2023+Onwards#DailyRelease8thMay,2023Onwards-3.2.2.ProdDeployment(NextDay1pm)";
    def slackMsg = "Confict found while merging config branch. \nBranch: ${REPO_PREFIX}/workflow-automation-service-config/tree/${configRepo}"
    slackMsg =  slackMsg + "\n\nThis has no impact on Release CD Job.\nBuild URL: ${env.BUILD_URL}"
    slackMsg =  slackMsg + "\n\nConflict needs manual fix, please refer <${playbookUrl}|playbook> for steps to follow."
    slackMsg =  slackMsg +" \n\ncc: @workflow-oncall"
    sendSlackNotification(slackMsg,"bad")
}


void sendE2EDeploymentSuccess(){
    getVariables();
    script {
        def slackMessage = 'E2E deployment Success!!!.\nPlease update Prod Config Branch:'
        def WAS_CONFIG_PRD_PRE_RELEASE = "https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service-config/tree/${workflowConfPreReleasePrd}"
        def WAS_CONFIG_PRD_POST_RELEASE = "https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service-config/tree/${workflowConfPostReleasePrd}"
        slackMessage = slackMessage + "\nWAS Config Pre Release Prd: " + WAS_CONFIG_PRD_PRE_RELEASE
        slackMessage = slackMessage + "\nWAS Config Post Release Prd: " + WAS_CONFIG_PRD_POST_RELEASE
        slackMessage = slackMessage + "\n\nPR Owners: ${PR_OWNERS}"
        sendSlackNotification(slackMessage + "\n\ncc: @workflow-oncall","good")
        sendEmail("E2E Deployment Success!!!.", slackMessage, PR_OWNERS)
    }
}

void sendBuildFailureNotification(){
    sendNotificationToAuthor("Build Failed", "bad");
    if ("${env.BRANCH_NAME}" == 'master'){
        sendFailureSlackNotification("Build Failed", "bad");
    }
}

def generateTagBody() {
    getVariables();
    def tagBodyArr = []
    container('jnlp') {
        script {
            def wasPrResponse  = getMergeDetails("${WAS_REPO_API_URL}","master");
            def releaseBranch = wasPrResponse.head.ref[0];
            println "releaseBranch :: " + releaseBranch;
            for (int i=0; i<wasPrResponse.head.ref.size();i++){
                String ref = wasPrResponse.head.ref[i];
                if(ref.equals(releaseBranch)){
                    for (int j=1;j<250;j++){
                        def commitMsgArr = getTagMessages(wasPrResponse.commits_url[i], j)
                        tagBodyArr.addAll(commitMsgArr);
                        if(commitMsgArr.size() < 50) {
                            break;
                        }
                    }
                }
            }
            def tagBody = tagBodyArr.join("\n\n")
            return tagBody
        }
    }
}


def getTagMessages(url, page){
    def responseJson = gitApiCall(url,page)
    def result_arr = []
    for(int i=0; i< responseJson.commit.size(); i++){
        if(responseJson.commit[i].author.email != "<EMAIL>"){
            def msg = responseJson.commit[i].message +" | "+responseJson.commit[i].author.name+"\n"
            result_arr.add(msg)
        }
    }
    return result_arr
}

def gitApiCall(url, page=1){
    withCredentials([usernamePassword(credentialsId: 'github-svc-sbseg-ci', passwordVariable: 'GITHUB_TOKEN', usernameVariable: 'GITHUB_USER')]) {
        container('maven') {
            def responseJson
            getScript = '''
                curl --max-time 120 -X GET \
                       -H "Accept: application/vnd.github+json" \
                       -u $GITHUB_USER:$GITHUB_TOKEN \
                       "''' + url + '''?per_page=50&page='''+page+'''"
            '''
            try {
                def response = sh(returnStdout: true, script: "${getScript}").trim()
                responseJson = readJSON text: response
                return responseJson
            }catch (Exception e) {
                print "gitApiCallError:: Error fetching details from url :${url}: ${e}"
            }
            return null
        }
    }
}

/**
 * Fetches the jacoco exclusions string based on jacoco exclusions defined in report/pom.xml
 * @return
 */
def getExclusion() {
    container('cdtools') {
        def cmd = "cat report/pom.xml | xq -x 'project/build/plugins/plugin[./groupId[contains(text(),\"org.jacoco\")]]/configuration/excludes/*' | tr '\\n' ',' | sed 's/,\$/\\n/' | tr -d '\\n'"
        def exclusions = sh label: "Get jacoco exclusions from plugin configuration", script: cmd, returnStdout: true
        echo "Jacoco exclusion from plugin config : ${exclusions}"
        return exclusions
    }
}


/**
 * CR Creation.
 **/
void createCR(){
    getVariables();
    container('servicenow') {
        openSnowCR(config, "prd-usw2-eks", config.image_full_name, config.changeDescription)
        echo "WAS weekly Release CR created"
        postCRDetails(config.environments["prd-usw2-eks"].namespace)
        stash(name: 'CRStash', includes: "cr-${config.environments["prd-usw2-eks"].namespace}.json")
    }
}

/**
 *  Close CR
 **/
void closeCR(){
    getVariables()
    container('servicenow') {
        unstash 'CRStash'
        closeSnowCR(config, "prd-usw2-eks")
    }
}


void milestoneE2E(){
    getVariables();
    milestone(ordinal: 110, label: "Deploy-e2e-usw2-eks-milestone")
    milestone(ordinal: 120, label: "Deploy-e2eb-usw2-eks-milestone")
    milestone(ordinal: 130, label: "Deploy-e2eb-use2-eks-milestone")
}


void milestonePrd(){
    getVariables();
    milestone(ordinal: 160, label: "Deploy-prd-usw2-eks")
    milestone(ordinal: 170, label: "Deploy-prdb-usw2-eks")
    milestone(ordinal: 180, label: "Deploy-prdb-use2-eks")
}

def sendReleaseSummary(){
    getVariables();
    def currentDate = java.time.LocalDateTime.now();
    def prs = "${PRs}".replaceAll("[\\[\\]]", "").replace(",","\n")
    def prOwners = "$PR_OWNERS".replaceAll("[\\[\\]]", "")
    def CR_hyperlink = "<${CR_URL}|${CR_NUMBER}>"
    def contributors = prOwners.replace(",","\n").toUpperCase()

    def msg = "WAS Release Summary (${currentDate})\n"

    def slackMsg = msg + "\n\nPR URL(s):\n${prs} \n\nCR: ${CR_hyperlink}\n\nContributors:\n${contributors}"
    def emailBody = msg+ "\n\nPR URL(s):\n${prs} \n\nCR: ${CR_URL}\n\nContributors:\n${contributors}"

    sendEmail("WAS Release Summary", emailBody, prOwners+",<EMAIL>, <EMAIL>, <EMAIL>" )
    sendSlackNotification(slackMsg+"\n cc: @Sandeep @bagarwal5", "good", "#was-build-report")

}

pipeline {

    agent {
        kubernetes {
            label "${config.pod_label}"
            yamlFile 'KubernetesPods.yaml'
        }
    }

    options {
      preserveStashes()
      // https://intuit-teams.slack.com/archives/C0534K8PXD5/p1681814801255699
      buildDiscarder(logRotator(daysToKeepStr: '7'))
    }

    environment {
        CODECOV_TOKEN="b5f2999c-1618-423c-98ea-a8717bb2f900"
        IBP_MAVEN_SETTINGS_FILE = credentials("ibp-maven-settings-file")
        AUTHOR = sh(returnStdout: true, script: 'git --no-pager show -s --format=\'%an\' $GIT_COMMIT')
        AUTHOR_EMAIL = sh(returnStdout: true, script: 'git --no-pager show -s --format=\'%ae\' $GIT_COMMIT')
        AUTOMATION_CREDS = credentials("was-automation-creds")
        FMEA_CREDS = credentials("was-fmea-creds")
        WAS_SECRET_PRE_PROD = credentials("was-secret-pre-prod")
        WAS_CLIENT_APP_SECRET_PRE_PROD = credentials("workflow-appconnect-pre-prod-client-creds")
        WAS_OFFLINE_SECRET_PRE_PROD = credentials("was-overwatch-system-offline-user-pre-prod")
        WAS_OFFLINE_SECRET_REALM_PRE_PROD = credentials("was-overwatch-system-offline-realm-pre-prod")
        GIT_COMMIT_HASH = "${env.GIT_COMMIT}"
        API_URL_PREFIX = "https://github.intuit.com/api/v3/repos/appintgwkflw-wkflautomate"
        WAS_REPO = "https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service.git"
        WAS_REPO_API_URL = "${API_URL_PREFIX}/workflow-automation-service"
        WAS_AUTOMATION_REPO_API_URL = "${API_URL_PREFIX}/workflows-automation-tests"
        GITHUB_CREDENTIALS = credentials('github-svc-sbseg-ci')
        GITHUB_USER = "${env.GITHUB_CREDENTIALS_USR}"
        GITHUB_TOKEN = "${env.GITHUB_CREDENTIALS_PSW}"
        date = sh(script: 'echo `date +"%Y/%m/%d"`', returnStdout: true).trim()
        // prod secrets
        AUTOMATION_CREDS_PRD = credentials("was-automation-creds-prd")
        WAS_SECRET_PRD = credentials("was-secret-prd")
        WAS_CLIENT_APP_SECRET_PRD = credentials("was-client-secret-prd")
        WAS_OFFLINE_SECRET_PROD = credentials("was-overwatch-system-offline-user-prod")
        WAS_OFFLINE_SECRET_REALM_PROD = credentials("was-overwatch-system-offline-realm-prod")
        REPO_PREFIX = "https://github.intuit.com/appintgwkflw-wkflautomate"
        WAS_CONFIG_REPO = "${REPO_PREFIX}/workflow-automation-service-config.git"
        WAS_CONFIG_API_REPO = "${API_URL_PREFIX}/workflow-automation-service-config"
        GIT_USER_EMAIL = "<EMAIL>"
        environmentVal = envValue();
        GROUP_ID = "workflowAutomationService-"+"${env.CHANGE_ID}";
        // Used to upload and access large test reports from s3 instead of within Jenkins to save space
        // This bucket is hosted in the https://devportal.intuit.com/app/dp/resource/2739332065651237777
        // which is owned by the IBP team and has Cloudfront running in front of it
        S3_IAM_ROLE_ARN = "arn:aws:iam::646158002977:role/ibp-cicd-core-s3-bucket-access"
        S3_BUCKET_BASE_PATH = "ibp-cicd-core/ibp2_s3_upoads"
        S3_URL_BASE = "https://ibp-cicd-core.prod1-ibp.a.intuit.com/ibp2_s3_upoads"
        NO_CHANGES = "No changes, hence PR not created"
        BRANCH_CUT_HOUR = 10
        PROD_E2E_BAKING_DAYS = 1
        //IN 24h, HH:MM format. This is expected release time.
        PROD_RELEASE_TIME = "13:00"
        //In case deadline time has attained due to re-run of CD pipeline, release will go next day 10 am.
        NEXT_RELEASE_TIME = "10:00"
        // This is deadline time, release will not go after 5pm.
        DEADLINE_RELEASE_TIME = "17:00"
        SLACK_TOKEN = credentials("WAS-BUILD-SLACK-TOKEN")

    }

    post {
        always {
            sendMetrics(config)
        }
        unsuccessful {
            script {
                sendBuildFailureNotification();
            }
        }
    }

    stages {
        stage('Backup Variables') {
            when {
                allOf {
                    branch 'master'
                    not { changeRequest() }
                }
            }
            steps {
                configureBranch();
            }
        }
        stage('📙️ Identify PR Owners') {
            when {
                allOf {
                    branch 'master'
                    not { changeRequest() }

                }
            }
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                prOwners();
            }
        }
        stage('Jira In Progress') {
            when {
                allOf {
                    branch 'master'
                    expression { return config.enableJiraTransition }
                }
            }
            steps {
                jiraTransition();
            }
        }
        stage('Automation Check') {
            when {
                allOf {
                    changeRequest();
                    expression { return env.CHANGE_TARGET != 'master'}
                }
            }
            options {
                timeout(time: 2, unit: 'DAYS')
            }
            steps {
                script{
                    validatePRBuild()
                }
            }
        }
        stage('PR files Check') {
            when {
                allOf {
                    changeRequest();
                    expression { return env.CHANGE_TARGET != 'master'}
                }
            }
            steps {
                script{
                    validatePRFilesLimit()
                }
            }
        }
        stage('Validate Release Branch') {
            when {
                allOf {
                    changeRequest();
                    expression { return env.CHANGE_TARGET == 'master'}
                }
            }
            steps {
                script{
                    validateMasterPRBuild()
                }
            }
        }
        stage('BUILD:') {
            when { anyOf {
                branch 'develop'
                branch 'master';
                changeRequest() } }
            stages {
                stage('Restore Backup') {
                    steps{
                        getVariables();
                    }
                }
                stage('Podman Multi-Stage Build') {
                    steps {
                        container('podman') {
                            podmanBuild("--rm=false --build-arg=\"build=${env.BUILD_URL}\" -t ${config.image_full_name} .")

                            // copy from container the results of the build so we have all the reports.
                            // this is where Maven puts the results, for other builds change the path to copy.
                            podmanMount(podmanFindImage([image: 'build', build: env.BUILD_URL]),
                                    {steps,mount ->
                                        sh(label: 'copy outputs to workspace', script: "cp -r ${mount}/usr/src/app/target ${WORKSPACE}/target")
                                    })
                            podmanPush(config)
                        }
                    }
                }
                // Install the aws cli and jq into the maven container so it can
                // upload the test reports to s3. We do this here instead of part
                // or running the tests to avoid a race condition between multiple
                // parallel tests trying to do the install concurrently
                stage('Add awscli and jq to maven container') {
                    steps {
                        container('maven') {
                          sh '''
                              # Install the aws cli and jq on the maven container
                              apt-get update -y
                              apt-get install -y awscli jq
                          '''
                        }
                    }
                }
                stage('PR-Automation') {
                    when {
                        allOf {
                            changeRequest();
                            expression { return env.CHANGE_TARGET != 'master'}
                        }
                    }
                    post {
                        always {
                            containerLog name: 'camundapp'
                            containerLog name: 'postgres'
                            containerLog name: 'test'
                        }
                    }
                    stages {
                        stage('Spin Application') {
                            parallel {
                                stage('Initialize DB') {
                                    post {
                                        failure {
                                            githubNotify context: 'DB Initialization', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
                                        }
                                    }
                                    steps {
                                        script {
                                            container('postgres') {
                                                sh label: "wait for postgres", script: "while ! pg_isready ; do echo 'waiting for database to start'; sleep 1; done"
                                                sh "psql -V"
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"CREATE ROLE data_capture_role;\" "
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"CREATE ROLE rds_replication; \" "
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"GRANT rds_replication TO data_capture_role; \" "
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"GRANT CONNECT ON DATABASE \"camunda\" TO data_capture_role; \" "
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"CREATE SCHEMA IF NOT EXISTS was;\" "
                                                sh "psql -a -h localhost -p 5432 -U camundaapp -d camunda -c \"GRANT USAGE ON SCHEMA was TO data_capture_role; \" "
                                            }
                                        }
                                    }
                                }
                                stage('Setup SQS') {
                                    steps {
                                        script {
                                            container('maven') {
                                                sh "aws --region us-west-2 --endpoint-url=http://localhost:9324 sqs create-queue --queue-name overwatch-test-sqs"
                                                sh "aws --region us-west-2 --endpoint-url=http://localhost:9324 sqs list-queues"
                                            }
                                        }
                                    }
                                }
                                stage('WAS App') {
                                    post {
                                        failure {
                                            githubNotify context: 'WAS App Deployment', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
                                        }
                                    }
                                    steps {
                                        script {
                                            try {
                                                container('test') {
                                                    echo "Consumer Group Id : ${GROUP_ID}"
                                                    /**
                                                     * WAS has two pods coming up now one on port 8443(Pod1) and another on port 9443(Pod2).
                                                     * Pod 2 does the round trip required for automating eventing workflows like ttlive and qblive.
                                                     * Pod 1 produces on -> workflow-externaltask-assigned-test
                                                     * Pod 2 consumes on -> workflow-externaltask-assigned-test
                                                     * Pod 2 produces on -> workflow-externaltask-complete-test
                                                     * Pod 1 consumes on -> workflow-externaltask-complete-test and marks the task complete.
                                                     * Pod 2 does not do fetch and lock on overwatch topics.
                                                     */
                                                    sh '''
                                             sleep 20s
                                             java -jar -Dspring.profiles.active=default -Dspring.liquibase.user=camundaapp -Dspring.datasource.username=camundaapp -DexternalTask.workers.test.topicName=overwatch-test -DexternalTask.workers.test.workerId=overwatch-test-worker -DexternalTask.workers.test-worker-0.topicName=demo-offering-overwatch-test -DexternalTask.workers.overwatch-test-bkey__123456789.disable=false -Dworkflowcore.engine.host=http://localhost:8080 -Djsk.spring.config.idps.connection.api_secret_key=${WORKSPACE}/idps_config/key_v2-3fadea6d5cea5.pem -Devent.idpsconfig.api_secret_key=${WORKSPACE}/idps_config/key_v2-e3b373bd2284e.pem -Dserver.port=8443 -Devent.producer.entityTopicsMapping.externalTaskTest=workflow-externaltask-assigned-test -Devent.consumer.entityTopicsMapping.externalTask=workflow-externaltask-complete-test -Devent.consumer.enabledEntities.workflowTransitionEvents=true -DGROUP_ID=${GROUP_ID} -Ddisable_value=false -Dsqs_enabled=true ${WORKSPACE}/target/was-app.jar >> /proc/1/fd/1 &
                                             java -jar -Dspring.profiles.active=default -Dspring.liquibase.user=camundaapp -Dspring.datasource.username=camundaapp -Dworkflowcore.engine.host=http://localhost:8080 -Djsk.spring.config.idps.connection.api_secret_key=${WORKSPACE}/idps_config/key_v2-3fadea6d5cea5.pem -Devent.idpsconfig.api_secret_key=${WORKSPACE}/idps_config/key_v2-e3b373bd2284e.pem -Dserver.port=9443 -Dmanagement.server.port=9490  -Devent.producer.entityTopicsMapping.externalTaskTest=workflow-externaltask-complete-test -Devent.consumer.entityTopicsMapping.externalTaskTest=workflow-externaltask-assigned-test -DGROUP_ID=${GROUP_ID} ${WORKSPACE}/target/was-app.jar >> /proc/1/fd/1 &
                                          '''
                                                }
                                            } catch (ex) {
                                                unstable("Caught an error deploying WAS app")
                                            }
                                        }
                                    }
                                }
                                stage('Application Health check') {
                                    post {
                                        failure {
                                            githubNotify context: 'App Health Check', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
                                        }
                                    }
                                    steps {
                                        sh label: 'Health Check', script: 'curl http://localhost:8080/health/full -k -4 --retry 20 --retry-delay 15 --retry-connrefused'
                                        sh label: 'Health Check', script: 'curl https://localhost:8443/health/full -k -4 --retry 20 --retry-delay 15 --retry-connrefused'
                                        sh label: 'Health Check', script: 'curl https://localhost:9443/health/full -k -4 --retry 20 --retry-delay 15 --retry-connrefused'
                                    }
                                }
                            }
                        }

                        stage('PR Automation') {
                            post {
                                success {
                                    githubNotify context: 'Automation Tests', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Passed', status: 'SUCCESS', targetUrl: env.RUN_DISPLAY_URL
                                }
                                failure {
                                    githubNotify context: 'Automation Tests', credentialsId: 'github-svc-sbseg-ci', gitApiUrl: 'https://github.intuit.com/api/v3/', description: 'Failed', status: 'FAILURE', targetUrl: env.RUN_DISPLAY_URL
                                }
                            }
                            steps {
                                script {
                                    parallel karateTests("pr-build")
                                }
                            }
                        }
                    }
                }

                stage('Whitelist Check') {
                    when {
                        allOf {
                            changeRequest();
                            expression { return env.CHANGE_TARGET != 'master'}
                        }
                    }
                    options {
                        timeout(time: 2, unit: 'DAYS')
                    }
                    steps {
                        whitelistCheck();
                    }
                }
                stage('Code Scans') {
                    when {
                        expression { false }
                    }
                    parallel {
                        stage('Code Analysis') {
                            when { expression {return config.SonarQubeAnalysis} }
                            steps {
                                container('test') {
                                    echo 'Running static Code analysis: from JenkinsFile'
                                    reportSonarQube(config)
                                }
                            }
                        }

                        stage('Nexus IQ Server Scan') {
                            steps {
                                nexusPolicyEvaluation iqApplication: "${config.asset_id}", iqStage: "build"
                            }
                        }

                        stage('checkmarx') {
                            steps {
                                checkmarx(config)
                            }
                        }
                    }
                }
                stage('Publish') {
                    parallel {
                        stage('Report Coverage & Unit Test Results') {
                            steps {
                                junit '**/surefire-reports/**/*.xml'
                                jacoco(exclusionPattern: getExclusion())
                                sh '''
                                curl -Os https://uploader.codecov.io/latest/linux/codecov
                                chmod +x codecov
                                ./codecov -t ${CODECOV_TOKEN} -u https://codecov.tools.a.intuit.com -C ${GIT_COMMIT_HASH}
                                rm -f ./codecov || true
                                '''
                            }
                        }
                        stage('CPD Certification & Publish') {
                            steps {
                                container('cpd2') {
                                    intuitCPD2Podman(config, "-i ${config.image_full_name} --buildfile Dockerfile")
                                }
                            }
                        }
                    }
                }
                stage('Scorecard Check') {
                    when { expression {return config.enableScorecardReadinessCheck} }
                    steps {
                        scorecardPreprodReadiness(config, "e2e-usw2-eks")
                    }
                }
                stage('Deploy Artifacts to Nexus') {
                    when {
                        allOf { branch 'master'; not {changeRequest()} }
                    }
                    steps {
                        catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                            container('maven') {
                                sh "mvn -s ${IBP_MAVEN_SETTINGS_FILE} clean deploy -Dmaven.test.skip=true"
                            }
                        }
                    }
                }
            }
        }

        stage('PERF/QAL Milestones Prep'){
            when
                {
                    beforeOptions true
                    allOf { branch 'develop'; not {changeRequest()} }
                }
                steps {
                    milestone(ordinal: 60, label: "Deploy-prf-usw2-eks")
                    milestone(ordinal: 70, label: "Deploy-prfb-usw2-eks")
                    milestone(ordinal: 80, label: "Deploy-qalb-usw2-eks")
                    milestone(ordinal: 90, label: "Deploy-qal-usw2-eks")
                    milestone(ordinal: 100, label: "Deploy-qalb-use2-eks")
                }
        }

        stage('QAL and Perf : DEPLOY SLA & SLB') {
            when { allOf { branch 'develop'; not { changeRequest() } } }
            steps {
                script {
                    parallel deployCode("qal")
                }
            }
        }

        stage('Running Karate Automations') {
            when { allOf { branch 'develop'; not { changeRequest() } } }
            steps {
                script {
                    parallel karateTests("qal")
                }
            }
        }

        stage('QAL/PRF : PROMOTION') {
            when { allOf { branch 'develop'; not { changeRequest() } } }
            steps {
                script {
                    parallel promoteCode("qal")
                }
            }
        }

        stage('QAL/PRF : Canary Deployment Status Wait'){
            when { allOf { branch 'develop'; not { changeRequest() } } }
            steps {
                script {
                    parallel canaryCompleteStatus('qal')
                }
            }
        }

        stage('FMEA Tests on QAL-ENV') {
            options {
                timeout(time: 90, unit: 'MINUTES')
            }
            parallel {
                stage('FMEA Tests on QAL-A') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        fmeaTests('maven','qal','qal-usw2-eks');
                    }
                }
                stage('FMEA Tests on QAL-B') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        sleep(time: 3, unit: "MINUTES")
                        fmeaTests('maven','qalb','qalb-usw2-eks');
                    }
                }
            }
        }
        stage('Backup E2E StartTime') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
            }
            steps {
                evaluateProdStartTime();
            }
        }

        stage('Scorecard Check - E2E') {
            when {
                beforeOptions true
                allOf { branch 'master'; not {changeRequest()} }
            }

            steps {
                   getVariables();
                scorecardPreprodReadiness(config, "e2e-usw2-eks")
            }
        }
        stage('E2E Milestones Prep') {
            when {
                beforeOptions true
                allOf { branch 'master'; not {changeRequest()} }
            }
            steps {
                milestoneE2E();
            }
        }
        stage('Go Live - E2E') {
            when {
                // Checks when condition before options execution.
                // PR-Build were getting stuck on this stage waiting to acquire lock.
                beforeOptions true
                allOf {
                    branch 'master'
                    not { changeRequest() }
                }
            }
            options {
                //E2E Stage Lock.
                lock(resource: 'Stage - E2E', inversePrecedence: true)
            }
            stages {
                stage('E2E : Restore Backup') {
                    steps {
                        getVariables()
                    }
                }
                stage('E2E : DEPLOY SLA & SLB') {
                    when { allOf { branch 'master'; not { changeRequest() } } }
                    steps {
                        script {
                            sendSlackNotification("WAS - E2E Deployment Initiated.\nBuild URL: ${env.BUILD_URL}\ncc: @workflow-oncall", "good")
                            parallel deployCode("e2e",2,10200)

                        }
                    }
                }

                stage('E2E Automations') {
                    when { allOf { branch 'master'; not { changeRequest() } } }
                    steps {
                        script {
                            parallel karateTests("e2e")
                        }
                    }
                }

                stage('E2E : PROMOTION') {
                    when { allOf { branch 'master'; not { changeRequest() } } }
                    steps {
                        script {
                            parallel promoteCode("e2e",10200)
                        }
                    }
                }

                stage('E2E : Canary Deployment Status Wait'){
                    when { allOf { branch 'master'; not { changeRequest() } } }
                    steps {
                        script {
                            parallel canaryCompleteStatus('e2e',10200)
                        }
                    }
                }
            }
        }
        stage ('📙️ Identify Release Branch') {
            when {
                allOf {
                    branch 'master'
                    not { changeRequest() }
                }
            }
            steps {
                validateAndSetReleaseBranch();
            }
        }
        stage('🌤️ LOG: Details - Config Branch Cut (Prod)') {
            when {
                allOf {
                    branch 'master'
                    not { changeRequest() }
                }
            }
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                sendE2EDeploymentSuccess()
            }
        }
        stage('Go Live Prod - Approval/Wait') {
            when {
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
            }
            options {
                timeout(time: 21, unit: 'DAYS')
            }
            steps {
                sleepTime();
                approvalOrWait(sleep_time);
            }
        }
        stage('Check deadline - Go Live Prod') {
            when {
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
            }
            options {
                timeout(time: 21, unit: 'DAYS')
            }
            steps {
                evaluateNextProdStartTime();
                approvalOrWait(deadline_sleep_time);
            }
        }
        stage('Config Merge: Pre Release Production') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
            }
            agent none
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                // check for any open PRs before creating and merging PR
                closeExistingPR("${WAS_CONFIG_API_REPO}", "${branch}", "${workflowConfPreReleasePrd}")
                configMerge("${workflowConfPreReleasePrd}");
            }
        }
        stage('Scorecard Check - Prod') {
            when {
                // Checks when condition before options execution.
                // PR-Build were getting stuck on this stage waiting to acquire lock.
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()};
                    not {expression {return config.preprodOnly}}
                }
            }
            agent none
            options {
                timeout(time: 4, unit: 'HOURS')
            }
            steps {
                getVariables();
                sendSlackNotification("WAS - Scorecard check.\nKindly check the pipeline for manual approval if required.\nBuild URL: ${env.BUILD_URL}\ncc: @workflow-oncall", "good")
                scorecardProdReadiness(config, "prd-usw2-eks")
            }
        }
         // Prep stage
        stage('Prd Milestones Prep'){
            when
            {
                beforeOptions true
                allOf {
                    branch 'master';
                    not {changeRequest()}
                    not {expression {return config.preprodOnly}}
                }
            }
            agent none
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                milestonePrd();
            }
        }
        stage('Create CR') {
            when {
                beforeOptions true
                allOf {
                    branch 'master';
                    not {
                        changeRequest()
                    };
                    expression { return config.autoCreateCR }
                    not {expression {return config.preprodOnly}}
                }
            }
            agent none
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                createCR();
            }
        }
        stage('Go Live : Production') {
            when {
                // Checks when condition before options execution.
                // PR-Build were getting stuck on this stage waiting to acquire lock.
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()};
                    not {expression {return config.preprodOnly}}
                }
            }
            agent none
            options {
                timeout(time: 1, unit: 'DAYS')
                lock(resource: 'Stage - Prod')
            }
            stages {
                // Prod Deployment
                    stage('Prd : Restore Backup') {
                        steps {
                            getVariables()
                        }
                    }
                stage('Prd : DEPLOY SLA & SLB') {
                    steps {
                        script {
                            sendSlackNotification("WAS - Production Deployment Initiated.\nBuild URL: ${env.BUILD_URL}\ncc: @workflow-oncall", "good")
                            parallel deployCode("prd",2,46800)
                        }
                    }
                }

                // Prd Automation
                stage('Prd Automations') {
                    steps {
                        script {
                            parallel karateTests("prd")
                        }
                    }
                }

                // Prd Promotion(s)
                stage('Prd : PROMOTION') {
                    steps {
                        script {
                            parallel promoteCode("prd",46800)
                        }
                    }
                }

                stage('Prd : Canary Deployment Status Wait'){
                    steps {
                        script {
                            parallel canaryCompleteStatus('prd',46800)
                        }
                    }
                }
            }
        }
        stage('Config Merge: Post Release Production') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
            }
            options {
                timeout(time: 21, unit: 'MINUTES')
            }
            steps {
                // check for any open PRs before creating and merging PR
                closeExistingPR("${WAS_CONFIG_API_REPO}", "${branch}", "${workflowConfPostReleasePrd}")
                configMerge("${workflowConfPostReleasePrd}");
            }
        }
        stage('Close Jira') {
            when {
                allOf {
                    branch 'master';
                    expression { return config.enableJiraTransition }
                }
            }
            steps {
                closeJira();
            }
        }
        stage('Create Release Tag') {
            when {
                allOf { branch 'master'; not {changeRequest()} }
            }
            steps {
                container('jnlp') {
                    getVariables()
                    dir('was') {
                        withCredentials([gitUsernamePassword(credentialsId: 'github-svc-sbseg-ci')]) {
                            git(url: "${WAS_REPO}", credentialsId: "github-svc-sbseg-ci", branch: "develop")

                            script {

                                // script to get next tag ver
                                def tagScript = '''
                                echo `git tag -l --sort=-version:refname | head -1 | awk -F. -v OFS=. 'NF==1{print ++$NF}; NF>1{if(length($NF+1)>length($NF))$(NF-1)++; $NF=sprintf("%0*d", length($NF), ($NF+1)%(10^length($NF))); print}'`
                                '''
                                // tag value
                                def tag = sh(returnStdout: true, script: "${tagScript}").trim()

                                if("${tag}".isEmpty()){
                                    tag = "v1.0";
                                }
                                // script to fetch commit details and replace new line with \n
                                def tagBody =  generateTagBody()
                                // build tag name
                                def tagName = " WAS - " + tag + " - Release - " + date
                                //create release tag
                                createRealeaseTag("${WAS_REPO_API_URL}", tag, tagName, tagBody)
                            }

                        }
                    }
                }
            }
        }
        // If any failure, CR remains open and MUST be closed manually with cause.
        stage('Close CR') {
            when {
                allOf {
                    branch 'master';
                    not {
                        changeRequest()
                    };
                    expression { return config.autoCreateCR }
                }
            }
            steps {
                closeCR()
            }
        }
        stage('Release Summary'){
            when {
                allOf {
                    branch 'master';
                    not {
                        changeRequest()
                    };
                    expression { return config.autoCreateCR }
                }
            }
            steps{
                sendReleaseSummary()
            }
        }
    }
}

