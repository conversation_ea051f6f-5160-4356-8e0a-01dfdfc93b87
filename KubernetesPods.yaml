apiVersion: v1
kind: Pod
metadata:
  annotations:
    iam.amazonaws.com/role: arn:aws:iam::733536204770:role/qbo-prd-jenkins
  name: appintgwkflw-wkflautomate-wkflatmnsvc-service-template-v2
spec:
  containers:
  - name: jnlp
    image: docker.intuit.com/dev/build/ibp/jnlp-slave-with-docker:3.36-2_master_85
    tty: true
    args: ["$(JENKINS_SECRET)", "$(JENKINS_AGENT_NAME)", "-url", "$(JENKINS_URL)"]
  - name: maven
    image: 'docker.intuit.com/docker-rmt/maven:3.6-jdk-11'
    tty: true
    command: ["cat"]
  - name: cdtools
    image: docker.intuit.com/dev/deploy/cd/argocd-utils:stable
    tty: true
    command: ["cat"]
    alwaysPullImage: true
  - name: servicenow
    image: docker.intuit.com/coe/servicenow-cr-agent/service/servicenow-cr-agent:latest
    tty: true
    command: ["cat"]
    alwaysPullImage: true
  - name: test
    image: docker.intuit.com/docker-rmt/maven:3.6-jdk-11
    tty: true
    command: ["cat"]
    alwaysPullImage: true
  - name: podman
    image: docker.intuit.com/oicp/standard/podman:latest
    tty: true
    command: [ "cat" ]
    imagePullPolicy: Always
    volumeMounts:
    - name: container-storage
      mountPath: /var/lib/containers
    resources:
      limits:
        github.com/fuse: 1
    securityContext:
      capabilities:
        add: [ "SYS_ADMIN", "SYS_RESOURCE" ]
  - name: cpd2
    image: docker.intuit.com/oicp/standard/cpd2:1
    tty: true
    command: ["cat"]
    imagePullPolicy: Always
  - name: postgres
    image: docker.intuit.com/docker-rmt/postgres:13.4
    tty: true
    args: [ "postgres" ]
    alwaysPullImage: true
    env:
      - name: POSTGRES_DB
        value: "camunda"
      - name: POSTGRES_USER
        value: "camundaapp"
      - name: POSTGRES_PASSWORD
        value: "Intuit01"
  - name: componentpostgres
    image: docker.intuit.com/docker-rmt/postgres:13.4
    ports:
      - containerPort: 8806
      - containerPort: 8807
    tty: true
    args: [ "postgres" ]
    alwaysPullImage: true
    env:
      - name: POSTGRES_DB
        value: "camunda"
      - name: POSTGRES_USER
        value: "camundaapp"
      - name: POSTGRES_PASSWORD
        value: "Intuit01"
      - name: PGPORT
        value: "8806"
  - name: camundapp
    image: docker.artifactory.a.intuit.com/appintgwkflw/wkflautomate/camundaservice/service/camundaservice:master-ab40dc5
    ports:
     - containerPort: 8080
       name: service
    resources:
      requests:
        memory: "1400M"
    env:
      - name: "OPENTRACING_JAEGER_UDPSENDER_HOST"
        valueFrom:
         fieldRef:
          apiVersion: "v1"
          fieldPath: "status.hostIP"
      - name: "JAEGER_AGENT_PORT"
        value: "6831"  
      - name: APP_ENV
        value: "default"
      - name: JAVA_OPTS
        value: "-Djsk.spring.config.idps.connection.api_secret_key=/app/idps_config/key_v2-f1e7ef16e4c97.pem -Devent.idpsconfig.api_secret_key=/app/idps_config/key_v2-e3b373bd2284e.pem -DdbHostPrefix=******************************** -Dspring.datasource.username=camundaapp -Dspring.liquibase.user=camundaapp -Dmanagement.metrics.export.statsd.enabled=false -Devent.producer.enabled=true"
  - name: sqs
    image: docker.intuit.com/appintgwkflw/wkflautomate/wkflatmnsvc/service/sqs
    tty: true
    imagePullPolicy: Always
    ports:
      - containerPort: 9324
      - containerPort: 9325
  volumes:
  - name: container-storage
    emptyDir: {}
