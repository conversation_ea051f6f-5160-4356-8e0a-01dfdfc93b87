package com.intuit.appintgwkflw.wkflautomate.was.aop.annotations;

import com.intuit.appintgwkflw.wkflautomate.was.aop.aspects.TraceAspect;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Author: <PERSON><PERSON> Date: 18/12/19 Description: Utilize the annotation to mark a parameter/return
 * value to be utilize in either of aspects. Ref {@link Trace}, {@link TraceAspect}
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.TYPE_USE, ElementType.FIELD})
public @interface Tracer {

  /**
   * Provide a key name which gets store in log.
   *
   * @return
   */
  WASContextEnums key() default WASContextEnums.NONE;

  /**
   * Tell what is the kind of this field is ? True -> Object False -> String,Double etc or
   * primitives
   *
   * @return
   */
  boolean kind() default false;
}
