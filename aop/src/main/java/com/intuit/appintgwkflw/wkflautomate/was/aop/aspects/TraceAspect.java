package com.intuit.appintgwkflw.wkflautomate.was.aop.aspects;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.lang.reflect.AnnotatedType;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Optional;

/**
 * Author: <PERSON><PERSON> Date: 18/12/19 Description: Aspect for capturing TraceIds {@link Trace}
 * {@link Tracer}
 */
@Aspect
@Component
@Slf4j
public class TraceAspect {

  @Autowired private WASContextHandler contextHandler;

  /**
   * Fetch method argument and filter based on {@link Tracer} annotation and put its value in MDC
   * map
   *
   * @param joinPoint joinPoint
   */
  @Before("@annotation(com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace)")
  public void paramToMDCBefore(JoinPoint joinPoint) throws IllegalAccessException {

    Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
    Object[] args = joinPoint.getArgs();

    final Parameter[] parameters = method.getParameters();

    for (int x = 0; x < parameters.length; x++) {
      Parameter p = parameters[x];
      Object obj = args[x];
      if (p.isAnnotationPresent(Tracer.class)) {
        Tracer tracer = p.getAnnotation(Tracer.class);
        inject(tracer, obj, p.getType().getDeclaredFields());
      }
    }
  }

  @AfterReturning(
      pointcut = "@annotation(com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace)",
      returning = "returnValue")
  public void paramToMDCAfter(JoinPoint joinPoint, Object returnValue) {

    Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
    final AnnotatedType annotatedReturnType = method.getAnnotatedReturnType();
    Optional<Tracer> traceKey =
        Optional.ofNullable(annotatedReturnType.getAnnotation(Tracer.class));
    traceKey.ifPresent(
        anno -> {
          try {
            inject(anno, returnValue, method.getReturnType().getDeclaredFields());
          } catch (IllegalAccessException e) {
            log.error("Failed to insert key/value pair in log handler because of ", e);
          }
        });
  }

  /**
   * Inject values to log handler
   *
   * @param tracer tracer
   * @param type type
   * @param declaredFields declaredFields
   * @throws IllegalAccessException IllegalAccessException
   */
  private void inject(Tracer tracer, Object type, Field[] declaredFields)
      throws IllegalAccessException {
    if (!tracer.kind()) {
      contextHandler.addKey(tracer.key(), String.valueOf(type));
    } else {
      for (Field f : declaredFields) {
        f.setAccessible(true);
        final Tracer annotation = f.getAnnotation(Tracer.class);
        if (annotation != null) contextHandler.addKey(annotation.key(), String.valueOf(f.get(type)));
      }
    }
  }
}
