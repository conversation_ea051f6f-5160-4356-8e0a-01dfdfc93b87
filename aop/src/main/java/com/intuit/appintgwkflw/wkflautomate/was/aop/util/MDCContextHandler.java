package com.intuit.appintgwkflw.wkflautomate.was.aop.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/** Author: <PERSON><PERSON> Date: 19/12/19 Description: */
@Component
@Slf4j
public class MDCContextHandler implements WASContextHandler {
  /**
   * Add pair in threadLocal (MDC)
   *
   * @param key key to add in MDC
   * @param value value to add in MDC for {@code key}
   */
  @Override
  public void addKey(final WASContextEnums key, final String value) {
    MDC.put(key.getValue(), value);
  }

  @Override
  public void logTime(String method, long durationInMs) {
    log.info("Execution time for method: {}, is: {} ms", method, durationInMs);
  }

  /**
   * return value for given mdc key
   *
   * @param key input mdc key
   */
  @Override
  public String get(WASContextEnums key) {
    return MDC.get(key.getValue());
  }

  @Override
  public Map<String, String> getAll() {
    return Optional.ofNullable(MDC.getCopyOfContextMap()).orElse(Collections.emptyMap());
  }

  @Override
  public void clear() {
    MDC.clear();
  }
}
