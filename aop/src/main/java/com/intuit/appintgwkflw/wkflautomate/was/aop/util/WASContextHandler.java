package com.intuit.appintgwkflw.wkflautomate.was.aop.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import java.util.Map;

/**
 * Author: <PERSON><PERSON> Date: 19/12/19 Description:
 */
public interface WASContextHandler {

  void addKey(final WASContextEnums key, final String value);

  void logTime(String method, long durationInMs);

  String get(WASContextEnums key);

  Map<String, String> getAll();

  void clear();
}
