package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.DefinitionRbacService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.Provides;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.DefinitionOperations;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Provides(Definition.class)
@Component
@AllArgsConstructor
public class DefinitionProvider extends WASBaseProvider<Definition>
    implements DefinitionOperations<RequestContext> {

  private DefinitionService definitionService;
  private DefinitionRbacService definitionRbacService;

  @Override
  protected SingleResult<Definition> wasReadOne(
      RequestContext context, @SuppressWarnings("rawtypes") GlobalId id, QueryHelper query) {
    verifyRealmId(context.getAuthorization());

    // RBAC check for READ operation using existing DefinitionRbacService
    definitionRbacService.verifyReadAccess(id.getLocalId(), context.getAuthorization().getRealm());

    boolean isMultiStep =
        MultiStepUtil.isTemplateDataPassed(query.getPreparedQuery().getSubQueries());
    return SingleResult.of(
        definitionService.getDefinitionReadOne(id.getLocalId(), id, isMultiStep));
  }

  @Override
  protected ListResult<Definition> wasReadList(RequestContext context, QueryHelper query) {
    verifyRealmId(context.getAuthorization());

    // RBAC check at method level for READ LIST operation
    // Note: This is a basic check. For better performance, RBAC filtering should be done
    // at the service level to filter out definitions the user doesn't have access to.
    verifyReadListAccess(context.getAuthorization());

    return definitionService.getAllDefinitions(context.getAuthorization(), query);
  }

  @Override
  protected SingleResult<Definition> wasWriteOne(RequestContext context, Definition definition) {
    verifyRealmId(context.getAuthorization());
    verifyTestDriveRealmId(context.getAuthorization());
    CrudOperation operation = CrudOperation.CREATE;

    if (definition.isIdSet()) {
      // Unmask the definition id:
      // input if like Invoice-Approval$11$c60ee4e8-38f1-11ea-95fb-26a7576e130e
      // then convert it back to like Invoice-Approval:11:c60ee4e8-38f1-11ea-95fb-26a7576e130e
      String definitionId = WasUtils.unMaskDefinitionId(definition.getId().getLocalId());
      definition.setId(definition.getId().setLocalId(definitionId));
      operation = CrudOperation.UPDATE;
    }

    if (definition.isDeletedSet() && definition.isDeleted().booleanValue()) {
      operation = CrudOperation.DELETE;
    } else if (definition.isStatusSet()
        && WorkflowStatusEnum.ENABLED == definition.getStatus()
        && !definition.isWorkflowStepsSet()) {
      operation = CrudOperation.ENABLED;
    } else if (definition.isStatusSet()
        && WorkflowStatusEnum.DISABLED == definition.getStatus()
        && !definition.isWorkflowStepsSet()) {
      operation = CrudOperation.DISABLED;
    }

    // RBAC check at method level - verify access based on operation
    verifyWriteAccess(operation, definition, context.getAuthorization());

    switch (operation) {
      case DELETE:
        return SingleResult.of(
            definitionService.deleteDefinition(definition, context.getAuthorization()));

      case UPDATE:
        return SingleResult.of(
            definitionService.updateDefinition(definition, context.getAuthorization()));

      case ENABLED:
        return SingleResult.of(
            definitionService.enableDefinition(definition, context.getAuthorization()));

      case DISABLED:
        return SingleResult.of(
            definitionService.disableDefinition(definition, context.getAuthorization()));

      default:
      case CREATE:
        return SingleResult.of(
            definitionService.createDefinition(definition, context.getAuthorization()));
    }
  }

  @Override
  public SingleResult<Definition> createDefinitionWithDefaultValues(
      RequestContext context, Definition.TemplateInput templateInput) {
    WorkflowLogger.logInfo(
        "Creating definition with default values for template id=%s", templateInput.getName());
    populateMDCHeaders(context);
    return CommonExecutor.execute(
        () -> {
          verifyRealmId(context.getAuthorization());
          return new SingleResult<>(
              definitionService.createDefinitionWithDefaultValues(
                  templateInput.getName(),
                  BooleanUtils.toBoolean(templateInput.isCreatedAsSystemUser()),
                  context.getAuthorization()));
        },
        "Exception occurred while performing create definiton with default values",
        e -> handleServiceException((WorkflowGeneralException) e),
        e1 -> handleSystemError(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  /**
   * Reading Definition with obfuscated/redacted values
   *
   * @param context
   * @param queryHelper
   * @return
   */
  @Override
  public ListResult<Definition> getDefinitionDetails(
      RequestContext context, QueryHelper queryHelper) {
    populateMDCHeaders(context);
    return CommonExecutor.execute(
        () -> {
          verifyRealmId(context.getAuthorization());

          // RBAC check for READ operation - extract definition ID from query using existing DefinitionRbacService
          Optional<Object> recordMetadataInput = queryHelper.getArg(WorkflowConstants.BY);
          if (recordMetadataInput.isPresent()) {
            Map<String, String> metaDataInput = (HashMap<String, String>) recordMetadataInput.get();
            String definitionId = String.valueOf(metaDataInput.get(WorkflowConstants.DEFINITION_ID));
            definitionRbacService.verifyReadAccess(definitionId, context.getAuthorization().getRealm());
          }

          boolean isMultiStep =
              MultiStepUtil.isTemplateDataPassed(queryHelper.getPreparedQuery().getSubQueries());
          return new ListResult<>(
              Collections.singletonList(
                  definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, isMultiStep)));
        },
        "Exception occurred while performing reading obfuscate operation ",
        e -> handleServiceExceptionList((WorkflowGeneralException) e),
        e1 -> handleSystemErrorList(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  /**
   * Helper method to extract workflow type from definition for access verification
   * @param definition the definition object
   * @return workflow type (action key) or null if not found
   */
  private String getWorkflowTypeFromDefinition(Definition definition) {
    if (definition == null) {
      return null;
    }

    // Try to get workflow type from template name first
    if (definition.getTemplate() != null && definition.getTemplate().getName() != null) {
      return definitionRbacService.getWorkflowTypeFromTemplate(definition.getTemplate().getName());
    }

    // If template is null, try to get workflow type from workflow steps (for custom workflows)
    if (definition.getWorkflowSteps() != null && !definition.getWorkflowSteps().isEmpty()) {
      return definition.getWorkflowSteps().stream()
              .findFirst()
              .map(workflowStep ->
                      workflowStep.getActions().stream()
                              .findFirst()
                              .map(actionMapper -> actionMapper.getActionKey())
                              .orElse(null))
              .orElse(null);
    }

    return null;
  }

  /**
   * Helper method to verify write access based on operation type
   * @param operation The CRUD operation being performed
   * @param definition The definition object
   * @param authorization The authorization context
   */
  private void verifyWriteAccess(CrudOperation operation, Definition definition, Authorization authorization) {
    switch (operation) {
      case CREATE:
        String workflowType = getWorkflowTypeFromDefinition(definition);
        definitionRbacService.verifyCreateAccess(workflowType);
        break;
      case UPDATE:
        definitionRbacService.verifyUpdateAccess(definition.getId().getLocalId(), authorization.getRealm());
        break;
      case DELETE:
        definitionRbacService.verifyDeleteAccess(definition.getId().getLocalId(), authorization.getRealm());
        break;
      case ENABLED:
      case DISABLED:
        // ENABLED and DISABLED operations are treated as UPDATE operations
        definitionRbacService.verifyUpdateAccess(definition.getId().getLocalId(), authorization.getRealm());
        break;
      default:
        // For any unknown operation, default to CREATE access check
        String defaultWorkflowType = getWorkflowTypeFromDefinition(definition);
        definitionRbacService.verifyCreateAccess(defaultWorkflowType);
        break;
    }
  }

  /**
   * Helper method to verify read list access
   * @param authorization The authorization context
   */
  private void verifyReadListAccess(Authorization authorization) {
    // Basic check - verify user has read access for at least one workflow type
    // We'll check both 'default' and 'approval' workflow types to see if user has any read access
    boolean hasReadAccess = false;

    try {
      // Try checking access for default workflow type
      definitionRbacService.verifyReadAccess("dummy-default-id", authorization.getRealm());
      hasReadAccess = true;
    } catch (Exception e) {
      // User doesn't have access to default workflow type, try approval
      try {
        definitionRbacService.verifyReadAccess("dummy-approval-id", authorization.getRealm());
        hasReadAccess = true;
      } catch (Exception e2) {
        // User doesn't have access to approval workflow type either
        hasReadAccess = false;
      }
    }

    if (!hasReadAccess) {
      throw new WorkflowGeneralException(WorkflowError.ACCESS_DENIED);
    }
  }
}
