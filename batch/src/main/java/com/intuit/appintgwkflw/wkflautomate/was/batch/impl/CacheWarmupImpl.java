package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;


import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.reader.DistinctOwnerIdCustomItemReader;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

import org.springframework.beans.factory.annotation.Qualifier;
import javax.persistence.EntityManagerFactory;


import java.util.Arrays;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.*;

@Service
@ConditionalOnExpression("${batch-job.stepConfig.cacheWarmup.enabled:false}")
public class CacheWarmupImpl extends BatchService<Long> {

    private final EntityManagerFactory entityManagerFactory;
    private final StepBuilderFactory stepBuilderFactory;
    private final PlatformTransactionManager transactionManager;
    private final BatchJobConfig batchJobConfig;
    private final ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
    private final EnabledDefinitionCacheService enabledDefinitionCacheService;
    private final TemplateDetailsRepository templateDetailsRepository;
    private final DefinitionDetailsRepository definitionDetailsRepository;
    private final MetricLogger metricLogger;

    public CacheWarmupImpl(EntityManagerFactory entityManagerFactory,
                           BatchJobConfig batchJobConfig,
                           ThreadPoolTaskExecutor batchThreadPoolTaskExecutor,
                           StepBuilderFactory stepBuilderFactory,
                           PlatformTransactionManager transactionManager,
                           EnabledDefinitionCacheService enabledDefinitionCacheService,
                           TemplateDetailsRepository templateDetailsRepository,
                           DefinitionDetailsRepository definitionDetailsRepository,
                           ApplicationContext applicationContext,
                           MetricLogger metricLogger) {
        super(batchJobConfig, batchThreadPoolTaskExecutor, applicationContext);
        this.batchJobConfig = batchJobConfig;
        this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
        this.entityManagerFactory = entityManagerFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.transactionManager = transactionManager;
        this.enabledDefinitionCacheService = enabledDefinitionCacheService;
        this.templateDetailsRepository = templateDetailsRepository;
        this.definitionDetailsRepository = definitionDetailsRepository;
        this.metricLogger = metricLogger;
    }

    /**
     * Reads definitions to be populated into cache
     * @return
     */
    @Override
    @Bean(name = CACHE_WARMUP_READER)
    @StepScope
    public ItemStreamReader<Long> reader() {
        try {
            DistinctOwnerIdCustomItemReader customItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
            customItemReader.setItemLimit(getBatchSize());
            customItemReader.init();
            customItemReader.afterPropertiesSet();
            WorkflowLogger.logInfo("step=CachePopulationReaderComplete, status=success");
            return customItemReader;
        }
        catch (Exception e) {
            WorkflowLogger.logError("step=CachePopulationInit, status=failed, error=" + e);
            metricLogger.logErrorMetric(MetricName.CACHE_WARMUP_JOB, Type.APPLICATION_METRIC, e);
        }
        return null;
    }

    @Override
    @Bean(name = CACHE_WARMUP_PROCESSOR)
    public ItemProcessor<Long, Long> processor() {

        return ownerId -> {
            try{
                WorkflowLogger.logInfo("step=ProcessingCachePopulation ownerId=%s", ownerId);
                // sending only bpmns
                enabledDefinitionCacheService.populateEnabledDefinitionKeysForRealmId(
                        ownerId
                );
            }
            catch (Exception e){
                WorkflowLogger.logError("step=ProcessingCachePopulationFailed ownerId=%s, error=%s", ownerId, e);
                metricLogger.logErrorMetric(MetricName.CACHE_WARMUP_JOB, Type.APPLICATION_METRIC, e);
            }
            return null;
        };
    }

    /**
     * Creates the ItemWriter
     *
     * @return {@link ItemWriter}
     */
    @Override
    @Bean(name = CACHE_WARMUP_WRITER)
    public ItemWriter<Long> writer() {
        return list -> {
            // do nothing
            WorkflowLogger.logDebug("Invoking cacheWarmup itemWriter ownerIds=%s", list);
        };
    }

    @Override
    @Bean(name = CACHE_WARMUP_STEP)
    public Step createStep(
            @Qualifier(CACHE_WARMUP_READER) ItemReader<Long> reader,
            @Qualifier(CACHE_WARMUP_PROCESSOR) ItemProcessor<Long, Long> processor,
            @Qualifier(CACHE_WARMUP_WRITER) ItemWriter<Long> writer
    ) {
        return stepBuilderFactory
                .get(CACHE_WARMUP_STEP_NAME)
                .transactionManager(transactionManager)
                .<Long, Long>chunk(getChunkSize())
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .taskExecutor(batchThreadPoolTaskExecutor)
                .build();
    }

    @Override
    public BatchJobType getName() {
        return BatchJobType.CACHE_WARMUP;
    }
}
