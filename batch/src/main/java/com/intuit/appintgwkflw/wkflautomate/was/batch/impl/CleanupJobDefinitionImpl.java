package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_PROCESSOR;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_READER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_STEP;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_STEP_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_WRITER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_QUERY_BY_TEMPLATE;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.MASTER_CLEANUP_QUERY;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition.BatchCleanupStatusType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition.CleanupDefinitionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import javax.persistence.EntityManagerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * Batch CleanUp job for All Cleanup Related activities. On the basis on the internal status the
 * corresponding operation in carried out with the help of a factory.
 *
 * <AUTHOR> psinha4
 */
@Service
@ConditionalOnExpression("${batch-job.stepConfig.cleanupDefinition.enabled:false}")
public class CleanupJobDefinitionImpl extends BatchService<DefinitionDetails> {

  private final EntityManagerFactory entityManagerFactory;
  private final StepBuilderFactory stepBuilderFactory;
  private final CleanupJobListener cleanupJobListener;
  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private final PlatformTransactionManager transactionManager;
  private BatchJobConfig batchjobConfig;
  private ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
  private MetricLogger metricLogger;

  public CleanupJobDefinitionImpl(BatchJobConfig batchjobConfig,
      EntityManagerFactory entityManagerFactory, StepBuilderFactory stepBuilderFactory,
      CleanupJobListener cleanupJobListener, PlatformTransactionManager transactionManager,
      ThreadPoolTaskExecutor batchThreadPoolTaskExecutor, ApplicationContext applicationContext, MetricLogger metricLogger) {
    super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
    this.batchjobConfig = batchjobConfig;
    this.entityManagerFactory = entityManagerFactory;
    this.stepBuilderFactory = stepBuilderFactory;
    this.cleanupJobListener = cleanupJobListener;
    this.transactionManager = transactionManager;
    this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
    this.metricLogger = metricLogger;
  }

  /**
   * Creates the ItemReader object
   *
   * @return {@link ItemReader}
   */
  @Override
  @Bean(name = CLEANUP_DEFINITION_READER)
  @StepScope
  public ItemStreamReader<DefinitionDetails> reader() {

    BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();

    /**
     * JPAPagingItemReader fetches as limit n offset n. When we delete rows, Few items moves up which
     * results in skipping few deletions.
     */
    JpaPagingItemReader<DefinitionDetails> reader = new JpaPagingItemReader<>() {
      @Override
      public int getPage() {
        return 0;
      }
    };
    JpaNativeQueryProvider<DefinitionDetails> queryProvider = new JpaNativeQueryProvider<>();
    setReader(batchjobConfig, entityManagerFactory, reader);
    String cleanupQuery = getQuery(batchJobConfigDetails, queryProvider);
    try {
      initReader(queryProvider, reader);
    } catch (Exception e) {
      logError("step=cleanUpReaderInit, status=failed, error=" + e);
      metricLogger.logErrorMetric(MetricName.CLEANUP_JOB, Type.APPLICATION_METRIC, e);
    }
    WorkflowLogger.logInfo("step=cleanUpReaderComplete, query=" + cleanupQuery);
    return reader;
  }

  public void setReader(
      BatchJobConfig batchjobConfig,
      EntityManagerFactory entityManagerFactory,
      JpaPagingItemReader<DefinitionDetails> reader) {
    reader.setPageSize(getBatchSize());
    reader.setEntityManagerFactory(entityManagerFactory);
  }

  public void initReader(JpaNativeQueryProvider<DefinitionDetails> queryProvider,
      JpaPagingItemReader<DefinitionDetails> reader) throws Exception {
    queryProvider.afterPropertiesSet();
    reader.setQueryProvider(queryProvider);
    reader.afterPropertiesSet();
  }

  /**
   * @return Iterate through the maxPage and cleanup definitions
   */
  @Override
  @Bean(name = CLEANUP_DEFINITION_PROCESSOR)
  public ItemProcessor<DefinitionDetails, DefinitionDetails> processor() {

    return definitionDetails -> {
      try {
        WorkflowLogger.logInfo("step=processingCleanupDefinition definitionId=%s", definitionDetails.getDefinitionId());
        BatchCleanupStatusType actionName = BatchCleanupStatusType.getBatchCleanupStatusTypeFromName(
            definitionDetails.getInternalStatus().name());
        CleanupDefinitionHandlers.getHandler(actionName).handle(definitionDetails);
      } catch (Exception e) {
        WorkflowLogger.logError("step=processingCleanupDefinitionFailed definitionId=%s, status=failed, error=", definitionDetails.getDefinitionId(), e);
        metricLogger.logErrorMetric(MetricName.CLEANUP_JOB, Type.APPLICATION_METRIC, e);
      }
      return null;
    };
  }

  /**
   * Creates the ItemWriter
   *
   * @return {@link ItemWriter}
   */
  @Override
  @Bean(name = CLEANUP_DEFINITION_WRITER)
  public ItemWriter<DefinitionDetails> writer() {
    JpaItemWriter<DefinitionDetails> writer = new JpaItemWriter<>();
    writer.setEntityManagerFactory(entityManagerFactory);
    return writer;
  }


  @Override
  @Bean(name = CLEANUP_DEFINITION_STEP)
  public Step createStep(@Qualifier(CLEANUP_DEFINITION_READER) ItemReader<DefinitionDetails> reader,
      @Qualifier(CLEANUP_DEFINITION_PROCESSOR) ItemProcessor<DefinitionDetails, DefinitionDetails> processor,
      @Qualifier(CLEANUP_DEFINITION_WRITER) ItemWriter<DefinitionDetails> writer) {

    return stepBuilderFactory
        .get(CLEANUP_DEFINITION_STEP_NAME)
        .transactionManager(transactionManager)
        .<DefinitionDetails, DefinitionDetails>chunk(getChunkSize())
        .reader(reader)
        .listener(cleanupJobListener)
        .processor(processor)
        .writer(writer)
        .taskExecutor(batchThreadPoolTaskExecutor)
        .build();
  }

  @Override
  public BatchJobType getName() {
    return BatchJobType.CLEANUP_DEFINITION;
  }

  private void logError(String msg) {
    WorkflowLogger.error(
        () -> WorkflowLoggerRequest.builder().message(msg).className(this.getClass().getName())
            .downstreamComponentName(DownstreamComponentName.WAS)
            .downstreamServiceName(DownstreamServiceName.CLEANUP_DEFINITION));
  }

  /**
   * Return the queries after populating the placeholders for templateName and the internal statuses
   * to be worked on
   *
   * @param batchJobConfigDetails Details about the jobs like the templateName, baseTime etc
   * @param queryProvider         JpaNativeQueryProvider for the sql operation
   * @return
   */
  public String getQuery(
      BatchJobConfigDetails batchJobConfigDetails,
      JpaNativeQueryProvider<DefinitionDetails> queryProvider) {
    Timestamp baseTimeStamp =
        Timestamp.valueOf(
            LocalDateTime.now().minusMinutes(batchJobConfigDetails.getBaseTimeJobStart()));

    Timestamp endTimeStamp =
        Timestamp.valueOf(
            LocalDateTime.now().plusMinutes(batchJobConfigDetails.getDurationInMinutes()));

    String updateStatus =
        "'" + StringUtils.join(batchJobConfigDetails.getCleanupStatus(), "','") + "'";
    //  Initially only start with a template name in prod. TODO Cleanup post testing.
    String query =
        StringUtils.isEmpty(batchJobConfigDetails.getTemplateName())
            ? String.format(MASTER_CLEANUP_QUERY, updateStatus, baseTimeStamp, endTimeStamp)
            : String.format(
                CLEANUP_QUERY_BY_TEMPLATE, batchJobConfigDetails.getTemplateName(), updateStatus,
                baseTimeStamp, endTimeStamp);
    queryProvider.setSqlQuery(query);
    queryProvider.setEntityClass(DefinitionDetails.class);
    return query;
  }

}
