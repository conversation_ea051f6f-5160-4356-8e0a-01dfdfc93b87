package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition.BatchCleanupStatusType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ProcessCleanupHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.*;

/**
 * Batch CleanUp job for cleaning up all the ended process based on TTL value
 *
 */
@Service
@ConditionalOnExpression("${batch-job.stepConfig.cleanupProcess.enabled:false}")
public class CleanupJobProcessImpl extends BatchService<ProcessDetails> {

    private final EntityManagerFactory entityManagerFactory;

    private BatchJobConfig batchjobConfig;

    private ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;

    private HistoryConfig historyConfig;


    private final StepBuilderFactory stepBuilderFactory;
    @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
    private final PlatformTransactionManager transactionManager;

    private final ProcessCleanupHelper processCleanupHelper;


    public CleanupJobProcessImpl(BatchJobConfig batchjobConfig,
                                 EntityManagerFactory entityManagerFactory, StepBuilderFactory stepBuilderFactory,
                                 PlatformTransactionManager transactionManager,
                                 ThreadPoolTaskExecutor batchThreadPoolTaskExecutor,
                                 ProcessCleanupHelper processCleanupHelper,
                                 ApplicationContext applicationContext, HistoryConfig historyConfig) {
        super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
        this.batchjobConfig = batchjobConfig;
        this.entityManagerFactory = entityManagerFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.transactionManager = transactionManager;
        this.processCleanupHelper = processCleanupHelper;
        this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
        this.historyConfig = historyConfig;
    }


    /**
     * Creates the ItemReader object
     *
     * @return {@link ItemReader}
     */
    @Override
    @Bean(name = CLEANUP_PROCESS_READER)
    @StepScope
    public ItemStreamReader<ProcessDetails> reader() {

        BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();

        /**
         * JPAPagingItemReader fetches as limit n offset n. When we delete rows, Few items moves up which
         * results in skipping few deletions.
         */
        JpaPagingItemReader<ProcessDetails> reader = new JpaPagingItemReader<>() {
            @Override
            public int getPage() {
                return 0;
            }
        };
        JpaNativeQueryProvider<ProcessDetails> queryProvider = new JpaNativeQueryProvider<>();
        setReader(batchjobConfig, entityManagerFactory, reader);
        String cleanupQuery = getQuery(batchJobConfigDetails, queryProvider);
        try {
            initReader(queryProvider, reader);
        } catch (Exception e) {
            logError("step=cleanUpReaderInit, status=failed, error=" + e);
        }
        WorkflowLogger.logInfo("step=cleanUpReaderComplete, query=" + cleanupQuery);
        return reader;
    }

    public void setReader(BatchJobConfig batchjobConfig,
                          EntityManagerFactory entityManagerFactory,
                          JpaPagingItemReader<ProcessDetails> reader) {
        reader.setPageSize(batchjobConfig.getBatchSize());
        reader.setEntityManagerFactory(entityManagerFactory);
    }

    public void initReader(JpaNativeQueryProvider<ProcessDetails> queryProvider,
                           JpaPagingItemReader<ProcessDetails> reader) throws Exception {
        queryProvider.afterPropertiesSet();
        reader.setQueryProvider(queryProvider);
        reader.afterPropertiesSet();
    }

    /**
     * @return Iterate through the maxPage and cleanup process
     */
    @Override
    @Bean(name = CLEANUP_PROCESS_PROCESSOR)
    public ItemProcessor<ProcessDetails, ProcessDetails> processor() {
        return processDetails -> {
            WorkflowLogger.logInfo("step=processingCleanupProcess processId=%s", processDetails.getProcessId());

            processCleanupHelper.deleteEndedProcess(processDetails);
            return null;
        };
    }

    /**
     * Creates the ItemWriter
     *
     * @return {@link ItemWriter}
     */
    @Override
    @Bean(name = CLEANUP_PROCESS_WRITER)
    public ItemWriter<ProcessDetails> writer() {
        JpaItemWriter<ProcessDetails> writer = new JpaItemWriter<>();
        writer.setEntityManagerFactory(entityManagerFactory);
        return writer;
    }


    @Override
    @Bean(name = CLEANUP_PROCESS_STEP)
    public Step createStep(@Qualifier(CLEANUP_PROCESS_READER) ItemReader<ProcessDetails> reader,
                           @Qualifier(CLEANUP_PROCESS_PROCESSOR) ItemProcessor<ProcessDetails, ProcessDetails> processor,
                           @Qualifier(CLEANUP_PROCESS_WRITER) ItemWriter<ProcessDetails> writer) {

        return stepBuilderFactory
                .get(CLEANUP_PROCESS_STEP_NAME)
                .transactionManager(transactionManager)
                .<ProcessDetails, ProcessDetails>chunk(batchjobConfig.getChunkSize())
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .taskExecutor(batchThreadPoolTaskExecutor)
                .build();
    }

    @Override
    public BatchJobType getName() {
        return BatchJobType.CLEANUP_PROCESS;
    }

    private void logError(String msg) {
        WorkflowLogger.logError("step=CleanupProcessInitFailure, status=WorkflowGeneralException, error=" + msg);
    }

    /**
     * Return the queries after fetching ended process
     *
     * @param batchJobConfigDetails Details about the jobs like the baseTime etc
     * @param queryProvider         JpaNativeQueryProvider for the sql operation
     * @return
     */
    public String getQuery(
            BatchJobConfigDetails batchJobConfigDetails,
            JpaNativeQueryProvider<ProcessDetails> queryProvider) {

        String updateStatus =
                "'" + StringUtils.join(batchJobConfigDetails.getCleanupStatus(), "','") + "'";

        Timestamp baseTimeStamp =
                Timestamp.valueOf(
                        LocalDateTime.now().minusDays(historyConfig.getTtl()).minusMinutes(batchJobConfigDetails.getBaseTimeJobStart()));

        Timestamp endTimeStamp =
                Timestamp.valueOf(
                        LocalDateTime.now().minusDays(historyConfig.getTtl()).plusMinutes(batchJobConfigDetails.getDurationInMinutes()));

        String query = String.format(PROCESS_CLEANUP_QUERY, updateStatus, baseTimeStamp, endTimeStamp);
        queryProvider.setSqlQuery(query);
        queryProvider.setEntityClass(ProcessDetails.class);
        return query;
    }

}