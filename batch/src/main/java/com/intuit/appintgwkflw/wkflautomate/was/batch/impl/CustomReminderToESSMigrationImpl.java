package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_ESS_MIGRATION_QUERY_ALL_OWNER_IDS;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_MIGRATION_PROCESSOR;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_MIGRATION_READER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_MIGRATION_STEP;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_MIGRATION_WRITER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_STEP_NAME;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import javax.persistence.EntityManagerFactory;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * <AUTHOR> This class migrate all the reminder definitions to ESS.
 */
@Service
@ConditionalOnExpression("${batch-job.stepConfig.customReminderToESSMigration.enabled:false}")
public class CustomReminderToESSMigrationImpl extends BatchService<DefinitionDetails> {

  private final EntityManagerFactory entityManagerFactory;
  private final BatchJobConfig batchjobConfig;
  private final ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
  private final StepBuilderFactory stepBuilderFactory;
  private final MetricLogger metricLogger;

  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private final PlatformTransactionManager transactionManager;

  private final EventScheduleHelper eventScheduleHelper;

  public CustomReminderToESSMigrationImpl(
          EntityManagerFactory entityManagerFactory,
          BatchJobConfig batchjobConfig,
          ThreadPoolTaskExecutor batchThreadPoolTaskExecutor,
          StepBuilderFactory stepBuilderFactory,
          PlatformTransactionManager transactionManager,
          EventScheduleHelper eventScheduleHelper, ApplicationContext applicationContext,
          MetricLogger metricLogger) {
    super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
    this.entityManagerFactory = entityManagerFactory;
    this.batchjobConfig = batchjobConfig;
    this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
    this.stepBuilderFactory = stepBuilderFactory;
    this.transactionManager = transactionManager;
    this.eventScheduleHelper = eventScheduleHelper;
    this.metricLogger = metricLogger;
  }

  @Override
  @Bean(name = CUSTOM_REMINDER_TO_ESS_MIGRATION_READER)
  @StepScope
  public ItemStreamReader<DefinitionDetails> reader() {
    JpaPagingItemReader<DefinitionDetails> reader =
        new JpaPagingItemReader<>() {
          @Override
          public int getPage() {
            return 0;
          }
        };
    BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();
    try {
      Timestamp baseTimeStamp = Timestamp.valueOf(
          LocalDateTime.now().minusMinutes(batchJobConfigDetails.getBaseTimeJobStart()));
      Timestamp endTimeStamp =
          Timestamp.valueOf(
              LocalDateTime.now().plusMinutes(batchJobConfigDetails.getDurationInMinutes()));
      reader.setPageSize(getBatchSize());
      reader.setEntityManagerFactory(entityManagerFactory);

      JpaNativeQueryProvider<DefinitionDetails> queryProvider = new JpaNativeQueryProvider<>();
      String migrationQuery =
          String.format(
              CUSTOM_REMINDER_ESS_MIGRATION_QUERY_ALL_OWNER_IDS,
              batchJobConfigDetails.getTemplateIds(), batchJobConfigDetails.getRecordTypes(),
              baseTimeStamp,
              endTimeStamp
          );
      queryProvider.setSqlQuery(migrationQuery);
      queryProvider.setEntityClass(DefinitionDetails.class);
      queryProvider.afterPropertiesSet();
      reader.setQueryProvider(queryProvider);
      reader.afterPropertiesSet();
      WorkflowLogger.logInfo(
          "step=CustomReminderESSMigrationReaderComplete, status=success, CustomReminderESSMigrationQuery=%s templateIds=%s recordTypes=%s",
          migrationQuery,
          batchJobConfigDetails.getTemplateIds(),
          batchJobConfigDetails.getRecordTypes());
    } catch (Exception e) {
      WorkflowLogger.logError(
          "step=CustomReminderESSMigrationReaderInit, status=failed, error=" + e);
      metricLogger.logErrorMetric(MetricName.CUSTOM_REMINDER_TO_ESS_MIGRATION_JOB, Type.APPLICATION_METRIC, e);
    }
    return reader;
  }

  /**
   * This method migrate the definitions to the ESS scheduler.
   *
   * @return
   */
  @Override
  @Bean(name = CUSTOM_REMINDER_TO_ESS_MIGRATION_PROCESSOR)
  public ItemProcessor<DefinitionDetails, DefinitionDetails> processor() {
    return definitionDetails -> {
      try {
        WorkflowLogger.logInfo(
            "step=CustomReminderESSMigrationProcessorInit, status=started, definitionId=%s",
            definitionDetails.getDefinitionId());
        DefinitionInstance definitionInstance = new DefinitionInstance();
        definitionInstance.setDefinitionDetails(definitionDetails);
        eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
        WorkflowLogger.logInfo(
            "step=CustomReminderESSMigrationProcessorInit, status=completed, definitionId=%s",
            definitionDetails.getDefinitionId());

      } catch (WorkflowGeneralException e) {
        definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
        WorkflowLogger.logError(
            "step=CustomReminderESSMigrationProcessInitFailure, status=WorkflowGeneralException, error="
                + e);
        metricLogger.logErrorMetric(MetricName.CUSTOM_REMINDER_TO_ESS_MIGRATION_JOB, Type.APPLICATION_METRIC, e);
      } catch (Exception exception) {
        // Won't fetch in the current batch
        definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
        WorkflowLogger.logError(
            "step=CustomReminderESSMigrationProcessInitFailure, status=failed, error=" + exception);
        metricLogger.logErrorMetric(MetricName.CUSTOM_REMINDER_TO_ESS_MIGRATION_JOB, Type.APPLICATION_METRIC, exception);
      }

      return definitionDetails;
    };
  }

  @Override
  @Bean(name = CUSTOM_REMINDER_TO_ESS_MIGRATION_WRITER)
  public ItemWriter<DefinitionDetails> writer() {
    JpaItemWriter<DefinitionDetails> writer = new JpaItemWriter<>();
    writer.setEntityManagerFactory(entityManagerFactory);
    return writer;
  }

  @Override
  @Bean(name = CUSTOM_REMINDER_TO_ESS_MIGRATION_STEP)
  public Step createStep(
      @Qualifier(CUSTOM_REMINDER_TO_ESS_MIGRATION_READER) ItemReader<DefinitionDetails> reader,
      @Qualifier(CUSTOM_REMINDER_TO_ESS_MIGRATION_PROCESSOR)
      ItemProcessor<DefinitionDetails, DefinitionDetails> processor,
      @Qualifier(CUSTOM_REMINDER_TO_ESS_MIGRATION_WRITER) ItemWriter<DefinitionDetails> writer) {
    return stepBuilderFactory
        .get(CUSTOM_REMINDER_TO_ESS_STEP_NAME)
        .transactionManager(transactionManager)
        .<DefinitionDetails, DefinitionDetails>chunk(getChunkSize())
        .reader(reader)
        .processor(processor)
        .writer(writer)
        .taskExecutor(batchThreadPoolTaskExecutor)
        .build();
  }

  @Override
  public BatchJobType getName() {
    return BatchJobType.CUSTOM_REMINDER_TO_ESS_MIGRATION;
  }
}
