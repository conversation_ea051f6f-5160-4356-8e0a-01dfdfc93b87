package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.*;


/**
 * Implementation of the SchedulingSvcMigrationCleanup batch job.
 * The batch job handles the cleanup of the SchedulingSvc migration.
 *
 *  * <AUTHOR>
 *
 */
@Service
@ConditionalOnExpression("${batch-job.stepConfig.schedulingSvcMigrationCleanup.enabled:false}")
public class SchedulingSvcMigrationCleanupImpl extends BatchService<DefinitionDetails> {
    private final EntityManagerFactory entityManagerFactory;
    private final BatchJobConfig batchjobConfig;
    private final ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
    private final StepBuilderFactory stepBuilderFactory;
    private final MetricLogger metricLogger;

    @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
    private final PlatformTransactionManager transactionManager;

    private final EventScheduleHelper eventScheduleHelper;

    public SchedulingSvcMigrationCleanupImpl(
            EntityManagerFactory entityManagerFactory,
            BatchJobConfig batchjobConfig,
            ThreadPoolTaskExecutor batchThreadPoolTaskExecutor,
            StepBuilderFactory stepBuilderFactory,
            PlatformTransactionManager transactionManager,
            EventScheduleHelper eventScheduleHelper, ApplicationContext applicationContext,
            MetricLogger metricLogger) {
        super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
        this.entityManagerFactory = entityManagerFactory;
        this.batchjobConfig = batchjobConfig;
        this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
        this.stepBuilderFactory = stepBuilderFactory;
        this.transactionManager = transactionManager;
        this.eventScheduleHelper = eventScheduleHelper;
        this.metricLogger = metricLogger;
    }

    /**
     * Creates and configures the reader for the batch job.
     * The reader fetches the DefinitionDetails entities to be migrated.
     *
     * @return The configured ItemStreamReader for DefinitionDetails.
     */
    @Override
    @Bean(name = SCHEDULING_SVC_MIGRATION_CLEANUP_READER)
    @StepScope
    public ItemStreamReader<DefinitionDetails> reader() {
        JpaPagingItemReader<DefinitionDetails> reader =
                new JpaPagingItemReader<>() {
                    @Override
                    public int getPage() {
                        return 0;
                    }
                };
        BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();
        try {
            Timestamp baseTimeStamp = Timestamp.valueOf(
                    LocalDateTime.now().minusMinutes(batchJobConfigDetails.getBaseTimeJobStart()));
            Timestamp endTimeStamp =
                    Timestamp.valueOf(
                            LocalDateTime.now().plusMinutes(batchJobConfigDetails.getDurationInMinutes()));
            reader.setPageSize(getBatchSize());
            reader.setEntityManagerFactory(entityManagerFactory);

            JpaNativeQueryProvider<DefinitionDetails> queryProvider = new JpaNativeQueryProvider<>();
            String migrationQuery =
                    String.format(
                            SCHEDULING_SVC_MIGRATION_CLEANUP_QUERY_ORDER_BY_DEFINITION_IDS, batchJobConfigDetails.getTemplateIds(),
                            baseTimeStamp,
                            endTimeStamp
                    );
            queryProvider.setSqlQuery(migrationQuery);
            queryProvider.setEntityClass(DefinitionDetails.class);
            queryProvider.afterPropertiesSet();
            reader.setQueryProvider(queryProvider);
            reader.afterPropertiesSet();
            WorkflowLogger.logInfo(
                    "step=SchedulingSvcMigrationCleanupReaderComplete, status=success, CustomScheduledActionsESSToSchedulingSvcMigrationQuery=%s templateIds=%s recordTypes=%s",
                    migrationQuery,
                    batchJobConfigDetails.getTemplateIds(),
                    batchJobConfigDetails.getRecordTypes());
        } catch (Exception e) {
            WorkflowLogger.logError(
                    "step=SchedulingSvcMigrationCleanupReaderInit, status=failed, error=" + e);
            metricLogger.logErrorMetric(MetricName.SCHEDULING_SVC_MIGRATION_CLEANUP_JOB, Type.APPLICATION_METRIC, e);
        }
        return reader;
    }

    /**
     * Creates and configures the processor for the batch job.
     * The processor handles the migration of each DefinitionDetails entity.
     *
     * @return The configured ItemProcessor for DefinitionDetails.
     */
    @Override
    @Bean(name =SCHEDULING_SVC_MIGRATION_CLEANUP_PROCESSOR)
    public ItemProcessor<DefinitionDetails, DefinitionDetails> processor() {
        return definitionDetails -> {
            try {
                WorkflowLogger.logInfo(
                        "step=SchedulingSvcMigrationCleanupProcessorInit, status=started, definitionId=%s",
                        definitionDetails.getDefinitionId());
                DefinitionInstance definitionInstance = new DefinitionInstance();
                definitionInstance.setDefinitionDetails(definitionDetails);
                eventScheduleHelper.cleanUpSchedulingSvcMigration(definitionInstance);
                WorkflowLogger.logInfo(
                        "step=SchedulingSvcMigrationCleanupProcessorInit, status=completed, definitionId=%s",
                        definitionDetails.getDefinitionId());

            } catch (WorkflowGeneralException e) {
                definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
                WorkflowLogger.logError(
                        e, "step=SchedulingSvcMigrationCleanupProcessInitFailure, status=WorkflowGeneralException");
                metricLogger.logErrorMetric(MetricName.SCHEDULING_SVC_MIGRATION_CLEANUP_JOB, Type.APPLICATION_METRIC, e);
            } catch (Exception exception) {
                // Won't fetch in the current batch
                definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
                WorkflowLogger.logError(
                        exception, "step=SchedulingSvcMigrationCleanupProcessInitFailure, status=failed");
                metricLogger.logErrorMetric(MetricName.SCHEDULING_SVC_MIGRATION_CLEANUP_JOB, Type.APPLICATION_METRIC, exception);
            }

            return definitionDetails;
        };
    }

    /**
     * Creates and configures the writer for the batch job.
     * The writer persists the migrated DefinitionDetails entities.
     *
     * @return The configured ItemWriter for DefinitionDetails.
     */
    @Override
    @Bean(name = SCHEDULING_SVC_MIGRATION_CLEANUP_WRITER)
    public ItemWriter<DefinitionDetails> writer() {
        JpaItemWriter<DefinitionDetails> writer = new JpaItemWriter<>();
        writer.setEntityManagerFactory(entityManagerFactory);
        return writer;
    }

    /**
     * Creates and configures the step for the batch job.
     * The step defines the reader, processor, and writer to be used in the job.
     *
     * @param reader The reader for the step.
     * @param processor The processor for the step.
     * @param writer The writer for the step.
     * @return The configured Step for the batch job.
     */
    @Override
    @Bean(name = SCHEDULING_SVC_MIGRATION_CLEANUP_STEP)
    public Step createStep(
            @Qualifier(SCHEDULING_SVC_MIGRATION_CLEANUP_READER) ItemReader<DefinitionDetails> reader,
            @Qualifier(SCHEDULING_SVC_MIGRATION_CLEANUP_PROCESSOR)
            ItemProcessor<DefinitionDetails, DefinitionDetails> processor,
            @Qualifier(SCHEDULING_SVC_MIGRATION_CLEANUP_WRITER) ItemWriter<DefinitionDetails> writer) {
        return stepBuilderFactory
                .get(SCHEDULING_SVC_MIGRATION_CLEANUP_STEP_NAME)
                .transactionManager(transactionManager)
                .<DefinitionDetails, DefinitionDetails>chunk(getChunkSize())
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .taskExecutor(batchThreadPoolTaskExecutor)
                .build();
    }

    /**
     * Returns the name of the batch job.
     *
     * @return The name of the batch job.
     */
    @Override
    public BatchJobType getName() {
        return BatchJobType.SCHEDULING_SVC_MIGRATION_CLEANUP;
    }
}
