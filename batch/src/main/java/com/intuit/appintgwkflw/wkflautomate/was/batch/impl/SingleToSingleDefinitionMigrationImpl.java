package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import javax.persistence.EntityManagerFactory;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.*;

/**
 * <AUTHOR> Contains the whole Logic to migarte single definition
 *
 */
@Service
@ConditionalOnExpression("${batch-job.stepConfig.singleToSingleDefinitionMigration.enabled:false}")
public class SingleToSingleDefinitionMigrationImpl extends BatchService<DefinitionDetails> {

  private final EntityManagerFactory entityManagerFactory;
  private final TemplateDetailsRepository templateDetailsRepository;
  private final BatchJobConfig batchjobConfig;
  private final ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
  private final StepBuilderFactory stepBuilderFactory;
  private final MetricLogger metricLogger;


  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private final PlatformTransactionManager transactionManager;
//  Used to store latest single definition templateId
  private static TemplateDetails templateDetails = null;

  public SingleToSingleDefinitionMigrationImpl(EntityManagerFactory entityManagerFactory, TemplateDetailsRepository templateDetailsRepository, BatchJobConfig batchjobConfig, ThreadPoolTaskExecutor batchThreadPoolTaskExecutor, StepBuilderFactory stepBuilderFactory, PlatformTransactionManager transactionManager, ApplicationContext applicationContext, MetricLogger metricLogger) {
    super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
    this.entityManagerFactory = entityManagerFactory;
    this.templateDetailsRepository = templateDetailsRepository;
    this.batchjobConfig = batchjobConfig;
    this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
    this.stepBuilderFactory = stepBuilderFactory;
    this.transactionManager = transactionManager;
    this.metricLogger = metricLogger;
  }

  @Override
  @Bean(name=SINGLE_TO_SINGLE_DEFINITION_MIGRATION_READER)
  @StepScope
  public ItemStreamReader<DefinitionDetails> reader() {
    JpaPagingItemReader<DefinitionDetails> reader = new JpaPagingItemReader<>() {
      @Override
      public int getPage() {
        return 0;
      }
    };
    BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();
    try{

      Timestamp endTimeStamp = Timestamp.valueOf(LocalDateTime.now().plusMinutes(batchJobConfigDetails.getDurationInMinutes()));

      reader.setPageSize(getBatchSize());
      reader.setEntityManagerFactory(entityManagerFactory);

      JpaNativeQueryProvider<DefinitionDetails> queryProvider = new JpaNativeQueryProvider<>();

      String migrationQuery =
          StringUtils.isEmpty(batchJobConfigDetails.getTemplateVersion())
          ? String.format(SINGLE_MIGRATION_QUERY_ALL_VERSIONS, batchJobConfigDetails.getTemplateName(),
              batchJobConfigDetails.getRecordTypes(), endTimeStamp)
          : String.format(SINGLE_MIGRATION_QUERY_BY_VERSION, batchJobConfigDetails.getTemplateName(),
              batchJobConfigDetails.getRecordTypes(), endTimeStamp, batchJobConfigDetails.getTemplateVersion());

      queryProvider.setSqlQuery(migrationQuery);
      queryProvider.setEntityClass(DefinitionDetails.class);

      queryProvider.afterPropertiesSet();
      reader.setQueryProvider(queryProvider);
      reader.afterPropertiesSet();
      WorkflowLogger.logInfo("step=SingleToSingleMigrationReaderComplete, status=success, MigrationQuery="+migrationQuery);
    } catch (Exception e) {
      WorkflowLogger.logError("step=SingleToSingleMigrationReaderInit, status=failed, error=" + e);
      metricLogger.logErrorMetric(MetricName.SINGLE_TO_SINGLE_DEFINITION_MIGRATION_JOB, Type.APPLICATION_METRIC, e);
    }
    return reader;
  }

  @Override
  @Bean(name=SINGLE_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR)
  public ItemProcessor<DefinitionDetails, DefinitionDetails> processor() {
    return definitionDetails -> {
      try{
        /*
         * Here we are fetching the latest single template id and fetching the template details ,
         * Once we get it from db storing it in static variable, so it can be reuse by further
         * and no need to make extra db calls
         */
        if(null == templateDetails){
           String singleTemplateId = MigrationServiceHelper.getSingleTemplateId(definitionDetails, templateDetailsRepository, getBatchConfig().getTemplateName());
           templateDetails = templateDetailsRepository.findByTemplateId(singleTemplateId);
          }
        WorkflowLogger.logInfo("step=SingleToSingleMigrations, definitionId=%s, oldTemplateId=%s, newTemplateId=%s", definitionDetails.getDefinitionId(), definitionDetails.getTemplateDetails().getId(), templateDetails.getId());
        //Updating template details in definition table
        definitionDetails.setTemplateDetails(templateDetails);
        return definitionDetails;
      } catch (WorkflowGeneralException e) {
          WorkflowLogger.logError("step=SingleToSingleMigrationProcessInitFailure, status=WorkflowGeneralException, error=" + e);
        metricLogger.logErrorMetric(MetricName.SINGLE_TO_SINGLE_DEFINITION_MIGRATION_JOB, Type.APPLICATION_METRIC, e);
      } catch (Exception e) {
        WorkflowLogger.logError("step=SingleToSingleMigrationProcessInitFailure, status=failed, error=" + e);
        metricLogger.logErrorMetric(MetricName.SINGLE_TO_SINGLE_DEFINITION_MIGRATION_JOB, Type.APPLICATION_METRIC, e);
      }
      return null;
    };
  }

  @Override
  @Bean(name=SINGLE_TO_SINGLE_DEFINITION_MIGRATION_WRITER)
  public ItemWriter<DefinitionDetails> writer() {
    JpaItemWriter<DefinitionDetails> writer = new JpaItemWriter<>();
    writer.setEntityManagerFactory(entityManagerFactory);
    return writer;
  }

  @Override
  @Bean(name = SINGLE_TO_SINGLE_DEFINITION_MIGRATION_STEP)
  public Step createStep(@Qualifier(SINGLE_TO_SINGLE_DEFINITION_MIGRATION_READER) ItemReader<DefinitionDetails> reader,
                         @Qualifier(SINGLE_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR) ItemProcessor<DefinitionDetails, DefinitionDetails> processor,
                         @Qualifier(SINGLE_TO_SINGLE_DEFINITION_MIGRATION_WRITER) ItemWriter<DefinitionDetails> writer) {
    return stepBuilderFactory
        .get(SINGLE_TO_SINGLE_DEFINITION_STEP_NAME)
        .transactionManager(transactionManager)
        .<DefinitionDetails, DefinitionDetails>chunk(getChunkSize())
        .reader(reader)
        .processor(processor)
        .writer(writer)
        .taskExecutor(batchThreadPoolTaskExecutor)
        .build();
  }

  @Override
  public BatchJobType getName() {
    return BatchJobType.SINGLE_TO_SINGLE_DEFINITION_MIGRATION;
  }



}
