package com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName.CLEANUP_DEFINITION;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Listener to log the batches executed.
 */
@Component
public class CleanupJobListener implements ItemWriteListener<DefinitionDetails> {

  @Override
  public void beforeWrite(List<? extends DefinitionDetails> list) {
    WorkflowLogger.logInfo("step=definitionStarted, cnt=%s", list.size());
  }

  @Override
  public void afterWrite(List<? extends DefinitionDetails> list) {
    WorkflowLogger.logInfo("step=definitionEnded, cnt=%s", list.size());
  }

  @Override
  public void onWriteError(Exception e, List<? extends DefinitionDetails> list) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(
                    "Failure=DBWriteForOfflineTicket failedCount=%s for ownerId=%s", list.size(),
                    list.stream()
                        .map((item) -> String.valueOf(item.getDefinitionId()))
                        .collect(Collectors.joining(", ")))
                .stackTrace(e)
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(CLEANUP_DEFINITION));
  }
}
