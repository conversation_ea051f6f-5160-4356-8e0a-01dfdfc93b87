package com.intuit.appintgwkflw.wkflautomate.was.batch.impl.reader;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import lombok.Setter;
import org.springframework.batch.item.*;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * Provides a custom item reader that reads distinct ownerIds
 * from the de_definitions_details table based on the templateIds
 */
public class DistinctOwnerIdCustomItemReader
        extends ItemStreamSupport implements ItemStreamReader<Long> {

    private static final String START_OWNER_ID = "read.start.ownerId";
    private final DefinitionDetailsRepository definitionsDetailsRepository;

    private final TemplateDetailsRepository templateDetailsRepository;
    private final BatchJobConfig batchJobConfig;

    private final Object lock = new Object();

    private volatile List<Long> results = new CopyOnWriteArrayList<>();

    private volatile Long startOwnerId;

    private volatile boolean isInitialized;

    private List<String> templateIds;

    @Setter
    private Integer itemLimit;

    private volatile int current = 0;

    private volatile boolean shouldFetchNewItems = true;

    @Setter
    private boolean saveState = false;

    public DistinctOwnerIdCustomItemReader(DefinitionDetailsRepository definitionsDetailsRepository, TemplateDetailsRepository templateDetailsRepository, BatchJobConfig batchJobConfig) {
        this.definitionsDetailsRepository = definitionsDetailsRepository;
        this.templateDetailsRepository = templateDetailsRepository;
        this.batchJobConfig = batchJobConfig;
        this.setName(ClassUtils.getShortName(DistinctOwnerIdCustomItemReader.class));
    }

    /**
     * Initializes the reader with required properties
     */
    public void init() {

        if (!isInitialized) {
            startOwnerId = definitionsDetailsRepository.findSmallestPositiveOwnerId()
                    .map(i -> i - 1)
                    .orElse(0L);
            templateIds = templateDetailsRepository.findCustomTemplateIds(Arrays.asList(batchJobConfig.getStepConfig().get(BatchJobType.CACHE_WARMUP).getWorkflows().split(",")));
            isInitialized = true;
            WorkflowLogger.logInfo("CustomItemReader initialized with startOwnerId=%s templateIds=%s", startOwnerId, templateIds);
        }
    }

    /**
     * Performs validation of required properties
     */
    public void afterPropertiesSet() {
        Assert.notNull(definitionsDetailsRepository, "definitionsDetailsRepository must not be null");
        Assert.notNull(templateDetailsRepository, "templateDetailsRepository must not be null");
        Assert.notEmpty(templateIds, "templateIds must not be empty");
        Assert.notNull(startOwnerId, "startOwnerId must not be null");
        Assert.isTrue(startOwnerId > 0, "startOwnerId must be greater than 0");
        Assert.notNull(itemLimit, "itemLimit must not be null");
        Assert.isTrue(itemLimit > 0, "itemLimit must be greater than 0");
    }

    @Override
    public Long read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {

        synchronized (lock) {
            if (results.isEmpty() || current == results.size()) {
                fetchNewItems();
                current = 0;
            }
            if (!results.isEmpty() && current < results.size()) {
                return results.get(current++);
            }
            return null;
        }
    }

    /**
     * Fetches new items from the database
     */
    private void fetchNewItems() {
        results.clear();
        if (!shouldFetchNewItems) {
            return;
        }
        List<Long> ownerIds = definitionsDetailsRepository
                .findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(startOwnerId,
                        templateIds,
                        PageRequest.of(0, itemLimit, Sort.by(Sort.Direction.ASC, "ownerId")
                        )
                );
        WorkflowLogger.logDebug("Fetched new items ownerIds=%s", ownerIds);
        if (!CollectionUtils.isEmpty(ownerIds)) {
            results.addAll(ownerIds);
            startOwnerId = ownerIds.get(ownerIds.size() - 1);
        }
        if (ownerIds.size() < itemLimit) {
            shouldFetchNewItems = false;
        }
        WorkflowLogger.logDebug("Fetching new items startOwnerId=%s shouldFetchNewItems=%s itemCount=%s",
                startOwnerId, shouldFetchNewItems, ownerIds.size());
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        if (saveState && executionContext.containsKey(this.getExecutionContextKey(START_OWNER_ID))) {
            startOwnerId = executionContext.getLong(this.getExecutionContextKey(START_OWNER_ID));
            WorkflowLogger.logDebug("CustomItemReader opened with startOwnerId=%s", startOwnerId);
        }
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        if (saveState) {
            executionContext.putLong(this.getExecutionContextKey(START_OWNER_ID), startOwnerId);
            WorkflowLogger.logDebug("CustomItemReader updated with startOwnerId=%s", startOwnerId);
        }
    }

    @Override
    public void close() throws ItemStreamException {
        isInitialized = false;
        startOwnerId = null;
        templateIds = null;
        current = 0;
    }
}
