{"protocolProfileBehavior": {}, "info": {"name": "Provider Collection", "_postman_id": "e87bc788-e6b5-48cb-ade0-047f8396a9cd", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Read One Template", "request": {"body": {"mode": "graphql", "graphql": {"query": "query q {\n  node(id: \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:db73b41b-b4e5-46ee-b6d1-ac5f48ba99f1\") {\n    ... on Workflows_Template {\n      name\n      id\n      description\n      displayName\n      version\n      category\n      workflowSteps {\n        edges {\n          node {\n            id\n            name\n            required\n            sequence\n            trigger {\n              id\n              parameters {\n                parameterType\n                getOptionsForFieldValue\n                parameterName\n                configurable\n                required\n                multiSelect\n                fieldValues\n              }\n            }\n            workflowStepCondition {\n              id\n              conditionalInputParameters {\n                inputParameter {\n                  parameterType\n                  getOptionsForFieldValue\n                parameterName\n                configurable\n                required\n                multiSelect\n                fieldValues\n                }\n                supportedOperators {\n                  symbol\n                  description\n                }\n                \n              }\n              ruleLines{\n                edges{\n                  node{\n            \n                    rules{\n                      parameterName\n                      conditionalExpression\n                    }\n                  }\n                }\n              }\n             \n            }\n            actions {\n              actionKey\n              action {\n                  name\n                  required\n                  selected\n                id\n                nexts {\n                  nextType\n                  nextAction {\n                    id\n                  }\n                  nextWorkflowStep {\n                    id\n                  }\n                }\n                parameters {\n                  parameterType\n                  getOptionsForFieldValue\n                parameterName\n                configurable\n                required\n                multiSelect\n                fieldValues\n                helpVariables\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}", "variables": ""}, "options": {"raw": {"language": "json"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Read All Template"}, "response": []}, {"name": "Read One Template with Variables", "request": {"body": {"mode": "graphql", "graphql": {"query": "query TemplateQuery($id: ID!) {\n\tnode(\n\t\tid: $id\n\t) {\n\t\t... on Workflows_Template {\n\t\t\tname\n\t\t\tid\n\t\t\tdisplayName\n\t\t\tversion\n\t\t\tdescription\n\t\t\tcategory\n\t\t\tworkflowSteps {\n\t\t\t\tedges {\n\t\t\t\t\tnode {\n\t\t\t\t\t    name\n\t\t\t\t\t\tid\n\t\t\t\t\t\trequired\n\t\t\t\t\t\tsequence\n\t\t\t\t\t\ttrigger {\n\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\tparameters {\n\t\t\t\t\t\t\t\tparameterName\n\t\t\t\t\t\t\t\tfieldValues\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t\tpossibleFieldValues\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tworkflowStepCondition {\n\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\tconditionalInputParameters {\n\t\t\t\t\t\t\t\tinputParameter {\n\t\t\t\t\t\t\t\t\tmultiSelect\n\t\t\t\t\t\t\t\t\tgetOptionsForFieldValue\n\t\t\t\t\t\t\t\t\tparameterType\n\t\t\t\t\t\t\t\t\tparameterName\n\t\t\t\t\t\t\t\t\tgetOptionsForFieldValue\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tsupportedOperators {\n\t\t\t\t\t\t\t\t\tsymbol\n\t\t\t\t\t\t\t\t\tdescription\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t  ruleLines{\n                edges{\n                  node{\n                    rules{\n                      parameterName\n\t\t\t\t\t\tconditionalExpression\n                    }\n\t\t\t\t\tmappedActionKeys\n                  }\n                }\n              }\n\t\t\t\t\t\t}\n\t\t\t\t\t\tactions {\n\t\t\t\t\t\t\tactionKey\n\t\t\t\t\t\t\taction {\n\t\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\t\tname\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t\tselected\n\t\t\t\t\t\t\t\tnexts {\n\t\t\t\t\t\t\t\t\tnextType\n\t\t\t\t\t\t\t\t\tnextAction {\n\t\t\t\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tnextWorkflowStep {\n\t\t\t\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tparameters {\n\t\t\t\t\t\t\t\t\tparameterType\n\t\t\t\t\t\t\t\t\tgetOptionsForFieldValue\n\t\t\t\t\t\t\t\t\tparameterName\n\t\t\t\t\t\t\t\t\tconfigurable\n\t\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t\t\tmultiSelect\n\t\t\t\t\t\t\t\t\thelpVariables\n\t\t\t\t\t\t\t\t\tfieldValues\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}", "variables": "{\n\"id\" : \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:8b9d336a-c9a3-4279-bb98-55933591279b\"\n}"}, "options": {"raw": {"language": "json"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Read All Template"}, "response": []}, {"name": "READ ALL TEMPLATE DEFINITION", "request": {"body": {"mode": "graphql", "graphql": {"query": "query workflows{\n  workflows{\n    templates{\n      edges{\n        node{\n          name\n          id\n          version\n          description\n          category\n          displayName\n          allowMultipleDefinitions\n         \n        }\n      }\n    }\n  }\n}", "variables": ""}}, "method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "intuit_tid", "value": "{{$guid}}", "type": "text"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Read All Template Definition\nhttps://workflowautomation-qal.api.intuit.com/v4/graphql?"}, "response": []}, {"name": "Write One Definition Query", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {  \ncreateWorkflows_Definition(input: $input_0) {\nworkflowsDefinitionEdge {\nnode {\nid\ndescription\nname\ndisplayName\ntemplate {\nid \n}\nmeta {\ncreated\nupdated\ncreatedBy {\nid\n}\n}    \nrecordType\nworkflowSteps {\nedges {\nnode {\n    id\ntrigger {\nid\nparameters {\nparameterName\nfieldValues\nrequired\npossibleFieldValues\n}\n}\nworkflowStepCondition {\nid\nruleLines {\nedges {\nnode {\nmappedActionKeys\nrules {\nconditionalExpression\nparameterName\nselectedlogicalGroupingOperator\n\n}\n}\n}\n}\n}\nactions {\nactionKey\naction {\nname\nid\nrequired\nparameters {\n   parameterName\nparameterType\nhelpVariables\ngetOptionsForFieldValue\nfieldValues\nconfigurable\nmultiSelect\n}\n}\n}\n}\n}\n}\n\n}\n\n}\n\n}\n}", "variables": "\n{\n    \"input_0\": {\n      \"clientMutationId\": \"InvoiceAppo\",\n      \"workflowsDefinition\": {\n        \"status\": \"ENABLED\",\n        \"name\": \"invoiceapproval\",\n        \"displayName\": \"Test-write\",\n        \"template\": {\n          \"id\": \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OmI4MDJjYjFkZTM:cc41c653-418e-4aa2-9379-f8fc1b612ac4\"\n        },\n        \"recordType\": \"invoice\",\n        \"workflowSteps\": [\n          {\n            \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:newInvoiceCreated_invoiceapproval\",\n            \"workflowStepCondition\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:decision_invoiceapproval\",\n              \"ruleLines\": [\n                {\n                  \"rules\": [\n                    {\n                      \"parameterName\": \"Amount\",\n                      \"conditionalExpression\": \"EQ 0\"\n                    }\n                  ],\n                  \"mappedActionKeys\": [\n                    \"approvalRequired\"\n                  ]\n                }\n              ]\n            },\n            \"actions\": [\n              {\n                \"actionKey\": \"approvalRequired\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendForApproval_invoiceapproval\",\n                  \"name\": \"Send for Approval\",\n                  \"required\": true,\n                  \"selected\": true,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:1\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"505836/521143\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \\nRegards\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Approver #1\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Send To\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Subject\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice [[DocNumber]] requires approval\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              }\n            ]\n          },\n          {\n            \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:waitForTimerToElapse1_invoiceApproval\",\n            \"trigger\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmExOTdjZjJhOTY:waitForTimerToElapse1_invoiceApproval\",\n              \"required\": true,\n              \"parameters\": [\n                {\n                  \"parameterName\": \"mappings\",\n                  \"fieldValues\": null,\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"send reminder to approver:sendReminderEmail_invoiceApproval\",\n                    \"Auto approve invoice:autoUpdateAsApproved_invoiceApproval\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"send reminder to approver\",\n                  \"fieldValues\": [\n                    \"send reminder to approver\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"send reminder to approver\",\n                    \"Auto approve invoice\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"Not Approved\",\n                  \"fieldValues\": [\n                    \"Not Approved\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"Not Approved\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"waitTime\",\n                  \"fieldValues\": [\n                    \"5\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": null,\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                }\n              ],\n              \"__typename\": \"Workflows_Trigger\"\n            },\n            \"actions\": [\n              {\n                \"actionKey\": \"sendApprovalReminder\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendReminderEmail_invoiceApproval\",\n                  \"name\": \"Send reminder\",\n                  \"required\": false,\n                  \"selected\": false,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"505836/521143\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \\nRegards\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Approver #1\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Send To\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Subject\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice [[DocNumber]] requires approval\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              },\n              {\n                \"actionKey\": \"autoUpdate\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:autoUpdateAsApproved_invoiceApproval\",\n                  \"name\": \"Auto approve invoice status\",\n                  \"required\": true,\n                  \"selected\": false,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"ACTION\",\n                      \"nextAction\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval\",\n                        \"__typename\": \"Workflows_Action\"\n                      },\n                      \"nextWorkflowStep\": null,\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"intuit-workflows/520402\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              },\n              {\n                \"actionKey\": null,\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval\",\n                  \"name\": \"Send notification to creator of invoice\",\n                  \"required\": false,\n                  \"selected\": true,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"intuit-workflows/520508\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"false\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"InvoiceNumber\",\n                        \"InvoiceDate\",\n                        \"ApproverName\",\n                        \"ApprovalStatusChangedDate\"\n                      ],\n                      \"fieldValues\": [\n                        \"Hi,\\n\\nInvoice for [[CustomerName]] for $[[Amount]] got approved.\\n\\nWarm regards,\\nQBO\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              }\n            ],\n            \"workflowStepCondition\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:evaluateUserDefinedAction_invoiceApproval\",\n              \"conditionalInputParameters\": [\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": true,\n                    \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"To\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": false,\n                    \"getOptionsForFieldValue\": null,\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"Message\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": true,\n                    \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"To\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": false,\n                    \"getOptionsForFieldValue\": null,\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"Subject\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                }\n              ],\n              \"ruleLines\": [\n                {\n                  \"mappedActionKeys\": [\n                    \"sendReminderEmail_invoiceApproval\"\n                  ],\n                  \"rules\": [\n                    {\n                      \"conditionalExpression\": \"${true}\"\n                    }\n                  ]\n                },\n                {\n                  \"mappedActionKeys\": [\n                    \"autoUpdateAsApproved_invoiceApproval\"\n                  ],\n                  \"rules\": [\n                    {\n                      \"conditionalExpression\": \"${false}\"\n                    }\n                  ]\n                }\n              ],\n              \"__typename\": \"Workflows_WorkflowStepCondition\"\n            }\n          }\n        ]\n      }\n    }\n\n}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{$guid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Update One Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {  \ncreateWorkflows_Definition(input: $input_0) {\nworkflowsDefinitionEdge {\nnode {\nid\ndescription\nname\ndisplayName\ntemplate {\nid \n}\nmeta {\ncreated\nupdated\ncreatedBy {\nid\n}\n}    \nrecordType\nworkflowSteps {\nedges {\nnode {\n    id\ntrigger {\nid\nparameters {\nparameterName\nfieldValues\nrequired\npossibleFieldValues\n}\n}\nworkflowStepCondition {\nid\nruleLines {\nedges {\nnode {\nmappedActionKeys\nrules {\nconditionalExpression\nparameterName\nselectedlogicalGroupingOperator\n\n}\n}\n}\n}\n}\nactions {\nactionKey\naction {\nname\nid\nrequired\nparameters {\nparameterType\nhelpVariables\ngetOptionsForFieldValue\nfieldValues\nconfigurable\nmultiSelect\n}\n}\n}\n}\n}\n}\n\n}\n\n}\n\n}\n}", "variables": "\n{\n    \"input_0\": {\n      \"clientMutationId\": \"InvoiceAppo\",\n      \"workflowsDefinition\": {\n        \"status\": \"DISABLED\",\n        \"name\": \"invoiceapproval\",\n        \"displayName\": \"Local Update Test Update-test\",\n        \"id\" : \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OjkxMjM4YWJmYjY:d4fde5fd-8936-11ea-baeb-7202e7c9cf80\",\n        \"template\": {\n          \"id\": \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OmI4MDJjYjFkZTM:39a81c4e-33a3-4139-b212-c23b0a7db3df\"\n        },\n        \"recordType\": \"invoice\",\n        \"workflowSteps\": [\n          {\n            \"id\": \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OmMyNjEwYmRmYWI:newInvoiceCreated_invoiceapproval_9130350327552966_cb7e3a1e-bbae-4d34-b66c-4b0ebf3ca896\",\n            \"workflowStepCondition\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:decision_invoiceapproval\",\n              \"ruleLines\": [\n                {\n                  \"rules\": [\n                    {\n                      \"parameterName\": \"Amount\",\n                      \"conditionalExpression\": \"EQ 100\"\n                    }\n                  ],\n                  \"mappedActionKeys\": [\n                    \"approvalRequired\"\n                  ]\n                }\n              ]\n            },\n            \"actions\": [\n              {\n                \"actionKey\": \"approvalRequired\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendForApproval_invoiceapproval\",\n                  \"name\": \"Send for Approval\",\n                  \"required\": true,\n                  \"selected\": true,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:1\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"505836/521143\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \\nRegards\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Approver #1\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Send To\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Subject\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice [[DocNumber]] requires approval\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              }\n            ]\n          },\n          {\n            \"id\": \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OmMyNjEwYmRmYWI:waitForTimerToElapse1_invoiceApproval_9130350327552966_cb7e3a1e-bbae-4d34-b66c-4b0ebf3ca896\",\n            \"trigger\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmExOTdjZjJhOTY:waitForTimerToElapse1_invoiceApproval\",\n              \"required\": true,\n              \"parameters\": [\n                {\n                  \"parameterName\": \"mappings\",\n                  \"fieldValues\": null,\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"send reminder to approver:sendReminderEmail_invoiceApproval\",\n                    \"Auto approve invoice:autoUpdateAsApproved_invoiceApproval\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"send reminder to approver\",\n                  \"fieldValues\": [\n                    \"send reminder to approver\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"send reminder to approver\",\n                    \"Auto approve invoice\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"Not Approved\",\n                  \"fieldValues\": [\n                    \"Not Approved\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": [\n                    \"Not Approved\"\n                  ],\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                },\n                {\n                  \"parameterName\": \"waitTime\",\n                  \"fieldValues\": [\n                    \"5\"\n                  ],\n                  \"required\": true,\n                  \"possibleFieldValues\": null,\n                  \"__typename\": \"Workflows_Definitions_InputParameter\"\n                }\n              ],\n              \"__typename\": \"Workflows_Trigger\"\n            },\n            \"actions\": [\n              {\n                \"actionKey\": \"sendApprovalReminder\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendReminderEmail_invoiceApproval\",\n                  \"name\": \"Send reminder\",\n                  \"required\": false,\n                  \"selected\": false,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"505836/521143\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \\nRegards\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Approver #1\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                      \"parameterName\": \"Send To\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": true,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"<EMAIL>\",\n                        \"<EMAIL>\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Subject\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"DocNumber\",\n                        \"InvoiceDate\",\n                        \"DueDate\",\n                        \"Balance\",\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [\n                        \"Invoice [[DocNumber]] requires approval\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              },\n              {\n                \"actionKey\": \"autoUpdate\",\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:autoUpdateAsApproved_invoiceApproval\",\n                  \"name\": \"Auto approve invoice status\",\n                  \"required\": true,\n                  \"selected\": false,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"ACTION\",\n                      \"nextAction\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval\",\n                        \"__typename\": \"Workflows_Action\"\n                      },\n                      \"nextWorkflowStep\": null,\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"intuit-workflows/520402\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              },\n              {\n                \"actionKey\": null,\n                \"action\": {\n                  \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval\",\n                  \"name\": \"Send notification to creator of invoice\",\n                  \"required\": false,\n                  \"selected\": true,\n                  \"nexts\": [\n                    {\n                      \"nextType\": \"WORFKLOWSTEP\",\n                      \"nextAction\": null,\n                      \"nextWorkflowStep\": {\n                        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3\",\n                        \"__typename\": \"Workflows_WorkflowStep\"\n                      },\n                      \"__typename\": \"Workflows_Action_Next\"\n                    }\n                  ],\n                  \"parameters\": [\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"actionName\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"executeWorkflowAction\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"taskHandler\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"appconnect\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"handlerId\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"intuit-workflows/520508\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"required\",\n                      \"configurable\": null,\n                      \"required\": null,\n                      \"multiSelect\": null,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"false\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"CC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"My Company Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"BCC\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"Customer Email\"\n                      ],\n                      \"fieldValues\": [],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"STRING\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Message\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": [\n                        \"CustomerName\",\n                        \"Amount\",\n                        \"InvoiceNumber\",\n                        \"InvoiceDate\",\n                        \"ApproverName\",\n                        \"ApprovalStatusChangedDate\"\n                      ],\n                      \"fieldValues\": [\n                        \"Hi,\\n\\nInvoice for [[CustomerName]] for $[[Amount]] got approved.\\n\\nWarm regards,\\nQBO\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send notification\",\n                      \"configurable\": false,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    },\n                    {\n                      \"parameterType\": \"BOOLEAN\",\n                      \"getOptionsForFieldValue\": null,\n                      \"parameterName\": \"Send email\",\n                      \"configurable\": true,\n                      \"required\": true,\n                      \"multiSelect\": false,\n                      \"helpVariables\": null,\n                      \"fieldValues\": [\n                        \"true\"\n                      ],\n                      \"__typename\": \"Workflows_Definitions_InputParameter\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Action\"\n                },\n                \"__typename\": \"Workflows_WorkflowStep_ActionMapper\"\n              }\n            ],\n            \"workflowStepCondition\": {\n              \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:evaluateUserDefinedAction_invoiceApproval\",\n              \"conditionalInputParameters\": [\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": true,\n                    \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"To\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": false,\n                    \"getOptionsForFieldValue\": null,\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"Message\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": true,\n                    \"getOptionsForFieldValue\": \"GET_ADMINS_ID\",\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"To\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                },\n                {\n                  \"inputParameter\": {\n                    \"multiSelect\": false,\n                    \"getOptionsForFieldValue\": null,\n                    \"parameterType\": \"STRING\",\n                    \"parameterName\": \"Subject\",\n                    \"__typename\": \"Workflows_Definitions_InputParameter\"\n                  },\n                  \"supportedOperators\": [\n                    {\n                      \"symbol\": \"CONTAINS\",\n                      \"description\": \"Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    },\n                    {\n                      \"symbol\": \"NOT_CONTAINS\",\n                      \"description\": \"Not Within\",\n                      \"__typename\": \"Workflows_Definitions_Operator\"\n                    }\n                  ],\n                  \"__typename\": \"Workflows_Definitions_ConditionalParameter\"\n                }\n              ],\n              \"ruleLines\": [\n                {\n                  \"mappedActionKeys\": [\n                    \"sendReminderEmail_invoiceApproval\"\n                  ],\n                  \"rules\": [\n                    {\n                      \"conditionalExpression\": \"${true}\"\n                    }\n                  ]\n                },\n                {\n                  \"mappedActionKeys\": [\n                    \"autoUpdateAsApproved_invoiceApproval\"\n                  ],\n                  \"rules\": [\n                    {\n                      \"conditionalExpression\": \"${false}\"\n                    }\n                  ]\n                }\n              ],\n              \"__typename\": \"Workflows_WorkflowStepCondition\"\n            }\n          }\n        ]\n      }\n    }\n\n}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{$guid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Update One Definition[2]", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {\n  createWorkflows_Definition(input:$input_0){\n  workflowsDefinitionEdge{\n    node\n    {\n    id\n    description\n    name\n    recordType\n    displayName\n    template {\n      id\n    }\n    workflowSteps {\n      edges {\n         \n      \tnode {\n      \t     id\n  \t\t\ttrigger {\n            id\n          }\n          workflowStepCondition {\n            id\n          }\n        \tactions {\n        \t  actionKey\n        \t}\n        }\n      }\n    }\n  }\n }\n}\n}\n\n\n", "variables": "{\n  \"input_0\": {\n    \"clientMutationId\": \"InvoiceAppo\",\n    \"workflowsDefinition\": {\n     \"description\": \"concurrency_test_201\",\n      \"status\": \"ENABLED\",\n      \"name\": \"garume1\",\n      \"recordType\": \"invoice\",\n      \"displayName\": \"garume1\",\n       \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjkxMjM4YWJmYjY:bf40f863-73f1-11ea-841c-02020567fee2\",\n      \"template\": {\n        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:a278f921-0a61-4bcb-a4e3-9605908b2de3\"\n      },\n      \"workflowSteps\": [\n        {\n          \"id\": \"djQuMToxMjM0OmI4MDJjYjFkZTM:waitForTimerToElapse1_invoiceApproval_9130347798120106_12e52fad-0b58-4844-9340-55171f4f9393\",\n          \"trigger\": {\n            \"name\": \"Send approval something\",\n            \"id\": \"djQuMToxMjM0OmExOTdjZjJhOTY:waitForTimerToElapse1_invoiceApproval\",\n            \"parameters\": {\n              \"parameterName\": \"waitTime\",\n              \"fieldValues\": [\n                \"8\"\n              ]\n            }\n          },\n          \"actions\": [\n            {\n              \"action\": {\n                \"id\": \"djQuMToxMjM0OmI4MDJjYjFkZTM:sendForApproval_invoiceapproval\",\n                \"parameters\": [\n                  {\n                    \"parameterName\": \"To\",\n                    \"fieldValues\": [\n                      \"abc\",\n                      \"def\"\n                    ]\n                  },\n                  {\n                  \"parameterName\": \"Approver #1\",\n                  \"fieldValues\": [\n                    \"<EMAIL>\"\n                  ]\n                },\n                  {\n                    \"parameterName\": \"invoice\",\n                    \"fieldValues\": [\n                      \"abc\",\n                      \"def\"\n                    ]\n                  }\n                ]\n              }\n            }\n          ],\n          \"workflowStepCondition\": {\n            \"id\": \"djQuMToxMjM0OmI4MDJjYjFkZTM:decision_invoiceapproval\",\n            \"ruleLines\": [\n              {\n                \"rules\": [\n                  {\n                    \"parameterName\": \"Amount\",\n                    \"conditionalExpression\": \"GT 100\"\n                  },{\n                  \t\"parameterName\":\"Customer\",\n                  \t\"conditionalExpression\" : \"CONTAINS 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100\"\n                  }\n                ],\n                \"mappedActionKeys\": [\n                  \"approvalRequired\"\n                ]\n              }\n            ]\n          }\n        },\n        {\n          \"id\": \"djQuMToxMjM0OmI4MDJjYjFkZTM:evaluateUserDefinedAction_invoiceApproval_9130347798120106_12e52fad-0b58-4844-9340-55171f4f9393\",\n          \"workflowStepCondition\": {\n            \"id\": \"djQuMToxMjM0OmI4MDJjYjFkZTM:evaluateUserDefinedAction_invoiceApproval\",\n            \"ruleLines\": [\n              {\n                \"mappedActionKeys\": [\n                  \"sendReminderEmail_invoiceApproval\"\n                ],\n                \"rules\": [\n                  {\n                    \"conditionalExpression\": \"true\"\n                  }\n                ]\n              },\n              {\n                \"mappedActionKeys\": [\n                  \"autoUpdateAsApproved_invoiceApproval\"\n                ],\n                \"rules\": [\n                  {\n                    \"conditionalExpression\": \"false\"\n                  }\n                ]\n              }\n            ]\n          }\n        }\n      ]\n    }\n  }\n}\n"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{$guid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Delete one Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {\n  createWorkflows_Definition(input:$input_0){\n  workflowsDefinitionEdge{\n    node\n    {\n    id\n    description\n    name\n    recordType\n    status\n    workflowSteps {\n      edges {\n      \tnode {\n      \t   id\n  \t\t\ttrigger {\n            id\n            name\n          }\n          workflowStepCondition {\n            id\n            ruleLines {\n              edges {\n                node {\n                  mappedActionKeys\n                  rules {\n                    conditionalExpression\n                    parameterName\n                    selectedlogicalGroupingOperator\n                  }\n                }\n              }\n            }\n          }\n        \tactions {\n        \t  actionKey\n        \t}\n        }\n      }\n    }\n  }\n }\n}\n}\n\n\n", "variables": "{\n  \"input_0\": {\n    \"clientMutationId\": \"InvoiceAppo\",\n    \"workflowsDefinition\": {\n \n      \"deleted\" : true,\n      \"id\" : \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjkxMjM4YWJmYjY:17436df6-7979-11ea-aa9b-f6c9ecd25363\"\n      }\n  }\n}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Disable Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {\n  createWorkflows_Definition(input:$input_0){\n  workflowsDefinitionEdge{\n    node\n    {\n    id\n    description\n    name\n    recordType\n    template {\n      id\n    }\n  }\n }\n}\n}\n\n\n", "variables": "{\n  \"input_0\": {\n    \"clientMutationId\": \"InvoiceAppo\",\n    \"workflowsDefinition\": {\n      \"description\": \"Disable GTE Definition\",\n      \"status\": \"DISABLED\",\n      \"name\": \"Disable GTE Definition\",\n      \"recordType\" : \"invoice\",\n      \n      \"id\" : \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjkxMjM4YWJmYjY:04845600-7fd3-11ea-aa9b-f6c9ecd25363\",\n      \"template\": {\n        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:96509c8d-f232-464f-a2aa-7eec1b1aaf5b\"\n      }\n      }\n  }\n}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Enable Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {\n  createWorkflows_Definition(input:$input_0){\n  workflowsDefinitionEdge{\n    node\n    {\n    id\n    description\n    name\n    recordType\n    template {\n      id\n    }\n  }\n }\n}\n}\n\n\n", "variables": "{\n  \"input_0\": {\n    \"clientMutationId\": \"InvoiceAppo\",\n    \"workflowsDefinition\": {\n      \"description\": \"Disable GTE Definition\",\n      \"status\": \"ENABLED\",\n      \"name\": \"Disable GTE Definition\",\n      \"recordType\" : \"invoice\",\n      \n      \"id\" : \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjkxMjM4YWJmYjY:373b02f7-7fd4-11ea-9823-9ad1d595bf2f\",\n      \"template\": {\n        \"id\": \"djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:96509c8d-f232-464f-a2aa-7eec1b1aaf5b\"\n      }\n      }\n  }\n}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}, {"key": "Authorization", "type": "text", "value": "{{Authorization}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition"}, "response": []}, {"name": "Read All Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "{\n  workflows{\n    definitions{\n      edges{\n        node{\n          name\n          id\n          description\n          displayName\n          recordType\n          version\n          status\n          workflowSteps{\n              edges{\n                  node{\n                      id\n                  }\n              }\n          }\n          template{\n            id\n          }\n          meta{\n            created\n            createdBy {\n              id\n            }\n          }\n        }\n      }\n    }\n  }\n}", "variables": ""}}, "method": "POST", "header": [{"key": "intuit_tid", "value": "{{intuit_tid}}", "type": "text"}, {"key": "Authorization", "value": "{{Authorization}}", "type": "text"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Read All Definition"}, "response": []}, {"name": "Read One Definition", "request": {"body": {"mode": "graphql", "graphql": {"query": "query q{\n\tnode(id: \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OjkxMjM4YWJmYjY:9caaabdc-896e-11ea-baeb-7202e7c9cf80\") {\n    ... on Workflows_Definition {\n      name\n      id\n      description\n      displayName\n      recordType\n      meta{\n            created\n            createdBy {\n              id\n            }\n          }\n      template{\n        name\n        id\n        description\n        workflowSteps {\n        edges {\n          node {\n            id\n            sequence\n            required\n            trigger{\n              name\n              id\n              required\n              parameters{\n                  parameterName\n                 parameterType\n                  helpVariables\n                  getOptionsForFieldValue\n                  fieldValues\n                  configurable\n                  multiSelect\n                  possibleFieldValues\n              }\n            }\n            actions{\n              actionKey\n              action{\n                name\n                id\n                required\n                selected\n                parameters{\n                  parameterType\n                  helpVariables\n                  getOptionsForFieldValue\n                  fieldValues\n                  configurable\n                  multiSelect\n                  possibleFieldValues\n                }\n                nexts{\n                  nextType\n                  nextAction {\n                    id\n                  }\n                  nextWorkflowStep {\n                    id\n                  }\n                }\n              }\n            }\n            workflowStepCondition{\n              id\n              required\n              description\n              deleted\n              ruleLines{\n                edges{\n                  node{\n                    mappedActionKeys\n                    rules{\n                      conditionalExpression\n                      parameterName\n                      conditionalExpression\n                      selectedlogicalGroupingOperator\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n      }\n    }\n  }\n}", "variables": ""}}, "method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}", "type": "text"}, {"key": "intuit_tid", "value": "{{intuit_tid}}", "type": "text"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "https://localhost.intuit.com:8443/v4/graphql?"}, "response": []}, {"name": "Read One Definition With Variables", "request": {"body": {"mode": "graphql", "graphql": {"query": "query DefinitionQuery($id: ID!) {\n    node(\n        id: $id\n    )  {\n    ... on Workflows_Definition {\n      name\n      id\n      description\n      displayName\n      recordType\n      meta{\n            created\n            createdBy {\n              id\n            }\n          }\n      template{\n        name\n        id\n        description\n        workflowSteps {\n        edges {\n          node {\n            id\n            sequence\n            required\n            trigger{\n              name\n              id\n              required\n              description\n              parameters{\n                 parameterType\n                  helpVariables\n                  getOptionsForFieldValue\n                  fieldValues\n                  configurable\n                  multiSelect\n              }\n            }\n            actions{\n              actionKey\n              action{\n                name\n                id\n                required\n                selected\n                parameters{\n                  parameterType\n                  helpVariables\n                  getOptionsForFieldValue\n                  fieldValues\n                  configurable\n                  multiSelect\n                }\n                nexts{\n                  nextType\n                  nextAction {\n                    id\n                  }\n                  nextWorkflowStep {\n                    id\n                  }\n                }\n              }\n            }\n            workflowStepCondition{\n              id\n              required\n              description\n              deleted\n              ruleLines{\n                edges{\n                  node{\n            \n                    rules{\n                      conditionalExpression\n                      parameterName\n                      conditionalExpression\n                      selectedlogicalGroupingOperator\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n      }\n    }\n  }\n}", "variables": "{\n\"id\" : \"djQuMTo5MTMwMzUwMzI3NTUyOTY2OjkxMjM4YWJmYjY:e300c98e-8867-11ea-9cb5-8e5c04b74379\"\n}"}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{intuit_tid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "https://localhost.intuit.com:8443/v4/graphql?"}, "response": []}, {"name": "Save Template", "request": {"body": {"mode": "formdata", "formdata": [{"key": "template_metadata", "value": "{\"status\":\"enabled\",\"creatorType\":\"system\",\"allowMultipleDefs\":false}", "type": "text"}, {"key": "files", "type": "file", "src": "/Users/<USER>/Documents/Project/Workflow/workflow-automation-service/app/src/main/resources/templates/prod/invoiceapproval.bpmn"}, {"key": "files", "type": "file", "src": "/Users/<USER>/Documents/Project/Workflow/workflow-automation-service/app/src/main/resources/templates/prod/decision_invoiceapproval.dmn"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "intuit_tid", "value": "{{intuit_tid}}"}, {"key": "Content-Type", "value": "{{Content-Type}}"}], "url": {"raw": "{{baseUrl}}{{saveUrl}}", "host": ["{{baseUrl}}{{saveUrl}}"]}, "description": "Updating a pre-canned template"}, "response": []}, {"name": "Update Template", "request": {"body": {"mode": "formdata", "formdata": [{"key": "template_metadata", "value": "{\"status\":\"enabled\",\"creatorType\":\"system\",\"allowMultipleDefs\":false}", "type": "text"}, {"key": "files", "type": "file", "src": []}, {"key": "files", "type": "file", "src": "/Users/<USER>/Documents/Project/Workflow/workflow-automation-service/app/src/main/resources/templates/prod/decision_invoiceapproval.dmn"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "{{Authorization}}"}, {"key": "intuit_tid", "value": "{{intuit_tid}}"}, {"key": "Content-Type", "value": "{{Content-Type}}"}], "url": {"raw": "{{baseUrl}}{{updateUrl}}", "host": ["{{baseUrl}}{{updateUrl}}"]}, "description": "Updating the Template"}, "response": []}, {"name": "Trigger Request", "request": {"body": {"mode": "raw", "raw": "{ \n   \"eventHeaders\":{ \n   \t  \"workflow\":\"approval\",\n   \t  \"entityType\":\"invoice\",\n      \"entityChangeType\":\"approved\"\n   },\n   \"entity\":{ \n      \"Invoice\":{ \n         \"CurrencyRef\":{ \n            \"name\":\"United States Dollar\",\n            \"value\":\"USD\"\n         },\n         \"EmailStatus\":\"NotSet\",\n         \"AllowOnlineACHPayment\":false,\n         \"AllowIPNPayment\":false,\n         \"MetaData\":{ \n            \"CreateTime\":\"2020-01-30T21:37:01-08:00\",\n            \"LastUpdatedTime\":\"2020-01-30T21:37:01-08:00\"\n         },\n         \"DocNumber\":\"1013\",\n         \"PrintStatus\":\"NeedToPrint\",\n         \"DueDate\":\"2020-03-01\",\n         \"LinkedTxn\":[ \n         ],\n         \"AllowOnlinePayment\":false,\n         \"TxnDate\":\"2020-01-31\",\n         \"DepartmentRef\":{ \n            \"name\":\"dep1\",\n            \"value\":\"1\"\n         },\n         \"Line\":[ \n            { \n               \"LineNum\":1,\n               \"DetailType\":\"SalesItemLineDetail\",\n               \"Amount\":2,\n               \"SalesItemLineDetail\":{ \n                  \"TaxCodeRef\":{ \n                     \"value\":\"NON\"\n                  },\n                  \"ItemAccountRef\":{ \n                     \"name\":\"Services\",\n                     \"value\":\"4\"\n                  },\n                  \"ItemRef\":{ \n                     \"name\":\"Gardening\",\n                     \"value\":\"3\"\n                  }\n               },\n               \"Id\":\"1\"\n            },\n            { \n               \"SubTotalLineDetail\":{ \n               },\n               \"DetailType\":\"SubTotalLineDetail\",\n               \"Amount\":2\n            }\n         ],\n         \"SyncToken\":\"0\",\n         \"sparse\":false,\n         \"TotalAmt\":100,\n         \"domain\":\"QBO\",\n         \"CustomField\":[ \n         ],\n         \"SalesTermRef\":{ \n            \"value\":\"3\"\n         },\n         \"Id\":\"22\",\n         \"Tag\":[ \n         ],\n         \"AllowOnlineCreditCardPayment\":false,\n         \"CustomerRef\":{ \n            \"name\":\"Customer1\",\n            \"value\":\"100\"\n         },\n         \"Balance\":2,\n         \"ApplyTaxAfterDiscount\":false\n      },\n      \"time\":\"2020-01-30T21:37:28.661-08:00\"\n   }\n}", "options": {"raw": {"language": "json"}}}, "method": "POST", "header": [{"key": "Authorization", "value": "Intuit_IAM_Authentication intuit_appid=Intuit.appintgwkflw.wkflautomate.qbowasapiclient,intuit_app_secret=XXXXXXXXXX,intuit_token=<Token_id>,intuit_userid=<User_Id>,intuit_token_type=IAM-Ticket,intuit_realmid=<Company_Id>", "type": "text"}, {"key": "intuit_tid", "value": "{{intuit_tid}}", "type": "text"}], "url": {"raw": "{{baseUrl}}v1/trigger", "host": ["{{baseUrl}}v1"], "path": ["trigger"]}}, "response": []}, {"response": [], "name": "Subscription API", "request": {"body": {"mode": "raw", "raw": "{\n    \"intuitAppId\":\"AP13072\",\n    \"companyId\": \"<Company_Id>\"\n}"}, "method": "GET", "header": [{"key": "Authorization", "value": "INTUIT_IAM x_intuit_authid=<Auth_Id>,x_intuit_ticket=<Token_id>", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://stage.api.appconnect.intuit.com/api/v1/subscriptions?intuitAppId=AP13072&companyId=<Company_Id>", "path": ["api", "v1", "subscriptions"], "query": [{"key": "intuitAppId", "value": "AP13072"}, {"key": "companyId", "value": "<Company_Id>"}], "protocol": "https", "host": ["stage", "api", "appconnect", "intuit", "com"]}, "description": "https://stage.api.appconnect.intuit.com/api/v1/subscriptions?intuitAppId=AP13276&companyId=<Company_Id>"}, "protocolProfileBehavior": {"disableBodyPruning": true}}, {"name": "Unsubscribe APIs", "request": {"body": {"mode": "raw", "raw": "{\n    \"intuitAppId\":\"AP13276\",\n    \"companyId\": \"<Company_Id>\"\n}"}, "method": "POST", "header": [{"key": "Authorization", "value": "INTUIT_IAM x_intuit_authid=<AUTH_ID>,x_intuit_ticket=<TOKEN_ID>", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://stage.api.appconnect.intuit.com/api/v1/subscriptions?intuitAppId=AP13276&companyId=<Company_Id>", "path": ["api", "v1", "subscriptions"], "query": [{"key": "intuitAppId", "value": "AP13276"}, {"key": "companyId", "value": "<Company_Id>"}], "protocol": "https", "host": ["stage", "api", "appconnect", "intuit", "com"]}, "description": "https://stage.api.appconnect.intuit.com/api/v1/subscriptions?intuitAppId=AP13276&companyId=<Company_Id>"}, "response": []}, {"name": "Evaluate Rules API", "request": {"body": {"mode": "raw", "raw": "{\n   \"eventHeaders\":{ \n      \"entityChangeType\":\"updated\",\n      \"entityType\":\"invoice\",\n      \"workflow\":\"approval\"\n   },\n   \"entity\":{ \n      \"Invoice\":{ \n         \"CurrencyRef\":{ \n            \"name\":\"United States Dollar\",\n            \"value\":\"USD\"\n         },\n         \"EmailStatus\":\"NotSet\",\n         \"AllowOnlineACHPayment\":false,\n         \"AllowIPNPayment\":false,\n         \"MetaData\":{ \n            \"CreateTime\":\"2020-01-30T21:37:01-08:00\",\n            \"LastUpdatedTime\":\"2020-01-30T21:37:01-08:00\"\n         },\n         \"DocNumber\":\"610\",\n         \"PrintStatus\":\"NeedToPrint\",\n         \"DueDate\":\"2020-03-01\",\n         \"LinkedTxn\":[ \n         ],\n         \"AllowOnlinePayment\":false,\n         \"TxnDate\":\"2020-01-31\",\n         \"DepartmentRef\":{ \n            \"name\":\"dep1\",\n            \"value\":\"1\"\n         },\n         \"Line\":[ \n            { \n               \"LineNum\":1,\n               \"DetailType\":\"SalesItemLineDetail\",\n               \"Amount\":2,\n               \"SalesItemLineDetail\":{ \n                  \"TaxCodeRef\":{ \n                     \"value\":\"NON\"\n                  },\n                  \"ItemAccountRef\":{ \n                     \"name\":\"Services\",\n                     \"value\":\"4\"\n                  },\n                  \"ItemRef\":{ \n                     \"name\":\"Gardening\",\n                     \"value\":\"3\"\n                  }\n               },\n               \"Id\":\"1\"\n            },\n            { \n               \"SubTotalLineDetail\":{ \n               },\n               \"DetailType\":\"SubTotalLineDetail\",\n               \"Amount\":2\n            }\n         ],\n         \"SyncToken\":\"0\",\n         \"sparse\":false,\n         \"TotalAmt\":1000,\n         \"domain\":\"QBO\",\n         \"CustomField\":[ \n         ],\n         \"SalesTermRef\":{ \n            \"value\":\"3\"\n         },\n         \"Id\":\"2\",\n         \"Tag\":[ \n         ],\n         \"AllowOnlineCreditCardPayment\":false,\n         \"CustomerRef\":{ \n            \"name\":\"Customer1\",\n            \"value\":\"10\"\n         },\n         \"Balance\":1,\n         \"ApplyTaxAfterDiscount\":false\n      },\n      \"time\":\"2020-01-30T21:37:28.661-08:00\"\n   }\n}"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "intuit_tid", "value": "{{$guid}}"}, {"key": "Authorization", "value": "Intuit_IAM_Authentication intuit_appid=Intuit.appintgwkflw.wkflautomate.qbowasapiclient,intuit_app_secret=XXXXXXXXXXXX,intuit_token_type=IAM-Ticket,intuit_realmid=<REALM_ID>,intuit_token=<TOKEN>,intuit_userid=<USER_ID>"}], "url": {"raw": "{{baseUrl}}v1/evaluate-rules", "host": ["{{baseUrl}}v1"], "path": ["evaluate-rules"]}}, "response": []}, {"name": "Create Definition With Default Values", "request": {"body": {"mode": "graphql", "graphql": {"query": "mutation Workflows_Definition($input_0: Workflows_Definition_createDefinitionWithDefaultValuesInput!) {\\n  Workflows_Definition_createDefinitionWithDefaultValues(input:$input_0){\\n\\tclientMutationId\\n    workflowsDefinitionTemplateInput{\\n      id\\n      description\\n      name\\n      displayName\\n      meta {\\n        created\\n        updated\\n        createdBy {\\n          id\\n        }\\n      }\\n      recordType\\nworkflowSteps {\\nedges {\\nnode {\\n    id\\ntrigger {\\nid\\nparameters {\\nparameterName\\nfieldValues\\nrequired\\npossibleFieldValues\\n}\\n}\\nworkflowStepCondition {\\nid\\nruleLines {\\nedges {\\nnode {\\nmappedActionKeys\\nrules {\\nconditionalExpression\\nparameterName\\nselectedlogicalGroupingOperator\\n\\n}\\n}\\n}\\n}\\n}\\nactions {\\nactionKey\\naction {\\nname\\nid\\nrequired\\nparameters {\\n   parameterName\\nparameterType\\nhelpVariables\\ngetOptionsForFieldValue\\nfieldValues\\nconfigurable\\nmultiSelect\\n}\\n}\\n}\\n}\\n}\\n}    \\n    }\\n  }\\n}", "variables": "{\"input_0\":{\"clientMutationId\":\"\",\"workflowsDefinitionTemplateInput\":{\"name\":\"invoiceoverduereminder\"}}}"}, "options": {"raw": {"language": "text"}}}, "method": "POST", "header": [{"key": "Authorization", "type": "text", "value": "{{Authorization}}"}, {"key": "intuit_tid", "type": "text", "value": "{{$guid}}"}], "url": {"raw": "{{baseUrl}}{{graphQl}}", "host": ["{{baseUrl}}{{graphQl}}"]}, "description": "Write One Definition With Default Values"}, "response": []}]}