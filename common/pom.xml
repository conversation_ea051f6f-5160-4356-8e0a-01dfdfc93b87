<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
    <artifactId>workflow-automation-service-aggregator</artifactId>
    <version>1.1.20</version>
  </parent>

  <artifactId>was-common</artifactId>
  <packaging>jar</packaging>

  <dependencies>

    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>was-entity</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
      <artifactId>was-integrations</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.intuit.identity.authn</groupId>
      <artifactId>authn-java-sdk</artifactId>
      <version>${authn.sdk.version}</version>
    </dependency>
    <dependency>
      <groupId>com.intuit.sb.api</groupId>
      <artifactId>v4-sdk-schema</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>

    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.vaadin.external.google</groupId>
          <artifactId>android-json</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxjava</artifactId>
    </dependency>

    <dependency>
      <groupId>com.intuit.platform.jsk</groupId>
      <artifactId>jsk-spring-boot-starter-v4-servlet</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.hibernate.javax.persistence</groupId>
          <artifactId>hibernate-jpa-2.0-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.hibernate.validator</groupId>
          <artifactId>hibernate-validator</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>iamticket-client</artifactId>
          <groupId>com.intuit.platform.integration.iamticket.client</groupId>
        </exclusion>
        <exclusion>
          <artifactId>tomcat-embed-core</artifactId>
          <groupId>org.apache.tomcat.embed</groupId>
        </exclusion>
        <exclusion>
          <artifactId>tomcat-embed-el</artifactId>
          <groupId>org.apache.tomcat.embed</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  <dependency>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-core</artifactId>
    <version>9.0.99</version>
  </dependency>
    <!--  Added this dependency to override the version of the dependency on this same library that
         jsk-spring-boot-starter-v4-servlet brings, but which currently has a CVE. -->
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.javatuples/javatuples -->
    <dependency>
      <groupId>org.javatuples</groupId>
      <artifactId>javatuples</artifactId>
      <version>1.2</version>
    </dependency>

    <!-- Resilience4j related dependency -->
    <dependency>
      <groupId>io.github.resilience4j</groupId>
      <artifactId>resilience4j-spring-boot2</artifactId>
    </dependency>

    <dependency>
      <groupId>com.intuit.async</groupId>
      <artifactId>async-execution</artifactId>
      <version>1.0.4</version>
    </dependency>

    <dependency>
      <groupId>com.intuit.platform.jsk.spring</groupId>
      <artifactId>jsk-spring-config-idps-client</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.intuit.sbg.variability.enablers.sbg-ve-impl</groupId>
      <artifactId>sbg-ve-impl-feature-toggle</artifactId>
    </dependency>

    <dependency>
      <groupId>com.intuit.experimentation</groupId>
      <artifactId>ixp-java-sdk</artifactId>
      <version>${ixp-java-sdk}</version>
    </dependency>

    <!-- Adding Authz Dependency -->
    <dependency>
      <groupId>com.intuit.identity.authz</groupId>
      <artifactId>authz-spring-sdk</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-aws-messaging</artifactId>
      <version>2.2.6.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>com.amazonaws</groupId>
          <artifactId>aws-java-sdk-s3</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>1.12.261</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.4.2</version>
    </dependency>

    <!--	Cache dependencies	-->
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
    </dependency>
      <dependency>
          <groupId>org.postgresql</groupId>
          <artifactId>postgresql</artifactId>
      </dependency>
      <dependency>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-core</artifactId>
      </dependency>
  </dependencies>

</project>
