package com.intuit.appintgwkflw.wkflautomate.was.common.annotations;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 *     <p>This annotation is used to log metrics for all incoming calls to WAS.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Order(1)
public @interface Metric {

  /**
   * The name of metric that will be logged
   *
   * @return the metric name {@link MetricName}
   */
  MetricName name();

  /**
   * type of metric to log.By Default it logs error and latency but can be configured.
   *
   * @return metric type
   */
  MetricType[] metricType() default {MetricType.ERROR, MetricType.LATENCY};

  /**
   * type of metric {@link Type}
   *
   * @return if it is the API metric,Service metric etc.
   */
  Type type();

  /**
   * log the metric {@link boolean}
   *
   * @return if we need to log the metric or not.
   */
  boolean log() default true;
}
