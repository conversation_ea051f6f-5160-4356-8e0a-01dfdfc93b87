package com.intuit.appintgwkflw.wkflautomate.was.common.annotations;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.OTHER;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.DIRECT;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * This annotation is used to log metrics for calls being made to remote services.
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceMetric {

  /**
   * This represents the remote service we are making a call to.
   */
  ServiceName serviceName() default OTHER;

  /**
   * This represents the type of service being called.
   */
  ServiceType type() default DIRECT;

  /**
   * This represents the method of the remote service we are invoking.
   */
  String methodName() default "";
}
