package com.intuit.appintgwkflw.wkflautomate.was.common.annotations;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(Throttles.class)
public @interface Throttle {
    ThrottleAttribute attribute() default ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY;
}
