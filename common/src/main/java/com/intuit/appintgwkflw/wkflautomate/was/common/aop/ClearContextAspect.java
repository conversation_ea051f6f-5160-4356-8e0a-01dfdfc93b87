package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ClearContext;
import lombok.AllArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** <AUTHOR> This class used to clear the context. */
@Aspect
@Component
public class ClearContextAspect {

  @Autowired private WASContextHandler contextHandler;

  @After("@annotation(clearContext)")
  public void clear(final JoinPoint joinPoint, final ClearContext clearContext) {
    contextHandler.clear();
  }
}
