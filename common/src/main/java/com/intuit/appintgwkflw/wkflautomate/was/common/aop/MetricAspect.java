package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import io.opentracing.Tracer;
import java.time.Instant;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * The type Metric aspect.
 *
 * <AUTHOR>
 */
@Aspect
@Component
@AllArgsConstructor
public final class MetricAspect {

  private final MetricLogger metricLogger;

  private final Tracer tracer;

  /**
   * logs the latency and error metrics for given caller.
   *
   * <p>logs latency metrics
   *
   * <p>logs error metrics
   *
   * @param point         ProceedingJoinPoint exposes the proceed
   * @param metricDetails input metric details
   * @return object
   * @throws Throwable the exception
   */
  @Around("@annotation(metricDetails)")
  public Object metric(final ProceedingJoinPoint point, final Metric metricDetails)
      throws Throwable {

    final Instant start = Instant.now();
    try {
      return point.proceed();
    } catch (final Exception ex) {
      metricLogger.logErrorMetric(metricDetails, ex);
      throw ex;
    } finally {
      metricLogger.logLatencyMetric(metricDetails, start);
      Optional.ofNullable(tracer.activeSpan())
          .ifPresent(span -> span.setOperationName(metricDetails.name().toString().toLowerCase()));
    }
  }
}
