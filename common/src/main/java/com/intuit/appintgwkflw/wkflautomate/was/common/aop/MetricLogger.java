package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.*;
import static java.util.Objects.nonNull;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricData;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MetricsConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.AppConnectDuzzitException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.metrics.MicrometerManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import java.lang.annotation.Annotation;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Optional;

import lombok.AllArgsConstructor;
import org.hibernate.exception.GenericJDBCException;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class MetricLogger {

  public static final String REQUESTS = "requests";
  public static final String ERRORS = "errors";

  private final MicrometerManager micrometerManager;
  private final MetricsConfig metricsConfig;

  /**
   * @param metric input metric details
   * @return true/false if metric is to be logged or not
   */
  private static boolean logMetric(final Metric metric, final MetricType metricType) {

    final MetricType[] metricTypes = metric.metricType();
    if (nonNull(metricTypes)) {
      return Arrays.stream(metricTypes).anyMatch(type -> type == metricType);
    }

    return false;
  }

  /**
   * @param error {@link Throwable} input exception
   * @return workflow error from exception
   */
  private static String getWorkflowError(final Throwable error) {

    String workflowError = WorkflowError.UNKNOWN_ERROR.name();
    Optional<WorkflowError> workflowErrorOpt = Optional.empty();
    if (error instanceof WorkflowGeneralException) {
      final WorkflowGeneralException ex = ((WorkflowGeneralException) error);
      workflowErrorOpt = Optional.ofNullable(ex.getWorkflowError());
    }
    if (workflowErrorOpt.isPresent()) {
      workflowError = workflowErrorOpt.get().name();
    }
    if (error instanceof AppConnectDuzzitException) {
      workflowError = ((AppConnectDuzzitException) error).getErrorCode();
    }
    return workflowError;
  }

  /**
   * creates the metric request from metric name and type.
   *
   * @param metricName input metric name
   * @return {@link Metric}
   */
  public static Metric createMetricRequest(
          final MetricName metricName, final Type type, final boolean log, final MetricType... metricTypes) {

    return new Metric() {

      @Override
      public Class<? extends Annotation> annotationType() {

        return Metric.class;
      }

      @Override
      public MetricName name() {

        return metricName;
      }

      @Override
      public MetricType[] metricType() {

        return metricTypes;
      }

      @Override
      public Type type() {

        return type;
      }

      @Override
      public boolean log() {
        return log;
      }
    };
  }

  /**
   * @param metricName logs the error metric for given metric name
   * @param error input exception
   */
  public void logErrorMetric(final MetricName metricName, final Type type, final Throwable error) {
    logErrorMetric(createMetricRequest(metricName, type, true, MetricType.ERROR), error);
  }

  /**
   * @param metricName logs the error metric for given metric name
   * @param error input exception
   */
  public void logLatencyMetric(final MetricName metricName, final Type type, final Instant start) {
    logLatencyMetric(createMetricRequest(metricName, type, true, MetricType.LATENCY), start);
  }

  /**
   * @param metric input metric details
   * @param start start time
   */
  public void logLatencyMetric(final Metric metric, final Instant start) {

    if (logMetric(metric, MetricType.LATENCY)) {
      final Duration duration = Duration.between(start, Instant.now());
      if(metric.log()) {
        info(
                () ->
                        WorkflowLoggerRequest.builder()
                                .metricName(metric.name().name())
                                .type(metric.type().name())
                                .metricType(MetricType.LATENCY.name())
                                .latency(Long.toString(duration.toMillis())));
      }
      final MetricData metricData =
          MetricData.builder()
              .type(String.format("%s.%s", metric.type().getValue(), REQUESTS))
              .name(metric.name().name())
              .build();
      addAdditionalTags(metric.type(), metricData);
      checkAndAddAdditionalTags(metricData);
      micrometerManager.recordTime(duration, metricData);
    }
  }

  /**
   * Log error or warning metric.
   *
   * @param metric input metric details
   * @param ex the exception
   */
  public void logErrorMetric(final Metric metric, final Throwable ex) {
    if (logMetric(metric, MetricType.ERROR)) {
      final boolean isWarning = getIsWarning(ex, metric);
      final String error = getWorkflowError(ex);
      if(isWarning){
        warn(() -> getErrorOrWarnLog(metric, error, ex, MetricType.WARN.name()));
      }else{
        error(() -> getErrorOrWarnLog(metric, error, ex, MetricType.ERROR.name()));
        final MetricData metricData =
                MetricData.builder()
                        .type(String.format("%s.%s", metric.type().getValue(), ERRORS))
                        .name(metric.name().name())
                        .exception(error)
                        .build();
        addAdditionalTags(metric.type(), metricData);
        checkAndAddAdditionalTags(metricData);
        micrometerManager.recordCount(metricData);
      }
    }
  }

  /**
   * Build logger object
   *
   * @param metric input metric details
   * @param error workflowError name
   * @param ex the exception
   * @param metricType metricType if warn or error
   */
  private WorkflowLoggerRequest.WorkflowLoggerRequestBuilder getErrorOrWarnLog(Metric metric, String error, Throwable ex, String metricType) {
    return WorkflowLoggerRequest.builder()
            .metricName(metric.name().name())
            .type(metric.type().name())
            .metricType(metricType)
            .metricError(error)
            .stackTrace(ex);
  }


  /**
   * Check if the workflowError should throw a warning
   *
   * @param ex the exception
   */
  public boolean getIsWarning(final Throwable error) {
    boolean isWarning = false;
    Optional<WorkflowError> workflowErrorOpt = Optional.empty();
    if (error instanceof WorkflowGeneralException) {
      final WorkflowGeneralException ex = ((WorkflowGeneralException) error);
      workflowErrorOpt = Optional.ofNullable(ex.getWorkflowError());
    }
    if (workflowErrorOpt.isPresent()) {
      isWarning = workflowErrorOpt.get().isWarning();
    }
    return isWarning;
  }

  /**
   * Determines if the given error should be treated as a warning based on specific conditions.
   *
   * <p>This method evaluates the provided `Throwable` error and the associated `Metric` to determine
   * if the error should be considered a warning. It checks for specific error conditions and scenarios
   * that warrant treating the error as a warning.
   *
   * @param error the `Throwable` error to evaluate
   * @param metric the `Metric` annotation associated with the error
   * @return `true` if the error should be treated as a warning, `false` otherwise
   */
  private boolean getIsWarning(final Throwable error, Metric metric) {
    if (error instanceof WorkflowGeneralException) {
      final WorkflowGeneralException ex = (WorkflowGeneralException) error;
      Optional<WorkflowError> workflowErrorOpt = Optional.ofNullable(ex.getWorkflowError());

      if (isUnauthorizedResourceAccessWarning(workflowErrorOpt, metric)) {
        return true;
      }

      return workflowErrorOpt.map(WorkflowError::isWarning).orElse(false);
    } else if (error instanceof GenericJDBCException) {
        return isCreateChildProcessWarning(metric);
    }
    /**
     * Suppressing UnauthorizedProductImpersonationException to prevent it from causing an alert
     * since this error occurs when realm is locked out of billing or doesn't have any QBO grants
     * https://devportal.intuit.com/app/dp/capability/351/capabilityDocs/main/docs/migration/OfflineTickets.md
     */
    else if(WasUtils.isUnauthorisedException((Exception) error)){
        return true;
    }
    return false;
  }

  // Suppress warning for UNAUTHORIZED_RESOURCE_ACCESS error in getWorkflowTasks method
  // This can occur when the user does not have access to approve the human task
  private boolean isUnauthorizedResourceAccessWarning(Optional<WorkflowError> workflowErrorOpt, Metric metric) {
    return workflowErrorOpt.isPresent() &&
            WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.equals(workflowErrorOpt.get()) &&
            metric != null &&
            MetricName.GET_WORKFLOW_TASKS.equals(metric.name());
  }

  // The possible reason could be aborting transaction due to some other failure as Postgres is "Read-committed".
  // One example: These errors can occur when two external tasks for a call activity are fetched and locked simultaneously.
  // If one task successfully creates an entry in the process table, the other task will fail with a UNIQUE_VIOLATION error.
  // Scenario: Two external tasks are processed concurrently by different threads. As both attempt to create the same process entry, one will succeed, and the other will encounter a UNIQUE_VIOLATION error.
  // In this case, we should treat the error as a warning as we have retries in place.
  private boolean isCreateChildProcessWarning(Metric metric) {
      return metric != null &&
              (MetricName.CREATE_CALLED_PROCESS.equals(metric.name())
              || MetricName.DOMAIN_EVENT_PUBLISH.equals(metric.name()));
  }


  private void addAdditionalTags(final Type type, final MetricData metricData) {

    for (final WASContextEnums tag : type.getMdcTags()) {
      metricData.addTag(tag.getValue());
    }
  }

  private void checkAndAddAdditionalTags(final MetricData metricData) {
    if (metricsConfig.isEmitOwnerId()) {
      addAdditionalTags(WASContextEnums.OWNER_ID.getValue(), metricData);
    }
    if (metricsConfig.isEmitWorkflowName()) {
      addAdditionalTags(WASContextEnums.WORKFLOW.getValue(), metricData);
    }
  }

  private void addAdditionalTags(final String tag, final MetricData metricData) {
    metricData.addTag(tag);
  }
}
