package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.V4_GRAPHQL;
import static java.util.Objects.nonNull;

import com.intuit.appintgwkflw.wkflautomate.telemetry.http.MonitoredHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.telemetry.http.MonitoredHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest.WorkflowLoggerRequestBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.StatusCodeRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlResponse;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Duration;
import java.time.Instant;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * This aspect weaves around the methods annotated with @ServiceMetric and emits metric logs for
 * downstream service calls being made. It has the following two paths:
 * <p>
 * 1. Success - emits an INFO log with latency.
 * <p>
 * 2. Failure - emits an ERROR log with error message.
 * <p>
 * Additionally, for HTTP calls, it emits an INFO log with the HTTP status code.
 */
@Aspect
@Component
@AllArgsConstructor
public final class ServiceMetricAspect {

  private final ServiceMetricLogger serviceMetricLogger;
  
  @Around("@annotation(serviceMetric)")
  public Object execute(final ProceedingJoinPoint joinPoint, final ServiceMetric serviceMetric)
      throws Throwable {

    final Instant start = Instant.now();
    try {
      final Object returnValue = joinPoint.proceed();
      logSuccess(joinPoint, serviceMetric, returnValue, start);
      return returnValue;
    } catch (final Exception ex) {
      logError(joinPoint, serviceMetric, ex);
      throw ex;
    }
  }

  private void logSuccess(final ProceedingJoinPoint joinPoint, final ServiceMetric serviceMetric,
      final Object returnValue, final Instant start) {

    final Duration duration = Duration.between(start, Instant.now());
    final WorkflowLoggerRequestBuilder requestBuilder = getWorkflowLoggerRequestBuilder(joinPoint,
        serviceMetric, returnValue, 1);
    requestBuilder.latency(Long.toString(duration.toMillis()));
    WorkflowLogger.info(() -> requestBuilder);
    serviceMetricLogger.logLatencyMetric(serviceName(requestBuilder, serviceMetric), duration);
  }

  private String serviceName(final WorkflowLoggerRequestBuilder requestBuilder,
      final ServiceMetric serviceMetric) {
    WorkflowLoggerRequest request = requestBuilder.build();
    return request.getLoggingParams()
        .getOrDefault(WorkflowLoggerRequest.SERVICE, serviceMetric.serviceName().name());
  }

  private void logError(final ProceedingJoinPoint joinPoint, final ServiceMetric serviceMetric,
      final Exception ex) {

    final WorkflowLoggerRequestBuilder requestBuilder = getWorkflowLoggerRequestBuilder(joinPoint,
        serviceMetric, null, 0);
    requestBuilder.metricError(ex.getMessage());
    setStatusCode(ex, requestBuilder);
    WorkflowLogger.error(() -> requestBuilder);
    serviceMetricLogger.logErrorMetric(serviceName(requestBuilder, serviceMetric), ex.getMessage());
  }

  private void setStatusCode(final Exception ex, final WorkflowLoggerRequestBuilder requestBuilder) {
    Integer statusCode =  StatusCodeRetryHelper.getMessageStatusCode(ex.getMessage());
    if(statusCode != -1) {
      requestBuilder.status(Integer.toString(statusCode));
    }
  }
  
  private WorkflowLoggerRequestBuilder getWorkflowLoggerRequestBuilder(
      final ProceedingJoinPoint joinPoint, final ServiceMetric serviceMetric,
      final Object returnValue, final int success) {

    final WorkflowLoggerRequestBuilder requestBuilder = WorkflowLoggerRequest.builder()
        .service(serviceMetric.serviceName().toString())
        .type(Type.SERVICE_METRIC.getValue())
        .success(Integer.toString(success));

    final Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
    final String methodName = StringUtils
        .defaultIfBlank(serviceMetric.methodName(), method.getName());
    requestBuilder.methodName(methodName);
    if (HTTP.equals(serviceMetric.type()) && null != returnValue) {
      addHttpMetrics(requestBuilder, method, joinPoint.getArgs(), returnValue);
    } else if (V4_GRAPHQL.equals(serviceMetric.type())) {
      addGraphQLMetrics(joinPoint, requestBuilder, returnValue);
    }
    return requestBuilder;
  }

  @SuppressWarnings({"rawtypes", "unchecked"})
  private void addGraphQLMetrics(final ProceedingJoinPoint joinPoint,
      final WorkflowLoggerRequestBuilder requestBuilder,
      final Object returnValue) {
    final Object[] args = joinPoint.getArgs();
    for (Object arg : args) {
      if (arg instanceof WASV4GraphqlRequest) {
        WASV4GraphqlRequest request = (WASV4GraphqlRequest) arg;
        requestBuilder.url(request.getUrl());
        requestBuilder.service(request.getServiceName().name());
        break;
      }
    }
    if (null != returnValue) {
      WASV4GraphqlResponse response = ((WASV4GraphqlResponse) returnValue);
      if (response.isError()) {
        response.getErrors().stream().findFirst().ifPresent(error -> {
          com.intuit.v4.Error graphQLError = (com.intuit.v4.Error) error;
          Integer statusCode = StatusCodeRetryHelper
              .getMessageStatusCode(graphQLError.getMessage());
          requestBuilder
              .status(statusCode != -1 ? Integer.toString(statusCode) : graphQLError.getCode());
          requestBuilder.metricError(graphQLError.getMessage());
        });
        requestBuilder.success("0");
      }
    }
  }

  private void addHttpMetrics(final WorkflowLoggerRequestBuilder requestBuilder,
      final Method method, final Object[] args, final Object returnValue) {
    final Parameter[] parameters = method.getParameters();
    if (parameters.length > 0) {
      final Parameter firstParam = parameters[0];
      final Object argument = args[0];
      if (MonitoredHttpRequest.class.isAssignableFrom(firstParam.getType())) {
        requestBuilder.url(((MonitoredHttpRequest) argument).getUrl());
        requestBuilder.methodName(((MonitoredHttpRequest) argument).getMethod());
      } else {
        requestBuilder.url(nonNull(argument) ? argument.toString() : null);
        final ServiceMetric annotation = method.getAnnotation(ServiceMetric.class);
        final String methodName = StringUtils
            .defaultIfBlank(annotation.methodName(), method.getName());
        requestBuilder.methodName(methodName);
      }
    }
    final String status = Integer.toString(((MonitoredHttpResponse) returnValue).statusCode());
    requestBuilder.status(status);
    final boolean isSuccess = ((MonitoredHttpResponse) returnValue).isSuccess2xx();
    if (!isSuccess) {
      requestBuilder.success(Integer.toString(0));
    }
  }
  
}