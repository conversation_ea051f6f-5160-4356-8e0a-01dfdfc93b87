package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricData;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.metrics.MicrometerManager;
import java.time.Duration;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * The type Service metric logger.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ServiceMetricLogger {

  private static final String REQUESTS = "requests";
  private static final String ERRORS = "errors";

  private final MicrometerManager micrometerManager;

  /**
   * Log latency metric.
   *
   * @param serviceName the service name
   * @param duration    the duration
   */
  public void logLatencyMetric(final String serviceName, final Duration duration) {

    final MetricData metricData = MetricData.builder()
        .type(String.format("%s.%s", Type.SERVICE_METRIC.getValue(), REQUESTS))
        .name(serviceName)
        .build();
    micrometerManager.recordTime(duration, metricData);
  }

  /**
   * Log error metric.
   *
   * @param serviceName the service name
   * @param error       input error
   */
  public void logErrorMetric(final String serviceName, final String error) {

    final MetricData metricData = MetricData.builder()
        .type(String.format("%s.%s", Type.SERVICE_METRIC.getValue(), ERRORS))
        .name(serviceName)
        .exception(error)
        .build();
    micrometerManager.recordCount(metricData);
  }
}
