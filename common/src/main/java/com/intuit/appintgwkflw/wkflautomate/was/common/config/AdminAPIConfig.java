package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for ADMIN API's.
 *
 * <AUTHOR>
 *
 */
@Configuration
@ConfigurationProperties(prefix = "admin", ignoreUnknownFields = true)
@Getter
@Setter
public class AdminAPIConfig {
    private boolean devPortalAuthorizationEnabled = false;
}
