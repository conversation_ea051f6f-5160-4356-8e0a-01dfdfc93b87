package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@Configuration
@ConfigurationProperties(prefix = "app")
@Getter
@Setter
public class AppConfig {

  private String appId;

  private String appSecret;

  private String env;
}
