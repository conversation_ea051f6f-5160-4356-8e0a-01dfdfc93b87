package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@Configuration
@Getter
@Setter
public class AppConnectConfig {

  @Value("${appconnect.workflow.taskHandler}")
  private String taskHandler;

  @Value("${appconnect.providerAppId}")
  private String providerAppId;

  @Value("${appconnect.subscriptionEndpoint}")
  private String subscriptionEndpoint;

  @Value("${appconnect.workflow.crud}")
  private String workflowCrudEndpoint;

  @Value("${appconnect.workflow.pollingFrequency}")
  private long pollingFrequency;

  @Value("${appconnect.endpoints.connector}")
  private String connectorEndpoint;

  @Value("${appconnect.mock.workflowId:98765}")
  private String workflowId;

  @Value("${appconnect.subscription.enableIdempotency:true}")
  private boolean enableIdempotency;
}
