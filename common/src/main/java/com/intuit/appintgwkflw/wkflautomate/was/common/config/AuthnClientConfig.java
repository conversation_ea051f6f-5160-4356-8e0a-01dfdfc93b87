package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.identity.authn.offline.sdk.client.AuthNClient;
import com.intuit.identity.authn.offline.sdk.client.AuthNClientFactory;
import com.intuit.identity.authn.offline.sdk.config.AuthNClientConfig;
import com.intuit.identity.authn.offline.sdk.config.AuthNResilienceConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * Handles configuration and initialization of AuthnClient
 */
@Configuration
public class AuthnClientConfig {

    private static final String AUTHN_CONFIG_BEAN = "authnClientConfigBean";

    @Value("${authn.systemOfflineJobId}")
    private String systemOfflineJobId;

    /**
     * Create the bean for config properties
     */
    @Bean(AUTHN_CONFIG_BEAN)
    @ConfigurationProperties(prefix = "authn")
    public AuthNClientConfig authNClientConfig() {
        return new AuthNClientConfig();
    }


    /**
     * Creates AuthnClient bean
     * Documentation - https://devportal.intuit.com/app/dp/capability/351/capabilityDocs/main/docs/migration/OfflineTickets.md#client-initialization
     */
    @Bean
    public AuthNClient authNClient(@Qualifier(AUTHN_CONFIG_BEAN) AuthNClientConfig authNClientConfig) {
        if (null == authNClientConfig.getAuthNResilienceConfig()){
            authNClientConfig.setAuthNResilienceConfig(new AuthNResilienceConfig());
        }
        return AuthNClientFactory.getAuthNClient(authNClientConfig);
    }

    public String getSystemOfflineJobId(){
        return systemOfflineJobId;
    }
}
