package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "authz")
public class AuthorizationConfig {
  private String env;
  private Integer connectionTimeOutMs;
}
