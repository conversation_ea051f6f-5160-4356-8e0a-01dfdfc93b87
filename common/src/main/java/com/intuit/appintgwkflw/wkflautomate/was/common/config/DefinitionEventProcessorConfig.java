package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * This class stores the config for definition event processors. e.g for event type like custom
 * reminder we need appconnect handler ids for calling custom reminder duzzits.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "definition-event-processor")
public class DefinitionEventProcessorConfig {
  private Map<DefinitionEventType, DefinitionEventProcessorAttributeConfig> definitionEventType;
}
