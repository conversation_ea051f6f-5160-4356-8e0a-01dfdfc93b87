package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR> Topic and Region Configuration for Domain Events
 */
@Getter
@Setter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "domain-event")
public class DomainEventConfig {

  private Map<DomainEventName, TopicDetails> topic;
  private String region;
  private boolean enabled;
  
  // used to check if trigger is enabled for processing trigger request that was published from event-in pipeline
  private DomainEventConsumerConfig consumer;
}
