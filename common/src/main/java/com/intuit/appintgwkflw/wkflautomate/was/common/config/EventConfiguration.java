package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.platform.jsk.config.client.idps.autoconfig.IdpsConnectionProperties;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * Config class for EventConfiguration. It reads all the properties from Spring config.
 * All of these modules are very specific to eventing, hence made as part of event module and not entity module
 */

@Setter
@Configuration
@ConfigurationProperties("event")
public class EventConfiguration {
    private EventRegionConfig primary;
    private EventRegionConfig secondary;
    @Getter
    private String securityProtocol;
    @Getter
    private String sslEnabledProtocol;
    @Getter
    private IdpsConnectionProperties idpsConfig;
    @Getter
    private EventConsumer consumer;
    @Getter
    private EventProducer producer;

    /**
     * This method returns region specific producer config for the primary or secondary region
     * based on the region enabled flag. This flag would have to be swapped in case primary region
     * event bus is down.
     * @return
     */
    public EventRegionConfig getProducerRegionConfig() {
        return producer.isEnableSecondary() ? Optional.ofNullable(secondary).orElse(primary) : primary;
    }

    /**
     * This method returns region specific consumer config for the primary or secondary region
     * based on the region enabled flag. This flag would have to be swapped in case primary region
     * event bus is down.
     * @return
     */
    public EventRegionConfig getConsumerRegionConfig() {
        return consumer.isEnableSecondary() ? Optional.ofNullable(secondary).orElse(primary) : primary;
    }

    public EventRegionConfig getProducerSecondaryRegionConfig() {
        return Optional.ofNullable(secondary).orElse(primary);
    }
}
