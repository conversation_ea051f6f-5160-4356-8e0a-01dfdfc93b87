package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import java.util.Map;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = EventScheduleConstants.CONFIG_PATH)
public class EventScheduleConfig {
  private boolean enabled;
  @NonNull private String url;
  private String timezone = EventScheduleConstants.TIMEZONE;
  private Map<String, EventScheduleWorkflowConfig> workflow;
}
