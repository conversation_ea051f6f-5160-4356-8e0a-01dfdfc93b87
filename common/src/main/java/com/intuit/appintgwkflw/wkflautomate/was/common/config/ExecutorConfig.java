package com.intuit.appintgwkflw.wkflautomate.was.common.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration related to thread pool which executes external task
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "worker.externaltask.executor")
@Getter
@Setter
public class ExecutorConfig {

  private int minThreads;
  private int maxThreads;
  private int maxQueueSize;
  
  // When the number of threads is greater than the core, this is the maximum time
  // that excess idle threads will wait for new tasks before terminating.
  private int idleThreadTimeoutSec;
  
  private int allowedGracefulShutDownTimeSec;
}
