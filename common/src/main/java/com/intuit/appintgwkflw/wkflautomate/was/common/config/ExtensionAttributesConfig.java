package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * External attributes config used in ExternalTaskClient initialisation
 * to include extension attributes in the external task properties
 * TODO: extend this configuration to add different types of extension elements
 */
@Configuration
@ConfigurationProperties(prefix = "extension-attributes")
@Data
public class ExtensionAttributesConfig {
  private boolean enabled;
}
