package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import lombok.Data;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Array of retry config for a particular external task. If a configuration isn't present here, it
 * uses the default one. Each ExternalTaskRetryConfig contains -
 * - workflowName : The workflow name
 *                  of the external task (eg, customReminder)
 * - externalTaskName : The external task name (eg, createTask)
 * - retryCount (Optional): Max times the task can be retried. Here, if retry count is specified in the task details in
 *                          BPMN, that is honoured first, and if not present there, the value in the retry config is
 *                          honoured. If value isn't present in the retry config, then the defaultRetryCount is honoured
 * - backOffStepSize (Optional): The unit duration of the backoff time, which is used to compute the retry time. Here,
 *                               if value isn't present in the retry config, then the defaultBackOffStepSize is honoured
 * - retryStrategyName (Optional): The retry strategy to be used. Here, if retry strategy name is specified in the task
 *                                 details in BPMN, that is honoured first, and if not present there, the value in the
 *                                 retry config is honoured. If value isn't present in the retry config, then the
 *                                 DEFAULT retry strategy is used.
 * - fatalOnRetryExhaust (Optional): This boolean is used to control if a Camunda incident is to be created for the
 *                                   external task if the retries are exhausted. This is a new feature in WAS, and is
 *                                   currently not present.
 */
@Data
public class ExternalTaskRetryConfig {
    private String workflowName;
    private String externalTaskName;
    private Integer retryCount;
    private Long backOffStepSize;
    private RetryStrategyName retryStrategyName;
    private boolean fatalOnRetryExhaust = false;

}
