package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * History configuration
 *
 * <AUTHOR>
 */

@Getter
@Setter
@Configuration
@ConfigurationProperties("history")
public class HistoryConfig {
  private long ttl;
}
