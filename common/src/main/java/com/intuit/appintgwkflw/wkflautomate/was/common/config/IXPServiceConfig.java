package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * Config properties for IXP feature flag
 *
 * Configuration parameters that influence the behavior of the SDK.
 * Aspects of the SDK such as retries, polling interval, service providers etc are all controlled by the configuration in this class.
 * An instance of IXPConfig is needed to initialize the IXPClient.
 *
 */

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "ixp")
public class IXPServiceConfig {

    // Flag to enable/disable IXP invocation
    private boolean enabled;

    // BU for the FF
    private String businessUnit;

    // App name for the FF
    private String applicationName;

    // Country for the FF
    private String country;

    // Client version calling IXP
    private String clientVersion;

    // Client name calling IXP
    private String clientName;

    // polling interval to invoke IXP to fetch updated FF value
    private int pollingIntervalSeconds;

    // Sub Env used for invocation of flag value i.e qal, e2e, prf, prd
    private String subEnv;

   // indicating whether environment is PRE_PROD or PRD
    private String env;

    // indicating read call timeout for fail fast
    private int dataServiceReadTimeout = 5000;

    // indicating CX call timeout for fail fast
    private int dataServiceCXTimeout = 5000;

}
