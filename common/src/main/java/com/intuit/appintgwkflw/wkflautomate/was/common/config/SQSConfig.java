package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.auth.STSAssumeRoleSessionCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSAsyncClientBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.aws.messaging.config.annotation.EnableSqs;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

@Configuration
@EnableSqs
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class SQSConfig {

  @Autowired
  private SqsRoleConfig sqsRoleConfig;
  // default number of retries is 5
  @Getter
  @Value("${ess-sqs.retryCount:5}")
  private  Integer retryCount;

  @Getter
  @Value("${ess-sqs.endPoint:}")
  private  String endPoint;

  @Bean
  @Profile("!default")
  public AWSCredentialsProvider getAssumeRoleCredentials() {
    WorkflowLogger.logInfo(
        "SQSConfig action=GetAssumeRoleCredentials state=Started UsingAssumeRole arn=%s",
        sqsRoleConfig.getAssumeRoleArn());
    return new STSAssumeRoleSessionCredentialsProvider.Builder(
            sqsRoleConfig.getAssumeRoleArn(), sqsRoleConfig.getAssumeRoleSession())
        .build();
  }

  @Bean
  @Primary
  @Profile("default")
  public AmazonSQSAsync amazonSQSAsync() {
    return AmazonSQSAsyncClientBuilder
        .standard()
        .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endPoint,
            Regions.US_WEST_2.getName()))
        .build();
  }

  @Bean
  @Primary
  @Profile("!default")
  public AmazonSQSAsync amazonSQSAsync(AWSCredentialsProvider awsCredentialsProvider) {
    return AmazonSQSAsyncClientBuilder.standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(Regions.US_WEST_2)
        .build();
  }

}
