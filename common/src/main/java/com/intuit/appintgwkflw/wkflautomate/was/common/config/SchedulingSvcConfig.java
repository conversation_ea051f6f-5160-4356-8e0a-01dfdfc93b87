package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.SchedulingSvcConstants;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** <AUTHOR> */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = SchedulingSvcConstants.CONFIG_PATH)
public class SchedulingSvcConfig {
    @NonNull
    private String baseUrl;
    private boolean migrationEnabled;
    private boolean numaflowProcessingEnabled;
}
