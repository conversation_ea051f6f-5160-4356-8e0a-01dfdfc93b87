package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class SqsRoleConfig {

    @Value("${ess-sqs.assumeRoleArn}")
    private String assumeRoleArn;

    @Value("${ess-sqs.assumeRoleSession}")
    private String assumeRoleSession;
}
