package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ThrottleAttributeConfigurations {
    // IS INTEGER enough for limit?
    private Map<ThrottleAttribute, Integer> threshold;
    private Map<ThrottleAttribute, Boolean> disable;
    private Map<ThrottleAttribute, Integer> timeframeMillis;
}
