package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "throttle")
public class ThrottleConfigs {
    private boolean externalTasksPerActivity;
    private boolean definitionsPerWorkflowPerRealm;
    private boolean definitionsPerWorkflowInTimeframe;
    private Map<ThrottleAttribute, Integer> warnDiffCount;
    private Map<String, ThrottleAttributeConfigurations> workflow;
}
