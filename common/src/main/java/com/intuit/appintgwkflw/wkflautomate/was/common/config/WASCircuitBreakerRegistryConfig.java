package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class WASCircuitBreakerRegistryConfig {
    private boolean enabled = true;
    private boolean offeringSpecific = true; // if true, a separate circuit breaker is created for each offering,
    // else the same circuit breaker is shared by all offerings
    private CircuitBreakerConfig.SlidingWindowType slidingWindowType;
    private boolean autoTransitionFromOpenToHalfOpen;
    private int slidingWindowSize;
    private int minimumNumberOfCalls;
    private int permittedNumberOfCallsInHalfOpen;
    private int waitDurationInOpen;
    private int failureRateThreshold;
    private List<Class> recordExceptions;
}
