package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/** <AUTHOR> */
@Configuration
@ConfigurationProperties(prefix = "http-client-config")
@Data
public class WASHttpClientConfiguration {

  private Integer maxPerRoute;
  private Integer maxTotal;
  private Integer connectionRequestTimeoutMillis;
  private Integer connectTimeoutMillis;
  private Integer socketTimeoutMillis;
  private Long timeToLiveMillis;
  private Integer validateConnectionInactivityMillis;
  private Integer idleConnectionTimeoutSecondsMillis;
  private boolean debug;
}
