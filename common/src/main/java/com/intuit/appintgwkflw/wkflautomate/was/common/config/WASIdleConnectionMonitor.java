package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.concurrent.TimeUnit;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

/**
 * If the connection gets closed on the server side, the client side connection is unable to detect
 * the change in the connection state.
 *
 * <p>Connection eviction is used to detect idle and expired connections and close them
 *
 * <p>{@link https://hc.apache.org/httpcomponents-client-ga/tutorial/html/connmgmt.html#d5e418}
 *
 * <AUTHOR>
 */
public class WASIdleConnectionMonitor extends Thread {
  private final HttpClientConnectionManager connMgr;
  private volatile boolean shutdown;
  private Integer idleTimeout;

  public WASIdleConnectionMonitor(PoolingHttpClientConnectionManager connMgr, Integer idleTimeout) {
    super();
    this.connMgr = connMgr;
    this.idleTimeout = idleTimeout;
  }

  @Override
  public void run() {
    try {
      while (!shutdown) {
        synchronized (this) {
          wait(5000);
          connMgr.closeExpiredConnections();
          connMgr.closeIdleConnections(idleTimeout, TimeUnit.MILLISECONDS);
        }
      }
    } catch (InterruptedException ex) {
      shutdown();
      interrupt();
      /*    InterruptedExceptions should never be ignored in the code, and simply logging the exception counts in this case as "ignoring". The throwing of the InterruptedException clears the interrupted state of the Thread,
      so if the exception is not handled properly the fact that the thread was interrupted will be lost. Instead, InterruptedExceptions should either be rethrown - immediately or after cleaning up the method's state -
      or the thread should be re-interrupted by calling Thread.interrupt() */
    }
  }

  public void shutdown() {
    shutdown = true;
    synchronized (this) {
      notifyAll();
    }
  }
}
