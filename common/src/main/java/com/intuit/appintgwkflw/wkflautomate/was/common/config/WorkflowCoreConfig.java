package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
public class WorkflowCoreConfig {

  @Value("${workflowcore.engine.host}")
  private String hostEndpointPrefix;

  @Value("${workflowcore.engine.endpoint}")
  private String restEndpointPrefix;

  @Value("${workflowcore.engine.definition.deployment}")
  private String restEndpointDefinitionDeployment;

  @Value("${workflowcore.engine.definition.deployment_crud}")
  private String restEndpointDefinitionDeploymentCrud;

  @Value("${workflowcore.engine.process.definition}")
  private String restEndpointProcessDefinition;

  @Value("${workflowcore.engine.start.instance}")
  private String restEndpointStartInstance;

  @Value("${workflowcore.engine.decision.definition}")
  private String restEndpointDecisionDefinition;

  @Value("${workflowcore.engine.evaluate.decision.definition}")
  public String restEndpointEvaluateDecisionDefinition;

  @Value("${workflowcore.engine.suspend.instance}")
  private String restEndpointSuspendInstance;

  @Value("${workflowcore.engine.process.instance}")
  private String restEndpointProcessInstance;
  
  @Value("${workflowcore.engine.process.variables}")
  private String restEndpointProcessInstanceVariables;

  @Value("${workflowcore.engine.variables}")
  private String restEndpointVariables;

  @Value("${workflowcore.engine.message}")
  private String restEndpointMessage;

  @Value("${workflowcore.engine.message-async}")
  private String restEndpointMessageAsync;

  @Value("${workflowcore.engine.execution.endpoint}")
  public String restEndpointExecution;

  @Value("${workflowcore.engine.execution.localVariables}")
  public String executionLocalVariables;

  @Value("${workflowcore.engine.process.key}")
  private String processKey;

  @Value("${workflowcore.engine.externalTask.endpoint}")
  private String externalTask;

  @Value("${workflowcore.engine.externalTask.complete}")
  private String taskComplete;

  @Value("${workflowcore.engine.externalTask.failure}")
  private String taskFailure;

  @Value("${workflowcore.engine.externalTask.extend-lock}")
  private String extendLock;

  @Value("${workflowcore.engine.serviceTask.endpoint}")
  private String serviceTask;

  @Value("${workflowcore.engine.process.delete}")
  private String deleteEndPoint;

  @Value("${workflowcore.engine.history.endpoint}")
  private String historyEndpoint;

  @Value("${workflowcore.engine.history.process-instance}")
  private String processInstanceHistoryEndpoint;
  
  @Value("${workflowcore.engine.history.process-variable-instance}")
  private String processVariableInstanceHistoryEndpoint;
  
  @Value("${workflowcore.engine.history.external-task-log}")
  private String externalTaskLogHistoryEndpoint;

  @Value("${workflowcore.engine.externalTask.v1Endpoint}")
  private String v1EndPoint;

  @Value("${workflowcore.engine.history.external-task-log-count}")
  private String externalTaskLogCountHistoryEndpoint;

}