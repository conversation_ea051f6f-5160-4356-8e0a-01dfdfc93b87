package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class WorkflowGlobalConfiguration {
  // Look-up table for DMN RuleTransformation
  private Map<String, String> operatorMap;

  // Reverse Look-up table
  private Map<String, String> operatorToSymbolMap;

  @PostConstruct
  void init() {
    operatorMap = new HashMap<>();
    operatorToSymbolMap = new HashMap<>();
    operatorMap.put("GT", ">");
    operatorMap.put("GTE", ">=");
    operatorMap.put("LT", "<");
    operatorMap.put("LTE", "<=");
    operatorMap.put("EQ", "==");
    operatorMap.put("CONTAINS", "contains");
    operatorMap.put("NOT_CONTAINS", "!contains");
    operatorToSymbolMap.put(">", "GT");
    operatorToSymbolMap.put(">=", "GTE");
    operatorToSymbolMap.put("<", "LT");
    operatorToSymbolMap.put("<=", "LTE");
    operatorToSymbolMap.put("==", "EQ");
  }
}
