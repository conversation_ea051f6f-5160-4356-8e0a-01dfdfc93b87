package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.Collections;
import java.util.Set;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * 
 *         Variability configuration
 *
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "variability")
@RefreshScope
@Configuration
public class WorkflowVariabilityClientConfig {
	private boolean enable;
	private int connectionRequestTimeout;
	private int socketTimeOut;
	private int maxConnections;
	private boolean sslEnabled;
	private int maxConnectionsPerRoute;
	private int connectTimeOutMilliSeconds;
	private long connectionKeepAliveTimeSeconds;
	private int maxIdleConnectionTimeMilliSeconds;
	private int maxIdleConnectionTimeoutSeconds;
	private boolean circuitBreakerEnabled;
	private boolean retryEnabled;
	private boolean instancePropertiesEnabled;
	private int instanceMaxAttempts;
	private boolean instanceExponentialBackoffEnabled;
	private Set<Long> enabledRealms = Collections.emptySet();
}