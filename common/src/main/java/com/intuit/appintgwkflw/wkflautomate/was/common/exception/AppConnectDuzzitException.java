package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
/** <AUTHOR> */
public class AppConnectDuzzitException extends WorkflowGeneralException {
  private static final long serialVersionUID = 1L;
  private String errorCode;
  private String errorMessage;

  /**
   * @param errorCode is the formatted Error Code
   * @param errorMessage is the formatted Error Message
   */
  public AppConnectDuzzitException(String errorCode, String errorMessage) {
    super(errorCode, errorMessage);
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }
}
