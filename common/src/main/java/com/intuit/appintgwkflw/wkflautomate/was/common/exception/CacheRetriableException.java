package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;

/**
    Exception class for Cache related exceptions which are retriable
**/

@Getter
public class CacheRetriableException extends WorkflowGeneralException {
    private static final long serialVersionUID = 1L;

    public CacheRetriableException(WorkflowError e, Exception ex) {
        super(e, ex.getMessage());
    }
}
