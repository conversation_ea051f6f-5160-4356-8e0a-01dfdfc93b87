package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;

/**
 * Exception for ProcessEngineException error which should be retried,
 * such as  deadlock, foreign key constraint violation, deleted etc
 * <AUTHOR>
 */
@Getter
public class CamundaProcessEngineRetriableException extends WorkflowGeneralException {
  private static final long serialVersionUID = 1L;

  public CamundaProcessEngineRetriableException(Throwable e) {
    super(WorkflowError.PROCESS_ENGINE_RETRY_EXHAUSTED, e);
  }
}
