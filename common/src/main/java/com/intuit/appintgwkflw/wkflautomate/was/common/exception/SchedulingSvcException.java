package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.intuit.v4.Error;
import lombok.Getter;

public class SchedulingSvcException extends WorkflowGeneralException {
    private static final long serialVersionUID = 1L;
    private String errorCode;
    private String errorMessage;
    @Getter
    private Error error;

    /**
     * @param errorCode is the formatted Error Code
     * @param errorMessage is the formatted Error Message
     */
    public SchedulingSvcException(WorkflowError workflowError, String errorCode, String errorMessage) {
        super(workflowError);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.error = new Error()
                        .code(this.errorMessage) // following WorkflowGeneralException init error
                        .message(this.errorCode) // following WorkflowGeneralException init error
                        .type(Error.ErrorTypeEnum.SYSTEM_ERROR);
    }
}
