package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import lombok.Getter;

@Getter
public class WorkflowCircuitOpenException extends WorkflowGeneralException {
    private static final long serialVersionUID = 1L;

    public WorkflowCircuitOpenException(WorkflowError e, CallNotPermittedException ex) {
        super(e, ex.getMessage());
    }
}
