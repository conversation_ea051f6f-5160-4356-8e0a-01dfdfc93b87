package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowErrorDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;

import java.util.Objects;
import java.util.Optional;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/** <AUTHOR> */
@ControllerAdvice
public class WorkflowExceptionHandler extends ResponseEntityExceptionHandler {

  /**
   * handler for any runtime unknown exception
   *
   * @param exception
   * @param webRequest
   * @param status
   * @return
   */
  @ExceptionHandler
  public ResponseEntity<Object> handleAll(Exception exception, WebRequest webRequest) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Exception occurred in handleAll exception handler")
                .stackTrace(exception)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_GLOBAL_ERROR)
                .className(this.getClass().getSimpleName()));

    HttpHeaders headers = new HttpHeaders();
    WorkflowGenericResponse response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.FAILURE)
            .errorDetails(
                WorkflowErrorDetails.builder().errorMessage(exception.getMessage()).build())
            .build();
    return new ResponseEntity<>(
        response, headers, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  /**
   * handler for all workflow related general exception.Based on the WorkflowError it will set the
   * response code and response error.
   *
   * @param exception
   * @param webRequest
   * @return
   */
  @ExceptionHandler(WorkflowGeneralException.class)
  public ResponseEntity<Object> handleWorkflowGeneralException(
      Exception exception, WebRequest webRequest) {

	// fetches component and service name from WorkflowError
    Optional<DownstreamComponentName> componentNameOpt = Optional.ofNullable(null);
    Optional<DownstreamServiceName> serviceNameOpt = Optional.ofNullable(null);
    if (exception instanceof WorkflowGeneralException) {
      WorkflowGeneralException ex = ((WorkflowGeneralException) exception);
      Optional<WorkflowError> workflowErrorOpt =
          Optional.ofNullable(ex.getWorkflowError()).filter(Objects::nonNull);
      componentNameOpt =
          workflowErrorOpt.map(workflowError -> workflowError.getDownstreamComponentName());
      serviceNameOpt =
          workflowErrorOpt.map(workflowError -> workflowError.getDownstreamServiceName());
    }
    DownstreamComponentName downstreamComponentName =
        componentNameOpt.isPresent() ? componentNameOpt.get() : DownstreamComponentName.WAS;
    DownstreamServiceName downstreamServiceName =
        serviceNameOpt.isPresent() ? serviceNameOpt.get() : DownstreamServiceName.WAS_GLOBAL_ERROR;

    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Exception occurred in handleWorkflowGeneralException exception handler")
                .stackTrace(exception)
                .downstreamComponentName(downstreamComponentName)
                .downstreamServiceName(downstreamServiceName)
                .className(this.getClass().getSimpleName()));

    HttpHeaders headers = new HttpHeaders();

    WorkflowError workflowError = fetchWorkflowError(exception);
    WorkflowErrorDetails errorDetails = populateErrorDetails(workflowError);
    WorkflowGenericResponse response =
        WorkflowGenericResponse.builder()
            .status(ResponseStatus.FAILURE)
            .errorDetails(errorDetails)
            .build();
    return new ResponseEntity<>(response, headers, workflowError.getStatus());
  }

  /**
   * fetches workflow error enum from exception message
   *
   * @param exception
   * @return
   */
	private WorkflowError fetchWorkflowError(Exception exception) {
		if (exception instanceof WorkflowGeneralException) {
			WorkflowGeneralException workflowGeneralException = (WorkflowGeneralException) exception;
			return null != workflowGeneralException.getWorkflowError() ? workflowGeneralException.getWorkflowError()
					: WorkflowError.INTERNAL_EXCEPTION;
		}
		return WorkflowError.INTERNAL_EXCEPTION;
	}

  /**
   * populate error details.
   *
   * @param workflowError
   * @return
   */
  private WorkflowErrorDetails populateErrorDetails(WorkflowError workflowError) {
    return WorkflowErrorDetails.builder()
        .errorCode(workflowError.getErrorCode())
        .errorMessage(workflowError.getErrorMessage())
        .errorDescription(workflowError.getErrorDescription())
        .build();
  }

  /**
   * overriding the default implementation and creating workflow error response.
   *
   * @param ex the exception
   * @param body the body for the response
   * @param headers the headers for the response
   * @param status the response status
   * @param request the current request
   */
  protected ResponseEntity<Object> handleExceptionInternal(
      Exception ex,
      @Nullable Object body,
      HttpHeaders headers,
      HttpStatus status,
      WebRequest request) {

    return handleAll(ex, request);
  }
}
