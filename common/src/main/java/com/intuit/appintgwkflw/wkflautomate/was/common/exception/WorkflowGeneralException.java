package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.intuit.v4.Error;
import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;

@Getter
/** <AUTHOR> */
public class WorkflowGeneralException extends RuntimeException {

  private static final long serialVersionUID = 1L;
  @Getter protected transient Error error;
  private WorkflowError workflowError;

  /**
   * Constructs a new WorkflowGeneralException with the specified workflow error.
   *
   * @param workflowError the workflow error as a string
   */
  public WorkflowGeneralException(String workflowError) {
    this(WorkflowError.value(workflowError));
  }

  public WorkflowGeneralException(WorkflowError workflowError, Throwable cause) {

    super(workflowError.getErrorMessage(), cause);
    this.workflowError = workflowError;
    initError(workflowError.name());
  }

  public WorkflowGeneralException(
      WorkflowError workflowError, Throwable cause, Object... workflowErrorMessageArgs) {

    super(formatErrorMessage(workflowError, workflowErrorMessageArgs), cause);
    this.workflowError = workflowError;
    initError(workflowError.name());
  }

  public WorkflowGeneralException(WorkflowError workflowError, Object... workflowErrorMessageArgs) {

    super(formatErrorMessage(workflowError, workflowErrorMessageArgs));
    this.workflowError = workflowError;
    initError(workflowError.name(), workflowErrorMessageArgs);
  }

  public WorkflowGeneralException(Throwable e) {

    super(e);
  }

  /** @param errorMessage to be thrown as an AppConnect ErrorMessage which extends this class */
  public WorkflowGeneralException(String errorCode, String errorMessage) {
    super(errorMessage);
  }

  /**
   * replaces the workflow error message with the message arguments.Input @WorkflowError should not
   * be null.
   *
   * @param workflowError workflow error
   * @param workflowErrorMessageArgs workflow error message arguments
   * @return WorkflowError with formated error message if required.
   * @throws NullPointerException if workflowError is null.
   */
  private static String formatErrorMessage(
      WorkflowError workflowError, Object... workflowErrorMessageArgs) {

    if (ArrayUtils.isEmpty(workflowErrorMessageArgs)) {
      return workflowError.getErrorMessage();
    }
    return String.format(workflowError.getErrorMessage(), workflowErrorMessageArgs);
  }

  private void initError(String name, Object... args) {
    this.error =
        new Error()
            .code(
                ArrayUtils.isEmpty(args)
                    ? this.workflowError.getErrorMessage()
                    : String.format(this.workflowError.getErrorMessage(), args))
            .message(name)
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR);
  }
}
