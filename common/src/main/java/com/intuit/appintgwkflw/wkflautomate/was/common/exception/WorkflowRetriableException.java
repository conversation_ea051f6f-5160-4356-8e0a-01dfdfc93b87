package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;

/**
 * Exception for error which should be retried
 * <AUTHOR>
 */
@Getter
public class WorkflowRetriableException extends WorkflowGeneralException {
  private static final long serialVersionUID = 1L;

  public WorkflowRetriableException(String workflowError) {
	 super(workflowError);
  }
  
  public WorkflowRetriableException(Exception e) {
    super(e);
  }
  
  public WorkflowRetriableException(WorkflowError workflowError) {
    super(workflowError);
  }

  public WorkflowRetriableException(WorkflowError workflowError, String errorMsg) {
    super(workflowError, errorMsg);
  }

  public WorkflowRetriableException(WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    super(workflowError, workflowErrorMessageArgs);
  }

  public WorkflowRetriableException(WorkflowError workflowError, Throwable exception) {
    super(workflowError, exception);
  }
}
