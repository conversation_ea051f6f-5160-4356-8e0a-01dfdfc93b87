package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.google.common.base.Supplier;

import java.util.Arrays;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *     <p>This class is responsible for evaluating input expression and
 *     throwing @WorkflowGeneralException with the @WorkflowError if condition satisfies.
 *     <p>These methods should be used instead of if else blocks for verifying conditions.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowVerfiy {

  /**
   * evaluates the given input string by calling {@link StringUtils.isBlank(input)} and if that
   * returns true throws @WorkflowGeneralException with given @WorkflowError. Replaces %s arguments
   * in the input error message of WorkflowError and replaces with the workflowErrorMessageArgs.
   *
   * @param input given input string.
   * @param workflowError workflow error to be thrown
   * @throws WorkflowGeneralException if expression evaluates to true.
   */
  public static void verify(
      String input, WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    verifyNull(workflowError, null);
    if (StringUtils.isBlank(input)) {
      throw new WorkflowGeneralException(workflowError, workflowErrorMessageArgs);
    }
  }

  /**
   * evaluates the expression and if true throws @WorkflowGeneralException with
   * given @WorkflowError. Replaces %s arguments in the input error message of WorkflowError and
   * replaces with the workflowErrorMessageArgs.
   *
   * @param expression input expression evaluation flag
   * @param workflowError workflow error to be thrown
   * @throws WorkflowGeneralException if expression evaluates to true.
   */
  public static void verify(
      boolean expression, WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    verifyNull(workflowError, null);
    if (expression) {
      throw new WorkflowGeneralException(workflowError, workflowErrorMessageArgs);
    }
  }

  /**
   * checks if given input reference is null and throws @WorkflowGeneralException with INVALID_INPUT
   * if input @WorkflowError is null else returns @WorkflowGeneralException with the given input
   * error.
   *
   * @throws WorkflowGeneralException if reference is null
   * @param reference input reference object
   * @param workflowError workflow error to be thrown
   */
  public static <T> void verifyNull(
      T reference, WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    if (null == reference) {
      WorkflowError error = fetchWorkflowError(workflowError);
      throw new WorkflowGeneralException(error, workflowErrorMessageArgs);
    }
  }

  /**
   * Verify null for multiple instances, if any of it is null then throw error
   *
   * @param workflowError error to be thrown
   * @param parameters list of parameters to be verified
   */
  public static void verifyNullForList(WorkflowError workflowError, Object... parameters) {
    Arrays.asList(parameters)
        .forEach(parameter -> WorkflowVerfiy.verifyNull(parameter, workflowError));
  }

  /**
   * replaces the workflow error message with the message arguments.Input @WorkflowError should not
   * be null.
   *
   * @param workflowError workflow error
   * @param workflowErrorMessageArgs workflow error message arguments
   * @return WorkflowError with formated error message if required.
   * @throws NullPointerException if workflowError is null.
   */
  private static String formatErrorMessage(
      WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    if (ArrayUtils.isEmpty(workflowErrorMessageArgs)) {
      return workflowError.getErrorMessage();
    }
    return String.format(workflowError.getErrorMessage(), workflowErrorMessageArgs);
  }

  /**
   * Test does given optional reference is present or not, if not through exception as per args
   *
   * @param reference Optional Expression which is need to be tested for present or not
   * @param workflowError workflow error message arguments
   * @param workflowErrorMessageArgs workflow error to be thrown
   * @param <T></T> Type
   */
  public static <T> void verify(
      Optional<T> reference, WorkflowError workflowError, Object... workflowErrorMessageArgs) {
    verifyNull(workflowError, null);
    if (!reference.isPresent()) {
      throw new WorkflowGeneralException(workflowError, workflowErrorMessageArgs);
    }
  }

  /**
   * evaluates the expression and throws exception
   *
   * @param expression input expression evaluation
   * @param exception WorkflowGeneralException to throw
   */
  public static void verify(boolean expression, Supplier<WorkflowGeneralException> exception) {
    verifyNull(exception, null);
    if (expression) {
      exception.get();
    }
  }

  /**
   * checks if given input reference is null and throws @WorkflowGeneralException passed as the
   * argument
   *
   * @throws WorkflowGeneralException if exception supplier is null
   * @param reference input reference
   * @param exception WorkflowEventException to throw
   */
  public static <T> void verifyNullAndThrow(
      T reference, Supplier<WorkflowEventException> exception) {
    verifyNull(exception, null);
    if (reference == null) {
      exception.get();
    }
  }

  /**
   * @param workflowError workflow error to be thrown
   * @return {@link WorkflowError}
   */
  private static WorkflowError fetchWorkflowError(WorkflowError workflowError) {
    return null == workflowError ? WorkflowError.INVALID_WORKFLOW_ERROR_INPUT : workflowError;
  }

  /**
   * checks if given input reference is null and throws @AppConnectDuzzitException passed as the
   * argument
   * @throws AppConnectDuzzitException if exception is true
   * @param duzzitException DuzzitException to throw
   */
  public static void verify(
      boolean expression, AppConnectDuzzitException duzzitException,Object... workflowErrorMessageArgs) {
    verifyNull(duzzitException, null);
    if (expression) {
      throw new AppConnectDuzzitException(duzzitException.getErrorCode(),duzzitException.getErrorMessage());
    }
  }
}
