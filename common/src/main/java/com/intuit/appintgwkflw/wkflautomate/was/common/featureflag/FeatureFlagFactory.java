package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;


import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
public class FeatureFlagFactory {

    @Autowired
    @Qualifier(WorkflowConstants.IXP_MANAGER_BEAN)
    private IXPManager ixpService;

    public FeatureManager getInstance() {

        return ixpService;

    }
}