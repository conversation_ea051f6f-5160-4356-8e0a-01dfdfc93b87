package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.identity.exptplatform.sdk.client.CacheStateChangeListener;

/**
 * Implementation of <code>IXPCacheStateChangeListener</code> to enable callbacks
 * when specific events pertaining to cache state change occurs.
 * An instance of CacheStateChangeListener is needed to initialize an IXP client instance.
 */

class IXPCacheStateChangeListener implements CacheStateChangeListener {

    @Override
    /**
     * Informs that the caches have been initialized and post-initialization actions can happen. Typical
     * action is to allow assignment calls to be serviced by the SDK and/or if you want to print out
     * active experiments, change the implementation to do it.
     */
    public void onCacheInitialize() {
        WorkflowLogger.logInfo("action=IXPCacheStateChangeListener; state=initialized;");
    }

    @Override
    /**
     * Informs there was a cache initialization failure and the SDK is not
     * ready to take assignment calls.
     */
    public void onCacheInitializationFailure(Exception ex) {
        WorkflowLogger.logError("action=IXPCacheStateChangeListener; state=cacheInitFailed", ex);
        throw new RuntimeException("Cache Initialization Failure", ex);
    }


    @Override
    /**
     * Informs there was a cache refresh failure. Repeated failures on the SDK might require
     * special handling on the app.
     */
    public void onCacheRefreshFailure(Exception ex) {
        WorkflowLogger.logError("action=IXPCacheStateChangeListener; state=cacheRefreshFailed;", ex);
    }

    @Override
    /**
     * Informs caches were successfully refreshed. Print out the updated experiments or do some
     * validations - Knock yourself out.
     */
    public void onCacheRefresh() {
        WorkflowLogger.logInfo("action=IXPCacheStateChangeListener; state=cacheRefreshed;");
    }
}
