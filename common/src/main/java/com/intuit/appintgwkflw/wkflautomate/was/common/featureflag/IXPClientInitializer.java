package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;


import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthnClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.IXPServiceConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.identity.authn.offline.sdk.client.AuthNClient;
import com.intuit.identity.exptplatform.enums.CacheElementTypeEnum;
import com.intuit.identity.exptplatform.enums.ConfigEnvironmentEnum;
import com.intuit.identity.exptplatform.featureflag.FeatureFlagClient;
import com.intuit.identity.exptplatform.sdk.client.IXPClientFactory;
import com.intuit.identity.exptplatform.sdk.client.IXPConfig;
import com.intuit.identity.exptplatform.sdk.exceptions.IXPClientInitializationException;
import com.intuit.identity.exptplatform.sdk.filters.CacheScope;
import com.intuit.identity.exptplatform.sdk.tracking.ClientInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Arrays;


@Component
@RequiredArgsConstructor
public class IXPClientInitializer {
    private final IXPServiceConfig ixpServiceConfig;

    private final AppConfig appConfig;

    private final AuthNClient authNClient;

    private final AuthnClientConfig authnClientConfig;

    private static final String PROD_ENVIRONMENT = "PROD";

    /**
     * Get single instance of IXP FeatureFlagClient used to evaluate feature flags
     * @return FeatureFlagClient instance
     */
    @Bean
    public FeatureFlagClient featureFlagClient() throws IXPClientInitializationException {
        WorkflowLogger.logInfo("action=featureFlagClient; state=begin;");
        FeatureFlagClient featureFlagClient = IXPClientFactory.getFeatureFlagClient();
        featureFlagClient.init(buildIXPConfig(), new IXPCacheStateChangeListener());
        WorkflowLogger.logInfo("action=featureFlagClient; state=success;");
        return featureFlagClient;
    }

    private IXPConfig buildIXPConfig() {

        ConfigEnvironmentEnum configEnvironmentEnum = PROD_ENVIRONMENT.equals(ixpServiceConfig.getEnv()) ?
                ConfigEnvironmentEnum.PROD : ConfigEnvironmentEnum.PRE_PROD;

        return IXPConfig.builder()
                .environment(configEnvironmentEnum)
                .cacheScope(getCacheScope())
                .pollForUpdates(true)
                .pollingInterval(ixpServiceConfig.getPollingIntervalSeconds())  // default polling interval from IXP is 1s, making it configurable
                .dataServiceReadTimeout(ixpServiceConfig.getDataServiceReadTimeout())
                .dataServiceCXTimeout(ixpServiceConfig.getDataServiceCXTimeout())
                .clientInfo(new ClientInfo(ixpServiceConfig.getClientName(), ixpServiceConfig.getClientVersion(), appConfig.getAppId()))
                .authNClient(authNClient)
                .offlineJobId(authnClientConfig.getSystemOfflineJobId()).enableRetriesAndCircuitBreakers(true)
                .build();
    }

    private CacheScope getCacheScope() {
        CacheScope cacheScope = new CacheScope();
        cacheScope.addBusinessUnit(ixpServiceConfig.getBusinessUnit());
        cacheScope.addApplicationName(ixpServiceConfig.getApplicationName());
        cacheScope.addCountry(ixpServiceConfig.getCountry());
        cacheScope.setCacheElementType(CacheElementTypeEnum.ALL); // ALL, EXPERIMENTS_ONLY OR FEATURE_FLAGS_ONLY
        cacheScope.setFeatureFlagSubEnvironments(Arrays.asList(ixpServiceConfig.getSubEnv()));
        WorkflowLogger.logInfo("action=getCacheScope; cachescope={}", cacheScope);
        return cacheScope;
    }
}
