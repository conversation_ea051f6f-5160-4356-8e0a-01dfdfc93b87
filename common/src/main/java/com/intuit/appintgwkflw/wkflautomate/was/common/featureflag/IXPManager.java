package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.IXPServiceConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.identity.exptplatform.assignment.entities.DefaultEntityIdImpl;
import com.intuit.identity.exptplatform.assignment.entities.EntityID;
import com.intuit.identity.exptplatform.featureflag.FeatureFlagClient;
import com.intuit.identity.exptplatform.featureflag.FeatureFlagParams;
import java.util.HashMap;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

@Component(WorkflowConstants.IXP_MANAGER_BEAN)
@RequiredArgsConstructor
public class IXPManager implements FeatureManager {

    private final IXPServiceConfig ixpServiceConfig;
    private final FeatureFlagClient featureFlagClient;
    private final AppConfig appConfig;
    private final WASContextHandler wasContextHandler;
    private static final String IXP_DESCRIPTION = "IXPService";
    private static final String EMPTY_REALM_ID = "NO_REALM";

    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    @Override
    public boolean getBoolean(String featureFlag, String ownerId) {

        EntityID entityID = getIXPEntityID(NumberUtils.createLong(ownerId));
        return evaluateBoolean(entityID, featureFlag, false, buildContextMap(NumberUtils.createLong(ownerId)), ownerId);
    }

    /**
     * Use this method to fetch featureFlags with Context, Make sure you are in valid context.
     *
     * @param featureFlag
     * @param defaultValue
     * @return
     */
    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public boolean getBoolean(String featureFlag, boolean defaultValue) {
        Long ownerId = WASContext.getOwnerId();
        EntityID entityID = getIXPEntityID(ownerId);
        return evaluateBoolean(entityID, featureFlag, defaultValue, buildContextMap(ownerId), String.valueOf(ownerId));
    }

    private FeatureFlagParams buildContextMap(Long ownerId) {
        // add user specific attributes to be considered while evaluation
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.INTUIT_REALMID, ownerId);
        contextMap.put(WorkflowConstants.SWIMLANE,  WasUtils.getSwimlane(appConfig.getEnv()));

        return buildFeatureFlagParams(contextMap);
    }

    /**
     * Use this method to fetch featureFlags with Context and workflowName, Make sure you are in valid context.
     * The workflow names added in ff configuration will only be allowed for single definition creation
     * @param featureFlag
     * @param defaultValue
     * @return
     */
    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public boolean getBoolean(String featureFlag, boolean defaultValue, String workflowName) {

        EntityID entityID = getIXPEntityID(WASContext.getOwnerId());

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.WORKFLOW_NAME, workflowName);
        contextMap.put(WorkflowConstants.SWIMLANE,  WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        return featureFlagClient
                .evaluateBooleanVariation(entityID, ixpServiceConfig.getSubEnv(), featureFlag, defaultValue, featureFlagParams);
    }

    /**
     * @param featureFlag  unique string used in code to evaluate the flag for a specific entity
     * @param realmId      unique id string identifying an account
     * @param defaultValue value to default to
     * @return true if feature flag is turned ON for the given authId, else false
     */
    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public boolean getBoolean(String featureFlag, boolean defaultValue, String workflowName, Long realmId) {

        EntityID entityID = getIXPEntityID(realmId);

        // add user specific attributes to be considered while evaluation
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.WORKFLOW_NAME, workflowName);
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, String.valueOf(realmId));

        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        return evaluateBoolean(entityID, featureFlag, defaultValue, featureFlagParams, String.valueOf(realmId));
    }

    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public boolean getBoolean(String flagName, String defaultValue, EntityID entityId, Map<String, Object> contextMap) {
        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        return featureFlagClient.evaluateBooleanVariation(entityId, ixpServiceConfig.getSubEnv(), flagName, Boolean.parseBoolean(defaultValue), featureFlagParams);
    }


    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public String getString(String flagName, String defaultValue, EntityID entityId, Map<String, Object> contextMap) {
        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        return featureFlagClient.evaluateStringVariation(entityId, ixpServiceConfig.getSubEnv(), flagName, String.valueOf(defaultValue), featureFlagParams);
    }

    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public String getString(String flagName, String defaultValue, Map<String, Object> contextMap, Long ownerId) {
        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        EntityID entityID = getIXPEntityID(ownerId);
        return featureFlagClient.evaluateStringVariation(entityID, ixpServiceConfig.getSubEnv(), flagName, String.valueOf(defaultValue), featureFlagParams);
    }

    private boolean evaluateBoolean(EntityID entityID, String featureFlag, boolean defaultValue,
                                    FeatureFlagParams featureFlagParams, String ownerId) {

        try{

            boolean isFFEnabled = featureFlagClient
                    .evaluateBooleanVariation(entityID, ixpServiceConfig.getSubEnv(), featureFlag, defaultValue, featureFlagParams);

            WorkflowLogger.logInfo("action=getIXPFeatureFlag; state=success; featureFlag=%s; isFFEnabled=%s; realmId=%s", featureFlag, isFFEnabled, ownerId);

            return isFFEnabled;
        }
        catch (Exception ex) {
            WorkflowLogger.logError("action=getIXPFeatureFlag; state=failed; featureFlag=%s; reamId=%s; errorMessage=%s", featureFlag, ownerId, ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * Gets an EntityID instance for getting experiment assignments.
     *
     * @param realmId - id that is used for identifying a unique company for treatment assignment. (In most cases, this will be realmId)
     *         Description of id for IXP assignment. It is used in analytics to identify where the id comes from.
     * @return entityID
     */
    private EntityID getIXPEntityID(Long realmId) {

        return DefaultEntityIdImpl
                .builder()
                .realmOrCompanyId(realmId != null ?  String.valueOf(realmId) : EMPTY_REALM_ID)
                .ns(IXP_DESCRIPTION)
                .build();
    }

    /**
     * This method fetch feature flag result for the given context map.
     * @param featureFlag
     * @param defaultValue
     * @param contextMap
     * @param realmId
     * @return
     */

    @Override
    @Metric(name = MetricName.FEATURE_FLAG_EVALUATION, metricType = {MetricType.LATENCY}, type = Type.SERVICE_METRIC, log = false)
    public boolean getBoolean(String featureFlag, boolean defaultValue,  Map<String, Object> contextMap, Long realmId) {
        EntityID entityID = getIXPEntityID(realmId);
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        FeatureFlagParams featureFlagParams = buildFeatureFlagParams(contextMap);

        return evaluateBoolean(entityID, featureFlag, defaultValue, featureFlagParams, String.valueOf(realmId));
    }

    private FeatureFlagParams buildFeatureFlagParams(Map<String, Object> contextMap) {
        return new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .transactionId(wasContextHandler.get(WASContextEnums.INTUIT_TID))
                .build();
    }
}
