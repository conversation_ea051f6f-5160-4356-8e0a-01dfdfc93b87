package com.intuit.appintgwkflw.wkflautomate.was.common.interceptor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.AUTHORIZATION_HEADER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import java.io.IOException;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.protocol.HttpContext;
import org.springframework.stereotype.Component;

/**
 * it populates the auth header required by the downstream service and INTUIT_TID in the http request from
 * the user context.It fetches the INTUIT_TID and AUTH Header from MDC and populates the auth header
 * details.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class HttpClientHeadersInterceptor implements HttpRequestInterceptor {

  private WASContextHandler contextHandler;

  private HeaderPopulator headerPopulator;

  private OfflineTicketClient offlineTicketClient;

  /** populates INTUIT_TID and auth header */
  @Override
  public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {

    /** populate INTUIT_TID */
    if (request.getFirstHeader(INTUIT_TID) == null) {
      String intuit_tid = contextHandler.get(WASContextEnums.INTUIT_TID);
      request.setHeader(INTUIT_TID, StringUtils.isEmpty(intuit_tid)?UUID.randomUUID().toString():intuit_tid);
    }

    /** For events OR non-realm systemUser in authorization context, use system offline ticket */
    String isEvent = contextHandler.get(WASContextEnums.IS_EVENT);
    if ((Boolean.parseBoolean(isEvent) || WASContext.isNonRealmSystemUser())
        && request.getFirstHeader(AUTHORIZATION_HEADER) == null) {
      request.setHeader(
          AUTHORIZATION_HEADER, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    }

    /**
     * populate Authorization header details. if header is not present then only populate the auth
     * details
     */
    if (request.getFirstHeader(AUTHORIZATION_HEADER) == null) {

      // populate user auth details by default
      request.setHeader(
          AUTHORIZATION_HEADER,
          headerPopulator.constructAuthzHeader(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)));
    }
  }
}
