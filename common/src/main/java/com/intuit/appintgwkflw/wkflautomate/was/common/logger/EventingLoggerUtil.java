package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;

import lombok.experimental.UtilityClass;

@UtilityClass
public class EventingLoggerUtil {

  public void logError(String message, String className, Object... messageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, messageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR)
                .className(className));
  }

  public void logError(String message, Throwable e, String className, Object... messageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, messageArgs)
                .stackTrace(e)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR)
                .className(className));
  }

  public void logDebug(String message, String className, Object... messageArgs) {
    WorkflowLogger.debug(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, messageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR)
                .className(className));
  }

  public void logInfo(String message, String className, Object... messageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, messageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR)
                .className(className));
  }

  public void logWarning(String message, String className, Object... messageArgs) {
    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, messageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR)
                .className(className));
  }
}
