package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import com.intuit.appintgwkflw.wkflautomate.telemetry.logger.SimpleLoggingFormat;
import com.intuit.appintgwkflw.wkflautomate.telemetry.logger.WorkflowLoggingFormat;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest.WorkflowLoggerRequestBuilder;
import java.util.function.Supplier;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * workflow logger that takes care of all logging in the workflow in a proper unified format.
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowLogger {

  private static final WorkflowLoggingFormat workflowLoggingFormat;

  static {
    workflowLoggingFormat = new SimpleLoggingFormat();
  }

  /**
   * logs the logging info in simple logging format with log level as error.
   *
   * @param loggerRequestSupplier<WorkflowLoggerRequestBuilder> input logging request
   */
  public static void error(final Supplier<WorkflowLoggerRequestBuilder> loggerRequestSupplier) {

    if (!log.isErrorEnabled() || isNull(loggerRequestSupplier)) {
      return;
    }
    final WorkflowLoggerRequest loggerRequest = loggerRequestSupplier.get().build();
    final String format = workflowLoggingFormat.format(loggerRequest.getLoggingParams());
    if (isNotEmpty(format)) {
      log.error(format);
    }
  }

  /**
   * logs the logging info in simple logging format with log level as debug
   *
   * @param loggerRequestSupplier<WorkflowLoggerRequestBuilder> input logging request
   */
  public static void debug(final Supplier<WorkflowLoggerRequestBuilder> loggerRequestSupplier) {

    if (!log.isDebugEnabled() || isNull(loggerRequestSupplier)) {
      return;
    }
    final WorkflowLoggerRequest loggerRequest = loggerRequestSupplier.get().build();
    final String format = workflowLoggingFormat.format(loggerRequest.getLoggingParams());
    if (isNotEmpty(format)) {
      log.debug(format);
    }
  }

  /**
   * logs the logging info in simple logging format with log level as info
   *
   * @param loggerRequestSupplier<WorkflowLoggerRequestBuilder> input logging request
   */
  public static void info(final Supplier<WorkflowLoggerRequestBuilder> loggerRequestSupplier) {

    if (!log.isInfoEnabled() || isNull(loggerRequestSupplier)) {
      return;
    }
    final WorkflowLoggerRequest loggerRequest = loggerRequestSupplier.get().build();
    final String format = workflowLoggingFormat.format(loggerRequest.getLoggingParams());
    if (isNotEmpty(format)) {
      log.info(format);
    }
  }

  /**
   * logs the warning in simple logging format with log level as error.
   *
   * @param loggerRequestSupplier<WorkflowLoggerRequestBuilder> input logging request
   */
  public static void warn(final Supplier<WorkflowLoggerRequestBuilder> loggerRequestSupplier) {

    if (!log.isWarnEnabled() || isNull(loggerRequestSupplier)) {
      return;
    }
    final WorkflowLoggerRequest loggerRequest = loggerRequestSupplier.get().build();
    final String format = workflowLoggingFormat.format(loggerRequest.getLoggingParams());
    if (isNotEmpty(format)) {
      log.warn(format);
    }
  }

  /**
   * log the exception as warn
   *
   * @param throwable the exception to log
   */
  public static void logWarning(final Throwable throwable) {

    if (throwable != null) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .stackTrace(throwable)
                  .message(throwable.getMessage()));
    }
  }

  /**
   * log the exception as error
   *
   * @param throwable the exception to log
   */
  public static void logError(final Throwable throwable) {
    if (throwable != null) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .stackTrace(throwable)
                  .message(throwable.getMessage()));
    }
  }

  /**
   * log string as error
   *
   * @param message
   * @param workflowMessageArgs
   */
  public static void logError(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () -> WorkflowLoggerRequest.builder().message(message, workflowMessageArgs));
  }

  /**
   * log error with message and exception
   *
   * @param throwable
   * @param message
   * @param workflowMessageArgs
   */
  public static void logError(
      final Throwable throwable, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .stackTrace(throwable)
                .message(message, workflowMessageArgs));
  }

  /**
   * log warn with message and exception
   *
   * @param throwable
   * @param message
   * @param workflowMessageArgs
   */
  public static void logWarn(
      final Throwable throwable, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .stackTrace(throwable)
                .message(message, workflowMessageArgs));
  }

  /**
   * log warn with message
   *
   * @param message
   * @param workflowMessageArgs
   */
  public static void logWarn(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.warn(
        () -> WorkflowLoggerRequest.builder().message(message, workflowMessageArgs));
  }

  /**
   * log info message
   *
   * @param message
   * @param workflowMessageArgs
   */
  public static void logInfo(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () -> WorkflowLoggerRequest.builder().message(message, workflowMessageArgs));
  }

  /**
   * log debug message
   *
   * @param message
   * @param workflowMessageArgs
   */
  public static void logDebug(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.debug(
        () -> WorkflowLoggerRequest.builder().message(message, workflowMessageArgs));
  }
}
