package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

/** <AUTHOR> */
@Getter
public class WorkflowLoggerRequest {

  private static final String DOWNSTREAM_SERVICE_NAME = "downstreamServiceName";
  private static final String DOWNSTREAM_COMPONENT_NAME = "downstreamComponentName";
  private static final String CLASS_NAME = "className";
  private static final String METHOD_NAME = "methodName";
  private static final String STACK_TRACE = "stackTrace";
  private static final String MESSAGE = "message";
  private static final String METRIC_NAME = "metricName";
  private static final String METRIC_TYPE = "metricType";
  private static final String TIME = "time";
  private static final String METRIC_ERROR = "error";
  private static final String TYPE = "type";
  public static final String SERVICE = "service";
  private static final String URL = "url";
  private static final String SUCCESS = "success";
  private static final String STATUS = "status";

  @Setter(AccessLevel.PRIVATE)
  private Map<String, String> loggingParams;

  private WorkflowLoggerRequest(final WorkflowLoggerRequestBuilder builder) {
    this.loggingParams = builder.loggingParams;
  }

  // TODO We are creating a new object for every log line. Is this really required ?
  public static WorkflowLoggerRequestBuilder builder() {
    return new WorkflowLoggerRequestBuilder();
  }

  @NoArgsConstructor(access = AccessLevel.PRIVATE)
  public static class WorkflowLoggerRequestBuilder {

    private final Map<String, String> loggingParams = new LinkedHashMap<>();
    private Throwable exception = null;

    public WorkflowLoggerRequest build() {
      /** populate internal fields for logging */
      populateInternal();
      return new WorkflowLoggerRequest(this);
    }

    public WorkflowLoggerRequestBuilder message(final String message, final Object... workflowMessageArgs) {
      if (isNotEmpty(message)) {
        loggingParams.put(
          MESSAGE,
          isNotEmpty(workflowMessageArgs)
            ? String.format(message, workflowMessageArgs)
            : message);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder stackTrace(final Throwable e) {
      if (nonNull(e)) {
        this.exception = e;
      }
      return this;
    }

    /**
     * appends stack trace and exception message in last of the logger format
     *
     * @param ex input exception
     */
    public WorkflowLoggerRequestBuilder stackTraceInternal(final Throwable ex) {
      if (nonNull(ex)) {
        loggingParams.put(STACK_TRACE, ExceptionUtils.getStackTrace(ex));
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder methodName(final String methodName) {
      if (isNotEmpty(methodName)) {
        loggingParams.put(METHOD_NAME, methodName);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder className(final String className) {
      if (isNotEmpty(className)) {
        loggingParams.put(CLASS_NAME, className);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder downstreamComponentName(
      final DownstreamComponentName downstreamComponentName) {
      if (nonNull(downstreamComponentName)) {
        loggingParams.put(DOWNSTREAM_COMPONENT_NAME, downstreamComponentName.name());
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder downstreamServiceName(
      final DownstreamServiceName downstreamServiceName) {
      if (nonNull(downstreamServiceName)) {
        loggingParams.put(DOWNSTREAM_SERVICE_NAME, downstreamServiceName.name());
      }
      return this;
    }

    /*
     * logs the internal fields automatically DEFINITION_ID,OWNER_ID,TEMPLATE_ID,PROCESS_INSTANCE_ID
     */
    private WorkflowLoggerRequestBuilder populateInternal() {

      addMDCParam(WASContextEnums.DEFINITION_ID.getValue())
          .addMDCParam(WASContextEnums.OWNER_ID.getValue())
          .addMDCParam(WASContextEnums.TEMPLATE_ID.getValue())
          .addMDCParam(WASContextEnums.PROCESS_INSTANCE_ID.getValue())
          .addMDCParam(WASContextEnums.RECORD_ID.getValue())
          .addMDCParam(WASContextEnums.HANDLER_ID.getValue())
          .addMDCParam(WASContextEnums.ENTITY_ID.getValue())
          .addMDCParam(WASContextEnums.WORKFLOW.getValue())
          .addMDCParam(WASContextEnums.ACTIVITY_ID.getValue())
          .addMDCParam(WASContextEnums.MESSAGE_EVENT.getValue())
          .addMDCParam(WASContextEnums.IS_DLQ.getValue())
          .addMDCParam(WASContextEnums.TASK_TYPE.getValue())
          .addMDCParam(WASContextEnums.EVENT_TYPE.getValue())
          .addMDCParam(WASContextEnums.TAGS_VERSION.getValue())
              .addMDCParam(WASContextEnums.APP_ID.getValue())
          .addOfferingId()
          .stackTraceInternal(this.exception);
      return this;
    }

    /**
     * Tries to getting offeringId in the following order 1. MDC 2. WASContext 3. Default value
     *
     * @return
     */
    private WorkflowLoggerRequestBuilder addOfferingId() {
      String value = MDC.get(WASContextEnums.OFFERING_ID.getValue());
      if (isEmpty(value)) {
        if (WASContext.getOfferingId().isPresent()) {
          value = WASContext.getOfferingId().get();
        } else {
          value = WorkflowConstants.DEFAULT_OFFERING;
        }
      }

      loggingParams.put(WASContextEnums.OFFERING_ID.getValue(), value);
      return this;
    }

    private WorkflowLoggerRequestBuilder addMDCParam(final String key) {

      final String value = MDC.get(key);
      if (isNotEmpty(value)) {
        loggingParams.put(key, value);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder metricName(final String metricName) {
      if (nonNull(metricName)) {
        loggingParams.put(METRIC_NAME, metricName);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder metricType(final String metricType) {
      if (nonNull(metricType)) {
        loggingParams.put(METRIC_TYPE, metricType);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder metricError(final String workflowError) {
      if (isNotEmpty(workflowError)) {
        loggingParams.put(METRIC_ERROR, workflowError);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder latency(final String time) {
      if (isNotEmpty(time)) {
        loggingParams.put(TIME, time);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder type(final String type) {

      if (isNotEmpty(type)) {
        loggingParams.put(TYPE, type);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder service(final String service) {

      if (isNotEmpty(service)) {
        loggingParams.put(SERVICE, service);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder url(final String url) {

      if (isNotEmpty(url)) {
        loggingParams.put(URL, url);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder success(final String success) {

      if (isNotEmpty(success)) {
        loggingParams.put(SUCCESS, success);
      }
      return this;
    }

    public WorkflowLoggerRequestBuilder status(final String status) {

      if (isNotEmpty(status)) {
        loggingParams.put(STATUS, status);
      }
      return this;
    }
  }
}
