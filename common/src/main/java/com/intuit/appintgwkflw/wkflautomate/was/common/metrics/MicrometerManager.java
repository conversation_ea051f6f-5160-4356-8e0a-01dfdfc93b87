package com.intuit.appintgwkflw.wkflautomate.was.common.metrics;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricData;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

/**
 * This class creates and manages metric instances to be used in the application.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MicrometerManager {

  private static final String DEFAULT_METRIC_TYPE = "method";

  private static final String METRIC_NAME_TAG = "name";
  private static final String EXCEPTION_TAG = "exception";
  public static final String DEFAULT_TAG_VALUE = "none";

  private final MeterRegistry meterRegistry;

  private final WASContextHandler contextHandler;

  /**
   * Record the time for the given metric.
   *
   * @param duration   the duration
   * @param metricData the metric data
   */
  public void recordTime(final Duration duration, final MetricData metricData) {

    try {
      final Timer.Builder timerBuilder = Timer
          .builder(getMetricType(metricData.getType()))
          .tag(METRIC_NAME_TAG, sanitize(metricData.getName(), DEFAULT_TAG_VALUE));
      additionalTags(metricData).ifPresent(timerBuilder::tags);
      timerBuilder.register(meterRegistry).record(duration);
    } catch (final Exception ex) {
      // we don't want to throw exception for metric emission failures
      WorkflowLogger.logWarn(ex, "Micrometer Exception");
    }
  }

  /**
   * Record the count the given metric.
   *
   * @param metricData the metric data
   */
  public void recordCount(final MetricData metricData, final long count) {

    try {
      final Counter.Builder counterBuilder = Counter
          .builder(getMetricType(metricData.getType()))
          .tag(METRIC_NAME_TAG, sanitize(metricData.getName(), DEFAULT_TAG_VALUE))
          .tag(EXCEPTION_TAG, sanitize(metricData.getException(), DEFAULT_TAG_VALUE));
      additionalTags(metricData).ifPresent(counterBuilder::tags);
      counterBuilder.register(meterRegistry).increment(count);
    } catch (final Exception ex) {
      // we don't want to throw exception for metric emission failures
      WorkflowLogger.logWarn(ex, "Micrometer Exception");
    }
  }

  public void recordCount(final MetricData metricData) {
    recordCount(metricData, 1);
  }

  private String getMetricType(final String type) {

    return sanitize(type, DEFAULT_METRIC_TYPE);
  }

  private String sanitize(final String original, final String defaultValue) {

    return StringUtils.isBlank(original) ? defaultValue : StringUtils.trim(original).toLowerCase();
  }

  private Optional<Tags> additionalTags(final MetricData metricData) {

    final Map<String, String> contextMap = contextHandler.getAll();
    final Set<String> additionalTags = metricData.getAdditionalTags();
    if (CollectionUtils.isEmpty(additionalTags)) {
      return Optional.empty();
    }
    return Optional.of(
        Tags.of(additionalTags.stream()
            .map(context -> Tag.of(context, sanitize(contextMap.get(context), DEFAULT_TAG_VALUE)))
            .collect(Collectors.toList()))
    );
  }
}
