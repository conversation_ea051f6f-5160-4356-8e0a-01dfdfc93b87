package com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthnClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.identity.authn.offline.sdk.client.AuthNClient;
import com.intuit.identity.authn.offline.sdk.model.GetHeadersForAccountRequest;
import com.intuit.identity.authn.offline.sdk.model.GetHeadersForOfflineJobRequest;
import com.intuit.identity.authn.offline.sdk.model.OfflineJobRequestHeaders;
import com.intuit.identity.authn.offline.sdk.utils.Constants;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@AllArgsConstructor
public class OfflineTicketClient {

  private WASContextHandler wasContextHandler;

  private AuthNClient authNClient;

  private AuthnClientConfig authnClientConfig;

  /**
   * Get the AuthHeaders for System User Offline ticket.
   *
   * @return the Authorization header string
   * @throws OfflineTicketClientException case of error
   */
  // Metric removed due to too much emission
  public String getSystemOfflineHeadersForOfflineJob() {
    String tid = wasContextHandler.get(WASContextEnums.INTUIT_TID);
    GetHeadersForOfflineJobRequest request = new GetHeadersForOfflineJobRequest.Builder()
            .withOfflineJobId(authnClientConfig.getSystemOfflineJobId())
            .withTransactionId(StringUtils.isBlank(tid) ? UUID.randomUUID().toString() : tid)
            .build();

    try {
      OfflineJobRequestHeaders offlineHeaders = authNClient.getHeadersForOfflineJob(request);
      Map<String, String> headersMap = offlineHeaders.getOfflineHeadersMap();
      return headersMap.get(Constants.AUTHORIZATION_HEADER);
    }
    catch (Exception e){
      WorkflowLogger.logError(e, "getSystemOfflineHeadersForOfflineJob failed");
      throw new OfflineTicketClientException(e);
    }
  }

  /**
   * Get the AuthHeaders for System User Offline ticket for the owner id from context.
   * This tkt is only to be used when the WAS System User has to impersonate into a realm to perform an action
   * on behalf of the user in the background.
   * This has to be used only in the cases where explicit consent is not required.
   *
   * Thread: https://intuit-teams.slack.com/archives/C04L56F6743/p1682009447527649
   *
   * @return the Authorization header string
   * @throws OfflineTicketClientException case of error
   */
  @ServiceMetric(serviceName = ServiceName.IDENTITY)
  public String getSystemOfflineHeaderWithContextRealmForOfflineJob(String realmId) {
    WorkflowVerfiy.verify(StringUtils.isBlank(realmId), WorkflowError.INVALID_REALM_ID);
    String tid = wasContextHandler.get(WASContextEnums.INTUIT_TID);
    GetHeadersForAccountRequest request = new GetHeadersForAccountRequest.Builder()
            .withOfflineJobId(authnClientConfig.getSystemOfflineJobId())
            .withImpersonatedAccountId(realmId)
            .withTransactionId(StringUtils.isBlank(tid) ? UUID.randomUUID().toString() : tid)
            .build();
    try {
      OfflineJobRequestHeaders offlineHeaders = authNClient.getHeadersForAccount(request);
      Map<String, String> headersMap = offlineHeaders.getOfflineHeadersMap();
      return headersMap.get(Constants.AUTHORIZATION_HEADER);
    }
    catch (Exception e){
      WorkflowLogger.logError(e, "getSystemOfflineHeaderWithContextRealmForOfflineJob failed");
      throw new OfflineTicketClientException(e);
    }
  }

}
