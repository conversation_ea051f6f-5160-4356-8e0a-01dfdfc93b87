package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import org.apache.commons.lang.math.NumberUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import java.net.SocketTimeoutException;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

/**
 * Retry handler for app connect duzzit executions. Responsible for handling app connect downstream errors also.
 * <AUTHOR> 
 */
@Component
@AllArgsConstructor
public class AppconnectDownstreamErrorsRetryHandler extends StatusCodeRetryHandlerImpl {

    @Override
    public void checkAndThrowRetriableException(Exception ex) {

        super.checkAndThrowRetriableException(ex);

        if (ex instanceof HttpStatusCodeException) {
            HttpStatusCodeException exHttp = (HttpStatusCodeException) ex;
            WorkflowTaskHandlerResponse response = ObjectConverter.fromJson(exHttp.getResponseBodyAsString(), WorkflowTaskHandlerResponse.class);

            if (Objects.nonNull(response) && Objects.nonNull(response.getDownstreamResponseStatus())) {
                checkAndThrowRetriableException(response.getDownstreamResponseStatus(), ex);
            }
        }
        else if (Objects.nonNull(ex) && ex.getCause() instanceof SocketTimeoutException) {
            throw new WorkflowRetriableException((SocketTimeoutException) ex.getCause());
        }
    }

    protected void checkAndThrowRetriableException(String statusCode, Exception ex) {
        if (super.retryConfig.getStatusCode().contains(NumberUtils.toInt(statusCode))) {
            WorkflowLogger.logWarn("Retrying AppConnect downstream error for statusCode=%s errorMessage=%s",
                    statusCode, ex.getMessage());
            throw new WorkflowRetriableException(ex);
        }
    }

    @Override
    public RetryHandlerName getName() {
        return RetryHandlerName.APPCONNECT_DOWNSTREAM_ERRORS;
    }
}
