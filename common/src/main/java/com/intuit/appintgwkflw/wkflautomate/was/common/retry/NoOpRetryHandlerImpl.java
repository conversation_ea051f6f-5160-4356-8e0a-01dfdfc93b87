package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;

/**
 * Implementation for RetryHandler which does not retry
 * <AUTHOR>
 */
@Component
public class NoOpRetryHandlerImpl implements RetryHandler {
  
  public void checkAndThrowRetriableException(Exception ex) {
    // Dummy comments added for sonar critical issue check
  }
  public RetryHandlerName getName() {
    return RetryHandlerName.NO_OP;
  }
}
