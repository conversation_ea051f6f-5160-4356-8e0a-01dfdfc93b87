package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class OptimisticLockRetryHandlerImpl extends StatusCodeRetryHandlerImpl {

  public void checkAndThrowRetriableException(Exception ex) {
    super.checkAndThrowRetriableException(ex);

    // Retry in case of optimistic lock exception
    if (ex instanceof HttpStatusCodeException) {
      HttpStatusCodeException exHttp = (HttpStatusCodeException) ex;
      if (exHttp.getStatusCode().value() == 500
          && isOptimisticLockException(ex.getMessage())) {
        WorkflowLogger.warn(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "Retrying for optimistic lock exception. errorMessage=%s",
                        exHttp.getResponseBodyAsString()));
        throw new WorkflowRetriableException(exHttp);
      }
    }
  }
  
  
  private boolean isOptimisticLockException(String message){
	 return StringUtils.containsIgnoreCase(message, WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION)
			 || StringUtils.containsIgnoreCase(message, WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION_CODE);
  }

  @Override
  public RetryHandlerName getName() {
    return RetryHandlerName.OPTIMISTIC_LOCK;
  }
}
