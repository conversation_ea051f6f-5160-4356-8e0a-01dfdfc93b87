package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CamundaProcessEngineRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;

import lombok.AllArgsConstructor;


/**
 * <AUTHOR> 
 * 
 * Retry PEE based on exception codes.
 * Refer: https://docs.camunda.org/manual/7.18/user-guide/process-engine/error-handling/#categories-ranges-and-codes
 *
 */
@Component
@AllArgsConstructor
public class PEECodeRetryHandlerImpl extends OptimisticLockRetryHandlerImpl {

	// Deadlock situation occurred in Persistence layer.
	private static Integer DEADLOCK_DETECTED_CODE = 10000;

	// A foreign key constraint was violated.
	private static Integer FOREIGN_KEY_VOILATION_CODE = 10001;

	// custom error code that will be used for retrying.
	private static Integer CUSTOM_ERROR_CODE = 11111;

	private static Integer PERSISTENCE_LAYER_ERROR_CODE = 0;

	private static String PERSISTENCE_LAYER_MESSAGE = "An exception occurred in the persistence layer";

	private static final Set<Integer> RETRY_ERROR = Set.of(DEADLOCK_DETECTED_CODE, FOREIGN_KEY_VOILATION_CODE,
			CUSTOM_ERROR_CODE);

	private static final String ERROR_PREFIX = "500 Internal Server Error: ";
	@Override
	public void checkAndThrowRetriableException(Exception ex) {
		super.checkAndThrowRetriableException(ex);

		// Retry in case of ProcessEngineException
		if (ex instanceof HttpStatusCodeException) {
			HttpStatusCodeException exHttp = (HttpStatusCodeException) ex;
			if (HttpStatus.INTERNAL_SERVER_ERROR.value() == exHttp.getStatusCode().value() && retryPEE(ex.getMessage())) {
				WorkflowLogger.warn(() -> WorkflowLoggerRequest.builder().message(
						"Retrying for process engine exception. errorMessage=%s", exHttp.getResponseBodyAsString()));
				throw new CamundaProcessEngineRetriableException(exHttp);
			}
		}
	}

	/**
	 * @param message exception message
	 * @return true/false if exception can be retried
	 */
	private boolean retryPEE(String message) {
		if (message != null) {
			message = message.replaceFirst("^" + ERROR_PREFIX, "");
		}
		CamundaRestException exception = ObjectConverter.fromJson(message, CamundaRestException.class);
		return null != exception && isProcessEngineException(exception) && (isRetriableErrorcode(exception) || isPersistenceLayerException(exception));
	}



	/**
	 * This method checks persistence layer exception
	 *
	 * @param exception
	 * @return
	 */
	private boolean isPersistenceLayerException(CamundaRestException exception) {
		return PERSISTENCE_LAYER_ERROR_CODE.equals(exception.getCode())
				&& StringUtils.containsIgnoreCase(exception.getMessage(), PERSISTENCE_LAYER_MESSAGE);
	}

	/**
	 * @param exception CamundaException object details
	 * @return true/false if error code can be retried
	 */
	private boolean isRetriableErrorcode(CamundaRestException exception) {
		return RETRY_ERROR.contains(exception.getCode());
	}

	/**
	 * @param exception CamundaException object details
	 * @return true/false if it is Process Engine exception
	 */
	private boolean isProcessEngineException(CamundaRestException exception) {
		return StringUtils.containsIgnoreCase(exception.getType(), WorkflowConstants.PROCESS_ENGINE_EXCEPTION);
	}

	@Override
	public RetryHandlerName getName() {
		return RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE;
	}
}
