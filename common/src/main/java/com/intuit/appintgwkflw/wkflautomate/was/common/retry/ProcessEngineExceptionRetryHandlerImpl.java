package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CamundaProcessEngineRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

/** <AUTHOR>
 * Performs check for ProcessEngineException in Camunda API dresponse
 */
@Component
@AllArgsConstructor
public class ProcessEngineExceptionRetryHandlerImpl extends OptimisticLockRetryHandlerImpl {

  private static final String UNKNOWN_PROPERTY = "Unknown property";
  private static final String NO_OUTGOING_FLOW_ERROR_CODE = "ENGINE-02004";

  @Override
  public void checkAndThrowRetriableException(Exception ex) {
    super.checkAndThrowRetriableException(ex);

    // Retry in case of optimistic lock exception
    if (ex instanceof HttpStatusCodeException) {
      HttpStatusCodeException exHttp = (HttpStatusCodeException) ex;
      if (exHttp.getStatusCode().value() == 500
          && isProcessEngineException(ex.getMessage())
          && isNonRetriableError(ex.getMessage())){
        WorkflowLogger.warn(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "Retrying for process engine exception. errorMessage=%s",
                        exHttp.getResponseBodyAsString()));
        throw new CamundaProcessEngineRetriableException(exHttp);
      }
    }
  }

  /**
   * Camunda throws unknown property error for variables which are not resolved.
   * No outgoing sequence flow is also thrown in few cases
   * @param message
   * @return
   */
  private boolean isNonRetriableError(String message) {
    return !StringUtils.containsIgnoreCase(message, UNKNOWN_PROPERTY)
        && !StringUtils.containsIgnoreCase(message, NO_OUTGOING_FLOW_ERROR_CODE);
  }
  
  private boolean isProcessEngineException(String message){
	 return StringUtils.containsIgnoreCase(message, WorkflowConstants.PROCESS_ENGINE_EXCEPTION);
  }

  @Override
  public RetryHandlerName getName() {
    return RetryHandlerName.PROCESS_ENGINE_EXCEPTION;
  }
}
