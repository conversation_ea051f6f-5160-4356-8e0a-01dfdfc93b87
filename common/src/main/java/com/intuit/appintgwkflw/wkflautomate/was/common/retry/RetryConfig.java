package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration's related to retry
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "retry")
@Getter
@Setter
public class RetryConfig {

  private List<Integer> statusCode;

  /**
   * Retry configuration for async processing of failed events
   */
  private Map<String, EntityRetryConfig> asyncRetryProcessing;
}
