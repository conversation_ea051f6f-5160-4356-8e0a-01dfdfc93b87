package com.intuit.appintgwkflw.wkflautomate.was.common.retry;


import java.util.Collections;
import java.util.Set;
import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * SqsRetry Config
 */
@Configuration
@ConfigurationProperties(prefix = "ess-sqs.retry")
@Getter
@Setter
public class SqsRetryConfig {
  // To avoid null pointer exception setting empty set
  private Set<Integer> statusCode = Collections.emptySet();
}
