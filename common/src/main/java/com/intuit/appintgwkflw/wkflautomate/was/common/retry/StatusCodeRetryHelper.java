package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@UtilityClass
public class StatusCodeRetryHelper {

  private final Map<String, Integer> HTTP_STATUS_MAP;

  static {
    HTTP_STATUS_MAP = new HashMap<>();
    for (HttpStatus httpStatus : HttpStatus.values()) {
      HTTP_STATUS_MAP.put(httpStatus.getReasonPhrase(), httpStatus.value());
    }
    HTTP_STATUS_MAP.put("Server Error", HttpStatus.INTERNAL_SERVER_ERROR.value());
  }

  /**
   * @param message : Error Message string.
   * @return statusCode
   */
  public Integer getMessageStatusCode(String message) {
    for (Entry<String, Integer> entry : HTTP_STATUS_MAP.entrySet()) {
      if (Objects.nonNull(message) && message.contains(entry.getKey())) {
        return entry.getValue();
      }
    }
    return -1;
  }

  public Map<String, Integer> getHttpStatusMap() {
    return HTTP_STATUS_MAP;
  }

}