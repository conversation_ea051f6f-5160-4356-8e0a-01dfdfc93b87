package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.v4.interaction.InteractionException;
import java.util.Map.Entry;
import java.util.Objects;
import org.springframework.stereotype.Component;

@Component
public class V4GraphqlStatusCodeRetryImpl extends StatusCodeRetryHandlerImpl {


  /**
   * Logic to retry or not based on an exception goes here
   *
   * @param ex
   */
  @Override
  public void checkAndThrowRetriableException(Exception ex) {
    if (ex instanceof InteractionException) {
      String message = ex.getMessage();
      for (Entry<String, Integer> entry : StatusCodeRetryHelper.getHttpStatusMap().entrySet()) {
        if (Objects.nonNull(message) && message.contains(entry.getKey())) {
          checkAndThrowRetriableException(entry.getValue(), ex);
          break;
        }
      }
    }
  }

  @Override
  public RetryHandlerName getName() {
    return RetryHandlerName.V4_GRAPHQL_STATUS_CODE;
  }

}