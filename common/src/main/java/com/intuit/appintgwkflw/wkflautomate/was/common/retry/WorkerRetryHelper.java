package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkerRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * This class has the utility to get the worker retry config for a particular external task 
 * 
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WorkerRetryHelper {

    private WorkerRetryConfig workerRetryConfig;

    private FeatureFlagManager featureFlagManager;

    private final int DEFAULT_RETRY_COUNT = 3;

    private final Long DEFAULT_BACKOFF_STEP_SIZE = 15000L;

    private final Long DEFAULT_MAXIMUM_BACK_OFF = 4L * 60L * 60L * 1000L;

  public int getDefaultRetryCount() {
    return Objects.nonNull(workerRetryConfig) 
            && Objects.nonNull(workerRetryConfig.getDefaultRetryCount())
        ? workerRetryConfig.getDefaultRetryCount()
        : DEFAULT_RETRY_COUNT;
  }

  public Long getDefaultBackOffStepSize() {
    return Objects.nonNull(workerRetryConfig)
            && Objects.nonNull(workerRetryConfig.getDefaultBackOffStepSize())
        ? workerRetryConfig.getDefaultBackOffStepSize()
        : DEFAULT_BACKOFF_STEP_SIZE;
  }

  public Long getMaximumBackOff() {
    return Objects.nonNull(workerRetryConfig)
            && Objects.nonNull(workerRetryConfig.getMaximumBackOff())
        ? workerRetryConfig.getMaximumBackOff()
        : DEFAULT_MAXIMUM_BACK_OFF;
  }

    public ExternalTaskRetryConfig getRetryConfig(String workflowName, String externalTaskName, String ownerId) {

        if(Objects.isNull(workerRetryConfig)) {
            return null;
        }
        
        if(Objects.isNull(workflowName) || Objects.isNull(externalTaskName) || Objects.isNull(ownerId)) {
            return null;
        }

        if(StringUtils.isEmpty(workflowName) || StringUtils.isEmpty(externalTaskName) || StringUtils.isEmpty(ownerId)) {
            return null;
        }

        if(!isWorkerRetryConfigEnabled(ownerId)) {
            return null;
        }
        
        if(Objects.isNull(workerRetryConfig.getExternalTaskConfig())) {
            return null;
        }
        
        Optional<ExternalTaskRetryConfig> externalTaskRetryConfigOptional =
                workerRetryConfig.getExternalTaskConfig().stream().filter(
                        externalTaskRetryConfig ->
                                workflowName.matches(externalTaskRetryConfig.getWorkflowName())
                                        && externalTaskName.matches(externalTaskRetryConfig.getExternalTaskName())
                ).findFirst();

        return externalTaskRetryConfigOptional.orElse(null);
    }

    private boolean isWorkerRetryConfigEnabled(String ownerId) {
        return featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId);
    }
    
}

