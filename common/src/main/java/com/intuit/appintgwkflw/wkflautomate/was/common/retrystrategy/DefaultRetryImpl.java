package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import org.springframework.stereotype.Component;

/**
 * Implementation of Default Retry
 * <AUTHOR>
 */

@Component
public class DefaultRetryImpl implements RetryStrategy {
    @Override
    public RetryStrategyName getName() {
        return RetryStrategyName.DEFAULT;
    }

    @Override
    public long computeRetryTimer(int retryLimit, int retryCount, Long backOffStepSize) {
        return backOffStepSize;
    }
}
