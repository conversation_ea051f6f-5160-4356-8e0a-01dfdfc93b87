package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkerRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.google.common.primitives.Ints.constrainToRange;

/**
 * Implementation of Retry with random exponential mod backOff
 * <AUTHOR>
 */

@Component
public class ExponentialModBackOffRandomRetryImpl implements RetryStrategy {

    @Autowired WorkerRetryHelper workerRetryHelper;
    @Autowired WorkerRetryConfig workerRetryConfig;
    private static final Integer DEFAULT_BACKOFF_FACTOR = 2;
    private static final Integer DEFAULT_RANDOM_STEP_SIZE = 1000;
    @Override
    public RetryStrategyName getName() {
        return RetryStrategyName.EXPONENTIAL_MOD_BACKOFF_RANDOM;
    }

    @Override
    public long computeRetryTimer(int retryLimit, int retryCount, Long backOffStepSize) {

        // The time is calculated by taking the exponential power of backoffFactor(suppose 2 ) for the amount of retries left,
        // ie, (retryLimit - retryCount)
        // In case the amount of retries, or the multiplier, is greater than or less than the allowed
        // range, then the multiplier is clipped to that range.
        // For example, in case of retry count = 1, retry limit = 3 & backOffStepSize = 10 secs, the
        // retry time would = backoffFactor^(3-1)*10 = 40 seconds.
        int backoffFactor = Optional.ofNullable(workerRetryConfig.getExponentialBackoffMultiplier())
                .filter(multiplier -> multiplier !=0)
                .orElse(DEFAULT_BACKOFF_FACTOR);

        int MIN_MULTIPLIER = (int) Math.pow(backoffFactor, 0);
        int MAX_MULTIPLIER = (int) Math.pow(backoffFactor, 9);

        int lowerMultiplier = constrainToRange((int) Math.pow(backoffFactor, retryLimit - retryCount ), MIN_MULTIPLIER, MAX_MULTIPLIER);
        int upperMultiplier = constrainToRange((int) Math.pow(backoffFactor, retryLimit - retryCount + 1), MIN_MULTIPLIER, MAX_MULTIPLIER);

        int randomStepSize = Optional.ofNullable(workerRetryConfig.getDefaultRandomStepSize())
                .filter(stepSize -> stepSize != 0)
                .orElse(DEFAULT_RANDOM_STEP_SIZE);

        long lowerLimit = Math.min(backOffStepSize * lowerMultiplier, workerRetryHelper.getMaximumBackOff());
        long upperLimit = Math.min(backOffStepSize * upperMultiplier, workerRetryHelper.getMaximumBackOff());
        /**
         * here we are finding the no of steps between lowerLimit and upperLimit
         * for ie randomStepSize = 1000 and lower limit = 1000 and upper limit = 2000
         * no of steps = (2000 - 1000)/1000 = 2
         */
        long noOfSteps = Math.round((float) (upperLimit - lowerLimit) / randomStepSize);

        WorkflowLogger.logInfo(
                "Calculated retry time retryStrategyName=EXPONENTIAL_MOD_BACKOFF_RANDOM step=computeRetryTimer lowerMultiplier=%s upperMultiplier=%s randomStepSize=%s lowerLimit=%s upperLimit=%s noOfSteps=%s",
                lowerMultiplier, upperMultiplier, randomStepSize, lowerLimit, upperLimit, noOfSteps
        );

        /**
         * so the final retry time is = lowerLimit + (randomStepSize * RandomUtils.nextLong(0, noOfSteps))
         * in our case min will be lowerLimit +  1000 * (any random number bw 0 to  noOfSteps) and max will be upperLimit
         */
        return lowerLimit + (randomStepSize * RandomUtils.nextLong(0, noOfSteps));
    }
}
