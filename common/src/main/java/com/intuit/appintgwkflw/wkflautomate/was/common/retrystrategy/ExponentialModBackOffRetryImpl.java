package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.google.common.primitives.Ints.constrainToRange;

/**
 * Implementation of Retry with Exponential Mod BackOff
 * <AUTHOR>
 */

@Component
public class ExponentialModBackOffRetryImpl implements RetryStrategy {

    @Autowired WorkerRetryHelper workerRetryHelper;

    private final int MIN_MULTIPLIER = (int) Math.pow(2, 0);
    private final int MAX_MULTIPLIER = (int) Math.pow(2, 9);

    @Override
    public RetryStrategyName getName() {
        return RetryStrategyName.EXPONENTIAL_MOD_BACKOFF;
    }

    @Override
    public long computeRetryTimer(int retryLimit, int retryCount, Long backOffStepSize) {

        // The time is calculated by taking the exponential power of 2 for the amount of retries left,
        // ie, (retryLimit - retryCount)
        // In case the amount of retries, or the multiplier, is greater than or less than the allowed
        // range, then the multiplier is clipped to that range.
        // For example, in case of retry count = 1, retry limit = 3 & backOffStepSize = 10 secs, the
        // retry time would = 2^(3-1)*10 = 40 seconds.

        int multiplier = constrainToRange((int) Math.pow(2, retryLimit - retryCount), MIN_MULTIPLIER, MAX_MULTIPLIER);
        return Math.min(backOffStepSize * multiplier, workerRetryHelper.getMaximumBackOff());
    }
}
