package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

/**
 * Interface for handling retries
 * <AUTHOR>
 */
public interface RetryStrategy {
    public RetryStrategyName getName();
    /**
     * Logic to compute the retry time based on appropriate strategy
     * @param retryLimit - Max retry limit
     * @param retryCount - Remaining retries
     */
    public long computeRetryTimer(int retryLimit, int retryCount, Long backOffStepSize);
}
