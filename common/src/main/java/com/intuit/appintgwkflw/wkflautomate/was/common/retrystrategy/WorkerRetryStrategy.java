package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import lombok.experimental.UtilityClass;

import java.util.EnumMap;
import java.util.Map;

/**
 * This class has the utility to get the retry strategy implementation based on the name
 * <AUTHOR>
 */
@UtilityClass
public class WorkerRetryStrategy {
    private static final Map<RetryStrategyName, RetryStrategy> RETRY_STRATEGY_MAP = new EnumMap<>(
            RetryStrategyName.class);

    /**
     * Adds a retry strategy.
     *
     * @param retryStrategyName the retry strategy name
     * @param retryStrategy     the retry strategy
     */
    public static void addStrategy(RetryStrategyName retryStrategyName, RetryStrategy retryStrategy) {
        RETRY_STRATEGY_MAP.put(retryStrategyName, retryStrategy);
    }

    /**
     * Gets a retry strategy.
     *
     * @param strategyName input strategy name
     * @return retry strategy impl
     */
    public static RetryStrategy getStrategy(RetryStrategyName strategyName) {
        return RETRY_STRATEGY_MAP
                .getOrDefault(strategyName, RETRY_STRATEGY_MAP.get(RetryStrategyName.DEFAULT));
    }

}
