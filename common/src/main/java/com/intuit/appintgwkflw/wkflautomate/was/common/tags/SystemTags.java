package com.intuit.appintgwkflw.wkflautomate.was.common.tags;

import com.intuit.appintgwkflw.wkflautomate.was.common.tags.Tag;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.VERSION_KEY;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor

/*
  This is a class which supports the addition of various user/system defined tags which can be used
  for version management of various attributes for example can be temple tags or custom-field tags
 */
public class SystemTags implements Serializable {

  public Map<String, Tag> tagsMapInstance = new HashMap<>();

  /**
   * Takes
   * @param key (for example system_tag)
   * @param value ( for example "1.2.3")
   * for example {"system_tag": {"version": "1.2.4"}}
   * */
  public void addSystemTag(String key, String value) {
    tagsMapInstance.put(key, new Tag(value));
  }

  public Tag getTag(String key) {
    return this.tagsMapInstance.get(key);
  }

  /**
   * @param tagString
   * populates the systemTag instance based on the string provided
   * */
  public void populateSystemTagFromString(String tagString) {
    Map<String, HashMap<String, String>> systemTagMap =
        ObjectConverter.fromJson(tagString, Map.class);
    for (Map.Entry<String, HashMap<String, String>> reducedObj : systemTagMap.entrySet()) {
      this.tagsMapInstance.put(
          reducedObj.getKey(), new Tag(reducedObj.getValue().get(VERSION_KEY)));
    }
  }
}
