package com.intuit.appintgwkflw.wkflautomate.was.common.threadPool;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * A factory for creating {@link ThreadPoolExecutor}.
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ThreadPoolExecutorFactory {

  /**
   * Creates a {@link ThreadPoolExecutor} with the specified parameters.
   * <p>
   * Max tasks accepted will be (maxThreadPoolSize + maxQueueSize + 1)
   * </p>
   * <p>
   * Max tasks running at any time will be (maxThreadPoolSize)
   * </p>
   * <p>
   * The additional task accepted will be added to the queue only after a slot becomes available in
   * the queue
   * </p>
   *
   * @param name        the name
   * @param queueSize   the queue size
   * @param minThreads  the min threads
   * @param maxThreads  the max threads
   * @param idleTimeout the idle timeout
   * @return the executor
   */
  public static ThreadPoolExecutor createExecutor(final String name, final int queueSize,
      final int minThreads, final int maxThreads, final int idleTimeout) {

    final ThreadPoolExecutor executor = new ThreadPoolExecutor(minThreads, maxThreads,
        idleTimeout, TimeUnit.SECONDS,
        new LinkedBlockingDeque<>(queueSize),
        new ThreadFactoryBuilder().setNameFormat(name + "-%d").build(),
        new RejectedHandler());

    WorkflowLogger.info(
        () -> WorkflowLoggerRequest.builder()
            .message("Created ThreadPoolExecutor with max threads=%s and queueSize=%s and executorName=%s",
                maxThreads, queueSize, name)
            .className(ThreadPoolExecutorFactory.class.getSimpleName()));
    return executor;
  }

  /**
   * If the shared executor has enough capacity to take the request, submit the request to the
   * shared executor. If it's running at full capacity, submit the request to the worker's reserved
   * executor. This means that we use the shared pool as much as possible, while also ensuring that
   * workers don't starve if the shared pool is full.
   *
   * @param sharedExecutor   the shared executor
   * @param reservedExecutor the reserved executor
   * @return the target executor
   */
  public static ThreadPoolExecutor getTargetExecutor(final ThreadPoolExecutor sharedExecutor,
      final ThreadPoolExecutor reservedExecutor) {

    if (sharedExecutor.getQueue().remainingCapacity() == 0 && null != reservedExecutor) {
      return reservedExecutor;
    }
    return sharedExecutor;
  }
}
