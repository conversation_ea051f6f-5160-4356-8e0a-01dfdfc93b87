package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.authz.WASAuthZClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ContextConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.identity.authz.sdk.client.AuthZErrorCode;
import com.intuit.identity.authz.sdk.client.AuthZRequestBuilder;
import com.intuit.identity.authz.sdk.exception.AuthZException;
import com.intuit.identity.authz.sdk.model.AuthZDecision;
import com.intuit.identity.authz.sdk.model.AuthZRequest;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.servlet.http.HttpServletRequest;

/**
 * This class provides methods to populate OAuth2 Headers and also check the OAuth Headers and RealmId Association .
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class AuthVerifier {

    public static final String IDENTITY_ACCESS_POLICY = "Intuit.iam.identity.account";
    public static final String TICKET_CONSTANT = "ticket";
    public static final String OFFLINE_ACCESS = "offline_access";
    public static final String ACCESS = "access";

    private WASContextHandler contextHandler;

    private WASAuthZClient wasAuthZClient;

    /**
     * This methods populates the OAuth2 headers from API GW Converts headers.
     * This method do the following 3 things -
     *  1) Read the intuit_realmid from separate request header name - "intuit_realmid"
     *  2) Call the IAMTicket verify realmid belongs to user token
     *  3) Set the realmId in Authorization key attribute.
     */
    public Authorization populateAndValidateRealmId(HttpServletRequest request, Authorization authorization){

        if(ObjectUtils.isEmpty(authorization.getRealm())) {
            String intuit_realmid = request.getHeader(WorkflowConstants.INTUIT_REALMID);
            if(ObjectUtils.isNotEmpty(intuit_realmid)){
                handleRealmMembershipCheck(authorization, intuit_realmid);
                authorization.putRealm(intuit_realmid);
                contextHandler.addKey(WASContextEnums.INTUIT_REALMID, authorization.getRealm());
                return authorization;
            }
        }
        return authorization;
    }

    /**
     * handle systemUser with Realm or Non-Realm
     * @param authorization
     */
    public void handleSystemUser(Authorization authorization){
    	if(!isSystemUser(authorization)) {
    		return;
    	}
    	if(StringUtils.isEmpty(authorization.getRealm())) {
    		handleNonRealmSystemUser(authorization);
    	} else {
    		handleRealmSystemUser(authorization);
    	}
    }
    
    /**
     * Populates realmId when authorization context belongs to system-user and realmId is present
     * @param authorization
     */
    private void handleRealmSystemUser(Authorization authorization){
        WASContext.setRealmSystemUserContext(true);
    }
    
    
    /**
     * Populates authId as realmId when authorization context belongs to system-user and realmId is absent
     * @param authorization
     */
    private void handleNonRealmSystemUser(Authorization authorization){
        authorization.putRealm(authorization.getAuthId());
        WASContext.setNonRealmSystemUserContext(true);
    }

    /**
     * Validates whether caller is a system user
     * @param authorization auth context
     */
    private boolean isSystemUser(Authorization authorization){
        AuthZRequest authZRequest = new AuthZRequestBuilder()
                .withResource(WorkflowConstants.ID, IDENTITY_ACCESS_POLICY)
                .withAction(WorkflowConstants.ID, OFFLINE_ACCESS)
                .withSubject(ContextConstants.USERID_KEY, authorization.getAuthId())
                .withSubject(TICKET_CONSTANT, authorization.get(WorkflowConstants.INTUIT_TOKEN))
                .build();

        try {
            return AuthZDecision.PERMIT == wasAuthZClient.getAuthZClient().authorize(authZRequest).getDecision();
        } catch (AuthZException e) {
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, e);
        }
    }

    /**
     * validates if user is part of the realm in auth context.
     * @param authorization auth context
     */
    private void handleRealmMembershipCheck(Authorization authorization, String realmId) {
        AuthZRequest authZRequest = new AuthZRequestBuilder()
                .withResource(WorkflowConstants.ID, IDENTITY_ACCESS_POLICY)
                .withAction(WorkflowConstants.ID, ACCESS)
                .withSubject(ContextConstants.USERID_KEY, authorization.getAuthId())
                .withSubject(ContextConstants.REALMID_KEY, realmId)
                .withSubject(TICKET_CONSTANT, authorization.get(WorkflowConstants.INTUIT_TOKEN))
                .build();
        try {
            AuthZDecision authZDecision = wasAuthZClient.getAuthZClient().authorize(authZRequest).getDecision();
            if (AuthZDecision.DENY == authZDecision){
                throw new WorkflowGeneralException(WorkflowError.USER_NOT_REALM_MEMBER);
            }
        } catch (AuthZException e) {
            handleAuthzError(e);
        }
    }

    /**
     * Exception handling for Authz
     */
    private void handleAuthzError(AuthZException e) {
        if (AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS == e.getAuthZErrorCode()){
            throw new WorkflowGeneralException(WorkflowError.USER_NOT_REALM_MEMBER, e);
        }
        throw new WorkflowGeneralException(WorkflowError.TICKET_VERIFICATION_FAILED, e);
    }

}
