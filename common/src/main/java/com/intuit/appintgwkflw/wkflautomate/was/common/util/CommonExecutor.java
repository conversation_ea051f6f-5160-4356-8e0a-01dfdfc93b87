package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import com.google.common.base.Verify;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.function.Supplier;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.UnknownContentTypeException;

@UtilityClass
public class CommonExecutor {

  /**
   * @param input    input lambda
   * @param errorMsg error message to be logged if error comes
   * @param <T>
   * @return response
   */
  public <T> T execute(final Supplier<T> input, final String errorMsg) {

    Verify.verifyNotNull(input, WorkflowConstants.INPUT_CANNOT_BE_NULL);
    Verify.verify(isNotEmpty(errorMsg), "error msg cannot be null or empty");
    try {
      return input.get();
    } catch (final Exception ex) {
      WorkflowLogger.logError(ex, errorMsg);
    }
    return null;
  }

  public void execute(final WorkflowSupplier input, final String errorMsg,
      final LogSupplier logFn) {

    Verify.verifyNotNull(input, WorkflowConstants.INPUT_CANNOT_BE_NULL);
    Verify.verifyNotNull(logFn, "logFn cannot be null");
    Verify.verify(isNotEmpty(errorMsg), "error msg cannot be null or empty");
    try {
      input.get();
    } catch (final Exception ex) {
      logFn.get(ex);
      WorkflowLogger.logError(ex, errorMsg);
    }
  }

  public <T> T execute(
      final Supplier<T> input,
      final String msg,
      final ProviderExceptionSupplier<T> er1,
      final ProviderExceptionSupplier<T> er2) {

    Verify.verifyNotNull(input, WorkflowConstants.INPUT_CANNOT_BE_NULL);

    try {
      return input.get();
    } catch (final WorkflowGeneralException e) {
      WorkflowLogger.logError(e, msg);
      return er1.get(e);
    } catch (final Exception e) {
      WorkflowLogger.logError(e, msg);
      return er2.get(e);
    }
  }

  /**
   * prepares the WASHttpResponse by setting response,status and error if exception occurs in
   * executing rest supplier.
   *
   * @param <T>          generic response type
   * @param input        input lambda
   * @param retryHandler the retry handler
   * @return {@link WASHttpResponse}
   */
  public <T> WASHttpResponse<T> executeRestSupplier(
      final Supplier<ResponseEntity<T>> input, final RetryHandlerName retryHandler) {

    Verify.verifyNotNull(input, WorkflowConstants.INPUT_CANNOT_BE_NULL);
    try {
      final ResponseEntity<T> response = input.get();
      return WASHttpResponse.<T>builder()
          .response(response.getBody())
          .status(response.getStatusCode())
          .isSuccess2xx(response.getStatusCode().is2xxSuccessful())
          .build();
    } catch (final HttpStatusCodeException ex) {
      final String errorMessage = String.format("Error in making rest call. Error=%s Headers=%s",
          ex.getResponseBodyAsString(), ex.getResponseHeaders());
      return handleException(retryHandler, ex, errorMessage, ex.getStatusCode());
    } catch (final UnknownContentTypeException ex) {
      // It was observed that this exception was thrown whenever there was a HTTP 5XX error
      // returned in the REST call. The handle went into `RestClientException` which is the parent
      // class of `UnknownContentTypeException` and hence some contextual information was lost
      final String errorMessage = String.format("Error in making rest call. Error=%s Headers=%s",
          ex.getResponseBodyAsString(), ex.getResponseHeaders());
      return handleException(retryHandler, ex, errorMessage,
          HttpStatus.valueOf(ex.getRawStatusCode()));
    } catch (final RestClientException ex) {
      final String errorMessage = String
          .format("Error in making rest call. Error=%s", ex.getMessage());
      return handleException(retryHandler, ex, errorMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private <T> WASHttpResponse<T> handleException(final RetryHandlerName retryHandler,
      final Exception exception, final String errorMessage, final HttpStatus httpStatus) {

    WASRetryHandler.getHandler(retryHandler).checkAndThrowRetriableException(exception);
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(errorMessage)
                .className(CommonExecutor.class.getSimpleName()));
    return WASHttpResponse.<T>builder().error(errorMessage).status(httpStatus).build();
  }
}
