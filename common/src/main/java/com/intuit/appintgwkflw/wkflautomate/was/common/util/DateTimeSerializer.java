package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdScalarSerializer;
import org.joda.time.DateTime;

import java.io.IOException;
import java.util.HashMap;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.*;

public class DateTimeSerializer extends StdScalarSerializer<DateTime> {
    public DateTimeSerializer () {
        super(DateTime.class);
    }

    @Override
    public void serialize(DateTime dateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        HashMap o = new HashMap<>();
        o.put(CALENDAR_YEAR, dateTime.getYear());
        o.put(CALENDAR_MONTH, dateTime.getMonthOfYear());
        o.put(CALENDAR_DAY, dateTime.getDayOfMonth());
        o.put(CALENDAR_CENTURY_OF_ERA, dateTime.getCenturyOfEra());
        o.put(CALENDAR_YEAR_OF_ERA, dateTime.getYearOfEra());
        o.put(CALENDAR_YEAR_OF_CENTURY, dateTime.getYearOfCentury());
        o.put(CALENDAR_WEEK_YEAR, dateTime.getWeekyear());
        o.put(CALENDAR_MONTH_OF_YEAR, dateTime.getMonthOfYear());
        o.put(CALENDAR_WEEK_OF_WEEK_YEAR, dateTime.getWeekOfWeekyear());
        o.put(CALENDAR_HOUR_OF_DAY, dateTime.getHourOfDay());
        o.put(CALENDAR_MINUTE_OF_HOUR, dateTime.getMinuteOfDay());
        o.put(CALENDAR_SECOND_OF_MINUTE, dateTime.getSecondOfMinute());
        o.put(CALENDAR_MILLIS_OF_SECOND, dateTime.getMillisOfSecond());
        o.put(CALENDAR_DAY_OF_WEEK, dateTime.getDayOfWeek());
        o.put(CALENDAR_ERA, dateTime.getEra());
        o.put(CALENDAR_DAY_OF_YEAR, dateTime.getDayOfYear());
        o.put(CALENDAR_MILLIS_OF_DAY, dateTime.getMillisOfSecond());
        o.put(CALENDAR_SECOND_OF_DAY, dateTime.getSecondOfDay());
        o.put(CALENDAR_MINUTE_OF_DAY, dateTime.getMinuteOfDay());
        o.put(CALENDAR_ZONE, dateTime.getZone());
        o.put(CALENDAR_MILLIS, dateTime.getMillis());
        o.put(CALENDAR_AFTER_NOW, dateTime.isAfterNow());
        o.put(CALENDAR_BEFORE_NOW, dateTime.isBeforeNow());
        jsonGenerator.writeObject(o);
    }
}
