package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import org.apache.commons.lang3.time.FastDateFormat;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;

/**
 * Util class for Date manipulation
 */
public class DateUtils {

  public enum DateFormat {
    YYYY_MM_DD("yyyy-MM-dd"), DD_MM_YYYY("dd-MM-yyyy"), MM_DD_YYYY("MM-dd-YYYY");

    String value;

    DateFormat(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }
  }

  /**
   * @param date Date for which timestamp is removed
   * @return date without timestamp
   */
  public static Date getDateWithoutTimestamp(Date date) {
    FastDateFormat formatter = FastDateFormat.getInstance(DateFormat.YYYY_MM_DD.getValue());
    try {
      return formatter.parse(formatter.format(date));
    } catch (ParseException e) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_DATE_FORMAT, e);
    }
  }

  /**
   * Converts the given date to certain format
   *
   * @param date       String date
   * @param dateFormat date format to be returned
   * @return formatted dateTime
   */
  public static DateTime getDateWithFormat(String date, DateFormat dateFormat) {
    try {
      return DateTime.parse(date, DateTimeFormat.forPattern(dateFormat.getValue()));
    } catch (IllegalArgumentException e) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_DATE_FORMAT, e);
    }
  }


  /**
   * Converts the given date to certain format
   *
   * @param date       String date
   * @param DateTimeFormatter date format to be returned
   * @return formatted dateTime
   */
  public static String getDateWithFormat(String date, DateTimeFormatter dateTimeFormatter) {
    try {
      LocalDate formattedDate = LocalDate.parse(date, dateTimeFormatter);
      return formattedDate.toString();
    } catch ( Exception e) {
      WorkflowLogger.logError(e, "Date Parsing Failed=%s", date);
    }
    return null;
  }

  /**
   * Converts the given timestamp to Date Time
   *
   * @param timestamp Timestamp timestamp
   * @return dateTime
   */

  public static DateTime getDateTime(Timestamp timestamp) {
    try {

      Date date = new Date(timestamp.getTime());
      return new DateTime(date);
    } catch (IllegalArgumentException e) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_DATE_FORMAT, e);
    }
  }

  /**
   * This method gives last x hours epoc time seconds
   *
   * @param hours
   * @return
   */
  public static long getLastXHoursUnixTimeStamp(int hours) {
    Instant now = Instant.now();
    // Subtract 24 hours from the current time
    Instant last24Hours = now.minus(24, ChronoUnit.HOURS);
    // Get the Unix timestamp of the last 24 hours
    return last24Hours.getEpochSecond();
  }

  /**
   * This method gives returns latest unix time stamp.
   *
   * @param
   * @return
   */
  public static String getCurrentTimeStamp() {
    Instant currentTimestamp = Instant.now();
    // Convert the timestamp to milliseconds
    long currentTimestampMillis = currentTimestamp.toEpochMilli();
    return String.valueOf(currentTimestampMillis);
  }

  /**
   * Gets the current date with the specified time zone.
   *
   * @param zoneId the time zone ID to use
   * @return the current date in the specified time zone
   */
  public static LocalDate getCurrentDateWithTimeZone(ZoneId zoneId) {
    ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);
    return zonedDateTime.toLocalDate();
  }
}
