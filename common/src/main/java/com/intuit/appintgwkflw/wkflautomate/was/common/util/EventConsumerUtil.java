package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class EventConsumerUtil {

  private EventConfiguration eventConfiguration;

  /**
   * @return Gives a topic to entity type mapping from the entityTopicsMapping config
   */
  public Map<String, String> getTopicToEntityTypeMapping() {
    Map<String, String> topicEntityTypeMap = new HashMap<>();
    eventConfiguration
        .getConsumer()
        .getEntityTopicsMapping()
        .forEach(
            (entityType, topicList) ->
                topicList.forEach(topic -> topicEntityTypeMap.putIfAbsent(topic, entityType)));
    return topicEntityTypeMap;
  }

  /**
   * @param filterDLQTopics boolean param to determine if dlq or non-dlq topic list is returned Used
   *                        to set the list of topics in kafka consumer
   * @return list of topics filtered from entityTopicsMapping
   */
  public List<String> getFilteredTopicList(String entityType, boolean filterDLQTopics) {
    List<String> topics = eventConfiguration.getConsumer().getEntityTopicsMapping().get(entityType);
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(topics), WorkflowError.MISSING_TOPIC_ENTITY_MAPPING);
    return topics
        .stream()
        .filter(value -> filterDLQTopics == value.endsWith(KafkaConstants.KAFKA_DLQ_SUFFIX))
        .collect(Collectors.toList());
  }

  /**
   * Extracts task id and worker if from entity header
   *
   * @param entityId
   * @return
   */
  public static Pair<String, String> getTaskIdAndWorkerId(String entityId,
      EventEntityType entityType) {
	
	entityType = entityType == null ? EventEntityType.EXTERNALTASK : entityType; 
    WorkflowVerfiy
        .verify(StringUtils.isEmpty(entityId), WorkflowError.MISSING_EVENT_HEADERS, entityType.name());
    String[] entityIdArray = entityId.split(WorkflowConstants.COLON);
    if (entityIdArray.length != 2) {
      EventingLoggerUtil
          .logError("entityId header is not in the correct format. entityId=%s, taskType=%s",
              EventConsumerUtil.class.getSimpleName(), entityId, entityType);
      WorkflowVerfiy
      	.verify(true, WorkflowError.MISSING_EVENT_HEADERS, entityId);
    }
    return new ImmutablePair<>(entityIdArray[0], entityIdArray[1]);
  }

}
