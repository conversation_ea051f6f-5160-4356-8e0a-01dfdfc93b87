package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import lombok.experimental.UtilityClass;

/**
 * Generates a unique hash for the given input
 *
 * <AUTHOR>
 */
@UtilityClass
public class HashingUtil {

  private final String MD5_HASHING_ALGO_FOR_STRING = "MD5";
  public final String ZERO_BIT = "0";
  public final int HEX_VALUE_BASE = 16;
  public final int MAX_NUMBER_OF_BITS = 32;

  /**
   * This function is used to create hash for a given input string using md5 hash function
   *
   * @param inputString
   * @return
   */
  public String generateHashForString(String inputString) {
    try {
      // Static getInstance method is called with hashing MD5
      MessageDigest md = MessageDigest.getInstance(MD5_HASHING_ALGO_FOR_STRING);

      // digest() method is called to calculate message digest
      byte[] messageDigest = md.digest(inputString.getBytes());

      // Convert byte array into signum representation
      BigInteger number = new BigInteger(1, messageDigest);

      // Convert message digest into hex value
      String hashSum = number.toString(HEX_VALUE_BASE);
      while (hashSum.length() < MAX_NUMBER_OF_BITS) {
        hashSum = ZERO_BIT + hashSum;
      }
      return hashSum;
    }

    // For specifying wrong message digest algorithms
    catch (NoSuchAlgorithmException e) {
      throw new WorkflowGeneralException(e);
    }
  }

}
