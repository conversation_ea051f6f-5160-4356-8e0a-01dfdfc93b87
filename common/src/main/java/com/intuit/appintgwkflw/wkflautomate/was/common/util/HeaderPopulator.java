package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COMMA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EQUAL;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ContextConstants;
import com.intuit.v4.Authorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class HeaderPopulator {

  @Autowired
  private AppConfig appConfig;

  @Autowired
  private WASContextHandler wasContextHandler;

  /**
   * prepare Authorization header to be passed to Workflow engine core rest api from input Auth
   * header from client.
   *
   * @param authorizationDetails input Authorization header
   * @return Intuit_IAM_Authentication Authorization header
   */
  public String constructAuthzHeader(String authorizationDetails) {
    if (isEmpty(authorizationDetails)) {
      return null;
    }
    Authorization authorization = new Authorization(authorizationDetails);
    StringBuilder sb = new StringBuilder();
    sb.append(ContextConstants.IAM_AUTHENTICATION_TYPE).append(" ");
//  TODO find a better way to assert this tokentype.
    String tokenType = authorizationDetails.contains(ContextConstants.IAM_OFFLINE_TICKET_TYPE) ? ContextConstants.IAM_OFFLINE_TICKET_TYPE
        : ContextConstants.IAM_TICKET_TYPE;

    sb.append(ContextConstants.TOKEN_TYPE_KEY).append(EQUAL).append(tokenType).append(COMMA);
    sb.append(ContextConstants.APP_ID_KEY).append(EQUAL).append(appConfig.getAppId()).append(COMMA);
    sb.append(ContextConstants.APP_SECRET_KEY).append(EQUAL).append(appConfig.getAppSecret())
        .append(COMMA);
    sb.append(ContextConstants.TOKEN_KEY).append(EQUAL)
        .append(authorization.get(ContextConstants.TOKEN_KEY)).append(COMMA);
    sb.append(ContextConstants.USERID_KEY).append(EQUAL).append(authorization.getAuthId())
        .append(COMMA);
    sb.append(ContextConstants.REALMID_KEY).append(EQUAL).append(authorization.getRealm());
    return sb.toString();
  }

  /**
   * Fetches authorization header from MDC context and constructs new Auth header
   * @return
   */
  public String constructAuthzHeaderFromContext() {
    String authHeader = wasContextHandler.get(WASContextEnums.AUTHORIZATION_HEADER);
    return constructAuthzHeader(authHeader);
  }
}
