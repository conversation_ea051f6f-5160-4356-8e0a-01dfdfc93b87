package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.fasterxml.jackson.databind.AnnotationIntrospector;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.AnnotationIntrospectorPair;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveFieldAnnotationIntrospector;


/**
 * <AUTHOR>
 * This object converter will be used only to convert entities while printing logs
 * If an entity has a field marked with RedactSensitiveField annotation then this mapper converts that field's value
 * to a defgault mask value while logging
 *
 * Note: This mapper can only be used for serialisation purposes, deserialisation is not supported yet
 * If a default mask value of null is used then deserialisation can also be achieved
 */
public class MaskedObjectConverter {

  private static final ObjectMapper mapper = new ObjectMapper();

  static {
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
    mapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS);

    //Add annotation introspector in object Mapper to use custom serializer for RedactSensitiveData annotated fields
    AnnotationIntrospector annotationIntrospector = AnnotationIntrospectorPair.pair(mapper.getSerializationConfig().getAnnotationIntrospector(),
        new RedactSensitiveFieldAnnotationIntrospector());
    mapper.setAnnotationIntrospector(annotationIntrospector);
  }

  private MaskedObjectConverter() {}

  public static String toJson(Object src) {
    try {
      String resp = mapper.writeValueAsString(src);
      return "null".equals(resp) ? null : resp;
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

}
