package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import java.io.InputStream;
import java.util.Objects;

/** <AUTHOR> */
public class ObjectConverter {

  private static final ObjectMapper mapper = new ObjectMapper();

  static {
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
    mapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS);
    mapper.registerModule(new JavaTimeModule());
    mapper.registerModule(new JodaModule());
    mapper.registerModule(new DateTimeModule());
    mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
  }

  private ObjectConverter() {}

  public static String toJson(Object src) {
    try {
      String resp = mapper.writeValueAsString(src);
      return "null".equals(resp) ? null : resp;
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

  public static <T> T fromJson(String json, Class<T> valueType) {
    try {
      return mapper.readValue(json, valueType);
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

  public static <T, V> V convertObject(T from, TypeReference<V> typeReference) {
    try {
      return mapper.convertValue(from, typeReference);
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

  public static <T> T convertObject(Object fromValue, Class<T> toValueType) {
    try {
      return Objects.isNull(fromValue) ? null : mapper.convertValue(fromValue, toValueType);
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

  public static <T> T fromJson(
      String content, @SuppressWarnings("rawtypes") TypeReference valueTypeRef) {
    try {
      return (T) mapper.readValue(content, valueTypeRef);
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }

  /**
   * Convert to json inputStream to class pojo toValueType
   * @param inputStream inputStream of jsonFile
   * @param valueTypeRef value to convert to
   * @param <T> generic type
   * @return object of type T
   */
  public static <T> T fromJsonStream(InputStream inputStream, TypeReference<T> valueTypeRef){
    try {
      return mapper.readValue(inputStream, valueTypeRef);
    } catch (Exception e) {
      WorkflowLogger.logWarning(e);
      return null;
    }
  }
}
