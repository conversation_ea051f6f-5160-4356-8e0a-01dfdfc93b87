package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import java.util.Optional;
import lombok.experimental.UtilityClass;

/**
 * Always add Get<PERSON> and Setters for all {@link ThreadLocal} variables.
 * Also add the required code changes in MdcPropagatingRunnable class,
 * so that variables are propagated in multi-threaded flows.
 */
@UtilityClass
public class WASContext {
  private static final ThreadLocal<Authorization> authContext = new InheritableThreadLocal<>();
  private static final ThreadLocal<String> offeringIdContext = new InheritableThreadLocal<>();
  private static final ThreadLocal<Boolean> nonRealmSystemUserContext = new InheritableThreadLocal<>();
  private static final ThreadLocal<Boolean> migrationContext = new InheritableThreadLocal<>();
  private static final ThreadLocal<Boolean> realmSystemUserContext = new InheritableThreadLocal<>();

  public static void setAuthContext(Authorization authorization) {
    authContext.set(authorization);
  }

  /**
   * Get the {@link Authorization} value from {@link #authContext}
   * @return {@link Authorization}
   */
  public static Authorization getAuthContext(){
    return authContext.get();
  }

  public static void setOfferingId(String offeringId) {
    offeringIdContext.set(offeringId);
  }

  /**
   * Sets the flag for non-realm system user in thread local
   * @param isSystemUser
   */
  public static void setNonRealmSystemUserContext(boolean isSystemUser){
    nonRealmSystemUserContext.set(isSystemUser);
  }

  /**
   * Sets the flag for realm system user in thread local
   * @param isSystemUser
   */
  public static void setRealmSystemUserContext(boolean isSystemUser){
    realmSystemUserContext.set(isSystemUser);
  }
  
  
  public static void setMigrationContext(boolean isMigration){
	  migrationContext.set(isMigration);
  }

  public static Optional<String> getOfferingId() {
    String offeringID = null;
    if (offeringIdContext.get() != null) {
      offeringID = offeringIdContext.get();
    }
    //TODO return Optional.ofNullable(offeringIdContext.get());
    return Optional.ofNullable(offeringID);
  }

  public static Optional<String> getApplicationId() {
    String appId = null;
    if (authContext.get() != null) {
      appId = authContext.get().get(WorkflowConstants.APP_ID);
    }
    return Optional.ofNullable(appId);
  }

  public static Long getOwnerId() {
    Long ownerId = null;
    if (authContext.get() != null) {
      ownerId = Long.valueOf(authContext.get().getRealm());
    }
    return ownerId;
  }

  /**
   * fetches the value for non-realm system user from thread local.
   * @return boolean
   */
  public static boolean isNonRealmSystemUser() {
    return nonRealmSystemUserContext.get() != null && nonRealmSystemUserContext.get();
  }
  
  /**
   * fetches the value for realm system user from thread local.
   * @return boolean
   */
  public static boolean isRealmSystemUser() {
    return realmSystemUserContext.get() != null && realmSystemUserContext.get();
  }

  public static boolean isMigrationContext() {
    return migrationContext.get() != null && migrationContext.get();
  }

  public static void clear() {
    authContext.remove();
    offeringIdContext.remove();
    nonRealmSystemUserContext.remove();
    migrationContext.remove();
    realmSystemUserContext.remove();
  }

}
