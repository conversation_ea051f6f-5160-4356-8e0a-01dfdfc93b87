package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.identity.authn.offline.sdk.exceptions.UnauthorizedProductImpersonationException;
import com.intuit.v4.GlobalId;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;


/** Author: <PERSON><PERSON> Date: 17/01/20 Description: Provide some generic method across WAS */
public final class WasUtils {

  // Invoice-Approval$11$c60ee4e8-38f1-11ea-95fb-26a7576e130e

  /**
   * This will unmask the definition id; so that can be utilize in camunda query Basically replaceMessageFormat.format("_{0}", realmId, "");
   * '$' with ':'
   *
   * @param definitionId like Invoice-Approval$11$c60ee4e8-38f1-11ea-95fb-26a7576e130e
   * @return like Invoice-Approval:11:c60ee4e8-38f1-11ea-95fb-26a7576e130e
   */
  public static String unMaskDefinitionId(final String definitionId) {

    if (StringUtils.isEmpty(definitionId))
      throw new IllegalArgumentException("Definition id or Definition key can't be null or empty");

    return definitionId.replace("$", ":");
  }

  /**
   * @param originalId given id which is defined as per {@link
   *     com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId}
   * @return This method takes ids in above form and reset to entityId_RealmID_UUID -> entityId
   */
  public static String unWrapId(final String originalId, final String realmId) {

    String appender =MessageFormat.format("_{0}", realmId);
    final int index = originalId.indexOf(appender);
    // if index present
    if (index > 0) {
      return originalId.substring(0, index);
    }
    return originalId;
  }

  /**
   * Get UUID from given Global id if present
   *
   * @param gid gid
   * @param realmId realmId
   * @return UUID otherwise null
   */
  public static String getUUID(GlobalId gid, String realmId) {
    if (gid.getLocalId() != null) {
      String originalId = gid.getLocalId();

      String appender = MessageFormat.format("_{0}", realmId);
      int index = originalId.indexOf(appender);
      if (index > 0) {
        index += appender.length()+1;
        return originalId.substring(index);
      }
    }
    return null;
  }

  /**
   * Get swimlane(A/B) from the environment
   * @param environment
   * @return Swimlane value A/B
   */
  public static String getSwimlane(String environment) {
    String prefix = StringUtils.EMPTY;
    if (Objects.nonNull(environment)) {
      prefix = environment.split(WorkflowConstants.HYPHEN)[0];
      return prefix.substring(prefix.length() - 1);
    }
    return prefix;
  }
  /**
   * Fetches the hostaddress of the machine, on failure returns 127.0.0.1
   * @return hostAddress
   */
  public static String getHostAddress() {
    try {
       return InetAddress.getLocalHost().getHostAddress();
    }
    catch (UnknownHostException e){
      WorkflowLogger.logWarn(e, "Error when setting the IP");
    }
    return "127.0.0.1";
  }

  /**
   * Check the given ownerId is test drive realm or not
   *
   * @param ownerId
   * @return
   */
  public static boolean isTestDriveRealm(String ownerId) {
    // all test drive companies start with "462*"
    return StringUtils.isNotBlank(ownerId) && StringUtils.startsWith(ownerId, "462");
  }


  /**
   * Check if the given exception is of type Unauthorized Product Impersonation
   *
   * @param ex
   * @return
   */
  public static boolean isUnauthorisedException(Exception ex) {
    if(!Objects.isNull(ex)) {
      // UnauthorizedProductImpersonationException is thrown when realm is either locked out or unauthorized.
      return ex instanceof OfflineTicketClientException && ex.getCause() instanceof UnauthorizedProductImpersonationException;
    }
    return false;
  }
}
