package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.google.common.base.Verify;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.v4.interaction.InteractionResults;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Retry(name = ResiliencyConstants.V4_GRAPHQL_CLIENT)
@RequiredArgsConstructor
public class V4GraphqlClient extends GraphqlClient {

  public static final String GRAPHQL_READ_METHOD = "READ";
  public static final String GRAPHQL_MUTATE_METHOD = "MUTATE";
	
  @Override
  @ServiceMetric(type=ServiceType.V4_GRAPHQL, methodName = GRAPHQL_MUTATE_METHOD)
  public <T> WASV4GraphqlResponse<T> write(WASV4GraphqlRequest wasGraphqlRequest) {
    return executeV4Supplier(
        () -> {
          try {
            InteractionResults interactionResults = getContext(wasGraphqlRequest)
                .interaction()
                .write(wasGraphqlRequest.getRequest())
                .executeSync();
            return buildResponse(interactionResults);
          } catch (Exception e) {
            // We are wrapping it under WorkflowGeneralException to avoid checked exceptions from v4-SDK
            throw new WorkflowGeneralException(e);
          }
        }
    );
  }

  @Override
  @ServiceMetric(type=ServiceType.V4_GRAPHQL, methodName = GRAPHQL_READ_METHOD)
  public <T> WASV4GraphqlResponse<T> read(WASV4GraphqlRequest wasGraphqlRequest) {
    return executeV4Supplier(
        () -> {
          try {
            InteractionResults interactionResults = getContext(wasGraphqlRequest)
                .interaction()
                .read(wasGraphqlRequest.getQuery())
                .executeSync();
            return buildResponse(interactionResults);
          } catch (Exception ex) {
            // We are wrapping it under WorkflowGeneralException to avoid checked exceptions from v4-SDK
            throw new WorkflowGeneralException(ex);
          }
        }
    );
  }

  @ServiceMetric(type=ServiceType.V4_GRAPHQL, methodName = GRAPHQL_READ_METHOD)
  public <T> WASV4GraphqlResponse<T> readList(WASV4GraphqlRequest wasGraphqlRequest) {
    return executeV4Supplier(
        () -> {
          try {
            InteractionResults interactionResults = getContext(wasGraphqlRequest)
                .interaction()
                .read(wasGraphqlRequest.getQuery())
                .executeSync();
            return buildListResponse(interactionResults);
          } catch (Exception ex) {
            // We are wrapping it under WorkflowGeneralException to avoid checked exceptions from v4-SDK
            throw new WorkflowGeneralException(ex);
          }
        }
    );
  }

  /**
   * prepares the {@link WASV4GraphqlResponse} by setting response and error if exception occurs in
   * executing v4graphql supplier.
   *
   * @param <T>   generic response type
   * @param input input lambda
   * @return {@link WASV4GraphqlResponse}
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException if error is of retryable type.
   */
  private <T> WASV4GraphqlResponse<T> executeV4Supplier(
      final Supplier<WASV4GraphqlResponse<T>> input) {
    Verify.verifyNotNull(input, WorkflowConstants.INPUT_CANNOT_BE_NULL);
    try {
      return input.get();
    } catch (WorkflowGeneralException ex) {
      // The supplier execution throws WorkflowGeneralException always and
      //actual exception is under the cause. We have to look for the cause to get actual error.
      Exception exception = (Exception) ex.getCause();
      logError(String.format(GRAPHQL_CALL_ERROR_MESSAGE, ExceptionUtils.getMessage(exception)));
      WASRetryHandler.getHandler(RetryHandlerName.V4_GRAPHQL_STATUS_CODE)
          .checkAndThrowRetriableException(exception);
      return buildErrorResponse(exception);
    }
  }
}
