package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.v4.Error;
import com.intuit.v4.util.RequestMetrics;

import java.util.List;

import lombok.Builder;
import lombok.Getter;

/**
 * The HttpResponse object of WAS.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
@Getter
@Builder(toBuilder = true)
public final class WASV4GraphqlResponse<T> {

  // downstream response
  private final T response;

  // if response has any error
  private final boolean error;

  // list of error
  private final List<Error> errors;

  // request metrics like tid,sdk version etc
  private final RequestMetrics requestMetrics;
}
