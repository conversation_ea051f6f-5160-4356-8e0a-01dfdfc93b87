package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.APP_CONNECT;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Http client used to call appConnect api's. This has retry & circuitBreaker capabilities for
 * appConnect
 *
 * <AUTHOR>
 */
@Retry(name = ResiliencyConstants.APP_CONNECT)
@Component
public class AppConnectWASClient {

  @Autowired private WASHttpClient client;

  @ServiceMetric(serviceName = APP_CONNECT, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
    final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {

    return client.httpResponse(wasHttpRequest);
  }
}
