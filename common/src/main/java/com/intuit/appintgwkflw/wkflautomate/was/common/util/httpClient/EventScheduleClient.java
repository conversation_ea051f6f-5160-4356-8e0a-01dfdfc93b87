package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.EVENT_SCHEDULE;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class EventScheduleClient {
  private WASHttpClient client;

  /**
   * Pushing Event Scheduler metrics and invokes API
   *
   * @param wasHttpRequest request
   * @return http Response
   */
  @ServiceMetric(serviceName = EVENT_SCHEDULE, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {

    return client.httpResponse(wasHttpRequest);
  }
}
