package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import io.github.resilience4j.retry.annotation.Retry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.UCS;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;

/**
 * <AUTHOR> Enabling resiliency for UCS API Calls
 */
@Retry(name = ResiliencyConstants.UCS)
@Component
public class UCSHttpClient {

  @Autowired private WASHttpClient client;

  @ServiceMetric(serviceName = UCS, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {
    return client.httpResponse(wasHttpRequest);
  }
}
