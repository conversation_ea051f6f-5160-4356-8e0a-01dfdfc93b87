package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.intuit.appintgwkflw.wkflautomate.was.common.observability.TagUtils;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.Tracer.SpanBuilder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class JpaTraceAspectTest {

  private final Tracer tracer = getMockTracer();

  @Mock
  private TagUtils tagUtils;

  @InjectMocks
  private JpaTraceAspect jpaTraceAspect;

  @Test
  public void whenInvokeAspect_andSuccessfulExecution_thenSuccess() throws Throwable {

    final ProceedingJoinPoint joinPoint = getMockProceedingJoinPoint();
    try {
      jpaTraceAspect.aroundJpaCalls(joinPoint);
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = NullPointerException.class)
  public void whenInvokeAspect_throwsException_thenThrowBack() throws Throwable {

    final ProceedingJoinPoint joinPoint = getMockProceedingJoinPoint();
    doThrow(NullPointerException.class).when(joinPoint).proceed();
    jpaTraceAspect.aroundJpaCalls(joinPoint);
  }

  private ProceedingJoinPoint getMockProceedingJoinPoint() {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final Signature signature = mock(Signature.class);
    doReturn("method").when(signature).getName();
    doReturn(signature).when(joinPoint).getSignature();
    return joinPoint;
  }

  private Tracer getMockTracer() {

    final Tracer tracer = mock(Tracer.class);
    final SpanBuilder spanBuilder = mock(SpanBuilder.class);
    final Span span = mock(Span.class);
    doReturn(span).when(spanBuilder).start();
    doReturn(spanBuilder).when(tracer).buildSpan(anyString());
    return tracer;
  }
}