package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import io.opentracing.Tracer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class MetricAspectTest {

  @Mock
  private MetricLogger metricLogger;

  @Mock
  private Tracer tracer;

  @InjectMocks private MetricAspect metricAspect;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testsucess() throws Throwable {
    final ProceedingJoinPoint point = Mockito.mock(ProceedingJoinPoint.class);
    final Metric metricDetails = Mockito.mock(Metric.class);
    Mockito.when(metricDetails.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
    try {
      metricAspect.metric(point, metricDetails);
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = NullPointerException.class)
  public void testFailureNLP() throws Throwable {
    final ProceedingJoinPoint point = Mockito.mock(ProceedingJoinPoint.class);
    final Metric metricDetails = Mockito.mock(Metric.class);
    Mockito.when(metricDetails.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
    Mockito.when(point.proceed()).thenThrow(new NullPointerException());
    metricAspect.metric(point, metricDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFailure() throws Throwable {
    final ProceedingJoinPoint point = Mockito.mock(ProceedingJoinPoint.class);
    final Metric metricDetails = Mockito.mock(Metric.class);
    Mockito.when(metricDetails.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
    Mockito.when(point.proceed())
        .thenThrow(new WorkflowGeneralException(WorkflowError.UNKNOWN_ERROR));
    metricAspect.metric(point, metricDetails);
  }
}
