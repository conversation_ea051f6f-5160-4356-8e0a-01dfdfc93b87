package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.MISSING_EVENT_HEADERS;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNKNOWN_ERROR;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MetricsConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.metrics.MicrometerManager;

import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.identity.authn.offline.sdk.exceptions.UnauthorizedProductImpersonationException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;

import org.hibernate.exception.GenericJDBCException;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.postgresql.util.PSQLException;
import org.postgresql.util.PSQLState;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MetricLoggerTest {

  @Mock
  private MicrometerManager micrometerManager;

  @Mock private MetricsConfig metricsConfig;

  @InjectMocks
  private MetricLogger metricLogger;

  @Test
  public void testLogErrorUnknownException() {
    try {
      metricLogger.logErrorMetric(
          MetricName.DELETE_ALL_WORKFLOWS, Type.APPLICATION_METRIC, new RuntimeException());
      Mockito.verify(micrometerManager, Mockito.times(1)).recordCount(Mockito.any());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testLogErrorWorkflowGeneralException() {
    try {
      metricLogger.logErrorMetric(
          MetricName.DELETE_ALL_WORKFLOWS,
          Type.APPLICATION_METRIC,
          new WorkflowGeneralException(UNKNOWN_ERROR));
      Mockito.verify(micrometerManager, Mockito.times(1)).recordCount(Mockito.any());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testLogLatency() {
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.LATENCY});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      Mockito.when(metric.log()).thenReturn(true);
      metricLogger.logLatencyMetric(metric, Instant.now());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testLogErrorMetric() {
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.ERROR});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      metricLogger.logErrorMetric(metric, new RuntimeException());
      Mockito.verify(micrometerManager, Mockito.times(1)).recordCount(Mockito.any());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void test_notLogErrorMetric() {
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.EVENT_EXTERNAL_TASK);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.ERROR});
      Mockito.when(metric.type()).thenReturn(Type.EVENT_METRIC);
      metricLogger.logErrorMetric(metric, new WorkflowGeneralException(MISSING_EVENT_HEADERS,"Missing Event Headers"));
      Mockito.verify(micrometerManager, Mockito.times(0)).recordCount(Mockito.any());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testLogLatencyOwnerIdEnabled() {
    Mockito.when(metricsConfig.isEmitOwnerId()).thenReturn(true);
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.LATENCY});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      Mockito.when(metric.log()).thenReturn(true);
      metricLogger.logLatencyMetric(metric, Instant.now());
    } catch (final Exception e) {
      Assert.fail();
    }
  }
  
  @Test
  public void testLogLatencyWorkerEnabled() {
    Mockito.when(metricsConfig.isEmitWorkflowName()).thenReturn(true);
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.LATENCY});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      metricLogger.logLatencyMetric(metric, Instant.now());
    } catch (final Exception e) {
      Assert.fail();
    }
  }
  
  @Test
  public void testLogLatencyWorkerAndOwnerIdEnabled() {
    Mockito.when(metricsConfig.isEmitWorkflowName()).thenReturn(true);
    Mockito.when(metricsConfig.isEmitOwnerId()).thenReturn(true);
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.LATENCY});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      metricLogger.logLatencyMetric(metric, Instant.now());
    } catch (final Exception e) {
      Assert.fail();
    }
  }
  

  @Test
  public void testLogErrorMetricOwnerIdEnabled() {
    Mockito.when(metricsConfig.isEmitOwnerId()).thenReturn(true);
    try {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_WORKFLOW_DEFINITION);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.ERROR});
      Mockito.when(metric.type()).thenReturn(Type.API_METRIC);
      metricLogger.logErrorMetric(metric, new RuntimeException());
      Mockito.verify(micrometerManager, Mockito.times(1)).recordCount(Mockito.any());
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void test_getIsWarningFalseAndSuppress_PSQLException_warningTrue() {
    try(MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.ERROR});
      Mockito.when(metric.name()).thenReturn(MetricName.CREATE_CALLED_PROCESS);
      GenericJDBCException genericJDBCException = getWorkflowGeneralExceptionWithPSQLDuplicateKeyErrorInCreateChildProcess();
      metricLogger.logErrorMetric(metric, genericJDBCException);

      Mockito.verify(micrometerManager, Mockito.times(0)).recordCount(Mockito.any());
      mockedLogger.verify(Mockito.times(1), () -> WorkflowLogger.warn(ArgumentMatchers.any()));
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @NotNull
  private static GenericJDBCException getWorkflowGeneralExceptionWithPSQLDuplicateKeyErrorInCreateChildProcess() {
    GenericJDBCException genericJDBCException = new GenericJDBCException("could not execute statement",
        new PSQLException("ERROR: current transaction is aborted, commands ignored until end of transaction block", PSQLState.IN_FAILED_SQL_TRANSACTION, new PSQLException("ERROR: duplicate key value violates unique constraint \"ru_process_details_pkey\"", PSQLState.UNIQUE_VIOLATION)));

      return genericJDBCException;
  }

  @Test
  public void test_getIsWarningFalseAndSuppress_SomeOtherException_PSQLException_warningFalse() {
    try(MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      final Metric metric = Mockito.mock(Metric.class);
      Mockito.when(metric.name()).thenReturn(MetricName.EVENT_EXTERNAL_TASK);
      Mockito.when(metric.metricType()).thenReturn(new MetricType[] {MetricType.ERROR});
      Mockito.when(metric.type()).thenReturn(Type.EVENT_METRIC);
      PSQLException psqlException = new PSQLException("UNKNOWN_STATE", PSQLState.UNKNOWN_STATE);

      metricLogger.logErrorMetric(metric, new WorkflowGeneralException(new WorkflowGeneralException(psqlException)));

      Mockito.verify(micrometerManager, Mockito.times(1)).recordCount(Mockito.any());
      mockedLogger.verify(Mockito.times(0), () -> WorkflowLogger.warn(ArgumentMatchers.any()));
    } catch (final Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testUnauthorizedProductImpersonationException()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
      final Metric metric = Mockito.mock(Metric.class);
      OfflineTicketClientException offlineTicketClientException = new OfflineTicketClientException(new UnauthorizedProductImpersonationException("Target account does not subscribe to a product that the offline job has access to"));
      Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
      method.setAccessible(true);

      boolean result = (boolean) method.invoke(metricLogger, offlineTicketClientException, metric);
      Assert.assertTrue("Expected warning for UnauthorizedProductImpersonationException exception", result);
    }

  @Test
  public void testUnauthorizedResourceAccessWarning() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    WorkflowGeneralException unauthorizedException = Mockito.mock(WorkflowGeneralException.class);
    Mockito.when(unauthorizedException.getWorkflowError()).thenReturn(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
    Metric mockMetric = Mockito.mock(Metric.class);
    Mockito.when(mockMetric.name()).thenReturn(MetricName.GET_WORKFLOW_TASKS);

    Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
    method.setAccessible(true);

    boolean result = (boolean) method.invoke(metricLogger, unauthorizedException, mockMetric);
    Assert.assertTrue("Expected warning for UNAUTHORIZED_RESOURCE_ACCESS", result);
  }

  @Test
  public void testCreateChildProcessWarningWithDuplicateKeyError() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    GenericJDBCException genericJDBCException = getWorkflowGeneralExceptionWithPSQLDuplicateKeyErrorInCreateChildProcess();

    Metric mockMetric = Mockito.mock(Metric.class);
    Mockito.when(mockMetric.name()).thenReturn(MetricName.CREATE_CALLED_PROCESS);
    Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
    method.setAccessible(true);

    boolean result = (boolean) method.invoke(metricLogger, genericJDBCException, mockMetric);

    Assert.assertTrue("Expected warning for CREATE_CHILD_PROCESS_GENERIC_JDBC_EXCEPTION with duplicate key error", result);
  }

  @Test
  public void testCreateChildProcessWarningWithDuplicateKeyErrorForDomainEventMetric() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    GenericJDBCException genericJDBCException = getWorkflowGeneralExceptionWithPSQLDuplicateKeyErrorInCreateChildProcess();

    Metric mockMetric = Mockito.mock(Metric.class);
    Mockito.when(mockMetric.name()).thenReturn(MetricName.DOMAIN_EVENT_PUBLISH);
    Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
    method.setAccessible(true);

    boolean result = (boolean) method.invoke(metricLogger, genericJDBCException, mockMetric);

    Assert.assertTrue("Expected warning for CREATE_CHILD_PROCESS_GENERIC_JDBC_EXCEPTION with duplicate key error", result);
  }

  @Test
  public void testCreateChildProcessWarningWithDuplicateKeyErrorForOtherMetric() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    GenericJDBCException genericJDBCException = getWorkflowGeneralExceptionWithPSQLDuplicateKeyErrorInCreateChildProcess();


    Metric mockMetric = Mockito.mock(Metric.class);
    Mockito.when(mockMetric.name()).thenReturn(MetricName.TEST);
    Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
    method.setAccessible(true);

    boolean result = (boolean) method.invoke(metricLogger, genericJDBCException, mockMetric);

    Assert.assertFalse("UnExpected warning for CREATE_CHILD_PROCESS_GENERIC_JDBC_EXCEPTION with duplicate key error", result);
  }

  @Test
  public void testOtherError() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Metric mockMetric = Mockito.mock(Metric.class);
    WorkflowGeneralException otherException = Mockito.mock(WorkflowGeneralException.class);
    Mockito.when(otherException.getWorkflowError()).thenReturn(WorkflowError.OTHER_TEST);

    Method method = MetricLogger.class.getDeclaredMethod("getIsWarning", Throwable.class, Metric.class);
    method.setAccessible(true);

    boolean result = (boolean) method.invoke(metricLogger, otherException, mockMetric);

    Assert.assertFalse("Did not expect warning for OTHER_TEST error", result);
  }



}
