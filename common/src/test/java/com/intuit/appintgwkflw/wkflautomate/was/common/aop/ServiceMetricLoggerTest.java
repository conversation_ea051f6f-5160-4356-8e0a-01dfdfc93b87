package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.metrics.MicrometerManager;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ServiceMetricLoggerTest {

  @Mock
  private MicrometerManager micrometerManager;

  @InjectMocks
  private ServiceMetricLogger serviceMetricLogger;

  @Test
  public void testLogLatency_serviceName() {
    serviceMetricLogger.logLatencyMetric("HUMAN_TASK", Duration.of(10, ChronoUnit.MILLIS));
    verify(micrometerManager).recordTime(any(), any());
  }

  @Test
  public void testLogErrorMetric_serviceName() {
    serviceMetricLogger.logErrorMetric("HUMAN_TASK", WorkflowError.UNKNOWN_ERROR.name());
    verify(micrometerManager).recordCount(any());
  }
  
  @Test
  public void testLogLatency_AppConnect() {
    serviceMetricLogger.logLatencyMetric("APP_CONNECT", Duration.of(10, ChronoUnit.MILLIS));
    verify(micrometerManager).recordTime(any(), any());
  }
  
  @Test
  public void testLogErrorMetric_AppConnect() {
    serviceMetricLogger.logErrorMetric("APP_CONNECT", WorkflowError.UNKNOWN_ERROR.name());
    verify(micrometerManager).recordCount(any());
  }
  
}
