package com.intuit.appintgwkflw.wkflautomate.was.common.authz;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthorizationConfig;
import com.intuit.identity.authz.sdk.client.AuthZClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class WASAuthzClientTest {

  @Mock private AppConfig appConfig;

  @Mock private AuthorizationConfig authorizationConfig;

  @InjectMocks private WASAuthZClient wasAuthZClient;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConfig.getAppId()).thenReturn("appid");
    Mockito.when(appConfig.getAppSecret()).thenReturn("secretId");
    Mockito.when(authorizationConfig.getEnv()).thenReturn("QAL");
    Mockito.when(authorizationConfig.getConnectionTimeOutMs()).thenReturn(1000);
  }

  @Before
  public void initWithException() throws Exception {
    try {
      wasAuthZClient.init();
    } catch (Exception e) {
      Assert.assertTrue(e.getMessage().contains("FAILED_TO_INITIALIZE_AUTHZ_CLIENT"));
    }
  }

  @Test
  public void getAuthzClientTest() {
    AuthZClient authZClientTest = wasAuthZClient.getAuthZClient();
    Assert.assertNotNull(authZClientTest);
  }
}
