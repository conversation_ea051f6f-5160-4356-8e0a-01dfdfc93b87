package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class EventConfigurationTest {
    @Test
    public void test_eventConsumerRegionConfig_primary() {
        EventConfiguration eventConfiguration = getEventConfiguration(false);
        assertEquals("primary", eventConfiguration.getConsumerRegionConfig().getBootStrapServers().get(0));
    }

    @Test
    public void test_eventConsumerRegionConfig_secondary() {
        EventConfiguration eventConfiguration = getEventConfiguration(true);
        assertEquals("secondary", eventConfiguration.getConsumerRegionConfig().getBootStrapServers().get(0));
    }

    @Test
    public void test_eventConsumerRegionConfig_secondaryRegion_withoutCrossRegionConfig() {
        EventConfiguration eventConfiguration = getEventConfiguration(true);
        eventConfiguration.setSecondary(null);
        assertEquals("primary", eventConfiguration.getConsumerRegionConfig().getBootStrapServers().get(0));
    }

    @Test
    public void test_eventProducerRegionConfig_primary() {
        EventConfiguration eventConfiguration = getEventConfiguration(false);
        assertEquals("primary", eventConfiguration.getProducerRegionConfig().getBootStrapServers().get(0));
    }

    @Test
    public void test_eventProducerRegionConfig_secondary() {
        EventConfiguration eventConfiguration = getEventConfiguration(true);
        assertEquals("secondary", eventConfiguration.getProducerRegionConfig().getBootStrapServers().get(0));
    }

    @Test
    public void test_eventProducerRegionConfig_secondaryRegion_withoutCrossRegionConfig() {
        EventConfiguration eventConfiguration = getEventConfiguration(true);
        eventConfiguration.setSecondary(null);
        assertEquals("primary", eventConfiguration.getProducerRegionConfig().getBootStrapServers().get(0));
    }

    private EventConfiguration getEventConfiguration(Boolean crossRegion) {
        EventConfiguration eventConfiguration = new EventConfiguration();
        EventRegionConfig eventRegionConfig = new EventRegionConfig();
        eventRegionConfig.setBootStrapServers(Collections.singletonList("primary"));
        EventRegionConfig eventCrossRegionConfig = new EventRegionConfig();
        eventCrossRegionConfig.setBootStrapServers(Collections.singletonList("secondary"));

        eventConfiguration.setPrimary(eventRegionConfig);
        eventConfiguration.setSecondary(eventCrossRegionConfig);

        EventConsumer eventConsumer = new EventConsumer();
        eventConsumer.setEnableSecondary(crossRegion);

        eventConfiguration.setConsumer(eventConsumer);

        EventProducer eventProducer = new EventProducer();
        eventProducer.setEnableSecondary(crossRegion);

        eventConfiguration.setProducer(eventProducer);

        return eventConfiguration;
    }
}
