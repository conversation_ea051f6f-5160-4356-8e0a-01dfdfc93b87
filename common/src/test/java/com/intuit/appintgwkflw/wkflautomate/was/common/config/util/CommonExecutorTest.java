package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.google.common.base.VerifyException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import org.junit.Assert;
import org.junit.Test;

/** <AUTHOR> */
public class CommonExecutorTest {
  private static final String TEST = "TEST";

  @Test(expected = VerifyException.class)
  public void testExecuteNull() {
    CommonExecutor.execute(null, null);
  }

  @Test(expected = VerifyException.class)
  public void testExecuteOneNull() {
    CommonExecutor.execute(() -> "hello", null);
  }

  @Test
  public void testExecute() {
    String response = CommonExecutor.execute(() -> "hello", "cannot be empty");
    Assert.assertEquals("hello", response);
  }

  @Test
  public void testExecuteException() {
    Integer response =
        CommonExecutor.execute(
            () -> {
              return 1 / 0;
            },
            "Some Exception occured");
    Assert.assertNull(response);
  }

  @Test(expected = VerifyException.class)
  public void testExecuteOverloadNull() {
    CommonExecutor.execute(null, null, null);
  }

  @Test(expected = VerifyException.class)
  public void testExecuteOverloadOneNull() {
    CommonExecutor.execute(() -> {}, null, null);
  }

  @Test(expected = VerifyException.class)
  public void testExecuteErrorMessageNull() {
    CommonExecutor.execute(() -> {}, null, (exception) -> {});
  }

  @Test
  public void testExecuteSuccess() {
    try {
      CommonExecutor.execute(() -> {}, TEST, (exception) -> {});
    } catch (Exception e) {
      Assert.fail("method execute should throw exception");
    }
  }

  @Test
  public void testExecuteExceptionWithLogFn() {
    try {
      CommonExecutor.execute(
          () -> {
            int i = 1 / 0;
          },
          TEST,
          (exception) -> {});
    } catch (Exception e) {
      Assert.fail("method execute should throw exception");
    }
  }
}
