package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class HeaderPopulatorTest {

  @InjectMocks private HeaderPopulator headerPopulator;

  @Mock
  private WASContextHandler wasContextHandler;

  @Mock private AppConfig appConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConfig.getAppId()).thenReturn("appid");
    Mockito.when(appConfig.getAppSecret()).thenReturn("secretId");
  }

  @Test
  public void constructAuthzHeaderNullEmpty() {
    Assert.assertNull(headerPopulator.constructAuthzHeader(null));
    Assert.assertNull(headerPopulator.constructAuthzHeader(""));
  }

  @Test
  public void constructAuthzHeader() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_realmid=\"yyy\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    String generatedHeader = headerPopulator.constructAuthzHeader(header);
    Assert.assertTrue(generatedHeader.contains("Intuit_IAM_Authentication"));
    Assert.assertTrue(generatedHeader.contains("intuit_appid=appid"));
    Assert.assertTrue(generatedHeader.contains("intuit_app_secret=secretId"));
  }

  @Test
  public void constructAuthzHeaderForOfflineTkt() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Offline-Ticket\",intuit_realmid=\"yyy\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    String generatedHeader = headerPopulator.constructAuthzHeader(header);
    Assert.assertTrue(generatedHeader.contains("Intuit_IAM_Authentication"));
    Assert.assertTrue(generatedHeader.contains("intuit_appid=appid"));
    Assert.assertTrue(generatedHeader.contains("intuit_app_secret=secretId"));
    Assert.assertTrue(generatedHeader.contains("IAM-Offline-Ticket"));
  }

  @Test
  public void constructAuthzHeaderFromContext() {
    Mockito.when(wasContextHandler.get(WASContextEnums.AUTHORIZATION_HEADER))
            .thenReturn("Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_realmid=\"yyy\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx");
    String generatedHeader = headerPopulator.constructAuthzHeaderFromContext();
    Assert.assertTrue(generatedHeader.contains("Intuit_IAM_Authentication"));
    Assert.assertTrue(generatedHeader.contains("intuit_appid=appid"));
    Assert.assertTrue(generatedHeader.contains("intuit_app_secret=secretId"));
  }
}
