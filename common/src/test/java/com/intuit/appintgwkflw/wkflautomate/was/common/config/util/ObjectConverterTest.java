package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import java.io.InputStream;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/** <AUTHOR> */
public class ObjectConverterTest {

  @Test
  @SuppressWarnings({"unchecked", "rawtypes"})
  public void testToJsonSuccess() {
    Map map = new HashMap<>();
    map.put("1", "1");
    String resp = ObjectConverter.toJson(map);
    Assert.assertNotNull(resp);
  }

  @Test
  public void testToJsonFailure() {
    String resp = ObjectConverter.toJson(null);
    Assert.assertNull(resp);
  }

  @SuppressWarnings("rawtypes")
  @Test
  public void testfromJsonSuccess() {
    Map resp = ObjectConverter.fromJson("{\"key\":\"value\"}", Map.class);
    Assert.assertNotNull(resp);
  }

  @SuppressWarnings("rawtypes")
  @Test
  public void testfromJsonFailure() {
    Map resp = ObjectConverter.fromJson(null, Map.class);
    Assert.assertNull(resp);
  }

  @Test
  public void testfromSuccess() {
    HashMap<String, Object> resp =
        ObjectConverter.fromJson(
            "{\"key\":\"value\"}", new TypeReference<HashMap<String, Object>>() {});
    Assert.assertNotNull(resp);
  }

  @Test
  public void testfromFailure() {
    HashMap<String, Object> resp =
        ObjectConverter.fromJson(null, new TypeReference<HashMap<String, Object>>() {});
    Assert.assertNull(resp);
  }

  @Test
  public void testConvertObjectFailure() {
    Map resp = ObjectConverter.convertObject("Test", Map.class);
    Assert.assertNull(resp);
  }

  @Test
  public void testConvertObjectTypeReferenceFailure() {
    Map resp = ObjectConverter.convertObject("Test", new TypeReference<Map<String, Object>>() {});
    Assert.assertNull(resp);
  }

  @Test
  public void testFromJsonStream() {
    InputStream is = this.getClass().getClassLoader().getResourceAsStream("test.json");
    Map<String, Object> response = ObjectConverter.fromJsonStream(is, new TypeReference<>() {});
    Assert.assertNotNull(response);
    Assert.assertTrue(response.containsKey("key1"));
    Assert.assertTrue(response.containsKey("key2"));
  }
}
