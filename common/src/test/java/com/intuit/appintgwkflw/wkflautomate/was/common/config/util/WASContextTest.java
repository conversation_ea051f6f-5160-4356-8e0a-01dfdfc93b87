package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Test;

public class WASContextTest {

  @Test
  public void testWASAuthContext() {
    Authorization authContext = new Authorization();
    authContext.putRealm("1234");
    authContext.put(WorkflowConstants.APP_ID, "ttlive");
    WASContext.setAuthContext(authContext);
    Assert.assertEquals("ttlive", WASContext.getApplicationId().get());
    Assert.assertEquals(Long.valueOf(1234), WASContext.getOwnerId());

    WASContext.clear();
    Assert.assertFalse(WASContext.getApplicationId().isPresent());
    Assert.assertNull(WASContext.getOwnerId());
  }

  @Test
  public void testEmptyWASAuthContext() {
    Assert.assertFalse(WASContext.getApplicationId().isPresent());
    Assert.assertNull(WASContext.getOwnerId());
  }
}
