package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import java.util.Collections;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.HashMap;
import java.util.Map;
import org.mockito.junit.MockitoJUnitRunner;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeatureFlagManagerTest {

  @Mock
  private FeatureFlagFactory featureFlagFactory;

  @Mock
  private FeatureManager featureManager;

  @InjectMocks
  private FeatureFlagManager featureFlagManager;

  @Test
  public void testGetBooleanWithContextMap() {
    // Setup mock behavior
    String flagName = "mockFeature";
    Map<String, Object> contextMap = new HashMap<>();
    contextMap.put("key1", "value1");
    contextMap.put("key2", 2);
    boolean expectedValue = true;
    when(featureFlagFactory.getInstance()).thenReturn(featureManager);
    when(featureManager.getBoolean(eq(flagName), eq(false), eq(contextMap), anyLong()))
        .thenReturn(expectedValue);

    // Call method to test
    boolean actualValue = featureFlagManager.getBooleanWithContextMap(
        flagName, false, contextMap, 12345L);

    // Verify results
    verify(featureFlagFactory).getInstance();
    verify(featureManager).getBoolean(eq(flagName), eq(false), eq(contextMap), anyLong());
    assertEquals(expectedValue, actualValue);
  }

  @Test
  public void testGetBoolean_workflowNameAndOwnerId() {
    // Setup mock behavior
    String flagName = "mockFeature2";
    String workflowName = "workflow1";
    Long ownerId = 54321L;
    boolean expectedValue = true;
    when(featureFlagFactory.getInstance()).thenReturn(featureManager);
    when(featureManager.getBoolean(eq(flagName), eq(false), eq(workflowName), eq(ownerId)))
        .thenReturn(expectedValue);

    // Call method to test
    boolean actualValue = featureFlagManager.getBoolean(
        flagName, false, workflowName, ownerId);

    // Verify results
    verify(featureFlagFactory).getInstance();
    verify(featureManager).getBoolean(eq(flagName), eq(false), eq(workflowName), eq(ownerId));
    assertEquals(expectedValue, actualValue);
  }
  @Test
  public void testGetBoolean_featureFlagWithoutContext() {
    // Setup mock behavior
    String flagName = "mockFeature";
    boolean defaultValue = false;
    boolean expectedValue = true;
    when(featureFlagFactory.getInstance()).thenReturn(featureManager);
    when(featureManager.getBoolean(eq(flagName), eq(defaultValue)))
        .thenReturn(expectedValue);

    // Call method to test
    boolean actualValue = featureFlagManager.getBoolean(
        flagName, defaultValue);

    // Verify results
    verify(featureFlagFactory).getInstance();
    verify(featureManager).getBoolean(eq(flagName), eq(defaultValue));
    assertEquals(expectedValue, actualValue);
  }

  @Test
  public void testGetBoolean_workflowName() {
    // Setup mock behavior
    String flagName = "mockFeature2";
    String workflowName = "workflow1";
    boolean defaultValue = true;
    boolean expectedValue = false;
    when(featureFlagFactory.getInstance()).thenReturn(featureManager);
    when(featureManager.getBoolean(eq(flagName), eq(defaultValue), eq(workflowName)))
        .thenReturn(expectedValue);

    // Call method to test
    boolean actualValue = featureFlagManager.getBoolean(
        flagName, defaultValue, workflowName);

    // Verify results
    verify(featureFlagFactory).getInstance();
    verify(featureManager).getBoolean(eq(flagName), eq(defaultValue), eq(workflowName));
    assertEquals(expectedValue, actualValue);
  }

  @Test
  public void testGetBoolean_legacy() {
    // Setup mock behavior
    String flagName = "mockFeature4";
    String ownerId = "12345";
    boolean expectedValue = true;
    when(featureFlagFactory.getInstance()).thenReturn(featureManager);
    when(featureManager.getBoolean(eq(flagName), eq(ownerId)))
        .thenReturn(expectedValue);

    // Call method to test
    boolean actualValue = featureFlagManager.getBoolean(
        flagName, ownerId);

    // Verify results
    verify(featureFlagFactory).getInstance();
    verify(featureManager).getBoolean(eq(flagName), eq(ownerId));
    assertEquals(expectedValue, actualValue);
  }

}

