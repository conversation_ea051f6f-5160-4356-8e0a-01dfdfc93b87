package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.IXPServiceConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.identity.exptplatform.assignment.entities.DefaultEntityIdImpl;
import com.intuit.identity.exptplatform.assignment.entities.EntityID;
import com.intuit.identity.exptplatform.featureflag.FeatureFlagClient;
import com.intuit.identity.exptplatform.featureflag.FeatureFlagParams;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.any;


@Import({IXPManager.class})
@RunWith(SpringRunner.class)
public class IXPManagerTest {

    @MockBean
    AppConfig appConfig;

    IXPManager ixpManager;

    @MockBean
    IXPServiceConfig ixpServiceConfig;

    @MockBean
    FeatureFlagClient featureFlagClient;

    @MockBean
    WASContextHandler wasContextHandler;

    private static final String IXP_DESCRIPTION = "IXPService";

    private static final String EMPTY_REALM_ID = "NO_REALM";

    private static final String SAMPLE_TID = "intuit_sample_tid";



    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn(SAMPLE_TID);
        ixpManager = new IXPManager(ixpServiceConfig, featureFlagClient, appConfig, wasContextHandler);
    }

    @DisplayName("Evaluate True for FF and ownerId")
    @Test
    public void shouldEvaluateTrue() {

        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("e2eb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("e2e");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))),
                        eq("e2e"), eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(true);

        Assert.assertTrue(ixpManager.getBoolean("qbo-adv-test", "1234"));
        assertTransactionId();
        WASContext.clear();
    }

    @DisplayName("Evaluate false for FF and ownerId")
    @Test
    public void shouldEvaluateFalse() {

        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("e2eb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("e2e");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))), eq("e2e"),
                        eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(false);

        Assert.assertFalse(ixpManager.getBoolean("qbo-adv-test", "1234"));
        assertTransactionId();
        WASContext.clear();
    }

    @DisplayName("Evaluate Boolean Variation for Empty realmId")
    @Test
    public void shouldEvaluateRealmIdNull() {

        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("e2eb-use2");

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("e2e");

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(null)),
                        eq("e2e"), eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(false);

        Assert.assertFalse(ixpManager.getBoolean("qbo-adv-test", null));
        assertTransactionId();
    }

    @DisplayName("Evaluate True for FF and default value")
    @Test
    public void evaluateBooleanVariation() {

        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("e2eb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("e2e");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))), eq("e2e"),
                        eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(true);

        Assert.assertTrue(ixpManager.getBoolean("qbo-adv-test", false));
        assertTransactionId();
        WASContext.clear();
    }

    @DisplayName("Evaluate True for FF and workflowName")
    @Test
    public void evaluateTrueFFWorkflowName() {
        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("e2eb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("e2e");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))), eq("e2e"),
                        eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(true);

        Assert.assertTrue(ixpManager.getBoolean("qbo-adv-test",false, "test-workflow-name"));
        assertTransactionId();
        WASContext.clear();
    }

    @DisplayName("Evaluate True for FF , realmID and workflowName")
    @Test
    public void evaluateTrueWorkflow() {
        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("qalb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("qal");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.WORKFLOW_NAME, "test-workflow-name");
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
                .context(contextMap)
                .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))), eq("qal"),
                        eq("qbo-adv-test"), eq(false), Mockito.any()))
                .thenReturn(true);

        Assert.assertTrue(ixpManager.getBoolean("qbo-adv-test",false, "test-workflow-name", 1234L));
        assertTransactionId();
        WASContext.clear();
    }

    @Test
    public void evaluateTrueWithContextMap() {
        Mockito.when(WasUtils.getSwimlane(appConfig.getEnv())).thenReturn("qalb-use2");

        Authorization authorization = new Authorization().realm("1234");
        WASContext.setAuthContext(authorization);

        Mockito.when(ixpServiceConfig.isEnabled()).thenReturn(true);
        Mockito.when(ixpServiceConfig.getSubEnv()).thenReturn("qal");

        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(WorkflowConstants.WORKFLOW_NAME, "test-workflow-name");
        contextMap.put(WorkflowConstants.SWIMLANE, WasUtils.getSwimlane(appConfig.getEnv()));
        contextMap.put(WorkflowConstants.INTUIT_REALMID, WASContext.getOwnerId());

        FeatureFlagParams featureFlagParams = new FeatureFlagParams.FeatureFlagParamsBuilder()
            .context(contextMap)
            .build();

        Mockito.when(featureFlagClient.evaluateBooleanVariation(eq(buildEntityId(Long.valueOf(authorization.getRealm()))), eq("qal"),
                eq("qbo-adv-test"), eq(false), Mockito.any()))
            .thenReturn(true);

        Assert.assertTrue(ixpManager.getBoolean("qbo-adv-test",false, contextMap, 1234L));
        assertTransactionId();
        WASContext.clear();
    }

    private  EntityID buildEntityId(Long realmId) {

        return DefaultEntityIdImpl
                .builder()
                .realmOrCompanyId(realmId != null ?  String.valueOf(realmId) : EMPTY_REALM_ID)
                .ns(IXP_DESCRIPTION)
                .build();
    }

    private void assertTransactionId() {
        ArgumentCaptor<FeatureFlagParams> actualFeatureFlagParam = ArgumentCaptor.forClass(FeatureFlagParams.class);
        Mockito.verify(featureFlagClient).evaluateBooleanVariation(
                any(), anyString(), anyString(), anyBoolean(), actualFeatureFlagParam.capture());
        Assert.assertEquals(SAMPLE_TID, actualFeatureFlagParam.getValue().getTransactionId());
    }

}