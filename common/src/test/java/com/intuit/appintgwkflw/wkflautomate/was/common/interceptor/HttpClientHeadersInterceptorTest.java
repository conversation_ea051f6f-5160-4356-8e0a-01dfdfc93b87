package com.intuit.appintgwkflw.wkflautomate.was.common.interceptor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import org.apache.http.*;
import org.apache.http.message.BasicHttpRequest;
import org.apache.http.protocol.HttpContext;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

@RunWith(MockitoJUnitRunner.Silent.class)
public class HttpClientHeadersInterceptorTest {

    @InjectMocks
    private HttpClientHeadersInterceptor httpClientHeadersInterceptor;
    private HttpRequest request;
    private HttpRequest dummyRequest;
    private HttpContext context;
    @Mock
    private WASContextHandler contextHandler;
    @Mock
    private HeaderPopulator headerPopulator;
    @Mock
    private OfflineTicketClient offlineTicketClient;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        request = new BasicHttpRequest("GET", "intuit.com");
        context = new HttpContext() {
            @Override
            public Object getAttribute(String s) {
                return null;
            }

            @Override
            public void setAttribute(String s, Object o) {

            }

            @Override
            public Object removeAttribute(String s) {
                return null;
            }
        };
    }


    @Test
    public void happyCase() throws HttpException, IOException {

        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID))
                .thenReturn("intuit_tid");
        Mockito.when(contextHandler.get(WASContextEnums.IS_EVENT))
                .thenReturn("isEvent");
        Mockito.when(headerPopulator.constructAuthzHeader("AuthDetail"))
                .thenReturn("headerPop");
        httpClientHeadersInterceptor.process(request,context);
        Assert.assertNotNull(request.getHeaders("intuit_tid"));
        Assert.assertNotNull(request.getHeaders("Authorization"));
    }

    @Test(expected = Exception.class)
    public void exceptionFlow() throws HttpException, IOException {
        httpClientHeadersInterceptor.process(dummyRequest,context);
    }

    @Test
    public void when_NonRealmSystemUser() throws HttpException, IOException {
        WASContext.setNonRealmSystemUserContext(true);
        Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()).thenReturn("offline-ticket");
        try {
            httpClientHeadersInterceptor.process(request, context);
            Assert.assertNotNull(request.getHeaders("intuit_tid"));
            Assert.assertNotNull(request.getHeaders(WorkflowConstants.AUTHORIZATION_HEADER));
            Assert.assertEquals("offline-ticket", request.getFirstHeader(WorkflowConstants.AUTHORIZATION_HEADER).getValue());
        }
        finally {
            WASContext.clear();
        }
    }
}
