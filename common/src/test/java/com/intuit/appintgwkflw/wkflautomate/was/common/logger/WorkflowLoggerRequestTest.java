package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import org.jboss.logging.MDC;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowLoggerRequestTest {

    public final static String TEST_URL = "Test";

  @After
  public void cleanUp() {
    WASContext.clear();
    MDC.clear();
  }

  @Test
  public void testLoggerRequestWithNullValues() {
    Assert.assertNotNull(
        WorkflowLoggerRequest.builder()
            .message(null)
            .stackTrace(null)
            .downstreamComponentName(null)
            .downstreamServiceName(null)
            .metricName(null)
            .metricType(null)
            .metricError(null)
            .latency(null)
            .type(null)
            .service(null)
            .url(null)
            .success(null)
            .status(null));
  }

    @Test
    public void testLoggerRequestUrl() {
        Assert.assertNotNull(WorkflowLoggerRequest.builder()
            .url(TEST_URL));
    }

  @Test
  public void testLoggerRequestOfferingIdDefault() {
    WorkflowLoggerRequest request = getRequest();
    Assert.assertEquals(
        WorkflowConstants.DEFAULT_OFFERING,
        request.getLoggingParams().get(WASContextEnums.OFFERING_ID.getValue()));
  }

  @Test
  public void testLoggerRequestOfferingIdMDC() {
    MDC.put(WASContextEnums.OFFERING_ID.getValue(), "test");
    WASContext.setOfferingId("test2");
    WorkflowLoggerRequest request = getRequest();
    Assert.assertEquals(
        "test", request.getLoggingParams().get(WASContextEnums.OFFERING_ID.getValue()));
  }

  @Test
  public void testLoggerRequestOfferingIdWASContext() {
    WASContext.setOfferingId("test2");
    WorkflowLoggerRequest request = getRequest();
    Assert.assertEquals(
        "test2", request.getLoggingParams().get(WASContextEnums.OFFERING_ID.getValue()));
  }

  private WorkflowLoggerRequest getRequest() {
    return WorkflowLoggerRequest.builder()
        .message(null)
        .stackTrace(null)
        .downstreamComponentName(null)
        .downstreamServiceName(null)
        .metricName(null)
        .metricType(null)
        .metricError(null)
        .latency(null)
        .type(null)
        .service(null)
        .url(null)
        .success(null)
        .status(null)
        .build();
  }
}
