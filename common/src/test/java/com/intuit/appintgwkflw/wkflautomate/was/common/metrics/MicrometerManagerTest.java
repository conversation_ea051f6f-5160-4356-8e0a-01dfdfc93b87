package com.intuit.appintgwkflw.wkflautomate.was.common.metrics;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricData;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import org.junit.Test;
import org.mockito.Mockito;
import io.micrometer.core.instrument.Meter.Id;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

public class MicrometerManagerTest {

  private static final String METRIC_NAME = "";

  private final Duration duration = Duration.of(1, ChronoUnit.SECONDS);

  private final WASContextHandler contextHandler = mock(WASContextHandler.class);

  private final MeterRegistry meterRegistry = spy(SimpleMeterRegistry.class);

  private final MicrometerManager micrometerManager = new MicrometerManager(meterRegistry,
      contextHandler);

  @Test
  public void whenRecordTime_withValidMetricName_thenSuccess() {

    micrometerManager.recordTime(duration, MetricData.builder().name(METRIC_NAME).build());

    assertTrue(getId().getName().contains(METRIC_NAME));
  }

  @Test
  public void whenRecordTime_withInvalidMetricName_thenUseDefault() {

    micrometerManager.recordTime(duration, MetricData.builder().build());

    assertTrue(getId().getName().contains("method"));
  }

  @Test
  public void whenRecordTime_withAdditionalTags_thenSuccess() {

    final MetricData metricData = MetricData.builder().name(METRIC_NAME).build();
    metricData.addTag(WASContextEnums.HANDLER_ID.getValue());
    micrometerManager.recordTime(duration, metricData);

    assertTrue(getId().getName().contains(METRIC_NAME));
  }

  @Test
  public void whenRecordCount_withException_thenSuccess() {

    micrometerManager
        .recordCount(MetricData.builder().name(METRIC_NAME).exception("unknown_error").build());

    assertTrue(getId().getName().contains(METRIC_NAME));
    assertEquals("unknown_error", getId().getTag("exception"));
  }

  @Test
  public void whenRecordCount_withoutException_thenSuccess() {

    micrometerManager.recordCount(MetricData.builder().name(METRIC_NAME).build());

    assertTrue(getId().getName().contains(METRIC_NAME));
    assertEquals("none", getId().getTag("exception"));
  }

  @Test
  public void whenRecordCount_success_multiCount() {
    micrometerManager.recordCount(MetricData.builder().name(METRIC_NAME).build(), 1);
    assertTrue(getId().getName().contains(METRIC_NAME));
  }

  @Test
  public void whenRecordTime_throwsException_thenDoNothing() {

    doThrow(RuntimeException.class).when(meterRegistry).timer(anyString());

    micrometerManager.recordTime(duration, MetricData.builder().name(METRIC_NAME).build());

    Mockito.verify(meterRegistry, times(0)).timer(anyString());
  }

  private Id getId() {

    return meterRegistry.getMeters().get(0).getId();
  }
}