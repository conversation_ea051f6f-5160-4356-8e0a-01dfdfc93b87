package com.intuit.appintgwkflw.wkflautomate.was.common.observability;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import io.opentracing.Span;
import java.util.UUID;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TagUtilsTest {

  @Mock
  private WASContextHandler contextHandler;

  @InjectMocks
  private TagUtils tagUtils;

  @Test
  public void whenAddCommonTags_withoutMdcValues_thenSuccess() {

    final Span span = mock(Span.class);
    tagUtils.addCommonTags(span);

    verify(span, times(0)).setTag(anyString(), anyString());
  }

  @Test
  public void whenAddCommonTags_withMdcValues_thenSuccess() {

    doReturn(ImmutableMap.of(WASContextEnums.INTUIT_TID.getValue(), UUID.randomUUID().toString()))
        .when(contextHandler).getAll();
    final Span span = mock(Span.class);

    tagUtils.addCommonTags(span);

    verify(span).setTag(anyString(), anyString());
  }
}