package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import org.apache.http.NoHttpResponseException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

public class AppconnectDownstreamErrorsRetryHandlerTest {

    @InjectMocks private AppconnectDownstreamErrorsRetryHandler appconnectDownstreamErrorsRetryHandler;

    @Mock private RetryConfig retryConfig;

    private List<Integer> statusCodes = new ArrayList<Integer>();

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        statusCodes.add(500);
        Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);
    }

    @Test(expected = WorkflowRetriableException.class)
    public void test_checkAndThrowRetriableException_noHttpResponse() {
        NoHttpResponseException noHttpResponseException = Mockito.mock(NoHttpResponseException.class);
        appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(noHttpResponseException);
    }

    @Test(expected = WorkflowRetriableException.class)
    public void test_checkAndThrowRetriableException_httpStatusCode() {
        HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
        HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
        Mockito.when(httpStatus.value()).thenReturn(500);
        Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
        appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
    }

    @Test
    public void test_checkAndThrowRetriableException_httpStatusCode_noExceptionCase() {
        HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
        HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
        Mockito.when(httpStatus.value()).thenReturn(401);
        Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
        try {
            appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
        } catch(WorkflowRetriableException ex) {
            Assert.fail("Should not have thrown any exception");
        }
    }

    @Test(expected = WorkflowRetriableException.class)
    public void test_checkAndThrowRetriableException_downstreamErrorRetryCase() {
        HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
        HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
        Mockito.when(httpStatus.value()).thenReturn(400);

        WorkflowTaskHandlerResponse workflowTaskHandlerResponse = new WorkflowTaskHandlerResponse();
        workflowTaskHandlerResponse.setDownstreamResponseStatus("429");

        statusCodes.add(429);
        Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);

        Mockito.when(httpStatusCodeException.getResponseBodyAsString()).thenReturn(ObjectConverter.toJson(workflowTaskHandlerResponse));
        Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
        appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
    }

    @Test
    public void test_checkAndDoNotThrowException_downstreamErrorRetryCaseForNonIntegerStatusCode() {
        HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
        HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
        Mockito.when(httpStatus.value()).thenReturn(400);

        WorkflowTaskHandlerResponse workflowTaskHandlerResponse = new WorkflowTaskHandlerResponse();
        workflowTaskHandlerResponse.setDownstreamResponseStatus("abc");

        statusCodes.add(429);
        Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);

        Mockito.when(httpStatusCodeException.getResponseBodyAsString()).thenReturn(ObjectConverter.toJson(workflowTaskHandlerResponse));
        Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);

        assertDoesNotThrow(() -> appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(httpStatusCodeException));
    }

    @Test(expected = WorkflowRetriableException.class)
    public void test_checkAndThrowRetriableException_socketTimeout() {
        ResourceAccessException resourceAccessException = Mockito.mock(ResourceAccessException.class);
        SocketTimeoutException socketTimeoutException = Mockito.mock(SocketTimeoutException.class);
        Mockito.when(resourceAccessException.getCause()).thenReturn(socketTimeoutException);
        appconnectDownstreamErrorsRetryHandler.checkAndThrowRetriableException(resourceAccessException);
    }

    @Test
    public void test_getRetryHandlerName(){
        Assert.assertEquals(RetryHandlerName.APPCONNECT_DOWNSTREAM_ERRORS, appconnectDownstreamErrorsRetryHandler.getName());
    }

}
