package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

public class DefaultRetryHandlerTest {

  @Mock private RetryConfig retryConfig;

  @InjectMocks private StatusCodeRetryHandlerImpl defaultRetryHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testCheckAndThrowRetriableException() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(500);

    List<Integer> statusCodes = new ArrayList<Integer>();
    statusCodes.add(500);
    Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);

    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getStackTrace())
        .thenReturn(
            new StackTraceElement[] {new StackTraceElement("class", "add", "Claims.java", 123)});

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    defaultRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void testCheckAndThrowRetriableExceptionNoException() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(501);

    List<Integer> statusCodes = new ArrayList<Integer>();
    statusCodes.add(500);
    Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);

    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getStackTrace())
        .thenReturn(
            new StackTraceElement[] {new StackTraceElement("class", "add", "Claims.java", 123)});

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    defaultRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void testCheckAndThrowRetriableExceptionNPE() {
    NullPointerException npe = Mockito.mock(NullPointerException.class);
    defaultRetryHandler.checkAndThrowRetriableException(npe);
  }
}
