package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkerRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.ExponentialModBackOffRandomRetryImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class ExponentialModBackOffRandomRetryImplTest {

    @Mock
    private WorkerRetryHelper workerRetryHelper;

    @Mock
    private WorkerRetryConfig workerRetryConfig;

    @InjectMocks
    private ExponentialModBackOffRandomRetryImpl retryStrategy;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(workerRetryHelper.getMaximumBackOff()).thenReturn(14400000L);
    }

    @Test
    public void testGetName() {
        assertEquals(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF_RANDOM, retryStrategy.getName());
    }

    @Test
    public void testComputeRetryTimer_DefaultBackoffFactor() {
        when(workerRetryConfig.getExponentialBackoffMultiplier()).thenReturn(0);
        long retryTimer = retryStrategy.computeRetryTimer(3, 1, 10L);
        assertEquals(true, retryTimer >= 40 && retryTimer < 80);
    }

    @Test
    public void testComputeRetryTimer_CustomBackoffFactor() {
        when(workerRetryConfig.getExponentialBackoffMultiplier()).thenReturn(3);
        long retryTimer = retryStrategy.computeRetryTimer(3, 1, 10L);
        assertEquals(true, retryTimer >= 30 && retryTimer < 180);
    }

    @Test
    public void testComputeRetryTimer_MinMultiplier() {
        when(workerRetryConfig.getExponentialBackoffMultiplier()).thenReturn(3);
        when(workerRetryConfig.getDefaultRandomStepSize()).thenReturn(1);
        long retryTimer = retryStrategy.computeRetryTimer(3, 2, 10L);
        assertEquals(true, retryTimer >= 30 && retryTimer < 90);
    }

    @Test
    public void testComputeRetryTimer_MaxMultiplier() {
        when(workerRetryConfig.getExponentialBackoffMultiplier()).thenReturn(2);
        when(workerRetryConfig.getDefaultRandomStepSize()).thenReturn(2);
        long retryTimer = retryStrategy.computeRetryTimer(3, 0, 10L);
        assertEquals(true, retryTimer >= 80 && retryTimer < 160);
    }


}