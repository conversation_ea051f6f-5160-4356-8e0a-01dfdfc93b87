package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

public class NoOpRetryHandlerTest {
  
  @Mock private RetryConfig retryConfig;

  @InjectMocks private NoOpRetryHandlerImpl retryHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }
  
  @Test
  public void testCheckAndThrowRetriableException() {
    HttpStatusCodeException  httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
 }

}