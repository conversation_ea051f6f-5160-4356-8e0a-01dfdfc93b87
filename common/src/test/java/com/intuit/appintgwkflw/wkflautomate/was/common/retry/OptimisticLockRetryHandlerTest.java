package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

public class OptimisticLockRetryHandlerTest {

  @Mock private RetryConfig retryConfig;

  @InjectMocks private OptimisticLockRetryHandlerImpl retryHandler;
  private final static int STATUS_CODE = 400;
  private final static String MESSAGE = "blah";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);

    List<Integer> statusCodes = new ArrayList<Integer>();
    statusCodes.add(502);
    Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testOptimisticLockingException() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(500);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getMessage())
        .thenReturn("blah" + WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION.toUpperCase() + "blah");

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }
  
  @Test(expected = WorkflowRetriableException.class)
  public void testOptimisticLockingException_typeRestException() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(500);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getMessage())
        .thenReturn("\"type\":\"RestException\", \"message\": \"" + WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION_CODE.toUpperCase() + " blah\" }");

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void testNonOptimisticLockingException() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(500);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getMessage()).thenReturn("blah");

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void testWrongStatusCode() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(STATUS_CODE);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    Mockito.when(httpStatusCodeException.getMessage()).thenReturn(MESSAGE);

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void testWrongStatusCodeWithWrongType() {
    Exception httpStatusCodeException = Mockito.mock(Exception.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(STATUS_CODE);

    Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
    retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }
}
