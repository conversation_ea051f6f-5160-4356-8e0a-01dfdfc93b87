package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CamundaProcessEngineRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;

public class PEECodeRetryHandlerTest {

	@Mock
	private RetryConfig retryConfig;

	@InjectMocks
	private PEECodeRetryHandlerImpl retryHandler;
	private final static int STATUS_CODE = 400;
	private final static String MESSAGE = "blah";

	@Before
	public void init() {
		MockitoAnnotations.initMocks(this);

		List<Integer> statusCodes = new ArrayList<Integer>();
		statusCodes.add(502);
		Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);
	}

	@Test(expected = WorkflowRetriableException.class)
	public void testOptimisticLockingException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage())
				.thenReturn("blah" + WorkflowConstants.OPTIMISTIC_LOCKING_EXCEPTION.toUpperCase() + "blah");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
	}

	@Test
	public void testProcessEngineNoExceptionDetailsException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(null);

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test(expected = CamundaProcessEngineRetriableException.class)
	public void testProcessEngineFKException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(
				"{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":10001}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
	}

	@Test(expected = CamundaProcessEngineRetriableException.class)
	public void testProcessEnginePLException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(
				"500 Internal Server Error: {\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":0}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
	}

	@Test(expected = CamundaProcessEngineRetriableException.class)
	public void testProcessEngineDeadlockException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(
				"{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":10000}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
	}
	
	@Test(expected = CamundaProcessEngineRetriableException.class)
	public void testProcessEngineCustomCodeException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(
				"{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":11111}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
	}
	
	@Test
	public void testProcessEngineNOnRetryCodeException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(
				"{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":10101}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test
	public void testProcessEngineException_typeRestException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn("{\"type\":\"RestException\", \"message\": \""
				+ WorkflowConstants.PROCESS_ENGINE_EXCEPTION.toUpperCase() + " blah\" }");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test
	public void testNonProcessEngineException() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn("{\"type\":\"NUllException\"}");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test
	public void testNonProcessEngineException_UnknownProperty() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(500);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage())
				.thenReturn("{\"type\":\"RestException\", \"message\":\"Unknown property used in expression\" }");

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test
	public void testWrongStatusCode() {
		HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(STATUS_CODE);
		Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
		Mockito.when(httpStatusCodeException.getMessage()).thenReturn(MESSAGE);

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}

	@Test
	public void testWrongStatusCodeWithWrongType() {
		Exception httpStatusCodeException = Mockito.mock(Exception.class);
		HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
		Mockito.when(httpStatus.value()).thenReturn(STATUS_CODE);

		Mockito.when(httpStatusCodeException.getSuppressed()).thenReturn(new Throwable[0]);
		try {
			retryHandler.checkAndThrowRetriableException(httpStatusCodeException);
		} catch (Exception e) {
			Assert.fail("Should not come in exception block");
		}
	}
}
