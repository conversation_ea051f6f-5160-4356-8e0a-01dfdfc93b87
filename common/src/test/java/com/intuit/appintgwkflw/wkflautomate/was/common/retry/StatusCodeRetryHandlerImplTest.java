package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.NoHttpResponseException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;

public class StatusCodeRetryHandlerImplTest {

  @InjectMocks private StatusCodeRetryHandlerImpl statusCodeRetryHandler;
  @Mock private RetryConfig retryConfig;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    List<Integer> statusCodes = new ArrayList<Integer>();
    statusCodes.add(500);
    Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_checkAndThrowRetriableException_noHttpResponse() {
    NoHttpResponseException noHttpResponseException = Mockito.mock(NoHttpResponseException.class);
    statusCodeRetryHandler.checkAndThrowRetriableException(noHttpResponseException);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_checkAndThrowRetriableException_noHttpResponse_withCause() {
    Throwable throwable = new RestClientException("server error").initCause(new NoHttpResponseException("no http"));
    statusCodeRetryHandler.checkAndThrowRetriableException((Exception) throwable);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_checkAndThrowRetriableException_httpStatusCode() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(500);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    statusCodeRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
  }

  @Test
  public void test_checkAndThrowRetriableExceptio_httpStatusCode() {
    HttpStatusCodeException httpStatusCodeException = Mockito.mock(HttpStatusCodeException.class);
    HttpStatus httpStatus = Mockito.mock(HttpStatus.class);
    Mockito.when(httpStatus.value()).thenReturn(401);
    Mockito.when(httpStatusCodeException.getStatusCode()).thenReturn(httpStatus);
    try {
      statusCodeRetryHandler.checkAndThrowRetriableException(httpStatusCodeException);
    } catch(WorkflowRetriableException ex) {
      Assert.fail("Should not have thrown any exception");
    }
  }
}
