package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.v4.interaction.InteractionException;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class V4GraphqlStatusCodeRetryImplTest {

  @Mock
  private RetryConfig retryConfig;

  @InjectMocks
  private V4GraphqlStatusCodeRetryImpl retryHandler;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);

    List<Integer> statusCodes = new ArrayList<Integer>();
    statusCodes.add(504);
    Mockito.when(retryConfig.getStatusCode()).thenReturn(statusCodes);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void test_InteractionException_GatewayTimeout() {
    InteractionException interactionException = new InteractionException("Remote API invocation error: Gateway Timeout");
    retryHandler.checkAndThrowRetriableException(interactionException);
  }

  @Test
  public void test_InteractionException_TooManyRequest() {
    InteractionException interactionException = new InteractionException("Remote API invocation error: Too Many Requests");
    try {
      retryHandler.checkAndThrowRetriableException(interactionException);
    }
    catch (Exception e){
      Assert.fail("Should not fail");
    }
  }

  @Test
  public void test_Non_InteractionException() {
    try {
      retryHandler.checkAndThrowRetriableException(new RuntimeException());
    }
    catch (Exception e){
      Assert.fail("Should not fail");
    }
  }

}
