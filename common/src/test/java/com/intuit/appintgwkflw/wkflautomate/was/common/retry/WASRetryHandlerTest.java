package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import org.junit.Test;
import org.mockito.Mock;
import junit.framework.Assert;


public class WASRetryHandlerTest {
  
  @Mock private StatusCodeRetryHandlerImpl retryHandlerD;
  
  @Test
  public void getHandler() {
    
    WASRetryHandler.addHandler(RetryHandlerName.STATUS_CODE, retryHandlerD);
    
    RetryHandler retryHandler = WASRetryHandler.getHandler(RetryHandlerName.STATUS_CODE);
    Assert.assertEquals(retryHandler, retryHandlerD);
    
    retryHandler = WASRetryHandler.getHandler(RetryHandlerName.NO_OP);
    Assert.assertEquals(retryHandler, retryHandlerD);
  }

}
