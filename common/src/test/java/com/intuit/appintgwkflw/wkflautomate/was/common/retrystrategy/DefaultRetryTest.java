package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class DefaultRetryTest {
    @InjectMocks private DefaultRetryImpl defaultRetry;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getNameTest() {
        Assert.assertEquals(RetryStrategyName.DEFAULT, defaultRetry.getName());
    }

    @Test
    public void computeRetryTimerTest() {
        long backOffStepSize = 15000L;
        long expectedValue = 15000L;
        Assert.assertEquals(expectedValue, defaultRetry.computeRetryTimer(4, 4, backOffStepSize));
    }
}
