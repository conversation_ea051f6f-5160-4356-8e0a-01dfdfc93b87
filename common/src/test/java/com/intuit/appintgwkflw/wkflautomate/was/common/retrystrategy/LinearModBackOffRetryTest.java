package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class LinearModBackOffRetryTest {

    @Mock
    WorkerRetryHelper workerRetryHelper;

    @InjectMocks
    private LinearModBackOffRetryImpl linearModBackOff;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(workerRetryHelper.getMaximumBackOff()).thenReturn(14400000L);
    }

    @Test
    public void getNameTest() {
        Assert.assertEquals(RetryStrategyName.LINEAR_MOD_BACKOFF, linearModBackOff.getName());
    }

    @Test
    public void computeRetryTimerWithBackOffTestBaseCase() {
        long backOffStepSize = 15000L;
        long actualValue = linearModBackOff.computeRetryTimer(4, 4, backOffStepSize);
        long expectedValue = 15000L;
        Assert.assertEquals(expectedValue, actualValue);
    }

    @Test
    public void computeRetryTimerWithBackOffTestBackOffCase() {
        long backOffStepSize = 15000L;
        long actualValue = linearModBackOff.computeRetryTimer(4, 2, backOffStepSize);
        long expectedValue = 3 * 15000L;
        Assert.assertEquals(expectedValue, actualValue);
    }

    @Test
    public void computeRetryTimerWithBackOffTestBackOffMaxMultiplier() {
        long backOffStepSize = 15000L;
        long actualValue = linearModBackOff.computeRetryTimer(15, 1, backOffStepSize);
        long expectedValue = 10 * 15000L;
        Assert.assertEquals(expectedValue, actualValue);
    }

    @Test
    public void computeRetryTimerWithBackOffTestBackOffMinMultiplier() {
        long backOffStepSize = 15000L;
        long actualValue = linearModBackOff.computeRetryTimer(0, 1, backOffStepSize);
        long expectedValue = 15000L;
        Assert.assertEquals(expectedValue, actualValue);
    }

    @Test
    public void computeRetryTimerWithBackOffTestBackOffMaxTime() {
        long backOffStepSize = 2000 * 1000L;
        long actualValue = linearModBackOff.computeRetryTimer(15, 1, backOffStepSize);
        long expectedValue = 4L * 60L * 60L * 1000L;
        Assert.assertEquals(expectedValue, actualValue);
    }
}
