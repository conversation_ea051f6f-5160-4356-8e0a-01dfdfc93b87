package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import org.junit.Test;
import org.mockito.Mock;
import junit.framework.Assert;

public class WorkerRetryStrategyTest {
    @Mock private ExponentialModBackOffRetryImpl exponentialModBackOff;
    @Mock private DefaultRetryImpl defaultRetry;

    @Test
    public void getHandler() {
        WorkerRetryStrategy.addStrategy(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF, exponentialModBackOff);
        WorkerRetryStrategy.addStrategy(RetryStrategyName.DEFAULT, defaultRetry);

        RetryStrategy retryStrategy = WorkerRetryStrategy.getStrategy(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF);
        Assert.assertEquals(exponentialModBackOff, retryStrategy);

        retryStrategy = WorkerRetryStrategy.getStrategy(RetryStrategyName.LINEAR_MOD_BACKOFF);
        Assert.assertEquals(defaultRetry, retryStrategy);
    }
}
