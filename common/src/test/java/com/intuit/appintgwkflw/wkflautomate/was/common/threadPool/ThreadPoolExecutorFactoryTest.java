package com.intuit.appintgwkflw.wkflautomate.was.common.threadPool;

import static org.hamcrest.CoreMatchers.equalToObject;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.doReturn;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ThreadPoolExecutorFactoryTest {

  @Mock
  private BlockingQueue<Runnable> blockingQueue;

  @Mock
  private ThreadPoolExecutor sharedExecutor;

  @Mock
  private ThreadPoolExecutor reservedExecutor;

  @Before
  public void setUp() {

    doReturn(1).when(blockingQueue).remainingCapacity();
    doReturn(blockingQueue).when(sharedExecutor).getQueue();
  }

  @Test
  public void whenCreateExecutor_thenSuccess() {

    final ThreadPoolExecutor threadPoolExecutor = ThreadPoolExecutorFactory
        .createExecutor("test", 1, 2, 2, 1);

    assertEquals(2, threadPoolExecutor.getCorePoolSize());
  }

  @Test
  public void whenGetTargetExecutor_forGlobalWorker_thenSuccess() {

    final ThreadPoolExecutor threadPoolExecutor = ThreadPoolExecutorFactory
        .getTargetExecutor(sharedExecutor, reservedExecutor);

    assertThat(threadPoolExecutor, equalToObject(sharedExecutor));
  }

  @Test
  public void whenGetTargetExecutor_andSharedPoolHasSpace_thenSuccess() {

    final ThreadPoolExecutor threadPoolExecutor = ThreadPoolExecutorFactory
        .getTargetExecutor(sharedExecutor, reservedExecutor);

    assertThat(threadPoolExecutor, equalToObject(sharedExecutor));
  }

  @Test
  public void whenGetTargetExecutor_andSharedPoolIsFull_thenSuccess() {

    doReturn(0).when(blockingQueue).remainingCapacity();

    final ThreadPoolExecutor threadPoolExecutor = ThreadPoolExecutorFactory
        .getTargetExecutor(sharedExecutor, reservedExecutor);

    assertThat(threadPoolExecutor, equalToObject(reservedExecutor));
  }

  @Test
  public void whenGetTargetExecutor_andSharedPoolIsFull_butReservedPoolIsNull_thenSuccess() {

    doReturn(0).when(blockingQueue).remainingCapacity();

    final ThreadPoolExecutor threadPoolExecutor = ThreadPoolExecutorFactory
        .getTargetExecutor(sharedExecutor, null);

    assertThat(threadPoolExecutor, equalToObject(sharedExecutor));
  }
}
