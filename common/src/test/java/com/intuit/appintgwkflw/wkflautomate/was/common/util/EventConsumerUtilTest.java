package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConsumer;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EventConsumerUtilTest {

  @Mock
  private EventConfiguration eventConfiguration;
  @Mock
  private EventConsumer eventConsumer;
  @InjectMocks
  private EventConsumerUtil eventConsumerUtil;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    Map<String, List<String>> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("entity1", Arrays.asList("topic1", "topic1-dlq"));
    entityTopicsMapping.put("entity2", Arrays.asList("topic2", "topic2-dlq"));
    Mockito.when(eventConfiguration.getConsumer()).thenReturn(eventConsumer);
    Mockito.when(eventConsumer.getEntityTopicsMapping()).thenReturn(entityTopicsMapping);
  }

  @Test
  public void testGetTopicToEntityTypeMapping() {
    Map<String, String> topicEntityTypeMap = eventConsumerUtil.getTopicToEntityTypeMapping();
    Assert.assertEquals("entity1", topicEntityTypeMap.get("topic1"));
    Assert.assertEquals("entity2", topicEntityTypeMap.get("topic2-dlq"));
    Assert.assertNull(topicEntityTypeMap.get("topic3"));
  }

  @Test
  public void testGetTopicList_NonDLQ() {
    List<String> topicList = eventConsumerUtil.getFilteredTopicList("entity1", false);
    Assert.assertTrue(topicList.contains("topic1"));
    Assert.assertFalse(topicList.contains("topic1-dlq"));
    Assert.assertFalse(topicList.contains("topic2"));
    Assert.assertFalse(topicList.contains("topic2-dlq"));
  }

  @Test
  public void testGetTopicList_NonDLQ2() {
    List<String> topicList = eventConsumerUtil.getFilteredTopicList("entity2", false);
    Assert.assertTrue(topicList.contains("topic2"));
    Assert.assertFalse(topicList.contains("topic2-dlq"));
    Assert.assertFalse(topicList.contains("topic1-dlq"));
    Assert.assertFalse(topicList.contains("topic1"));
  }

  @Test
  public void testGetTopicList_DLQ2() {
    List<String> topicList = eventConsumerUtil.getFilteredTopicList("entity2", true);
    Assert.assertFalse(topicList.contains("topic2"));
    Assert.assertTrue(topicList.contains("topic2-dlq"));
    Assert.assertFalse(topicList.contains("topic1-dlq"));
    Assert.assertFalse(topicList.contains("topic1"));
  }

  @Test
  public void test_getTaskIdAndWorkerId_success() {
    Pair<String, String> taskIdWorkerId = EventConsumerUtil
        .getTaskIdAndWorkerId("workerId:entityId", EventEntityType.EXTERNALTASK);
    Assert.assertEquals(taskIdWorkerId.getLeft(), "workerId");
    Assert.assertEquals(taskIdWorkerId.getRight(), "entityId");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_getTaskIdAndWorkerId_entityIdNotPresent() {
    EventConsumerUtil.getTaskIdAndWorkerId("", EventEntityType.EXTERNALTASK);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_getTaskIdAndWorkerId_entityIdInvalid() {
	  EventConsumerUtil.getTaskIdAndWorkerId("abc", EventEntityType.EXTERNALTASK);
  }

  @Test
  public void test_getTaskIdAndWorkerId_noEventEntityType() {
    Pair<String, String> taskIdWorkerId = EventConsumerUtil
        .getTaskIdAndWorkerId("workerId:entityId", null);
    Assert.assertEquals(taskIdWorkerId.getLeft(), "workerId");
    Assert.assertEquals(taskIdWorkerId.getRight(), "entityId");
  }

}
