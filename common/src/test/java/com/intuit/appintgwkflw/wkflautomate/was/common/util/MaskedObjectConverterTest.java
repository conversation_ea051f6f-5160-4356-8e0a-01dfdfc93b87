package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/** <AUTHOR> */
public class MaskedObjectConverterTest {

  @Test
  @SuppressWarnings({"unchecked", "rawtypes"})
  public void testToJsonSuccess() {
    Map map = new HashMap<>();
    map.put("1", "1");
    String resp = MaskedObjectConverter.toJson(map);
    Assert.assertNotNull(resp);
  }

  @Test
  public void testToJsonMaskSuccess() {
    Map<String, Object> map = new HashMap<>();
    map.put("test", "test");
    ExternalTaskAssigned externalTaskAssigned = ExternalTaskAssigned.builder().businessEntityId("test-id").taskName("task-name").businessEntityType("type").variables(map).build();
    String resp = MaskedObjectConverter.toJson(externalTaskAssigned);
    String expectedResponse = "{\"workflowMetadata\":null,\"taskName\":\"task-name\",\"businessEntityType\":\"type\",\"businessEntityId\":\"test-id\",\"variables\":\"*****\",\"extensions\":null,\"txnId\":null}";
    Assert.assertEquals(expectedResponse, resp);
  }

  @Test
  public void testToJsonFailure() {
    String resp = MaskedObjectConverter.toJson(null);
    Assert.assertNull(resp);
  }

  @Test
  public void testToJsonFailureException() {
    HashMap<String, String> invalidJSONPayload= new HashMap<>();
    invalidJSONPayload.put(null,"021");
    String resp = MaskedObjectConverter.toJson(invalidJSONPayload);
    Assert.assertNull(resp);
  }
}
