package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

@RunWith(MockitoJUnitRunner.class)
public class MdcTaskDecoratorTest {

    @InjectMocks
    @Autowired
    private MdcTaskDecorator mdcTaskDecorator;

    @Test
    public void testDecoratorHappyFlow() {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                // do nothing
            }
        };
        Runnable returnRunnable = mdcTaskDecorator.decorate(runnable);
        Assert.assertNotNull(returnRunnable);
    }
}
