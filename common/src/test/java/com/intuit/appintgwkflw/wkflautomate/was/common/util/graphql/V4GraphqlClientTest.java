package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.V4GraphqlStatusCodeRetryImpl;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.v4.Context;
import com.intuit.v4.Error;
import com.intuit.v4.ErrorCodeEnum;
import com.intuit.v4.ModelInterface;
import com.intuit.v4.Query;
import com.intuit.v4.RequestContext;
import com.intuit.v4.Result;
import com.intuit.v4.interaction.Interaction;
import com.intuit.v4.interaction.InteractionRequest;
import com.intuit.v4.interaction.InteractionResults;
import com.intuit.v4.providers.RemoteProvider;
import com.intuit.v4.util.RequestMetrics;
import com.intuit.v4.util.RequestMetricsInterface;
import com.intuit.v4.work.Project;
import java.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
public class V4GraphqlClientTest {

  @InjectMocks
  private V4GraphqlClient v4GraphqlClient;

  @Mock
  private OfflineTicketClient offlineTicketClient;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private HeaderPopulator headerPopulator;

  private static MockedStatic<WASRetryHandler> mockedStatic;

  @BeforeClass
  public static void beforeClass(){
    mockedStatic =  Mockito.mockStatic(WASRetryHandler.class);
  }

  @AfterClass
  public static void afterClass(){
    mockedStatic.close();
  }

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    mockedStatic.when(
        () -> WASRetryHandler.getHandler(RetryHandlerName.V4_GRAPHQL_STATUS_CODE))
        .thenReturn(new V4GraphqlStatusCodeRetryImpl());
  }

  @Test
  @SuppressWarnings({"unchecked", "serial"})
  public void testwrite() throws Exception {

    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    RequestMetricsInterface requestMetricsInterface = Mockito.mock(RequestMetricsInterface.class);
    RequestMetrics requestMetrics = Mockito.mock(RequestMetrics.class);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test_tid");
    Mockito.when(requestContext.getCustomHeaders())
        .thenReturn(
            new HashMap<String, String>() {
              {
                put("test", "test");
              }
            });
    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.getSingleResult()).thenReturn(project);
    Mockito.when(interactionResults.getContext()).thenReturn(requestContext);
    Mockito.when(requestContext.getRequestLogger()).thenReturn(requestMetricsInterface);
    Mockito.when(requestMetricsInterface.getRequestMetrics()).thenReturn(requestMetrics);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);
    
    
    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .customHeaders(Collections.singletonMap("test", "test"))
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNotNull(createdProject);
    Assert.assertEquals("Project 1", createdProject.getName());
    Assert.assertNotNull(creationResponse.getRequestMetrics());
  }

  @Test
  @SuppressWarnings({"unchecked", "serial"})
  public void testRequestContextFailureDueToOfflineTicket() throws Exception {

    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test_tid");
    Mockito.when(requestContext.getCustomHeaders())
        .thenReturn(
            new HashMap<String, String>() {
              {
                put("test", "test");
              }
            });
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenThrow(new OfflineTicketClientException("Failed"));
    
    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> response = v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .customHeaders(Collections.singletonMap("test", "test"))
                .targetRealmId("1234")
                .build());
    Assert.assertTrue(response.isError());
    Assert.assertEquals("Failed", response.getErrors().get(0).getMessage());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteInvalidAuthType() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.getSingleResult()).thenReturn(project);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> response = v4GraphqlClient.write(
        WASV4GraphqlRequest.builder()
            .url("http://url")
            .request(project)
            .authType(null)
            .build());
    Assert.assertTrue(response.isError());
    Assert.assertEquals( WorkflowError.INVALID_GRAPHQL_AUTH_TYPE.getErrorMessage(), response.getErrors().get(0).getMessage());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteUserAuthType() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.getSingleResult()).thenReturn(project);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .authType(V4AuthType.USER)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNotNull(createdProject);
    Assert.assertEquals("Project 1", createdProject.getName());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteRuntimeException() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenThrow(new RuntimeException("Unknown Error occured"));
    
    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
    Assert.assertEquals(
        ErrorCodeEnum.PLT500.value(),
        creationResponse.getErrors().stream().findFirst().get().getCode());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteIntercationResultNull() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(null);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertEquals(true, creationResponse.isError());
    //TODO
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteIntercationResultHasError() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasError()).thenReturn(true);
    List<Result> errorList = new ArrayList<>();
    Result result = new Result();
    result.addError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error"));
    errorList.add(result);
    Mockito.when(interactionResults.iterator()).thenReturn(errorList.iterator());

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }

  @Test
  @SuppressWarnings({"unchecked", "deprecation"})
  public void testwriteIntercationResultException() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasException()).thenReturn(true);
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("test_tid");
    List<Result> errorList = new ArrayList<>();
    Result result = new Result();
    result.addError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error"));

    result.setError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error2"));
    errorList.add(result);
    Mockito.when(interactionResults.iterator()).thenReturn(errorList.iterator());

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testwriteIntercationResultErrorItrNull() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.write(project)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasException()).thenReturn(true);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .request(project)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertTrue(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testRead() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestRead = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestRead);
    Mockito.when(interactionRequestRead.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.getSingleResult()).thenReturn(project);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .customHeaders(Collections.singletonMap("test", "test"))
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNotNull(createdProject);
    Assert.assertEquals("Project 1", createdProject.getName());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testReadFilters() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    Interaction<ModelInterface, InteractionResults> message = Mockito.mock(Interaction.class);
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestRead = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestRead);
    Mockito.when(interactionRequestRead.executeSync()).thenReturn(interactionResults);
    Result result = new Result();
    result.setData(Collections.singletonList(project));
    Mockito.when(interactionResults.getEntities()).thenReturn(Collections.singletonList(result));

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<List<Project>> creationResponse =
        v4GraphqlClient.readList(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .customHeaders(Collections.singletonMap("test", "test"))
                .query(query)
                .targetRealmId("1234")
                .build());

    List<Project> createdProject = creationResponse.getResponse();
    Assert.assertNotNull(createdProject);
    Assert.assertEquals("Project 1", createdProject.get(0).getName());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testReadRuntimeException() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestRead = Mockito.mock(InteractionRequest.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestRead);
    Mockito.when(interactionRequest.executeSync()).thenThrow(new RuntimeException("Unknown Error occured"));

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
    Assert.assertEquals(
        ErrorCodeEnum.PLT500.value(),
        creationResponse.getErrors().stream().findFirst().get().getCode());
  }

  @Test
  public void testReadWorkflowGeneralException() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    Query query = Mockito.mock(Query.class);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> response = v4GraphqlClient.read(
        WASV4GraphqlRequest.builder()
            .url("http://url")
            .authType(null)
            .query(query)
            .targetRealmId("1234")
            .build());
    Assert.assertTrue(response.isError());
    Assert.assertEquals( WorkflowError.INVALID_GRAPHQL_AUTH_TYPE.getErrorMessage(), response.getErrors().get(0).getMessage());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testReadInteractionResultNull() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequest.executeSync()).thenReturn(null);

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertEquals(true, creationResponse.isError());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testReadIntercationResultHasError() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasError()).thenReturn(true);
    List<Result> errorList = new ArrayList<>();
    Result result = new Result();
    result.addError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error"));
    errorList.add(result);
    Mockito.when(interactionResults.iterator()).thenReturn(errorList.iterator());

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }

  @Test
  @SuppressWarnings({"unchecked", "deprecation"})
  public void testReadIntercationResultException() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasException()).thenReturn(true);
    List<Result> errorList = new ArrayList<>();
    Result result = new Result();
    result.addError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error"));

    result.setError(
        new Error()
            .code(ErrorCodeEnum.PLT500.value())
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .message("error2"));
    errorList.add(result);
    Mockito.when(interactionResults.iterator()).thenReturn(errorList.iterator());

    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);

    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertFalse(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }

  @Test
  @SuppressWarnings("unchecked")
  public void testReadIntercationResultErrorItrNull() throws Exception {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.remote()).thenReturn(Mockito.mock(RemoteProvider.class));
    Mockito.when(requestContext.getRequestContext(Mockito.any())).thenReturn(requestContext);
    InteractionRequest interactionRequest = Mockito.mock(InteractionRequest.class);
    InteractionRequest interactionRequestWrite = Mockito.mock(InteractionRequest.class);
    InteractionResults interactionResults = Mockito.mock(InteractionResults.class);
    Query query = Mockito.mock(Query.class);

    Mockito.when(requestContext.interaction()).thenReturn(interactionRequest);
    Mockito.when(interactionRequest.read(query)).thenReturn(interactionRequestWrite);
    Mockito.when(interactionRequestWrite.executeSync()).thenReturn(interactionResults);
    Mockito.when(interactionResults.hasException()).thenReturn(true);
    Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
    urlContextMap.put("http://url", requestContext);
    ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap",urlContextMap);
    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url("http://url")
                .query(query)
                .targetRealmId("1234")
                .build());

    Project createdProject = creationResponse.getResponse();
    Assert.assertNull(createdProject);
    Assert.assertTrue(creationResponse.isError());
    Assert.assertTrue(CollectionUtils.isEmpty(creationResponse.getErrors()));
  }
  
  
  @Test
  public void test_urlContextMap() throws Exception {
	  ReflectionTestUtils.setField(v4GraphqlClient, "urlContextMap", new ConcurrentHashMap<>());
	  Context ctx1 = v4GraphqlClient.getContext("http://url");
	  Context ctx2 = v4GraphqlClient.getContext("http://url");
	  Context ctx3 = v4GraphqlClient.getContext("http://url");
	  Map<String, Context> urlContextMap = (Map) ReflectionTestUtils.getField(v4GraphqlClient, "urlContextMap");
	  Assert.assertEquals(1, urlContextMap.size());
	  Assert.assertEquals(ctx1, ctx2);
	  Assert.assertEquals(ctx1, ctx3);
  }
}
