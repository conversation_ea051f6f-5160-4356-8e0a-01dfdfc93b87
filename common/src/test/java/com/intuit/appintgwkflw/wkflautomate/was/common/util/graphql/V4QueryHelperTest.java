package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.Filter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.V4QueryRequest;
import com.intuit.v4.work.Project;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class V4QueryHelperTest {

  @InjectMocks
  private V4QueryHelper v4QueryHelper;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testQueryHelperOneFilter(){
    List<Object> createdByApp = new ArrayList<>();
    createdByApp.add("taskManager"); // hardcoded for now

    List<Filter> whereFilter = new ArrayList<>();
    whereFilter.add(Filter.generateFilterObj(
        "meta.createdByApp.id", //hardcoded for now
        createdByApp,
        V4QueryHelper.FILTER_OPERATOR_DOUBLE_EQUAL));

    V4QueryRequest v4QueryRequest = V4QueryRequest.builder()
        .type(new Project().getTypeId())
        .whereFilters(whereFilter)
        .build();
    Assert.assertNotNull(v4QueryHelper.buildV4Query(v4QueryRequest));
  }

  @Test
  public void testQueryHelperTwoFilter(){
    List<Object> createdByApp = new ArrayList<>();
    createdByApp.add("taskManager");

    List<Object> description = new ArrayList<>();
    createdByApp.add("QBO 1");

    List<Filter> whereFilter = new ArrayList<>();
    whereFilter.add(Filter.generateFilterObj(
        "meta.createdByApp.id", //hardcoded for now
        createdByApp,
        V4QueryHelper.FILTER_OPERATOR_DOUBLE_EQUAL));

    whereFilter.add(Filter.generateFilterObj(
        "description", //hardcoded for now
        description,
        V4QueryHelper.FILTER_OPERATOR_DOUBLE_EQUAL));

    V4QueryRequest v4QueryRequest = V4QueryRequest.builder()
        .type(new Project().getTypeId())
        .whereFilters(whereFilter)
        .build();
    Assert.assertNotNull(v4QueryHelper.buildV4Query(v4QueryRequest));
  }

}
