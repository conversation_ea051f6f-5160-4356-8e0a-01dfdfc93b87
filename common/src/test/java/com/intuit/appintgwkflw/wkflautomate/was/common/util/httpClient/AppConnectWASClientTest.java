package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpMethod;

public class AppConnectWASClientTest {

  @Mock WASHttpClient wasHttpClient;
  @InjectMocks private AppConnectWASClient client;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  @SuppressWarnings("rawtypes")
  public void testHttpResponse() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    
    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .build();

    Mockito.when(wasHttpClient.httpResponse(wasHttpRequest)).thenReturn(response);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp = client.httpResponse(wasHttpRequest);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }
}
