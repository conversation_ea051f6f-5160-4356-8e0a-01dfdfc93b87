package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;

public class CamundWASClientTest {

  @Mock WASHttpClient wasHttpClient;
  @InjectMocks private CamundaWASClient client;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  @SuppressWarnings("rawtypes")
  public void testHttpResponse() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    
    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .build();

    Mockito.when(wasHttpClient.httpResponse(wasHttpRequest)).thenReturn(response);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp = client.httpResponse(wasHttpRequest);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }

  @Test
  public void testPostResponse() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);

    Mockito.when(wasHttpClient.postResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(response);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
        client.postResponse(
            actionRequest.getEndpoint(),
            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
            WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }
  
  @Test
  public void testPostResponseWithoutHandler() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);

    Mockito.when(wasHttpClient.postResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(response);

    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
        client.postResponse(
            actionRequest.getEndpoint(),
            new HttpEntity<AppConnectTaskHandlerRequest>(actionRequest, null),
            WorkflowTaskHandlerResponse.class);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }

  @Test
  public void testGetResponse() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    Mockito.when(wasHttpClient.getResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(response);
    
    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
        client.getResponse(
            actionRequest.getEndpoint(),
            null,
            WorkflowTaskHandlerResponse.class,RetryHandlerName.STATUS_CODE);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }
  
  @Test
  public void testGetResponseWithoutHandler() {
    AppConnectTaskHandlerRequest actionRequest =
        AppConnectTaskHandlerRequest.builder().workflowId("TEST").build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    Mockito.when(wasHttpClient.getResponse(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(response);
    
    WASHttpResponse<WorkflowTaskHandlerResponse> resp =
        client.getResponse(
            actionRequest.getEndpoint(),
            null,
            WorkflowTaskHandlerResponse.class);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }

  @Test
  public void testOptimisticLockingRetryHttpRequest() {
    WASHttpRequest<String, String> wasHttpRequest = Mockito.mock(WASHttpRequest.class);
    client.httpResponse(wasHttpRequest);
    Mockito.verify(wasHttpRequest).setRetryHandler(RetryHandlerName.OPTIMISTIC_LOCK);
  }

  @Test
  public void testOptimisticLockingRetryPost() {
    HttpEntity<String> requestEntity = Mockito.mock(HttpEntity.class);
    client.postResponse("url", requestEntity, String.class);
    Mockito.verify(wasHttpClient)
        .postResponse("url", requestEntity, String.class, RetryHandlerName.OPTIMISTIC_LOCK);
  }
}
