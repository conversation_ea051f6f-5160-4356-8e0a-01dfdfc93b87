package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.List;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;

public class EventScheduleClientTest {

  @Mock WASHttpClient wasHttpClient;
  @InjectMocks EventScheduleClient eventScheduleClient;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testEventScheduleClientHttpClient() {

    EventSchedule eventSchedule = new EventSchedule();
    eventSchedule.setStatus(ScheduleStatus.ACTIVE);
    WASHttpRequest<List<EventSchedule>, List<EventScheduleResponse>> wasHttpRequest =
        WASHttpRequest.<List<EventSchedule>, List<EventScheduleResponse>>builder()
            .httpMethod(HttpMethod.POST)
            .request(List.of(eventSchedule))
            .responseType(new ParameterizedTypeReference<List<EventScheduleResponse>>() {})
            .url("https://paymentschedule-qal.api.intuit.com/v4/entities")
            .build();
    WASHttpResponse mockResponse = Mockito.mock(WASHttpResponse.class);
    Mockito.when(mockResponse.isSuccess2xx()).thenReturn(true);
    Mockito.when(wasHttpClient.httpResponse(wasHttpRequest)).thenReturn(mockResponse);
    WASHttpResponse<List<EventScheduleResponse>> response =
        eventScheduleClient.httpResponse(wasHttpRequest);
    Assert.assertTrue(response.isSuccess2xx());
  }
}
