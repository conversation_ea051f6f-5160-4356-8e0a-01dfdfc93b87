package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.identity.authn.offline.sdk.exceptions.UnauthorizedProductImpersonationException;
import com.intuit.v4.GlobalId;
import com.intuit.v4.globalid.GlobalIdV41;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WasUtilTest {

  @Test(expected = IllegalArgumentException.class)
  public void testUnMaskDefinitionIdFail() {
    WasUtils.unMaskDefinitionId(null);
  }

  @Test
  public void testUnMaskDefinitionId() {
    String defId = "$123";
    String val = WasUtils.unMaskDefinitionId(defId);
    Assert.assertNotNull(":123", val);
  }

  @Test
  public void testUnWrapId() {
    String originalId = "123_REALMID";
    String realmId = "REALMID";
    Assert.assertEquals("123", WasUtils.unWrapId(originalId, realmId));
    originalId = "REALMID";
    Assert.assertEquals(originalId, WasUtils.unWrapId(originalId, realmId));
  }

  @Test
  public void testGetUUID() {
    String localId = "REALMID";
    String realmId = "REALMID";
    // checking null case
    GlobalId<? extends GlobalId> gid = GlobalIdV41.create(realmId, null);
    Assert.assertEquals(null, WasUtils.getUUID(gid, realmId));
    gid = GlobalIdV41.create(realmId, localId);
    Assert.assertEquals(null, WasUtils.getUUID(gid, realmId));
    localId = "123_REALMID_UUID";
    gid = GlobalIdV41.create(realmId, localId);
    Assert.assertEquals("UUID", WasUtils.getUUID(gid, realmId));
  }

  @Test(expected = StringIndexOutOfBoundsException.class)
  public void testGetUUIDFail() {
    String localId = "123_REALMID";
    String realmId = "REALMID";
    GlobalId<? extends GlobalId> gid = GlobalIdV41.create(realmId, localId);
    WasUtils.getUUID(gid, realmId);
  }

  @Test
  public void testGetSwimlane() {
    String environment = "e2eb";
    String swimlane = WasUtils.getSwimlane(environment);
    Assert.assertEquals("b",swimlane);
  }

  @Test
  public void testGetSwimlaneForEast() {
    String environment = "e2eb-use2";
    String swimlane = WasUtils.getSwimlane(environment);
    Assert.assertEquals("b",swimlane);
  }

  @Test
  public void testGetSwimlaneForNullEnvironment() {
    String environment = null;
    String swimlane = WasUtils.getSwimlane(environment);
    Assert.assertEquals("", swimlane);
  }

  @Test
  public void testIsUnauthorisedExceptionWithUnauthorizedProductImpersonationException() {
    UnauthorizedProductImpersonationException unauthorizedException = new UnauthorizedProductImpersonationException("Unauthorized");
    OfflineTicketClientException offlineTicketClientException = new OfflineTicketClientException(unauthorizedException);
    boolean result = WasUtils.isUnauthorisedException(offlineTicketClientException);
    Assert.assertTrue(result);
  }

  @Test
  public void test_isUnauthorisedException_with_OfflineTicketClientException_but_not_UnauthorizedProductImpersonationException() {
    OfflineTicketClientException offlineTicketClientException = new OfflineTicketClientException("offline tests");
    boolean result = WasUtils.isUnauthorisedException(offlineTicketClientException);
    Assert.assertFalse(result);
  }

  @Test
  public void test_isUnauthorisedException_with_null() {
    boolean result = WasUtils.isUnauthorisedException(null);
    Assert.assertFalse(result);
  }
}
