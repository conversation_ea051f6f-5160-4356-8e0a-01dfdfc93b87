<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <appender class="ch.qos.logback.core.ConsoleAppender" name="SERVER_CONSOLE">
        <withJansi>true</withJansi>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                <pattern>
                [%date{"yyyy-MM-dd'T'HH:mm:ss,SSSZ"}]-[%-5level]-[%logger{0}]-[%-4.-4line] %msg %ex{full}%n
            </pattern>
            </pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="SERVER_CONSOLE"/>
    </root>

</configuration>