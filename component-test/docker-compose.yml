version: "3"
services:
  camunda:
    container_name: camunda_db
    image: 'postgres'
    ports:
      - '5432:5432'
    environment:
      POSTGRES_PASSWORD: Intuit01
      POSTGRES_DB: camunda
      POSTGRES_USER: sas
    volumes:
      - '${WORKDIR}/database:/var/lib/postgresql'

  kafka:
    image: johnnypark/kafka-zookeeper:2.6.0
    hostname: kafka
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      ADVERTISED_HOST: 127.0.0.1
      NUM_PARTITIONS: 10