package com.intuit.appintgwkflw.wkflautomate.was.connector;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.connector.config.ConnectorConstants;
import com.intuit.appintgwkflw.wkflautomate.was.connector.config.ConnectorsConfig;
import com.intuit.appintgwkflw.wkflautomate.was.connector.config.GenericConnectorTaskConfig;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpHeaders;

/**
 * Generic connector to make config driven api calls.
 *
 * <AUTHOR>
 */
public abstract class GenericConnector {

  @Autowired
  private ConnectorsConfig connectorsConfig;

  @Autowired
  private WASContextHandler wasContextHandler;

  private static final String TEST_ENV_IDENTIFIER = "test";
  /**
   * Generate the payload for a request and execute the call
   *
   * @param env
   * @param connectorId
   * @param parameters
   * @return
   */
  public final Object execute(String env, String connectorId, Map<String, Map<String, Object>> parameters) {
    GenericConnectorTaskConfig genericConnectorTaskConfig = getConfigForConnectorId(connectorId);

    //List on urls for each environment
    Map<String, String> urlMap = genericConnectorTaskConfig.getUrl();
    String requestUrl = urlMap.get(env);

    // Checks if the URL map contains a test environment identifier and if the URL for the test environment is not blank.
    // If the current owner ID is present in the list of test companies, the request URL is set to the test environment URL.
    // Example config: src/test/resources/yaml/generic_connector_with_test_env.yaml
    // Provide testCompanies: ["testCompany1", "testCompany2"] and test url in the desired env config
    if(urlMap.containsKey(TEST_ENV_IDENTIFIER) && StringUtils.isNotBlank(urlMap.get(TEST_ENV_IDENTIFIER))){
      String currentOwnerId = wasContextHandler.get(WASContextEnums.OWNER_ID);
      if(genericConnectorTaskConfig.getTestCompanies().contains(currentOwnerId)) {
        requestUrl = urlMap.get(TEST_ENV_IDENTIFIER);
      }
    }
    String method = genericConnectorTaskConfig.getMethod();

    return executeRequest(method, genericConnectorTaskConfig.getBodyParameters(), genericConnectorTaskConfig.getQueryVariables(), parameters, requestUrl);
  }

  /**
   * Filter the config for instructions related to payload creation, request and response variables etc.
   *
   * @param connectorId
   * @return
   */
  private GenericConnectorTaskConfig getConfigForConnectorId(String connectorId) {
    List<GenericConnectorTaskConfig> genericConnectorTaskConfigs = connectorsConfig.getConfig()
        .stream().filter(config -> config.getName().equals(connectorId))
        .collect(Collectors.toList());
    if (genericConnectorTaskConfigs.size() > 1) {
      throw new RuntimeException(ConnectorConstants.MULTIPLE_CONFIGS_ERROR);
    } else if (genericConnectorTaskConfigs.isEmpty()) {
      throw new RuntimeException(ConnectorConstants.NO_CONFIG_PRESENT);
    }
    return genericConnectorTaskConfigs.get(0);
  }

  /**
   * Execute the request call to the service url
   *
   * @param method
   * @param bodyParameters
   * @param queryVariables
   * @param parameters
   * @param requestUrl
   * @return
   */
  protected abstract Object executeRequest(String method, List<Map<String, String>> bodyParameters, List<Map<String, String>> queryVariables, Map<String, Map<String, Object>> parameters, String requestUrl);

  /**
   * Create the request body for the api calls
   * This will internally call the getVariable function which get the payload and on the basis of the api call (REST/GraphQL) would further modify the payload.
   * TODO: Handle the GraphQL part
   *
   * @param bodyParameters
   * @param queryVariables
   * @param parameters
   * @return
   */
  protected HashMap<String, Object> generateRequestBody(List<Map<String, String>> bodyParameters, List<Map<String, String>> queryVariables, Map<String, Map<String, Object>> parameters) {
    return getVariables(bodyParameters, parameters);
  }

  /**
   * Generate the payload for the request.
   * Structure:-
   * variables:
   * - key: finalKey1
   *   value: processVariables[arrayKey1]
   * - key: finalKey2
   *   value: derivedVariables[arrayKey2]
   * - key: finalKey3
   *   value: valX
   * -------
   * parameters:
   * - processVariables
   *      - arrayKey1: val1
   *      - arrayKeyX: val2
   *      - arrayKeyY: val3
   * - derivedVariables
   *      - arrayKey2: val4
   *      - arrayKeyXX: val5
   *      - arrayKeyYY: val6
   * -------
   * Response:
   * finalKey1: val1
   * finalKey2: val4
   * finalKey3: valX
   *
   * @param variables
   * @param parameters
   * @return
   */
  private HashMap<String, Object> getVariables(List<Map<String, String>> variables, Map<String, Map<String, Object>> parameters) {
    HashMap<String, Object> variableValues = new HashMap<>();

    variables.forEach(val -> {
      Pair<Boolean, Object> res = checkForVariableReference(val.get(ConnectorConstants.VALUE));
      if (res.getFirst()) {
        Pair<String, String> ref = (Pair<String, String>) res.getSecond();
        if(!parameters.get(ref.getFirst()).containsKey(ref.getSecond())){
          throw new RuntimeException(ConnectorConstants.INVALID_VALUE_ERROR);
        }
        Object value = parameters.get(ref.getFirst()).get(ref.getSecond());
        if(ObjectUtils.isNotEmpty(value)) {
          variableValues.put(val.get(ConnectorConstants.KEY), value);
        }
      } else {
        String value = val.get(ConnectorConstants.VALUE);
        if(ObjectUtils.isNotEmpty(value)) {
          variableValues.put(val.get(ConnectorConstants.KEY), value);
        }
      }
    });

    return variableValues;
  }

  /**
   * Check is the value of a variable is a primitive datatype or a reference to a location
   * Examples:
   *  - processVariables[arrayKey1] -> return (true, Pair.of(processVariables, arrayKey1))
   *  - derivedVariables[arrayKey2] -> return (true, Pair.of(derivedVariables, arrayKey2))
   *  - valX -> return(false, valX)
   *
   * @param path
   * @return
   */
  Pair<Boolean, Object> checkForVariableReference(String path){
    if(path.matches(ConnectorConstants.VARIABLE_REGEX)){
      return Pair.of(true, Pair.of(path.split(ConnectorConstants.BRACKET_REGEX)[0], StringUtils.substringBetween(path, ConnectorConstants.OPEN_BRACKET, ConnectorConstants.CLOSE_BRACKET)));
    }
    return Pair.of(false, path);
  }

  /**
   * Generate the authorization header. To be implemented by the class
   *
   * @return
   */
  protected abstract HttpHeaders populateHeader();
}
