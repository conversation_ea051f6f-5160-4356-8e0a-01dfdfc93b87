package com.intuit.appintgwkflw.wkflautomate.was.connector.config;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "connectors")
public class ConnectorsConfig {
  List<GenericConnectorTaskConfig> config;
}
