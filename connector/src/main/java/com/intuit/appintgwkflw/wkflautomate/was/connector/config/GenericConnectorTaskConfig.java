package com.intuit.appintgwkflw.wkflautomate.was.connector.config;

import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class GenericConnectorTaskConfig {
  private String authenticationType; //TODO utilise it in the handling
  private String name;
  private Map<String, String> url;
  private String method;
  private List<String> testCompanies;
  private List<Map<String, String>> bodyParameters;
  private List<Map<String, String>> queryVariables;
}
