package com.intuit.appintgwkflw.wkflautomate.was.connector.constants;

import lombok.Getter;

/**
 * Enum for the ParameterVariableType used in the connector.
 *
 * <AUTHOR>
 */
@Getter
public enum ParameterVariableType {
  PROCESS_VARIABLE("processVariable"),
  DERIVED_VARIABLE("derivedVariable");

  ParameterVariableType(String parameterVariableType) {
    this.parameterVariableType = parameterVariableType;
  }
  private String parameterVariableType;
}
