package com.intuit.appintgwkflw.wkflautomate.was.connector;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.connector.config.ConnectorsConfig;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 */
public class GenericConnectorTest {

    @Mock
    private ConnectorsConfig connectorsConfig;

    @Mock
    private WASContextHandler wasContextHandler;

    @InjectMocks
    private GenericConnector testGenericConnector = new GenericConnector() {
        @Override
        protected Object executeRequest(String method, List<Map<String, String>> bodyParameters,
                                        List<Map<String, String>> queryVariables, Map<String, Map<String, Object>> parameters,
                                        String requestUrl) {
            return Map.of("passed", true);
        }

        @Override
        protected HttpHeaders populateHeader() {
            return new HttpHeaders();
        }
    };

    public static String YAML_PATH = "yaml/test_generic_connector.yaml";
    public static String PROCESS_VARIABLE = "processVariable";
    public static String DERIVED_VARIABLE = "derivedVariable";

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPost_Success() throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        ConnectorsConfig config = mapper.readValue(readResourceAsString(YAML_PATH), ConnectorsConfig.class);
        Mockito.when(connectorsConfig.getConfig()).thenReturn(config.getConfig());

        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, String> inputVariables = new HashMap<>();
        inputVariables.put("key2", "12345");
        inputVariables.put("key3", "INVOICE");

        Map<String, Object> processVariables = new HashMap<>();
        inputVariables.forEach((key, value) -> processVariables.put(key, (Object) value));
        parameters.put(PROCESS_VARIABLE, processVariables);

        Map<String, Object> derivedVariables = new HashMap<>();
        derivedVariables.put("key4", "SEQUENTIAL");
        List<String> keyDetails = Arrays.asList("123", "245");
        derivedVariables.put("key5",
                keyDetails.stream().map(x -> Map.of("keyMap", x)).collect(Collectors.toList()));

        parameters.put(DERIVED_VARIABLE, derivedVariables);

        Object res = testGenericConnector.execute("default",
                "testHandlerIdPost", parameters);
        Assertions.assertTrue(((Map) res).get("passed").equals(true));
    }

    @Test
    public void testGET_Success() throws IOException {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        ConnectorsConfig config = mapper.readValue(readResourceAsString(YAML_PATH), ConnectorsConfig.class);
        Mockito.when(connectorsConfig.getConfig()).thenReturn(config.getConfig());

        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, String> inputVariables = new HashMap<>();
        inputVariables.put("key2", "12345");

        Map<String, Object> processVariables = new HashMap<>();
        inputVariables.forEach((key, value) -> processVariables.put(key, (Object) value));
        parameters.put(PROCESS_VARIABLE, processVariables);
        parameters.put(DERIVED_VARIABLE, Map.of("key3", "val3"));

        Object res = testGenericConnector.execute("default", "testHandlerIdGet", parameters);
        Assertions.assertTrue(((Map) res).get("passed").equals(true));
    }

    @Test
    public void testRequestUrlCaptureAsTestUrl() throws IOException {
        // Create a spy for the GenericConnector class
        GenericConnector spyConnector = Mockito.spy(testGenericConnector);
        String yamlPathForTestCompany = "yaml/generic_connector_with_test_env.yaml";

        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        ConnectorsConfig config = mapper.readValue(readResourceAsString(yamlPathForTestCompany), ConnectorsConfig.class);
        Mockito.when(connectorsConfig.getConfig()).thenReturn(config.getConfig());
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("9341452686387612");
        // Define the parameters for the execute method
        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, String> inputVariables = new HashMap<>();
        inputVariables.put("key2", "12345");
        parameters.put(PROCESS_VARIABLE, new HashMap<>(inputVariables));
        parameters.put(DERIVED_VARIABLE, Map.of("key3", "val3"));

        // Call the execute method on the spy object
        spyConnector.execute("default", "testHandlerIdPost", parameters);

        // Capture the value of requestUrl using an argument captor
        ArgumentCaptor<String> requestUrlCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(spyConnector).executeRequest(Mockito.anyString(), Mockito.anyList(), Mockito.anyList(), Mockito.anyMap(), requestUrlCaptor.capture());

        // Verify the captured value
        String capturedRequestUrl = requestUrlCaptor.getValue();
        Assertions.assertNotNull(capturedRequestUrl);
        Assertions.assertEquals("http://test-localhost:8448/graphql", capturedRequestUrl); // Replace with the expected URL
    }

    @Test
    public void testRequestUrlCaptureAsMainUrl() throws IOException {
        // Create a spy for the GenericConnector class
        GenericConnector spyConnector = Mockito.spy(testGenericConnector);
        String yamlPathForTestCompany = "yaml/generic_connector_with_test_env.yaml";

        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        ConnectorsConfig config = mapper.readValue(readResourceAsString(yamlPathForTestCompany), ConnectorsConfig.class);
        Mockito.when(connectorsConfig.getConfig()).thenReturn(config.getConfig());
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("NOT_TEST_COMPANY");
        // Define the parameters for the execute method
        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, String> inputVariables = new HashMap<>();
        inputVariables.put("key2", "12345");
        parameters.put(PROCESS_VARIABLE, new HashMap<>(inputVariables));
        parameters.put(DERIVED_VARIABLE, Map.of("key3", "val3"));

        // Call the execute method on the spy object
        spyConnector.execute("default", "testHandlerIdPost", parameters);

        // Capture the value of requestUrl using an argument captor
        ArgumentCaptor<String> requestUrlCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(spyConnector).executeRequest(Mockito.anyString(), Mockito.anyList(), Mockito.anyList(), Mockito.anyMap(), requestUrlCaptor.capture());

        // Verify the captured value
        String capturedRequestUrl = requestUrlCaptor.getValue();
        Assertions.assertNotNull(capturedRequestUrl);
        Assertions.assertEquals("http://localhost:8448/graphql", capturedRequestUrl); // Replace with the expected URL
    }

    @Test
    public void testRequestUrlCaptureAsMainUrlBecauseTestUrlIsBlank() throws IOException {
        // Create a spy for the GenericConnector class
        GenericConnector spyConnector = Mockito.spy(testGenericConnector);
        String yamlPathForTestCompany = "yaml/generic_connector_with_test_env.yaml";

        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        ConnectorsConfig config = mapper.readValue(readResourceAsString(yamlPathForTestCompany), ConnectorsConfig.class);
        Mockito.when(connectorsConfig.getConfig()).thenReturn(config.getConfig());
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("NOT_TEST_COMPANY");
        // Define the parameters for the execute method
        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, String> inputVariables = new HashMap<>();
        inputVariables.put("key2", "12345");
        parameters.put(PROCESS_VARIABLE, new HashMap<>(inputVariables));
        parameters.put(DERIVED_VARIABLE, Map.of("key3", "val3"));

        // Call the execute method on the spy object
        spyConnector.execute("default", "testHandlerIdGet", parameters);

        // Capture the value of requestUrl using an argument captor
        ArgumentCaptor<String> requestUrlCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(spyConnector).executeRequest(Mockito.anyString(), Mockito.anyList(), Mockito.anyList(), Mockito.anyMap(), requestUrlCaptor.capture());

        // Verify the captured value
        String capturedRequestUrl = requestUrlCaptor.getValue();
        Assertions.assertNotNull(capturedRequestUrl);
        Assertions.assertEquals("http://localhost:8448/graphql", capturedRequestUrl); // Replace with the expected URL
    }


    @Test
    public void generateRequestBody_testResponse() {
        List<Map<String, String>> bodyParameters = new ArrayList<>();
        bodyParameters.add(Map.of("key", "finalKey1", "value", "processVariable[arrayKey1]"));
        bodyParameters.add(Map.of("key", "finalKey2", "value", "derivedVariable[arrayKey2]"));
        bodyParameters.add(Map.of("key", "finalKey3", "value", "valX"));

        Map<String, Map<String, Object>> parameters = new HashMap<>();
        parameters.put(PROCESS_VARIABLE, Map.of("arrayKey1", "val1", "arrayKeyX", "val2", "arrayKeyY", "val3"));
        parameters.put(DERIVED_VARIABLE, Map.of("arrayKey2", "val4", "arrayKeyXX", "val5", "arrayKeyYY", "val3"));
        HashMap<String, Object> res = testGenericConnector.generateRequestBody(bodyParameters, null, parameters);
        Assertions.assertEquals(res.size(), 3);
        Assertions.assertEquals(res.get("finalKey1"), "val1");
        Assertions.assertEquals(res.get("finalKey2"), "val4");
        Assertions.assertEquals(res.get("finalKey3"), "valX");
    }

    public static String readResourceAsString(String path) {
        try (InputStream stream = Resources.getResource(path).openStream()) {
            return IOUtils.toString(stream, Charsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}