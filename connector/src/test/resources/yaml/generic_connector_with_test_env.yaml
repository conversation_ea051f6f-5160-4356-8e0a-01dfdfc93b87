config:
  - authenticationType: privateAuthPlus
    name: testHandlerIdPost
    testCompanies:
      - 9341452686387612
    url:
      default: http://localhost:8448/graphql
      test: http://test-localhost:8448/graphql
    method: POST
    bodyParameters:
      - key: query
        value: 'mutation postRequest($input_0: PostRequest!){postTestRequest(requestPayload: $input_0){id}}'
      - key: variables
        value: input_0
    queryVariables:
      - key: key1
        value: val1
  - authenticationType: privateAuthPlus
    name: testHandlerIdGet
    testCompanies:
      - 9341452686387612
    url:
      default: http://localhost:8448/graphql
      test:
    method: POST
    bodyParameters:
      - key: query
        value: 'mutation postRequest($input_0: PostRequest!){postTestRequest(requestPayload: $input_0){id}}'
      - key: variables
        value: input_0
    queryVariables:
      - key: key1
        value: val1