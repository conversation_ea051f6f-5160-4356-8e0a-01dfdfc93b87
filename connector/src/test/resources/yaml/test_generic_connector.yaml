config:
  - authenticationType: privateAuthPlus
    name: testHandlerIdPost
    url:
      default: http://localhost:8448/graphql
      local: http://localhost:8448/graphql
      qal: https://approvalengine-qal.api.intuit.com/graphql
      e2e: https://approvalengine-qal.api.intuit.com/graphql
    method: POST
    bodyParameters:
      - key: query
        value: 'mutation postRequest($input_0: PostRequest!){postTestRequest(requestPayload: $input_0){id}}'
      - key: variables
        value: input_0
    queryVariables:
      - key: key1
        value: val1
      - key: key2
        value: processVariable[key2]
      - key: key3
        value: processVariable[key3]
      - key: key4
        value: derivedVariable[key4]
      - key: key5
        value: derivedVariable[key5]
  - authenticationType: privateAuthPlus
    name: testHandlerIdGet
    url:
      default: http://localhost:8448/graphql
      local: http://localhost:8448/graphql
      qal: https://approvalengine-qal.api.intuit.com/graphql
      e2e: https://approvalengine-qal.api.intuit.com/graphql
    method: GET
    bodyParameters:
      - key: query
        value: 'query getTestRequest($input_0: GetRequest!){getTestRequest(requestPayload: $input_0){id}}'
      - key: variables
        value: input_0
    queryVariables:
      - key: key1
        value: val1
      - key: key2
        value: processVariable[key2]
      - key: key3
        value: derivedVariable[key3]
  - authenticationType: privateAuthPlus
    name: testHandlerIdOptions
    url:
      default: http://localhost:8448/graphql
      local: http://localhost:8448/graphql
      qal: https://approvalengine-qal.api.intuit.com/graphql
      e2e: https://approvalengine-qal.api.intuit.com/graphql
    method: OPTIONS
    bodyParameters:
      - key: query
        value: 'query'
      - key: variables
        value: input_0
    queryVariables:
      - key: key1
        value: val1
      - key: key2
        value: processVariable[key2]
      - key: key3
        value: derivedVariable[key3]