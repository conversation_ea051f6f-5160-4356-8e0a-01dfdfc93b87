package com.intuit.appintgwkflw.wkflautomate.was.core.approval;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector.ConnectorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalGroupResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
@AllArgsConstructor
public class ApprovalRestHandler {

    private final ConnectorImpl connector;
    private final AppConfig appConfig;


    private final static String ENTITY_ID = "entityId";
    private final static String ENTITY_TYPE = "entityType";
    private final static String DERIVED_VARIABLE = "derivedVariable";
    private final static String GET_APPROVAL_GROUP_CONNECTOR_ID = "getApprovalGroup";

    public ApprovalGroupResponse fetchApprovalGroup(String entityId, String entityType) throws IOException {
        Map<String, Map<String, Object>> parameters = new HashMap<>();
        Map<String, Object> derivedVariables = new HashMap<>();
        derivedVariables.put(ENTITY_ID, entityId);
        derivedVariables.put(ENTITY_TYPE, entityType);
        parameters.put(DERIVED_VARIABLE, derivedVariables);
        Object res = connector.execute(appConfig.getEnv(), GET_APPROVAL_GROUP_CONNECTOR_ID, parameters);
        return ObjectConverter.convertObject(res, ApprovalGroupResponse.class);
    }

}
