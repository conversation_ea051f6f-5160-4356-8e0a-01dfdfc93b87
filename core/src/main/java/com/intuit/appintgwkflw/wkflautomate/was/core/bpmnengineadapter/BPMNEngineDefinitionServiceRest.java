package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDefinitionKeyRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import lombok.NonNull;

public interface BPMNEngineDefinitionServiceRest {

  /**
   * @param deployDefinition: Contains the file that needs to be deployed, auth headers and reponse
   *     type
   * @return Response: Deploys the BPMN and DMNs, returns the response
   */
  <T> WASHttpResponse<T> deployDefinition(DeployDefinition deployDefinition);

  /**
   * Update process Definition Status by given status
   *
   * <AUTHOR> Gupta
   * @param definitionId definitionId
   * @param status Status to be set
   */
  void updateProcessDefinitionStatus(@NonNull final String definitionId, final boolean status);

  /**
   * Delete the definition from Camunda
   *
   * <AUTHOR> Gupta
   * @param  deleteDeploymentRequest {@link DeleteDeploymentRequest}
   */

  void deleteDefinition(
      @NonNull DeleteDeploymentRequest deleteDeploymentRequest);

  /**
   * Delete the deployment from Camunda
   * @param deleteDeploymentRequest {@link DeleteDeploymentRequest}
   */
  void deleteDeployment(@NonNull final DeleteDeploymentRequest deleteDeploymentRequest);

  /**
   * @return {@link BpmnResponse}
   * @param definitionId : Definition Id for which BPMN XML has to be requested
   */
  WASHttpResponse<BpmnResponse> getBPMNXMLDefinition(String definitionId);

  /**
   * @param definitionId : Definition Id for which DMN XML has to be requested
   * @return {@link DmnResponse}
   */
  WASHttpResponse<DmnResponse> getDMNXMLDefinition(String definitionId);

  /**
   *
   * @param deleteDefinitionKeyRequest : Deletes the definitions by key
   */
  void deleteDefinitionByKey(
          @NonNull DeleteDefinitionKeyRequest deleteDefinitionKeyRequest);

  /**
   *
   * @param definitionId Fetch Deployment details from the definitionId
   * @return
   */
  WASHttpResponse<DeploymentResponse> getDeploymentDetails(String definitionId);
}