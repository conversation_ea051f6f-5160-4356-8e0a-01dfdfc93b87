package com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import liquibase.pro.packaged.S;
import lombok.AllArgsConstructor;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.CACHE;

@Component
@AllArgsConstructor
@ConditionalOnExpression("${cache.enabled:false}")
public class RedissonClientOperationImpl implements CacheClientOperation {

    private RedissonClient redissonClient;

    /**
     * Retrieves the Redis hash corresponding to the cache key
     * Doesn't make an actual call to Redis. Just a mock object on which
     * Redisson functions can be called
     *
     * @param cacheKey
     * @return
     */
    @Override
    @ServiceMetric(serviceName = CACHE)
    public Map<String, String> getMap(String cacheKey) {
        // getMap() returns an RMap. Operations associated later with this
        // RMap (get(), put(), etc) interact with the cache

        return redissonClient.getMap(cacheKey);
    }

    /**
     * Clears the entire content, i.e. all key-values of the map corresponding to the cache key
     **/
    @Override
    @ServiceMetric(serviceName = CACHE)
    public void clearMap(Map<String, String> hashObject) {
        hashObject.clear();
    }

    /**
     * Removes the specific field-value from the hash object
     *
     * @param hashObject
     * @return
     */
    @Override
    @ServiceMetric(serviceName = CACHE)
    public void removeKey(Map<String, String> hashObject, String hashKey) {
        hashObject.remove(hashKey);
    }

    /**
     * Inserts the value (string) corresponding to a field of the hash object into the cache
     * E.g. if the cache schema is represented as:
     *  <realmId>:       --- The main cacheKey
     *       <actionKey>:     ---- Hash Object Key
     *              <value>       ---- Value corresponding to the hash field (Redis supports
     *                                  only a string as the nested value)
     *  Here, we are taking the parameters:
     *      a. hashObject that corresponds to the map of actionKey to value
     *      b. hashField that corresponds to the actionKey
     *      c. hashFieldValue that corresponds to the value
     *
     * @param hashObject
     * @param hashField
     * @param hashFieldValue
     */
    @Override
    @ServiceMetric(serviceName = CACHE)
    public void put(Map<String, String> hashObject,
                                            String hashField,
                                            String hashFieldValue) {
        hashObject.put(hashField, hashFieldValue);
    }

    /**
     * Inserts values corresponding to all fields of the hash object into the cache in bulk
     *
     * @param hashObject
     * @param hashFieldValue
     */
    @Override
    @ServiceMetric(serviceName = CACHE)
    public void putAll(Map<String, String> hashObject, Map<String, String> hashFieldValue) {
        hashObject.putAll(hashFieldValue);
    }


    /**
     * Set a TTL to the cache key
     *
     * @param hashObject
     * @param d
     */
    @Override
    public void setTTL(Map<String, String> hashObject, Duration d) {
        RMap<String, String> record = (RMap<String, String>) hashObject;
        record.expire(d);
    }


    /**
     *
     *
     * @param hashObject
     * @param hashField
     * @return
     */
    @Override
    public boolean containsKeyInMap(Map<String, String> hashObject,
                                            String hashField) {
        if (!hashObject.isEmpty() &&
                hashObject.containsKey(hashField)) {
            return true;
        }
        return false;

    }

    @Override
    public <T> T getValueFromMap(Map<String, String> hashObject,
                                            String hashField,
                                            Class<T> hashFieldValueType) {

        if(!containsKeyInMap(hashObject, hashField)) {
            return null;
        }

        // Deserialize value
        T hashFieldValue = ObjectConverter.fromJson(
                hashObject.get(hashField),
                hashFieldValueType
        );

        return hashFieldValue;
    }
}
