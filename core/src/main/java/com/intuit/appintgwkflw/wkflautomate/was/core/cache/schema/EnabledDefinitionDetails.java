package com.intuit.appintgwkflw.wkflautomate.was.core.cache.schema;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import lombok.Data;

import java.util.Set;

/**
 * Schema for storing details of enabled definitions in cache.
 *
 */
@Data
public class EnabledDefinitionDetails {
    private Set<String> definitionKeys;

    /**
     * Store as a JSON string in cache
     * @return
     */
    public String toString() {
        return ObjectConverter.toJson(this);
    }
}
