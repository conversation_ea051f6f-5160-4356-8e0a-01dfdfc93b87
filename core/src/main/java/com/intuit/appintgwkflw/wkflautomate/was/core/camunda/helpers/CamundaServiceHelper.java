package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_TEMP_FILE_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_TYPE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.Variable;
import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;

@UtilityClass
public class CamundaServiceHelper {

  /**
   * Creates a DeployDefinitionRequest for BPMN engine core
   *
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnModelInstanceList list of {@link DmnModelInstance}
   * @return {@link DeployDefinition}
   */
  public DeployDefinition createDeployDefinitionRequest(
      BpmnModelInstance bpmnModelInstance, List<DmnModelInstance> dmnModelInstanceList,
      String deploymentName) {
    /* Creates the bpmn temp file object */
    File bpmnFile = createBpmnFile(bpmnModelInstance);

    /* iterate through all the dmns and create the file for each */
    List<File> dmnFileList = createDmnFiles(dmnModelInstanceList);

    // TODO headers
    return new DeployDefinition(dmnFileList, bpmnFile,
    		null, DeployDefinitionResponse.class, deploymentName);
  }

  public File createBpmnFile(BpmnModelInstance bpmnModelInstance) {
    try {
      File bpmnFile =
          File.createTempFile(
              MessageFormat.format(
                  "{0}{1}", WorkflowConstants.BPMN_TEMP_FILE_KEY, UUID.randomUUID()),
              BPMN_TYPE);
      bpmnFile.deleteOnExit();
      Bpmn.writeModelToFile(bpmnFile, bpmnModelInstance);
      return bpmnFile;
    } catch (IOException e) {
      throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, e);
    }
  }

  private List<File> createDmnFiles(List<DmnModelInstance> dmnModelInstanceList) {
    return dmnModelInstanceList.stream()
        .map(
            dmnModelInstance -> {
              try {
                File dmnFile =
                    File.createTempFile(
                        MessageFormat.format(
                            "{0}{1}", DMN_TEMP_FILE_KEY, UUID.randomUUID()),
                        DMN_TYPE);
                dmnFile.deleteOnExit();
                Dmn.writeModelToFile(dmnFile, dmnModelInstance);
                return dmnFile;
              } catch (IOException e) {
                throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, e);
              }
            })
        .collect(Collectors.toList());
  }

  /**
   * Prepare request with processInstanceId and response (map) provided.
   *
   * @param processInstanceId - Process Id.
   * @param response          - Map of variables to be saved in Camunda.
   * @return
   */
  public CamundaUpdateRequest prepareProcessUpdateRequest(String processInstanceId,
      Map<String, Object> response) {
    Map<String, Variable> modificationVariableMap = new HashMap<>();
    for (Map.Entry<String, Object> entry : response.entrySet()) {
      modificationVariableMap.put(entry.getKey(), Variable.builder().value(entry.getValue()).build());
    }
    return CamundaUpdateRequest.builder().processInstanceId(processInstanceId)
        .modifications(modificationVariableMap).build();
  }
  
  /**
   * Prepare request with executionId and response (map) provided.
   *
   * @param executionId -   Execution Id.
   * @param response    -   Map of variables to be saved in Camunda.
   * @return
   */
  public CamundaUpdateRequest prepareExecutionUpdateRequest(String executionId,
      Map<String, Object> response) {
    Map<String, Variable> modificationVariableMap = new HashMap<>();
    for (Map.Entry<String, Object> entry : response.entrySet()) {
      modificationVariableMap.put(entry.getKey(), Variable.builder().value(entry.getValue()).build());
    }
    return CamundaUpdateRequest.builder().executionId(executionId)
        .modifications(modificationVariableMap).build();
  }
  
  
  /**
   * Camunda Modification payload for Execution Update being prepared using local variables came in update event.
   *  
   * @param variableMap - Variables came in event.
   * @return Map<String, Variable> typed modification.
   */
  @SuppressWarnings("unchecked")
  public Map<String, Variable> prepareModificationMap(Map<String, Object> variableMap) {
     return Optional.ofNullable(variableMap).orElse(Collections.emptyMap()).entrySet()
     	.stream().map(entry -> {
     		Map<String, Object> variable = (Map<String, Object>) entry.getValue();
	 		return  Map.entry(entry.getKey(), Variable.builder().value(variable.get(ActivityConstants.VARIABLE_MAP_VALUE))
	 			.type((String) variable.get(ActivityConstants.VARIABLE_MAP_TYPE)).build());
     	}).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

}