package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_ERROR;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowHistoryResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.GetProcessDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * This defines the REST APIs for interacting with the history endpoints of the Camunda Engine.
 */
@Component
@AllArgsConstructor
public class CamundaHistoryServiceRest implements BPMNEngineHistoryServiceRest {
	
  private static final String DESERIALIZE_VALUES = "deserializeValues";
  private static final String MAX_RESULTS = "maxResults";
  private static final String FIRST_RESULT = "firstResult";
  
  private final CamundaWASClient camundaWASClient;
  private final WorkflowCoreConfig workflowCoreConfig;
  private final CamundaRestUtil camundaRestUtil;

  private OfflineTicketClient offlineTicketClient;

  @Override
  public WASHttpResponse<WorkflowHistoryResponse> getProcessDetails(
      final GetProcessDetailsRequest getProcessDetailsRequest) {

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    final String processDetailsUrl =
        camundaRestUtil.getCamundaBaseURL()
            + workflowCoreConfig.getHistoryEndpoint()
            + workflowCoreConfig.getProcessInstanceHistoryEndpoint()
            + getProcessDetailsRequest.getProcessInstanceId();

    final HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);
    final WASHttpResponse<WorkflowHistoryResponse> response = camundaWASClient
        .getResponse(processDetailsUrl, requestEntity, WorkflowHistoryResponse.class);
    WorkflowVerfiy.verify(!response.isSuccess2xx(), PROCESS_DETAILS_ERROR, response.getError());
    return response;
  }
  
  @Override
  public WASHttpResponse<List<ProcessVariableDetailsResponse>> getProcessVariableDetails(
          ProcessVariableDetailsRequest processVariableDetailsRequest) {
      final HttpHeaders requestHeaders = new HttpHeaders();
      requestHeaders.setContentType(MediaType.APPLICATION_JSON);

      final String processDetailsUrl = camundaRestUtil.getCamundaBaseURL() + workflowCoreConfig.getHistoryEndpoint()
              + workflowCoreConfig.getProcessVariableInstanceHistoryEndpoint();

      String processDetailsUrlWithQueryParams = UriComponentsBuilder.fromHttpUrl(processDetailsUrl)
              .queryParam(FIRST_RESULT, processVariableDetailsRequest.getFirstResult())
              .queryParam(MAX_RESULTS, processVariableDetailsRequest.getMaxResults())
              .queryParam(DESERIALIZE_VALUES, processVariableDetailsRequest.isDeserializeValues()).toUriString();

      // Calling via System Offline Ticket
      requestHeaders.set(
              WorkflowConstants.AUTHORIZATION_HEADER, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
          
      WASHttpRequest<ProcessVariableDetailsRequest, List<ProcessVariableDetailsResponse>> wasHttpRequest =
              WASHttpRequest.<ProcessVariableDetailsRequest, List<ProcessVariableDetailsResponse>>builder()
                  .httpMethod(HttpMethod.POST)
                  .request(processVariableDetailsRequest)                 
                  .requestHeaders(requestHeaders)
                  .responseType(new ParameterizedTypeReference<List<ProcessVariableDetailsResponse>>() {})
                  .url(processDetailsUrlWithQueryParams)
                  .build();
      
       WASHttpResponse<List<ProcessVariableDetailsResponse>> response = camundaWASClient.httpResponse(wasHttpRequest);
          
      WorkflowVerfiy.verify(!response.isSuccess2xx(), WorkflowError.PROCESS_VARIABLE_DETAILS_ERROR,
              response.getError());
      return response;
  }


  @Override
  public WASHttpResponse<List<ExternalTaskLog>> getExternalTaskLogs(
      ExternalTaskLogRequest externalTaskLogRequest) {

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    final String externalTaskLogUrl =
        camundaRestUtil.getCamundaBaseURL() + workflowCoreConfig.getHistoryEndpoint()
            + workflowCoreConfig.getExternalTaskLogHistoryEndpoint();

    String externalTaskLogRequestUrl = UriComponentsBuilder.fromHttpUrl(externalTaskLogUrl)
        .queryParam(FIRST_RESULT, externalTaskLogRequest.getFirstResult())
        .queryParam(MAX_RESULTS, externalTaskLogRequest.getMaxResults()).toUriString();

    // Calling via System Offline Ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        offlineTicketClient.getSystemOfflineHeadersForOfflineJob());

    WASHttpRequest<ExternalTaskLogRequest, List<ExternalTaskLog>> wasHttpRequest =
        WASHttpRequest.<ExternalTaskLogRequest, List<ExternalTaskLog>>builder()
            .httpMethod(HttpMethod.POST)
            .request(externalTaskLogRequest)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<List<ExternalTaskLog>>() {
            })
            .url(externalTaskLogRequestUrl)
            .build();

    WASHttpResponse<List<ExternalTaskLog>> response = camundaWASClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy.verify(!response.isSuccess2xx(), WorkflowError.EXTERNAL_TASK_LOG_ERROR,
        response.getError());
    return response;
  }

    @Override
    public WASHttpResponse<Map<String, Integer>> getExternalTaskCount(
            ExternalTaskLogRequest externalTaskLogRequest) {

        final HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        final String externalTaskLogCountUrl =
                camundaRestUtil.getCamundaBaseURL().concat(workflowCoreConfig.getHistoryEndpoint())
                        .concat(workflowCoreConfig.getExternalTaskLogCountHistoryEndpoint());

        // Calling via System Offline Ticket
        requestHeaders.set(
                WorkflowConstants.AUTHORIZATION_HEADER,
                offlineTicketClient.getSystemOfflineHeadersForOfflineJob());

        WASHttpRequest<ExternalTaskLogRequest, Map<String, Integer>> wasHttpRequest =
                WASHttpRequest.<ExternalTaskLogRequest, Map<String, Integer>>builder()
                        .httpMethod(HttpMethod.POST)
                        .request(externalTaskLogRequest)
                        .requestHeaders(requestHeaders)
                        .responseType(new ParameterizedTypeReference<Map<String, Integer>>() {})
                        .url(externalTaskLogCountUrl)
                        .build();

        WASHttpResponse<Map<String, Integer>> response = camundaWASClient.httpResponse(wasHttpRequest);

        WorkflowVerfiy.verify(!response.isSuccess2xx(), WorkflowError.EXTERNAL_TASK_LOG_ERROR,
                response.getError());

        return response;
    }
}
