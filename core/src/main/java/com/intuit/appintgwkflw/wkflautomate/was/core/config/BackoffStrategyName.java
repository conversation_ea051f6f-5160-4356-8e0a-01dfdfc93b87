package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.camunda.bpm.client.backoff.BackoffStrategy;
import org.camunda.bpm.client.backoff.ExponentialBackoffStrategy;

import java.util.Optional;

/** Deciding the backoff strategy for ExternalTask worker. */
public enum BackoffStrategyName {

  /**
   * EXPONENTIAL - It will calculate backoff in same way for both errors and empty responses
   * ERROR_EXPONENTIAL - It will calculate backoff differently for error and empty responses. Error
   * backoff happens on specific http status codes, empty response backoff happens in same way as it
   * happens in case of EXPONENTIAL strategy
   */
  EXPONENTIAL,
  ERROR_EXPONENTIAL;

  public BackoffStrategy getBackoffStrategy(Worker worker,ImmutablePair<Client, Optional<Client>> client) {

    switch (this) {
      case ERROR_EXPONENTIAL:
        return new WASExternalTaskWorkerBackoffStrategy(worker, client);
      case EXPONENTIAL:
      default:
        Long backOffInitTime =
            client
                .getRight()
                .map(Client::getBackOffInitTime)
                .orElse(client.getLeft().getBackOffInitTime());
        Long backOffFactor =
            client
                .getRight()
                .map(Client::getBackOffFactor)
                .orElse(client.getLeft().getBackOffFactor());
        Long backOffMaxTime =
            client
                .getRight()
                .map(Client::getBackOffMaxTime)
                .orElse(client.getLeft().getBackOffMaxTime());
        ExponentialBackoffStrategy exponentialBackoffStrategy =
            new ExponentialBackoffStrategy(backOffInitTime, backOffFactor, backOffMaxTime);
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .className("BackoffStrategyName")
                    .message(
                        "BackoffStrategy selected=%s, topicName=%s, BackOffInitTime=%s, BackOffFactor=%s, BackOffMaxTime=%s",
                        EXPONENTIAL,
                        worker.getTopicName(),
                        backOffInitTime,
                        backOffFactor,
                        backOffMaxTime)
                    .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                    .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK));
        return exponentialBackoffStrategy;
    }
  }
}
