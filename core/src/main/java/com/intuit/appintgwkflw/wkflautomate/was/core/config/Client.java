package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Client {

  private String endpoint;

  private Boolean backOffDisable = Boolean.FALSE; //Default it is false in the root config

  private Long backOffInitTime;

  private Long backOffFactor;

  private Long backOffMaxTime;

  private Long errorBackOffInitTime;

  private Float errorBackOffFactor;

  private Long errorBackOffMaxTime;

  private Integer retryCount;

  private Long retryTimer;

  private BackoffStrategyName backoffStrategyName = BackoffStrategyName.EXPONENTIAL;

  private int slidingWindow;

  private List<Integer> backOffStatusCodes;
}
