package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "clients-access")
public class ClientAccessConfig {
    private TemplateSaveUpdateConfig templateSaveUpdateConfig = new TemplateSaveUpdateConfig();
}
