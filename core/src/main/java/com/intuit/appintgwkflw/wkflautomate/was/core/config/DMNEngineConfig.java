package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.feelprovider.CustomListFunctionProvider;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DaysTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.ListTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.List;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.camunda.bpm.dmn.engine.DmnEngineConfiguration;
import org.camunda.bpm.dmn.engine.impl.DefaultDmnEngineConfiguration;
import org.camunda.bpm.dmn.engine.impl.type.StringDataTypeTransformer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> stripathy1, sbaliarsing
 * DMN Bean in Service to evaluate rules
 */

@Configuration
public class DMNEngineConfig {

  @Value("${camunda.bpm.dmnFeelEnableLegacyBehavior:#{false}}")
  private boolean isJuelEnabled;

  @Value("${camunda.bpm.dmnEnableCustomTransformer:#{false}}")
  private boolean enableCustomTransformer;

  /**
   *     The existing dmn engine will be used only for JUEL expression evaluation
   *     Creates an instance of {@link DmnEngine} supporting JUEL expression
   *     <B>enableFeelLegacyBehavior is set to true</B>.
   * @return {@link DmnEngine}
   */
  @Bean(WorkflowConstants.JUEL_DMN_ENGINE)
  public DmnEngine getJuelDmnEngine() {
    DefaultDmnEngineConfiguration dmnEngineConfiguration =
        (DefaultDmnEngineConfiguration)
            DmnEngineConfiguration.createDefaultDmnEngineConfiguration();
    dmnEngineConfiguration.setEnableFeelLegacyBehavior(isJuelEnabled);
    // Not setting JUEL as DMN engine will automatically pick expressions
    /*
    dmnEngineConfiguration.setDefaultInputEntryExpressionLanguage(
        WorkflowConstants.JUEL_EXPRESSION_LANGUAGE);
    */
    return dmnEngineConfiguration.buildEngine();
  }

  /**
   *     A new DMN Engine is created for FEEL expressions evaluation
   *     Creates an instance of {@link DmnEngine} supporting FEEL expression
   *     <B>enableFeelLegacyBehavior is set to false</B>
   * @return {@link DmnEngine}
   */
  @Bean(WorkflowConstants.FEEL_DMN_ENGINE)
  public DmnEngine getFeelDmnEngine() {
    DefaultDmnEngineConfiguration dmnEngineConfiguration =
        (DefaultDmnEngineConfiguration)
            DmnEngineConfiguration.createDefaultDmnEngineConfiguration();
    if(enableCustomTransformer) {
      //Though we are adding only for Feel engine, the {@Link @DefaultDataTypeTransformerRegistry#} collection is static field so
      //it will impact all.
      dmnEngineConfiguration.getTransformer().getDataTypeTransformerRegistry()
          .addTransformer("days", new DaysTransformer());
      dmnEngineConfiguration.getTransformer().getDataTypeTransformerRegistry()
          .addTransformer("list", new ListTransformer());
    }
    dmnEngineConfiguration.setEnableFeelLegacyBehavior(!isJuelEnabled);
    dmnEngineConfiguration.setFeelCustomFunctionProviders(
        List.of(new CustomListFunctionProvider()));
    return dmnEngineConfiguration.buildEngine();
  }
}
