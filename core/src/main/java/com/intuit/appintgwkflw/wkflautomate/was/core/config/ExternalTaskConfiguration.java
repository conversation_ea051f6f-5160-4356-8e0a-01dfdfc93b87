package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "external-task")
public class ExternalTaskConfiguration {

  private Client client;

  private ThreadPool threadPool;

  private Map<String, Worker> workers;
}
