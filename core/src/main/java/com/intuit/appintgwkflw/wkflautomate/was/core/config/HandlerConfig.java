package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * This class is responsible for creating map of appconnect handlers
 * which was hardcoded in bpmn i.e close task
 * When to use this : when you need to pass some extra parameters while calling app-connect to perfrom certain action
 * you can use this config
 *
 * appconnect-handlers:
 *   handlers:
 *     '[intuit-workflows/taskmanager-update-task]':
 *       project:
 *         parameters:
 *           - name: ProjectType
 *             fieldType: string
 *             fieldValues:
 *               - 'QB_PROJECT_FINANCIAL_REMINDER'
 *           - name: TaskType
 *             fieldType: string
 *             fieldValues:
 *               - 'PROJECT'
 *           - name: Id
 *             handlerFieldName: TxnId
 *             fieldType: string
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "appconnect-handlers")
public class HandlerConfig {

    Map<String, Map<String, Map<String, List<Parameter>>>> handlers;
}
