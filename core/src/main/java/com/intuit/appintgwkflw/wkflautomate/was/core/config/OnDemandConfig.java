package com.intuit.appintgwkflw.wkflautomate.was.core.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "on-demand")
public class OnDemandConfig {
    private Map<String, Map<String, ActionOnDemandValueConfig>> actionConfig;
}
