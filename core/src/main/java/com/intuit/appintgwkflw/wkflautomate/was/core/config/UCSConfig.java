package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @apiNote Configuration Class to do the following: <br>
 *     </> 1. enabled: Flag to determine whether to call API to verify Access <br>
 *     </> 2. url: UCS API URI
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ucs")
public class UCSConfig {
  private boolean enabled = Boolean.FALSE;
  private String url;
  private String verifyAccess;
  private String delete;
  private String deleteAll;
}
