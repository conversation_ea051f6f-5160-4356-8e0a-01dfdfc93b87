package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Worker {

  private boolean disable;

  private String topicName;

  private long lockDuration;

  private int maxTasks;

  private long asyncResponseTimeout;

  private long extendedLockDuration;

  private String workerId;

  private String offeringId;

  // Adding threadPool configuration per-worker basis
  private ThreadPool threadPool;
  // Adding client metadata per-worker basis
  private Client client;
}
