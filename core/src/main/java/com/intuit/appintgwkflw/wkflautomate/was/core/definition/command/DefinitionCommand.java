package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;

/** <AUTHOR> */
public interface DefinitionCommand {

  /** @return return name for initialization */
  String getName();
  
  /**
   * @param definitionInstance input definition details
   * @param realmId input realm id of company for which command is to be initiated
   */
  void execute(DefinitionInstance definitionInstance, String realmId);
}
