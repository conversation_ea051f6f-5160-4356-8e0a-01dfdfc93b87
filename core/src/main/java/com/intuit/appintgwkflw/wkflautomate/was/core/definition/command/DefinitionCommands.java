package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import java.util.HashMap;
import java.util.Map;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * The type Definition commands.
 *
 * <AUTHOR>  <p>Class acts as factory to return commands for definitions
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DefinitionCommands {

  private static final Map<String, DefinitionCommand> DEFINITION_COMMAND_MAP = new HashMap<>();

  /**
   * Adds a command.
   *
   * @param commandName       the command name
   * @param definitionCommand the definition command
   */
  public static void addCommand(String commandName, DefinitionCommand definitionCommand) {

    DEFINITION_COMMAND_MAP.put(commandName, definitionCommand);
  }

  /**
   * Gets a command.
   *
   * @param commandName caller commandName
   * @return DefinitionCommand for given command name
   */
  public static DefinitionCommand getCommand(String commandName) {

    return DEFINITION_COMMAND_MAP.get(commandName);
  }
}
