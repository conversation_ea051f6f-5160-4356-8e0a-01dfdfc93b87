package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;


/**
 * <AUTHOR>
 *     <p>Initiate a delete command for definition in WAS and do the cleanup.
 */
@Component
@AllArgsConstructor
public class DefinitionDeleteCommand implements DefinitionCommand {

  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private final TriggerDetailsRepository triggerDetailsRepository;

  private final FeatureManager featureManager;

  /**
   * initiate delete in WAS.
   *
   * <p>1. Delete Definition in Camunda 2. Cleanup ProcessDetails and definition details and Delete
   * Workflow in app-connect
   *
   * @param definitionInstance input definition detail
   * @param ownerId input owner id of company for which deletion is to be initiated
   */
  public void execute(final DefinitionInstance definitionInstance, final String ownerId) {
    boolean isCorrelateAsyncEnabled =
        CommandHelper.isCorrelateAsyncEnabled(featureManager, ownerId);
    final State inputRequest =
        CommandHelper.prepareStateRequestForDisabledAndDelete(
            definitionInstance, ownerId, isCorrelateAsyncEnabled);

    String messageName =
        CommandHelper.getTriggerNameFromMessageName(
            WorkflowConstants.CAMUNDA_DELETED_MESSAGE_NAME,
            triggerDetailsRepository
                .findByTemplateDetails(
                    definitionInstance.getDefinitionDetails().getTemplateDetails())
                .orElse(Collections.emptyList()));

    if (StringUtils.isBlank(messageName)) {
      // For more details on this, refer this JIRA - https://jira.intuit.com/browse/QBOES-20672
      if(TemplateCategory.CUSTOM.name().equalsIgnoreCase(
          definitionInstance.getDefinitionDetails().getTemplateDetails().getTemplateCategory()
      )) {
        messageName = CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;
      }
      else {
        logWarn("No Delete trigger details found to Correlate");
        return;
      }
    }

    State state = new RxExecutionChain(inputRequest,
        new CorrelateAllMessageTask(bpmnEngineRunTimeServiceRest,
            messageName)).execute();

    Optional.ofNullable(state.getValue(AsyncTaskConstants.CORRELATE_ALL_EXCEPTION)).ifPresent(error
        -> logErrorAndThrow((RuntimeException) error,
        "Exception occurred while performing delete for definitionId=%s",
        definitionInstance.getDefinitionDetails().getDefinitionId()));

    logInfo("Deletion command completed successfully for ownerId=%s and definitionId=%s",
        ownerId, definitionInstance.getDefinitionDetails().getDefinitionId());
  }

  @Override
  public String getName() {
    return CrudOperation.DELETE.name();
  }

  private void logInfo(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .className(this.getClass().getSimpleName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));
  }

  private void logWarn(final String message, final Object... workflowMessageArgs) {
	    WorkflowLogger.warn(
	        () ->
	            WorkflowLoggerRequest.builder()
	                .message(message, workflowMessageArgs)
	                .className(this.getClass().getSimpleName())
	                .downstreamComponentName(DownstreamComponentName.WAS)
	                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));
	  }

  private void logErrorAndThrow(
      final RuntimeException error, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .stackTrace(error)
                .className(this.getClass().getSimpleName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));
    throw error;
  }
}
