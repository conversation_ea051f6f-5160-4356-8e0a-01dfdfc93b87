package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class DefinitionDisableCommand implements DefinitionCommand {

  private final TriggerDetailsRepository triggerDetailsRepository;

  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private final EventScheduleHelper eventScheduleHelper;
  private final FeatureManager featureManager;
  private final SchedulingService schedulingService;

  /**
   * execute disabled command to app-connect for given definition and closing the in flight
   * processes if any in async.
   *
   * @param definitionInstance input definition details
   * @param ownerId input realm id
   */
  public void execute(final DefinitionInstance definitionInstance, final String ownerId) {

    boolean isCorrelateAsyncEnabled =
        CommandHelper.isCorrelateAsyncEnabled(featureManager, ownerId);
    final State inputRequestForDeploy =
        CommandHelper.prepareStateRequestForDisabledAndDelete(
            definitionInstance, ownerId, isCorrelateAsyncEnabled);

    logInfo("initiated Enable/Disable in async for camunda");

    String messageName =
        CommandHelper.getTriggerNameFromMessageName(
            WorkflowConstants.CAMUNDA_DISABLE_MESSAGE_NAME,
            getTriggerDetailsList(definitionInstance.getDefinitionDetails()));

    if (StringUtils.isBlank(messageName)) {
      if(TemplateCategory.CUSTOM.name().equalsIgnoreCase(
          definitionInstance.getDefinitionDetails().getTemplateDetails().getTemplateCategory()
      )) {
        messageName = CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;
      }
      else {
        logWarn("No Disable trigger details found to Correlate");
        return;
      }
    }

    RxExecutionChain rxExecutionChain =
        new RxExecutionChain(
            inputRequestForDeploy,
            new CorrelateAllMessageTask(bpmnEngineRunTimeServiceRest, messageName));
    SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionInstance.getDefinitionDetails(), Status.INACTIVE);
    EventScheduleMetaData eventScheduleMetaData = EventScheduleServiceUtil.getScheduleMetaData(
            definitionInstance.getDefinitionDetails(), ScheduleStatus.INACTIVE);
    List<Task> updateEventScheduleTasks = eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(inputRequestForDeploy, definitionInstance, eventScheduleMetaData, schedulingMetaData);
    updateEventScheduleTasks.forEach(task ->
            Optional.ofNullable(task).ifPresent(rxExecutionChain::next)
    );
    State state = rxExecutionChain.execute();

    Optional.ofNullable(state.getValue(AsyncTaskConstants.CORRELATE_ALL_EXCEPTION)).ifPresent(error
        -> logErrorAndThrow((RuntimeException) error,
        "Exception occurred while performing disable for definitionId=%s",
        definitionInstance.getDefinitionDetails().getDefinitionId()));

    logInfo("Signalled Camunda processes for disabled successfully");
  }

  /**
   * @param message : message
   * @param workflowMessageArgs : workflowMessageArgs varArgs
   */
  private void logInfo(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
  }

  /**
   * @param error : Error of type Throwable
   * @param message : Error message
   * @param workflowMessageArgs : workflowMessageArgs varArgs
   */
  private void logErrorAndThrow(final RuntimeException error, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .className(this.getClass().getSimpleName())
                .stackTrace(error)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
    throw error;
  }
  /**
   * @param message : message
   * @param workflowMessageArgs : workflowMessageArgs varArgs
   */
  private void logWarn(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
  }

  @Override
  public String getName() {
    return CrudOperation.DISABLED.name();
  }

  private List<TriggerDetails> getTriggerDetailsList(DefinitionDetails definitionDetails) {
    return triggerDetailsRepository
        .findByTemplateDetails(
            definitionDetails.getTemplateDetails())
        .orElse(Collections.emptyList());
  }
}
