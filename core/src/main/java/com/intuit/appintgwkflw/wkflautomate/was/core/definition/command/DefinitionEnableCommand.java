package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.WORKFLOW_ID_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.SchedulingSvcException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.ActivateDeActivateAppConnectWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.payments.schedule.ScheduleStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class DefinitionEnableCommand implements DefinitionCommand {

  private AppConnectService appConnectService;

  private AuthDetailsService authDetailsService;

  private EventScheduleHelper eventScheduleHelper;

  private SchedulingService schedulingService;

  private WASContextHandler wasContextHandler;

  /**
   * execute enabled command to app-connect and ESS for given definition
   *
   * @param definitionInstance input definition details
   * @param ownerId input owner id
   */
  public void execute(DefinitionInstance definitionInstance, String ownerId) {

    State inputRequestForDeploy =
        CommandHelper.prepareStateRequestForEnabled(definitionInstance, ownerId);


    logInfo("initiated Enable in sync for appconnect");

    try {
      RxExecutionChain rxExecutionChain =
          new RxExecutionChain(inputRequestForDeploy)
              .next(
                  new ActivateDeActivateAppConnectWorkflowTask(
                      authDetailsService, appConnectService));

      SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionInstance.getDefinitionDetails(), Status.ACTIVE);
      EventScheduleMetaData eventScheduleMetaData = EventScheduleServiceUtil.getScheduleMetaData(definitionInstance.getDefinitionDetails(), ScheduleStatus.ACTIVE);
      List<Task> updateEventScheduleTasks = eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(inputRequestForDeploy, definitionInstance, eventScheduleMetaData, schedulingMetaData);

      updateEventScheduleTasks.forEach(task ->
              Optional.ofNullable(task).ifPresent(rxExecutionChain::next)
      );
      //When xml is not stored in app connect(like multi condition case), workflow id will be null
      if(Objects.nonNull(inputRequestForDeploy.getValue(WORKFLOW_ID_KEY))) {
        // here execute the tasks
        rxExecutionChain.execute();
      }
      logInfo(
          "enable command completed successfully for definitionId=%s", 
          definitionInstance.getDefinitionDetails().getDefinitionId());
    } catch (SchedulingSvcException e){
      logError(
              e,
              "exception occurred while updating the status in scheduling service definitionId=%s",
              definitionInstance.getDefinitionDetails().getDefinitionId()
      );
      throw e;
    }
    catch (Exception e) {
      logError(
          e,
          "Exception occurred while performing enable for definitionId=%s",
          definitionInstance.getDefinitionDetails().getDefinitionId());
      throw new WorkflowGeneralException(
          WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED,
          e,
          definitionInstance.getDefinition().getId().getLocalId());
    }
  }

  /**
   * @param message
   * @param workflowMessageArgs
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_ENABLE_DEFINITION));
  }

  /**
   * @param error
   * @param message
   * @param workflowMessageArgs
   */
  private void logError(Throwable error, String message, Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .stackTrace(error)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_ENABLE_DEFINITION));
  }

  @Override
  public String getName() {
    return CrudOperation.ENABLED.name();
  }

}
