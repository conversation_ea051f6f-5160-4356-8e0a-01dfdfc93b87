package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.DynamicBpmnWorkflowStepProcessor;
import com.intuit.v4.workflows.StepTypeEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * This factory is used to provide the java objects of type DynamicBpmnWorkflowStepProcessor class
 * for a given workflow step in create definition payload.
 *
 * <AUTHOR>
 */
@Component
public class DynamicBpmnWorkflowStepProcessorFactory {

  private static Map<String, DynamicBpmnWorkflowStepProcessor> workflowStepProcessorMap;

  /**
   * This constructor is used to initialize the map of workflow step processors.
   *
   * @param dynamicBpmnWorkflowStepProcessors List of DynamicBpmnWorkflowStepProcessor
   */
  @Autowired
  public DynamicBpmnWorkflowStepProcessorFactory(
      List<DynamicBpmnWorkflowStepProcessor> dynamicBpmnWorkflowStepProcessors) {
    if (Objects.isNull(workflowStepProcessorMap)) {
      workflowStepProcessorMap = new HashMap<>();
    }
    dynamicBpmnWorkflowStepProcessors.forEach(
        dynamicBpmnWorkflowStepProcessor ->
            workflowStepProcessorMap.put(
                dynamicBpmnWorkflowStepProcessor.getStepType().toString(),
                dynamicBpmnWorkflowStepProcessor));
  }

  /**
   * This method returns the java object of type DynamicBpmnWorkflowStepProcessor for a given
   * workflow step in created definition payload.
   *
   * @param stepTypeEnum StepTypeEnum
   * @return DynamicBpmnWorkflowStepProcessor
   */
  public DynamicBpmnWorkflowStepProcessor getProcessorFromWorkflowStep(StepTypeEnum stepTypeEnum) {

    DynamicBpmnWorkflowStepProcessor dynamicBpmnWorkflowStepProcessor =
        workflowStepProcessorMap.get(stepTypeEnum.toString());

    WorkflowVerfiy.verify(Objects.isNull(dynamicBpmnWorkflowStepProcessor),
        WorkflowError.INVALID_WORKFLOW_STEP);

    return dynamicBpmnWorkflowStepProcessor;
  }
}
