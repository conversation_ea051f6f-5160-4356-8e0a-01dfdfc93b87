package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadMultiStepWorkflowHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Factory that returns the type of workflow step (action/condition)
 * that is to be built.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepBuilderFactory {
    private final MultiStepDefinitionActionBuilder multiStepDefinitionActionBuilder;
    private final MultiStepDefinitionConditionBuilder multiStepDefinitionConditionBuilder;

    /**
     * This function returns the handler based on the step type which will
     * be used to parse and generate the appropriate type of workflow step
     *
     * @param targetElement bpmn flow node element - only passed during read
     * @return {@link ReadMultiStepWorkflowHandler}
     */
    public ReadMultiStepWorkflowHandler getHandler(FlowNode targetElement) {
        // bpmn will have elements of different types, we will only be parsing
        // the business rule and call activity elements to build workflowSteps
        if (BpmnProcessorUtil.isBusinessRuleTaskElement(targetElement)) {
            return multiStepDefinitionConditionBuilder;
        } else if (BpmnProcessorUtil.isCallActivityElement(targetElement)) {
            return multiStepDefinitionActionBuilder;
        } else {
            // do nothing if no matching handlers are found for an element type
            return null;
        }
    }

    /**
     * This function call the processWorkflowStep function based on the type of
     * step to be constructed (i.e action or condition) basis the current bpmn
     * flow node element being processed
     *
     * @param multiStepWorkflowEntity multiStepWorkflowEntity
     * @param targetElement           bpmn flowNode element
     */
    public void buildWorkflowStep(MultiStepWorkflowEntity multiStepWorkflowEntity, FlowNode targetElement) {
        ReadMultiStepWorkflowHandler readMultiStepWorkflowHandler = getHandler(targetElement);
        // we have handlers defined only for call activity and business rule elements, for all
        // other types of bpmn elements the handler returned will be null
        if (Objects.nonNull(readMultiStepWorkflowHandler)) {
            readMultiStepWorkflowHandler.buildWorkflowStep(multiStepWorkflowEntity);
        }
    }
}
