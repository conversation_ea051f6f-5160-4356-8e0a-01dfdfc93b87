package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiWorkflowStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiWorkflowStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.CreateMultiActionStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.CreateMultiConditionStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Factory that returns the type of workflow step that is to be processed/created.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepProcessorFactory {

  private final CreateMultiConditionStepProcessor createMultiConditionStepProcessor;
  private final CreateMultiActionStepProcessor createMultiActionStepProcessor;

  /**
   * This function returns the handler based on the step type which will be used to parse and
   * generate the appropriate type of workflow step
   *
   * @param workflowStep based on the step type of workflow step, handler will be returned
   * @return {@link MultiWorkflowStepBuilder}
   */
  public MultiWorkflowStepHandler getHandler(boolean isDmnStep, WorkflowStep workflowStep) {
    if (isDmnStep || MultiStepUtil.isConditionStep(workflowStep)) {
      return createMultiConditionStepProcessor;
    } else if (MultiStepUtil.isActionStep(workflowStep)) {
      return createMultiActionStepProcessor;
    } else {
      throw new WorkflowGeneralException(WorkflowError.INVALID_WORKFLOW_STEP);
    }
  }

  /**
   * It calls the processWorkflowStep of appropriate class(condition/action)
   * based on the type of step
   *
   * @param workflowStep
   * @param currentElementId
   * @param definitionInstance
   * @param activityIds
   * @param visitedDmnRootWorkflowStepIds
   * @return
   */
  public Map<String, String> processWorkflowStep(WorkflowStep workflowStep,
      String currentElementId,
      DefinitionInstance definitionInstance,
      List<String> activityIds,
      Set<String> visitedDmnRootWorkflowStepIds) {
    boolean isDmnStep = MultiStepUtil.isDmnStep(currentElementId, definitionInstance);
    MultiWorkflowStepHandler multiWorkflowStepHandler = getHandler(isDmnStep, workflowStep);
    return multiWorkflowStepHandler.processWorkflowStep(currentElementId, workflowStep,
        definitionInstance, activityIds, visitedDmnRootWorkflowStepIds);
  }
}