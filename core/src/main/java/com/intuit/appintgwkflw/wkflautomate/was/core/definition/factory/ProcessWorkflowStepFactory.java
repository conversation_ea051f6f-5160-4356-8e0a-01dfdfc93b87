package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepCompositeBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiWorkflowStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.StepTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

/**
 * Factory that returns the type of workflowstep to build
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ProcessWorkflowStepFactory {
    private final MultiStepConditionBuilder multiStepConditionBuilder;
    private final MultiStepActionBuilder multiStepActionBuilder;
    private final MultiStepCompositeBuilder multiStepCompositeBuilder;

    /**
     * This function returns the handler based on the step type defined in the config which will be
     * used to parse and generate the appropriate type of workflow step
     *
     * @param currentConfigStep based on which we decide how to parse the workflowStep
     * @return {@link MultiWorkflowStepBuilder}
     */


    public MultiWorkflowStepBuilder getHandler(Steps currentConfigStep) {
        String stepType = currentConfigStep.getStepType();
        boolean isDayStep = MultiStepUtil.isStepTypeComposite(currentConfigStep);
        if (isDayStep) {
            return multiStepCompositeBuilder;
        } else if (StepTypeEnum.CONDITION.name().equals(stepType.toUpperCase())) {
            return multiStepConditionBuilder;
        } else if (StepTypeEnum.ACTION.name().equals(stepType.toUpperCase())) {
            return multiStepActionBuilder;
        } else {
            throw new IllegalArgumentException(
                    MessageFormat.format("{0} not supported", stepType));
        }
    }
}
