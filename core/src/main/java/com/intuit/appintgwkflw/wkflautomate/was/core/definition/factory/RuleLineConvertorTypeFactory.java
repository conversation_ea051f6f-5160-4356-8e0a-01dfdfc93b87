package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiSplitConditionRuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepConditionRuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.RuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * This factory is returns the type of Rule line builder used to build the rule lines of the payload
 */
@Component
@AllArgsConstructor
public class RuleLineConvertorTypeFactory {

  private final MultiSplitConditionRuleLineBuilder multiSplitConditionRuleLineBuilder;
  private final MultiStepConditionRuleLineBuilder multiStepConditionRuleLineBuilder;

  /**
   * This handler returns the type of rule line builder
   *
   * @param indexToYesAndNoDmnRulesMap
   * @return
   */
  public RuleLineBuilder getHandler(
      Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap) {
    return isMultiSplit(indexToYesAndNoDmnRulesMap) ? multiSplitConditionRuleLineBuilder :
        multiStepConditionRuleLineBuilder;
  }

  /**
   * This function is used to find out whether the definition has multi split at the base/root level
   * node
   *
   * @param indexToYesAndNoDmnRulesMap
   * @return
   */
  private boolean isMultiSplit(
      Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap) {
    return indexToYesAndNoDmnRulesMap.get(WorkflowConstants.INDEX_INITIAL_VALUE)
        .get(WorkflowConstants.YES_RULE).size() > 1;
  }
}


