package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomAttributesUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringExpressionHelper;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStepCondition;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Description;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.camunda.bpm.model.dmn.instance.Text;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/** This class handles the processing of DMN rules. */
@Component
@AllArgsConstructor
public class BusinessRuleTaskHandler implements ConditionalElementHandler {
  private final WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private CustomWorkflowConfig customWorkflowConfig;

    private final FeatureFlagManager featureFlagManager;

  /**
   * This method does the processing of Dmn task Adding ruleLines in the dmn table based on
   * WorkflowStepCondition entity
   *
   * @param workflowStepCondition {@link WorkflowStepCondition}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnInstanceList list of {@link DmnModelInstance}
   * @param definitionId {@link DefinitionId}
   */
  @Override
  public void process(
      WorkflowStepCondition workflowStepCondition,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnInstanceList,
      DefinitionId definitionId,
      Definition definition,
      boolean useFeelExpr) {
    WorkflowVerfiy.verifyNullForList(
        null, workflowStepCondition, bpmnModelInstance, dmnInstanceList, definitionId);
    // find the DmnModelInstance from the list of DmnModelInstance which is linked to the workflow
    // condition
    DmnModelInstance dmnModelInstance =
        dmnInstanceList.stream()
            .filter(
                modelInstance ->
                    Objects.nonNull(
                        modelInstance.getModelElementById(
                            workflowStepCondition.getId().getLocalId())))
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException((WorkflowError.DMN_NOT_FOUND)));
    DecisionTable decisionTable = getDecisionTable(dmnModelInstance);
    if (decisionTable == null) {
      return;
    }

    // map of label to index. ruleInput need to be set in the same order of columnName
    Map<String, Integer> inputLabelToIndexMap = new HashMap<>();
    Map<String, Integer> outputLabelToIndexMap = new HashMap<>();
    Map<String, String> inputLabelToTypeMap = new HashMap<>();
    Map<String, Attribute> nametoAttributeMap;

    populateInputOutputMap(
        decisionTable, inputLabelToIndexMap, outputLabelToIndexMap, inputLabelToTypeMap);

    Collection<Rule> rules = decisionTable.getRules();
    if (!CollectionUtils.isEmpty(rules)) {
      // removing the default row
      // There will be a default row with annotation telling which column is default and needs to be
      // shown on the UI.
      rules.clear();
    }

        /** Boolean flag to check for customWorkflow Used in processRuleLine to check for */
        Boolean isCustomWorkflow = CustomWorkflowUtil.isCustomWorkflow(definition.getTemplate());

        nametoAttributeMap =
                isCustomWorkflow
                        ? customWorkflowConfig.getRecordObjForType(definition.getRecordType()).getAttributes()
                        .stream()
                        .collect(Collectors.toMap(Attribute::getName, Function.identity()))
                        : Collections.emptyMap();

    //Moving it to effectively final variable to be passed in lambda below

    // set the rules in the table from the rule lines
    WorkflowLogger.logInfo(
        "step=rules_set expression_lang_used=%s",
        useFeelExpr
            ? WorkflowConstants.FEEL_EXPRESSION_LANGUAGE
            : WorkflowConstants.JUEL_EXPRESSION_LANGUAGE);
    workflowStepCondition
        .getRuleLines()
        .forEach(
            ruleLine -> {
              // process RuleLine
              Rule rule =
                  processRuleLine(
                      ruleLine,
                      dmnModelInstance,
                      inputLabelToIndexMap,
                      outputLabelToIndexMap,
                      inputLabelToTypeMap,
                      nametoAttributeMap,
                          isCustomWorkflow,
                      useFeelExpr);
              rules.add(rule);
              if (Objects.isNull(ruleLine.getId())) {
                ruleLine.setId(
                    ConditionalElementHandler.createGlobalId(
                        definitionId.toBuilder().entityId(rule.getId()).build()));
              }
            });

    // add a default rule
    rules.add(
        processRuleLine(
            new RuleLine().rules(new ArrayList<>()).mappedActionKeys(new ArrayList<>()),
            dmnModelInstance,
            inputLabelToIndexMap,
            outputLabelToIndexMap,
            inputLabelToTypeMap,
            nametoAttributeMap,
                isCustomWorkflow,
            useFeelExpr));
  }

  /*default*/ DecisionTable getDecisionTable(DmnModelInstance dmnModelInstance) {
    // get the decisionTable element list from the dmn
    Collection<DecisionTable> decisionTableList =
        dmnModelInstance.getModelElementsByType(DecisionTable.class);
    // there would be max one decision table in one dmn
    Optional<DecisionTable> decisionTableOptional = decisionTableList.stream().findFirst();
    if (!decisionTableOptional.isPresent()) {
      WorkflowLogger.logWarn("No decision table present in the DMN. Skipping!!");
      return null;
    }
    DecisionTable decisionTable = decisionTableOptional.get();
    return decisionTable;
  }
  private void populateInputOutputMap(
      DecisionTable decisionTable,
      Map<String, Integer> inputLabelToIndexMap,
      Map<String, Integer> outputLabelToIndexMap,
      Map<String, String> inputLabelToTypeMap) {
    // map of label to index. ruleInput need to be set in the same order of columnName
    AtomicReference<Integer> inputIndex = new AtomicReference<>(0);
    decisionTable
        .getInputs()
        .forEach(
            decisionInput -> {
              inputLabelToTypeMap.put(
                  decisionInput.getLabel(), decisionInput.getInputExpression().getTypeRef());
              inputLabelToIndexMap.put(
                  decisionInput.getLabel(), inputIndex.getAndSet(inputIndex.get() + 1));
            });

    // Repeat the same for outputs
    AtomicReference<Integer> outputIndex = new AtomicReference<>(0);
    decisionTable
        .getOutputs()
        .forEach(
            decisionOutput ->
                outputLabelToIndexMap.put(
                    decisionOutput.getLabel(), outputIndex.getAndSet(outputIndex.get() + 1)));
  }

  // For each ruleLine, create a rule in DMN
  private Rule processRuleLine(RuleLine ruleLine,
      DmnModelInstance dmnModelInstance,
      Map<String, Integer> inputLabelToIndexMap,
      Map<String, Integer> outputLabelToIndexMap,
      Map<String, String> inputLabelToTypeMap,
      Map<String,Attribute> nametoAttributeMap ,
      Boolean isCustomWorkflowWithCustomFields,
      boolean useFeelExpr) {
    // output Variables
    List<String> mappedActionKeys = ruleLine.getMappedActionKeys();

    // create a dmn rule for each ruleLine
    Rule dmnRule = dmnModelInstance.newInstance(Rule.class);

    // Description {Annotations in dmn goes here}
    Description description = dmnModelInstance.newInstance(Description.class);

    // need to create all inputEntries (not just passed from schema) to maintain the order
    List<InputEntry> inputEntries =
        IntStream.range(0, inputLabelToIndexMap.size())
            .mapToObj(
                idx -> {
                  InputEntry inputEntry = dmnModelInstance.newInstance(InputEntry.class);
                  inputEntry.setText(dmnModelInstance.newInstance(Text.class));
                  inputEntry.setExpressionLanguage(
                      CustomWorkflowUtil.getInputExpressionLanguage(useFeelExpr));
                  return inputEntry;
                })
            .collect(Collectors.toList());
    // need to create all outputEntries and not just passed in schema to maintain order
    List<OutputEntry> outputEntries =
        IntStream.range(0, outputLabelToIndexMap.size())
            .mapToObj(
                idx -> {
                  OutputEntry outputEntry = dmnModelInstance.newInstance(OutputEntry.class);
                  Text text = dmnModelInstance.newInstance(Text.class);
                  text.setTextContent(String.valueOf(Boolean.FALSE));
                  outputEntry.setText(text);
                  return outputEntry;
                })
            .collect(Collectors.toList());

    ruleLine
        .getRules()
        .forEach(
            rule -> {
              // getting the parameter name
              String parameterName = rule.getParameterName();
              String parameterType = StringUtils.EMPTY;

              /**
               * get conditional expression to be set in ruleLine of decisionTable Additional
               * safeguards against the ruleLines with null paramType
               */
              if (isCustomWorkflowWithCustomFields
                  && CustomAttributesUtil.isCustomFieldRuleLine(nametoAttributeMap, rule)
                  && (Objects.nonNull(rule.getParameterType()))) {
                parameterType = rule.getParameterType().toString();
                parameterName = WorkflowConstants.CUSTOM_FIELD_PREFIX + rule.getParameterName();
              }
              String userFriendlyExpr = rule.getConditionalExpression();

              String dmnFriendlyExpr =
                  getDMNFriendlyExpression(
                      parameterName,
                      userFriendlyExpr,
                      StringUtils.isEmpty(parameterType)
                          ? inputLabelToTypeMap.get(parameterName)
                          : parameterType, useFeelExpr);

              // Check if rule implies select all (ex ALL_Customer).
              // In case of select all, no expression (text content) will be set for the input.
              // DMN rule's annotation (description) will be updated to help UI to know this info

              boolean isSelectAll = StringExpressionHelper.isSelectAllRule(parameterName, userFriendlyExpr);

              inputEntries
                  .get(inputLabelToIndexMap.get(parameterName))
                  .getText()
                  .setTextContent(isSelectAll ? null : dmnFriendlyExpr);

              // In case of string input, setting annotation for not-null condition to help UI
              // distinguish between Select All and Select None
              if (WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(
                  inputLabelToTypeMap.get(parameterName))
                  && isSelectAll) {
                if (StringUtils.isEmpty(description.getTextContent())) {
                  // Customer:SELECT_ALL
                  description.setTextContent(
                      MessageFormat.format("{0}:{1}", parameterName, WorkflowConstants.SELECT_ALL));
                } else {
                  // Customer:SELECT_ALL,Department:SELECT_ALL
                  description.setTextContent(
                      MessageFormat.format("{0},{1}:{2}",
                          description.getTextContent(),
                          parameterName, WorkflowConstants.SELECT_ALL));
                }
              }
            });
    // add input entries to dmnRule
    dmnRule.getInputEntries().addAll(inputEntries);
    // Add annotation element
    dmnRule.setDescription(description);

    // create output entry
    mappedActionKeys.forEach(
        actionKey ->
            outputEntries
                .get(outputLabelToIndexMap.get(actionKey))
                .getText()
                .setTextContent(String.valueOf(Boolean.TRUE)));
    dmnRule.getOutputEntries().addAll(outputEntries);
    return dmnRule;
  }


  /**
   * @param parameterName : Specifies the name of DMN's attribute on which condition is implied -
   *     Amount, Customer etc
   * @param expression : The condition expression passed from UI - GTE 100 OR CONTAINS 1,2 etc
   * @param type : Type of the Parameter, if it is of type String,date,number etc
   * @return
   */
  public String getDMNFriendlyExpression(String parameterName, String expression, String type,
      boolean useFeelExpr) {
    // For Default Row
    if (StringUtils.isEmpty(expression)) return null;

    // get the transformer based on the data type
    DMNDataTypeTransformer dataTypeTransformer =
        DMNDataTypeTransformers.getTransformer(DMNSupportedOperator.value(type));

    //if transformer found then transform input rules to DMN friendly rules
    if (Objects.nonNull(dataTypeTransformer)) {
      WorkflowLogger.logInfo(
          "step=dmn_transformation transformer_used=%s", dataTypeTransformer.getName());
      return dataTypeTransformer.transformToDmnFriendlyExpression(expression, parameterName, type,
          useFeelExpr);
    }
    //TODO: Throw Exception here
    WorkflowLogger.logWarn(
        "step=dmn_transformation status=dmn_transformer_not_found parameterType=%s.", type);
    return null;
  }
}