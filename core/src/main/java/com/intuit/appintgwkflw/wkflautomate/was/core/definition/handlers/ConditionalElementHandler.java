package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStepCondition;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;

import java.util.List;

public interface ConditionalElementHandler {

  /**
   * Process the WorkflowStepCondition
   * @param workflowStepCondition {@link WorkflowStepCondition}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnModelInstance list of {@link DmnModelInstance}
   * @param definitionId {@link DefinitionId}
   * @param useFeelExpr
   */
  void process(
      WorkflowStepCondition workflowStepCondition,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstance, DefinitionId definitionId, Definition definition,
      boolean useFeelExpr);

  static GlobalId createGlobalId(DefinitionId definitionId) {
    return GlobalId.builder()
        .setLocalId(definitionId.getEntityId())
        .setRealmId(definitionId.getRealmId())
        .build();
  }
}
