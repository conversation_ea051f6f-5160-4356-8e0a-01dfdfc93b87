package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.ConditionExpression;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.springframework.stereotype.Component;

/** Class handles processing for Exclusive Gateway task */
@Component
public class ExclusiveGatewayHandler implements ConditionalElementHandler {

  /**
   * This method does the processing of Exclusive gateway task. It sets the expression coming from
   * --{@link WorkflowStepCondition} in Bpmn according to ruleLines
   *
   * @param workflowStepCondition {@link WorkflowStepCondition}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnInstanceList
   * @param definitionId {@link DefinitionId}
   */
  @Override
  public void process(
      WorkflowStepCondition workflowStepCondition,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnInstanceList,
      DefinitionId definitionId,
      Definition definition,
      boolean useFeelExpr) {
    WorkflowVerfiy.verifyNullForList(
        null, workflowStepCondition, bpmnModelInstance, dmnInstanceList, definitionId);

    workflowStepCondition
        .getRuleLines()
        .forEach(
            ruleLine -> {
              // for exclusive gateway there would be a single rule and one mapped key
              Optional<String> mappedKeyOptional =
                  ruleLine.getMappedActionKeys().stream().findFirst();
              if (!mappedKeyOptional.isPresent()) {
                return;
              }
              String mappedKey = mappedKeyOptional.get();
              // get action element
              FlowNode actionElement = bpmnModelInstance.getModelElementById(mappedKey);
              // get its sequenceFlow
              Optional<SequenceFlow> sequenceFlowOptional =
                  actionElement.getIncoming().stream().findFirst();
              // there would be one rule for xor gateway
              sequenceFlowOptional.ifPresent(
                  sequenceFlow ->
                      processSequenceFlowElement(
                          sequenceFlow, bpmnModelInstance, ruleLine, definitionId));
              // In case of XOR gateway only one mapped key is possible
              ruleLine.setMappedActionKeys(
                  Collections.singletonList(
                      definitionId.toBuilder().entityId(mappedKey).build().toString()));
            });
    workflowStepCondition.set(WorkflowConstants.WORKFLOW_STEP_CONDITION_TYPE, BpmnComponentType.EXCLUSIVE_GATEWAY.getName());
  }

  private void processSequenceFlowElement(
      SequenceFlow sequenceFlowElement,
      BpmnModelInstance bpmnModelInstance,
      RuleLine ruleLine,
      DefinitionId definitionId) {
    List<RuleLine.Rule> ruleList = ruleLine.getRules();
    ConditionExpression conditionExpression =
        Objects.nonNull(sequenceFlowElement.getConditionExpression())
            ? sequenceFlowElement.getConditionExpression()
            : bpmnModelInstance.newInstance(ConditionExpression.class);
    conditionExpression.setTextContent(ruleList.get(0).getConditionalExpression());
    sequenceFlowElement.setConditionExpression(conditionExpression);
    // set the id of sequence flow if it is not already set
    ruleLine.setId(
        ConditionalElementHandler.createGlobalId(
            definitionId.toBuilder().entityId(sequenceFlowElement.getId()).build()));
  }
}
