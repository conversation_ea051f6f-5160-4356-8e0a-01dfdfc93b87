package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.google.common.base.CharMatcher;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.CompositeStepBuilderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadCompositeStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.StepNext;
import com.intuit.v4.workflows.WorkflowStepCondition;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * This file is used to build the rule lines for the multi condition workflow
 *
 */
@Component
@AllArgsConstructor
public class MultiStepConditionRuleLineBuilder implements RuleLineBuilder{

  private final WASContextHandler wasContextHandler;
  private final MultiStepRuleLineBuilder multiStepRuleLineBuilder;
  private final CompositeStepBuilderFactory compositeStepBuilderFactory;

  /**
   * This function initiates dfs traversal of the rulesMap that was generated post reading the dmn
   *
   * @param record                  Record instance
   * @param multiStepWorkflowEntity MultiStepWorkflowEntity instance
   * @param indexToRulesMap         map of dmn rules keyed by index values
   * @param attributeToHeaderMap    map of dmn input column values
   */
  @Override
  public void convertRulesToWorkflowStepCondition(
      Record record,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      Map<String, Map<String, List<List<Rule>>>> indexToRulesMap,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap) {

    if (indexToRulesMap.isEmpty()) {
      // if an empty map is returned after reading the dmn then
      // we cannot construct condition type workflowStep, return directly
      return;
    }
    // get list of yes rules for 0th key value from the dmn rules map
    List<List<Rule>> yesRules = indexToRulesMap.get(
            String.valueOf(WorkflowConstants.START_INDEX_VALUE))
        .get(WorkflowConstants.YES_RULE);
    // get list of no rules for 0th key value from the dmn rules map
    List<List<Rule>> noRules = indexToRulesMap.get(
            String.valueOf(WorkflowConstants.START_INDEX_VALUE))
        .get(WorkflowConstants.NO_RULE);
    WorkflowStep firstWorkflowStep = new WorkflowStep();
    // generate first workflowStep's id which will be used in upcoming recursive calls
    GlobalId firstWorkflowStepId = MultiStepUtil.generateWorkflowStepId(
        firstWorkflowStep.getTypeId(),
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        wasContextHandler);
    WorkflowLogger.logInfo("step=convertRulesToWorkflowStepCondition ruleIndex=%s",
        WorkflowConstants.START_INDEX_VALUE);
    buildWorkflowStepFromRuleDFS(
        record,
        Objects.isNull(yesRules) ? null : yesRules.stream().findFirst().orElse(null),
        Objects.isNull(noRules) ? null : noRules.stream().findFirst().orElse(null),
        attributeToHeaderMap,
        multiStepWorkflowEntity,
        indexToRulesMap,
        firstWorkflowStepId);
  }

  /**
   * This function recursively builds workflowSteps from rules present in the dmn rules map
   *
   * @param yesRules                list of yes rules
   * @param noRules                 list of no rules
   * @param attributeToHeaderMap    map of dmn's input/output columns
   * @param multiStepWorkflowEntity MultiStepWorkflowEntity instance
   * @param indexToRulesMap         map of dmn rules keyed by index
   * @param currentWorkflowStepId   current workflow step id
   */
  protected void buildWorkflowStepFromRuleDFS(
      Record record,
      List<Rule> yesRules,
      List<Rule> noRules,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      Map<String, Map<String, List<List<Rule>>>> indexToRulesMap,
      GlobalId currentWorkflowStepId) {

    Map<String, GlobalId> actionIdToStepIdMap = multiStepWorkflowEntity.getActionIdToStepIdMap();
    List<WorkflowStep> workflowSteps = multiStepWorkflowEntity.getWorkflowSteps();
    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = multiStepWorkflowEntity.getCompositeStepIdToWorkflowStepConditionMap();

    WorkflowStep workflowStep = new WorkflowStep();
    List<WorkflowStep.StepNext> stepNexts = new ArrayList<>();
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    RuleLine ruleLine = new RuleLine();

    // TODO - create a POJO to pass values to MultiStepRuleLineBuilder class

    // if our DMN has only one implicit column (Index), then rule lines will be empty
    List<RuleLine.Rule> generatedRuleLines = multiStepRuleLineBuilder.buildV4RuleLine(
        record, yesRules, currentWorkflowStepId, attributeToHeaderMap);
    WorkflowStep parentStep = MultiStepUtil.getParentWorkflowStep(workflowSteps,
        currentWorkflowStepId);

    ReadCompositeStepHandler handler = null;

    // if generatedRuleLines are not empty then set workflowStepCondition
    // and also add the current step in the list of workflow
    if (!generatedRuleLines.isEmpty()) {
      // set ruleLines
      ruleLine.setRules(generatedRuleLines);

      workflowStepCondition.setRuleLines(Arrays.asList(ruleLine));
      workflowStepCondition.setId(MultiStepUtil.generateWorkflowStepId(
          workflowStepCondition.getTypeId(),
          WorkflowConstants.CUSTOM_DECISION_ELEMENT,
          wasContextHandler));

      // Don't create a workflow step if rule's parameter type is FieldTypeEnum.DAYS and create workflow step condition for each flow
      handler = compositeStepBuilderFactory.getHandler(ruleLine);
      handler.handleCompositeStep(currentWorkflowStepId,
          workflowStep,
          stepNexts,
          workflowStepCondition,
          multiStepWorkflowEntity, parentStep);
    }
    List<StepNext> nextPaths = buildNextPaths(yesRules, noRules);

    // NextPaths is present inspite of the presence of rulelines or not
    // based on next paths generated, iterate over them and continue processing

    for (StepNext nextObj : nextPaths) {
      String result = nextObj.getWorkflowStepId();
      GlobalId workflowStepId = MultiStepUtil.generateWorkflowStepId(
          workflowStep.getTypeId(),
          MultiStepUtil.getWorkflowStepTypeId(result),
          wasContextHandler);
      nextObj.setWorkflowStepId(String.valueOf(workflowStepId));
      stepNexts.add(nextObj);

      // Remove the link of the intermediate decision element and merge it to create a composite step
      // Will be null only in case there are no rules
      if (handler != null) {
        handler.addToMap(nextObj, compositeStepIdToWorkflowStepConditionMap, workflowStepId,
            workflowStepCondition, parentStep, currentWorkflowStepId);
      }

      // This should be false since composite step will always be a leaf node and only a single condition for days
      if (MultiStepUtil.isDecisionResultTypeCondition(result)) {
        List<Rule> nextYesRules = indexToRulesMap.get(result).get(WorkflowConstants.YES_RULE).stream().findFirst().get();
        List<Rule> nextNoRules = indexToRulesMap.get(result).get(WorkflowConstants.NO_RULE).stream().findFirst().get();
        WorkflowLogger.logInfo("step=convertRulesToWorkflowStepCondition ruleIndex=%s", result);
        buildWorkflowStepFromRuleDFS(
            record,
            nextYesRules,
            nextNoRules,
            attributeToHeaderMap,
            multiStepWorkflowEntity,
            indexToRulesMap,
            workflowStepId);
      } else {
        /**
         * if dmn decisionResult is false, then there's no further action, hence skip adding
         * to map In case of JUEL expressions, 'action-2' is being converted to action-2
         * (removing single quotes) In case of FEEL expressions, "action-2" is being
         * converted to action-2 (removing double quotes)
         */
        char charToTrim = result.startsWith("\"")? '\"':'\'';
        result = CharMatcher.is(charToTrim).trimFrom(result);
        actionIdToStepIdMap.put(result, workflowStepId);
      }
    }
  }

  /**
   * This function generates the Next paths for the current workflowStep object that is being built
   *
   * @param yesRules list of yes rules
   * @param noRules  list of no rules
   * @return next paths
   */
  private List<WorkflowStep.StepNext> buildNextPaths(
      List<Rule> yesRules,
      List<Rule> noRules) {
    List<WorkflowStep.StepNext> nextPaths = new ArrayList<>();
    WorkflowStep.StepNext yesPath = buildNextPath(NextLabelEnum.YES, yesRules);
    WorkflowStep.StepNext noPath = buildNextPath(NextLabelEnum.NO, noRules);
    if (Objects.nonNull(yesPath)) {
      nextPaths.add(yesPath);
    }
    if (Objects.nonNull(noPath)) {
      nextPaths.add(noPath);
    }
    WorkflowLogger.logInfo("step=generateNextPaths yesPath=%s noPath=%s", yesPath, noPath);
    return nextPaths;
  }

  /**
   * @param nextLabelEnum yes or no path type
   * @param rules         list of yes/no rules
   * @return path object
   */
  private WorkflowStep.StepNext buildNextPath(NextLabelEnum nextLabelEnum, List<Rule> rules) {
    WorkflowStep.StepNext path = new WorkflowStep.StepNext();

    if(CollectionUtils.isEmpty(rules)) {
      return null;
    }

    OutputEntry ruleOutput = rules.stream().findFirst().get()
        .getOutputEntries().stream().findFirst().orElse(null);

    String outputValue = ruleOutput.getText().getTextContent();
    if(Objects.nonNull(outputValue)) {
      outputValue = outputValue.replace("\"", "");
    }

    if (CustomWorkflowUtil.isFalse(outputValue)) {
      return null;
    }

    path.setWorkflowStepId(outputValue);
    path.setLabel(nextLabelEnum);
    return path;
  }

}
