package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadMultiStepWorkflowHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiStepPlaceholderSubstitutor;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * This class is responsible for building actionGroup within workflowStep
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepDefinitionActionBuilder implements ReadMultiStepWorkflowHandler {

  private final ReadCustomDefinitionHandler readCustomDefinitionHandler;
  private final MultiStepPlaceholderSubstitutor multiStepPlaceholderSubstitutor;

  /**
   * This function prepares workflowStep with actionGroup
   *
   * @param multiStepWorkflowEntity multiStepWorkflowEntity
   */
  @Override
  public void buildWorkflowStep(MultiStepWorkflowEntity multiStepWorkflowEntity) {
    // if for current call activity element, a corresponding activity
    // instance does not exist then we will directly return
    if (Objects.isNull(multiStepWorkflowEntity.getActivityInstance())) {
      return;
    }
    List<WorkflowStep> workflowSteps = multiStepWorkflowEntity.getWorkflowSteps();
    Template templateObj = multiStepWorkflowEntity.getTemplate();
    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = multiStepWorkflowEntity.getCompositeStepIdToWorkflowStepConditionMap();
    WorkflowStep workflowStep = new WorkflowStep();
    // generate the actionGroup object with parent action and subAction structure
    ActionGroup stepActionGroup = readCustomDefinitionHandler.populateWorkflowStepWithActionGroup(
        templateObj.getName(), templateObj.getRecordType());
    workflowStep.setActionGroup(stepActionGroup);
    // filter out non-configurable parameters from parent action as well as subActions
    filterNonConfigurableActionParameters(workflowStep.getActionGroup());
    // substitute parameters field values with call activity placeholder values
    multiStepPlaceholderSubstitutor.substitutePlaceholderValues(
        multiStepWorkflowEntity.getActivityInstance(),
        templateObj.getRecordType(),
        workflowStep);
    // setting stepId for current workflowStep
    workflowStep.setId(multiStepWorkflowEntity.getGlobalId());
    workflowStep.setStepType(StepTypeEnum.ACTION);
    workflowSteps.add(workflowStep);
    WorkflowLogger.logInfo("step=processWorkflowStep stepType=action globalId=%s actionGroupId=%s",
        multiStepWorkflowEntity.getGlobalId(), stepActionGroup.getAction().getId());

    // If it contains the workflowstepcondition set it and change the steptype to workflowStep
    if (compositeStepIdToWorkflowStepConditionMap.containsKey(multiStepWorkflowEntity.getGlobalId())) {
      workflowStep.setWorkflowStepCondition(
          compositeStepIdToWorkflowStepConditionMap.get(multiStepWorkflowEntity.getGlobalId()));
      workflowStep.setStepType(StepTypeEnum.WORFKLOWSTEP);
    }
    workflowStep.setNext(Collections.emptyList());
  }

  /**
   * This function filters out non-configurable parameters from the parent action and subActions
   * list as these non-configurable parameters are not shown on the UI. All parameters where the
   * configurable field is set to false will be removed
   *
   * @param actionGroup actionGroup
   */
  private void filterNonConfigurableActionParameters(ActionGroup actionGroup) {
    // filter out non-configurable parameters from parent action
    actionGroup.getAction().setParameters(getFilteredParameters(actionGroup.getAction()));
    // filter out non-configurable parameters from sub actions
    actionGroup.getAction().getSubActions().stream().forEach(subAction -> {
      subAction.setParameters(getFilteredParameters(subAction));
    });
  }

  /**
   * This function filters parameter list based on whether isConfigurable is set to true or false
   *
   * @param action action object
   * @return filtered list of inputParameters
   */
  private List<InputParameter> getFilteredParameters(Action action) {
    return action.getParameters().stream().filter(inputParameter ->
        inputParameter.isConfigurable()).collect(Collectors.toList());
  }
}
