package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.RuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessingHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.LogicalOperatorsEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;


/**
 * This class is responsible for building rule line object in workflowStep Condition
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepRuleLineBuilder implements RuleLineBuilder {
  private final ReadCustomDefinitionHandler readCustomDefinitionHandler;
  private final FeatureFlagManager featureFlagManager;

  /**
   * This function converts dmn rule to workflowStep equivalent ruleLine. DMN rule -> ruleLine each
   * DMN rule has a list of input entries. These input entries are then converted to rule object.
   *
   * @param record               custom workflow record type object
   * @param yesRules             list of yesRules
   * @param workflowStepId       workflowStepId
   * @param attributeToHeaderMap map of dmn columns
   * @return list of ruleLines
   */
  @Override
  public List<RuleLine.Rule> buildV4RuleLine(
      Record record,
      List<Rule> yesRules,
      GlobalId workflowStepId,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap) {
    List<RuleLine.Rule> rules = new ArrayList<>();
    yesRules.stream().forEach(rule -> {
      List<InputEntry> ruleInputEntries = new ArrayList<>(rule.getInputEntries());
      AtomicInteger loopIterator = new AtomicInteger(0);
      Set<String> selectAllParameters = BpmnProcessingHelper.getSelectAllParametersForRule(rule);
      ruleInputEntries.stream().forEach(inputEntry -> {
        // skip processing the index column values while building rule lines
        if (ruleInputEntries.indexOf(inputEntry) != 0) {
          RuleLine.Rule generatedRule = buildV4Rule(
              attributeToHeaderMap,
              selectAllParameters,
              workflowStepId,
              inputEntry,
              loopIterator.get(),
              record);
          if (Objects.nonNull(generatedRule)) {
            rules.add(generatedRule);
          }
          WorkflowLogger.logInfo("step=generateV4RuleLine workflowStepId=%s ruleLineType=yes", workflowStepId);
        }
        loopIterator.getAndIncrement();
      });
    });
    return rules;
  }

  /**
   * This function generates the V4 Rule object from each Input Entry
   *
   * @param attributeToHeaderMap Map of DMN's input/output column values
   * @param selectAllParameters  select all parameter values
   * @param workflowStepId       workflowStepId
   * @param inputEntry           input entry
   * @param loopIterator         current iterator
   * @return rule object
   */
  private RuleLine.Rule buildV4Rule(
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      Set<String> selectAllParameters,
      GlobalId workflowStepId,
      InputEntry inputEntry,
      int loopIterator,
      Record record) {
    RuleLine.Rule rule = new RuleLine.Rule();
    try {
      DmnHeader dmnHeaderValue = MultiStepUtil.getDmnHeaderValue(attributeToHeaderMap,
          loopIterator);
      String dmnHeaderKey = MultiStepUtil.getDmnHeaderKey(attributeToHeaderMap, dmnHeaderValue);
      // set rule attributes
      rule.setParameterName(dmnHeaderKey);
      rule.selectedlogicalGroupingOperator(LogicalOperatorsEnum.AND);
      String inputEntryText = inputEntry.getText().getTextContent();
      if (StringUtils.isEmpty(inputEntryText)) {
        return null;
      }
      // if inputEntry has text content then we will convert it to
      // UI compatible rule expression
      String ruleExpression =
          BpmnProcessingHelper.getUIFriendlyRuleExpression(
              dmnHeaderKey,
              dmnHeaderValue.getDataType(),
              selectAllParameters,
              inputEntry.getText().getTextContent());
      if (StringUtils.isEmpty(ruleExpression)) {
        return null;
      }
      rule.setConditionalExpression(ruleExpression);
      rule.setParameterType(
          FieldTypeEnum.valueOf(dmnHeaderValue.getDataType().toUpperCase()));
      // get updated conditional expression by replacing rules with type double and && operator
      // Such rules will be converted to BTW operator
      RuleLine.Rule updatedRule = readCustomDefinitionHandler.getUpdatedRuleLines(rule, record);
      if (Objects.isNull(updatedRule)) {
        return null;
      }
      WorkflowLogger.logInfo(
          "step=generateV4Rule workflowStepId=%s parameterName=%s parameterType=%s conditionalExpression=%s",
          workflowStepId,
          updatedRule.getParameterName(),
          updatedRule.getParameterType(),
          updatedRule.getConditionalExpression());
      return updatedRule;
    } catch (Exception exception) {
      WorkflowLogger.error(
          () -> WorkflowLoggerRequest.builder()
              .message(
                  "Exception occurred while reading rules: unsupported parameter type workflowStepId=%s rule=%s inputEntry=%s",
                  workflowStepId, rule, inputEntry)
              .stackTrace(exception)
              .downstreamComponentName(DownstreamComponentName.WAS)
              .className(ReadCustomDefinitionHandler.class.getSimpleName()));
      throw new WorkflowGeneralException(
          "Exception occurred while reading rules: unsupported parameter type");
    }
  }
}