package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ProcessWorkflowStepFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiWorkflowStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.TemplateData;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * Builds template with multiple workflowSteps and templateSteps for multi-condition/multi-step
 * workflows
 *
 * <AUTHOR>
 */
@Component(WorkflowBeansConstants.MULTI_STEP_TEMPLATE_BUILDER)
@Slf4j
public class MultiStepTemplateBuilder extends TemplateBuilder {

  private ProcessWorkflowStepFactory processWorkflowStepFactory;

  public MultiStepTemplateBuilder(
      TemplateConditionBuilder conditionBuilder,
      TemplateActionBuilder actionBuilder,
      CustomWorkflowConfig customWorkflowConfig,
      WASContextHandler wasContextHandler,
      FeatureFlagManager featureFlagManager,
      TranslationService translationService,
      TemplateLabelsService templateLabelsService,
      ProcessWorkflowStepFactory processWorkflowStepFactory) {
    super(conditionBuilder,
        actionBuilder,
        customWorkflowConfig,
        wasContextHandler,
        featureFlagManager,
        translationService,
        templateLabelsService);
    this.processWorkflowStepFactory = processWorkflowStepFactory;
  }

  /**
   * Builds template for multi-condition/multi-step
   *
   * @param source              source
   * @param recordType          record type object
   * @param actionKey           action key
   * @param isPreCannedTemplate template type custom or precanned
   * @return template object
   */
  @Metric(name = MetricName.MULTI_STEP_READ_ONE_CUSTOM_TEMPLATE_BUILDER, type = Type.APPLICATION_METRIC)
  public Template build(
      String source,
      String recordType,
      String actionKey,
      boolean isPreCannedTemplate) {
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    WorkflowLogger.logInfo(
        "step=readMultiStepTemplateStart recordType=%s actionKey=%s isMultiStep=true",
        recordType, actionKey);
    Template template = initTemplateWithDefaultProperties(recordType);
    // if actionKey passed in ReadOne graphql call is null then pick
    // the actionkey from the first actionGroup under a record
    if (StringUtils.isBlank(actionKey)) {
      actionKey = record.getActionGroups().stream().findFirst().get().getId();
    }
    template.setWorkflowSteps(buildPrecannedWorkflowSteps(record, actionKey, isPreCannedTemplate));
    // If the config for a given record and action key contains actionIdMapper then we will
    // return templateData in the ReadOne template response. Multi-condition workflows will be enabled
    // individually per entity. The actionIdMapper will be added to the config and templateData should be
    // returned accordingly as part of ReadOne template response when queried. If templateData is passed in
    // graphql query only then return it in the response
    template.setTemplateData(generateTemplateData(record, actionKey));
    WorkflowLogger.logInfo(
        "step=readMultiStepTemplateEnd recordType=%s actionKey=%s isMultiStep=true",
        recordType, actionKey);
    return template;
  }

  /**
   * This function returns precanned template object based on template name/id passed
   *
   * @param templateId precanned template Id
   * @return template object
   */
  @Metric(name = MetricName.MULTI_STEP_READ_PRECANNED_TEMPLATE_BUILDER, type = Type.APPLICATION_METRIC)
  public Template getConfigTemplateById(final String templateId) {
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap().get(templateId);
      if (ObjectUtils.isEmpty(configTemplate)) {
          return null;
      }
    return prepareMultiStepTemplateFromConfig(configTemplate);
  }

  /**
   * This function prepares the template object for precanned templates
   *
   * @param configTemplate precanned template config
   * @return template object
   */
  private Template prepareMultiStepTemplateFromConfig(ConfigTemplate configTemplate) {
    Template template = new Template();
    try {
      WorkflowLogger.logInfo(
          "step=readPrecannedMultiStepTemplateStart templateId=%s recordType=%s actionKey=%s isMultiStep=%s",
          configTemplate.getId(), configTemplate.getRecord(),
          configTemplate.getActionGroups().get(0).getId(), true);
      template.setName(configTemplate.getId());
      template.setDisplayName(translationService.getString(configTemplate.getName(), getLocale()));
      template.setRecordType(configTemplate.getRecord());
      String actionKey = configTemplate.getActionGroups().stream().findFirst().get().getId();
      template.setDescription(
          translationService.getString(configTemplate.getDescription(), getLocale()));
      template.setId(
          GlobalId.builder()
              .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
              .setTypeId(template.getTypeId())
              .setLocalId(configTemplate.getId())
              .build());
      template.setWorkflowSteps(
          buildPrecannedWorkflowSteps(
              configTemplate,
              actionKey,
              true));
      // set templateData object
      template.setTemplateData(generateTemplateData(
          customWorkflowConfig.getRecordObjForType(configTemplate.getRecord()),
          configTemplate.getActionGroups().get(0).getId()));
      // set template category
      template.setCategory(configTemplate.getCategory());
      template.setRecurrence(prepareTemplateRecurrence(configTemplate.getRecurrenceRule()));
      templateLabelsService.fill(template);
    } catch (WorkflowGeneralException e) {
      WorkflowLogger.logError(
          "Exception occurred in Reading Template Id = %s", configTemplate.getId(), e);
      return null;
    }
    WorkflowLogger.logInfo(
        "step=readPrecannedMultiStepTemplateEnd templateId=%s recordType=%s actionKey=%s",
        configTemplate.getId(), configTemplate.getRecord(),
        configTemplate.getActionGroups().get(0).getId());
    return template;
  }

  /**
   * This function generates the list of default workflowSteps as defined in the custom workflow
   * config
   *
   * @param record              record object
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom/precanned
   * @return list of constructed workflowSteps
   */
  private List<WorkflowStep> buildPrecannedWorkflowSteps(
      Record record,
      String actionKey,
      boolean isPreCannedTemplate) {
    WorkflowLogger.logInfo(
        "step=buildWorkflowSteps recordType=%s actionKey=%s isPreCannedTemplate=%s",
        record.getId(), actionKey, isPreCannedTemplate);
    // initialise the configStepIdMap which will contain a map of stepId and config step object
    // this map will be used during runtime to extract the next config step object using stepId as key
    Map<Integer, Steps> configStepIdMap = new LinkedHashMap<>();
    configStepIdMap = MultiStepUtil.buildConfigStepIdMap(customWorkflowConfig, record, actionKey,
        isPreCannedTemplate);
    // list of default workflowSteps that will ultimately be returned in the response
    List<WorkflowStep> precannedWorkflowSteps = new ArrayList<>();
    // extract the first config step from the list and start parsing it based on the type defined
    if (configStepIdMap.isEmpty()) {
      WorkflowLogger.logWarn(
          "Workflow steps not defined in template config source=%s record=%s isPrecanned=%s",
          record, actionKey, isPreCannedTemplate);
      throw new WorkflowGeneralException(WorkflowError.STEPS_NOT_FOUND_IN_TEMPLATE_CONFIG);
    }
    List<Steps> currentConfigSteps = MultiStepUtil.findFirstWorkflowSteps(configStepIdMap);

    for (Steps currentConfigStep : currentConfigSteps ) {
      buildWorkflowStepFromConfigStep(
          record,
          actionKey,
          currentConfigStep,
          precannedWorkflowSteps,
          isPreCannedTemplate,
          configStepIdMap,
          null);
    }
    return precannedWorkflowSteps;
  }

  /**
   * Create template data with list of actionGroups. A workflow can have multiple steps. Each step
   * will have same building blocks like a trigger, set of rules, actions etc. If an approval type
   * template is requested then all supported actions like reminder/notification etc will continue
   * to be sent as part of workflowSteps. The approval building block present in the config will be
   * sent as part of the actionGroup object in templateData
   *
   * @param record              record
   * @param actionKey           action key
   * @return template steps comprising workflowStepCondition and list of actionGroups
   */
  public TemplateData generateTemplateData(
      Record record, String actionKey) {
    WorkflowLogger.logInfo(
        "step=buildTemplateData recordType=%s actionKey=%s",
        record.toString(), actionKey);
    TemplateData templateStepObject = new TemplateData();
    // set workflowStepCondition as part of templateData
    templateStepObject.setWorkflowStepCondition(
        conditionBuilder.build(record, actionKey, false, null));

    List<com.intuit.v4.workflows.ActionGroup> templateStepActionGroups = new ArrayList<>();
    List<ActionGroup> recordActionGroups = record.getActionGroups();

    if (CollectionUtils.isNotEmpty(recordActionGroups)) {
      recordActionGroups.stream().forEach(actionGroup -> {
        // If record action group contains actionIdMapper and the action group id is equal to actionKey then return it as part of actionGroup in templateData
        if (actionKey.equalsIgnoreCase(actionGroup.getId()) && Objects.nonNull(actionGroup.getActionIdMapper())
            && Objects.nonNull(actionGroup.getActionIdMapper().getActionId())) {
          Action action = actionBuilder.buildTemplateStepAction(
              record,
              actionGroup,
              actionGroup.getActionIdMapper().getActionId());
          com.intuit.v4.workflows.ActionGroup stepActionGroup = new com.intuit.v4.workflows.ActionGroup();
          stepActionGroup.setActionKey(actionGroup.getId());
          stepActionGroup.setAction(action);
          templateStepActionGroups.add(stepActionGroup);
        }
      });
    }
    templateStepObject.setActionGroup(templateStepActionGroups);
    return templateStepObject;
  }

  /**
   * This function populates the the workflow step object like adding trigger, nexts, and
   * workflowStepCondition or actionGroup based on whether its a conditon type step or an action
   * type step
   *
   * @param genericRecordObject record type object
   * @param currentConfigStep   current config step
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom/precanned
   * @param path                path object
   * @param configStepIdMap map of stepId and step
   * @return populated workflowStep
   */
  private WorkflowStep getPopulatedWorkflowStep(
      Record genericRecordObject,
      Steps currentConfigStep,
      String actionKey,
      boolean isPreCannedTemplate,
      WorkflowStep.StepNext path,
      Map<Integer, Steps> configStepIdMap) {
    // based on the config step type defined, get the appropriate handler and
    // process the step according to its type
    MultiWorkflowStepBuilder multiWorkflowStepBuilder = processWorkflowStepFactory
        .getHandler(currentConfigStep);
    WorkflowStep currentWorkflowStep = multiWorkflowStepBuilder.processWorkflowStep(
        genericRecordObject,
        actionKey,
        isPreCannedTemplate,
        currentConfigStep,
        path, configStepIdMap);
    // set the triggers for the current workflow step
    currentWorkflowStep.setTrigger(getTrigger(genericRecordObject, actionKey));
    WorkflowLogger.logInfo("step=buildNextPaths step=%s stepType=%s",
        currentConfigStep.getStepId(), currentConfigStep.getStepType());
    // generate the nexts array for the current workflowStep
    currentWorkflowStep.setNext(processNexts(currentConfigStep));
    return currentWorkflowStep;
  }

  /**
   * This function processes each config step. Based on the type of config step, we call handler
   * functions to process it either as a condition step or as an action step. The constructed step
   * is added to the list of precanned workflowSteps which will be finally be returned. We then
   * proceed with parsing the next paths for the current workflowStep. Once the next paths are
   * generated, this function is again invoked to parse the step basis the type
   *
   * @param record              record type object
   * @param actionKey           action key
   * @param currentConfigStep   current config step
   * @param workflowSteps       list of default workflow steps constructed so far
   * @param isPreCannedTemplate whether template is precanned/custom
   * @param path                path of current constructed workflow step
   */
  private void buildWorkflowStepFromConfigStep(
      Record record,
      String actionKey,
      Steps currentConfigStep,
      List<WorkflowStep> workflowSteps,
      boolean isPreCannedTemplate,
      Map<Integer, Steps> configStepIdMap,
      WorkflowStep.StepNext path) {
    WorkflowLogger.logInfo(
        "step=buildEachWorkflowStep stepId=%s stepType=%s actionKey=%s isPrecanned=%s",
        currentConfigStep.getStepId(),
        currentConfigStep.getStepType(),
        actionKey, isPreCannedTemplate);

    WorkflowStep currentWorkflowStep = getPopulatedWorkflowStep(
        record,
        currentConfigStep,
        actionKey,
        isPreCannedTemplate,
        path, configStepIdMap);
    // add the current step to the list of default workflow steps to be finally returned
    workflowSteps.add(currentWorkflowStep);
    // if nexts is not empty then process them individually based on whether the next
    // is an action step or a condition step.
    currentWorkflowStep.getNext().stream().forEach(nextPath -> {
      Optional<Steps> nextWorkflowStep = Optional.ofNullable(
          configStepIdMap.get(Integer.parseInt(nextPath.getWorkflowStepId())));
      // if config step with the given id exists, proceed to parse it accordingly based on
      // step type defined as an action or a condition step
      if (nextWorkflowStep.isPresent()) {
        buildWorkflowStepFromConfigStep(
            record,
            actionKey,
            nextWorkflowStep.get(),
            workflowSteps,
            isPreCannedTemplate,
            configStepIdMap,
            nextPath);
      }
    });
  }

  /**
   * This function processes the nexts (a list comprising yes or no paths) defined in the config
   * step andconverts them to the next type compatible with workflowStep object i.e as a
   * WorkflowStep.StepNext object
   *
   * @param configStep current config step
   * @return list of next paths generated from the config step
   */
  private List<WorkflowStep.StepNext> processNexts(Steps configStep) {
    List<WorkflowStep.StepNext> nexts = new ArrayList<>();
    // if step has nexts defined in the config then return the workflowStep equivalent of StepNext
    // otherwise return an empty array OR if it is a composite step since it will be a leaf node
    if (MultiStepUtil.isLeafNode(configStep)) {
      WorkflowLogger.logInfo("step=nextPathsNotFound step=%s", configStep.getStepId());
      return nexts;
    }
    configStep.getNexts().stream().forEach(next -> {
      WorkflowStep.StepNext nextPath = new WorkflowStep.StepNext();
      nextPath.setLabel(NextLabelEnum.fromValue(next.getLabel().toUpperCase()));
      nextPath.setWorkflowStepId(next.getStepId());
      WorkflowLogger.logInfo("step=generateNextPath nextLabel=%s nextPathId=%s",
          nextPath.getLabel().toString(), nextPath.getWorkflowStepId());
      nexts.add(nextPath);
    });
    return nexts;
  }
}
