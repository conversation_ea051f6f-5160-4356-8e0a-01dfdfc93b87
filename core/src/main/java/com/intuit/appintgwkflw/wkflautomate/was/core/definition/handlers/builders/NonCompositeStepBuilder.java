package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadCompositeStepHandler;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * This class has functions to handle the processing of a non composite step for read definition
 *
 * <AUTHOR>
 */
@Component
public class NonCompositeStepBuilder extends ReadCompositeStepHandler {

  /**
   * This step creates a workflowStep with the workflowstep condition for a non composite
   * (condition) step and adds it to the list
   *
   * @param currentWorkflowStepId
   * @param workflowStep
   * @param stepNexts
   * @param workflowStepCondition
   * @param multiStepWorkflowEntity
   * @param parentStep
   */
  @Override
  public void handleCompositeStep(GlobalId currentWorkflowStepId,
      WorkflowStep workflowStep,
      List<WorkflowStep.StepNext> stepNexts,
      WorkflowStepCondition workflowStepCondition,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      WorkflowStep parentStep) {
    List<WorkflowStep> workflowSteps = multiStepWorkflowEntity.getWorkflowSteps();
    workflowStep.setWorkflowStepCondition(workflowStepCondition);
    workflowStep.setNext(stepNexts);
    workflowStep.setId(currentWorkflowStepId);
    workflowStep.setStepType(StepTypeEnum.CONDITION);
    workflowSteps.add(workflowStep);
  }
}
