package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import org.camunda.bpm.model.dmn.instance.Rule;

import java.util.List;
import java.util.Map;

public interface RuleLineBuilder {

  /**
   * This function initiates dfs traversal of the rulesMap that was generated post reading the dmn
   *
   * @param record                  Record instance
   * @param multiStepWorkflowEntity MultiStepWorkflowEntity instance
   * @param indexToRulesMap         map of dmn rules keyed by index values
   * @param attributeToHeaderMap    map of dmn input column values
   */
  void convertRulesToWorkflowStepCondition(
      Record record,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      Map<String, Map<String, List<List<Rule>>>> indexToRulesMap,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap
  );

}
