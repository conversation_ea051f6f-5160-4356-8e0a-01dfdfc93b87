package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionIdMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/** This class will parse config and form V4 actions to be sent to clients */
@Component
@AllArgsConstructor
public class TemplateActionBuilder {
  private final WASContextHandler wasContextHandler;
  private final TranslationService translationService;

  /**
   * Build V4 actions for the record type from config. Each record has list of actions groups which
   * in turn can have one or more actions
   *
   * <pre>{@Code
   *  records:
   *  - id: RecordType
   *    actionGroups:
   *    - id: reminder
   *      actions:
   *       - id: sendExternalEmail
   *         name: Send Customer Email
   *
   *  actionGroups:
   *   - id: reminder
   *     actionIds:
   *     - createTask
   *     - sendExternalEmail
   *
   *  actions:
   *   - id: createTask
   *     parameters:
   *     - id: txnId
   *     - id: realmId
   *  }
   * </pre>
   *
   * @param record
   * @return
   */
  public List<WorkflowStep.ActionMapper> build(Record record) {
    return buildV4Actions(record.getActionGroups(), getRecordId(record));
  }

  /**
   * Transform config call activity actions to V4 actions
   * @return action object that contains subActions
   */
  public com.intuit.v4.workflows.Action buildTemplateStepAction(Record record, ActionGroup recordActionGroup, String parentActionId) {
    AtomicReference<com.intuit.v4.workflows.Action> action = new AtomicReference<com.intuit.v4.workflows.Action>();
    List<Action> recordActions = recordActionGroup.getActions();
    recordActions.stream().forEach(recordAction -> {
      if (recordAction.getId().equalsIgnoreCase(parentActionId)) {
        com.intuit.v4.workflows.Action v4ActionObj = new com.intuit.v4.workflows.Action();
        List<com.intuit.v4.workflows.Action> workflowSubActionsList = new ArrayList();
        v4ActionObj.setName(recordAction.getName());
        v4ActionObj.setId(GlobalId.builder()
                .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
                .setTypeId(v4ActionObj.getTypeId())
                .setLocalId(recordAction.getId())
                .build());
        v4ActionObj.setParameters(
                recordAction.getParameters().stream()
                        .filter(parameter -> BooleanUtils.toBoolean(parameter.getRequiredByUI()))
                        .map(CustomWorkflowUtil::transformActionToInputParameter)
                        .map(inputParameter -> localiseInputParameterFieldValues(inputParameter, getRecordId(record)))
                        .collect(Collectors.toList()));
        recordAction.getSubActions().stream().forEach(subAction -> {
          workflowSubActionsList.add(buildV4Action(subAction, getRecordId(record)));
        });
        v4ActionObj.setSubActions(workflowSubActionsList);
        action.set(v4ActionObj);
      }
    });
    return action.get();
  }

  /**
   * Transform config actions to V4 actions
   *
   * @param actionGroups
   * @return
   */
  private List<WorkflowStep.ActionMapper> buildV4Actions(
      List<ActionGroup> actionGroups, String recordId) {
    List<WorkflowStep.ActionMapper> workflowActionList = new ArrayList<>();
    for (ActionGroup actionGroup : actionGroups) {
      ActionIdMapper actionIdMapper = actionGroup.getActionIdMapper();
      String parentActionId = Objects.nonNull(actionIdMapper) ? actionIdMapper.getActionId() : null;
      for (Action action : actionGroup.getActions()) {
        WorkflowStep.ActionMapper actionV4 = new WorkflowStep.ActionMapper();
        actionV4.setActionKey(actionGroup.getId());
        // filter out the action object whose actionId matches the call activity actionId
        // actionId defined under actionIdMapper For eg: sendForApproval
        if(Objects.isNull(parentActionId) || !action.getId().equals(parentActionId)) {
          actionV4.setAction(buildV4Action(action, recordId));
          workflowActionList.add(actionV4);
        }
      }
    }
    return workflowActionList;
  }

  /**
   * Transform config action to V4 action
   *
   * @param action actions under action groups in config
   * @return
   */
  private com.intuit.v4.workflows.Action buildV4Action(Action action, String recordId) {
    com.intuit.v4.workflows.Action v4Action = new com.intuit.v4.workflows.Action();
    v4Action.setName(action.getName());
    // In case of pre-canned templates, some actions are selected by default so we will be honoring
    // selected value otherwise if it is null then will default to false.
    v4Action.setSelected(BooleanUtils.toBoolean(action.getSelected()));
    // in case of custom workflows, some actions are mandatory in which case required field will
    // be true
    v4Action.setRequired(BooleanUtils.toBoolean(action.getRequired()));
    v4Action.setId(
        GlobalId.builder()
            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(v4Action.getTypeId())
            .setLocalId(action.getId())
            .build());
    // Return only parameters which are required by UI
    v4Action.setParameters(
        action.getParameters().stream()
            .filter(parameter -> BooleanUtils.toBoolean(parameter.getRequiredByUI()))
            .map(CustomWorkflowUtil::transformActionToInputParameter)
            .map(inputParameter -> localiseInputParameterFieldValues(inputParameter, recordId))
            .collect(Collectors.toList()));
    return v4Action;
  }

  /**
   * This method localised the field values of InputParameter.
   *
   * @param inputParameter {@link InputParameter}
   * @param recordId
   * @return localised InputParameter {@link InputParameter}
   */
  private InputParameter localiseInputParameterFieldValues(
      InputParameter inputParameter, String recordId) {
    if (CollectionUtils.isNotEmpty(inputParameter.getFieldValues())) {
      List<String> localizedFieldValues = new ArrayList<>();
      RecordType recordType = RecordType.fromType(recordId);
      for (String fieldValue : inputParameter.getFieldValues()) {
        String localisedFieldValue =
            translationService.getFormattedString(
                fieldValue,
                wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE),
                Optional.ofNullable(recordType).map(RecordType::getDisplayValue).orElse(null));
        localizedFieldValues.add(localisedFieldValue);
      }
      inputParameter.setFieldValues(localizedFieldValues);
    }

    return inputParameter;
  }

  /**
   * This method returns the recordId for given record.
   *
   * @param record It can be either Record {@link Record} or ConfigTemplate {@link ConfigTemplate}
   * @return recordId
   */
  private String getRecordId(Record record) {
    // Reason for this check
    /**
     * For 'get all templates' API , we need to get 'record type' using 'record' field as 'id' field
     * contains the 'templateId'. For other cases, 'record type' will be retrieved using 'id'.
     */
    if (record instanceof ConfigTemplate) {
      return ((ConfigTemplate) record).getRecord();
    }
    return record.getId();
  }
}
