package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.RecurrenceParserUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.RecurrenceRuleMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import liquibase.pro.packaged.T;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/** This class will parse config and form V4 template to be sent to clients */
@Component(WorkflowBeansConstants.TEMPLATE_BUILDER)
@AllArgsConstructor
@Slf4j
public class TemplateBuilder {
  protected TemplateConditionBuilder conditionBuilder;
  protected TemplateActionBuilder actionBuilder;
  protected CustomWorkflowConfig customWorkflowConfig;
  protected WASContextHandler wasContextHandler;
  private FeatureFlagManager featureFlagManager;
  protected final TranslationService translationService;
  protected TemplateLabelsService templateLabelsService;

  /**
   * Get V4 template object from template config
   *
   * @param source
   * @param recordType record object
   * @param actionKey action key
   * @return V4 template object
   */
  public Template build(String source, String recordType, String actionKey, boolean isPreCannedTemplate) {
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    if (record == null) {
      WorkflowLogger.logWarn(
          "Template metadata requested for invalid record type. Source={}, RecordType={}",
          source,
          recordType);
      throw new WorkflowGeneralException(WorkflowError.INVALID_INPUT);
    }
    Template template = initTemplateWithDefaultProperties(recordType);

    if (StringUtils.isBlank(actionKey)) {
      actionKey = record.getActionGroups().get(0).getId();
    }
    template.setWorkflowSteps(createWorkflowSteps(record, actionKey, false));
    return template;
  }

  /**
   * This method process bpmn and skips setting the workflowSteps withing the
   * definition object
   *
   * @param templateId      template details object
   * @param templateDetails template id
   * @return template object
   */
  public Template buildTemplateDetails(GlobalId templateId, TemplateDetails templateDetails) {
    WorkflowLogger.logInfo("step=generateMultiStepTemplate for templateId=%s", templateId);
    Template template = new Template();
    template.setCategory(templateDetails.getTemplateCategory());
    // Set template status
    if (!org.springframework.util.ObjectUtils.isEmpty(templateDetails.getStatus())
            && !org.apache.commons.lang3.StringUtils.isEmpty(templateDetails.getStatus().getStatus())) {
      template.setStatus(
              WorkflowStatusEnum.fromValue(templateDetails.getStatus().getStatus().toUpperCase()));
    }
    template.setDescription(translationService.getFormattedString(
            templateDetails.getDescription(),
            wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)));
    template.setName(templateDetails.getTemplateName());
    template.setVersion(String.valueOf(templateDetails.getVersion()));
    // Adding setter for display name
    template.setDisplayName(translationService.getFormattedString(
            templateDetails.getDisplayName(),
            wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)));
    // Adding setter for allowMultipleDefinitions
    template.setAllowMultipleDefinitions(templateDetails.getAllowMultipleDefinitions());
    // Adding setter for Record Type
    Optional.ofNullable(templateDetails.getRecordType())
            .ifPresent(recordType -> template.setRecordType(recordType.getRecordType()));
    // set labels for templates
    templateLabelsService.fill(template);
    template.setId(templateId);
    return template;
  }

  /** Get Template with default props */
  protected Template initTemplateWithDefaultProperties(String recordType) {
    Template template = new Template();
    // In case of pre-canned templates, it is name of the template. It can be kept null.
    template.setName(WorkflowConstants.BUILD_CUSTOM_WORKFLOW);
    template.setRecordType(RecordType.fromType(recordType).getRecordType());
    return template;
  }

  /**
   * Create workflow steps with conditions and actions.
   *
   * <p>A workflow can have multiple steps. Each step will have same building blocks like a trigger,
   * set of rules and actions. While reading a template, we are not sure of user's intentions so we
   * will return single step. UI can provide an experience to replicate this step and create
   * multi-step workflow. While reading definition, it can have multiple steps based on the workflow
   * created.
   *
   * @param actionKey action key
   * @param record record object
   * @param isPreCannedTemplate whether template is precanned or custom
   * @return List of workflow steps
   */
  private List<WorkflowStep> createWorkflowSteps(
      Record record, String actionKey, Boolean isPreCannedTemplate) {
    WorkflowStep step = new WorkflowStep();
    step.setWorkflowStepCondition(conditionBuilder.build(record, actionKey, isPreCannedTemplate, null));
    // stepType for old workflow steps which include both workflowStepCondition and actions list
    step.setStepType(StepTypeEnum.WORFKLOWSTEP);

    step.setActions(actionBuilder.build(record));

    // This id is required for mutating a workflowStep during Create Definition call. For all the
    // workflowStep(s), trigger ID is equivalent to the workflowStep Id as there can't be a
    // workflowStep without trigger. So, we're hard-coding this id for the single workflow Step
    // therefore making this ID available to the UI.
    step.setId(
        GlobalId.builder()
            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(step.getTypeId())
            .setLocalId(WorkflowConstants.CUSTOM_START_EVENT)
            .build());
    step.setTrigger(getTrigger(record, actionKey));
    step.setRequired(true);
    return Arrays.asList(step);
  }

  /**
   * Method to get the trigger for a record and action
   *
   * @param record
   * @param actionKey
   * @return
   */
  protected Trigger getTrigger(Record record, String actionKey) {
    Trigger trigger = new Trigger();
    ActionGroup actionGroup =
            record.getActionGroups().stream()
                    .filter(actionGrp -> actionGrp.getId().equalsIgnoreCase(actionKey))
                    .findFirst()
                    .orElse(null);
    WorkflowVerfiy.verify(Objects.isNull(actionGroup), WorkflowError.INVALID_ACTION_KEY);
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Trigger triggerConfig = actionGroup.getTrigger();
    if (Objects.nonNull(triggerConfig) && !ObjectUtils.isEmpty(triggerConfig.getParameters())) {
      trigger.setId(
              GlobalId.builder()
                      .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
                      .setTypeId(trigger.getTypeId())
                      .setLocalId(WorkflowConstants.CUSTOM_START)
                      .build());
      List<InputParameter> triggerParameters = getTriggerParametersFromConfig(triggerConfig);
      trigger.setParameters(triggerParameters);
    }

    return trigger;
  }

  /**
   * Method to get the trigger parameters from the config for a record and action
   * @param trigger
   * @return List<InputParameter>
   */
  private List<InputParameter> getTriggerParametersFromConfig(com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Trigger trigger) {
    return Objects.nonNull(trigger.getParameters()) ? trigger.getParameters().stream()
            .map(CustomWorkflowUtil::transformActionToInputParameter)
            .collect(Collectors.toList()) : Collections.EMPTY_LIST;
  }

  /**
   * This methods build preCanned Templates and return a list of Templates
   *
   * @return
   */
  public List<Template> buildConfigTemplates() {
    Map<String, ConfigTemplate> templateMap = customWorkflowConfig.getTemplateMap();
    return templateMap.values().stream()
        .map(configTemplate -> prepareTemplateEntityFromConfigTemplate(configTemplate, false))
            .filter(template -> template != null)
            .collect(Collectors.toList());
  }

  /**
   * This method returns a template from the configuration based on Template Id
   *
   * @param templateId {@link String} : Template ID
   * @return
   */
  public Template getConfigTemplateById(String templateId) {
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap().get(templateId);
    if (ObjectUtils.isEmpty(configTemplate)) return null;

    return prepareTemplateEntityFromConfigTemplate(configTemplate, true);
  }

  /**
   * Builder Method to Build the Template Response.
   *
   * @param configTemplate : {@link ConfigTemplate}
   * @return : {@link Template} : v4 Template object
   */
  private Template prepareTemplateEntityFromConfigTemplate(
      ConfigTemplate configTemplate, boolean readWorkflowSteps) {
    Template template = new Template();
    try {
      template.setName(configTemplate.getId());
      template.setDisplayName(translationService.getString(configTemplate.getName(), getLocale()));
      template.setRecordType(configTemplate.getRecord());
      template.setDescription(
          translationService.getString(configTemplate.getDescription(), getLocale()));
      template.setId(
          GlobalId.builder()
              .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
              .setTypeId(template.getTypeId())
              .setLocalId(configTemplate.getId())
              .build());
      if (readWorkflowSteps) {
        template.setWorkflowSteps(
            createWorkflowSteps(
                configTemplate, configTemplate.getActionGroups().get(0).getId(), true));
      }
      template.setCategory(configTemplate.getCategory());
      template.setRecurrence(prepareTemplateRecurrence(configTemplate.getRecurrenceRule()));
      templateLabelsService.fill(template);
    } catch (WorkflowGeneralException | LocalisationGeneralException e) {
      WorkflowLogger.logError(
          "Exception occurred in Reading Template Id = %s", configTemplate.getId(), e);
      return null;
    }
    return template;
  }

  /**
   * sample recurrence rule of type WEEKLY
   * "recurrenceRule": {
   *    "interval": 1,
   *    "recurType": "WEEKLY",
   *    "daysOfWeek": ["MONDAY"]
   *    "startDate": "2021-08-02"
   * }
   *
   * if recurrenceRule is null, template recurrence is set to null
   * else, recurrenceRule mapping is done and set to template
   *
   */
  protected RecurrenceRule prepareTemplateRecurrence(RecurrenceRuleMapper recurrenceRule) {
    if (Objects.isNull(recurrenceRule)) {
      return null;
    }
    JSONObject recurrenceRuleJson = new JSONObject(ObjectConverter.toJson(recurrenceRule));
    return RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson);
  }

  protected String getLocale() {
    return wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE);
  }
}
