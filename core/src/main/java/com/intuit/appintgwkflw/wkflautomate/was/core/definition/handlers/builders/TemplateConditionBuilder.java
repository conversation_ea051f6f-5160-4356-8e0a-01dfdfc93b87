package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Operator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.ConditionalParameter;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * This class will parse the config and build the conditionalInput Parameters to be sent to the UI.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class TemplateConditionBuilder {
  private final CustomWorkflowConfig customWorkflowConfig;
  private WASContextHandler wasContextHandler;
  private TranslationService translationService;
  private FeatureFlagManager featureFlagManager;
  /**
   * Creates WorkflowStepCondition from config for a record. The template configuration has uber
   * list of attributes. Each record defines list of supported attributes. An attribute can have one
   * or more field overridden which will take highest precedence.
   *
   * @param record : Record
   * @param actionKey : {@link String}
   * @param isPreCanned : {@link Boolean}
   */
  public WorkflowStepCondition build(Record record, String actionKey, Boolean isPreCanned, Steps configStep) {
    /* <pre>[ replace with open brace ]@Code
     *   records
     *  - id: recordType,
     *    attributes:     -----------> Record attributes
     *    - Attribute1
     *     - Attribute2
     *     defaultAttributes:
     *     - String1
     *     - String2
     *
     *   attributes: -----------> Global attributes
     *   - Attribute1
     *   - Attribute2
     * [ replace with closed brace ]
     *
     */
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    if (record == null) {
      // This should not happen as we are checking for not null record type in the caller
      return workflowStepCondition;
    }

    ActionGroup actionGroup =
        record.getActionGroups().stream()
            .filter(actionGrp -> actionGrp.getId().equalsIgnoreCase(actionKey))
            .findFirst()
            .orElse(null);
    WorkflowVerfiy.verify(Objects.isNull(actionGroup), WorkflowError.INVALID_ACTION_KEY);

    Set<String> unsupportedAttributes = actionGroup.getUnsupportedAttributes();

    //Filter out hidden and unsupported attributes and create v4 conditions.
    // If attribute is controlled by an FF, check that FF is enabled.
    Predicate<Attribute> isAttributeEnabled =
        attribute -> !BooleanUtils.toBoolean(attribute.getHidden()) && (
            Objects.isNull(unsupportedAttributes) || !unsupportedAttributes.contains(
                attribute.getId())) && (Objects.isNull(attribute.getControllerFF())
            || featureFlagManager.getBoolean(attribute.getControllerFF(), false));

    List<ConditionalParameter> conditionalInputParams =
        record.getAttributes().stream()
            .filter(isAttributeEnabled)
            .map(this::prepareConditionalInputParameter)
            .collect(Collectors.toList());

    workflowStepCondition.setConditionalInputParameters(conditionalInputParams);
    // if config step is passed then pick attributes defined at step level
    // otherwise pick attributes defined at record level
    List<Attribute> recordAttributes = Objects.nonNull(configStep) && CollectionUtils.isNotEmpty(configStep.getAttributes())
            ? configStep.getAttributes() : record.getAttributes();
    // Set rule lines
    workflowStepCondition.setRuleLines(
        prepareDefaultRules(recordAttributes, record, actionKey, isPreCanned, configStep));

    // Setting GlobalID to condition object. this will be the Decision ID of DMN and same id will be
    // used in DMN reference field of Business Rule Task element of the BPMN.
    workflowStepCondition.setId(
        GlobalId.builder()
            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(workflowStepCondition.getTypeId())
            .setLocalId(WorkflowConstants.CUSTOM_DECISION_ELEMENT)
            .build());
    return workflowStepCondition;
  }

  /**
   * This method creates conditional input parameter from attribute and localised the operators name.
   *
   * @param attribute : Attribute from the config.
   * @return
   */
  private ConditionalParameter prepareConditionalInputParameter(Attribute attribute) {
    ConditionalParameter conditionalParameter = new ConditionalParameter();
    conditionalParameter.setInputParameter(transformToInputParam(attribute));
    /**
     * Get List of Supported Operators for record attribute. We will filter out unsupported
     * operators from list of all operators for a data type
     * <pre> {@Code
     *  dataTypes:
     *  - type: days
     *    operators:
     *     - id: BF
     *       name: days.before #Before
     *     - id: "ON"
     *       name: days.on #On
     *     - id: AF
     *       name: days.after #After
     *
     *  attributes:
     *  - id: transactionDate
     *    unsupportedOperators:
     *     - BF
     *
     * }
     *
     */

    // Get operator list using attribute data type and filter out unsupported operators
    List<Operator> dataTypeOperators =
        customWorkflowConfig.getDataTypes().stream()
            .filter(t -> t.getType().equalsIgnoreCase(attribute.getType()))
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DATATYPE_NOT_FOUND))
            .getOperators();

    Predicate<Operator> isOperatorSupported =
        operator ->
            CollectionUtils.isEmpty(attribute.getUnsupportedOperators())
                || !attribute.getUnsupportedOperators().contains(operator.getId());

    // Filter out unsupported operators and covert to v4 equivalent operator
    List<com.intuit.v4.workflows.definitions.Operator> supportedOperators =
        dataTypeOperators.stream()
            .filter(isOperatorSupported)
            .map(CustomWorkflowUtil::transformOperator)
            .map(
                operator -> {
                  operator.setDescription(
                      translationService.getString(
                          operator.getDescription(),
                          wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)));
                  return operator;
                }) // localised the description
            .collect(Collectors.toList());

    conditionalParameter.setSupportedOperators(supportedOperators);
    return conditionalParameter;
  }

  /**
   * Form input parameter object from attribute
   *
   * @param attribute Template attribute object
   * @return
   */
  private InputParameter transformToInputParam(Attribute attribute) {
    String id = attribute.getId();
    String type = attribute.getType();
    InputParameter inputParameter = new InputParameter();
    inputParameter.setParameterName(attribute.getName());
    inputParameter.setParameterType(
        ObjectUtils.isEmpty(type) ? null : FieldTypeEnum.fromValue(type.toUpperCase()));
    inputParameter.setPossibleFieldValues(0, id); // Revisit later
    inputParameter.setRequired(BooleanUtils.toBoolean(attribute.getRequired()));
    inputParameter.setConfigurable(BooleanUtils.toBoolean(attribute.getConfigurable()));
    inputParameter.setMultiSelect(BooleanUtils.toBoolean(attribute.getMultiSelect()));
    inputParameter.setGetOptionsForFieldValue(attribute.getFieldValueOptions());
    return inputParameter;
  }

  /**
   * Prepares rule lines for record. Each record has list of default attributes. These default
   * attributes constitute rule lines
   *
   * @param attributeList : Map Of Properties/Attributes of highest precedence
   * @param record : Value of recordType {@link Record}
   * @param actionKey : Action Key
   * @param isPreCanned : {@link Boolean}
   */
  private List<RuleLine> prepareDefaultRules(
      List<Attribute> attributeList, Record record, String actionKey, Boolean isPreCanned,
      Steps configStep) {
    RuleLine ruleLine = new RuleLine();
    // Reading the default attribute Ids from record
    Map<String, Attribute> recordAttributeMap =
        attributeList.stream().collect(Collectors.toMap(Attribute::getId, Function.identity()));

    ActionGroup actiongroup =
        record.getActionGroups().stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
            .findFirst()
            .get();

    // get defaultAttributes list
    Set<String> defaultAttributes = CustomWorkflowUtil.getDefaultAttributes(
        recordAttributeMap,
        customWorkflowConfig,
        record,
        actiongroup,
        configStep);
    // In case of entities like Purchase Order, Estimate,  we don't have multiple actiongroup
    // support for now. So we're using the defaultAttributes defined at the entity level. In case in
    // future, if we plan on introducing different actionGroups per new entities, the local
    // properties will be overridden as on line 215.
    // This check will ensure that both pre-canned templates and custom workflow entities will work
    // as
    // expected in case defaultAttributes is not defined at actiongroup level for an entity and will
    // help in avoiding unnecessary changes in the existing config.
    defaultAttributes.stream()
        .forEach(id -> ruleLine.addRules(createRuleLineFromAttribute(recordAttributeMap.get(id))));
    // We need to set MappedActionKeys on rules. This mapped action keys are from the list of
    // outputs of the dmn to be be used.
    // This is used while populating rule lines in dmn. BusinessRuleTaskHandler#processRuleLine
    // We have defined generic single output in the dmn

    ruleLine.setMappedActionKeys(Arrays.asList(WorkflowConstants.CUSTOM_DECISION_RESULT));
    return Arrays.asList(ruleLine);
  }

  /**
   * Form rule line from attribute
   *
   * @param attribute
   * @return
   */
  private Rule createRuleLineFromAttribute(Attribute attribute) {
    Rule rule = new Rule();
    rule.setParameterName(attribute.getName());
    rule.setConditionalExpression(
        MessageFormat.format(
            "{0} {1}", attribute.getDefaultOperator(), attribute.getDefaultValue()));
    return rule;
  }
}
