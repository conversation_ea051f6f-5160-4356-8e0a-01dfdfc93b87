package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.definitions.InputParameter;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * This class will parse the config and build the trigger Parameters to be sent to the UI.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class TemplateTriggerBuilder {

    private WASContextHandler wasContextHandler;

    /**
     * Method to build the trigger to be passed to the UI after parsing the config
     * @param record
     * @param actionKey
     * @return Trigger
     */
    public Trigger build(Record record, String actionKey) {
        Trigger trigger = new Trigger();
        ActionGroup actionGroup =
                record.getActionGroups().stream()
                        .filter(actionGrp -> actionGrp.getId().equalsIgnoreCase(actionKey))
                        .findFirst()
                        .orElse(null);
        WorkflowVerfiy.verify(Objects.isNull(actionGroup), WorkflowError.INVALID_ACTION_KEY);
        if (Objects.nonNull(actionGroup.getTrigger()) && Objects.nonNull(actionGroup.getTrigger().getParameters())) {
            List<InputParameter> parameters = actionGroup.getTrigger().getParameters().stream().map(CustomWorkflowUtil::transformActionToInputParameter)
                    .collect(Collectors.toList());
            trigger.setParameters(parameters);
            trigger.setId(
                    GlobalId.builder()
                            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
                            .setTypeId(trigger.getTypeId())
                            .setLocalId(WorkflowConstants.CUSTOM_START)
                            .build());
        }
        return trigger;
    }
}
