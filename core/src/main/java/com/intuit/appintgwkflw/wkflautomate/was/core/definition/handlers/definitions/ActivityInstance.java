package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import java.util.Map;
import lombok.Builder;
import lombok.Data;
import org.camunda.bpm.model.dmn.DmnModelInstance;

/**
 * Wrapper class for an activity containing information like activityType(condition, call activity),
 * the placeholder values associated with it, etc.
 *
 * <AUTHOR>
 */
@Data
@Builder
public class ActivityInstance {
  //dmnModelInstance associated for the dmn activity. It will be null for call activity
  private DmnModelInstance dmnModelInstance;

  //The placeholder values associated with the activity
  private Map<String, Object> userAttributes;

  //Map of child activityId to the child activity instances(ex. createTask, sendPushNotification, etc.)
  private Map<String, ActivityInstance> childActivityInstances;
}