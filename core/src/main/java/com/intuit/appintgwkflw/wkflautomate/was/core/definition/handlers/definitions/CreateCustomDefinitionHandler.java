package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ConfigurationDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomAttributesUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.Incoming;
import org.camunda.bpm.model.bpmn.impl.instance.TimerEventDefinitionImpl;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ConditionExpression;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.xml.impl.instance.ModelElementInstanceImpl;
import org.springframework.stereotype.Component;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * This class has additional logic needed for creating definition for custom workflows.
 *
 * <p>Sets filter conditions and process variables in startEvent Sets handler details and additional
 * parameters (process variables etc) in tasks
 */
@Component(WorkflowBeansConstants.CREATE_CUSTOM_DEFINITION_HANDLER)
public class CreateCustomDefinitionHandler extends CreateDefinitionHandler {
  private final CustomWorkflowConfig customWorkflowConfig;
  private final FilterParameterExtractorUtil filterParameterExtractorUtil;

  //TODO: Refactor to reduce the number of dependencies in this class and the parent class

  public CreateCustomDefinitionHandler(
      ConditionalElementFactory conditionalElementFactory,
      PlaceholderExtractorProvider placeholderExtractorProvider,
      CustomWorkflowConfig customWorkflowConfig,
      TranslationService translationService,
      WASContextHandler wasContextHandler,
      FeatureFlagManager featureFlagManager,
      FilterParameterExtractorUtil filterParameterExtractorUtil) {
    super(
        conditionalElementFactory,
        placeholderExtractorProvider,
        translationService,
        wasContextHandler,
        featureFlagManager);
    this.customWorkflowConfig = customWorkflowConfig;
    this.filterParameterExtractorUtil = filterParameterExtractorUtil;
  }

  /**
   * Adds/updates the user provided values in parametersDetails in the BPMN xml. Here the function
   * to process parameter details is passed
   *
   * @param baseElement {@link BaseElement}
   * @param actionMapper {@link ActionMapper}
   * @return
   */
  @Override
  protected Map<String, HandlerDetails.ParameterDetails> updateActionParametersWithUserInputs(
      BaseElement baseElement, ActionMapper actionMapper, String recordType) {
    return updateParametersWithUserInputs(
        baseElement,
        inputOutputContent ->
            CustomWorkflowUtil.processActionParameterDetails(
                customWorkflowConfig, baseElement.getId(),
                actionMapper.getAction().getParameters(),
                actionMapper.getActionKey(), recordType));
  }

  /**
   * Once action is created, do additional setup for custom workflow.<br>
   * Set handler details<br>
   * Set process variables<br>
   * Mutate conditional element's expression. For example if task needs to be created, ${createTask
   * == true} expression in the sequence flow associated with task will be change to ${true}
   *  @param actionMapper
   * @param bpmnModelInstance
   * @param definition
   */
  @Override
  protected void postProcessAction(
      ActionMapper actionMapper, BpmnModelInstance bpmnModelInstance,
      Definition definition) {
    // Ids of the BPMN element Action like createTask, sendNotification etc. and config action
    // element have to be consistent
    Action v4Action = actionMapper.getAction();
    // Split the local Id as during processing, local Ids are changed to localId_realmId_uuid
    String actionId = v4Action.getId().getLocalId().split(WorkflowConstants.UNDERSCORE)[0];

    FlowElement actionElement =
        bpmnModelInstance.getModelElementById(v4Action.getId().getLocalId());

    // code to mutate inclusive gateway branches
    Collection<Incoming> incomingSequenceElements =
        actionElement.getChildElementsByType(Incoming.class);
    if (!incomingSequenceElements.isEmpty()) {

      // then read the value from the payload and mutate the branch
      String incomingSequenceElementId =
          incomingSequenceElements.stream()
              .map(ModelElementInstanceImpl::getTextContent)
              .collect(Collectors.joining(WorkflowConstants.COMMA));

      processSequenceFlowElement(
          incomingSequenceElementId, bpmnModelInstance, v4Action.isSelected());
    }

    if (!BooleanUtils.toBoolean(v4Action.isSelected())) {
      // If action is not selected, skip post processing steps
      return;
    }

    // Find relevant config action entry. From this entry, we will get handler details and other
    // parameters
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action recordAction =
        CustomWorkflowUtil.getRecordActionFromConfig(
            customWorkflowConfig, definition.getRecordType(), actionMapper.getActionKey(), actionId);

    //Adding this log to debug issue with custom workflow in Perf environment, we will remove this once the issue is resolved.
    //TODO Remove this logs once the issue with perf env is figured out
    if(recordAction == null){
      WorkflowLogger.logInfo("Record action object is null for recordType=%s actionId=%s actionKey=%s",definition.getRecordType(), actionId, actionMapper.getActionKey());
      WorkflowLogger.logInfo("Custom workflow config from config file=%s", customWorkflowConfig.toString());
    } else if(recordAction.getHandler() == null){
      WorkflowLogger.logInfo("Record action handler is null for recordType=%s actionId=%s actionKey=%s",definition.getRecordType(), actionId, actionMapper.getActionKey());
      WorkflowLogger.logInfo("Custom workflow config from config file=%s", customWorkflowConfig.toString());
    }

    // Append the handler details for actions elements in the bpmn from the template config.
    setHandlerDetails(actionElement, recordAction.getHandler().getHandlerDetail());
    // Get and set any non client configured parameters like process variables
    setNonConfigurableParameters(actionElement, recordAction, actionMapper, definition.getRecordType());

    // The help variables are added as PROCESS_VARIABLE in the duzzit paramDetails
    setHelpVariablesAsProcessVariablesInAction(actionElement, recordAction);

    // For Scheduled Action, Rules needs to be send to appConnect in general.
    // TODO: In case we have multiple actions where we need to set/un-set this, approach can be
    // re-visited. https://jira.intuit.com/browse/QBOES-10984
    //TODO: To remove once custom scheduled actions is deployed as SDEF and migrated completely
    if (CustomWorkflowType.SCHEDULEDACTIONS
        .getActionKey()
        .equalsIgnoreCase(actionMapper.getActionKey())) {
      Map<String, ParameterDetails> parameterDetailsMap = filterParameterExtractorUtil.getFilterParameterDetails(definition);
      BaseElement modelElement = bpmnModelInstance.getModelElementById(actionId);
      BpmnProcessorUtil.setParametersInElement(modelElement, () -> parameterDetailsMap);
    }
  }

  /**
   * Create and set Handler details. The HandlerDetails object is referred by WAS in resolving which
   * action handler to execute while servicing Camunda tasks
   *
   * @param baseElement {@link BaseElement}
   * @param handlerDetails
   */
  private void setHandlerDetails(BaseElement baseElement, HandlerDetails handlerDetails) {
    BpmnProcessorUtil.setVariableInElement(
        baseElement, WorkFlowVariables.HANDLER_DETAILS_KEY, () -> handlerDetails);
  }

  private void setNonConfigurableParameters(
      FlowElement actionElement,
      com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
          recordAction,
      ActionMapper actionMapper,
      String recordType) {
    BpmnProcessorUtil.setParametersInElement(
        actionElement,
        () ->
            CustomWorkflowUtil.extractProjectMetaDataForAction(
                recordAction, actionMapper, recordType));
  }

  /**
   * Add the help variables in the action duzzit paramDetails as the PROCESS_VARIABLE. This is
   * needed to pass the values from the poller duzzit to the action duzzit input parameter.
   */
  private void setHelpVariablesAsProcessVariablesInAction(
      FlowElement actionElement,
      com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
          recordAction) {
    Map<String, ParameterDetails> helpVariableParamMap = CustomWorkflowUtil.getHelpVariableParameterMap(recordAction);
    // Update the parameterDetails in the BPMN with the helpVariableParamMap which contains the
    // processVariables
    BpmnProcessorUtil.setParametersInElement(actionElement, () -> helpVariableParamMap);
  }

  /**
   * Set filterConditions object and processVariable list in StartEvent.
   *
   * @param definitionInstance {@link DefinitionInstance}
   */
  @Override
  public void postProcess(DefinitionInstance definitionInstance) {
    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(definitionInstance.getBpmnModelInstance());
    Optional<Map<String, Set<String>>> optionalStepDetails =
        SchemaDecoder.getStepDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(startEventElement));

    WorkflowVerfiy.verify(!optionalStepDetails.isPresent(), WorkflowError.STEP_DETAILS_NOT_FOUND);
    if (optionalStepDetails.isPresent()) {
      setAppConnectFilters(definitionInstance, optionalStepDetails.get().keySet());
    }
    setProcessVariables(startEventElement, definitionInstance.getDefinition());
    setTriggerHandlerDetailInStartEvent(startEventElement, definitionInstance.getDefinition());
  }

  /**
   * Set handlerDetails in start event if present in config.
   * This is done to reuse the same workflow for different kinds of trigger. For example Custom
   * notification can be sent acting on an event in real time (payment remittance) or based on
   * schedule (project profitability notification). The trigger will be different and set
   * while creating the definition.
   * @param startEventElement
   * @param definition
   */
  private void setTriggerHandlerDetailInStartEvent(FlowElement startEventElement, Definition definition) {
    // Get the actiongroup from definition and check if trigger is available in action group. If yes,
    // set this trigger as handlerDetails in start event
    Optional<WorkflowStep> workflowStep = definition.getWorkflowSteps().stream().findFirst();
    if(workflowStep.isPresent()) {
      Optional<ActionMapper> stepAction = workflowStep.get().getActions().stream().findFirst();
      stepAction.ifPresent(actionMapper -> {
        String actionKey = actionMapper.getActionKey();
        ActionGroup actionGroupForKey = customWorkflowConfig
            .getRecordObjForType(definition.getRecordType()).getActionGroups().stream()
            .filter(actionGroup -> actionKey.equalsIgnoreCase(actionGroup.getId()))
            .findFirst().get();
        if (actionGroupForKey.getTrigger() != null
            && actionGroupForKey.getTrigger().getHandler() != null) {

          setHandlerDetails(startEventElement,
              actionGroupForKey.getTrigger().getHandler().getHandlerDetail());
        }
      });
    }
  }

  /**
   * Saves the Recurrence object as a json in the startElement of the bpmn.
   *
   * @param definitionInstance : {@link DefinitionInstance}
   */
  @Override
  protected void setRecurringInfo(DefinitionInstance definitionInstance) {
    FlowElement startEventElement =
        BpmnProcessorUtil.findStartEventElement(definitionInstance.getBpmnModelInstance());
    RecurrenceRule recurrenceRule = definitionInstance.getDefinition().getRecurrence();

    Optional<CamundaProperty> camundaPropertyOptional =
        BpmnProcessorUtil.getCamundaProperty(
            startEventElement, WorkFlowVariables.RECURRENCE_RULE_KEY.getName());

    camundaPropertyOptional.ifPresent(
        camundaProperty -> {
          String recurrenceRuleJson = ObjectConverter.toJson(recurrenceRule);
          camundaProperty.setCamundaValue(recurrenceRuleJson);
        });
    setTimerDefinitionForRecurElements(definitionInstance, startEventElement);
  }

  /**
   * Set Recurrence Properties key,value map identifying which key [start/cron] needs to be mapped
   * on which Camunda timer element
   *
   * @param definitionInstance : {@link DefinitionInstance}
   * @param startEventElement : : {@link FlowElement} Start Element of the bpmn
   */
  private void setTimerDefinitionForRecurElements(
      DefinitionInstance definitionInstance, FlowElement startEventElement) {
    Optional<Map<String, String>> optionalRecurrenceDetails =
        SchemaDecoder.getRecurrenceElementDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(startEventElement));
    if (!optionalRecurrenceDetails.isPresent()) {
      return;
    }

    Map<String, String> recurrenceDetails = optionalRecurrenceDetails.get();

    // Populating Start Date
    setDateOrFrequency(
        recurrenceDetails.get(WorkflowConstants.RECURRENCE_START_DATE), definitionInstance);
    // Populating Cron Expression
    setDateOrFrequency(
        recurrenceDetails.get(WorkflowConstants.RECURRENCE_SCHEDULE), definitionInstance);
  }

  /**
   * Utility Method to populate bpmn with the date values and cron Expression -> Frequency in Timer
   * Events. Based on RecurrenceDetails object, it will have 2 keys, 1 for startDate and other for
   * cron Expression. The startDate will be of type TimeDate object and cron will be of type
   * TimeCycle object. We are using this utility method to set startDate and cron expression based
   * on the mappings and in the correct object.
   *
   * @param id : id of the timer event element.
   * @param definitionInstance : definitionInstance object
   */
  private void setDateOrFrequency(String id, DefinitionInstance definitionInstance) {
    if (StringUtils.isBlank(id)) {
      return;
    }
    RecurrenceRule recurrenceRule = definitionInstance.getDefinition().getRecurrence();

    String cronExpression = RecurrenceUtil.getCronExpression(recurrenceRule);
    String startDateOutput = RecurrenceUtil.getFormattedRecurrenceStartDate(recurrenceRule);
    Optional<TimerEventDefinitionImpl> timerEventOptional = RecurrenceUtil.getTimerEvent(id, definitionInstance);

    if (timerEventOptional.isPresent()) {
      TimerEventDefinitionImpl timerEvent = timerEventOptional.get();
      // Setting date here
      if (Objects.nonNull(timerEvent.getTimeDate())) {
        timerEvent.getTimeDate().setTextContent(startDateOutput);
      }
      // Setting cron here
      else if (Objects.nonNull(timerEvent.getTimeCycle())) {
        timerEvent.getTimeCycle().setTextContent(cronExpression);
      }
    }
  }


  /**
   * Utility method to set process variables in start element. Process variables will be set from
   * rulelines
   *
   * @param startEvent {@link FlowElement} : Start Event of BPMN Process
   * @param definition {@link Definition} : Definition Object
   */
  private void setProcessVariables(FlowElement startEvent, Definition definition) {
    String processVariableDetailsData =
        BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements())
            .get(WorkFlowVariables.PROCESS_VARIABLE_DETAILS_KEY.getName());

    if (StringUtils.isEmpty(processVariableDetailsData)) {
      return;
    }

    List<ProcessVariableDetail> processVariableDetails =
        ObjectConverter.fromJson(
            processVariableDetailsData, new TypeReference<List<ProcessVariableDetail>>() {});

    // Map of process  variable name vs process variable. It will help as we don't want to add same
    // process variable twice.
    Map<String, ProcessVariableDetail> processNameToDetailMap =
        processVariableDetails.stream()
            .collect(Collectors.toMap(ProcessVariableDetail::getVariableName, Function.identity()));

    // Append process variables from rules in start event
    appendProcessVariableListFromRules(processNameToDetailMap, definition);

    // Append process variables from helpVariables in start event
    appendProcessVariablesFromHelpVariables(processNameToDetailMap, definition.getRecordType());

    // replace this mutated list in the Camunda properties of startEvent
    Optional<CamundaProperty> camundaPropertyOptional =
        BpmnProcessorUtil.getCamundaProperty(
            startEvent, WorkFlowVariables.PROCESS_VARIABLE_DETAILS_KEY.getName());

    camundaPropertyOptional.ifPresent(
        camundaProperty -> {
          String jsonString = ObjectConverter.toJson(processNameToDetailMap.values());
          camundaProperty.setCamundaValue(jsonString);
        });
  }

  /**
   * This is a utility method which will append the list of process variables from the Rule
   * parameters. We use attribute Id as process variable name.
   *
   * @param processNameToDetailMap
   * @param definition
   */
  private void appendProcessVariableListFromRules(
      Map<String, ProcessVariableDetail> processNameToDetailMap, Definition definition) {
    // Get the RuleLines from the WorkflowStepCondition object
    List<Rule> ruleLines = CustomWorkflowUtil.getRulesFromDefinition(definition);

    // Get list of Parameter name for the rules provided
    Set<String> parameterNames =
        ruleLines.stream().map(RuleLine.Rule::getParameterName).collect(Collectors.toSet());

    Map<String, String> typeToNativeTypeMap =
        customWorkflowConfig.getDataTypes().stream()
            .collect(Collectors.toMap(DataType::getType, DataType::getNativeType));
    customWorkflowConfig
        .getAttributes()
        .forEach(
            attribute -> {
              if (parameterNames.contains(attribute.getName())) {
                ProcessVariableDetail processVariableDetail = new ProcessVariableDetail();
                processVariableDetail.setVariableName(attribute.getName());
                processVariableDetail.setVariableType(typeToNativeTypeMap.get(attribute.getType()));
                processNameToDetailMap.put(
                    processVariableDetail.getVariableName(), processVariableDetail);
              }
            });

    /**
     * Adding the support for saving the custom fields in the process variables in definition xml
     * Getting the Custom Field Attributes and appending them in the process variables
     */
      Map<String, Attribute> nameToAttributeMapping =
          customWorkflowConfig
              .getRecordObjForType(definition.getRecordType())
              .getAttributes()
              .stream()
              .collect(Collectors.toMap(Attribute::getName, Function.identity()));
      List<Attribute> customAttributes =
          CustomAttributesUtil.getRulesForCustomAttributes(nameToAttributeMapping, ruleLines);
      CustomAttributesUtil.updateProcessVariableMapWithCustomFieldAttributes(
          customAttributes, processNameToDetailMap, typeToNativeTypeMap);
  }

  /**
   * Add process variables needed to fill helpVariables. For example DocNumber is not part of rules
   * but it is part of help variables
   */
  private void appendProcessVariablesFromHelpVariables(
      Map<String, ProcessVariableDetail> processNameToDetailMap, String recordType) {
    customWorkflowConfig.getRecordObjForType(recordType).getHelpVariables().stream()
        .forEach(
            helpVariable -> {
              String[] helpVariableTokens = helpVariable.split(WorkflowConstants.COLON);
              String processVariableName = helpVariableTokens[1];
              String type = helpVariableTokens[2];
              if (!processNameToDetailMap.containsKey(processVariableName)) {
                ProcessVariableDetail processVariableDetail = new ProcessVariableDetail();
                processVariableDetail.setVariableName(processVariableName);
                processVariableDetail.setVariableType(type);
                processNameToDetailMap.put(
                    processVariableDetail.getVariableName(), processVariableDetail);
              }
            });
  }

  /**
   * This method processes Sequence flows leading from Inclusive Gateway to Actions. It injects
   * conditional expression based on user selection at the UI. It checked Action block's isSelected
   * parameter.
   *
   * @param sequenceFlowElementId : Id of the sequence Element {@link String}
   * @param bpmnModelInstance : bpmn model instance object {@link BpmnModelInstance}
   * @param actionSelected
   */
  private void processSequenceFlowElement(
      String sequenceFlowElementId, BpmnModelInstance bpmnModelInstance, Boolean actionSelected) {
    SequenceFlow sequenceElement = bpmnModelInstance.getModelElementById(sequenceFlowElementId);
    if (sequenceElement != null) {
      ConditionExpression conditionExpression =
          Objects.nonNull(sequenceElement.getConditionExpression())
              ? sequenceElement.getConditionExpression()
              : bpmnModelInstance.newInstance(ConditionExpression.class);

      conditionExpression.setTextContent(
          MessageFormat.format(
              "{0}{1}{2}",
              WorkflowConstants.DMN_VAR_OPEN_BRACE, BooleanUtils.toBoolean(actionSelected),WorkflowConstants.DMN_VAR_CLOSE_BRACE));

      sequenceElement.setConditionExpression(conditionExpression);
    }
  }

  /**
   * Sets filters using rule lines and the record type. Sets the extracted filter in various
   * workflow steps to be sent to app-connect duzzits for filtering. In startEvent element, it will
   * set parameterDetails in extensions element and in waitEvent element will set parameterDetails
   * in input/output element
   *
   * @param definitionInstance : definition instance {@link DefinitionInstance}
   * @param steps : Map of step-details {@link Map }
   */
  private void setAppConnectFilters(DefinitionInstance definitionInstance, Set<String> steps) {
    // Get the Rules from the definition
    Map<String, ParameterDetails> parameterDetailsMap =
       filterParameterExtractorUtil.getFilterParameterDetails(definitionInstance.getDefinition());
    BpmnModelInstance bpmnModelInstance = definitionInstance.getBpmnModelInstance();
    for (String stepId : steps) {
      BaseElement modelElement = bpmnModelInstance.getModelElementById(stepId);
      BpmnProcessorUtil.setParametersInElement(modelElement, () -> parameterDetailsMap);
    }
  }


}
