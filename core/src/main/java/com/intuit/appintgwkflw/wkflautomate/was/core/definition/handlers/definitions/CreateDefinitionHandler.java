package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttle;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ConfigurationDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ParameterDetailsConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BoundaryEvent;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.Definitions;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.Task;
import org.camunda.bpm.model.bpmn.instance.TimerEventDefinition;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DmnElement;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/** This class handles processing of Definition by writing to bpmn and update the object */
@Component(WorkflowBeansConstants.CREATE_DEFINITION_HANDLER)
@RequiredArgsConstructor
public class CreateDefinitionHandler implements DefinitionCrudHandler<DefinitionInstance> {

  private final ConditionalElementFactory conditionalElementFactory;
  private final PlaceholderExtractorProvider placeholderExtractorProvider;
  private final TranslationService translationService;
  private final WASContextHandler wasContextHandler;
  private final FeatureFlagManager featureFlagManager;

  /**
   * Updates the definition object, populating Ids and update the bpmn and dmn instances
   *
   * @param definitionInstance {@link Definition}
   * @param realmId Company id
   * @return Updated {@link Definition} object
   */
  // The @Throttle annotation below will throttle both CREATE and UPDATE definition calls.
  // Since currently, this method is also used in the update definition flow.
  @Throttle(attribute = ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)
  @Throttle(attribute = ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)
  @Override
  public DefinitionInstance process(DefinitionInstance definitionInstance, String realmId)
      throws WorkflowGeneralException {
    long startTime = System.nanoTime();

    // unique id which is used for updating the id of different elements
    final String uniqueId =
        definitionInstance.getUuid() == null
            ? UUID.randomUUID().toString()
            : definitionInstance.getUuid();

    DefinitionId.DefinitionIdBuilder definitionIdBuilder =
        DefinitionId.builder().realmId(realmId).uniqueId(uniqueId);

    // Update the start process element's id in the bpmn
    updateProcessElementId(definitionInstance.getBpmnModelInstance(), definitionIdBuilder.build());
    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());
    // Process workflow steps from the definition
    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                processWorkflowStep(
                    workflowStep,
                    definitionInstance.getBpmnModelInstance(),
                    definitionInstance.getDmnModelInstanceList(),
                    definitionIdBuilder.build(),
                    definitionInstance.getDefinition(),
                    useFeelExpr));

    updateNonUserConfiguredSectionsWithLocalisedContent(definitionInstance);

    updateIdForElements(definitionInstance.getBpmnModelInstance(),
        definitionInstance.getDmnModelInstanceList(), definitionIdBuilder.build());

    // post-processing of data.
    postProcess(definitionInstance);

    // Extracting the respective placeholder extractor for Custom/Precanned: required for creating
    // single-definition
    PlaceholderExtractor placeHolderExtractor =
        placeholderExtractorProvider.getPlaceholderExtractor(definitionInstance);
    // Extracting the placeholder values from the definition and setting it in the definition
    // instance
    if (Objects.nonNull(placeHolderExtractor)) {
      definitionInstance.setPlaceholderValue(
          placeHolderExtractor.extractPlaceholderValue(definitionInstance));
    }

    // set-recurrence info if applicable. In case template doesn't have the recurrenceKey, it does
    // nothing
    setRecurringInfo(definitionInstance);

    // Get the lookupKeys from definition object if present and map appropriately to store in definitionInstance
    definitionInstance.setLookupKeys(LookupKeysMapper.getMapOfLookupKeysFromList(definitionInstance.getDefinition().getLookupKeys()).orElse(Collections.emptyMap()));

    WorkflowLogger.logInfo(
        "Total Processing Time=%s Category=%s",
        (System.nanoTime() - startTime),
        definitionInstance.getTemplateDetails().getTemplateCategory());

    return definitionInstance;
  }

  // Update the process element's id in the bpmn. The id convention is
  // {{originalId}}_{{realmId}}_{{uniqueId}}
  private void updateProcessElementId(
      BpmnModelInstance bpmnModelInstance, DefinitionId definitionId) {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    // There would be only one process
    Optional<Process> processOptional = processes.stream().findFirst();
    // update the name of the definition
    processOptional.ifPresent(
        process ->
            process.setId(definitionId.toBuilder().entityId(process.getId()).build().toString()));
  }

  /**
   * @param workflowStep {@link WorkflowStep}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnModelInstanceList DmnLIst
   * @param definitionId {@link DefinitionId}
   * @param definition {@link Definition}
   */
  private void processWorkflowStep(
      WorkflowStep workflowStep,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstanceList,
      DefinitionId definitionId,
      Definition definition,
      boolean useFeelExpr) {
    processTrigger(workflowStep.getTrigger(), bpmnModelInstance, definitionId, definition);

    // process actions
    List<WorkflowStep.ActionMapper> actionMappers = workflowStep.getActions();
    if (Objects.nonNull(actionMappers)) {
      WorkflowLogger.logInfo(
          "Skip processing empty Action Mapper for Template=%s, workflowStep=%s, and realm=%s",
          ObjectUtils.isEmpty(definition.getTemplate()) ? null : definition.getTemplate().getId(),
          workflowStep.getId(),
          definitionId.getRealmId());
      actionMappers.forEach(
          actionMapper -> processAction(actionMapper, bpmnModelInstance, definitionId, definition));
    }
    // process conditions
    String workflowStepId = workflowStep.getId().getLocalId();
    processWorkflowCondition(
        workflowStep.getWorkflowStepCondition(),
        bpmnModelInstance,
        dmnModelInstanceList,
        definitionId,
        definition,
        useFeelExpr);

    workflowStep.setId(
        workflowStep
            .getId()
            .setLocalId(definitionId.toBuilder().entityId(workflowStepId).build().toString()));
  }

  /**
   * @param workflowStepCondition {@link WorkflowStepCondition}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param dmnModelInstanceList list of dmn {@link DmnModelInstance}
   * @param definitionId {@link DefinitionId}
   * @param definition {@link Definition}
   */
  private void processWorkflowCondition(
      WorkflowStepCondition workflowStepCondition,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstanceList,
      DefinitionId definitionId,
      Definition definition,
      boolean useFeelExpr) {
    if (ObjectUtils.isEmpty(workflowStepCondition)) {
      WorkflowLogger.logInfo(
          "Skip processing empty Workflow condition for Template={}, and realm={}",
          ObjectUtils.isEmpty(definition.getTemplate()) ? null : definition.getTemplate().getId(),
          definitionId.getRealmId());
      return;
    }
    // get conditional element
    GlobalId<?> globalConditionId = workflowStepCondition.getId();
    BaseElement conditionElement =
        bpmnModelInstance.getModelElementById(globalConditionId.getLocalId());
    conditionalElementFactory
        .getHandler(conditionElement)
        .process(
            workflowStepCondition,
            bpmnModelInstance,
            dmnModelInstanceList,
            definitionId,
            definition,
            useFeelExpr);
    workflowStepCondition.setId(
        globalConditionId.setLocalId(
            definitionId.toBuilder().entityId(globalConditionId.getLocalId()).build().toString()));
  }

  /**
   * @param trigger {@link Trigger}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param definitionId {@link DefinitionId}
   * @param definition {@link Definition}
   */
  private void processTrigger(
      Trigger trigger,
      BpmnModelInstance bpmnModelInstance,
      DefinitionId definitionId,
      Definition definition) {
    if (ObjectUtils.isEmpty(trigger)) {
      WorkflowLogger.logInfo(
          "Skip processing empty Trigger for templateId=%s , realmId=%s",
          ObjectUtils.isEmpty(definition.getTemplate()) ? null : definition.getTemplate().getId(),
          definitionId.getRealmId());
      return;
    }
    GlobalId<?> triggerGlobalId = trigger.getId();
    BaseElement element = bpmnModelInstance.getModelElementById(trigger.getId().getLocalId());

    // setting parameter details if any
    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap =
        updateParametersWithUserInputs(
            element,
            inputOutputContent ->
                processParameterDetails(
                    inputOutputContent, trigger.getParameters()));

    if (element instanceof BoundaryEvent) {
      // for boundary event update the timerDefinition expression
      Collection<TimerEventDefinition> timerEventDefinitions =
          element.getChildElementsByType(TimerEventDefinition.class);
      // there would be only one TimerDefinition
      Optional<TimerEventDefinition> timerEventDefinitionOptional =
          timerEventDefinitions.stream().findFirst();
      timerEventDefinitionOptional.ifPresent(
          timerEventDefinition -> {
            Optional<String> waitTimeOptional =
                parameterDetailsMap.get(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .getFieldValue().stream()
                    .findFirst();
            waitTimeOptional.ifPresent(
                waitTime ->
                    timerEventDefinition
                        .getTimeDuration()
                        .setTextContent(MessageFormat.format("P{0}D", waitTime)));
          });
    }
    postProcessTrigger(trigger, bpmnModelInstance);

    trigger.setId(
        triggerGlobalId.setLocalId(
            definitionId.toBuilder().entityId(triggerGlobalId.getLocalId()).build().toString()));
  }

  /**
   * Any processing needed post triggers are set.
   *
   * @param trigger
   * @param bpmnModelInstance
   */
  protected void postProcessTrigger(Trigger trigger, BpmnModelInstance bpmnModelInstance) {
    // Do nothing. Used by custom  workflows
  }

  /**
   * This method process the action which updates the parameters for action and update action object
   *
   * @param actionMapper {@link ActionMapper}
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param definitionId {@link DefinitionId}
   * @param definition : {@link Definition} Template will be null for Byo Templates and non-null for
   */
  private void processAction(
      ActionMapper actionMapper,
      BpmnModelInstance bpmnModelInstance,
      DefinitionId definitionId,
      Definition definition) {
    Action action = actionMapper.getAction();
    if (Objects.nonNull(action)) {
      GlobalId<?> globalActionId = action.getId();
      // updateParameters for action
      String actionId = globalActionId.getLocalId();
      updateActionParametersWithUserInputs(
          bpmnModelInstance.getModelElementById(actionId),actionMapper,definition.getRecordType());
      postProcessAction(actionMapper, bpmnModelInstance,definition);
      action.setId(
          globalActionId.setLocalId(
              definitionId.toBuilder().entityId(actionId).build().toString()));
    }
  }

  /**
   * Any processing needed post action are set.
   *  @param actionMapper
   * @param bpmnModelInstance
   * @param definition
   */
  protected void postProcessAction(ActionMapper actionMapper, BpmnModelInstance bpmnModelInstance, Definition definition) {
    // Do nothing
  }

  /**
   * Adds/updates the user provided values in parametersDetails in the BPMN xml
   * Here the function to process parameter details is passed
   * @param baseElement {@link BaseElement}
   * @param actionMapper {@link ActionMapper}
   * @return
   */
  protected Map<String, HandlerDetails.ParameterDetails> updateActionParametersWithUserInputs(
      BaseElement baseElement, ActionMapper actionMapper, String recordType) {
    return updateParametersWithUserInputs(
        baseElement,
        inputOutputContent ->
            processParameterDetails(
                inputOutputContent, actionMapper.getAction().getParameters()));
  }

  /**
   * Adds/updates the user provided values in parametersDetails in the BPMN xml
   *
   * @param baseElement {@link BaseElement}
   * @param processParameterDetailsFn processParameterDetails function which dynamically gets passed
   * @return Updated map
   */
  protected Map<String, HandlerDetails.ParameterDetails> updateParametersWithUserInputs(
      BaseElement baseElement, Function<String, Map<String, ParameterDetails>> processParameterDetailsFn) {
    AtomicReference<Map<String, ParameterDetails>> parameterDetailsMap =
        new AtomicReference<>(new HashMap<>());

    Optional<CamundaInputParameter> camundaInputParameterOptional =
        BpmnProcessorUtil.getCamundaInputParam(
            baseElement, WorkFlowVariables.PARAMETERS_KEY.getName());
    camundaInputParameterOptional.ifPresent(
        camundaInputParameter -> {
          String inputOutputContent = camundaInputParameter.getTextContent();
          parameterDetailsMap.set(
                  removeParametersWithEmptyFieldValues(processParameterDetailsFn.apply(inputOutputContent)));
          String jsonString = ObjectConverter.toJson(parameterDetailsMap);
          camundaInputParameter.setTextContent(jsonString);
        });

    Optional<CamundaProperty> camundaPropertyOptional =
        BpmnProcessorUtil.getCamundaProperty(
            baseElement, WorkFlowVariables.PARAMETERS_KEY.getName());
    camundaPropertyOptional.ifPresent(
        camundaProperty -> {
          String inputOutputContent = camundaProperty.getCamundaValue();
          parameterDetailsMap.set(
                  removeParametersWithEmptyFieldValues(processParameterDetailsFn.apply(inputOutputContent)));
          String jsonString = ObjectConverter.toJson(parameterDetailsMap);
          camundaProperty.setCamundaValue(jsonString);
        });

    return parameterDetailsMap.get();
  }

  /**
   * Removes all non process variable parameters which have empty/null field values from {@link ParameterDetails} list
   * @param parameterDetailsMap - map for parameter name to {@link ParameterDetails}
   * @return - {@link ParameterDetails} map without empty/null non process variable parameters
   */
  public Map<String, ParameterDetails> removeParametersWithEmptyFieldValues(Map<String, ParameterDetails> parameterDetailsMap) {

    Predicate<Map.Entry<String, ParameterDetails>> processVariableFilter =
            parameterDetailsEntry ->
                    Objects.nonNull(parameterDetailsEntry.getValue().getValueType()) &&
                            parameterDetailsEntry.getValue().getValueType()
                                    .equals(ParameterDetailsValueType.PROCESS_VARIABLE);

    Predicate<Map.Entry<String, ParameterDetails>> emptyFieldValueFilter =
            parameterDetailsEntry ->
                    Objects.isNull(parameterDetailsEntry.getValue().getFieldValue()) ||
                            parameterDetailsEntry.getValue().getFieldValue()
                                    .stream().anyMatch(Objects::isNull) ||
                            parameterDetailsEntry.getValue().getFieldValue()
                                    .stream().allMatch(String::isEmpty);

    return parameterDetailsMap.entrySet().stream()
            .filter(processVariableFilter.or(Predicate.not(emptyFieldValueFilter)))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  /**
   * This method iterates through parameter list provided in the workflow step and fills in field
   * value defined in parameterDetails defined in BPMN
   *
   * @param parameterContent - Defined in the BPMN
   * @param parameters - Provided in the request
   * @return
   */
  private Map<String, ParameterDetails> processParameterDetails(
      String parameterContent, List<InputParameter> parameters) {
    Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
    if (StringUtils.isEmpty(parameterContent)) {
      return parameterDetailsMap;
    }

    parameterDetailsMap.putAll(
        Objects.requireNonNull(
            ObjectConverter.fromJson(
                parameterContent, new TypeReference<Map<String, ParameterDetails>>() {})));
    // replace from parameters, the fieldValue
    parameters.forEach(
        parameter -> {
          if (parameterDetailsMap.containsKey(parameter.getParameterName())) {
            // got old parameter detail object
            ParameterDetails parameterDetails =
                parameterDetailsMap.get(parameter.getParameterName());
            // replace fieldValue
            parameterDetails.setFieldValue(parameter.getFieldValues());
          }
        });
    // map is updated
    return parameterDetailsMap;
  }

  private void updateIdForElements(
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstanceList,
      DefinitionId definitionId) {
    // replace id for each element in bpmn
    bpmnModelInstance
        .getModelElementsByType(FlowElement.class)
        .forEach(
            flowElement -> {
              String updatedId =
                  definitionId.toBuilder().entityId(flowElement.getId()).build().toString();
              if (flowElement instanceof BusinessRuleTask) {
                // Update decision Ref value for DMN task elements. We are going to update the dmn
                // id with same value
                BusinessRuleTask businessRuleTask = (BusinessRuleTask) flowElement;
                businessRuleTask.setCamundaDecisionRef(updatedId);
              }

              if (flowElement instanceof StartEvent) {
                // Update step details property with new id
                updateStepDetails((StartEvent) flowElement, definitionId);
                // Update recurrence details property with new  id
                updateRecurrenceElementDetails((StartEvent) flowElement, definitionId);
              }

              flowElement.setId(updatedId);
            });

    // Update id of Decision element in DMNs
    dmnModelInstanceList.forEach(
        dmnModelInstance ->
            dmnModelInstance
                .getModelElementsByType(DmnElement.class)
                .forEach(
                    dmnElement -> {
                      // setting id only for decision type and ignore other id's
                      if (dmnElement instanceof Decision) {
                        dmnElement.setId(
                            definitionId
                                .toBuilder()
                                .entityId(dmnElement.getId())
                                .build()
                                .toString());
                        return;
                      }
                    }));
  }

  /**
   * The method updates recurrence details information in startEvent element with new IDs created
   * during definition time
   *
   * @param flowElement : Start Event Element
   * @param definitionId : definition id
   */
  private void updateRecurrenceElementDetails(StartEvent flowElement, DefinitionId definitionId) {
    Optional<Map<String, String>> optionalRecurrenceDetails =
        SchemaDecoder.getRecurrenceElementDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(flowElement));

    if (optionalRecurrenceDetails.isPresent()) {
      // Map of step name as key and interim recurrence details as value
      Map<String, String> updatedRecurrenceDetails = new LinkedHashMap<>();

      // Modify Recurrence details with extended id
      optionalRecurrenceDetails
          .get()
          .forEach(
              (recurrenceDetailKey, elementId) ->
                  updatedRecurrenceDetails.put(
                      recurrenceDetailKey,
                      definitionId.toBuilder().entityId(elementId).build().toString()));

      // Get recurrenceDetails property from extensions and update its value
      List<CamundaProperties> propertiesList =
          flowElement
              .getExtensionElements()
              .getElementsQuery()
              .filterByType(CamundaProperties.class)
              .list();
      if (ObjectUtils.isNotEmpty(propertiesList)) {
        CamundaProperties propertiesLists = propertiesList.get(0);
        for (CamundaProperty camundaProperty : propertiesLists.getCamundaProperties()) {
          if (WorkFlowVariables.RECURRENCE_DETAILS_KEY
              .getName()
              .equalsIgnoreCase(camundaProperty.getCamundaName())) {
            camundaProperty.setCamundaValue(ObjectConverter.toJson(updatedRecurrenceDetails));
            break;
          }
        }
      }
    }
  }

  /**
   * * The method updates step details information in startEvent element with new IDs created during
   * definition time
   *
   * @param flowElement : Start Event Element
   * @param definitionId : definition id
   */
  private void updateStepDetails(StartEvent flowElement, DefinitionId definitionId) {
    Optional<Map<String, Set<String>>> optionalStepDetails =
        SchemaDecoder.getStepDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(flowElement));
    if (optionalStepDetails.isPresent()) {
      // Map of step name as key and interim step details as value
      Map<String, Set<String>> updatedStepDetails = new LinkedHashMap<>();

      // Modify step details with extended id
      optionalStepDetails
          .get()
          .forEach(
              (stepName, interimSteps) ->
                  updatedStepDetails.put(
                      definitionId.toBuilder().entityId(stepName).build().toString(),
                      interimSteps.stream()
                          .map(t -> definitionId.toBuilder().entityId(t).build().toString())
                          .collect(Collectors.toSet())));

      // Get stepDetail property from extensions and update its value
      List<CamundaProperties> propertiesList =
          flowElement
              .getExtensionElements()
              .getElementsQuery()
              .filterByType(CamundaProperties.class)
              .list();
      if (ObjectUtils.isNotEmpty(propertiesList)) {
        CamundaProperties propertiesLists = propertiesList.get(0);
        for (CamundaProperty camundaProperty : propertiesLists.getCamundaProperties()) {
          if (camundaProperty
              .getCamundaName()
              .equalsIgnoreCase(WorkFlowVariables.STEP_DETAILS_KEY.getName())) {
            camundaProperty.setCamundaValue(ObjectConverter.toJson(updatedStepDetails));
            break;
          }
        }
      }
    }
  }

  protected void setRecurringInfo(DefinitionInstance definitionInstance){
    // Do nothing. Used by custom  workflows
  }

  /**
   * This method find out all the Tasks {@link Task } for BpmnModelInstance which are not required
   * By UI. By using Task get all the input parameters list. Each input parameter translated by
   * calling translateInputParameterFieldValues method.
   *
   * @param definitionInstance {@link DefinitionInstance}
   */
  private void updateNonUserConfiguredSectionsWithLocalisedContent(
      DefinitionInstance definitionInstance) {
    List<Task> tasks = getAllNonUserConfiguredTasks(definitionInstance.getBpmnModelInstance());

    tasks.forEach(
        task -> {
          List<InputParameter> inputParameters = getInputParametersFromBaseElement(task);
          inputParameters.forEach(
              inputParameter ->
                  translateInputParameterFieldValues(
                      inputParameter,
                      Optional.ofNullable(definitionInstance.getDefinition().getRecordType())
                          .map(RecordType::fromType)
                          .map(RecordType::getDisplayValue)
                          .orElse(null)));
          updateParametersWithUserInputs(
              task,
              inputOutputContent -> processParameterDetails(inputOutputContent, inputParameters));
        });
  }

  /**
   * This method get the parameterDetails from the baseElement {@link BaseElement}. If
   * parameterDetails exists then get the text content and parse it into map of parameter name and
   * ParameterDetails {@link ParameterDetails}. Using parameterDetailsMap converts into list of
   * InputParameter with name and fieldValues.
   *
   * @param baseElement {@link BaseElement}
   * @return returns the list of input parameters
   */
  private List<InputParameter> getInputParametersFromBaseElement(BaseElement baseElement) {
    Optional<CamundaInputParameter> camundaInputParameterOptional =
        BpmnProcessorUtil.getCamundaInputParam(
            baseElement, WorkFlowVariables.PARAMETERS_KEY.getName());
    List<InputParameter> inputParameters = new ArrayList<>();
    camundaInputParameterOptional.ifPresent(
        camundaInputParameter -> {
          String inputOutputContent = camundaInputParameter.getTextContent();
          if (StringUtils.isNotBlank(inputOutputContent)) {
            Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
            parameterDetailsMap.putAll(
                Objects.requireNonNull(
                    ObjectConverter.fromJson(
                        inputOutputContent,
                        new TypeReference<Map<String, ParameterDetails>>() {})));

            for (Map.Entry<String, ParameterDetails> parameterDetailsEntry :
                parameterDetailsMap.entrySet()) {
              InputParameter inputParameter = new InputParameter();
              inputParameter.setParameterName(parameterDetailsEntry.getKey());
              inputParameter.setFieldValues(parameterDetailsEntry.getValue().getFieldValue());
              inputParameters.add(inputParameter);
            }
          }
        });

    return Collections.unmodifiableList(inputParameters);
  }

  /**
   * This method translate all the field values of input parameter into localised string using
   * translation service {@link TranslationService}.
   *
   * @param inputParameter {@link InputParameter}
   * @param recordDisplayValue recordId This parameter used by translation service to localise the token
   *     parameters.
   */
  private void translateInputParameterFieldValues(InputParameter inputParameter, String recordDisplayValue) {
    List<String> fieldValues = inputParameter.getFieldValues();
    List<String> newFieldValues = new ArrayList<>();
    String locale = wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE);
    if (!CollectionUtils.isEmpty(fieldValues)) {
      fieldValues.forEach(
          fieldValue -> {
            newFieldValues.add(
                translationService.getFormattedString(fieldValue, locale, recordDisplayValue));
          });
      inputParameter.setFieldValues(newFieldValues);
    }
  }

  /**
   * This method returns all the Task which are non user configured.
   *
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @return List of all Task {@link Task} which are non user configure.
   */
  private List<Task> getAllNonUserConfiguredTasks(BpmnModelInstance bpmnModelInstance) {
    Definitions def = bpmnModelInstance.getDefinitions();
    Collection<Task> tasks = def.getModelInstance().getModelElementsByType(Task.class);
    return tasks.stream()
        .filter(task -> task.getExtensionElements() != null && !isUserConfiguredTask(task))
        .collect(Collectors.toList());
  }

  /**
   * This method find out the CamundaInputParameter for TASK_DETAILS_KEY and if this key present
   * then convert the content into {@link TaskDetails} class. Using required field in TaskDetails find
   * out isUserConfigured or not.
   *
   * @return true if isUserConfigured and else false.
   */
  private boolean isUserConfiguredTask(Task task) {
    Optional<CamundaInputParameter> camundaInputParameterOptional =
        BpmnProcessorUtil.getCamundaInputParam(task, WorkFlowVariables.TASK_DETAILS_KEY.getName());

    AtomicBoolean isUserConfiguredTask = new AtomicBoolean(false);
    camundaInputParameterOptional.ifPresent(
        camundaInputParameter -> {
          String inputOutputContent = camundaInputParameter.getTextContent();
          if (StringUtils.isNotBlank(inputOutputContent)) {
            TaskDetails taskDetails =
                ObjectConverter.fromJson(inputOutputContent, new TypeReference<TaskDetails>() {});
            isUserConfiguredTask.set(BooleanUtils.toBoolean(taskDetails.getRequired()));
          }
        });

    return isUserConfiguredTask.get();
  }
}
