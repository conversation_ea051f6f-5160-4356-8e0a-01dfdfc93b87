package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;

/**
 * Author: <PERSON><PERSON> Date: 15/01/20 Description: Help to do crud operation on Definition. Each
 * operation need to implement this and provide its operation
 */
public interface DefinitionCrudHandler<T> {

  T process(T definition, String realmId) throws WorkflowGeneralException;

  // This method can be utilized in the child classes for anything which is needed to be done post processing the definition.
  // For example, we need to do rule transformation and building process variables metadata for custom workflows
  default void postProcess(DefinitionInstance definitionInstance) {
    // Do nothing
  }
}
