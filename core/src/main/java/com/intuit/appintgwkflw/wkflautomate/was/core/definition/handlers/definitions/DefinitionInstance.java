package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.intuit.v4.workflows.WorkflowStep;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
/**
 * Author: <PERSON><PERSON> Date: 20/01/20 Description: Wrapper class for avoiding fetching
 * bpmnModelInstance, dmnModelInstanceList etc
 */
@Getter
@Setter
@RequiredArgsConstructor
@NoArgsConstructor(force = true)
public class DefinitionInstance {
  private final Definition definition;
  private final BpmnModelInstance bpmnModelInstance;
  private final List<DmnModelInstance> dmnModelInstanceList;
  private final TemplateDetails templateDetails;
  private DefinitionDetails definitionDetails;
  private List<ProcessDetails> processDetails;
  private List<DefinitionDetails> definitionDetailsList;
  private Map<String, WorkflowStep> workflowStepMap;
  private Map<String, ActivityInstance> activityInstanceMap = new HashMap<>();
  // Denotes the workflow id of given definition
  private String workflowId;
  private boolean attributeUpdate = true;
  private String uuid;
  private Map<String, Object> placeholderValue;
  private Map<String, String> lookupKeys;
  //This field is set in case of definition migration by batch job, else it is null
  private Long modifiedBy;
}