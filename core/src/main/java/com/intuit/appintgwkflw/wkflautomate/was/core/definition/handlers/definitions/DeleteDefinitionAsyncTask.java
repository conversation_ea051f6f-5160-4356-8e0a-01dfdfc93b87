package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.DELETE_DEFINITION_ASYNC_TASK;

@AllArgsConstructor
public class DeleteDefinitionAsyncTask implements Task {

    private DefinitionServiceHelper definitionServiceHelper;
    private DataStoreDeleteTaskService dataStoreDeleteTaskService;
    private MetricLogger metricLogger;

    @Override
    public State execute(State state) {
        try {
            String ownerId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
            EventingLoggerUtil.logInfo(
                    "Starting delete stale definitions for ownerId=%s",
                    this.getClass().getSimpleName(), ownerId);

            List<DefinitionDetails> defintionDetailsList = state.getValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST);
            dataStoreDeleteTaskService.deleteDefinitions(defintionDetailsList);

            definitionServiceHelper.deleteAllByParentIdIn(defintionDetailsList.stream()
                    .map(DefinitionDetails::getDefinitionId)
                    .collect(Collectors.toList()));

            EventingLoggerUtil.logInfo(
                    "Completed delete stale definitions for ownerId=%s",
                    this.getClass().getSimpleName(), ownerId);
        }
        catch (Exception exception) {
            EventingLoggerUtil.logError("Exception occurred while deleting the stale definitions, exception=%s",
                    this.getClass().getSimpleName(), exception.getMessage());
            metricLogger.logErrorMetric(DELETE_DEFINITION_ASYNC_TASK, Type.APPLICATION_METRIC, exception);
        }

        return state;
    }
}
