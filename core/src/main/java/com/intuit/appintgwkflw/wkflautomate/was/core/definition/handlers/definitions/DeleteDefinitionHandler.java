package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DELETE_IN_PROGRESS;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.CommandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.v4.workflows.Definition;
import java.util.List;
import java.util.stream.Collectors;
import com.intuit.async.execution.request.State;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/** Author: Nitin Gupta Date: 15/01/20 Description: */
@Component(WorkflowBeansConstants.DELETE_DEFINITION_HANDLER)
@AllArgsConstructor
public class DeleteDefinitionHandler implements DefinitionCrudHandler<Definition> {

  private DefinitionServiceHelper definitionServiceHelper;

  private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  private MetricLogger metricLogger;

  @Override
  public Definition process(Definition definition, String ownerId) throws WorkflowGeneralException {

    CommandHelper.validateDefinition(definition);

    String definitionId = definition.getId().getLocalId();

    /**
     * Find Definition details and throw error if definition not found for given definition id and
     * realm id.
     */
    final DefinitionDetails definitionDetails =
        definitionServiceHelper.findByDefinitionId(definitionId, ownerId);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Delete definition invoked for definitionId=%s recordType=%s", definitionId,
                    definitionDetails.getRecordType())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));

    List<DefinitionDetails> definitionDetailsList;
    //TODO: We can clean up this code later after looking into prod db
    if (definitionDetails.getWorkflowId() == null) {
      definitionDetailsList = definitionServiceHelper.getDefinitionListByDefinitionKeyAndOwnerId(
          definitionDetails.getDefinitionKey(), Long.valueOf(ownerId));
    } else {
      definitionDetailsList = definitionServiceHelper.getDefinitionListByWorkflowId(
          definitionDetails.getWorkflowId(), Long.valueOf(ownerId));
    }

    if(CustomWorkflowType.APPROVAL.getTemplateName().equals(definitionDetails.getTemplateDetails().getTemplateName())) {
      definitionDetailsList.addAll(
          definitionServiceHelper.findStaleDefinitionForPrecannedTemplateAndRecordType(
              definitionDetails.getOwnerId().toString(),
              definitionDetails.getTemplateDetails().getTemplateName(),
              definitionDetails.getRecordType()
          )
      );
    }

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetailsList(definitionDetailsList);
    definitionInstance.setDefinitionDetails(definitionDetails);
    //Execute task to disable
    DefinitionCommands.getCommand(CrudOperation.DELETE.name())
        .execute(definitionInstance, ownerId);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Performing delete operation in database and camunda for definitionId=%s",
                    definitionId)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));

    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(definitionDetailsList, InternalStatus.MARKED_FOR_DELETE);

    definitionInstance.getDefinition().setMessage(DELETE_IN_PROGRESS);

    int maxVersion = definitionDetailsList.stream()
            .mapToInt(DefinitionDetails::getVersion)
            .max().orElse(0);
    List<DefinitionDetails> staleDefintionDetailsList = definitionDetailsList.stream()
            .filter(currentDefinition -> currentDefinition.getVersion() < maxVersion)
            .collect(Collectors.toList());

    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST, staleDefintionDetailsList);
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, ownerId);
    new RxExecutionChain(state, new DeleteDefinitionAsyncTask(definitionServiceHelper, dataStoreDeleteTaskService, metricLogger)).executeAsync();

    return definition;
  }
}
