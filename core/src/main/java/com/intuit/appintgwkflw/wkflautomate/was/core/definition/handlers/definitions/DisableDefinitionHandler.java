package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.CommandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DISABLE_IN_PROGRESS;

@Component(WorkflowBeansConstants.DISABLE_DEFINITION_HANDLER)
@AllArgsConstructor
public class DisableDefinitionHandler implements DefinitionCrudHandler<DefinitionInstance> {

  private DefinitionServiceHelper definitionServiceHelper;

  private UpdateDefinitionStatusInDataStoreService updateDefinitionStatusInDataStoreService;

  /*
   * process the definition and do the following steps. <br>
   * 1. validates the definition details of the company.<br>
   * 2. Fetches the process details that are still active. <br>
   * 3. Update the status and internal status in definition table and internal status in process
   * table.<br>
   * 4. Execute disable command that internally closes the in flights process if any and disable the
   * definition from app-connect.<br>
   *
   * @param definitionInstance input definition details
   * @param ownerId input owner Id
   */
  @Override
  public DefinitionInstance process(DefinitionInstance definitionInstance, String ownerId)
      throws WorkflowGeneralException {

    CommandHelper.validateDefinitionInstance(definitionInstance);

    // if not details found throw exception
    DefinitionDetails definitionDetails =
        definitionServiceHelper.findByDefinitionId(
            definitionInstance.getDefinition().getId().getLocalId(), ownerId);

    logInfo(
        "Disable definition invoked for definitionId=%s recordType=%s",
        definitionInstance.getDefinition().getId().getLocalId(), definitionDetails.getRecordType());

    // This is done as the latest version has to be marked disabled and not the others.
    definitionInstance.setDefinitionDetails(definitionDetails);

    //Execute task to disable
    DefinitionCommands.getCommand(CrudOperation.DISABLED.name())
        .execute(definitionInstance, ownerId);

    logInfo(
        "updating status of definition to status=%s",
        definitionInstance.getDefinition().getStatus());

    // updating the status and internal_status in case of enable disable in process and definition
    // table
    updateDefinitionStatusInDataStoreService.updateStatusForDisabled(definitionInstance);

    definitionInstance.getDefinition().setMessage(DISABLE_IN_PROGRESS);
    return definitionInstance;
  }

  /**
   * logs the info message.
   *
   * @param message input message
   * @param workflowMessageArgs some formating args for message
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
  }
}
