package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEFINITION_ENABLED_SUCCESSFULLY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED;
import static com.intuit.v4.GlobalId.create;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.CommandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartRecurringProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerRecurringProcessHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component(WorkflowBeansConstants.ENABLE_DEFINITION_HANDLER)
@AllArgsConstructor
public class EnableDefinitionHandler implements DefinitionCrudHandler<DefinitionInstance> {

  private DefinitionServiceHelper definitionServiceHelper;

  private TemplateDetailsRepository templateDetailsRepository;

  private UpdateDefinitionStatusInDataStoreService updateDefinitionStatusInDataStoreService;

  private MetricLogger metricLogger;

  private BpmnProcessorImpl bpmnProcessor;

  private RunTimeService runTimeService;

  private WASContextHandler contextHandler;

  private ProviderHelper providerHelper;

  private EventScheduleHelper eventScheduleHelper;

  private TriggerRecurringProcessHelper triggerRecurringProcessHelper;

  /**
   * process the definition and do the following steps. <br> 1. validates the definition details of
   * the company.<br> 2. Update the status and internal status in definition table. <br> 3. Execute
   * enable command in app-connect for given definition<br>
   *
   * @param definitionInstance input definition details
   * @param ownerId            input owner id
   */
  @Override
  public DefinitionInstance process(DefinitionInstance definitionInstance, String ownerId) {

    CommandHelper.validate(definitionInstance);

    // Helper method to see if there exists an already enabled definition for the template and
    // realm. If there is then we will throw error when user is trying to enable the definitions.
    checkForExistingEnabledDefinitions(
        definitionInstance.getDefinition().getId(),
        definitionInstance.getDefinition().getTemplate().getId().getLocalId(),
        ownerId,
        definitionInstance.getDefinition().getRecordType());

    // if not details found throw exception
    DefinitionDetails definitionDetails =
        definitionServiceHelper.findByDefinitionId(
            definitionInstance.getDefinition().getId().getLocalId(), ownerId);

    logInfo(
        "Enable definition invoked for definitionId=%s recordType=%s",
        definitionInstance.getDefinition().getId().getLocalId(), definitionDetails.getRecordType());

    definitionInstance.setDefinitionDetails(definitionDetails);

    logInfo(
        "updating status of definition to status=%s",
        definitionInstance.getDefinition().getStatus());

    // updating the status to enabled and internal_status to null

    /* initiate enable command for app-connect and ESS in sync */
    DefinitionCommands.getCommand(CrudOperation.ENABLED.name())
        .execute(definitionInstance, ownerId);
    updateDefinitionStatusInDataStoreService.updateStatusForEnabled(definitionInstance);

    /**
     * remove the shouldStartRecurringProcess block post migration since the workflow will no longer
     * need recurring process TODO: https://jira.intuit.com/browse/QBOES-21483
     */
    if (shouldStartRecurringProcess(definitionDetails)) {
      startProcessWithRecurrenceDetails(definitionInstance, ownerId);
    }
    definitionInstance.getDefinition().setMessage(DEFINITION_ENABLED_SUCCESSFULLY);
    return definitionInstance;
  }

  private boolean shouldStartRecurringProcess(DefinitionDetails definitionDetails) {
    // TODO: For single definition, it should fetch from placeholder values.
    // https://jira.intuit.com/browse/QBOES-21032
    if (Objects.nonNull(definitionDetails.getDefinitionData())) {
      BpmnModelInstance modelInstance =
          Bpmn.readModelFromStream(new ByteArrayInputStream(definitionDetails.getDefinitionData()));
      FlowElement startEventElement = BpmnProcessorUtil.findStartEventElement(modelInstance);

      Optional<CamundaProperty> recurrenceElementOptional =
          BpmnProcessorUtil.getCamundaProperty(
              startEventElement, WorkFlowVariables.RECURRENCE_RULE_KEY.getName());

      // In case the workflow contains parameter recurrenceRule then it is a recurrence workflow.
      if (recurrenceElementOptional.isPresent()) {
        boolean isDefinitionFromOldTemplate =
            triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(
                definitionDetails);
        /**
         * irrespective of ESS event scheduling FF being enabled, a recurring process should be
         * triggered only for definitions that have no corresponding schedule data in
         * de_scheduler_details
         */
        return isDefinitionFromOldTemplate;
      }
    }
    return false;
  }

  /**
   * Helper method that prepares the Trigger Payload and Start the process for Definitions having
   * recurrence element(s).
   *
   * @param definitionInstance : {@link DefinitionInstance}
   * @param ownerId            : {@link String}
   */
  private void startProcessWithRecurrenceDetails(
      DefinitionInstance definitionInstance, String ownerId) {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, ownerId);
    inputRequest.addValue(
        AsyncTaskConstants.DEFINITION_ID_KEY,
        definitionInstance.getDefinitionDetails().getDefinitionId());
    TemplateDetails templateDetails =
        definitionInstance.getDefinitionDetails().getTemplateDetails();
    TransactionEntity transactionEntity =
        TriggerUtil.prepareTransactionEntity(
            templateDetails,
            definitionInstance.getDefinitionDetails().getRecordType(),
            ENTITY_CHANGE_TYPE_CREATED,
            definitionInstance.getDefinitionDetails().getWorkflowId(),
            getRulesForDefinition(definitionInstance.getDefinitionDetails(), ownerId),
            contextHandler, null);

    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);
    RxExecutionChain rxExecutionChain = new RxExecutionChain(inputRequest);
    rxExecutionChain.next(new StartRecurringProcessTask(metricLogger, runTimeService)).execute();

    // Rollback Enable definition in case there is an exception thrown while starting the process.
    if (Objects.nonNull(
        inputRequest.getValue(AsyncTaskConstants.RECURRENCE_START_PROCESS_TASK_FAILURE))) {
      WorkflowLogger.logError(
          "Exception occurred while performing enable for definitionId=%s",
          definitionInstance.getDefinitionDetails().getDefinitionId());
      // Setting status as Disabled and rollback it in the database as disabled.
      definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
      updateDefinitionStatusInDataStoreService.updateStatusForDisabled(definitionInstance);
      WorkflowGeneralException workflowException =
          inputRequest.getValue(AsyncTaskConstants.RECURRENCE_START_PROCESS_EXCEPTION);
      throw new WorkflowGeneralException(
          WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED,
          workflowException,
          definitionInstance.getDefinition().getId().getLocalId());
    }
  }

  /**
   * Helper method that prepares and return List<{@link com.intuit.v4.workflows.RuleLine.Rule}>
   * object from the stored workflow definition.
   *
   * @param definitionDetails : {@link DefinitionDetails}
   * @param ownerId
   */
  private List<RuleLine.Rule> getRulesForDefinition(
      DefinitionDetails definitionDetails, String ownerId) {
    Optional<List<DefinitionDetails>> definitions =
        definitionServiceHelper.findByParentId(definitionDetails.getDefinitionId());
    if (definitions.isPresent()) {
      // As of now each bpmn has only single dmn associated.
      DefinitionDetails dmnDefinitions = definitions.get().get(0);
      // Setting RecordType from Definitions
      definitionDetails.getTemplateDetails().setRecordType(definitionDetails.getRecordType());
      // generate DefinitionInstance object from bpmn and dmn model instances
      DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
          Bpmn.readModelFromStream(
              new ByteArrayInputStream(definitionDetails.getDefinitionData())),
          Collections.singletonList(
              Dmn.readModelFromStream(
                  new ByteArrayInputStream(dmnDefinitions.getDefinitionData()))),
          definitionDetails.getTemplateDetails());

      try {
        com.intuit.v4.workflows.Template template =
            (com.intuit.v4.workflows.Template)
                bpmnProcessor.processBpmn(
                    definitionInstance,
                    create(
                        contextHandler.get(WASContextEnums.INTUIT_REALMID),
                        WorkflowConstants.TEMPLATE_TYPE_ID,
                        definitionInstance.getTemplateDetails().getId()),
                    true);

        return template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getRuleLines()
            .get(0)
            .getRules();
      } catch (Exception ex) {
        WorkflowLogger.logError(
            ex,
            "Error in fetching definitionDetails for definitionId=%s and ownerId=%s",
            definitionDetails.getDefinitionId(),
            ownerId);
        throw new WorkflowGeneralException(
            WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED,
            ex,
            definitionDetails.getDefinitionId());
      }
    }
    return Collections.emptyList();
  }

  /**
   * This is a helper method which aims to check if there exists an already enabled Definition for a
   * Template withing a Realm/Company.
   *
   * @param definitionId GlobalId of Input definition
   * @param templateId   : Template Details
   * @param ownerId      : Realm id
   * @throws WorkflowGeneralException if Enabled Definition already exists for that template
   */
  private void checkForExistingEnabledDefinitions(
      GlobalId<?> definitionId, String templateId, String ownerId, String recordType) {
    // allow enable for template with multiple definitions allowed
    TemplateDetails templateDetails =
        templateDetailsRepository
            .findById(templateId)
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST));
    List<DefinitionDetails> definitionDetails;
    WorkflowVerfiy.verify(Objects.isNull(recordType), WorkflowError.INPUT_INVALID,
        WASContextEnums.RECORD_TYPE.getValue());
    try {
      providerHelper.isMultipleDefinitionAllowed(definitionId, templateDetails, ownerId, recordType,
          true);
    } catch (WorkflowGeneralException e) {
      WorkflowLogger.logError("Enabled Definition Already exists , error=%s", e);
      throw new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_ALREADY_EXISTS);
    }

    if (templateDetails.getAllowMultipleDefinitions()) {
      //Todo: recordType check for invoice to be removed post beta
      if (recordType.equals(RecordType.INVOICE.getRecordType())
          && templateDetails.getTemplateCategory().equals(TemplateCategory.CUSTOM.name())) {
        // If current definition is custom, check if precanned definition is present for same owner
        // and recordtype and action
        definitionDetails = definitionServiceHelper.findEnabledDefinitionForPrecannedTemplateAndRecordType(
            ownerId, templateDetails.getTemplateName(), RecordType.fromType(recordType));
        WorkflowVerfiy.verify(
            !CollectionUtils.isEmpty(definitionDetails),
            WorkflowError.ENABLED_DEFINITION_ALREADY_EXISTS,
            definitionDetails.stream()
                .map(DefinitionDetails::getDefinitionName)
                .collect(Collectors.toList()));

      }
      return;
    }
  }

  /**
   * logs the info message.
   *
   * @param message             input message
   * @param workflowMessageArgs some formating args for message
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_ENABLE_DEFINITION));
  }
}
