package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WORKFLOW_STEP_NEXTS;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> PlaceHolder Extractor for RuleLines.
 */
@Component
@AllArgsConstructor
public class MultiConditionPlaceholderExtractor {

  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * Extract the DMN Rule lines and store it in Dmn placeholder values
   *
   * @param definitionInstance
   * @param workflowStepId
   * @param pathResults
   */
  public void extractPlaceholderValue(DefinitionInstance definitionInstance,
      String workflowStepId, Pair<String, String> pathResults) {
    Map<String, Object> conditionPlaceholderValue = (Map<String, Object>) definitionInstance
        .getPlaceholderValue().getOrDefault(DMN_PLACEHOLDER_VALUES, new HashMap<>());

    Map<String, WorkflowStep> workflowStepMap = definitionInstance.getWorkflowStepMap();
    Map<String, Object> currentStepPlaceholder = new HashMap<>();
    WorkflowStep currentWorkflowStep = workflowStepMap.get(workflowStepId);

    currentStepPlaceholder.put(WorkflowConstants.RULE_LINE_VARIABLES,
        CustomWorkflowUtil.getRulesOfCurrentStep(currentWorkflowStep));

    String yesPathStepId = null;
    String noPathStepId = null;
    String workflowStepLocalId = GlobalId.from(workflowStepId).getLocalId();
    for (WorkflowStep.StepNext next : currentWorkflowStep.getNext()) {
      String nextWorkflowStepLocalId = GlobalId.from(next.getWorkflowStepId()).getLocalId();
      yesPathStepId =
          MultiStepUtil.isYesLabel(next.getLabel()) ? nextWorkflowStepLocalId : yesPathStepId;
      noPathStepId =
          MultiStepUtil.isNoLabel(next.getLabel()) ? nextWorkflowStepLocalId : noPathStepId;
    }

    Map<String, String> nexts = new HashMap<>();
    nexts.put(WorkflowConstants.YES_RULE,
        StringUtils.isNumeric(pathResults.getValue0()) ? yesPathStepId : pathResults.getValue0());
    nexts.put(WorkflowConstants.NO_RULE,
        StringUtils.isNumeric(pathResults.getValue1()) ? noPathStepId : pathResults.getValue1());
    currentStepPlaceholder.put(WORKFLOW_STEP_NEXTS, nexts);
    conditionPlaceholderValue.put(workflowStepLocalId, currentStepPlaceholder);
    definitionInstance.getPlaceholderValue()
        .putIfAbsent(DMN_PLACEHOLDER_VALUES, conditionPlaceholderValue);
  }

  /**
   * Updates the rule line as process variables in definition placeholders.
   *
   * @param definitionInstance
   * @param attributes
   */
  public void createProcessVariables(DefinitionInstance definitionInstance,
      List<Attribute> attributes) {
    Map<String, ProcessVariableData> processVariableMap =
        (Map<String, ProcessVariableData>) definitionInstance.getPlaceholderValue()
            .get(PROCESS_VARIABLES);

    Map<String, String> typeToNativeTypeMap =
        customWorkflowConfig.getDataTypes().stream()
            .collect(Collectors.toMap(DataType::getType, DataType::getNativeType));

//  For Example: Term is a variable, which gets populated.
    for (Attribute attribute : attributes) {
      processVariableMap.putIfAbsent(
          attribute.getName(),
          ProcessVariableData.builder().type(typeToNativeTypeMap.get(attribute.getType())).build());
    }
  }
}
