package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CONFIG_PARAMETER_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.PlaceholderParameterAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.PlaceholderParameterDetailsAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.springframework.stereotype.Component;

/**
 * This class extracts placeholder values for a given definition. As of now, it is used during
 * create/update of a Multi Step definition.
 */
@AllArgsConstructor
@Component
public class MultiStepPlaceholderExtractor implements PlaceholderExtractor {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final WASContextHandler wasContextHandler;


  /**
   * Extract placeholder values from the definition
   *
   * @param definitionInstance
   * @return map of bpmn and dmn placeholder values
   */
  @Override
  @Metric(name = MetricName.MULTI_STEP_DEFINITION_PLACEHOLDER_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public Map<String, Object> extractPlaceholderValue(
      DefinitionInstance definitionInstance) {

    final String recordType = definitionInstance.getDefinition().getRecordType();
    final String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(
        definitionInstance.getDefinition());

    Map<String, List<PlaceholderParameterAttribute>> configPlaceholderParameterAttributes = new HashMap<>();

    Map<String, Object> bpmnPlaceHolders = getActionVariables(actionKey, recordType,
        configPlaceholderParameterAttributes);

    Map<String, ProcessVariableData> processVariables =
        (Map<String, ProcessVariableData>) bpmnPlaceHolders.get(PROCESS_VARIABLES);

    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(definitionInstance.getBpmnModelInstance());
    // Gets the process variables from the start event
    processVariables.putAll(
        SingleDefinitionUtil.getProcessVariablesFromStartEvent(startEventElement));
    Map<String, Object> userVariables = new HashMap<>();
    // add locale here
    Map<String, String> localeMap = new HashMap<>();
    localeMap.put(
        WASContextEnums.INTUIT_WAS_LOCALE.getValue(),
        wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE));
    bpmnPlaceHolders.put(USER_META_DATA, localeMap);

    SingleDefinitionUtil.setRecurrenceRuleInUserVariables(definitionInstance, userVariables);
    SingleDefinitionUtil.setRecurrenceVariablesInProcessVariables(definitionInstance,
        processVariables, startEventElement);
    bpmnPlaceHolders.put(USER_VARIABLES, userVariables);
    bpmnPlaceHolders.put(CONFIG_PARAMETER_VARIABLES, configPlaceholderParameterAttributes);

    WorkflowLogger.logInfo(
        "step=extractPlaceholderValue recordType=%s actionKey=%s",
        recordType,
        actionKey);

    return bpmnPlaceHolders;
  }

  /**
   * Passes through all the action present in the config for that recordType and actionKey.
   *
   * @param actionKey
   * @param recordType
   * @param placeholderParameterAttributes Its a map config placeholders, which will be used to
   *                                       substitute during step extraction
   * @return a map of ProcessVariables, which will be stored in definitionDetails placeholders.
   */
  private Map<String, Object> getActionVariables(String actionKey, String recordType,
      Map<String, List<PlaceholderParameterAttribute>> placeholderParameterAttributes) {

    List<com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action> actions =
        CustomWorkflowUtil.getAllConfigActionsForRecordTypeAndActionKey(customWorkflowConfig,
            recordType, actionKey);

    Map<String, Object> variableMap = new HashMap<>();
    Map<String, Object> processVariables = new HashMap<>();

    ActionMapper actionMapper = actionMapperAdapter(actionKey);

    for (com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action action : actions) {
//    Fetches Process Variable and help variable map for this actionId.
      actionMapper.getAction().setId(GlobalId.builder().setLocalId(action.getId()).build());
      Map<String, ProcessVariableData> consolidatedProcessVariables =
          computeActionVariables(actionMapper, action.getId(), recordType,
              placeholderParameterAttributes);
      variableMap.putAll(consolidatedProcessVariables);
    }
    processVariables.put(PROCESS_VARIABLES, variableMap);
    return processVariables;

  }

  /**
   * Get the config parameters for a particular record type and action
   *
   * @param actionMapper
   * @param actionId
   * @param recordType
   * @param actionUserVariables Returns a map of actionId To List Parameters.
   * @return map of process variable and its corresponding parameters
   */
  private Map<String, ProcessVariableData> computeActionVariables(
      ActionMapper actionMapper, String actionId, String recordType,
      Map<String, List<PlaceholderParameterAttribute>> actionUserVariables) {

    // Get action record from the config.
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action recordAction =
        CustomWorkflowUtil.getConfigActionEntry(
            this.customWorkflowConfig, recordType, actionMapper.getActionKey(), actionId);

    WorkflowLogger.logInfo("step=fetchActionVariables recordType=%s actionKey=%s, actionId=%s",
        recordType, actionMapper.getActionKey(), actionId);

    // Extract non-configurable metadata parameters like TaskType/Project-type
    Map<String, HandlerDetails.ParameterDetails> actionMetaData =
        CustomWorkflowUtil.extractProjectMetaDataForAction(recordAction, actionMapper, recordType);

    Map<String, ProcessVariableData> processVariables = new HashMap<>();

    List<PlaceholderParameterAttribute> placeholderParameterAttributes = new ArrayList<>();
    for (Parameter parameter : recordAction.getParameters()) {
      PlaceholderParameterAttribute placeholderParameterAttribute = new PlaceholderParameterAttribute();
      PlaceholderParameterDetailsAttribute placeholderParameterDetailsAttribute = new PlaceholderParameterDetailsAttribute();
      List<String> fieldValues = parameter.getFieldValues();
      final String parameterName = parameter.getName();
      // if the parameter is part of metadata like task-type/project-type, then it has to be
      // replaced first.
      if (actionMetaData.containsKey(parameterName)) {
        fieldValues = actionMetaData.get(parameterName).getFieldValue();
      }

      if (ParameterDetailsValueType.PROCESS_VARIABLE.equals(parameter.getValueType())) {
        processVariables.put(
            parameterName, ProcessVariableData.builder().type(parameter.getFieldType()).build());
      } else {
        placeholderParameterAttribute.setParameterName(parameterName);
        placeholderParameterDetailsAttribute.setFieldValues(fieldValues);
        placeholderParameterAttribute.getPlaceholderParameterDetailsAttributes()
            .add(placeholderParameterDetailsAttribute);
      }

      Map<String, String> helpVariables = CustomWorkflowUtil.populateHelpProcessVariables(
          processVariables, parameter);
      placeholderParameterDetailsAttribute.setHelpVariableMap(helpVariables);
      placeholderParameterAttributes.add(placeholderParameterAttribute);
    }

    actionUserVariables.put(actionId, placeholderParameterAttributes);

    return processVariables;
  }

  /**
   * ActionMapper Adapter creates an object where all the action is marked as selected.
   *
   * @param actionKey
   * @return
   */
  private ActionMapper actionMapperAdapter(String actionKey) {
    ActionMapper actionMapper = new ActionMapper();
    actionMapper.setActionKey(actionKey);
    Action action = new Action();
    action.setSelected(true);
    actionMapper.setAction(action);
    return actionMapper;
  }

}
