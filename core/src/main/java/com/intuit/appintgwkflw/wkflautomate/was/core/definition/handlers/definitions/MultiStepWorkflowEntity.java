package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Entity class to process and build Workflow Step
 */
@Data
@Builder
public class MultiStepWorkflowEntity {

  private List<WorkflowStep> workflowSteps;
  private ActivityInstance activityInstance;
  private Template template;
  // map of actionId's to workflowStepId's
  private Map<String, GlobalId> actionIdToStepIdMap;
  // globalId used to build current workflowStepId
  private GlobalId globalId;
  private Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap;
}
