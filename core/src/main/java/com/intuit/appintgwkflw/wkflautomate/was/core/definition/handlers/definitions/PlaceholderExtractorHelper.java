package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Trigger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PlaceholderExtractorHelper {
    private final AuthHelper authHelper;

    /**
     * Assuming actionLocalId to be of the form originalActionId_realmId_UUID, we only the want the
     * actionId without the realmid and uuid to be saved in the placeholder values. To get the
     * originalActionId, we will split the action id based on realm id obtained from the auth.
     *
     * @param action
     * @return originalActionId
     */
    public String getOriginalActionId(Action action) {
        String actionLocalId = action.getId().getLocalId();
        return getActionIdFromLocalId(actionLocalId);
    }

    /**
     * Assuming actionId to be of the form actionId_realmId_UUID, we only the want the
     * actionId without the realmid and uuid to be saved in the placeholder values. To get the
     * originalActionId, we will split the action id based on realm id obtained from the auth.
     *
     * @param trigger
     * @return originalActionId
     */
    public String getOriginalActionIdFromTrigger(Trigger trigger) {
        String triggerActionId = trigger.getId().getLocalId();
        return getActionIdFromLocalId(triggerActionId);
    }

    /**
     * Return the actionId from the input localId actionId_realmId_UUID
     * @param localId
     * @return
     */
    public String getActionIdFromLocalId(String localId) {
        return localId.substring(0, localId.indexOf(authHelper.getOwnerId()) - 1);
    }
}
