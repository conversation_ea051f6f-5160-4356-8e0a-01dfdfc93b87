package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RULE_LINE_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.STRING_TYPE_CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WORKFLOW_STEP_CONDITION_TYPE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.springframework.stereotype.Component;

/**
 * This class extracts placeholder values for a given definition. As of now, it is used during
 * create/update of a precanned definition as part of single definition.
 * <p>
 * The output in json format looks like this:-
 * <p>
 * * <pre>{@Code
 * {
 *   "bpmn_placeholder_values": {
 *     "process_variables": {
 *       "processVariableName": {
 *         "value": "xyz",
 *         "type": "string"
 *       }
 *     },
 *     "user_variables": {
 *       "actionId": {
 *         "parameters": {
 *           "parameterName-1": {
 *             "fieldValue": [
 *               "xyz"
 *             ]
 *           },
 *           "parameterName-2": {
 *             "fieldValue": [
 *               "pqr"
 *             ]
 *           }
 *         },
 *         "selected": "true"
 *       }
 *     }
 *   },
 *   "dmn_placeholder_values": {
 *       "rule_line_variables": {
 *         "TxnAmount": "GTE 10",
 *         "TxnDueDays": "AF 10"
 *      }
 *     }
 *   }
 * }
 */
@Component(WorkflowBeansConstants.PRECANNED_DEFINITION_PLACEHOLDER_EXTRACTOR)
@AllArgsConstructor
public class PrecannedDefinitionPlaceholderExtractor implements PlaceholderExtractor {

  private static final String MAPPED_ACTION_KEYS = "MAPPED_ACTION_KEYS";
  private final PlaceholderExtractorHelper placeholderExtractorHelper;
  private final WASContextHandler wasContextHandler;

  @Override
  @Metric(name = MetricName.PRECANNED_DEFINITION_PLACEHOLDER_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public Map<String, Object> extractPlaceholderValue(DefinitionInstance definitionInstance) {
    // Map to store bpmn and dmn placeholder values.
    Map<String, Object> definitionPlaceholders = new HashMap<>();
    // Store user entered field values like subject/message in the email
    Map<String, Object> userVariables = new HashMap<>();
    // Gets the process and user variables from the workflow steps
    // Store process variables to be used by process instance like action is select/not-selected
    Map<String, ProcessVariableData> processVariables = new HashMap<>();
    // Store user entered workflow rules
    Map<String, Object> ruleLineVariables = new HashMap<>();

    //Extract rule line variables from definition
    ruleLineVariables.put(
        RULE_LINE_VARIABLES,
        SingleDefinitionUtil.getRuleLinesForWorkflowCondition(
            definitionInstance.getDefinition()));

    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep -> {
              Map<String, Object> stepActionVariables =
                  getStepActionVariables(workflowStep.getActions(), workflowStep.getTrigger());
              userVariables.putAll((Map<String, Object>) stepActionVariables.get(USER_VARIABLES));
              Map<String, String> actionKeyMap = (Map<String, String>) stepActionVariables.get(
                  MAPPED_ACTION_KEYS);
//            fetch WorkflowCondition for Exclusive G/Ws and set them as process variables.
              processVariables.putAll(
                  getStepConditionProcessVariables(workflowStep.getWorkflowStepCondition(),
                      actionKeyMap));
//            Update Trigger Process Variables into placeholders
              if (Objects.nonNull(workflowStep.getTrigger()) && Objects.nonNull(
                  workflowStep.getTrigger().getParameters())) {
                workflowStep.getTrigger().getParameters().forEach(
                    inputParameter -> processVariables.putAll(
                        SingleDefinitionUtil.getParameterForTrigger(inputParameter)));
              }
            });

    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(definitionInstance.getBpmnModelInstance());
    // Gets the process variables from the start event
    processVariables.putAll(
        SingleDefinitionUtil.getProcessVariablesFromStartEvent(startEventElement));

    // bpmn placeholder values comprising of user and process variables
    Map<String, Object> bpmnPlaceHolders = new HashMap<>();
    bpmnPlaceHolders.put(PROCESS_VARIABLES, processVariables);
    bpmnPlaceHolders.put(USER_VARIABLES, userVariables);

    // add locale here
    Map<String, String> localeMap = new HashMap<>();
    localeMap.put(
        WASContextEnums.INTUIT_WAS_LOCALE.getValue(),
        wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE));
    bpmnPlaceHolders.put(USER_META_DATA, localeMap);

    definitionPlaceholders.put(BPMN_PLACEHOLDER_VALUES, bpmnPlaceHolders);
    definitionPlaceholders.put(DMN_PLACEHOLDER_VALUES, ruleLineVariables);
    return definitionPlaceholders;
  }

  /**
   * In Case of the workflowStepCondition is of type EXCLUSIVE_GATEWAY, property is set from
   * (ExclusiveGatewayHandler) All the nested triggers passed via Exclusive_Gateway are set to true
   * and set as process_variables.
   *
   * @param workflowStepCondition
   * @param actionKeyMap
   * @return
   */
  private Map<String, ProcessVariableData> getStepConditionProcessVariables(
      WorkflowStepCondition workflowStepCondition,
      Map<String, String> actionKeyMap) {
    Map<String, ProcessVariableData> processVariables = new HashMap<>();

    // workflowStepCondition can be null when it doesnt have a bpmn & ConditionType can be null in case of any other G/Ws other than Exclusive G/W
    if (Objects.isNull(workflowStepCondition) || Objects.isNull(
        workflowStepCondition.get(WORKFLOW_STEP_CONDITION_TYPE))) {
      return processVariables;
    }

    if (BpmnComponentType.EXCLUSIVE_GATEWAY.getName()
        .equals(workflowStepCondition.get(WORKFLOW_STEP_CONDITION_TYPE))) {
      workflowStepCondition.getRuleLines().forEach(
          ruleLine -> {
//          In case of Exclusive G/W, only one rule is send
            String mappedKey =
                ruleLine.getMappedActionKeys().stream().findFirst().orElse(null);
            if (Objects.nonNull(mappedKey)) {
              String actionId = placeholderExtractorHelper.getActionIdFromLocalId(mappedKey);
              Rule rule = ruleLine.getRules().stream().findFirst().orElse(null);
              if (Objects.isNull(actionId) || Objects.isNull(rule)) {
                return;
              }
              String conditionalExpression = rule.getConditionalExpression();
              String actionKey = actionKeyMap.get(actionId);
              if (Objects.nonNull(conditionalExpression) && Objects.nonNull(actionKey)) {
                String conditionalValue = SingleDefinitionUtil.stripBooleanFromVariable(
                    conditionalExpression);
                processVariables.put(actionKey,
                    ProcessVariableData.builder().type(STRING_TYPE_CAMUNDA)
                        .value(conditionalValue)
                        .build());
              }
            }
          }
      );
    }
    return processVariables;
  }

  /**
   * Get the user variable map from actions and triggers
   *
   * @param actions
   * @param trigger
   * @return map of user variables variable and its corresponding parameters
   */
  private Map<String, Object> getStepActionVariables(List<WorkflowStep.ActionMapper> actions,
      Trigger trigger) {
    Map<String, Object> actionVariables = new HashMap<>();

    // Map to store user variable Field Values like subject in the email, set by the user for a
    //  actionid can be create-task/send-customer-email
    Map<String, Object> userVariables = new HashMap<>();
    Map<String, String> actionKeyMap = new HashMap<>();

    //  User Variables for trigger
    if (ObjectUtils.isNotEmpty(trigger)) {
      String triggerId = placeholderExtractorHelper.getOriginalActionIdFromTrigger(trigger);
      Map<String, Object> variableMap = SingleDefinitionUtil.getTriggerVariables(trigger);
      userVariables.putAll(Collections.singletonMap(triggerId, variableMap));
    }

    // user variables map for action
    actions.forEach(
        actionMapper -> {
          String actionId = placeholderExtractorHelper.getOriginalActionId(
              actionMapper.getAction());
          actionKeyMap.put(actionId, actionMapper.getActionKey());
          Map<String, Object> variableMap = getActionVariables(actionMapper);
          userVariables.putAll(Collections.singletonMap(actionId, variableMap));
        });
    actionVariables.put(USER_VARIABLES, userVariables);
    actionVariables.put(MAPPED_ACTION_KEYS, actionKeyMap);
    return actionVariables;
  }

  /**
   * Get the user variable map from action
   *
   * @param actionMapper
   * @return map of user variable and its corresponding parameters
   */
  private Map<String, Object> getActionVariables(WorkflowStep.ActionMapper actionMapper) {
    Action action = actionMapper.getAction();
    if (Objects.isNull(action)) {
      return Collections.emptyMap();
    }

    // Get user parameters and their field values.
    Map<String, Object> consolidatedActionVariables = getConsolidatedActionVariables(actionMapper);

    Map<String, Object> actionUserVariables = new HashMap<>();
    // Add user parameters corresponding to the action id
    actionUserVariables.put(ACTION_PARAMETERS, consolidatedActionVariables);
    actionUserVariables.put(ACTION_SELECTED, BooleanUtils.toBoolean(action.isSelected()));
    return actionUserVariables;
  }



  /**
   * Get the field values for the corresponding action. Do this only if the the parameter is
   * configurable, otherwise we can extract the same from the template. eg. non-configurable :
   * taskhandlerId, workflowName eg. configurable : subject, message
   *
   * @param actionMapper
   * @return map of user variable and its corresponding field values.
   */
  private Map<String, Object> getConsolidatedActionVariables(
      WorkflowStep.ActionMapper actionMapper) {
    Map<String, Object> clientPassedActionParameters =
        actionMapper.getAction().getParameters().stream()
            .filter(inputParameter -> BooleanUtils.toBoolean(inputParameter.isConfigurable()))
            .collect(
                Collectors.toMap(
                    InputParameter::getParameterName,
                    param -> Collections.singletonMap(FIELD_VALUE, param.getFieldValues())));
    return clientPassedActionParameters;
  }
}
