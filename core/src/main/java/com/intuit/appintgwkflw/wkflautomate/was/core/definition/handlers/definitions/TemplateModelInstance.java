package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import java.util.List;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@RequiredArgsConstructor
public class TemplateModelInstance {

  private final BpmnModelInstance bpmnModelInstance;
  private final List<DmnModelInstance> dmnModelInstanceList;
  private final boolean isUpdate;
}
