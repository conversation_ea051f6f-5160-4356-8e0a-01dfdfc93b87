package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.cronutils.utils.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author: Nitin Gupta Date: 15/01/20 Description:
 */
@Component(WorkflowBeansConstants.UPDATE_DEFINITION_HANDLER)
public class UpdateDefinitionHandler implements DefinitionCrudHandler<DefinitionInstance> {

  @Autowired
  private DefinitionServiceHelper definitionServiceHelper;

  @Autowired
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;

  @Override
  public DefinitionInstance process(DefinitionInstance definitionInstance, String realmId)
      throws WorkflowGeneralException {

    String definitionId = definitionInstance.getDefinition().getId().getLocalId();

    // Assert template id
    WorkflowVerfiy.verifyNull(
        definitionInstance.getDefinition().getTemplate().getId(), WorkflowError.TEMPLATE_NOT_FOUND);

    // Assert Definition id
    WorkflowVerfiy.verifyNull(
        definitionInstance.getDefinition().getId(), WorkflowError.DEFINITION_NOT_FOUND);

    String templateId = definitionInstance.getDefinition().getTemplate().getId().getLocalId();
    WorkflowLogger.logInfo(
        "Update definition invoked for definitionId=%s , templateId=%s", definitionId, templateId);

    // Fetch list of definitions associated  with the template
    List<DefinitionDetails> definitionDetails =
        definitionServiceHelper.fetchDefinitions(templateId, realmId);


    // assert given definition is present in definition list or not
    Optional<DefinitionDetails> definitionDetailsOptional =
        definitionServiceHelper.getDefinitionDetailsById(definitionDetails, definitionId);
    DefinitionDetails updatedDefinitionDetail;

    if (isMigration(definitionInstance)) {
      updatedDefinitionDetail = definitionServiceHelper.findByDefinitionId(definitionId);
      definitionInstance.setWorkflowId(updatedDefinitionDetail.getWorkflowId());
      definitionInstance.setModifiedBy(updatedDefinitionDetail.getModifiedByUserId());

    }
    else {
      updatedDefinitionDetail =
          definitionDetailsOptional.orElseThrow(
              () -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));
    }

    updatedDefinitionDetail.setVersion(updatedDefinitionDetail.getVersion() + 1);
    definitionInstance.setDefinitionDetails(updatedDefinitionDetail);

    if (!isMigration(definitionInstance)) {
      // Set workflow id to get utilize in app-connect call
      definitionInstance.setWorkflowId(
          definitionDetails.stream()
              .filter(e -> e.getDefinitionId().equalsIgnoreCase(definitionId))
              .findFirst()
              .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND))
              .getWorkflowId());
    }

    return definitionInstance;
  }

  @VisibleForTesting
  public boolean isMigration(DefinitionInstance definitionInstance){
    return WASContext.isMigrationContext() || (CustomWorkflowUtil.isCustomApprovalWorkflow(definitionInstance.getDefinition().getTemplate())
        && customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definitionInstance.getDefinition()));
  }
}
