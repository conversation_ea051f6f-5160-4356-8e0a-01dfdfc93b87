package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.util;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.MessageEventDefinition;
import org.camunda.bpm.model.bpmn.instance.SendTask;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;

@UtilityClass
public class BpmnOverwatchUtil {

    public static final String OVERWATCH_SUFFIX = "_overwatch";
    public static final String OVERWATCH_TOPIC = "overwatch-test";


    public BpmnModelInstance updateBpmnModelInstanceForOverwatchTests(BpmnModelInstance bpmnModelInstance) {
        BpmnModelInstance overwatchBpmnModelInstance = bpmnModelInstance.clone();
        updateTasks(overwatchBpmnModelInstance);
        return overwatchBpmnModelInstance;
    }

    private static void updateTasks(BpmnModelInstance bpmnModelInstance) {
        bpmnModelInstance.getModelElementsByType(SendTask.class).forEach(
                sendTask -> updateTaskTopic(sendTask)
        );

        bpmnModelInstance.getModelElementsByType(ServiceTask.class).forEach(
                serviceTask -> updateTaskTopic(serviceTask)
        );

        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class).forEach(
                businessRuleTask -> updateTaskTopic(businessRuleTask)
        );

        bpmnModelInstance.getModelElementsByType(MessageEventDefinition.class).forEach(
                messageEventDefinition -> updateTaskTopic(messageEventDefinition)
        );

        bpmnModelInstance.getModelElementsByType(CallActivity.class).forEach(
                callActivity -> updateCallActivity(callActivity)
        );
    }

    private static void updateTaskTopic(SendTask task) {
        if(StringUtils.isNotBlank(task.getCamundaTopic())) {
            task.setCamundaTopic(OVERWATCH_TOPIC);
        }
    }

    private static void updateTaskTopic(ServiceTask task) {
        if(StringUtils.isNotBlank(task.getCamundaTopic())) {
            task.setCamundaTopic(OVERWATCH_TOPIC);
        }
    }

    private static void updateTaskTopic(BusinessRuleTask task) {
        if(StringUtils.isNotBlank(task.getCamundaTopic())) {
            task.setCamundaTopic(OVERWATCH_TOPIC);
        }
    }

    private static void updateTaskTopic(MessageEventDefinition task) {
        if(StringUtils.isNotBlank(task.getCamundaTopic())) {
            task.setCamundaTopic(OVERWATCH_TOPIC);
        }
    }

    private static void updateCallActivity(CallActivity callActivity) {
        callActivity.setCalledElement(callActivity.getCalledElement() + OVERWATCH_SUFFIX);
    }
}