package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * This class contains methods to build composite workflow steps for multi-condition workflows.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepCompositeBuilder implements MultiWorkflowStepBuilder {

  private final MultiStepConditionBuilder multiStepConditionBuilder;
  private final MultiStepActionBuilder multiStepActionBuilder;

  /**
   * This function combines the action and condition step to  creates a composite workflowStep if
   * the step type is DAYS. The constructed workflowStep will include actionGroup along with a
   * workflowStepCondition and will be the leaf node.
   *
   * @param record              record object
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom or precanned
   * @param currentConfigStep   current config step which will be used to construct the
   *                            workflowStep
   * @param configStepIdMap     Map of stepId and step
   * @param path                next path object if previous workflowStep
   * @return
   */

  // This will always have one child which will be an action node for the YES path
  @Override
  public WorkflowStep processWorkflowStep(Record record, String actionKey,
      Boolean isPreCannedTemplate, Steps currentConfigStep
      , WorkflowStep.StepNext path, Map<Integer, Steps> configStepIdMap) {
    // Building the condition
    WorkflowLogger.logInfo("Combining condition and action step for MCR step=multiStepCompositeBuilder status=started");
    WorkflowStep conditionStep = multiStepConditionBuilder.processWorkflowStep(record,
        actionKey,
        isPreCannedTemplate,
        currentConfigStep,
        path,
        configStepIdMap);
    // No need to setNexts for this particular step since this is part of composite step

    // Will always have one child and will never be null since condition cannot be a leaf node
    AtomicReference<Steps> nextWorkflowStep = new AtomicReference<>();
    currentConfigStep.getNexts().stream()
        .forEach(nextPath -> nextWorkflowStep.set(
            configStepIdMap.get(Integer.parseInt(nextPath.getStepId()))));

    // Building the action
    WorkflowStep actionStep = multiStepActionBuilder.processWorkflowStep(record,
        actionKey,
        isPreCannedTemplate,
        nextWorkflowStep.get(),
        path,
        configStepIdMap);

    // Setting parameter values
    Map<String, String> parameterValues = nextWorkflowStep.get().getAttributes().stream()
        .collect(Collectors.toMap(Attribute::getId, Attribute::getDefaultValue));
    actionStep.getActionGroup().getAction().getParameters().stream()
        .forEach(parameter -> parameter.setFieldValues(
            List.of(parameterValues.get(parameter.getParameterName()))));

    // Combine actionStep and conditionStep to create composite step
    WorkflowStep compositeStep = new WorkflowStep();
    compositeStep.setWorkflowStepCondition(conditionStep.getWorkflowStepCondition());
    compositeStep.setActionGroup(actionStep.getActionGroup());
    compositeStep.setStepType(StepTypeEnum.WORFKLOWSTEP);
    compositeStep.setRequired(true);
    compositeStep.setId(actionStep.getId());

    WorkflowLogger.logInfo(
        "Combined condition=%s and action=%s step for MCR when isPreCannedTemplate=%s step=multiStepCompositeBuilder status=completed",
        conditionStep.getId(), actionStep.getId(),isPreCannedTemplate);

    // The next of the step is handled in the processNexts method

    return compositeStep;
  }
}
