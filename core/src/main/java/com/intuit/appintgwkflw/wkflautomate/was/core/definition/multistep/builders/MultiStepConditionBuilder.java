package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Builds workflow steps with conditions for multi-condition multi-step workflows
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepConditionBuilder implements MultiWorkflowStepBuilder {

  private final WASContextHandler wasContextHandler;
  private final TemplateConditionBuilder conditionBuilder;

  /**
   * This function builds workflowStep from config step if the config step includes an attribute
   * list. The constructed workflowStep will include workflowStepCondition with
   * conditionalInputParameters, supportedOperators and ruleLines (constructed from the attributes
   * defined for the particular config step)
   *
   * @param record              record
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom or precanned
   * @param currentConfigStep   current config step which will be used to construct the
   *                            workflowStep
   * @param configStepIdMap     map of stepId and step
   * @param path                next path object if previous workflowStep
   */
  @Override
  public WorkflowStep processWorkflowStep(
      Record record,
      String actionKey,
      Boolean isPreCannedTemplate,
      Steps currentConfigStep,
      WorkflowStep.StepNext path,
      Map<Integer, Steps> configStepIdMap) {
    WorkflowStep conditionStep = new WorkflowStep();
    WorkflowLogger.logInfo("step=buildConditionStep step=%s, stepType=%s",
        currentConfigStep.getStepId(), currentConfigStep.getStepType());
    conditionStep.setWorkflowStepCondition(
        conditionBuilder.build(
            record,
            actionKey,
            true,
            currentConfigStep));
    conditionStep.setStepType(StepTypeEnum.CONDITION);
    conditionStep.setId(
        GlobalId.builder()
            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(conditionStep.getTypeId())
            .setLocalId(WorkflowConstants.CUSTOM_DECISION_ELEMENT
                .concat(WorkflowConstants.HYPHEN)
                .concat(Integer.toString(currentConfigStep.getStepId())))
            .build());
    // if path is passed then set the current constructed stepId as the
    // path's setNextWorkflowStepId. Previous step's next id's should match
    // the current workflowSteps id.
    if (Objects.nonNull(path)) {
      path.setWorkflowStepId(conditionStep.getId().toString());
    }
    conditionStep.setRequired(true);
    return conditionStep;
  }
}
