package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import java.io.ByteArrayInputStream;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * This class reads activityDetails for the definition and converts to ActivityInstance object.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepDefinitionActivityBuilder {

  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  /**
   * This function generates a map where activity id as the key and corresponding Definition
   * ActivityInstance object as the value that is built using all the activities associated with a
   * particular definition id
   *
   * @param definitionId workflow definition id
   * @param dmnResponses list of dmn xml strings
   * @return map of  activityIds and ActivityInstance objects
   */
  @Metric(name = MetricName.MULTI_STEP_READ_DEFINITION_PLACEHOLDER_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public Map<String, ActivityInstance> generateActivityInstanceMap(final String definitionId,
      List<DmnResponse> dmnResponses) {
    Map<String, ActivityInstance> activityIdToActivityInstance = new HashMap<>();

    // create dmn definitionActivityInstance and add it to our map
    generateActivityIdToDmnInstancesMap(activityIdToActivityInstance, dmnResponses);
    WorkflowLogger.logInfo("step=FetchActivityDefinitionsFromDB method=start definitionId=%s", definitionId);
    List<DefinitionActivityDetail> parentDefinitionActivityDetails = definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(
            definitionId)
        .orElseThrow(() -> new WorkflowGeneralException(
            WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND));

    parentDefinitionActivityDetails.forEach(parentDefinitionActivityDetail -> {
      ActivityInstance parentActivityInstance = buildDefinitionActivityInstance(
          parentDefinitionActivityDetail, null);
      List<DefinitionActivityDetail> childActivityDefinitions = parentDefinitionActivityDetail.getChildActivityDetails();
      populateChildActivityInstances(parentActivityInstance, childActivityDefinitions);
      activityIdToActivityInstance.put(parentDefinitionActivityDetail.getActivityId(),
          parentActivityInstance);
    });

    return activityIdToActivityInstance;
  }

  /**
   * This function prepares a map of decisionElement ActivityId to DmnModelInstance.
   *
   * @param activityInstanceMap activity instance map keyed by activityId
   * @param dmnResponses        list of dmn xml strings
   */
  private void generateActivityIdToDmnInstancesMap(
      Map<String, ActivityInstance> activityInstanceMap,
      List<DmnResponse> dmnResponses) {
    if (dmnResponses.isEmpty()) {
      throw new WorkflowGeneralException(WorkflowError.DMN_NOT_FOUND);
    }
    dmnResponses.stream().forEach(dmnResponse -> {
      DmnModelInstance dmnModelInstance = Dmn.readModelFromStream(
          new ByteArrayInputStream(dmnResponse.getDmnXml().getBytes()));
      ActivityInstance dmnActivityInstance = buildDefinitionActivityInstance(null,
          dmnModelInstance);
      Collection<Decision> decision = dmnModelInstance.getModelElementsByType(Decision.class);
      String decisionActivityId = decision.stream().findFirst().get().getId();
      activityInstanceMap.put(decisionActivityId, dmnActivityInstance);
    });
  }

  /**
   * This function builds and populates the parent ActivityInstance object
   *
   * @param definitionActivityDetail call activity object
   * @return activity instance constructed from call activity
   */
  private ActivityInstance buildDefinitionActivityInstance(
      DefinitionActivityDetail definitionActivityDetail,
      DmnModelInstance dmnModelInstance) {
    Map<String, Object> placeholders = new HashMap<>();
    // definitionActivityDetail will be null only when we are building a dmn instance
    if (Objects.nonNull(definitionActivityDetail)) {
      placeholders = new JSONObject(definitionActivityDetail.getUserAttributes()).toMap();
    }
    return ActivityInstance.builder()
        .userAttributes(placeholders)
        .dmnModelInstance(dmnModelInstance)
        .build();
  }

  /**
   * This function builds and populates the associated child ActivityInstance objects and sets them
   * within the associated parent ActivityInstance object, this relation can be determined by using
   * data available in the parentId parameter again each call activity object
   *
   * @param parentActivity           parent activity instance
   * @param childActivityDefinitions list of all call activity definitions
   */
  private void populateChildActivityInstances(
      ActivityInstance parentActivity,
      List<DefinitionActivityDetail> childActivityDefinitions) {
    Map<String, ActivityInstance> childActivityInstances = new HashMap<>();

    if (CollectionUtils.isNotEmpty(childActivityDefinitions)) {
      childActivityDefinitions.stream().forEach(activity -> {
        ActivityInstance childActivityInstance = buildDefinitionActivityInstance(activity,
            null);
        childActivityInstances.put(activity.getActivityId(), childActivityInstance);
      });
    }
    parentActivity.setChildActivityInstances(childActivityInstances);
  }
}