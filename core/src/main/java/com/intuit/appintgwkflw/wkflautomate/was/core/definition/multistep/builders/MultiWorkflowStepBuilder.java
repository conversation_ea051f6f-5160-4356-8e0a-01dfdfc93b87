package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.WorkflowStep;

import java.util.Map;

public interface MultiWorkflowStepBuilder {

  /**
   * Convert config step to workflowStep object based on whether it is an action step type, composite step type or a
   * condition step type
   *
   * @param record              record object
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom or precanned
   * @param currentConfigStep   current config step which will be used to construct the
   *                            workflowStep
   * @param configStepIdMap     map of stepId and  step
   * @param path                next path object if previous workflowStep
   */
  WorkflowStep processWorkflowStep(
      Record record,
      String actionKey,
      Boolean isPreCannedTemplate,
      Steps currentConfigStep,
      WorkflowStep.StepNext path,
      Map<Integer, Steps> configStepIdMap);
}
