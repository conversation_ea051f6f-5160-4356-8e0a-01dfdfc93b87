package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 * This class helps in adding implicit elements from the baseTemplate to bpmnModelInstance.
 */

@Component
@RequiredArgsConstructor
public class DynamicBpmnImplicitElementsHelper {

    private final DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

    public void addImplicitElementsFromBaseTemplate(
            BpmnModelInstance bpmnModelInstance, BpmnModelInstance baseTemplateBpmnModelInstance) {
        FlowNode baseTemplateStartFlowNode =
                baseTemplateBpmnModelInstance.getModelElementById(
                        CustomWorkflowUtil.findStartEventElement(bpmnModelInstance).getId());

        addImplicitElementsForOutgoingNodes(
                baseTemplateStartFlowNode, bpmnModelInstance, baseTemplateBpmnModelInstance);
    }

    private void addImplicitElementsForOutgoingNodes(
            FlowNode baseTemplateRootNode,
            BpmnModelInstance bpmnModelInstance,
            BpmnModelInstance baseTemplateBpmnModelInstance) {

        Collection<SequenceFlow> outgoingSequenceFlows = baseTemplateRootNode.getOutgoing();
        for (SequenceFlow outgoingSequenceFlow : outgoingSequenceFlows) {
            FlowNode baseTemplateTargetNode = outgoingSequenceFlow.getTarget();

            if (DynamicBpmnUtil.isNodeAnImplicitElement(
                    baseTemplateTargetNode, baseTemplateBpmnModelInstance)) {

                FlowNode flowNode = bpmnModelInstance.getModelElementById(baseTemplateRootNode.getId());

                if (Objects.isNull(flowNode)) {
                    continue;
                }

                FlowNode targetFlowNode =
                        bpmnModelInstance.getModelElementById(baseTemplateTargetNode.getId());

                // create the targetFlowNode if not already present
                if (Objects.isNull(targetFlowNode)) {

                    DynamicBpmnFlowNodeProcessor flowNodeProcessor =
                            dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(baseTemplateTargetNode);

                    flowNodeProcessor.addImplicitEvents(
                            flowNode,
                            baseTemplateTargetNode,
                            outgoingSequenceFlow,
                            bpmnModelInstance,
                            baseTemplateBpmnModelInstance);

                    targetFlowNode = bpmnModelInstance.getModelElementById(baseTemplateTargetNode.getId());

                    flowNodeProcessor.addExtensionElements(
                            targetFlowNode,
                            baseTemplateTargetNode,
                            bpmnModelInstance,
                            baseTemplateBpmnModelInstance);
                }
                // if targetNode is not already connected to sourceNode then connect both the nodes
                else if (!DynamicBpmnUtil.isSourceAndTargetNodesConnected(flowNode, targetFlowNode)) {

                    if (Objects.isNull(outgoingSequenceFlow.getConditionExpression())) {
                        flowNode
                                .builder()
                                .sequenceFlowId(outgoingSequenceFlow.getId())
                                .connectTo(targetFlowNode.getId())
                                .done();
                    } else {
                        flowNode
                                .builder()
                                .sequenceFlowId(outgoingSequenceFlow.getId())
                                .condition(outgoingSequenceFlow.getName(), outgoingSequenceFlow.getTextContent())
                                .connectTo(targetFlowNode.getId())
                                .done();
                    }
                }
            }
            addImplicitElementsForOutgoingNodes(
                    baseTemplateTargetNode, bpmnModelInstance, baseTemplateBpmnModelInstance);
        }
    }
}
