package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

/**
 * This class is used to save the information regarding a node in the dynamically
 * created Bpmn before creating its adjacency list.
 * <AUTHOR>
 */
@Getter
@Builder
public class DynamicBpmnNode implements Comparable<DynamicBpmnNode> {

  private final String activityId;

  private final BpmnComponentType bpmnComponentType;

  private final List<String> childNodes;

  @Override
  public int compareTo(DynamicBpmnNode dynamicBpmnNode) {
    return this.getActivityId().compareTo(dynamicBpmnNode.getActivityId());
  }
}
