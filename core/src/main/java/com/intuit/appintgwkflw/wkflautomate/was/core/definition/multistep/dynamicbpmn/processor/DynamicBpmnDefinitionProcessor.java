package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.HYPHEN;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnImplicitElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnSubProcessEventHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnNode;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.TemplateServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Definitions;
import org.camunda.bpm.model.dmn.instance.Output;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class is responsible for building the bpmnModelInstance dynamically from the
 * CreateDefinition payload
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DynamicBpmnDefinitionProcessor {

  private final DynamicBpmnProcessorHelper dynamicBpmnProcessorHelper;
  private final StartEventFlowNodeProcessor startEventFlowNodeProcessor;
  private final DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;
  private final DynamicBpmnImplicitElementsHelper dynamicBpmnImplicitElementsHelper;
  private final DynamicBpmnSubProcessEventHelper dynamicBpmnSubProcessEventHelper;
  private final TemplateServiceImpl templateServiceImpl;
  private final WASContextHandler contextHandler;

  @Metric(name = MetricName.CREATE_AND_DEPLOY_BPMN_DYNAMICALLY, type = Type.API_METRIC)
  public DefinitionInstance buildDefinitionInstanceDynamically(Definition definition) {

    expandCompositeWorkflowSteps(definition);

    String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definition);

    WorkflowLogger.logInfo("step=buildDefinitionInstanceDynamically status=Started actionKey=%s", actionKey);

    // Get the bpmnModelInstance of baseTemplate. This contains both explicit & implicit elements.
    final BpmnModelInstance baseTemplateBpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile(
            DynamicBpmnUtil.getBaseTemplateResourcePath(actionKey, ModelType.BPMN));

    Set<String> visitedNodeSet = new HashSet<>();
    DynamicBpmnNode rootNode =
        DynamicBpmnNode.builder()
            .activityId(BpmnComponentType.START_EVENT.getName())
            .bpmnComponentType(BpmnComponentType.START_EVENT)
            .build();
    visitedNodeSet.add(rootNode.getActivityId());

    // Build the bpmnModelInstance from the CreateDefinition payload
    BpmnModelInstance bpmnModelInstance =
        createBpmnModelInstanceFromPayload(
            definition, visitedNodeSet, rootNode, baseTemplateBpmnModelInstance);

    // add extension elements to all nodes in bpmnModelInstance from baseTemplate
    addExtensionElements(bpmnModelInstance, baseTemplateBpmnModelInstance);

    // create and connect all the implicit nodes present in the baseTemplate to bpmnModelInstance
    dynamicBpmnImplicitElementsHelper.addImplicitElementsFromBaseTemplate(bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    // update the processElement and add subprocess to the bpmnModelInstance
    dynamicBpmnSubProcessEventHelper.updateProcessElementAndAddSubprocess(bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    // add child events to the subprocess from baseTemplate
    dynamicBpmnSubProcessEventHelper.updateSubprocessWithChildEvents(bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    String hashSum = dynamicBpmnProcessorHelper.getHashValueForTemplateAdjacencyList(
        bpmnModelInstance, actionKey);

    // create dmnModelInstance for each BusinessRuleTask from the baseDmnTemplate
    List<DmnModelInstance> dmnModelInstanceList =
        createDmnModelInstancesFromBaseDmnTemplate(bpmnModelInstance, definition, hashSum, actionKey);

    // append hashSum to the BpmnProcessId
    dynamicBpmnSubProcessEventHelper.updateBpmnProcessId(bpmnModelInstance,
        baseTemplateBpmnModelInstance, hashSum);

    // append hashSum to the activityIds of all BusinessRuleTask
    DynamicBpmnUtil.updateBusinessRuleTaskActivityIds(bpmnModelInstance, hashSum);

    // templateName is taken from baseTemplate while creating templateDetails instance
    TemplateDetails templateDetails = templateServiceImpl.getOrSaveTemplateDetailsByHashValue(
        bpmnModelInstance,
        dmnModelInstanceList,
        hashSum);

    //Add Workflow in Context.
    contextHandler.addKey(WASContextEnums.WORKFLOW, templateDetails.getTemplateName());

    // update name to be sent to UI as display name to avoid confusion. Definition name will be the template name.
    // templateName is taken from baseTemplate while creating templateDetails instance
    definition.setName(templateDetails.getTemplateName());

    WorkflowLogger.logInfo("step=buildDefinitionInstanceDynamically status=completed definitionName=%s",
            definition.getName());

    return new DefinitionInstance(definition, bpmnModelInstance, dmnModelInstanceList,
        templateDetails);
  }

  protected BpmnModelInstance createBpmnModelInstanceFromPayload(
      Definition definition,
      Set<String> visitedNodeSet,
      DynamicBpmnNode rootNode,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    AbstractFlowNodeBuilder flowNodeBuilder =
        Bpmn.createExecutableProcess()
            .startEvent(rootNode.getActivityId())
            .name(rootNode.getActivityId())
            .camundaAsyncBefore(true);

    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
    Map<String, String> effectiveParentIdMap = new HashMap<>();
    Map<String, WorkflowStep> localIdWorkflowStepMap = new HashMap<>();

    // populate the workflowStepMap for all nodes
    for (WorkflowStep workflowStep : definition.getWorkflowSteps()) {
      workflowStepMap.put(workflowStep.getId().toString(), workflowStep);
      localIdWorkflowStepMap.put(workflowStep.getId().getLocalId(), workflowStep);
    }

    // get the first workflow step (which is not present in any of the child lists)
    WorkflowStep firstWorkflowStep = getFirstWorkflowStep(definition.getWorkflowSteps());

    // recursively populate effectiveParentIdMap for all nodes
    populateEffectiveParentIdForANode(firstWorkflowStep, null, effectiveParentIdMap,
        workflowStepMap);

    // recursively populate the dynamic activityId for root and other nodes
    Map<String, String> dynamicActivityIdMap = new HashMap<>();
    dynamicActivityIdMap.put(rootNode.getActivityId(), rootNode.getActivityId());
    dynamicBpmnProcessorHelper.populateDynamicActivityIdMap(
        dynamicActivityIdMap, effectiveParentIdMap, workflowStepMap, localIdWorkflowStepMap,
        firstWorkflowStep);

    // recursively process the workflow steps
    DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData =
        DynamicBpmnWorkflowStepProcessorMetaData.builder()
            .flowNodeBuilder(flowNodeBuilder)
            .workflowStep(firstWorkflowStep)
            .parentWorkflowStep(null)
            .workflowStepMap(workflowStepMap)
            .effectiveParentIdMap(effectiveParentIdMap)
            .dynamicActivityIdMap(dynamicActivityIdMap)
            .baseTemplateBpmnModelInstance(baseTemplateBpmnModelInstance)
            .build();
    dynamicBpmnProcessorHelper.processWorkflowStep(dynamicBpmnWorkflowStepProcessorMetaData,
        visitedNodeSet);
    flowNodeBuilder = dynamicBpmnWorkflowStepProcessorMetaData.getFlowNodeBuilder();

    dynamicBpmnProcessorHelper.addEndEventToBpmnModelInstance(
        definition.getWorkflowSteps(), flowNodeBuilder, baseTemplateBpmnModelInstance,
        dynamicActivityIdMap);

    return flowNodeBuilder.done();
  }

  private WorkflowStep getFirstWorkflowStep(List<WorkflowStep> workflowStepList) {

    List<String> childWorkflowSteps = new ArrayList<>();
    for (WorkflowStep workflowStep : workflowStepList) {
      if (!CollectionUtils.isEmpty(workflowStep.getNext())) {
        List<WorkflowStep.StepNext> stepNextList = workflowStep.getNext();
        for (WorkflowStep.StepNext stepNext : stepNextList) {
          childWorkflowSteps.add(stepNext.getWorkflowStepId());
        }
      }
    }

    WorkflowStep firstWorkflowStep = null;
    for (WorkflowStep workflowStep : workflowStepList) {
      if (!childWorkflowSteps.contains(String.valueOf(workflowStep.getId()))) {
        firstWorkflowStep = workflowStep;
        break;
      }
    }

    if (Objects.isNull(firstWorkflowStep)) {
      throw new WorkflowGeneralException(WorkflowError.CYCLIC_WORKFLOW_STEPS_NOT_ALLOWED);
    }

    return firstWorkflowStep;
  }

  /**
   * This method populates the effectiveParentId for all nodes in the workflowStepMap recursively on
   * the basis of effectiveParentWorkflowStep in the Create/Update definition payload. Effective
   * parent id will tell us that current workflow step will be connected to which node while
   * creating the elements of the bpmn.
   *
   * For multi condition custom approval dynamic Bpmns effective parent is determined as follows -
   * 1. If effectiveParentWorkflowStep is null -> effective parent = START_EVENT
   * 2. If workflowStep is action node -> effective parent = effective parent of the parent node
   * 3. If workflowStep is decision node and parent node is action -> effective parent = parent node
   * 4. If workflowStep is decision node and parent node is decision -> effective parent = effective parent of the parent node
   *
   * Eg : Consider the following multi-condition custom approval workflow :
   *
   *                             decisionElement
   *                                /        \
   *                 sendForApproval-1     decisionElement-1
   *                      /                     /         \
   *             decisionElement-2   sendForApproval-2   decisionElement-3
   *                /       \                               /         \
   *    sendForApproval-3   sendForApproval-4    sendForApproval-5    sendForApproval-6
   *
   * In the above workflow, the effective parent while creating the bpmn for the nodes will be -
   * decisionElement : START_EVENT
   * sendForApproval-1 : decisionElement
   * decisionElement-1 : decisionElement
   * decisionElement-2 : sendForApproval-1
   * sendForApproval-2 : decisionElement
   * decisionElement-3 : decisionElement
   * sendForApproval-3 : decisionElement-2
   * sendForApproval-4 : decisionElement-2
   * sendForApproval-5 : decisionElement
   * sendForApproval-6 : decisionElement
   *
   * @param workflowStep                WorkflowStep
   * @param effectiveParentWorkflowStep WorkflowStep
   * @param effectiveParentIdMap        Map<String, String>
   * @param workflowStepMap             Map<String, WorkflowStep>
   */
  private void populateEffectiveParentIdForANode(
      final WorkflowStep workflowStep,
      WorkflowStep effectiveParentWorkflowStep,
      Map<String, String> effectiveParentIdMap,
      final Map<String, WorkflowStep> workflowStepMap) {
    String workflowStepId = workflowStep.getId().getLocalId();

    // if effectiveParentIdMap already contains the workflowStepId, then no need to process it again
    if (effectiveParentIdMap.containsKey(workflowStepId)) {
      return;
    }

    if (Objects.isNull(effectiveParentWorkflowStep)) {
      effectiveParentIdMap.put(workflowStepId, BpmnComponentType.START_EVENT.getName());
      effectiveParentWorkflowStep = workflowStep;
    }

    // if current node is ActionStep or if current is ConditionStep but parent was ActionStep
    else if (MultiStepUtil.isActionStep(workflowStep)
        || (MultiStepUtil.isConditionStep(workflowStep)
        && MultiStepUtil.isActionStep(effectiveParentWorkflowStep)
        && !effectiveParentWorkflowStep.getActionGroup().isEmpty())) {
      effectiveParentIdMap.put(workflowStepId, effectiveParentWorkflowStep.getId().getLocalId());
      effectiveParentWorkflowStep = workflowStep;
    }

    // if current node is ConditionStep and parent was also ConditionStep, so merge both of them
    else if (MultiStepUtil.isConditionStep(workflowStep)
        && MultiStepUtil.isConditionStep(effectiveParentWorkflowStep)) {
      effectiveParentIdMap.put(workflowStepId, effectiveParentWorkflowStep.getId().getLocalId());
    }

    // recursively check for child nodes
    for (WorkflowStep.StepNext nextStep : workflowStep.getNext()) {
      if (!nextStep.isEmpty()) {
        String childId = nextStep.getWorkflowStepId();
        populateEffectiveParentIdForANode(
            DynamicBpmnUtil.getWorkflowStepFromId(childId, workflowStepMap),
            effectiveParentWorkflowStep,
            effectiveParentIdMap,
            workflowStepMap);
      }
    }
  }

  private void addExtensionElements(
      BpmnModelInstance bpmnModelInstance, BpmnModelInstance precannedDynamicBpmnModelInstance) {

    FlowNode startFlowNode =
        bpmnModelInstance.getModelElementById(
            CustomWorkflowUtil.findStartEventElement(bpmnModelInstance).getId());

    addExtensionElementToOutgoingNodes(
        startFlowNode, bpmnModelInstance, precannedDynamicBpmnModelInstance);
  }

  private void addExtensionElementToOutgoingNodes(
      FlowNode rootNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    // add extension elements to start event
    if (rootNode instanceof StartEvent) {
      startEventFlowNodeProcessor.addExtensionElements(
          rootNode, null, bpmnModelInstance, baseTemplateBpmnModelInstance);
    }

    Collection<SequenceFlow> outgoingSequenceFlows = rootNode.getOutgoing();
    for (SequenceFlow outgoingSequenceFlow : outgoingSequenceFlows) {

      FlowNode targetNode = outgoingSequenceFlow.getTarget();

      // add properties specific to certain nodes
      if (rootNode instanceof BusinessRuleTask) {
        DynamicBpmnUtil.addConditionToSequenceFlows(
            outgoingSequenceFlow, targetNode, bpmnModelInstance);
      }

      // add extension elements to other flow nodes
      DynamicBpmnFlowNodeProcessor flowNodeProcessor =
          dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(targetNode);
      flowNodeProcessor.addExtensionElements(
          targetNode, null, bpmnModelInstance, baseTemplateBpmnModelInstance);

      addExtensionElementToOutgoingNodes(
          targetNode, bpmnModelInstance, baseTemplateBpmnModelInstance);
    }
  }

  private List<DmnModelInstance> createDmnModelInstancesFromBaseDmnTemplate(
      BpmnModelInstance bpmnModelInstance, Definition definition, String hashSum, String actionKey) {
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

    DmnModelInstance baseTemplateDmnModelInstance =
        BpmnProcessorUtil.readDMNFile(
            DynamicBpmnUtil.getBaseTemplateResourcePath(actionKey, ModelType.DMN));

    final Collection<BusinessRuleTask> businessRuleTaskList =
        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class);

    int index = 1;
    for (final BusinessRuleTask businessRuleTask : businessRuleTaskList) {
      DmnModelInstance modelInstance = Dmn.createEmptyModel();
      Definitions definitions = DmnProcessorUtil.prepareDefinitionsMetaData(modelInstance);
      Decision decision =
          DmnProcessorUtil.prepareDecisionMetaDataFromBaseTemplate(
              modelInstance, baseTemplateDmnModelInstance);
      decision.setId(
          new StringBuilder(decision.getId()).append(HYPHEN)
              .append(index).append(UNDERSCORE)
              .append(hashSum).toString());
      DecisionTable decisionTable =
          DmnProcessorUtil.prepareDecisionTableMetaDataFromBaseTemplate(
              modelInstance, baseTemplateDmnModelInstance);
      Output output =
          DmnProcessorUtil.prepareOutputMetaDataFromBaseTemplate(
              modelInstance, baseTemplateDmnModelInstance);

      decisionTable.addChildElement(output);
      decision.addChildElement(decisionTable);
      definitions.addChildElement(decision);
      modelInstance.setDefinitions(definitions);
      dmnModelInstanceList.add(modelInstance);
      index++;
    }

    return dmnModelInstanceList;
  }

  /**
   * This method expands the COMPOSITE workflow steps into CONDITION and ACTION steps
   * and adds them to the workflowStepsList while the original COMPOSITE step is removed from workflowStepList
   * @param definition Definition
   */
  private void expandCompositeWorkflowSteps(Definition definition) {

    List<WorkflowStep> workflowStepsToBeAdded = new ArrayList<>();
    List<WorkflowStep> workflowStepsToBeRemoved = new ArrayList<>();

    // iterate over all the workflow steps and expand the composite workflow steps
    definition.getWorkflowSteps().forEach(workflowStep -> {
      if (MultiStepUtil.isCompositeStep(workflowStep)) {
        splitCompositeStepIntoConditionAndActionStep(definition, workflowStep,
            workflowStepsToBeAdded, workflowStepsToBeRemoved);
      }
    });

    // perform the add/remove operations on workflowStepList
    definition.getWorkflowSteps().addAll(workflowStepsToBeAdded);
    definition.getWorkflowSteps().removeAll(workflowStepsToBeRemoved);
  }

  /**
   * This method splits the COMPOSITE WORKFLOWSTEP into CONDITION and ACTION steps
   *
   * @param definition
   * @param compostieWorkflowStep
   * @param workflowStepsToBeAdded
   * @param workflowStepsToBeRemoved
   */
  private void splitCompositeStepIntoConditionAndActionStep(
      Definition definition,
      WorkflowStep compostieWorkflowStep,
      List<WorkflowStep> workflowStepsToBeAdded,
      List<WorkflowStep> workflowStepsToBeRemoved) {

    // copy to a new ACTION workflow step and remove the condition
    WorkflowStep actionWorkflowStep = new WorkflowStep(compostieWorkflowStep);
    actionWorkflowStep.setWorkflowStepCondition(null);
    actionWorkflowStep.setStepType(StepTypeEnum.ACTION);

    // copy to a new CONDITION workflow step and remove the action group
    WorkflowStep conditionWorkflowStep = new WorkflowStep(compostieWorkflowStep);
    conditionWorkflowStep.setActionGroup(null);
    conditionWorkflowStep.setStepType(StepTypeEnum.CONDITION);

    // create a new globalId for the condition step
    conditionWorkflowStep.setId(
        GlobalId.builder()
            .setRealmId(contextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(conditionWorkflowStep.getWorkflowStepCondition().getTypeId())
            .setLocalId(conditionWorkflowStep.getWorkflowStepCondition().getId().getLocalId() + HYPHEN + UUID.randomUUID())
            .build());

    // add newly created ACTION step as a child of the CONDITION step
    WorkflowStep.StepNext conditionStepNext = new WorkflowStep.StepNext();
    conditionStepNext.setLabel(NextLabelEnum.YES);
    conditionStepNext.setWorkflowStepId(actionWorkflowStep.getId().toString());
    conditionWorkflowStep.setNext(List.of(conditionStepNext));

    // find the parent of COMPOSITE step and replace COMPOSITE step with newly created CONDITION step in the stepNextList
    WorkflowStep parentWorkflowStep = MultiStepUtil.getParentWorkflowStep(definition.getWorkflowSteps(), compostieWorkflowStep.getId());
    if (Objects.nonNull(parentWorkflowStep)) {
      // Since the composite step can be either in the YES or NO path of the parent node
      // Now we will have to map the condition step to the label that the composite step is mapped to.
      // eg: (parentStep) --> (LABEL,compositeStep) will become (parentStep) --> (LABEL, conditionStep) --> (YES, actionStep)
      NextLabelEnum label = parentWorkflowStep.getNext().stream().filter(stepNext -> stepNext.getWorkflowStepId().equals(compostieWorkflowStep.getId().toString())).map(stepNext -> stepNext.getLabel()).findFirst().get();
      parentWorkflowStep.getNext().removeIf(stepNext -> stepNext.getWorkflowStepId().equals(compostieWorkflowStep.getId().toString()));
      WorkflowStep.StepNext parentStepNext = new WorkflowStep.StepNext();
      parentStepNext.setLabel(label);
      parentStepNext.setWorkflowStepId(conditionWorkflowStep.getId().toString());
      parentWorkflowStep.getNext().add(parentStepNext);
    }

    // add the new CONDITION and ACTION steps to workflowStepsToBeAddedList
    workflowStepsToBeAdded.add(conditionWorkflowStep);
    workflowStepsToBeAdded.add(actionWorkflowStep);

    // add the COMPOSITE step to workflowStepsToBeRemovedList
    workflowStepsToBeRemoved.add(compostieWorkflowStep);
  }

}
