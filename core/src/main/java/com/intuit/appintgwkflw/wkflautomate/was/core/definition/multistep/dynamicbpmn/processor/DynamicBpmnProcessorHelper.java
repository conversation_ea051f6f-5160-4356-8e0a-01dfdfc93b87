package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HashingUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnWorkflowStepProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnAdjacencyListData;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnNode;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.EndEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.DynamicBpmnWorkflowStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * This class processes the workflow steps and builds the bpmn elements dynamically using
 * create/update definition payload.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class DynamicBpmnProcessorHelper {

  private final DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  private final DynamicBpmnWorkflowStepProcessorFactory dynamicBpmnWorkflowStepProcessorFactory;

  private final EndEventFlowNodeProcessor endEventFlowNodeProcessor;

  /**
   * This public method is responsible for getting the appropriate processor bean from the
   * dynamicBpmnWorkflowStepProcessorFactory and creating the appropriate bpmn element and adding it
   * to the AbstractFlowNodeBuilder from the given dynamicBpmnWorkflowStepProcessorMetaData
   *
   * @param dynamicBpmnWorkflowStepProcessorMetaData DynamicBpmnWorkflowStepProcessorMetaData
   * @param visitedNodeSet                           Set<String>
   */
  public void processWorkflowStep(
      DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData,
      Set<String> visitedNodeSet) {

    WorkflowStep workflowStep = dynamicBpmnWorkflowStepProcessorMetaData.getWorkflowStep();

    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(workflowStep) || ObjectUtils.isEmpty(workflowStep.getId()),
        WorkflowError.INVALID_WORKFLOW_STEP);

    String workflowStepId = workflowStep.getId().getLocalId();

    if (visitedNodeSet.contains(workflowStepId)) {
      return;
    }

    DynamicBpmnWorkflowStepProcessor dynamicBpmnWorkflowStepProcessor =
        dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
            workflowStep.getStepType());

    dynamicBpmnWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);
    visitedNodeSet.add(workflowStepId);

    // Recursively build for child nodes
    workflowStep.getNext().stream()
        .filter(nextStep -> Objects.nonNull(nextStep) && !nextStep.isEmpty())
        .forEach(nextStep -> {
          String childId = nextStep.getWorkflowStepId();
          dynamicBpmnWorkflowStepProcessorMetaData.setWorkflowStep(
              DynamicBpmnUtil.getWorkflowStepFromId(
                  childId, dynamicBpmnWorkflowStepProcessorMetaData.getWorkflowStepMap()));
          dynamicBpmnWorkflowStepProcessorMetaData.setParentWorkflowStep(workflowStep);
          processWorkflowStep(dynamicBpmnWorkflowStepProcessorMetaData, visitedNodeSet);
        });
  }

  /**
   * This public method is responsible for creating an end event node and connecting all the leaf
   * nodes in the bpmn to it. We should not create multiple end events with different ids, instead
   * all leaf elements should be connected to a single end event node.
   *
   * @param workflowStepList              List<WorkflowStep>
   * @param flowNodeBuilder               AbstractFlowNodeBuilder
   * @param baseTemplateBpmnModelInstance BpmnModelInstance
   * @param dynamicActivityIdMap          Map<String, String>
   */
  public void addEndEventToBpmnModelInstance(
      List<WorkflowStep> workflowStepList,
      AbstractFlowNodeBuilder flowNodeBuilder,
      final BpmnModelInstance baseTemplateBpmnModelInstance,
      final Map<String, String> dynamicActivityIdMap) {
    // connect leaf node to endEvent from all explicit nodes
    AtomicBoolean isEndEventCreated = new AtomicBoolean(false);

    // list is reversed to avoid overlapping sequenceFlows and improve linting of dynamically created Bpmn
    Collections.reverse(workflowStepList);
    workflowStepList.stream()
        .filter(workflowStep ->
            // checking if current workflow step is a leaf node
            CollectionUtils.isEmpty(workflowStep.getNext()))
        .forEach(workflowStep -> {

          String workflowStepId = workflowStep.getId().getLocalId();

          // if endEvent is not created already, then create a new one otherwise just connect to current workflowStep
          if (!isEndEventCreated.get()) {
            endEventFlowNodeProcessor.createEndEvent(workflowStepId, flowNodeBuilder,
                baseTemplateBpmnModelInstance, dynamicActivityIdMap);
            isEndEventCreated.set(true);
          } else {
            flowNodeBuilder
                .moveToNode(dynamicActivityIdMap.get(workflowStepId))
                .connectTo(BpmnComponentType.END_EVENT.getName());
          }
        });
  }

  public void populateDynamicActivityIdMap(
      Map<String, String> dynamicActivityIdMap,
      final Map<String, String> effectiveParentIdMap,
      final Map<String, WorkflowStep> workflowStepMap,
      final Map<String, WorkflowStep> localIdWorkflowStepMap,
      final WorkflowStep workflowStep
  ) {

    DynamicBpmnWorkflowStepProcessor dynamicBpmnWorkflowStepProcessor =
        dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
            workflowStep.getStepType());

    String workflowStepId = workflowStep.getId().getLocalId();
    String dynamicWorkflowStepName = dynamicBpmnWorkflowStepProcessor.getDynamicActivityId(
        dynamicActivityIdMap,
        localIdWorkflowStepMap.get(effectiveParentIdMap.get(workflowStepId)));
    dynamicActivityIdMap.put(workflowStepId, dynamicWorkflowStepName);

    // recursively check for child nodes
    workflowStep.getNext().stream()
        .filter(nextStep -> Objects.nonNull(nextStep) && !nextStep.isEmpty())
        .forEach(nextStep -> {
          String childId = nextStep.getWorkflowStepId();
          populateDynamicActivityIdMap(
              dynamicActivityIdMap,
              effectiveParentIdMap,
              workflowStepMap,
              localIdWorkflowStepMap,
              DynamicBpmnUtil.getWorkflowStepFromId(childId, workflowStepMap));
        });
  }

  public String getHashValueForTemplateAdjacencyList(final BpmnModelInstance bpmnModelInstance,
                                                     final String actionKey) {
    FlowElement startEvent = BpmnProcessorUtil.findStartEventElement(bpmnModelInstance);

    Set<String> visitedNodes = new HashSet<>();

    List<DynamicBpmnNode> bpmnNodeList = new ArrayList<>();

    createAdjacencyList(bpmnNodeList, bpmnModelInstance, startEvent.getId(),
        visitedNodes);

    //Sorting the adjacency list so as to maintain one particular order for it so
    //that we can maintain one unique hash for one type of bpmn
    //For example: even if all sendForApproval after the dmn are in any order,
    //then will come in one particular order
    Collections.sort(bpmnNodeList);

    // add actionKey and bpmnNodeList to parent node for creating dynamicBpmnAdjacencyListData
    // actionKey is added to generate different hashSums for approval and reminder Bpmns with same structure
    DynamicBpmnAdjacencyListData dynamicBpmnAdjacencyListData =
            DynamicBpmnAdjacencyListData.builder()
                    .actionKey(actionKey)
                    .bpmnNodeList(bpmnNodeList)
                    .build();

    String adjacencyListString = ObjectConverter.toJson(dynamicBpmnAdjacencyListData);

    return HashingUtil.generateHashForString(adjacencyListString);
  }

  private void createAdjacencyList(List<DynamicBpmnNode> adjacencyList,
      final BpmnModelInstance bpmnModelInstance,
      String currentElementId, Set<String> visitedNodes) {

    if (visitedNodes.contains(currentElementId)) {
      return;
    }

    FlowNode currentBpmnNode = bpmnModelInstance.getModelElementById(currentElementId);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(currentBpmnNode);
    BpmnComponentType currentElementType = dynamicBpmnFlowNodeProcessor.getType();
    List<String> childNodes = new ArrayList<>();

    List<String> outgoingActivityIds =
        fetchOutgoingActivityIds(currentElementId, bpmnModelInstance);

    //sorting the activityIds so that they come in one particular order in the adjacency list
    Collections.sort(outgoingActivityIds);

    WorkflowLogger.logInfo(
        "step=createAdjacencyListForTemplateBpmn currentBpmnElementActivityId=%s "
            + "outgoingActivityIds=%s", currentElementId, outgoingActivityIds);

    DynamicBpmnNode currentNode = DynamicBpmnNode.builder()
        .childNodes(childNodes)
        .bpmnComponentType(currentElementType)
        .activityId(currentElementId)
        .build();
    visitedNodes.add(currentNode.getActivityId());

    adjacencyList.add(currentNode);

    outgoingActivityIds.forEach(activityId -> {
      createAdjacencyList(adjacencyList,
          bpmnModelInstance, activityId, visitedNodes);
      childNodes.add(activityId);
    });
  }

  private List<String> fetchOutgoingActivityIds(String currentCallActivityId,
      BpmnModelInstance bpmnModelInstance) {
    FlowNode flowNode = bpmnModelInstance.getModelElementById(currentCallActivityId);
    return flowNode.getOutgoing().stream()
        .map(SequenceFlow::getTarget)
        .map(BaseElement::getId)
        .collect(Collectors.toList());
  }
}
