package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.SubProcess;

/**
 * This interface includes methods to dynamically build the bpmn elements using base bpmn template.
 *
 * <AUTHOR>
 */
public interface DynamicBpmnFlowNodeProcessor {

  BpmnComponentType getType();

  void addExtensionElements(
      FlowNode flowNode,
      FlowNode baseTemplateFlowNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance);

  void addEventToSubProcess(
      FlowNode sourceNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance);

  void addImplicitEvents(
      FlowNode flowNode,
      FlowNode baseTemplateTargetNode,
      SequenceFlow outgoingSequenceFlow,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance);
}
