package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.EscalationEventDefinition;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.MessageEventDefinition;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for -
 * 1. adding extension elements to EndEvent element
 * 2. adding implicit elements to EndEvent element
 * 3. adding EndEvent element to existing subprocess
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class EndEventFlowNodeProcessor implements DynamicBpmnFlowNodeProcessor {

  private final DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  @Override
  public BpmnComponentType getType() {
    return BpmnComponentType.END_EVENT;
  }

  @Override
  public void addExtensionElements(
      FlowNode flowNode,
      FlowNode baseTemplateFlowNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    if (Objects.isNull(baseTemplateFlowNode)
        && Objects.nonNull(flowNode)
        && Objects.nonNull(baseTemplateBpmnModelInstance)) {
      baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById(flowNode.getId());
    }

    if (Objects.isNull(flowNode)
        && Objects.nonNull(baseTemplateFlowNode)
        && Objects.nonNull(bpmnModelInstance)) {
      flowNode = bpmnModelInstance.getModelElementById(baseTemplateFlowNode.getId());
    }

    if (Objects.isNull(flowNode) || Objects.isNull(baseTemplateFlowNode)) {
      WorkflowLogger.logError(
          "Unable to add extension elements as both EndEvent and BaseTemplateEndEvent found null.");
      return;
    }

    dynamicBpmnExtensionElementsHelper.addAllValidExtensionElements(
        flowNode, baseTemplateFlowNode, bpmnModelInstance);
  }

  @Override
  public void addEventToSubProcess(
      FlowNode sourceNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance) {

    Optional<EndEvent> precannedEndEvent =
        baseTemplateSubprocess.getChildElementsByType(EndEvent.class).stream().findFirst();

    if (precannedEndEvent.isEmpty()) {
      WorkflowLogger.logError(
          "Unable to add end event to subprocess as BaseTemplateSubProcessEndEvent is found empty.");
      return;
    }

    Optional<MessageEventDefinition> baseTemplateMessageEventDefinition =
        precannedEndEvent.get().getChildElementsByType(MessageEventDefinition.class).stream().findFirst();

    // handling different types of endEvents present in baseTemplate like - message, escalation, normal end event
    if (baseTemplateMessageEventDefinition.isPresent()) {
      subProcess
          .builder()
          .moveToNode(sourceNode.getId())
          .endEvent(precannedEndEvent.get().getId())
          .name(precannedEndEvent.get().getName())
          .messageEventDefinition()
          .message(DynamicBpmnUtil.getMessageNameFromFlowNode(precannedEndEvent.get()))
          .camundaType(baseTemplateMessageEventDefinition.get().getCamundaType())
          .camundaTopic(baseTemplateMessageEventDefinition.get().getCamundaTopic())
          .done();
    } else {
      subProcess
          .builder()
          .moveToNode(sourceNode.getId())
          .endEvent(precannedEndEvent.get().getId())
          .name(precannedEndEvent.get().getName())
          .done();
    }
  }

  @Override
  public void addImplicitEvents(
      FlowNode sourceFlowNode,
      FlowNode baseTemplateTargetFlowNode,
      SequenceFlow outgoingSequenceFlow,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    EndEvent baseTemplateEndEvent =
        baseTemplateBpmnModelInstance.getModelElementById(BpmnComponentType.END_EVENT.getName());

    Optional<MessageEventDefinition> baseTemplateMessageEventDefinition =
        baseTemplateEndEvent.getChildElementsByType(MessageEventDefinition.class).stream().findFirst();

    Optional<EscalationEventDefinition> baseTemplateEscalationEventDefinition =
        baseTemplateEndEvent.getChildElementsByType(EscalationEventDefinition.class).stream().findFirst();

    // handling different types of endEvents present in baseTemplate like - message, escalation, normal end event
    if (Objects.nonNull(outgoingSequenceFlow.getName())
        && Objects.nonNull(outgoingSequenceFlow.getTextContent())) {
      if (baseTemplateMessageEventDefinition.isPresent()) {
        sourceFlowNode
            .builder()
            .sequenceFlowId(outgoingSequenceFlow.getId())
            .condition(outgoingSequenceFlow.getName(), outgoingSequenceFlow.getTextContent())
            .endEvent(baseTemplateTargetFlowNode.getId())
            .name(baseTemplateTargetFlowNode.getName())
            .messageEventDefinition()
            .message(baseTemplateMessageEventDefinition.get().getMessage().getName())
            .camundaType(baseTemplateMessageEventDefinition.get().getCamundaType())
            .camundaTopic(baseTemplateMessageEventDefinition.get().getCamundaTopic());
      } else if (baseTemplateEscalationEventDefinition.isPresent()) {
        sourceFlowNode
            .builder()
            .sequenceFlowId(outgoingSequenceFlow.getId())
            .condition(outgoingSequenceFlow.getName(), outgoingSequenceFlow.getTextContent())
            .endEvent(baseTemplateTargetFlowNode.getId())
            .name(baseTemplateTargetFlowNode.getName())
            .escalation(baseTemplateEscalationEventDefinition.get().getEscalation().getEscalationCode());
      }
      else {
        sourceFlowNode
            .builder()
            .sequenceFlowId(outgoingSequenceFlow.getId())
            .condition(outgoingSequenceFlow.getName(), outgoingSequenceFlow.getTextContent())
            .endEvent(baseTemplateTargetFlowNode.getId());
      }
    } else {
      sourceFlowNode
          .builder()
          .sequenceFlowId(outgoingSequenceFlow.getId())
          .endEvent(baseTemplateTargetFlowNode.getId());
    }
  }

  /**
   * This method helps to add the endEvent node to workflowStepId node using the AbstractFlowNodeBuilder.
   * If MessageEventDefinition is present in the baseTemplate, then add it similarly to the current bpmn.
   *
   * @param workflowStepId String
   * @param flowNodeBuilder AbstractFlowNodeBuilder
   * @param baseTemplateBpmnModelInstance BpmnModelInstance
   */
  public void createEndEvent(
      String workflowStepId,
      AbstractFlowNodeBuilder flowNodeBuilder,
      BpmnModelInstance baseTemplateBpmnModelInstance,
      Map<String, String> dynamicActivityIdMap) {

    EndEvent baseTemplateEndEvent =
        baseTemplateBpmnModelInstance.getModelElementById(BpmnComponentType.END_EVENT.getName());

    Optional<MessageEventDefinition> optionalBaseTemplateMessageEventDefinition =
        baseTemplateEndEvent.getChildElementsByType(MessageEventDefinition.class).stream().findFirst();

    Optional<EscalationEventDefinition> optionalBaseTemplateEscalationEventDefinition =
        baseTemplateEndEvent.getChildElementsByType(EscalationEventDefinition.class).stream().findFirst();

    // handling different types of endEvents present in baseTemplate like - message, escalation, normal end event
    if (optionalBaseTemplateMessageEventDefinition.isPresent()) {
      flowNodeBuilder
          .moveToNode(dynamicActivityIdMap.get(workflowStepId))
          .endEvent(baseTemplateEndEvent.getId())
          .messageEventDefinition()
          .message(optionalBaseTemplateMessageEventDefinition.get().getMessage().getName())
          .camundaType(optionalBaseTemplateMessageEventDefinition.get().getCamundaType())
          .camundaTopic(optionalBaseTemplateMessageEventDefinition.get().getCamundaTopic());
    } else if (optionalBaseTemplateEscalationEventDefinition.isPresent()) {
      flowNodeBuilder
          .moveToNode(dynamicActivityIdMap.get(workflowStepId))
          .endEvent(baseTemplateEndEvent.getId())
          .escalation(optionalBaseTemplateEscalationEventDefinition.get().getEscalation().getEscalationCode());
    }

    else {
      flowNodeBuilder.moveToNode(dynamicActivityIdMap.get(workflowStepId))
          .endEvent(baseTemplateEndEvent.getId());
    }
  }
}
