package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for -
 * 1. adding extension elements to StartEvent element
 * 2. adding implicit elements to StartEvent element
 * 3. adding StartEvent element to existing subprocess
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StartEventFlowNodeProcessor implements DynamicBpmnFlowNodeProcessor {

  private final DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  @Override
  public BpmnComponentType getType() {
    return BpmnComponentType.START_EVENT;
  }

  @Override
  public void addExtensionElements(
      FlowNode flowNode,
      FlowNode baseTemplateFlowNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    if (Objects.isNull(baseTemplateFlowNode) && Objects.nonNull(flowNode)
        && Objects.nonNull(baseTemplateBpmnModelInstance)) {
      baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById(flowNode.getId());
    }

    if (Objects.isNull(flowNode) && Objects.nonNull(baseTemplateFlowNode)
        && Objects.nonNull(bpmnModelInstance)) {
      flowNode = bpmnModelInstance.getModelElementById(baseTemplateFlowNode.getId());
    }

    if (Objects.isNull(flowNode) || Objects.isNull(baseTemplateFlowNode)) {
      WorkflowLogger.logError(
          "Unable to add extension elements as both StartEvent and BaseTemplateStartEvent found null.");
      return;
    }

    dynamicBpmnExtensionElementsHelper.addAllValidExtensionElements(
        flowNode, baseTemplateFlowNode, bpmnModelInstance);
  }

  @Override
  public void addEventToSubProcess(
      FlowNode sourceNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance) {
    Optional<StartEvent> precannedStartEvent =
        baseTemplateSubprocess.getChildElementsByType(StartEvent.class).stream().findFirst();

    if (precannedStartEvent.isPresent()) {
      subProcess
          .builder()
          .embeddedSubProcess()
          .startEvent(precannedStartEvent.get().getId())
          .name(precannedStartEvent.get().getName())
          .escalation(DynamicBpmnUtil.getEscalationCodeFromFlowNode(precannedStartEvent.get()));
    }
  }

  @Override
  public void addImplicitEvents(
      FlowNode flowNode,
      FlowNode targetFlowNode,
      SequenceFlow outgoingSequenceFlow,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {
    // do nothing
  }
}
