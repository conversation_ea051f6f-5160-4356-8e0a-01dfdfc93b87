package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.impl.instance.CallActivityImpl;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for creating a Call Activity element in the Dynamic Bpmn Model and
 * adding to the AbstractFlowNodeBuilder.
 *
 * <AUTHOR>
 */
@Component
@NoArgsConstructor
public class CallActivityWorkflowStepProcessor implements DynamicBpmnWorkflowStepProcessor {

  private static final String elementType = "elementType";
  private static final String to = "_to_";

  @Override
  public StepTypeEnum getStepType() {
    return StepTypeEnum.ACTION;
  }

  @Override
  public void processDynamicBpmnStep(
      DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData) {

    WorkflowStep workflowStep = dynamicBpmnWorkflowStepProcessorMetaData.getWorkflowStep();
    String workflowStepId = workflowStep.getId().getLocalId();

    // if workflow steps contains Action then create a Call Activity
    if (MultiStepUtil.isActionStep(workflowStep)) {

      String effectiveParentId =
          dynamicBpmnWorkflowStepProcessorMetaData.getEffectiveParentIdMap().get(workflowStepId);

      // fetch the call activity from the base template bpmn model using the CTA action id
      CallActivityImpl baseTemplateCallActivityImpl =
          dynamicBpmnWorkflowStepProcessorMetaData
              .getBaseTemplateBpmnModelInstance()
              .getModelElementById(workflowStep.getActionGroup().getAction().getId().getLocalId());

      Map<String, String> callActivityPropertiesMap =
          BpmnProcessorUtil.getMapOfCamundaProperties(
              baseTemplateCallActivityImpl.getExtensionElements());

      // return if the element type is not explicit
      if (!DynamicBpmnUtil.isElementExplicit(callActivityPropertiesMap.get(elementType))) {
        return;
      }

      Map<String, String> dynamicActivityIdMap = dynamicBpmnWorkflowStepProcessorMetaData.getDynamicActivityIdMap();
      String dynamicWorkflowStepId = dynamicActivityIdMap.get(workflowStepId);
      String sequenceFlowId = new StringBuilder(BpmnComponentType.SEQUENCE_FLOW.toString()).append(
          to).append(dynamicWorkflowStepId).toString();

      dynamicBpmnWorkflowStepProcessorMetaData
          .getFlowNodeBuilder()
          .moveToNode(dynamicActivityIdMap.get(effectiveParentId))
          .sequenceFlowId(sequenceFlowId)
          .callActivity(dynamicWorkflowStepId)
          .name(baseTemplateCallActivityImpl.getName())
          .camundaCalledElementBinding(
              StringUtils.isEmpty(baseTemplateCallActivityImpl.getCamundaCalledElementBinding())
                  ? "latest"
                  : baseTemplateCallActivityImpl.getCamundaCalledElementBinding())
          .calledElement(baseTemplateCallActivityImpl.getCalledElement());
    }
  }

  /**
   * This method returns the dynamic activity id of the workflow step for CALL_ACTIVITY elements
   * present in the map. The map contains the workflowStepId as key and dynamic activity id as
   * value.
   *
   * @param dynamicActivityIdMap        - Map<String, String>
   * @param effectiveParentWorkflowStep - WorkflowStep
   */
  @Override
  public String getDynamicActivityId(Map<String, String> dynamicActivityIdMap,
      WorkflowStep effectiveParentWorkflowStep) {
    String prefix = BpmnComponentType.CALL_ACTIVITY.getName();

    int callActivityCounter =
        dynamicActivityIdMap.values().stream()
            .filter(value -> value.startsWith(prefix)).collect(Collectors.toList()).size() + 1;

    return new StringBuilder(prefix).append(WorkflowConstants.HYPHEN).append(callActivityCounter)
        .toString();
  }
}
