package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;

import java.util.Map;

/**
 * This interface includes methods to process the workflow steps and build the bpmn elements using
 * workflow steps from create/update definition payload dynamically.
 *
 * <AUTHOR>
 */
public interface DynamicBpmnWorkflowStepProcessor {

  /**
   * This method returns the step type of the workflow step.
   *
   * @return StepTypeEnum
   */
  StepTypeEnum getStepType();

  /**
   * This method is responsible for creating a bpmn element in the Dynamic Bpmn Model and adding to
   * the AbstractFlowNodeBuilder from given dynamicBpmnWorkflowStepProcessorMetaData .
   *
   * @param dynamicBpmnWorkflowStepProcessorMetaData - DynamicBpmnWorkflowStepProcessorMetaData
   */
  void processDynamicBpmnStep(
      DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData);

  /**
   * This method returns the dynamic activity id of the workflow step on the basis of number of BusinessRuleTask
   * or CallActivity elements already present in the map.
   * The map contains the workflowStepId as key and dynamic activity id as value.
   *
   * @param dynamicActivityIdMap - Map<String, String>
   * @param effectiveParentWorkflowStep - WorkflowStep
   */
  String getDynamicActivityId(Map<String, String> dynamicActivityIdMap, WorkflowStep effectiveParentWorkflowStep);
}
