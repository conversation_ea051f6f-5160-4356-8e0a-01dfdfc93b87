package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;

import java.util.List;
import java.util.Map;

/**
 * This class has default implementation for methods to handle composite step for read definition.
 *
 * <AUTHOR>
 */
public abstract class ReadCompositeStepHandler {

  /**
   * This function handles creation a workflow step and adds it to the list only in case of non-composite step
   *
   * @param currentWorkflowStepId
   * @param workflowStep
   * @param stepNexts
   * @param workflowStepCondition
   * @param multiStepWorkflowEntity
   * @param parentStep
   */
  public void handleCompositeStep(@SuppressWarnings("rawtypes") GlobalId currentWorkflowStepId,
      WorkflowStep workflowStep,
      List<WorkflowStep.StepNext> stepNexts,
      WorkflowStepCondition workflowStepCondition,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      WorkflowStep parentStep){
    WorkflowLogger.logInfo(
        "step=handleCompositeStep No need to create a new workflow step since it is a composite step globalId=%s",
        currentWorkflowStepId);
  }

  /**
   * This function adds the workflowStepConditon to the compositeStepIdToWorkflowStepConditionMap map to be
   * used for creation of composite step only in case of composite step.
   *
   * @param nextObj
   * @param compositeStepIdToWorkflowStepConditionMap
   * @param compositeStepId
   * @param workflowStepCondition
   * @param parentStep
   */
  public void addToMap(WorkflowStep.StepNext nextObj,
      Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap,
      @SuppressWarnings("rawtypes") GlobalId compositeStepId,
      WorkflowStepCondition workflowStepCondition,
      WorkflowStep parentStep, GlobalId conditionStepId){
    WorkflowLogger.logInfo(
        "step=addToMap No need to add to map since not a composite step globalId=%s",
        compositeStepId);
  }
}
