package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * This class involves the functions to map activity Ids for CallActivity bpmn element
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CallActivityOutgoingActivityMapper implements OutgoingActivityMapper {

  @Override
  public List<Pair<String, String>> fetchOutgoingActivityIds(String currentCallActivityId,
      DefinitionInstance definitionInstance) {
    List<Pair<String, String>> outgoingActivityIds = new ArrayList<>();
    try {
      CallActivity callActivity = definitionInstance.getBpmnModelInstance()
          .getModelElementById(currentCallActivityId);

      callActivity.getOutgoing().forEach(nextBpmnElement -> {
        if (nextBpmnElement.getTarget() instanceof CallActivity) {
          outgoingActivityIds.add(new Pair<>(nextBpmnElement.getTarget().getId(),
              ((CallActivity) nextBpmnElement.getTarget()).getCalledElement()));
        } else if (nextBpmnElement.getTarget() instanceof BusinessRuleTask) {
          outgoingActivityIds.add(new Pair<>(nextBpmnElement.getTarget().getId(), null));
        } else if (nextBpmnElement.getTarget() instanceof EndEvent) {
            return;
        } else {
            throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_MODEL_INSTANCE);
        }
      });
    } catch (final Exception exception) {
      WorkflowLogger.logError("step=fetchOutgoingActivityIdsForCallActivity " +
          "currentCallActivityId=%s", currentCallActivityId);
      throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_MODEL_INSTANCE, exception);
    }

    return outgoingActivityIds;
  }
}
