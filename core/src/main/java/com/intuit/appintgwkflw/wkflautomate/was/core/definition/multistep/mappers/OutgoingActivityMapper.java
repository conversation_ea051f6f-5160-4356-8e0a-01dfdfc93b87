package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import java.util.List;
import org.javatuples.Pair;

/**
 * This interface is build to handle the various activity mappers depending upon the type of
 * definition
 *
 * <AUTHOR>
 */
public interface OutgoingActivityMapper {

  /**
   * This function is used to return all the outgoing activityIds corresponding to the id of the
   * current element(BusinessRuleTask)
   *
   * @param currentElementId
   * @param definitionInstance
   * @return
   */
  List<Pair<String, String>> fetchOutgoingActivityIds(String currentElementId,
      DefinitionInstance definitionInstance);
}
