package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import static com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants.PROJECT_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants.TASK_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CLOSE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CONFIG_PARAMETER_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiWorkflowStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProjectType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.PlaceholderParameterAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.PlaceholderParameterDetailsAttribute;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class involves the functions to extract and attributes for a particular workflowStep during
 * create/read definition.
 *
 * <AUTHOR> stripathy1
 */
@Component
@AllArgsConstructor
public class CreateMultiActionStepProcessor implements MultiWorkflowStepHandler {

  private final FilterParameterExtractorUtil filterParameterExtractorUtil;

  @Override
  public Map<String, String> processWorkflowStep(String actionElementId,
      WorkflowStep workflowStep,
      DefinitionInstance definitionInstance,
      List<String> activityIds,
      Set<String> visitedWorkflowSteps) {

    WorkflowLogger.logInfo(
        "step=processActionWorkflowStep, actionElementId=%s activityIds=%s",
        actionElementId, activityIds);
    Map<String, List<PlaceholderParameterAttribute>> placeholderParameterAttributes = (Map<String, List<PlaceholderParameterAttribute>>)
        definitionInstance.getPlaceholderValue().get(CONFIG_PARAMETER_VARIABLES);

    Action workflowStepAction = workflowStep.getActionGroup().getAction();

    // ParentActionId is sendForApproval - for approval workflows
    String parentActionId = workflowStepAction.getId().getLocalId();

    Map<String, List<String>> parentFieldValues = workflowStepAction.getParameters().stream()
        .collect(Collectors.toMap(InputParameter::getParameterName,
            inputParameter -> inputParameter.getFieldValues()));

    String currentWorkflowStepId = String.valueOf(workflowStep.getId());

    // Substitute for parentAction
    ActivityInstance activityInstance = createActionUserAttributes(workflowStepAction,
        placeholderParameterAttributes.get(parentActionId), Collections.emptyMap(),
        definitionInstance,
        currentWorkflowStepId);

    for (Action subAction : workflowStepAction.getSubActions()) {
      //  Substitute for subActions
      String actionId = subAction.getId().getLocalId();
      ActivityInstance subActivityInstance = createActionUserAttributes(subAction,
          placeholderParameterAttributes.get(actionId), parentFieldValues, definitionInstance,
          currentWorkflowStepId);
      activityInstance.getChildActivityInstances().put(actionId, subActivityInstance);
    }

    // Update the placeholders.
    definitionInstance.getActivityInstanceMap().put(actionElementId, activityInstance);

    // Return ActivityId to StepId for MultiStep workflows. In MultiCondition, it should return emptyMa
    if (ObjectUtils.isEmpty(workflowStep.getNext()) || ObjectUtils.isEmpty(activityIds)) {
      return Collections.emptyMap();
    }

    LinkedList<String> activityIdList = (LinkedList<String>) activityIds;
    return workflowStep.getNext().stream().
        collect(Collectors.toMap(next -> next.getWorkflowStepId(),
            next -> activityIdList.removeFirst()));
  }

  /**
   * @param workflowStepAction            ParentActions or its subAction.
   * @param placeholderParameterAttribute
   * @param parentFieldValues
   * @return ActivityInstance for that Action
   */
  private ActivityInstance createActionUserAttributes(
      Action workflowStepAction,
      List<PlaceholderParameterAttribute> placeholderParameterAttribute,
      Map<String, List<String>> parentFieldValues,
      DefinitionInstance definitionInstance,
      String currentWorkflowStepId) {
    Map<String, Object> actionUserVariables = new HashMap<>();

    Map<String, Object> actionParameters = substitutePlaceholder(workflowStepAction,
        placeholderParameterAttribute, parentFieldValues, definitionInstance, currentWorkflowStepId);
    actionUserVariables.put(ACTION_SELECTED,
        BooleanUtils.toBoolean(workflowStepAction.isSelected()));
    actionUserVariables.put(ACTION_PARAMETERS, actionParameters);

    return ActivityInstance.builder().userAttributes(actionUserVariables)
        .childActivityInstances(new HashMap<>())
        .build();
  }

  /**
   * Replaces field values as: 1) User provided workflow step field values over config field values.
   * 2) Parent Action Parameter Field Values override to subAction parameter fieldValues.
   *
   * @param workflowStepAction
   * @param placeholderParameterAttributes
   * @param parentParameters               -> its Empty for parentAction
   * @return
   */
  private Map<String, Object> substitutePlaceholder(
      Action workflowStepAction,
      List<PlaceholderParameterAttribute> placeholderParameterAttributes,
      Map<String, List<String>> parentParameters,
      DefinitionInstance definitionInstance,
      String currentWorkflowStepId) {

    Map<String, Object> actionParameters = new HashMap<>();
    Map<String, List<String>> workflowStepActionParameterMap = workflowStepAction.getParameters()
        .stream()
        .collect(Collectors.toMap(InputParameter::getParameterName,
            inputParameter -> inputParameter.getFieldValues()));

    for (PlaceholderParameterAttribute placeholderParameterAttribute : placeholderParameterAttributes) {
      String parameterName = placeholderParameterAttribute.getParameterName();
      for (PlaceholderParameterDetailsAttribute placeholderParameterDetailsAttribute : placeholderParameterAttribute.getPlaceholderParameterDetailsAttributes()) {
        Map<String, String> helpVariables = placeholderParameterDetailsAttribute.getHelpVariableMap();
        List<String> fieldValues = placeholderParameterDetailsAttribute.getFieldValues();

        fieldValues = workflowStepActionParameterMap.getOrDefault(parameterName, fieldValues);

        // Override Parent Action Placeholders to subAction.
        fieldValues = parentParameters.getOrDefault(parameterName, fieldValues);

        // Substitute HelpVariables
        fieldValues = CustomWorkflowUtil.replaceHelpVariableInFieldValuesFromConfigMap(
            helpVariables, fieldValues);

        // update fieldValues for ProjectType and TaskType for Custom Reminder Workflows.
        fieldValues = handleProjectTypeAndTaskType(definitionInstance, parentParameters,
            parameterName, fieldValues);

        actionParameters.put(parameterName, Collections.singletonMap(FIELD_VALUE, fieldValues));
      }
    }

    // add once only for parent action
    if (MapUtils.isEmpty(parentParameters)) {
      addFilterConditionsForEssFlow(definitionInstance, actionParameters, currentWorkflowStepId);
    }

    if (actionParameters.containsKey(WorkflowConstants.IS_RECURRING_ENABLED)) {
      Map<String, Object> isRecurringEnabled = (Map<String, Object>) actionParameters.get(WorkflowConstants.IS_RECURRING_ENABLED);
      if (isRecurringEnabled.containsKey(FIELD_VALUE) && isRecurringEnabled.get(FIELD_VALUE) instanceof List) {
        List<String> fieldValue = (List<String>) isRecurringEnabled.get(FIELD_VALUE);
        if (fieldValue.contains("true")) {
          WorkflowLogger.logInfo("Creating recurring action for activityId=%s step=substitutePlaceholder",
              currentWorkflowStepId);
        }
      }
    }

    return actionParameters;
  }

  /**
   * Handle ProjectType and TaskType for Custom Reminder Workflows.
   *
   * @param definitionInstance
   * @param parentParameters
   * @param parameterName
   * @param fieldValues
   * @return
   */
  private List<String> handleProjectTypeAndTaskType(
      DefinitionInstance definitionInstance,
      Map<String, List<String>> parentParameters,
      String parameterName,
      List<String> fieldValues) {

    // return if not a custom reminder workflow
    if (!CustomWorkflowUtil.isCustomReminderWorkflow(definitionInstance.getDefinition())) {
      return fieldValues;
    }

    if (PROJECT_TYPE.equalsIgnoreCase(parameterName)
        || TASK_TYPE.equalsIgnoreCase(parameterName)
        || CLOSE_TASK.equalsIgnoreCase(parameterName)) {

      if (MapUtils.isEmpty(parentParameters) ||
          CollectionUtils.isEmpty(parentParameters.get(FILTER_CLOSE_TASK_CONDITIONS))) {
        WorkflowLogger.logError(
            "step=handleProjectTypeAndTaskType, FILTER_CLOSE_TASK_CONDITIONS is null in parent parameters.");
        throw new WorkflowGeneralException(WorkflowError.FILTER_CLOSE_TASK_ACTION_NOT_FOUND);
      }

      ProjectType project =
          ProjectType.getProject(
              definitionInstance.getDefinition().getRecordType(),
              CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definitionInstance.getDefinition()),
              parentParameters.get(FILTER_CLOSE_TASK_CONDITIONS).get(0));

      // get project name or task name based on parameterName
      switch (parameterName) {
        case Constants.PROJECT_TYPE:
          return Collections.singletonList(project.name());
        case Constants.TASK_TYPE:
          return Collections.singletonList(project.getTaskType().name());
        case WorkflowConstants.CLOSE_TASK:
          return Collections.singletonList(project.getCloseTaskType().name());
        default:
          break;
      }
    }

    return fieldValues;
  }

  /**
   * Add filter conditions for ESS flow for each action. Filter conditions are needed to fetch
   * txnList from AppConnect in ESS based workflows.
   *
   * @param definitionInstance
   * @param actionParameters
   */
  private void addFilterConditionsForEssFlow(
      DefinitionInstance definitionInstance,
      Map<String, Object> actionParameters,
      String currentWorkflowStepId) {

    if (!CustomWorkflowUtil.isCustomReminderWorkflow(definitionInstance.getDefinition())) {
      return;
    }

    WorkflowLogger.logInfo("Extracting parameterDetails for workflowStepId=%s, step=addFilterConditionsForEssFlow",
        currentWorkflowStepId);

    Map<String, HandlerDetails.ParameterDetails> filterParameterDetails =
        filterParameterExtractorUtil.getFilterParameterDetails(definitionInstance.getDefinition(),
            definitionInstance.getWorkflowStepMap(), currentWorkflowStepId);

    filterParameterDetails.keySet().forEach(key -> {
      HandlerDetails.ParameterDetails parameterDetails = filterParameterDetails.get(key);
      List<String> fieldValue = parameterDetails.getFieldValue();
      actionParameters.put(key, Collections.singletonMap(FIELD_VALUE, fieldValue));
    });
  }

}
