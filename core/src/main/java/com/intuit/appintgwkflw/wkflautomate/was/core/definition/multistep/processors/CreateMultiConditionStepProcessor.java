package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiConditionBusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiWorkflowStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.springframework.stereotype.Component;

/**
 * This class processes the condition workflow step using create/update definition payload.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CreateMultiConditionStepProcessor implements MultiWorkflowStepHandler {

  private final MultiConditionBusinessRuleTaskHandler multiConditionBusinessRuleTaskHandler;

  public Map<String, String> processWorkflowStep(
      String dmnElementId,
      WorkflowStep workflowStep,
      DefinitionInstance definitionInstance,
      List<String> activityIds,
      Set<String> visitedDmnRootWorkflowStepIds) {

    WorkflowLogger.logInfo(
        "step=processConditionWorkflowStep, dmnElementId=%s, activityIds=%s",
        dmnElementId, activityIds);

    Map<String, String> activityStepIdMap = null;

    //Either it is the root workflow step or any other step containing workflow condition
    if (visitedDmnRootWorkflowStepIds.isEmpty() ||
        !visitedDmnRootWorkflowStepIds.contains(workflowStep.getId().toString())) {

      visitedDmnRootWorkflowStepIds.add(String.valueOf(workflowStep.getId()));

      DmnModelInstance dmnModelInstance = CustomWorkflowUtil
          .getDmnModelInstance(definitionInstance, dmnElementId);

      activityStepIdMap = multiConditionBusinessRuleTaskHandler
          .workflowDecisionHandler(dmnModelInstance, workflowStep, definitionInstance,
               activityIds);

      ActivityInstance activityInstance = ActivityInstance.builder()
          .dmnModelInstance(dmnModelInstance).build();
      definitionInstance.getActivityInstanceMap().put(dmnElementId, activityInstance);

    }
    return activityStepIdMap;
  }
}
