package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Description;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.camunda.bpm.model.dmn.instance.Text;
import org.springframework.stereotype.Component;

/**
 * This class is the implementation of BusinessRuleProcessor in the case when there is no condition
 * present in the definition. So, in that case it creates a dmn with one input column(index) and one
 * output column(decisionResult). And it will have one row where index value will be 0 and
 * decisionResult will contain the activity id of the action that is there in th payload
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class EmptyConditionRuleLineProcessor implements RuleLineProcessor {

  private final CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  private final FeatureFlagManager featureFlagManager;

  // This function is used to create index column in dmn and set the data type of
  // output column as string
  @Override
  public DecisionTable createDmnHeaders(DmnModelInstance dmnModelInstance,
      WorkflowStep firstWorkflowStep, DefinitionInstance definitionInstance) {
    WorkflowLogger.logInfo(
        "step=createDmnHeadersForEmptyConditionDmn");

    DecisionTable decisionTable = CustomWorkflowUtil.getDecisionTable(dmnModelInstance);
    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());
    customWorkflowDecisionHandler.addDecisionInput(
        new AtomicInteger(WorkflowConstants.START_INDEX_VALUE),
        WorkflowConstants.INDEX_COLUMN,
        WorkflowConstants.INTEGER_TYPE_CAMUNDA,
        dmnModelInstance,
        decisionTable,
        useFeelExpr);

    CustomWorkflowUtil.setOutputDmnColumnType(dmnModelInstance);

    return decisionTable;
  }

  @Override
  public Map<String, Map<String, DmnHeader>> buildDmnHeadersMap(DecisionTable decisionTable) {
    return Collections.emptyMap();
  }

  @Override
  public void buildDmn(DefinitionInstance definitionInstance,
      RuleLineInstance ruleLineInstance,
      List<String> activityIds,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      Map<String, String> stepIdToActivityIdMap,
      DmnModelInstance dmnModelInstance) {
    String activityId = ((LinkedList<String>) activityIds).pollFirst();

    stepIdToActivityIdMap.put(ruleLineInstance.getWorkflowStepId(), activityId);

    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());
    Rule rule = processRuleLine(
        ruleLineInstance.getCurrentIndexColumnValue(), activityId, dmnModelInstance, useFeelExpr);

    ruleLineInstance.addRules(rule);
  }

  /**
   * This function creates a camunda dmn rule for each ruleline of payload that is passed to it.
   *
   * @param nodeIndex
   * @param outputResult
   * @param dmnModelInstance
   * @return
   */
  private Rule processRuleLine(
      int nodeIndex,
      String outputResult,
      DmnModelInstance dmnModelInstance, boolean useFeelExpr) {
    // Create a dmn rule for each ruleLine
    Rule dmnRule = dmnModelInstance.newInstance(Rule.class);

    // Description {Annotations in dmn goes here}
    Description description = dmnModelInstance.newInstance(Description.class);

    InputEntry inputEntry = createAndSetInputEntry(dmnModelInstance, nodeIndex, useFeelExpr);

    List<OutputEntry> outputEntries = createAndSetOutputEnteries(dmnModelInstance, outputResult);

    // Add input entries to dmnRule
    dmnRule.getInputEntries().add(inputEntry);

    // Add annotation element
    dmnRule.setDescription(description);

    dmnRule.getOutputEntries().addAll(outputEntries);
    return dmnRule;
  }

  private List<OutputEntry> createAndSetOutputEnteries(DmnModelInstance dmnModelInstance,
      String outputResult) {
    DecisionTable decisionTable = CustomWorkflowUtil.getDecisionTable(dmnModelInstance);
    List<OutputEntry> outputEntries =
        IntStream.range(0, decisionTable.getOutputs().size())
            .mapToObj(
                idx -> {
                  OutputEntry outputEntry = dmnModelInstance.newInstance(OutputEntry.class);
                  Text text = dmnModelInstance.newInstance(Text.class);
                  text.setTextContent(String.format("'%s'", outputResult));
                  outputEntry.setText(text);
                  return outputEntry;
                })
            .collect(Collectors.toList());

    return outputEntries;
  }

  private InputEntry createAndSetInputEntry(DmnModelInstance dmnModelInstance, int nodeIndex,
      boolean useFeelExpr) {
    InputEntry inputEntry = dmnModelInstance.newInstance(InputEntry.class);
    inputEntry.setText(dmnModelInstance.newInstance(Text.class));
    inputEntry.setExpressionLanguage(
        CustomWorkflowUtil.getInputExpressionLanguage(useFeelExpr));
    inputEntry.getText()
        .setTextContent(String.format("%s == %s", WorkflowConstants.INDEX_COLUMN, nodeIndex));
    return inputEntry;
  }
}
