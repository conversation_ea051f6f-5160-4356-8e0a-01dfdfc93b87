package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_FIELD_PREFIX;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiConditionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomAttributesUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringExpressionHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.StepNext;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Description;
import org.camunda.bpm.model.dmn.instance.DmnModelElementInstance;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.camunda.bpm.model.dmn.instance.Text;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;


/**
 * This class implements all the functions that are used to parse the conditions present in the
 * current subtree of given root workflow step(starting from the given root workflow step till any
 * action is encountered for each branch)
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class MultiConditionRuleLineProcessor implements RuleLineProcessor {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final BusinessRuleTaskHandler businessRuleTaskHandler;
  private final CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  private final MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor;
  private final FeatureFlagManager featureFlagManager;

  @Override
  public DecisionTable createDmnHeaders(
      DmnModelInstance dmnModelInstance, WorkflowStep firstWorkflowStep,
      DefinitionInstance definitionInstance) {
    WorkflowLogger.logInfo(
        "step=createMultiConditionWorkflows maxChildren=%s", 1);

    List<Attribute> dmnHeaders = filterSupportedAttributesForDMN(definitionInstance.getDefinition(),
        firstWorkflowStep, definitionInstance.getWorkflowStepMap());

    multiConditionPlaceholderExtractor.createProcessVariables(definitionInstance, dmnHeaders);

    DecisionTable decisionTable = CustomWorkflowUtil.getDecisionTable(dmnModelInstance);
    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());
    addImplicitColumn(dmnModelInstance, decisionTable, new AtomicInteger(0), useFeelExpr);

    CustomWorkflowUtil.setOutputDmnColumnType(dmnModelInstance);
    // It adds decision column headers in the dmn
    customWorkflowDecisionHandler.addDecisionInputs(dmnModelInstance, decisionTable,
        dmnHeaders, useFeelExpr);

    return decisionTable;
  }


  /**
   * It adds index column to the dmn table which is used to maintain the tree structure of the dmn
   *
   * @param modelInstance
   * @param parentElement
   * @param columnCounter
   */
  private void addImplicitColumn(DmnModelInstance modelInstance,
      DmnModelElementInstance parentElement,
      AtomicInteger columnCounter,
      boolean useFeelExpr) {
    // adding the index column
    customWorkflowDecisionHandler.addDecisionInput(
        new AtomicInteger(columnCounter.getAndIncrement()),
        WorkflowConstants.INDEX_COLUMN,
        WorkflowConstants.INTEGER_TYPE_CAMUNDA,
        modelInstance,
        parentElement,
        useFeelExpr);
  }

  //Recursive function for creating dmn tree by adding the rules in dmn table while maintaining
  // the index column and it returns a list of actionIds
  @Override
  public void buildDmn(
      DefinitionInstance definitionInstance,
      RuleLineInstance ruleLineInstance,
      List<String> activityIds,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      Map<String, String> stepIdToActivityIdMap,
      DmnModelInstance dmnModelInstance) {
    Map<String, WorkflowStep> workflowStepMap = definitionInstance.getWorkflowStepMap();

    if (MultiStepUtil.isActionStep(workflowStepMap.get(ruleLineInstance.getWorkflowStepId()))) {
      return;
    }

    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());

    int leftChildIndex = MultiStepUtil.computeLeftChildIndex(
        ruleLineInstance.getCurrentIndexColumnValue());
    int rightChildIndex = MultiStepUtil.computeRightChildIndex(
        ruleLineInstance.getCurrentIndexColumnValue());

    Pair<String, String> pathResults =
        computeOutputsOfYesAndNoPaths(leftChildIndex, rightChildIndex,
            workflowStepMap, ruleLineInstance.getWorkflowStepId(), activityIds,
            stepIdToActivityIdMap);

    // Add the true path rule line in the dmn
    workflowStepMap.get(ruleLineInstance.getWorkflowStepId()).getWorkflowStepCondition()
        .getRuleLines()
        .forEach(
            ruleLine -> {
              Rule rule = processRuleLine(
                  ruleLineInstance.getCurrentIndexColumnValue(), pathResults.getValue0(),
                  ruleLine, dmnModelInstance, attributeToHeaderMap, useFeelExpr);

              ruleLineInstance.addRules(rule);

            });

    // Add the false path rule line in the dmn
    Rule rule = processRuleLine(
        ruleLineInstance.getCurrentIndexColumnValue(), pathResults.getValue1(),
        new RuleLine().rules(new ArrayList<>()),
        dmnModelInstance, attributeToHeaderMap, useFeelExpr);
    ruleLineInstance.addRules(rule);

    multiConditionPlaceholderExtractor.extractPlaceholderValue(definitionInstance,
        ruleLineInstance.getWorkflowStepId(), pathResults);

    // Calling the function recursively for the left and right child to create the
    // rule lines for the child nodes in the dmn
    definitionInstance.getWorkflowStepMap().get(ruleLineInstance.getWorkflowStepId())
        .getNext().forEach(nextChild -> {
              RuleLineInstance nextRuleLineInstance = buildRuleEntity(nextChild, leftChildIndex,
                  rightChildIndex, ruleLineInstance.getRules());
              buildDmn(definitionInstance, nextRuleLineInstance, activityIds, attributeToHeaderMap,
                  stepIdToActivityIdMap, dmnModelInstance);
            }
        );
  }

  /**
   * This function is used to compute the outputs of yes and no path results. The output can have 3
   * types of values: 1. ActionStepId : corresponding to the action that needs to be executed 2.
   * Index value : corresponding to the index of next row which needs to be evaluated 3. false :
   * corresponds to the auto-approval case
   *
   * @param leftChildIndex
   * @param rightChildIndex
   * @param workflowStepMap
   * @param workflowStepId
   * @param activityIds
   * @param stepIdToActivityIdMap
   * @return
   */
  private Pair<String, String> computeOutputsOfYesAndNoPaths(int leftChildIndex,
      int rightChildIndex,
      Map<String, WorkflowStep> workflowStepMap,
      String workflowStepId,
      List<String> activityIds,
      Map<String, String> stepIdToActivityIdMap) {

    // Setting the result for yes path to index of left child and the result of no path result
    // to index of right child
    String yesPathResult = String.valueOf(leftChildIndex);
    String noPathResult = String.valueOf(rightChildIndex);

    Boolean yesPathPresent = Boolean.FALSE;
    Boolean noPathPresent = Boolean.FALSE;

    // If the result of any path is an action item, then update the path result to the activity
    // id of that action workflow step.
    List<WorkflowStep.StepNext> nexts = workflowStepMap.get(workflowStepId).getNext();
    for (WorkflowStep.StepNext next : nexts) {

      boolean isActionStep = MultiStepUtil.isActionStep(
          workflowStepMap.get(next.getWorkflowStepId()));

      if (isActionStep) {
        stepIdToActivityIdMap.putIfAbsent(next.getWorkflowStepId(),
            ((LinkedList<String>) activityIds).pollFirst());
      }

      if (MultiStepUtil.isYesLabel(next.getLabel())) {
        yesPathPresent = Boolean.TRUE;
        yesPathResult =
            isActionStep ? stepIdToActivityIdMap.get(next.getWorkflowStepId()) : yesPathResult;
      } else if (MultiStepUtil.isNoLabel(next.getLabel())) {
        noPathPresent = Boolean.TRUE;
        noPathResult =
            isActionStep ? stepIdToActivityIdMap.get(next.getWorkflowStepId()) : noPathResult;
      }
    }

    // If yesPath or noPath are not present, then setting false in their corresponding pathResults
    yesPathResult = yesPathPresent ? yesPathResult : String.valueOf(Boolean.FALSE);
    noPathResult = noPathPresent ? noPathResult : String.valueOf(Boolean.FALSE);

    return new Pair<>(yesPathResult, noPathResult);
  }

  /**
   * This function creates a camunda dmn rule for each ruleline of payload that is passed to it.
   *
   * @param nodeIndex
   * @param outputResult
   * @param ruleLine
   * @param dmnModelInstance
   * @param attributeToHeaderMap
   * @return
   */
  protected Rule processRuleLine(
      int nodeIndex,
      String outputResult,
      RuleLine ruleLine,
      DmnModelInstance dmnModelInstance,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      boolean useFeelExpr) {
    // Create a dmn rule for each ruleLine
    Rule dmnRule = dmnModelInstance.newInstance(Rule.class);

    // Description {Annotations in dmn goes here}
    Description description = dmnModelInstance.newInstance(Description.class);

    Map<String, DmnHeader> inputAttributeToHeaderMap =
        attributeToHeaderMap.get(WorkflowConstants.INPUT);
    Map<String, DmnHeader> outputAttributeToHeaderMap =
        attributeToHeaderMap.get(WorkflowConstants.OUTPUT);

    List<InputEntry> inputEntries =
        createInputEntries(dmnModelInstance, inputAttributeToHeaderMap, useFeelExpr);

    List<OutputEntry> outputEntries =
        createAndSetOutputEntries(
            dmnModelInstance, outputAttributeToHeaderMap, outputResult, useFeelExpr);

    //setting the input entry values
    ruleLine.getRules().forEach(
        rule -> setInputEntryValues(rule, inputEntries,
            inputAttributeToHeaderMap, description, useFeelExpr));

    setImplicitColumnValue(inputEntries, inputAttributeToHeaderMap, nodeIndex, useFeelExpr);

    // Add input entries to dmnRule
    dmnRule.getInputEntries().addAll(inputEntries);

    // Add annotation element
    dmnRule.setDescription(description);

    dmnRule.getOutputEntries().addAll(outputEntries);
    return dmnRule;
  }

  /**
   * This function is used to set the input values in the input entry of camunda
   *
   * @param rule
   * @param inputEntries
   * @param inputAttributeToHeaderMap
   * @param description
   */
  private void setInputEntryValues(RuleLine.Rule rule, List<InputEntry> inputEntries,
      Map<String, DmnHeader> inputAttributeToHeaderMap,
      Description description,
      boolean useFeelExpr) {
    // Fetching the parameter name for the current rule
    String parameterName = rule.getParameterName();
    String parameterType = ObjectUtils.isEmpty(rule.getParameterType())
        ? inputAttributeToHeaderMap.get(parameterName).getDataType()
        : rule.getParameterType().toString();

    String expression = rule.getConditionalExpression();

    if (StringUtils.isNumeric(parameterName)){
      parameterName= CUSTOM_FIELD_PREFIX + parameterName;
    }

    String parsedExpression =
        businessRuleTaskHandler.getDMNFriendlyExpression(
            parameterName,
            expression,
            parameterType,
            useFeelExpr);

    // Check if rule implies select all (ex ALL_Customer).
    // In case of select all, no expression (text content) will be set for the input.
    // DMN rule's annotation (description) will be updated to help UI to know this info
    boolean isSelectAll = StringExpressionHelper.isSelectAllRule(parameterName, expression);

    inputEntries
        .get(inputAttributeToHeaderMap.get(parameterName).getColumnIndex())
        .getText()
        .setTextContent(isSelectAll ? null : parsedExpression);

    // In case of string input, setting annotation for not-null condition to help UI
    // distinguish between Select All and Select None
    if (WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(
        inputAttributeToHeaderMap.get(parameterName).getDataType())
        && isSelectAll) {
      if (StringUtils.isEmpty(description.getTextContent())) {
        // Customer:SELECT_ALL
        description.setTextContent(
            MessageFormat.format("{0}:{1}", parameterName, WorkflowConstants.SELECT_ALL));
      } else {
        // Customer:SELECT_ALL,Department:SELECT_ALL
        description.setTextContent(
            MessageFormat.format(
                "{0},{1}:{2}",
                description.getTextContent(),
                parameterName,
                WorkflowConstants.SELECT_ALL));
      }
    }
  }

  /**
   * Get attribute entries for the rules present in the current dmn (subTree from rootWorkflowStep
   * till any action is encountered for each branch)
   *
   * @param definition
   * @param rootWorkflowStep
   * @param workflowStepMap
   * @return
   */
  private List<Attribute> filterSupportedAttributesForDMN(Definition definition,
      WorkflowStep rootWorkflowStep,
      Map<String, WorkflowStep> workflowStepMap) {

    // Get rules for the subtree of the root workflow step(subTree from rootWorkflowStep
    // till any action is encountered for each branch)
    Set<RuleLine.Rule> rules = getRulesOfSubTree(rootWorkflowStep, workflowStepMap);

    Set<String> parameterNames = rules.stream()
        .map(RuleLine.Rule::getParameterName)
        .collect(Collectors.toSet());

    // Return list of attributes using record attribute and global attribute
    Record record = customWorkflowConfig.getRecordObjForType(definition.getRecordType());
    Map<String, Attribute> nameToAttributeMapping =
        record.getAttributes().stream()
            .collect(Collectors.toMap(Attribute::getName, Function.identity()));

    List<Attribute> dmnAttributes = parameterNames.stream()
        .filter(nameToAttributeMapping::containsKey)
        .map(nameToAttributeMapping::get)
        .collect(Collectors.toList());

    List<Attribute> customFieldAttributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nameToAttributeMapping, new ArrayList<>(rules));
    return Stream.of(dmnAttributes, customFieldAttributes).
        flatMap(Collection::stream).collect(Collectors.toList());
  }

  /**
   * Get list of rule lines of the subtree for the given root workflow step (subTree from
   * rootWorkflowStep till any action is encountered for each branch)
   *
   * @param firstWorkflowstep
   * @param workflowStepMap
   * @return
   */
  private Set<RuleLine.Rule> getRulesOfSubTree(WorkflowStep firstWorkflowstep,
      Map<String, WorkflowStep> workflowStepMap) {

    Set<RuleLine.Rule> currentSubTreeRules = new HashSet<>();

    if (firstWorkflowstep.getStepType().equals(StepTypeEnum.ACTION)) {
      return currentSubTreeRules;
    }

    // RuleLine has set of rules. Get uber list of parameter names from all such rules
    currentSubTreeRules.addAll(CustomWorkflowUtil.getRulesOfCurrentStep(firstWorkflowstep));

    workflowStepMap.get(String.valueOf(firstWorkflowstep.getId()))
        .getNext().forEach(nextChild -> {
          WorkflowStep currentWorkflowStep = workflowStepMap.get(
              String.valueOf(nextChild.getWorkflowStepId()));
          currentSubTreeRules.addAll(getRulesOfSubTree(currentWorkflowStep, workflowStepMap));
        });

    return currentSubTreeRules;
  }

  public Map<String, Map<String, DmnHeader>> buildDmnHeadersMap(
      DecisionTable decisionTable) {
    Map<String, DmnHeader> inputAttributeToHeaderMap = new HashMap<>();
    Map<String, DmnHeader> outputAttributeToHeaderMap = new HashMap<>();
    Map<String, Map<String, DmnHeader>> attributeToHeaderMap = new HashMap<>();

    //Putting the dmnHeader object for every input dmn column header in the map
    AtomicInteger inputIndex = new AtomicInteger(0);
    decisionTable
        .getInputs()
        .forEach(
            decisionInput -> {
              inputAttributeToHeaderMap.put(
                  decisionInput.getLabel(), new DmnHeader(
                      inputIndex.getAndIncrement(),
                      decisionInput.getInputExpression().getTypeRef()));
            });

    // Repeat the same for outputs
    AtomicInteger outputIndex = new AtomicInteger(0);
    decisionTable
        .getOutputs()
        .forEach(
            decisionOutput ->
                outputAttributeToHeaderMap.put(
                    decisionOutput.getLabel(), new DmnHeader(
                        outputIndex.getAndIncrement(),
                        decisionOutput.getTypeRef())));

    attributeToHeaderMap.put(WorkflowConstants.INPUT, inputAttributeToHeaderMap);
    attributeToHeaderMap.put(WorkflowConstants.OUTPUT, outputAttributeToHeaderMap);

    return attributeToHeaderMap;
  }

  /**
   * Need to create all inputEntries (not just passed from schema) to maintain the order
   *
   * @param dmnModelInstance
   * @param inputAttributeToHeaderMap
   * @return
   */
  private List<InputEntry> createInputEntries(DmnModelInstance dmnModelInstance,
      Map<String, DmnHeader> inputAttributeToHeaderMap, boolean useFeelExpr) {
    List<InputEntry> inputEntries =
        IntStream.range(0, inputAttributeToHeaderMap.size())
            .mapToObj(
                idx -> {
                  InputEntry inputEntry = dmnModelInstance.newInstance(InputEntry.class);
                  inputEntry.setText(dmnModelInstance.newInstance(Text.class));
                  inputEntry.setExpressionLanguage(
                      CustomWorkflowUtil.getInputExpressionLanguage(useFeelExpr));
                  return inputEntry;
                })
            .collect(Collectors.toList());

    return inputEntries;
  }

  /**
   * Need to create all outputEntries and not just passed in schema to maintain order and then
   * further setting the output result as well.
   *
   * @param dmnModelInstance
   * @param outputAttributeToHeaderMap
   * @param outputResult
   * @return
   */
  private List<OutputEntry> createAndSetOutputEntries(DmnModelInstance dmnModelInstance,
      Map<String, DmnHeader> outputAttributeToHeaderMap,
      String outputResult,
      boolean useFeelExpr) {
    List<OutputEntry> outputEntries =
        IntStream.range(0, outputAttributeToHeaderMap.size())
            .mapToObj(
                idx -> {
                  OutputEntry outputEntry = dmnModelInstance.newInstance(OutputEntry.class);
                  Text text = dmnModelInstance.newInstance(Text.class);
                  if (useFeelExpr) {
                    text.setTextContent(String.format("\"%s\"", outputResult));
                  } else {
                    if (CustomWorkflowUtil.isBoolean(outputResult) || StringUtils.isNumeric(
                        outputResult)) {
                      text.setTextContent(outputResult);
                    } else {
                      text.setTextContent(String.format("'%s'", outputResult));
                    }
                  }

                  outputEntry.setText(text);
                  return outputEntry;
                })
            .collect(Collectors.toList());

    return outputEntries;
  }

  /**
   * Setting the value of the index column to the index of the current node
   *
   * @param inputEntries
   * @param inputAttributeToHeaderMap
   * @param nodeIndex
   */
  private void setImplicitColumnValue(List<InputEntry> inputEntries,
      Map<String, DmnHeader> inputAttributeToHeaderMap,
      int nodeIndex, boolean useFeelExpr) {
    String dmnExpr = useFeelExpr ? String.valueOf(nodeIndex)
        : String.format("%s == %s", WorkflowConstants.INDEX_COLUMN, nodeIndex);
    inputEntries
        .get(inputAttributeToHeaderMap.get(WorkflowConstants.INDEX_COLUMN).getColumnIndex())
        .getText()
        .setTextContent(dmnExpr);
  }

  private RuleLineInstance buildRuleEntity(StepNext nextChild, int leftChildIndex,
      int rightChildIndex, Collection<Rule> rules) {
    return RuleLineInstance.builder().currentIndexColumnValue
            (MultiStepUtil.isYesLabel(nextChild.getLabel()) ? leftChildIndex : rightChildIndex).
        workflowStepId(nextChild.getWorkflowStepId()).rules(rules).build();
  }
}
