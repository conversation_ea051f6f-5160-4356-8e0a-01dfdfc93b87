package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiConditionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.WorkflowStepPathResultInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.StepNext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * MultiSplitRuleLineProcessor is used to generate rule lines in DMN, when we have multi splits instead of multi condition.
 * Multi Split is more than two braches , instead of traditional yes/no branch.
 */
@Component((WorkflowBeansConstants.CREATE_MULTI_SPLIT_RULE_LINE_PROCESSOR))
public class MultiSplitRuleLineProcessor extends MultiConditionRuleLineProcessor {
  private final FeatureFlagManager featureFlagManager;
  public MultiSplitRuleLineProcessor(CustomWorkflowConfig customWorkflowConfig,
      BusinessRuleTaskHandler businessRuleTaskHandler,
      CustomWorkflowDecisionHandler customWorkflowDecisionHandler,
      MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor,
      FeatureFlagManager featureFlagManager) {
    super(customWorkflowConfig, businessRuleTaskHandler, customWorkflowDecisionHandler,
        multiConditionPlaceholderExtractor, featureFlagManager);
    this.featureFlagManager = featureFlagManager;
  }

  /**
   *
   * @param definitionInstance
   * @param ruleLineInstance        an object consisting of variables that are unique for the current
   *                                ruleline and are used to build the same
   * @param activityIds             list of activityIds of all the call Activity Ids corresponding
   *                                to the current dmn
   * @param attributeToHeaderMap    map of columnType(input dmn column/output dmn column) to map of
   *                                attribute(ex. name = TxnAmount) to DmnHeader(ex. {dateType =
   *                                Integer, columnIndex = 0})
   * @param stepIdToActivityIdMap   map of step id of workflow step to the corresponding call
   *                                activity id which will be responsible for its execution during
   *                                trigger
   * @param dmnModelInstance        dmnModelInstance of the current dmn
   */
  @Override
  public void buildDmn(
      DefinitionInstance definitionInstance,
      RuleLineInstance ruleLineInstance,
      List<String> activityIds,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      Map<String, String> stepIdToActivityIdMap,
      DmnModelInstance dmnModelInstance) {
    Map<String, WorkflowStep> workflowStepMap = definitionInstance.getWorkflowStepMap();

    boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
        definitionInstance.getTemplateDetails());

    int maxNumberOfChildren = ruleLineInstance.getSiblingSteps().size();

    WorkflowLogger.logInfo(
        "step=createMultiSplitWorkflows maxChildren=%s", maxNumberOfChildren);

    WorkflowStepPathResultInstance workflowStepPathResultInstance  =
        computeOutputsOfChildrenPaths(maxNumberOfChildren,
            workflowStepMap, activityIds,
            stepIdToActivityIdMap,
            ruleLineInstance);


    for (WorkflowStep siblingWorkflowStep : ruleLineInstance.getSiblingSteps()) {
      String yesPath = workflowStepPathResultInstance.getStepIdToSiblingIndexMap()
          .get(siblingWorkflowStep.getId().toString());
//    Currently we have single branch Multi split workflows.
      siblingWorkflowStep.getWorkflowStepCondition().getRuleLines()
          .forEach(
              ruleLine -> {
                Rule rule = processRuleLine(WorkflowConstants.START_INDEX_VALUE,
                    yesPath,
                    ruleLine, dmnModelInstance, attributeToHeaderMap, useFeelExpr);
                ruleLineInstance.addRules(rule);
              }
          );
    }

    // Add the false path rule line in the dmn
    Rule rule = processRuleLine(
        WorkflowConstants.START_INDEX_VALUE, workflowStepPathResultInstance.getNoPathValue(),
        new RuleLine().rules(new ArrayList<>()),
        dmnModelInstance, attributeToHeaderMap, useFeelExpr);
    ruleLineInstance.addRules(rule);

//  TODO Call Recursively, when there are branches withing roots of Multi split workflows.
  }

  /**
   * Computes the path results of the children of their siblings.
   * @param maxNumberOfChildren
   * @param workflowStepMap
   * @param activityIds
   * @param stepIdToActivityIdMap
   * @param ruleLineInstance
   * @return
   */
  private WorkflowStepPathResultInstance computeOutputsOfChildrenPaths(int maxNumberOfChildren,
      Map<String, WorkflowStep> workflowStepMap,
      List<String> activityIds,
      Map<String, String> stepIdToActivityIdMap,
      RuleLineInstance ruleLineInstance) {

    WorkflowStepPathResultInstance workflowStepPathResultInstance = new WorkflowStepPathResultInstance();
    Map<String, String> stepIdToPathListMap = new HashMap<>();
//  Currently parentIndex = 0, but when we have recursion ,this value needs to be mapped with StepId and then retrieve the value from a map
    int parentIndex = 0;
//  Sibling count is the increase the counter of siblings when we have decision elements coming from another decision element
    int siblingCount = 1;
    for (WorkflowStep workflowStep : ruleLineInstance.getSiblingSteps()) {
      for (StepNext next : workflowStep.getNext()) {
        boolean isActionStep = MultiStepUtil.isActionStep(
            workflowStepMap.get(next.getWorkflowStepId()));

        if (isActionStep) {
          stepIdToActivityIdMap.putIfAbsent(next.getWorkflowStepId(),
              ((LinkedList<String>) activityIds).pollFirst());
        }
        String pathResult;
        if (MultiStepUtil.isYesLabel(next.getLabel())) {
          pathResult = getPathResult(maxNumberOfChildren, siblingCount, parentIndex,
              isActionStep, stepIdToActivityIdMap.get(next.getWorkflowStepId()));
          stepIdToPathListMap.putIfAbsent(workflowStep.getId().toString(), pathResult);
        }
//     TODO Build a map to pass the computed next nodes in recursion to the parent node.
      }
      siblingCount++;
    }

    workflowStepPathResultInstance.setNoPathValue(String.valueOf(Boolean.FALSE));

    workflowStepPathResultInstance.setStepIdToSiblingIndexMap(stepIdToPathListMap);
    return workflowStepPathResultInstance;
  }

  /**
   *
   * @param maxNumberOfChildren Maximum number of children a multi split workflow can have
   * @param siblingChildrenCount  Number of parents the workflow can have
   * @param parentIndex parent index to calculate the offset for dmn output column
   * @param isActionStep ActionStep helps us to get the activityId instead of computing the dmn column
   * @param activityId
   * @return
   */
  private String getPathResult(int maxNumberOfChildren, int siblingChildrenCount,
      int parentIndex, boolean isActionStep,
      String activityId) {

    String nextPathIndex = String.valueOf(MultiStepUtil.computeChildIndex(
        parentIndex, maxNumberOfChildren, siblingChildrenCount));
    return isActionStep ? activityId : nextPathIndex;
  }

}
