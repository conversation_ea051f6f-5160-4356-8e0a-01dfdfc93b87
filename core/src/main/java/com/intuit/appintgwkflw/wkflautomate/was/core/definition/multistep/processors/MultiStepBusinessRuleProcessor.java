package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;

import java.util.List;
import java.util.Map;

public interface MultiStepBusinessRuleProcessor {
    /**
     * This function reads the dmn and converts it to a readable map
     * Ex -
     * {
     *      "0" - {
     *          "yes" - List<Rule> yes rules
     *          "no" - List<Rule> no rules
     *      },
     *      "1" - {
     *          "yes" - List<Rule> yes rules
     *          "no" - List<Rule> no rules
     *      }
     * }
     *
     * @param decisionTable        decision table instance
     * @param attributeToHeaderMap dmn headers map
     * @return dmn rules map keyed by respective index values
     */
    Map<String, Map<String, List<List<Rule>>>> parseDecisionTable(
            DecisionTable decisionTable,
            Map<String, Map<String, DmnHeader>> attributeToHeaderMap);
}
