package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import java.util.List;
import java.util.Map;
import org.camunda.bpm.model.dmn.instance.Rule;

/**
 * This interface includes functions that are used to build ruleLine and rule object
 *
 * <AUTHOR>
 */
public interface RuleLineBuilder {

  /**
   * This function converts dmn rule to workflowStep equivalent ruleLine. DMN rule -> ruleLine Each
   * DMN rule has a list of input entries. These input entries are then converted to rule object.
   *
   * @param record               record type object
   * @param rules                list of rules which are used build the ruleLines
   * @param workflowStepId       workflowStepId
   * @param attributeToHeaderMap map of dmn input/output header columns
   * @return list of ruleLines
   */
  List<RuleLine.Rule> buildV4RuleLine(
      Record record,
      List<Rule> rules,
      GlobalId workflowStepId,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap);
}
