package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.List;
import java.util.Map;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;

/**
 * This interface includes functions that are used to parse the conditions present in the current
 * subtree of given root workflow step(starting from the given root workflow step till any action is
 * encountered for each branch)
 *
 * <AUTHOR>
 */
public interface RuleLineProcessor {

  /**
   * The dmn column headers will be created dynamically from the rules provided in the current
   * subtree of root workflow step (subTree from root workflow step till any action is encountered
   * for each branch) of create/update definition payload.
   *
   * @param dmnModelInstance
   * @param firstWorkflowStep
   * @param definitionInstance
   * @return
   */
  DecisionTable createDmnHeaders(
      DmnModelInstance dmnModelInstance, WorkflowStep firstWorkflowStep,
      DefinitionInstance definitionInstance);

  /**
   * This function is used to build the dmn Headers Map of columnName to their corresponding
   * columnIndex and their data type
   *
   * @param decisionTable
   * @return
   */
  Map<String, Map<String, DmnHeader>> buildDmnHeadersMap(
      DecisionTable decisionTable);

  /**
   * This function is used to build the dmn by adding rules in the dmn table from the given
   * conditions of the create/update definition payload
   *
   * @param definitionInstance
   * @param ruleLineInstance          an object consisting of variables that are unique for the current
   *                                ruleline and are used to build the same
   * @param dmnModelInstance        dmnModelInstance of the current dmn
   * @param attributeToHeaderMap    map of columnType(input dmn column/output dmn column) to map of
   *                                attribute(ex. name = TxnAmount) to DmnHeader(ex. {dateType =
   *                                Integer, columnIndex = 0})
   * @param activityIds             list of activityIds of all the call Activity Ids corresponding
   *                                to the current dmn
   * @param stepIdToActivityIdMap   map of step id of workflow step to the corresponding call
   *                                activity id which will be responsible for its execution during
   *                                trigger
   */
  void buildDmn(DefinitionInstance definitionInstance,
      RuleLineInstance ruleLineInstance,
      List<String> activityIds,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap,
      Map<String, String> stepIdToActivityIdMap,
      DmnModelInstance dmnModelInstance);
}
