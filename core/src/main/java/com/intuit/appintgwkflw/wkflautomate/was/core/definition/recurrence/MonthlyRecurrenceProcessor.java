package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import static com.cronutils.model.field.expression.FieldExpressionFactory.always;
import static com.cronutils.model.field.expression.FieldExpressionFactory.and;
import static com.cronutils.model.field.expression.FieldExpressionFactory.every;
import static com.cronutils.model.field.expression.FieldExpressionFactory.questionMark;

import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.FieldExpressionFactory;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/*
 * handle recurrence for recurType = MONTHLY
 */
@Component
public class MonthlyRecurrenceProcessor implements RecurrenceProcessor {

  private static final Integer MAX_MONTH_IN_YEAR = 12;
  /**
   * intervals for monthly recurrence which can give a fixed recurrence pattern for any month
   */
  private static final Set<Integer> FIXED_RECURRENCE_INTERVALS =
      new HashSet<>(Arrays.asList(2, 3, 4, 6));

  /**
   * given a recurrence rule of type MONTHLY
   * "recurrence": {
   *    "interval": 1,
   *    "recurType": "MONTHLY",
   *    "daysOfMonth": [1, 25],
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => and(on(1), on(25)) = 1,25
   * monthsOfYear => every(1) = * (all months)
   * daysOfWeek => ? (any)
   * year => * (always)
   *
   * resultant cron expression 0 0 0 1,25 * ? *
   *
   * given a recurrence rule of type MONTHLY
   * "recurrence": {
   *    "interval": 2,
   *    "recurType": "MONTHLY",
   *    "dayOfWeek": ["MONDAY"],
   *    "weekOfMonth": ["LAST"],
   *    "startDate": "2021-09-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => ? (any - since daysOfWeek and weekOfMonth parameter is populated)
   * monthsOfYear => 9,11,1,3,5,7 (every 2 months starting 09)
   * daysOfWeek => on(2,L) = 2L (Last week - Monday)
   * year => * (always)
   *
   * resultant cron expression 0 0 0 ? 1,3,5,7,9,11 2L *
   *
   * given a recurrence rule of type MONTHLY
   * "recurrence": {
   *    "interval": 3,
   *    "recurType": "MONTHLY",
   *    "dayOfWeek": ["MONDAY"],
   *    "weekOfMonth": ["LAST"],
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => ? (any - since daysOfWeek and weekOfMonth parameter is populated)
   * monthsOfYear => 8,11,2,5 (every 3 months starting 08)
   * daysOfWeek => on(2,L) = 2L (Last week - Monday)
   * year => * (always)
   *
   * resultant cron expression 0 0 0 ? 2,5,8,11 2L *
   *
   * given a recurrence rule of type MONTHLY
   * "recurrence": {
   *    "interval": 5,
   *    "recurType": "MONTHLY",
   *    "daysOfMonth": [1, 25],
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => and(on(1), on(25)) = 1,25
   * monthsOfYear => every(5) = * /5 (every 5 months)
   * daysOfWeek => ? (any)
   * year => * (always)
   */
  // resultant cron expression 0 0 0 1,25 */5 ? *

  @Override
  public String getRecurrence(RecurrenceRule recurrenceRule) {
    FieldExpression monthsOfYear = every(recurrenceRule.getInterval());
    FieldExpression year = always();
    FieldExpression daysOfMonth;
    FieldExpression daysOfWeek;

    /*
     * check if the recurrenceInterval given can form a fixed pattern
     */
    if (FIXED_RECURRENCE_INTERVALS.contains(recurrenceRule.getInterval())) {
      monthsOfYear =
          populateMonthlyRecurrencePattern(
              recurrenceRule.getStartDate().getMonthOfYear(), recurrenceRule.getInterval());
    }
    /*
     * In a monthly schedule, if week of the month is provided, it is used to calculate a relative
     * day in the month such as First Monday, Last thursday etc
     *
     * Otherwise, it will be particular day like 2nd, 25th etc
     */
    if (Objects.nonNull(recurrenceRule.getWeekOfMonth())) {
      daysOfMonth = questionMark();
      daysOfWeek = populateWeeksOfMonthParameter(recurrenceRule);
    } else {
      daysOfMonth = populateDaysOfMonthParameter(recurrenceRule);
      daysOfWeek = questionMark();
    }
    return buildCronExpression(
        year, daysOfMonth, monthsOfYear, daysOfWeek, recurrenceRule.getRecurrenceTime());
  }

  /**
   * @param startMonth based on the provided start date
   * @param recurrenceInterval recurrence frequency for monthly. example: every 3 months
   * @return field expression containing all the months forming the recurrence pattern
   */
  private FieldExpression populateMonthlyRecurrencePattern(int startMonth, int recurrenceInterval) {
    /*
     * find the recurrence pattern from the startDate month
     * using the recurrenceInterval multiples (in the range [0 - 12/recurrenceInterval))
     *
     * For example: for startDate month 9 and recurrence interval 2
     * recurrence pattern is found in the multiple range [0 - 6)
     * result: 9, 11, 1, 3, 5, 7
     * (9 + 2*0) % 12 = 9
     * (9 + 2*1) % 12 = 11
     * (9 + 2*2) % 12 = 1
     * (9 + 2*3) % 12 = 3
     * (9 + 2*4) % 12 = 5
     * (9 + 2*5) % 12 = 7
     */
    Set<Integer> recurrenceMonths =
        IntStream.range(0, (MAX_MONTH_IN_YEAR / recurrenceInterval))
            .mapToObj(
                i -> {
                  int recurringMonth = (startMonth + recurrenceInterval * i) % MAX_MONTH_IN_YEAR;
                  return recurringMonth == 0 ? MAX_MONTH_IN_YEAR : recurringMonth;
                })
            .collect(Collectors.toSet());

    return and(
        recurrenceMonths.stream().map(FieldExpressionFactory::on).collect(Collectors.toList()));
  }

  @Override
  public RecurrencePattern buildESSRecurrencePattern(RecurrenceRule recurrenceRule) {
    RecurrencePatternType recurType =
        ObjectUtils.isNotEmpty(recurrenceRule.getWeekOfMonth())
            ? RecurrencePatternType.RELATIVEMONTHLY
            : RecurrencePatternType.ABSOLUTEMONTHLY;

    RecurrencePattern recurrencePattern =
        new RecurrencePattern().interval(recurrenceRule.getInterval()).type(recurType);
    mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, recurrencePattern);
    return recurrencePattern;
  }

  public RecurTypeEnum getName() {
    return RecurTypeEnum.MONTHLY;
  }
}
