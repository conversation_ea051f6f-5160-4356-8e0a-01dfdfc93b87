package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.v4.common.RecurTypeEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * class acts as factory to return recurrence schedule handlers
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RecurrenceHandler {

    private static final Map<RecurTypeEnum, RecurrenceProcessor> RECURRENCE_SCHEDULER_MAP = new HashMap<>();

    /**
     * Adds a handler.
     *
     * @param recurType  the recurType
     * @param handler the handler
     */
    public static void addHandler(RecurTypeEnum recurType, RecurrenceProcessor handler) {
        RECURRENCE_SCHEDULER_MAP.put(recurType, handler);
    }

    /**
     * Gets handler.
     *
     * @param recurType caller recurType
     * @return rule handler impl
     */
    public static RecurrenceProcessor getHandler(RecurTypeEnum recurType) {
        return RECURRENCE_SCHEDULER_MAP.get(recurType);
    }

    /**
     * Contains boolean.
     *
     * @param recurType caller recurType
     * @return true /false if handler is present or not
     */
    public static boolean contains(RecurTypeEnum recurType) {
        return RECURRENCE_SCHEDULER_MAP.containsKey(recurType);
    }
}