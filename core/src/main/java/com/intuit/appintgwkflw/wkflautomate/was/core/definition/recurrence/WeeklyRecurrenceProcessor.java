package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import static com.cronutils.model.field.expression.FieldExpressionFactory.always;
import static com.cronutils.model.field.expression.FieldExpressionFactory.questionMark;

import com.cronutils.model.field.expression.FieldExpression;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import java.util.List;
import org.springframework.stereotype.Component;

/*
 * handle recurrence for recurType = WEEKLY
 */
@Component
public class WeeklyRecurrenceProcessor implements RecurrenceProcessor {
  /**
   * given a recurrence rule of type WEEKLY
   * "recurrence": {
   *    "interval": 1,
   *    "recurType": "WEEKLY",
   *    "daysOfWeek": ["MONDAY", "WEDNESDAY"]
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => ? = any
   * monthsOfYear => always() = * (all months)
   * daysOfWeek => and(on(2), on(4)) - 2,4 - (monday, wednesday)
   * year => * = (always)
   *
   * resultant cron expression 0 0 0 ? * 2,4 *
   *
   * given a recurrence rule of type WEEKLY
   * "recurrence": {
   *    "interval": 2,
   *    "recurType": "WEEKLY",
   *    "dayOfWeek": ["MONDAY"],
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the expression returned will be R/P2W and the dayOfWeek will be
   * controlled based on the startDate
   */
  @Override
  public String getRecurrence(RecurrenceRule recurrenceRule) {
    /**
     * As cron expressions do not support every N weeks recurrence cron for weeks is generated as
     * per 1week = 7 days for every 1 week ( = 7 days) recurrence, it is mandatory to provide the
     * corresponding day of the week (sunday or monday.. ) from which schedule should start
     *
     * for (N > 1), the dayOfWeek is handled in the startDate. The time cycle expression (R/P<n>W)
     * is returned as per https://en.wikipedia.org/wiki/ISO_8601#Repeating_intervals
     */
    FieldExpression monthsOfYear = always();
    FieldExpression year = always();
    FieldExpression daysOfMonth;
    FieldExpression daysOfWeek;
    String weeklyRepeatingInterval = "R/P%sW";
    if (recurrenceRule.getInterval() > 1) {
      return String.format(weeklyRepeatingInterval, String.valueOf(recurrenceRule.getInterval()));
    } else {
      daysOfMonth = questionMark();
      daysOfWeek = populateDaysOfWeekParameter(recurrenceRule);
    }

    return buildCronExpression(
        year, daysOfMonth, monthsOfYear, daysOfWeek, recurrenceRule.getRecurrenceTime());
  }

  @Override
  public RecurrencePattern buildESSRecurrencePattern(RecurrenceRule recurrenceRule) {
    List<DayOfWeekType> daysOfWeek =
        mapDaysOfWeekTypeForESSSchedule(recurrenceRule.getDaysOfWeek());
    return new RecurrencePattern()
        .interval(recurrenceRule.getInterval())
        .type(RecurrencePatternType.fromValue(getName().value()))
        .daysOfWeek(daysOfWeek);
  }

  public RecurTypeEnum getName() {
    return RecurTypeEnum.WEEKLY;
  }
}
