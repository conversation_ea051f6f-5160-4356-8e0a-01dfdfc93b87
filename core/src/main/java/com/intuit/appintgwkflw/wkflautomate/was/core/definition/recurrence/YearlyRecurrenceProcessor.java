package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import static com.cronutils.model.field.expression.FieldExpressionFactory.every;
import static com.cronutils.model.field.expression.FieldExpressionFactory.on;
import static com.cronutils.model.field.expression.FieldExpressionFactory.questionMark;

import com.cronutils.model.field.expression.Always;
import com.cronutils.model.field.expression.FieldExpression;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/*
 * handle recurrence for recurType = YEARLY
 */
@Component
public class YearlyRecurrenceProcessor implements RecurrenceProcessor {

  /**
   * given a recurrence rule of type YEARLY
   * "recurrence": {
   *    "interval": 1,
   *    "recurType": "YEARLY",
   *    "daysOfMonth": [1, 25],
   *    "monthsOfYear": ["MARCH", "APRIL"]
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => and(on(1), on(25)) = 1,25
   * monthsOfYear => and(on(3), on(4)) = 3,4 (March and April)
   * daysOfWeek => ? = any
   * year => every(on(presentYear), 1) = 2021/1 (every year starting 2021)
   *
   * resultant cron expression 0 0 0 1,25 3,4 ? 2021/1
   *
   * given a recurrence rule of type YEARLY
   * "recurrence": {
   *    "interval": 2,
   *    "recurType": "YEARLY",
   *    "daysOfWeek": ["MONDAY"],
   *    "monthsOfYear": ["MARCH", "APRIL"]
   *    "weekOfMonth": "THIRD",
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => ? (any) = since daysOfWeek and weekOfMonth parameter are available
   * monthsOfYear => and(on(3), on(4)) = 3,4 (March and April)
   * daysOfWeek => on(2, #, 3) = on(Monday, #, Third) = 2#3
   * year => every(on(presentYear), 2) = 2021/2 (every 2years starting 2021)
   *
   * resultant cron expression 0 0 0 ? 3,4 2#3 2021/2
   */

  @Override
  public String getRecurrence(RecurrenceRule recurrenceRule) {
    /*
     * set yearly interval starting the present year
     */
    FieldExpression year = every(on(LocalDateTime.now().getYear()), recurrenceRule.getInterval());
    FieldExpression daysOfMonth = populateDaysOfMonthParameter(recurrenceRule);
    FieldExpression monthsOfYear = populateMonthsOfYearParameter(recurrenceRule);
    FieldExpression daysOfWeek = questionMark();
    /*
     * For yearly recurrence, if days of month expression is of type always additionally check if
     * WeekOfMonth parameter is populated. If yes, calculate the relative day in the month
     */
    if ((daysOfMonth instanceof Always)) {
      if (Objects.nonNull(recurrenceRule.getWeekOfMonth())) {
        daysOfWeek = populateWeeksOfMonthParameter(recurrenceRule);
      } else {
        daysOfWeek = populateDaysOfWeekParameter(recurrenceRule);
      }
      daysOfMonth = questionMark();
    }
    return buildCronExpression(
        year, daysOfMonth, monthsOfYear, daysOfWeek, recurrenceRule.getRecurrenceTime());
  }

  @Override
  public RecurrencePattern buildESSRecurrencePattern(RecurrenceRule recurrenceRule) {
    List<Integer> monthIndex = mapMonthIndexForESSSchedule(recurrenceRule.getMonthsOfYear());
    RecurrencePatternType recurType =
        ObjectUtils.isNotEmpty(recurrenceRule.getWeekOfMonth())
            ? RecurrencePatternType.RELATIVEYEARLY
            : RecurrencePatternType.ABSOLUTEYEARLY;

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(recurrenceRule.getInterval())
            .type(recurType)
            .monthIndex(monthIndex);
    mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, recurrencePattern);
    return recurrencePattern;
  }

  public RecurTypeEnum getName() {
    return RecurTypeEnum.YEARLY;
  }
}
