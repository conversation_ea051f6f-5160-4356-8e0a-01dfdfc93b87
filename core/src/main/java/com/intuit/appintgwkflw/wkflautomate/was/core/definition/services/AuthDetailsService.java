package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;

public interface AuthDetailsService {

  /**
   * Get the authDetails from the AuthDetails entity
   *
   * @param realmId
   * @return
   */
  AuthDetails getAuthDetailsFromRealmId(String realmId);

  /**
   * Get the authDetails from the AuthDetails entity and does not do any validation checks on the
   * auth data
   *
   * @param realmId
   * @return
   */
  AuthDetails getAuthDetailsFromRealmIdSafe(String realmId);

  /**
   * Renew the ticket and saves into database in async manner
   *
   * @param processId the workflow
   * @return Authorization header
   */
  String renewOfflineTicketAndUpdateDB(String processId);

  /**
   * Renew the ticket and saves into database in async manner
   *
   * @param authDetails the authDetails
   * @return Authorization header
   */
  String renewOfflineTicketAndUpdateDB(AuthDetails authDetails);


  /**
   * Renew the ticket and saves into database in async manner
   *
   * @param ownerId the workflow
   * @return Authorization header
   */
  String renewOfflineTicketAndUpdateDB(Long ownerId);

}
