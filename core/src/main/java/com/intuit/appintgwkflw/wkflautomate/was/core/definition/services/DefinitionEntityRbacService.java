package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.appintgwkflw.wkflautomate.was.common.service.EntityRbacService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.RequestContext;
import com.intuit.v4.GlobalId;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.workflows.Definition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Definition-specific implementation of EntityRbacService.
 * This service handles RBAC checks specifically for Definition entities.
 */
@Service
public class DefinitionEntityRbacService implements EntityRbacService {

    @Autowired
    private DefinitionRbacService definitionRbacService;

    @Override
    public void verifyReadAccess(RequestContext context, GlobalId id, QueryHelper query) {
        // Extract definition ID and realm ID from the request
        String definitionId = id.getLocalId();
        String realmId = context.getAuthorization().getRealm();
        
        // Delegate to the existing DefinitionRbacService
        definitionRbacService.verifyReadAccess(definitionId, realmId);
    }

    @Override
    public void verifyReadAccessFromQuery(RequestContext context, QueryHelper query) {
        // Extract definition ID from query parameters (used for obfuscated reads)
        Optional<Object> recordMetadataInput = query.getArg(WorkflowConstants.BY);
        if (recordMetadataInput.isPresent()) {
            Map<String, String> metaDataInput = (HashMap<String, String>) recordMetadataInput.get();
            String definitionId = String.valueOf(metaDataInput.get(WorkflowConstants.DEFINITION_ID));
            String realmId = context.getAuthorization().getRealm();
            
            // Delegate to the existing DefinitionRbacService
            definitionRbacService.verifyReadAccess(definitionId, realmId);
        }
    }

    @Override
    public boolean supportsEntity(Class<?> entityClass) {
        // This service only supports Definition entities
        return Definition.class.isAssignableFrom(entityClass);
    }
}
