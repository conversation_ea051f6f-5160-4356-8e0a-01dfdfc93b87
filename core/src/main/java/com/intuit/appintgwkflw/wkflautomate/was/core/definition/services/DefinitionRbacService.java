package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Centralized service for handling RBAC (Role-Based Access Control) operations for workflow definitions.
 * This service provides a single point of control for all RBAC checks across different CRUD operations.
 */
@Service
public class DefinitionRbacService {

    @Autowired
    private DefinitionServiceHelper definitionServiceHelper;

    @Autowired
    private AccessVerifier accessVerifier;

    @Autowired
    private DefinitionRbacConfig definitionRbacConfig;

    /**
     * Verifies read access for a single definition.
     * This method is called from the provider layer to check if the user has permission
     * to read a specific workflow definition.
     *
     * @param definitionId The ID of the definition to check access for
     * @param realmId The realm ID of the requesting user
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyReadAccess(String definitionId, String realmId) {
        if (!definitionRbacConfig.isEnabledForOperation("READ")) {
            return; // RBAC disabled for READ operations
        }

        String workflowType = getWorkflowType(definitionId, realmId);
        if (workflowType != null) {
            boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, "READ");
            if (!hasAccess) {
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
            }
        }
    }

    /**
     * Verifies create access for a workflow definition.
     *
     * @param workflowType The type of workflow being created
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyCreateAccess(String workflowType) {
        if (!definitionRbacConfig.isEnabledForOperation("CREATE")) {
            return; // RBAC disabled for CREATE operations
        }

        if (workflowType != null) {
            boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, "CREATE");
            if (!hasAccess) {
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
            }
        }
    }

    /**
     * Verifies update access for a workflow definition.
     *
     * @param definitionId The ID of the definition to update
     * @param realmId The realm ID of the requesting user
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyUpdateAccess(String definitionId, String realmId) {
        if (!definitionRbacConfig.isEnabledForOperation("UPDATE")) {
            return; // RBAC disabled for UPDATE operations
        }

        String workflowType = getWorkflowType(definitionId, realmId);
        if (workflowType != null) {
            boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, "UPDATE");
            if (!hasAccess) {
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
            }
        }
    }

    /**
     * Verifies delete access for a workflow definition.
     *
     * @param definitionId The ID of the definition to delete
     * @param realmId The realm ID of the requesting user
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyDeleteAccess(String definitionId, String realmId) {
        if (!definitionRbacConfig.isEnabledForOperation("DELETE")) {
            return; // RBAC disabled for DELETE operations
        }

        String workflowType = getWorkflowType(definitionId, realmId);
        if (workflowType != null) {
            boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, "DELETE");
            if (!hasAccess) {
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
            }
        }
    }

    /**
     * Helper method to extract workflow type from definition ID.
     * This method handles the database query to get workflow type information.
     *
     * @param definitionId The definition ID
     * @param realmId The realm ID
     * @return The workflow type (action key) or null if not found
     */
    private String getWorkflowType(String definitionId, String realmId) {
        try {
            return definitionServiceHelper.getWorkflowType(definitionId, realmId);
        } catch (Exception e) {
            // If we can't determine workflow type, we'll allow the operation to proceed
            // and let other security checks handle authorization
            return null;
        }
    }

    /**
     * Extracts workflow type from definition template name for create operations.
     * This is used when we have the template name but not yet a persisted definition.
     *
     * @param templateName The template name
     * @return The workflow type (action key) or null if not found
     */
    public String getWorkflowTypeFromTemplate(String templateName) {
        if (templateName == null) {
            return null;
        }
        return CustomWorkflowType.getActionKey(templateName);
    }
}
