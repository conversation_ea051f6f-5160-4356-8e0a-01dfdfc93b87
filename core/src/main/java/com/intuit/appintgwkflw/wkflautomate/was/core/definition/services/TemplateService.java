package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.workflows.Template;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import org.camunda.bpm.model.xml.ModelValidationException;
import org.springframework.web.multipart.MultipartFile;

public interface TemplateService {

  /*
   * To save BPMN and DMN templates. If the template doesn't exist, this API will create new templates and
   * triggers else it will create new entry in de_template_details with upgraded version else will throw error as
   * Template already exists
   * @param templates : 1 BPMN file with all referenced DMNs
   */
  WorkflowGenericResponse saveTemplate(
      MultipartFile[] templates, TemplateMetadata templateMetadata);

  /*
   * To update existing BPMN and DMN templates. If the template doesn't exist, this API will throw error
   * as Template doesn't exist else it will create new entry in de_template_details with upgraded version for existing
   * ones and create trigger entries in de_trigger_details with reference to new template records.
   * @param templates : 1 BPMN file with all referenced DMNs
   */
  WorkflowGenericResponse updateTemplate(
      MultipartFile[] templates, TemplateMetadata templateMetadata);

  /**
   * @param name : Id of the template to be fetched which is extracted from the Global Id
   * @param globalId : Global Id
   * @return : Template to UI as required by the V4 Provider
   * @throws IOException
   */
  Template fetchTemplate(String name, GlobalId globalId, boolean isMultiStepPayload) throws IOException;

  void fetchTemplates(String templateName, String zipfileName);

  WorkflowGenericResponse fetchTemplateByVersion(String templateName, int templateVersion);

  /**
   * @param templateId template id for which details need to be fetched
   * @return Object of {@link TemplateDetails}
   */
  Optional<TemplateDetails> getTemplateDetails(String templateId);

  /**
   * Get the list of children templates
   *
   * @param templateDetails {@link TemplateDetails}
   * @return list of Templates
   */
  Optional<List<TemplateDetails>> getReferencedChildTemplates(TemplateDetails templateDetails);
  /**
   * @return List of Templates to UI as required by the V4 Provider.
   * @throws IOException
   */
  List<Template> fetchAllTemplates(QueryHelper query) throws IOException;

  WorkflowGenericResponse updateTemplateStatus(String templateName, String templateStatus);

  /**
   * Get TemplateDetails entity from templateName
   *
   * @param templateName
   * @return
   */
  TemplateDetails getTemplateByName(String templateName);

  /**
   * @return List<Templates> with WorkflowSteps if filter with workflowSteps is passsed.</>
   * @throws IOException
   */

  /**
   * Gets all enabled templateDetails by templateName
   * @param templateName
   * @return
   */
  List<TemplateDetails> getAllEnabledTemplatesByName(final String templateName);

  List<Template> fetchAllTemplatesWithWorkflowSteps(QueryHelper query) throws IOException;

  /**
   * @param authorization input authorization details
   * @param query input query details
   * @return List of Templates to UI as required by the V4 Provider.
   * @throws IOException
   */
  ListResult<Template> readAllTemplates(Authorization authorization, QueryHelper query)
      throws IOException;

  /**
   * @param templates : Array of Multipart files
   * @return : True/False based on Template validation
   * @throws ModelValidationException
   * @throws IOException
   */
  boolean validateTemplate(MultipartFile[] templates, TemplateMetadata templateMetadata) throws ModelValidationException, IOException;

  /**
   * Get template metadata for a record type from template configuration [Filtering of condition is
   * done based on actionKey] - if templateData is passed in query return it in the response as well
   * based on shouldReturntemplateData flag value
   *
   * @param source
   * @param recordType
   * @param actionKey
   * @param isMultiStepPayload boolean whether templateData should be returned as part of readOne
   * @return template
   */
  Template getTemplateMetadataForRecordType(String source, String recordType, String actionKey, boolean isMultiStepPayload);

  /**
   * This method will return all the templates present in Spring Cloud config
   *
   * @return
   */
  List<Template> getConfigTemplates();

  /**
   * This method will return Template object from the list of Templates given an id
   *
   * @param templateId : {@link String} : Template Id
   * @param isMultiStep whether multistep worklfow or not
   * @return template
   */
  Template getConfigTemplateById(String templateId, boolean isMultiStep);

  /**
   * This method will filter out duplicate templates from the list of database templates and return
   * the result.
   *
   * @param preCannedConfigTemplates : {@link List<Template>} : List of Pre-canned Config Templates
   * @param templates : {@link List<Template>} : List of Db Templates
   * @return
   */
  List<Template> filterConfigTemplatesFromDbTemplates(
      List<Template> preCannedConfigTemplates, List<Template> templates);
}
