package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import com.intuit.v4.workflows.Definition;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserContributionService {

  /**
   * This call is guarded by the flag. Once UCS is live in production then will remove this if *
   * check.
   *
   * @param resourceId
   * @param ucsVerifyAccessRequest
   */
  void verifyAccess(final String resourceId, final UcsVerifyAccessRequest ucsVerifyAccessRequest);

  /**
   * @param definition
   * @param recordType
   * @param isMultiStep
   */
  void obfuscateDefinitionDetails(
      final Definition definition, final String recordType, boolean isMultiStep);

  void deletePublishedTemplate(final String definitionId);
  void deleteAllPublishedTemplates(final String realmId);
  void deleteAllPublishedTemplates(final String realmId, final List<String> definitionKeys);
}
