package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.intuit.v4.WorkflowsBulkAction;
import com.intuit.v4.WorkflowsBulkAction.WorkflowBulkActionDetails;

/** <AUTHOR> */
public interface WorkflowBulkActionService {

  /**
   * fetch all definition details for given company and then start deleting the data associated with
   * that company and remove the auth details for given company in async manner.
   *
   * @param workflowDeletionDetails workflow deletion details
   */
  WorkflowsBulkAction deleteAll(WorkflowBulkActionDetails workflowBulkActionDetails);
}
