package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.v4.workflows.Definition;

/**
 * <AUTHOR>
 * <p>
 * This is an abstract class which contains the logic to transform the payload given to it.
 */
public abstract class BaseDefinitionTransformer {

  /**
   * This function transforms the payload given to it accordingly depending on whether it's
   * migration case or rollback case
   *
   * @param readDefinition
   * @param definitionDetails
   * @param updatedTemplateId templateId of the template to which we need to migrate/rollback the
   *                          definition
   */
  public void transformPayload(
      Definition readDefinition, DefinitionDetails definitionDetails, String updatedTemplateId) {
    TransformationDecisionDTO transformationDecisionDTO = TransformationDecisionDTO.builder()
        .definition(readDefinition).definitionDetails(definitionDetails)
        .templateId(updatedTemplateId)
        .build();
    if (isMigrationCase(transformationDecisionDTO)) {
      migrate(readDefinition, definitionDetails);
    } else {
      rollback(readDefinition, definitionDetails);
    }
  }

  /**
   * This function checks whether it's migration case or rollback case
   *
   * @param transformationDecisionDTO
   *
   * @return
   */
  protected abstract boolean isMigrationCase(TransformationDecisionDTO transformationDecisionDTO);

  /**
   * This function contains the logic to transform the payload for migration case
   *
   * @param readDefinition    the readPayload for definition we need to migrate
   * @param definitionDetails definitionDetails for the readDefinition
   */
  protected abstract void migrate(Definition readDefinition, DefinitionDetails definitionDetails);

  /**
   * This function contains the logic to transform the payload for rollback case
   *
   * @param readDefinition    definition which was last working correctly
   * @param definitionDetails definitionDetails for the readDefinition
   */
  protected abstract void rollback(Definition readDefinition, DefinitionDetails definitionDetails);

}
