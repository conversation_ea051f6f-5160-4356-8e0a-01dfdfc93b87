package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.v4.workflows.Definition;

import java.util.List;

/**
 * Interface to manage the split between Single and User reads.
 */
public interface DefinitionDetailsRead {

    /**
     * Method to get the Definition XML.
     * @param definitionDetails {@link DefinitionDetails}
     * @return {@link BpmnResponse}
     */
    BpmnResponse getBPMNXMLDefinition(DefinitionDetails definitionDetails);

    /**
     * Method to substitute definition with placeholder value
     * @param definition {@link Definition}
     * @param bpmnDefinitionDetail {@link DefinitionDetails}
     */
    default void substitutePlaceHolder(
            Definition definition, DefinitionDetails bpmnDefinitionDetail) {}

    /**
     * Method to substitute recurrence details in the definition with placeholder values.
     *
     * @param definition the {@link Definition} object to be updated with recurrence details.
     * @param bpmnDefinitionDetail the {@link DefinitionDetails} containing the placeholder values.
     */
    default void substituteRecurrence(
            Definition definition, DefinitionDetails bpmnDefinitionDetail) {}

    /**
     * This method compares the single definition object populated using placeholder values with
     * the user definition object created using definition data stored in definition table
     * This needs to be cleaned up after some time
     *
     * @param definition
     * @param bpmnDefinitionDetails
     * @param dmnXmlStrings required to construct template object
     * @param realmId required to construct template object
     */
    default void verifyPlaceholderDefinition(Definition definition, DefinitionDetails bpmnDefinitionDetails,
                                             List<String> dmnXmlStrings, String realmId){}


}
