package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.workflows.Definition;
import java.util.List;

// TODO all the methods in this interface have 'authorization' as a parameter. This needs to be
// removed and the object needs to be read via MDC
public interface DefinitionService {

  /**
   * Creates definition
   *
   * @param definition {@link Definition}
   * @param authorization authorization
   * @return created {@link Definition} object
   */
  Definition createDefinition(Definition definition, Authorization authorization)
      throws WorkflowGeneralException;

  /**
   * Delete definition
   *
   * @param definition {@link Definition}
   * @param authorization authorization
   * @return created {@link Definition} object
   */
  Definition deleteDefinition(Definition definition, Authorization authorization);

  /**
   * Update definition
   *
   * @param definition {@link Definition}
   * @param authorization authorization
   * @return created {@link Definition} object
   */
  Definition updateDefinition(Definition definition, Authorization authorization)
      throws WorkflowGeneralException;

  Definition disableDefinition(Definition definition, Authorization authorization)
	      throws WorkflowGeneralException;

  Definition enableDefinition(Definition definition, Authorization authorization)
	      throws WorkflowGeneralException;

  Definition getDefinitionReadOne(String definitionId, GlobalId id, boolean isMultiStep);

  List<Definition> getDefinitionList(Authorization authorization, QueryHelper query);

  List<Definition> getDefinitionListWithWorkflowSteps(
      Authorization authorization, QueryHelper query);

  /**
   * @param authorization input {@link Authorization} details
   * @param query input {@link QueryHelper} details
   * @return list of definition for given company
   */
  ListResult<Definition> getAllDefinitions(Authorization authorization, QueryHelper query);

  /**
   * Create definition with default values for a given template name. The method looks into
   * precanned template catalog and picks the entry matching template name. The default values
   * are used from this entry.
   *
   * @param templateName
   * @param authorization authorization
   * @return created {@link Definition} object
   */
  Definition createDefinitionWithDefaultValues(String templateName, boolean isCreatedAsSystemUser,
      Authorization authorization)
      throws WorkflowGeneralException;

  Definition getDefinitionWithObfuscatedValues(RequestContext context, QueryHelper queryHelper, boolean isMultiStep);
}
