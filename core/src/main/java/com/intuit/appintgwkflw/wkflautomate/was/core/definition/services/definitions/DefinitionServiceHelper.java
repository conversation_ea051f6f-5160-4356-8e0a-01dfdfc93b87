package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.checkAndSetSystemTagObjectFromTemplate;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import static com.intuit.v4.GlobalId.create;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Streams;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.SchedulingSvcException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.DefinitionDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeployDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DisableDeleteWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.MultiStepSaveDefinitionDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.PopulateAuthDetailsTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveAndActivateAppConnectWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveDefinitionInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartRecurringProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateDefinitionDetailsTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.*;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerRecurringProcessHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.*;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.User;
import com.intuit.v4.common.Metadata;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.javatuples.Pair;
import org.joda.time.DateTime;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/** Author: Nitin Gupta Date: 20/01/20 Description: */
@AllArgsConstructor
@Component
public class DefinitionServiceHelper {
  private DefinitionDetailsRepository definitionDetailsRepository;
  private TemplateDetailsRepository templateDetailsRepository;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private ProcessDetailsRepository processDetailsRepository;
  private AppConnectService appConnectService;
  private AuthDetailsService authDetailsService;
  private EnabledDefinitionCacheService enabledDefinitionCacheService;
  private AuthDetailsServiceHelper authDetailsServiceHelper;
  private WASContextHandler contextHandler;
  private TriggerRecurringProcessHelper triggerRecurringProcessHelper;
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;
  private final IXPManager ixpManager;
  private final DefinitionDomainEventHandler definitionDomainEventHandler;
  private final EventScheduleHelper eventScheduleHelper;
  private final MultiStepSaveDefinitionDataStoreTask multiStepSaveDefinitionDataStoreTask;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final SchedulingService schedulingService;
  
  /**
   * Save the definition details in Database
   *
   * @param deployDefinitionResponse {@link DeployDefinitionResponse}
   * @param definitionInstance {@link DefinitionInstance}
   * @param authorization {@link Authorization
   * @return updated definition
   */
  @Transactional
  public Definition saveUpdateDefinitionDetails(DeployDefinitionResponse deployDefinitionResponse,
      DefinitionInstance definitionInstance, Authorization authorization, boolean isUpdate) {

    try {
      Definition definition = definitionInstance.getDefinition();
      final Pair<String, List<DefinitionDetails>> definitionDetailsPair =
          buildDefinitionDetailsEntity(deployDefinitionResponse, definitionInstance, authorization,
              definition, Objects.isNull(definitionInstance.getDefinitionDetails()) ? 0
                  : definitionInstance.getDefinitionDetails().getVersion());

      // Insert into db
      List<DefinitionDetails> definitionDetails = definitionDetailsPair.getValue1();
      String bpmnDefinitionId = definitionDetailsPair.getValue0();

      WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
          .message("Performing insert/update in db for definitionId=%s", bpmnDefinitionId));
      saveUpdateInDataStore(definition, definitionDetails, isUpdate, definitionInstance, authorization);

      Optional<DefinitionDetails> updatedDefinitionDetails =
          definitionDetailsRepository.findByDefinitionId(bpmnDefinitionId);

      definitionInstance.setDefinitionDetails(updatedDefinitionDetails
          .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND)));
      // Update definition with new id
      definition.setId(GlobalId.builder().setLocalId(bpmnDefinitionId)
          .setRealmId(authorization.getRealm()).build());
      // Update definition with definitionKey
      definition.setDefinitionKey(updatedDefinitionDetails.get().getDefinitionKey());

      return definition;

    } catch (DataAccessException e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Exception occurred while performing database operation=%s", isUpdate)
          .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .className(this.getClass().getName()).stackTrace(e));
      throw new WorkflowGeneralException(WorkflowError.INTERNAL_EXCEPTION, e);
    }
    catch (CacheNonRetriableException | CacheRetriableException e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
              .message("Exception occurred while performing cache operation=%s", isUpdate)
              .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
              .downstreamComponentName(DownstreamComponentName.CACHE)
              .className(this.getClass().getName()).stackTrace(e));
      throw new WorkflowGeneralException(WorkflowError.POPULATE_CACHE_FAILED, e);
    }
    catch (Exception e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Trying to update Obsolete Definition. Please try again!")
          .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .className(this.getClass().getName()).stackTrace(e));
      throw new WorkflowGeneralException(WorkflowError.CONCURRENT_UPDATE_DEFINITION_FAILED, e);
    }
  }

  private void saveUpdateInDataStore(Definition definition,
      List<DefinitionDetails> definitionDetails, boolean isUpdate, DefinitionInstance definitionInstance, Authorization authorization) {

    Timestamp originalSetupDate =  new Timestamp(System.currentTimeMillis());

    Long originalSetupUser = getUserId(definition, authorization);

    if (isUpdate) {
      originalSetupDate = definitionInstance.getDefinitionDetails().getOriginalSetupDate();
      originalSetupUser = definitionInstance.getDefinitionDetails().getOriginalSetupUser();
      updateDefinitionEntriesByDefinitionId(definition.getId().getLocalId(), false);
    }

      for (DefinitionDetails definitionDetail : definitionDetails) {
        definitionDetail.setOriginalSetupDate(originalSetupDate);
        definitionDetail.setOriginalSetupUser(originalSetupUser);
      }

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("Inserting new entry of definition in database "));
    // Persist in Database

    // Need to check if definition is getting created with Disabled status then update the
    // definition's internal status as to marked for disable
    if (Status.DISABLED.getStatus().equalsIgnoreCase(definition.getStatus().value())) {

      definitionDetails
          .forEach(definitions -> definitions.setInternalStatus(InternalStatus.MARKED_FOR_DISABLE));
    }

    definitionDetailsRepository.saveAll(definitionDetails);

    // Save enabled definition details into cache
    org.apache.commons.collections4.CollectionUtils.emptyIfNull(definitionDetails)
              .stream()
              .filter(dd -> dd.getModelType().equals(ModelType.BPMN))
              .forEach(dd-> enabledDefinitionCacheService.updateCacheWithDefinitionDetails(dd));

    List<DomainEntityRequest<DefinitionDetails>> domainEvents = org.apache.commons.collections4.CollectionUtils.emptyIfNull(definitionDetails)
            .stream().map(
                    definitionDetail -> DomainEntityRequest.<DefinitionDetails>builder()
                            .request(definitionDetail)
                            .entityChangeAction(isUpdate ? EntityChangeAction.UPDATE : EntityChangeAction.CREATE)
                            .build()
            ).collect(Collectors.toList());

    definitionDomainEventHandler.publishAll(domainEvents);

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("new entry of definition in database successfully inserted"));
  }


  @Transactional
  public void updateDefinitionEntriesByDefinitionId(final String definitionId,
      boolean deleteFromAppConnect) {

    // Only Needs to be marked Stale, status should not be updated.
    long definitionDetailsEntriesUpdatedCount = definitionDetailsRepository.updateInternalStatus(
        InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId));
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("Definition entries updated from Datastore, updatedCount=%s",
            definitionDetailsEntriesUpdatedCount)
        .downstreamComponentName(DownstreamComponentName.WAS)
        .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION));

    WorkflowLogger
        .info(() -> WorkflowLoggerRequest.builder().message("Definition entries updated in WAS db")
            .downstreamComponentName(DownstreamComponentName.WAS)
            .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));
  }

  /**
   * Execute the async tasks for workflow CRUD
   *
   * @param definitionInstance
   * @param authorization
   * @param isUpdate
   * @return Definition
   */
  public Definition executeAsyncWorkflowTasks(
      DefinitionInstance definitionInstance, Authorization authorization, boolean isUpdate) {

    State inputRequestForDeploy = new State();

    DeployDefinition deployDefinition =
        CamundaServiceHelper.createDeployDefinitionRequest(
            definitionInstance.getBpmnModelInstance(),
            definitionInstance.getDmnModelInstanceList(),
            DefinitionDeploymentUtil.getDeploymentName(definitionInstance));

    inputRequestForDeploy.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, deployDefinition);

    // deploy to camunda, in parallel - save in db & appconnect
    inputRequestForDeploy.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());

    // Add entityType and entityOperation for registerToken
    inputRequestForDeploy.addValue(
        WorkflowConstants.ENTITY_TYPE, definitionInstance.getDefinition().getRecordType());
    Map<String, ParameterDetails> definitionTrigger =
        CustomWorkflowUtil.getTriggerFromDefinition(definitionInstance.getDefinition());
    if (definitionTrigger.containsKey(AsyncTaskConstants.ENTITY_OPERATION)) {
      String entityOperation =
          String.join(
              ",", definitionTrigger.get(AsyncTaskConstants.ENTITY_OPERATION).getFieldValue());
      inputRequestForDeploy.addValue(AsyncTaskConstants.ENTITY_OPERATION, entityOperation);
    }
    createWithRollBack(isUpdate, inputRequestForDeploy, definitionInstance, authorization);
    // update metadata
    // Update recordType to displayValue for the response
    Definition definition = definitionInstance.getDefinition();
    definition.setRecordType(RecordType.fromType(definition.getRecordType()).getRecordType());
    definition.setMeta(getMetadata(authorization, definitionInstance.getDefinitionDetails()));
    // Set definition Key here
    definition.setDefinitionKey(
        Optional.ofNullable(definitionInstance.getDefinitionDetails())
            .map(DefinitionDetails::getDefinitionKey)
            .orElse(null));
    return definition;
  }

  /**
   * @param definitionDetailsList
   * @param realmId
   * @return
   */
  public List<DefinitionDetails> processMarkedDefinitionsAndGetEnabledDefinitions(
      List<DefinitionDetails> definitionDetailsList, String realmId) {

    // Disable/delete in appconnect in case the definition is disabled or deleted in async
    Predicate<DefinitionDetails> enabledCondition =
        (definitionDetails) ->
            Objects.equals(Status.ENABLED, definitionDetails.getStatus())
                && (Objects.isNull(definitionDetails.getInternalStatus()) ||
                Objects.equals(InternalStatus.STALE_DEFINITION, definitionDetails.getInternalStatus()));

    executeDisableDeleteWorkflowTasks(
        realmId,
        definitionDetailsList.stream()
            .filter(enabledCondition.negate())
            .collect(Collectors.toList()));

    return definitionDetailsList.stream().filter(enabledCondition).collect(Collectors.toList());
  }

  /**
   * @param isUpdate if updateDefinition
   * @param state {@link State}
   * @param definitionInstance {@link DefinitionInstance}
   * @param authorization {@link Authorization}
   * @return {@link State}
   */
  @VisibleForTesting
  State createWithRollBack(boolean isUpdate, State state, DefinitionInstance definitionInstance,
      Authorization authorization) {

    RxExecutionChain executionChain =
        getExecutionTasks(state, definitionInstance, authorization, isUpdate);
    state = executionChain.execute();

    /*
     Catch exception and invoke roll back
     Whether it's an update call or not, invoke rollback,
     on failure in cache update, so that DB is in sync with cache.
    */
    if (!isUpdate || cacheUpdateFailed(state))
        checkAndRollbackWithException(state);
    /* Update DB */
    if (BooleanUtils.isNotTrue(state.getValue(AsyncTaskConstants.IS_SINGLE_DEFINITION))) {
      // update not required in case of single definition
      state = new UpdateDefinitionDetailsTask(definitionDetailsRepository).execute(state);
    }
    /* Catch exception and invoke roll back */
    if (!isUpdate)
      checkAndRollbackWithException(state);

    return state;
  }


  /**
   * Returns whether update in cache failed.
   *
   * @return
   */
  private boolean cacheUpdateFailed(State state) {
    return Optional.ofNullable(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_EXCEPTION))
            .filter(WorkflowGeneralException.class::isInstance)
            .map(WorkflowGeneralException.class::cast)
            .map(WorkflowGeneralException::getWorkflowError)
            .filter(WorkflowError.POPULATE_CACHE_FAILED::equals)
            .isPresent();
  }

  /**
   *
   * @param state
   * @param definitionInstance
   * @param authorization
   * @param isUpdate
   * @return
   * <P>
   * Returns ExecutionChain based on below Conditions
   * 1) UserDefn + RealmUser : DeploydefnTask ->  saveDefinitionInDataStoreTask -> saveAndActivateAppConnectWorkflowTask
   * 2) UserDefn + !RealmUser : PopulateAuthDetailsTask -> DeploydefnTask ->  saveDefinitionInDataStoreTask
   * -> saveAndActivateAppConnectWorkflowTask
   * 3) SingleDefn + RealmUser: saveAndActivateAppConnectWorkflowTask -> saveDefinitionInDataStoreTask
   * 4) SingleDefnn + !RealmUser: PopulateAuthDetailsTask + saveAndActivateAppConnectWorkflowTask -> saveDefinitionInDataStoreTask
   * 5) If recurring process: getExecutionChainForRecurringProcess
   * </P>
   */
  private RxExecutionChain getExecutionTasks(State state, DefinitionInstance definitionInstance,
      Authorization authorization, boolean isUpdate) {
    boolean isPrecannedToCustomMigration =
            CustomWorkflowUtil.isCustomWorkflow(definitionInstance.getDefinition().getTemplate())
                    && customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(
                    definitionInstance.getDefinition()
            );

    boolean isSingleDefinition =
        Objects.nonNull(definitionInstance.getTemplateDetails()) && DefinitionType.SINGLE
            .equals(definitionInstance.getTemplateDetails().getDefinitionType());
    boolean shouldActivate =
        WorkflowStatusEnum.ENABLED == definitionInstance.getDefinition().getStatus()
            && (
                    Objects.isNull(definitionInstance.getDefinitionDetails())
                            || !Status.ENABLED.equals(definitionInstance.getDefinitionDetails().getStatus())
                            || isPrecannedToCustomMigration
        );

    /* Create App Connect Subscription and create DB */
    SaveDefinitionInDataStoreTask saveDefinitionInDataStoreTask =
        new SaveDefinitionInDataStoreTask(this, definitionInstance, authorization, isUpdate);

    SaveAndActivateAppConnectWorkflowTask saveAndActivateAppConnectWorkflowTask =
        new SaveAndActivateAppConnectWorkflowTask(appConnectService, authDetailsService,
            definitionInstance.getBpmnModelInstance(), shouldActivate, isUpdate, isPrecannedToCustomMigration,
            definitionInstance.getWorkflowId(), definitionInstance.getDefinition().getDisplayName());

    boolean isMultiCondition = MultiStepUtil.isMultiCondition(definitionInstance.getDefinition());
    if (isUpdate && isSingleDefinition) {
      // Definition id in normal case is returned by Camunda upto deployment. As in single
      // definition, there is no definition deployment, we will generate the definition id.
      state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, UUID.randomUUID().toString());
    }

    state.addValue(AsyncTaskConstants.IS_SINGLE_DEFINITION, isSingleDefinition);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);
    state.addValue(AsyncTaskConstants.AUTHORIZATION_KEY, authorization);
    state.addValue(AsyncTaskConstants.IS_UPDATE, isUpdate);

    //We are saving the instance of DefinitionServiceHelper class in the state so to avoid
    //cyclic dependency that will get created as we are calling DefinitionServiceHelper class again
    //from MultiStepSaveDefinitionDataStoreTask to save definition details in Db
    state.addValue(AsyncTaskConstants.DEFINITION_SERVICE_HELPER_OBJECT, this);

    RxExecutionChain executionChain;
	/**
	 * Handle System User Generated Single Definitions.  
	 */
    if (WASContext.isMigrationContext() || WASContext.isRealmSystemUser()) {
      executionChain = new RxExecutionChain(state);
    }else{
      executionChain = new RxExecutionChain(state, new PopulateAuthDetailsTask(contextHandler, authDetailsServiceHelper));
    }
    boolean isMigratedToScheduling = schedulingService.isMigrated(definitionInstance.getDefinitionDetails());
    // Moved the Scheduling call first in update flow as it is more prone to errors
    if(isUpdate && (schedulingService.isEnabled(definitionInstance.getDefinition(), state.getValue(AsyncTaskConstants.REALM_ID_KEY)) || isMigratedToScheduling))
      executionChain = getExecutionChainForUpdateEventScheduling(definitionInstance, state, executionChain, isMigratedToScheduling);

    if (isMultiCondition) {
      boolean isEssFlowEnabled = CustomWorkflowUtil.isCustomReminderWorkflow(definitionInstance.getDefinition());

      executionChain = isEssFlowEnabled ?
          getExecutionChainForMultiStepEssFlow(state, definitionInstance,
              appConnectService, authDetailsService,shouldActivate, isUpdate, isPrecannedToCustomMigration, executionChain) :
          getExecutionChainForMultiStep(executionChain);

    } else {
      executionChain = isSingleDefinition ?
              getExecutionChainForSingleDefinition(saveAndActivateAppConnectWorkflowTask,
                      saveDefinitionInDataStoreTask, executionChain) :
              getExecutionChainForUserDefinition(saveAndActivateAppConnectWorkflowTask,
                      saveDefinitionInDataStoreTask, executionChain);
    }

    // add trigger process tasks for a recurring process
    if (RecurrenceUtil.isValidRecurrence(definitionInstance.getDefinition().getRecurrence())) {
      executionChain = getExecutionChainForRecurringProcess(
          definitionInstance, state, isUpdate, executionChain);
    }

    return getExecutionChainForEventSchedule(definitionInstance, state, isUpdate, executionChain);
  }


  private RxExecutionChain getExecutionChainForUserDefinition(SaveAndActivateAppConnectWorkflowTask saveAndActivateAppConnectWorkflowTask,
                                                              SaveDefinitionInDataStoreTask saveDefinitionInDataStoreTask,
                                                              RxExecutionChain executionChain) {
    return executionChain.next(new DeployDefinitionTask(bpmnEngineDefinitionServiceRest))
              .next(saveDefinitionInDataStoreTask, saveAndActivateAppConnectWorkflowTask);
  }

  //TODO Currently we are creating subscription details in appconnect. Will remove it in the next PR.
  private RxExecutionChain getExecutionChainForMultiStep(RxExecutionChain executionChain) {
    return executionChain.next(multiStepSaveDefinitionDataStoreTask);
  }

  /**
   * Get Execution Chain for MultiStep Definition with ESS Flow
   * @param state
   * @param definitionInstance
   * @param appConnectService
   * @param authDetailsService
   * @param shouldActivate
   * @param isUpdate
   * @param isPrecannedToCustomMigration
   * @return RxExecutionChain
   */
  private RxExecutionChain getExecutionChainForMultiStepEssFlow(State state, DefinitionInstance definitionInstance,
                                                                final AppConnectService appConnectService,
                                                                final AuthDetailsService authDetailsService,
                                                                final boolean shouldActivate,
                                                                final boolean isUpdate,
                                                                final boolean isPrecannedToCustomMigration,
                                                                final RxExecutionChain executionChain) {

    WorkflowLogger.logInfo("step=CreateMultiStepDefinitionWithESSFlow status=Started definitionId=%s",
        definitionInstance.getDefinition().getId());

    // Save child Bpmn in AppConnect because CallActivity support is not present in AppConnect to save parent Bpmn
    BpmnModelInstance childBpmnModelInstance =
        getChildBpmnModelInstanceFromParent(definitionInstance.getBpmnModelInstance(),
            CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definitionInstance.getDefinition()));

    SaveAndActivateAppConnectWorkflowTask saveAndActivateAppConnectWorkflowTaskWithChildBpmn =
        new SaveAndActivateAppConnectWorkflowTask(appConnectService, authDetailsService,
            childBpmnModelInstance, shouldActivate, isUpdate, isPrecannedToCustomMigration,
            definitionInstance.getWorkflowId(), definitionInstance.getDefinition().getDisplayName());

    boolean isAppConnectUpdateWorkflowDisabled = ixpManager.getBoolean(WorkflowConstants.APPCONNECT_WORKFLOW_UPDATE_DISABLED_FF, false);

    if(isAppConnectUpdateWorkflowDisabled && isUpdate){
      state.addValue(AsyncTaskConstants.IS_APPCONNECT_UPDATE_DISABLED, true);
      WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
          .message("step=CreateMultiStepDefinitionWithESSFlow Appconnect workflow update is disabled for MCR workflows for definitionId=%s",definitionInstance.getDefinition().getId()));
    }

    // For MultiStep definition with ESS flow, skip deploying in camunda step.
    // Save in AppConnect then save in DB
    return executionChain.next(saveAndActivateAppConnectWorkflowTaskWithChildBpmn)
          .next(multiStepSaveDefinitionDataStoreTask);
  }

  private RxExecutionChain getExecutionChainForSingleDefinition(SaveAndActivateAppConnectWorkflowTask saveAndActivateAppConnectWorkflowTask,
                                                                SaveDefinitionInDataStoreTask saveDefinitionInDataStoreTask,
                                                                RxExecutionChain executionChain){
    // For Single definition, skip deploying in camunda step.
    // Just save in app-connect and then save in DB, won't be deployed in camunda
    // First save and activate appconnect task to get workflowId and then save definition in data store
    return executionChain.next(saveAndActivateAppConnectWorkflowTask).next(saveDefinitionInDataStoreTask);
  }

  /**
   * 1. !isEventScheduleEnabled && !isUpdate -> Start new recurring process task
   * 2. !isEventScheduleEnabled && isUpdate -> Correlate existing process task and start new recurring
   * process task
   * 3. isEventScheduleEnabled && !isUpdate -> no action to be taken / no tasks added
   * to executionChain
   * 4. isEventScheduleEnabled && isUpdate ->
   *    a) isDefinitionFromOldTemplate = true (no active schedules found)
   *    Correlate existing process task and create new schedules task for updated definition
   *   b) isDefinitionFromOldTemplate = false -> no action needed
   *
   * @param definitionInstance
   * @param state
   * @param isUpdate
   * @param executionChain
   * @return
   */
  private RxExecutionChain getExecutionChainForRecurringProcess(
      DefinitionInstance definitionInstance,
      State state,
      boolean isUpdate,
      RxExecutionChain executionChain) {
    CorrelateAllMessageTask correlateAllMessageTask;
    String workflowName = definitionInstance.getTemplateDetails().getTemplateName();
    boolean isEventScheduleEnabled =
        eventScheduleHelper.isEventSchedulingEnabledForWorkflow(workflowName, state.getValue(AsyncTaskConstants.REALM_ID_KEY));

    /**
     * TODO: remove the branching based on isEventScheduleEnabled post migration
     * https://jira.intuit.com/browse/QBOES-21483
     *
     * For an update on an old definition (enabled/disabled status), if ess schedule FF is
     * enabled, a new ESS schedule should be created
     */
    if (isEventScheduleEnabled) {
      boolean isDefinitionFromOldTemplate =
          triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(
              definitionInstance.getDefinitionDetails());
      /**
       * if it is a creation flow or an update flow on definition with schedules - take no action
       */
      if (!isUpdate || !isDefinitionFromOldTemplate) {
        return executionChain;
      }
      /**
       * if definition does not have any schedules, the definition is from the older version of
       * template and will have an active recurring process. In case of update to new version of
       * template, end the recurring process and create new ESS schedules
       */
      correlateAllMessageTask =
          triggerRecurringProcessHelper.prepareCorrelateMessageTask(definitionInstance, state);
      executionChain = executionChain.next(correlateAllMessageTask);
      /**
       * Creating and storing new ess schedules in case of update done for a definition initially
       * created in old version of template
       */
      return getExecutionChainForEventSchedule(definitionInstance, state, false, executionChain);
    }

    // prevent triggering a recurring process for a disabled definition on old template
    if (WorkflowStatusEnum.DISABLED == definitionInstance.getDefinition().getStatus()) {
      return executionChain;
    }
    /**
     * during an update for a recurring workflow, the existing active process needs to be terminated
     * as it will be using a stale definition For the updated definition, a new process is triggered
     * Therefore, as part of the set of tasks executed, the old process is ended and then a new
     * process is started for the updated definition
     */
    StartRecurringProcessTask startRecurringProcessTask =
        triggerRecurringProcessHelper.getStartRecurringProcessTask(definitionInstance, state);
    if (isUpdate) {
      correlateAllMessageTask =
          triggerRecurringProcessHelper.prepareCorrelateMessageTask(definitionInstance, state);
      return executionChain.next(correlateAllMessageTask).next(startRecurringProcessTask);
    }
    return executionChain.next(startRecurringProcessTask);
  }

  /**
   * This method prepares the execution chain for the event schedule and added tasks which are
   * responsible for saving and updating the data into database and ESS
   *
   * @param definitionInstance
   * @param state
   * @param isUpdate
   * @param executionChain
   * @return
   */
  private RxExecutionChain getExecutionChainForEventSchedule(
      DefinitionInstance definitionInstance,
      State state,
      boolean isUpdate,
      RxExecutionChain executionChain) {
    String workflowName = definitionInstance.getTemplateDetails().getTemplateName();
    // Handle definition create
    if (!isUpdate) {
      // two tasks returned by this method, one for saving details in ESS and other is saving into
      // was-db.
      List<Task> createScheduleTasks = eventScheduleHelper.prepareScheduleCreateTasks(state, workflowName);
      
      // FF is true or recurrence details are present call scheduling service
		if (schedulingService.isEnabled(definitionInstance.getDefinition(), state.getValue(AsyncTaskConstants.REALM_ID_KEY))) {
			state.addValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED, true);
          SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionInstance.getDefinition(), SchedulingServiceUtil.getStatus(definitionInstance.getDefinition().getStatus()));
          createScheduleTasks = Collections.singletonList(eventScheduleHelper.prepareSchedulingCreateTask(state, schedulingMetaData));
		}
      // add here in the chain one after the other, because output of previous task will be the
      // input of next task.
      createScheduleTasks.forEach(task -> executionChain.next(task));
      return executionChain;
    }
    // Handled in the early stage
    if (schedulingService.isEnabled(definitionInstance.getDefinition(), state.getValue(AsyncTaskConstants.REALM_ID_KEY))){
      return executionChain;
    }

    // Handle definition update
    ScheduleStatus scheduleStatus =
        definitionInstance.getDefinition().getStatus() == WorkflowStatusEnum.ENABLED
            ? ScheduleStatus.ACTIVE
            : ScheduleStatus.INACTIVE;
    EventScheduleMetaData metaData = EventScheduleServiceUtil.getScheduleMetaData(definitionInstance.getDefinitionDetails(), scheduleStatus);

    List<Task> updateScheduleTasks = eventScheduleHelper.prepareScheduleUpdateTasks(state, metaData);
    updateScheduleTasks.forEach(task -> executionChain.next(task));
    return executionChain;
  }

  /**
   * This method prepares the execution chain for the event schedule and added tasks which are
   * responsible for saving and updating the data into database and ESS
   *
   * @param definitionInstance
   * @param state
   * @param executionChain
   * @return
   */
  private RxExecutionChain getExecutionChainForUpdateEventScheduling(
          DefinitionInstance definitionInstance,
          State state,
          RxExecutionChain executionChain,
          boolean isMigratedToScheduling) {
    if(!ObjectUtils.isEmpty(definitionInstance.getDefinitionDetails().getDefinitionKey()))
      definitionInstance.getDefinition().setDefinitionKey(definitionInstance.getDefinitionDetails().getDefinitionKey());
    SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionInstance.getDefinition(), SchedulingServiceUtil.getStatus(definitionInstance.getDefinition().getStatus()));
    return executionChain.next(eventScheduleHelper.prepareSchedulingUpdateTask(state, schedulingMetaData, isMigratedToScheduling));
  }

  /**
   * Rolling back create. Note that this methods will be invoked only once, by the chain that fails
   * first
   *
   * @param state
   */
  private void checkAndRollbackWithException(State state) {
    List<Task> rollbackTask = new ArrayList<>();

    boolean appConnectSaveAndActivateTaskFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.APP_CONNECT_TASK_FAILURE));
    boolean saveDefinitionTaskFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE));
    boolean updateDefinitionTaskFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
    boolean startRecurringProcessFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.RECURRENCE_START_PROCESS_TASK_FAILURE));
    boolean saveEventScheduleTaskFailed =
        BooleanUtils.isTrue(
            state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    boolean saveEventSchedulingTaskFailed =
            BooleanUtils.isTrue(
                    state.getValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE));
    boolean saveScheduleDetailsTaskFailed =
            BooleanUtils.isTrue(
                    state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE));

    /* Call Roll back's asynchronously */
    if (appConnectSaveAndActivateTaskFailed
        || saveDefinitionTaskFailed
        || updateDefinitionTaskFailed
        || startRecurringProcessFailed
        || saveEventSchedulingTaskFailed
        || saveEventScheduleTaskFailed
        || saveScheduleDetailsTaskFailed) {
      if(Objects.isNull(state.getValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED))){
        new RxExecutionChain(state, new SaveEventScheduleRollbackTask(eventScheduleHelper)).execute();
      }
      rollbackTask
          .add(new SaveAndActivateAppConnectRollBackTask(appConnectService, authDetailsService));
      rollbackTask.add(new SaveDefinitionRollBackTask(definitionDetailsRepository, definitionActivityDetailsRepository,
              enabledDefinitionCacheService));

      // roll back not required in case of single definition as this is not called in create
      if (BooleanUtils.isNotTrue(state.getValue(AsyncTaskConstants.IS_SINGLE_DEFINITION))) {
        rollbackTask.add(new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest));
      }

      new RxExecutionChain(state, rollbackTask.toArray(new Task[rollbackTask.size()]))
          .executeAsync();

      /* Throw exception for the call */
      if (appConnectSaveAndActivateTaskFailed) {
        throw new WorkflowGeneralException(
            state.getValue(AsyncTaskConstants.APP_CONNECT_ERROR_MESSAGE),
            (Exception) state.getValue(AsyncTaskConstants.APP_CONNECT_EXCEPTION));
      }

      if (saveDefinitionTaskFailed || startRecurringProcessFailed) {
        throw new WorkflowGeneralException(
            state.getValue(AsyncTaskConstants.SAVE_DEFINITION_ERROR_MESSAGE),
            (Exception) state.getValue(AsyncTaskConstants.SAVE_DEFINITION_EXCEPTION));
      }

      if (saveEventScheduleTaskFailed) {
        throw new WorkflowGeneralException(
            state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE),
            (Exception) state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION));
      }

      if(saveEventSchedulingTaskFailed){
        Exception exception = (Exception) state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION);
        if (exception instanceof SchedulingSvcException) {
          throw (SchedulingSvcException) exception;
        }
        throw new WorkflowGeneralException(
          state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE),
          (Exception) state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION));
      }

      if (saveScheduleDetailsTaskFailed) {
        throw new WorkflowGeneralException(
                state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_ERROR_MESSAGE),
                (Exception) state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_IN_DATASTORE_EXCEPTION));
      }

      // updateDefinition task failed
      throw new WorkflowGeneralException(
          state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_ERROR_MESSAGE),
          (Exception) state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_EXCEPTION));
    }
  }

  /**
   * Build object to store in database
   *
   * @param deployDefinitionResponse {@link DeployDefinitionResponse}
   * @param definitionInstance {@link DefinitionInstance}
   * @param authorization {@link Authorization}
   * @param definition {@link Definition}
   * @return Pair < bpmnDefinitionId, Object to store in database >
   */
  private Pair<String, List<DefinitionDetails>> buildDefinitionDetailsEntity(
      DeployDefinitionResponse deployDefinitionResponse, DefinitionInstance definitionInstance,
      Authorization authorization, Definition definition, int version) {
    Map<String, Object> definitionPlaceholderMap = definitionInstance.getPlaceholderValue();

    // Extract the BPMN placeholder value to be set in definition details for db-deployment
    Map<String, Object> bpmnPlaceHolderValue =
        !Objects.isNull(definitionPlaceholderMap)
            ? (Map<String, Object>) definitionPlaceholderMap.get(BPMN_PLACEHOLDER_VALUES)
            : null;

    // Extract the DMN placeholder value to be set in definition details for db-deployment
    Map<String, Object> dmnPlaceHolderValue =
        !Objects.isNull(definitionPlaceholderMap)
            ? (Map<String, Object>) definitionPlaceholderMap.get(DMN_PLACEHOLDER_VALUES)
            : null;

    /* Get the processDefinitions from the response */
    TemplateDetails bpmnTemplateDetails = definitionInstance.getTemplateDetails();
    Map<String, DeployDefinitionResponse.DeployedDefinition> processDefinitionsMap =
        deployDefinitionResponse.getDeployedProcessDefinitions();

    // Assuming is we have one BPMN, get the processDefinition
    DeployDefinitionResponse.DeployedDefinition bpmnDefinition =
        getProcessDefinition(processDefinitionsMap);

    // Get the decisionDefinitions reference
    Map<String, DeployDefinitionResponse.DeployedDefinition> decisionDefinitionsMap =
        deployDefinitionResponse.getDeployedDecisionDefinitions();

    //In multi condition, we won't be storing definition xml anymore for bpmn
    byte[] definitionData = MultiStepUtil.isMultiCondition(definition) ? null :
            BpmnProcessorUtil.convertBpmnModelInstanceToByteArray(
                    definitionInstance.getBpmnModelInstance());

    DefinitionDetails bpmnDefinitionDetails =
            buildDefinitionDetailsObject(bpmnTemplateDetails, bpmnDefinition, authorization,
                    definitionData, definition, ModelType.BPMN, definitionInstance.getWorkflowId(),
                    null, bpmnPlaceHolderValue, definitionInstance.getLookupKeys(), definitionInstance.getModifiedBy());

    bpmnDefinitionDetails.setVersion(version);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(bpmnDefinitionDetails);

    // loop through the decision definition map in order to save all the dmn definition details
    // also in the db along with bpmn definition details
    if (MapUtils.isNotEmpty(decisionDefinitionsMap)) {
      decisionDefinitionsMap.values().forEach(
              decisionDefinition -> {
                // in case of multi condition definition fetch the dmn model instance corresponding
                // to the activity id of the deployed definition
                DmnModelInstance dmnModelInstance = MultiStepUtil.isMultiCondition(definition) ?
                        CustomWorkflowUtil.getDmnModelInstance(definitionInstance, decisionDefinition.getActivityId())
                        : definitionInstance.getDmnModelInstanceList().stream().findFirst().get();
                DefinitionDetails dmnDefinitionDetails = buildDefinitionDetailsObject(
                        bpmnTemplateDetails, decisionDefinition, authorization,
                        BpmnProcessorUtil.convertDmnModelInstanceToByteArray(dmnModelInstance),
                        definition, ModelType.DMN, null, bpmnDefinition.getId(),
                        dmnPlaceHolderValue, definitionInstance.getLookupKeys(), definitionInstance.getModifiedBy());
                dmnDefinitionDetails.setVersion(version);

                definitionDetailsList.add(dmnDefinitionDetails);
              });
    }
    return Pair.with(bpmnDefinition.getId(), definitionDetailsList);
  }

  /**
   * Build Definition Details Object
   *
   * @param bpmnTemplateDetails {@link TemplateDetails}
   * @param deployedDefinition {@link DeployDefinitionResponse.DeployedDefinition}
   * @param authorization {@link Authorization}
   * @param definition {@link Definition}
   * @param modelType {@link ModelType}
   * @param parentId bpmnDefinition ID
   * @param placeholderValue
   * @return {@link DefinitionDetails}
   */
  private DefinitionDetails buildDefinitionDetailsObject(TemplateDetails bpmnTemplateDetails,
      DeployDefinitionResponse.DeployedDefinition deployedDefinition, Authorization authorization,
      byte[] definitionData, Definition definition, ModelType modelType, String workflowId,
      String parentId, Map<String, Object> placeholderValue, Map<String, String> lookupKeys, Long modifiedBy) {
    // WASContext.isRealmSystemUser() - true only in case of batch job
    Long createdByUserId =
            WASContext.isMigrationContext() ? modifiedBy : getUserId(definition, authorization);
    return DefinitionDetails.builder().definitionId(deployedDefinition.getId())
        .definitionKey(deployedDefinition.getKey()).modelType(modelType)
        .definitionName(definition.getDisplayName()).description(definition.getDescription())
        .templateDetails(bpmnTemplateDetails).ownerId(Long.parseLong(authorization.getRealm()))
        .createdByUserId(createdByUserId)
        .modifiedByUserId(createdByUserId)
        .status(getStatusFromWorkFlowStatus(definition.getStatus())).parentId(parentId)
        .recordType(RecordType.fromType(definition.getRecordType())).definitionData(definitionData)
        .placeholderValue(ObjectConverter.toJson(placeholderValue))
        .lookupKeys(ObjectConverter.toJson(lookupKeys))
        .workflowId(workflowId).build();
  }

  /**
   * Retrieving the owner id corresponding to a definition based on whether its created
   * by system or user
   * @param definition
   * @param authorization
   * @return
   */
  private long getUserId(Definition definition, Authorization authorization) {
    return BooleanUtils.toBoolean(definition.isCreatedAsSystemUser()) ? Long.parseLong(
            WorkflowConstants.SYSTEM_OWNER_ID) : Long.parseLong(authorization.getAuthId());
  }

  /**
   * create meta information for definition from definition details
   * @param authorization
   * @param definitionDetails
   * @return
   */
  @VisibleForTesting
  /*Package Protected*/ Metadata getMetadata(Authorization authorization, DefinitionDetails definitionDetails) {
    final Metadata meta = new Metadata();
    meta.setCreated(new DateTime(definitionDetails.getCreatedDate()));
    meta.setUpdated(new DateTime(definitionDetails.getModifiedDate()));
    meta.setCreatedBy(getMetaUser(authorization, () -> definitionDetails.getCreatedByUserId()));
    meta.setUpdatedBy(getMetaUser(authorization, () -> definitionDetails.getModifiedByUserId()));
    return meta;
  }

  private User getMetaUser(Authorization authorization, Supplier<Long> userIdSupplier) {
    Long userId = userIdSupplier.get();
    if (userId != null) {
      User user = new User();
      final GlobalId<? extends GlobalId> userGlobalId = create(authorization.getRealm(),
          user.getTypeId(), String.valueOf(userIdSupplier.get()));
      user.setId(userGlobalId);
      return user;
    }
    return null;
  }


  private Status getStatusFromWorkFlowStatus(WorkflowStatusEnum workflowStatusEnum) {
    return WorkflowStatusEnum.ENABLED.equals(workflowStatusEnum) ? Status.ENABLED : Status.DISABLED;
  }

  /**
   * This process un-wrap the ids which is being set by
   * {@link com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler}.
   * Un-Wrap: Id has been updated as defined by
   * {@link com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId} which has
   * entityId_RealmID_UUID
   *
   * <p>
   * This method takes ids in above form and reset to entityId_RealmID_UUID -> entityId
   *
   * @param definitionInstance {@link DefinitionInstance}
   */
  public void unWrapDefinitionWithIds(final DefinitionInstance definitionInstance, String realmId) {

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message(
        "Un-wrapping ids to mimic it is a new definition creation. By removing realm and UUID"));
    DefinitionUnWrapIds.updateProcessId(definitionInstance.getBpmnModelInstance(), realmId);
    DefinitionUnWrapIds.updateWorkflowStepId(definitionInstance, realmId);
  }

  /**
   * This method returns all the Definitions specific to realm Id with applied query filters. In
   * case of Stale Definition [Update Definition case], it will return the most updated one which is
   * not marked stale. It fetches all the definition data except definition data and placeholder values
   *
   * @param realmId : Definitions are returned specific to realm[company id]
   * @return
   */
  public Optional<List<DefinitionDetails>> getDefinitionListWithoutWorkflowSteps(long realmId, QueryHelper query) {
    return definitionDetailsRepository.findAllDefinitionsWithoutWorkflowSteps(realmId, ModelType.BPMN, query);
  }

  /**
   * This method returns all the Definitions specific to realm Id with applied query filters. In
   * case of Stale Definition [Update Definition case], it will return the most updated one which is
   * not marked stale.
   *
   * @param realmId : Definitions are returned specific to realm[company id]
   * @return
   */
  public Optional<List<DefinitionDetails>> getDefinitionList(long realmId, QueryHelper query) {
    // Passing MARKED_FOR_DISABLE as we will be displaying disabled(MARKED_FOR_DISABLE) and
    // enabled definitions to UI
    return definitionDetailsRepository.findAllDefinitions(realmId, ModelType.BPMN, query);
  }

  /**
   * This process un-wrap the ids which is being set by
   * {@link com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler}.
   * Un-Wrap: Id has been updated as defined by
   * {@link com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId} which has
   * entityId_RealmID_UUID
   *
   * @param templateId templateId
   * @param ownerId ownerId
   * @return list of definition by template and owner id
   */
  @Trace
  public List<DefinitionDetails> fetchDefinitions(
      @Tracer(key = WASContextEnums.TEMPLATE_ID) String templateId, String ownerId) {
    final Optional<List<DefinitionDetails>> byOwnerIdAndTemplateDetails =
        definitionDetailsRepository.findByOwnerIdAndTemplateDetails(
            ownerId != null ? Long.valueOf(ownerId) : null,
            TemplateDetails.builder().id(templateId).build());

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("Definition fetched by templatedId=%s and ownerId=%s", templateId, ownerId)
        .downstreamComponentName(DownstreamComponentName.WAS)
        .downstreamServiceName(DownstreamServiceName.WAS_UPDATE_DEFINITION));

// In Case of Rollback, list will be empty
    return byOwnerIdAndTemplateDetails.orElse(Collections.emptyList());
  }

  /**
   * Count of processes in Running State for this definition filter by definition id and process
   * status
   *
   * @param definitionId definitionId
   * @return return count of process Details by definition id or empty list if not process
   *         associated with
   */
  public long getCountOfActiveProcessDetails(String definitionId) {
    WorkflowLogger.info(
        () -> WorkflowLoggerRequest.builder().message("Fetching Process details by definition Id"));

    return processDetailsRepository.countByDefinitionDetailsAndProcessStatus(
        DefinitionDetails.builder().definitionId(definitionId).build(), ProcessStatus.ACTIVE);
  }

  /**
   * @param definitionDetails definitionDetails
   * @return count of definition which are in active state
   */
  public long getActiveDefinitionCount(List<DefinitionDetails> definitionDetails) {
    return definitionDetails.stream().filter(e -> e.getStatus() == Status.ENABLED).count();
  }

  /**
   * Indicates that delete of definition is possible when 1. Either there is no active definition 2.
   * if there is active definition, then the one which we are deleting should not have any active
   * process running.
   *
   * @param definitionId definitionId
   * @return true if permissible otherwise false
   */
  @Trace
  public boolean isDefinitionDeletePermissible(
      @Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId) {

    /**
     * in BETA we don't have multiple definitions so not required to check if it is last active
     * definition so deletion will be allowed. Revisit in GA.
     */
    return getCountOfActiveProcessDetails(definitionId) == 0;
  }

  /**
   * Check this list contains given definition id
   *
   * @param definitionDetails definitionDetails
   * @param definitionId definitionId
   * @return true if present otherwise false
   */
  public Optional<DefinitionDetails> getDefinitionDetailsById(
      List<DefinitionDetails> definitionDetails, String definitionId) {
    return definitionDetails.stream()
        .filter(details -> details.getDefinitionId().equalsIgnoreCase(definitionId)).findFirst();
  }

  /**
   * This method returns Definition Details object by definitionId
   *
   * @param definitionId : Definition ID
   * @return
   */
  @Trace
  public DefinitionDetails findByDefinitionId(
      @Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId) {
	  DefinitionDetails definitionDetails = definitionDetailsRepository.findByDefinitionId(definitionId)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_DETAILS));
        /**
         * Add Workflow in Context.
         *
         */
        contextHandler
        	.addKey(WASContextEnums.WORKFLOW, definitionDetails.getTemplateDetails().getTemplateName());

        return definitionDetails;
  }

  @Trace
  public List<DefinitionDetails> findByDefinitionIdOrParentId(
      @Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId) {
    return definitionDetailsRepository.findByDefinitionIdOrParentId(definitionId, definitionId)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_DETAILS));
  }

  /**
   * This method returns Definition Details of DMN objects by definitionId
   *
   * @param definitionId : Definition ID
   * @return
   */
  public Optional<List<DefinitionDetails>> findByParentId(String definitionId) {
    return definitionDetailsRepository.findByParentId(definitionId);
  }

  /**
   * This method returns BPMN XML from Camunda Database
   *
   * @param definitionDetails {@link DefinitionDetails}
   * @return
   */
  public BpmnResponse getBPMNXMLDefinition(DefinitionDetails definitionDetails) {
    // Get the data from WAS DB if present
    return getBpmnFromDefinitonDetails(definitionDetails)
        .orElseGet(
            () -> {
              WASHttpResponse<BpmnResponse> response =
                  bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(
                      definitionDetails.getDefinitionId());
              WorkflowVerfiy.verify(!response.isSuccess2xx(), WorkflowError.EMPTY_BPMN_EXCEPTION);
              return response.getResponse();
            });
  }

  /**
   * This method returns DMN XML Lists from Camunda Database
   *
   * @param dmnDefinitionDetails
   * @return
   */
  @SuppressWarnings("unchecked")
  public List<DmnResponse> getDMNXMLDefinition(List<DefinitionDetails> dmnDefinitionDetails) {
    List<DmnResponse> dmnRespons = new ArrayList<>();
    State requestState = new State();
    List<DmnFetchTask> taskList = new ArrayList<>();
    Optional<DefinitionDetails> dmnDefinitionDetailOptional = dmnDefinitionDetails.stream().findAny();
    if (dmnDefinitionDetailOptional.isPresent()
        && Objects.nonNull(dmnDefinitionDetailOptional.get().getDefinitionData())) {
      //If any dmnData is in WAS, then every DMN is expected to have the data in WAS DB
      return getDMNFromDefinitonDetails(dmnDefinitionDetails);
    }
    dmnDefinitionDetails.forEach(dmnDefinitionDetail -> {
      String defId = dmnDefinitionDetail.getDefinitionId();
      requestState.addValue(defId, defId);
      DmnFetchTask dmnFetchTask = new DmnFetchTask(defId, defId, bpmnEngineDefinitionServiceRest);
      taskList.add(dmnFetchTask);
    });

    State resp =
        new RxExecutionChain(requestState, taskList.toArray(new DmnFetchTask[taskList.size()]))
            .execute();
    resp.getAll().forEach((k, v) -> {
      WASHttpResponse<DmnResponse> response = (WASHttpResponse<DmnResponse>) v;
      if (response.isSuccess2xx()) {
        dmnRespons.add(response.getResponse());
      } else {
        WorkflowLogger
            .error(() -> WorkflowLoggerRequest.builder().className(this.getClass().getSimpleName())
                .message("Exception occurred while fetching DMN response for definitionId"));
      }
    });

    return dmnRespons;
  }

  /**
   * update the internal_status of definition
   *
   * @param definitionId input definition id
   * @param status {@link InternalStatus} input status value
   * @return how many rows updated
   */
  @Trace
  public void updateDefinitionState(@Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId,
      InternalStatus status) {
    int count =
        definitionDetailsRepository.updateInternalStatus(status, Arrays.asList(definitionId));
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("Marked internal_status=%s for definitionId=%s", status, definitionId)
        .downstreamComponentName(DownstreamComponentName.WAS)
        .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));
    WorkflowVerfiy.verify(count == 0, WorkflowError.DELETE_DEFINITION_FAILED);
  }

  /**
   * @param definitionId input definition id
   * @param ownerId input owner Id
   * @return Definition details for given definition id and ownerId else throws
   *         INVALID_DEFINITION_DETAILS exception
   */
  @Trace
  public DefinitionDetails findByDefinitionId(
      @Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId, String ownerId) {

	Optional<DefinitionDetails> defnDetailsOpt = definitionDetailsRepository
        .findByDefinitionIdAndOwnerId(definitionId, Long.parseLong(ownerId));

    DefinitionDetails definitionDetails = defnDetailsOpt.orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_DETAILS,definitionId));
    /**
     * Add Workflow in Context.
     *
     */
    contextHandler
    	.addKey(WASContextEnums.WORKFLOW, definitionDetails.getTemplateDetails().getTemplateName());

    return definitionDetails;

  }

  /**
   * Returns workflowType based on template
   * @param definitionId
   * @param ownerId
   * @return
   */
  public String getWorkflowType(String definitionId, String ownerId){
    DefinitionDetails definitionDetails = findByDefinitionId(definitionId,ownerId);
    return CustomWorkflowType.getActionKey(definitionDetails.getTemplateDetails().getTemplateName());
  }

  /**
   * @param ownerId input company id
   * @return all definition details for a given company
   */
  public List<DefinitionDetails> getAllDefinitionList(long ownerId) {
    return definitionDetailsRepository.findByOwnerIdAndModelType(ownerId, ModelType.BPMN)
        .orElse(null);
  }

  /**
   * For a given company, get all the definitions excluding the ones MARKED_FOR_DELETE.
   * The definition xml data, placeholder values, lookup keys etc are not retrieved.
   *
   * @param ownerId company id
   * @return all definition details for a given company
   */
  public List<DefinitionDetails> getAllDefinitionListForDeletion(long ownerId) {
    return definitionDetailsRepository
        .findByOwnerIdModelTypeAndInternalStatusNotInWithoutDefinitionData(
            ownerId, ModelType.BPMN, Arrays.asList(InternalStatus.MARKED_FOR_DELETE))
        .orElse(Collections.emptyList());
  }

  /**
   * @param workflowId : Workflow id
   * @param ownerId : input company id
   * @return : All Bpmn Definition List
   */
  public List<DefinitionDetails> getDefinitionListByWorkflowId(String workflowId, Long ownerId) {
    return definitionDetailsRepository.findByWorkflowIdAndOwnerId(workflowId, ownerId)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
  }

  /**
   * @param definitionKey : definition key
   * @param ownerId       : input company id
   * @return : All Bpmn Definition List
   */
  public List<DefinitionDetails> getDefinitionListByDefinitionKeyAndOwnerId(String definitionKey,
      Long ownerId) {
    return definitionDetailsRepository.findByDefinitionKeyAndOwnerId(definitionKey, ownerId)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
  }

  /**
   * @param defDetails
   * @param statusToUpdate
   */
  @Transactional
  public void updateInternalStatusAndPublishDomainEvent(
      List<DefinitionDetails> defDetails, InternalStatus statusToUpdate) {
    /** update the definition state as MARKED_FOR_DELETE for All Definitions */
    if (CollectionUtils.isEmpty(defDetails)) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No definitions found to mark for delete")
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION));
      return;
    }

    List<String> definitionIds =
        defDetails.stream().map(DefinitionDetails::getDefinitionId).collect(Collectors.toList());

    int count = definitionDetailsRepository.updateInternalStatus(statusToUpdate, definitionIds);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Updated status=%s for definitionIds=%s count=%s",
                    InternalStatus.MARKED_FOR_DELETE, definitionIds, count)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION));

    Optional<List<DefinitionDetails>> definitionDetailList =
        definitionDetailsRepository.findAllByDefinitionsInParentId(definitionIds, definitionIds);

    definitionDetailList.ifPresent(
        definitionDetails -> {
          List<DomainEntityRequest<DefinitionDetails>> domainEvents =
              definitionDetails.stream()
                  .map(
                      definitionDetail ->
                          DomainEntityRequest.<DefinitionDetails>builder()
                              .request(definitionDetail)
                              .entityChangeAction(EntityChangeAction.DELETE)
                              .build())
                  .collect(Collectors.toList());

          definitionDomainEventHandler.publishAll(domainEvents);
        });

    // Insert enabled definition details into cache
    definitionDetailList.ifPresent(definitionDetails -> {
      definitionDetails
              .stream()
              .filter(dd -> dd.getModelType().equals(ModelType.BPMN))
              .forEach(dd-> enabledDefinitionCacheService.updateCacheWithDefinitionDetails(dd));
    });

  }

  /**
   * * This method finds enabled definition for a given Template
   *
   * @param templateId
   * @param ownerId
   * @param enabled
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *         null</>
   */
  public List<DefinitionDetails> findEnabledDefinitionForTemplate(String templateId, String ownerId,
      Status enabled) {
    return definitionDetailsRepository
        .findByOwnerIdAndTemplateDetails_IdAndStatusAndModelTypeAndInternalStatusIsNull(
            Long.parseLong(ownerId), templateId, enabled, ModelType.BPMN)
        .orElse(null);
  }

  private DeployDefinitionResponse.DeployedDefinition getProcessDefinition(
      Map<String, DeployDefinitionResponse.DeployedDefinition> processDefinitionsMap) {
    return processDefinitionsMap.values().stream().findFirst()
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.CAMUNDA_DEPLOYMENT_FAILED));
  }

  /**
   * Creates and saves definition in data store using template information
   *
   * @param deployDefinitionResponse
   * @param templateDetails
   * @param templateModelInstance
   * @param ownerId
   * @return
   */
  public List<DefinitionDetails> saveUpdateDefinitionDetailsFromTemplate(
      DeployDefinitionResponse deployDefinitionResponse, TemplateDetails templateDetails,
      TemplateModelInstance templateModelInstance, String ownerId) {

    try {
      List<DefinitionDetails> definitionDetails = new ArrayList<>();
      int version = 1;

      // Get latest version and mark old definition as stale
      if (templateModelInstance.isUpdate()) {

        Optional<TemplateDetails> oldTemplateDetails =
                templateDetailsRepository.findByTemplateNameAndVersion(
                        templateDetails.getTemplateName(),
                        templateDetails.getVersion() - 1);

        // Get previous definition and mark it stale
        final Optional<DefinitionDetails> oldDefinitionDetails =
                fetchDefinitionsByStatus(
                        oldTemplateDetails
                                .orElseThrow(
                                        () ->
                                                new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST))
                                .getId(),
                        null)
                        .stream()
                        .findAny();

        DefinitionDetails oldDefinitionDetail =
            oldDefinitionDetails.orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

        SystemTags newSystemTag = new SystemTags(), oldSystemTag = new SystemTags();
        // checking if current template  has systemTag
        checkAndSetSystemTagObjectFromTemplate(newSystemTag, templateDetails.getTag());
        // checking if previous template has system tags
        checkAndSetSystemTagObjectFromTemplate(
            oldSystemTag, oldTemplateDetails.get().getTag());

        // Get previous definition and mark it stale old definition will only be stale if it doesn't
        //  have template tag version
        if (Objects.isNull(newSystemTag.getTag(SYSTEM_TAG))
            || Objects.nonNull(newSystemTag.getTag(SYSTEM_TAG).getVersion())
                && Objects.isNull(oldSystemTag.getTag(SYSTEM_TAG))) {
          oldDefinitionDetail.setInternalStatus(InternalStatus.STALE_DEFINITION);
        }
        definitionDetails.add(oldDefinitionDetail);

          // TODO see if DMN's also need to be marked stale
          // Get version to be used for current definition
          version = templateDetails.getVersion();

      }

      definitionDetails.addAll(buildDefinitionDetailsEntityFromTemplate(deployDefinitionResponse,
          templateDetails, templateModelInstance, ownerId, version));

      WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
          .message("Performing insert/update in db for definition"));

      List<DefinitionDetails> definitionDetailsList = definitionDetailsRepository.saveAll(definitionDetails);

      DefinitionDetails definitionDetail = definitionDetailsList.stream().
              filter(defDetail -> defDetail.getModelType() == ModelType.BPMN)
              .findFirst()
              .orElse(null);

      definitionDomainEventHandler.publish(
              DomainEntityRequest.<DefinitionDetails>builder()
                      .request(definitionDetail)
                      .entityChangeAction(templateModelInstance.isUpdate() ? EntityChangeAction.UPDATE : EntityChangeAction.CREATE)
                      .build());

      return definitionDetailsList;

    } catch (DataAccessException e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Exception occurred while performing database operation=%s")
          .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .className(this.getClass().getName()).stackTrace(e));
      throw new WorkflowGeneralException(WorkflowError.INTERNAL_EXCEPTION, e);

    } catch (Exception e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Exception occured while saving/updating definition. Please try again!")
          .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .className(this.getClass().getName()).stackTrace(e));
      throw new WorkflowGeneralException(WorkflowError.DEFINITION_SAVE_ERROR, e);
    }
  }

  /**
   * Executes the disable/delete appconnect workflow
   * @param markedDefinitionDetailsList
   * @param realmId
   */
  public void executeDisableDeleteWorkflowTasks(final String realmId, final List<DefinitionDetails> markedDefinitionDetailsList) {
    State request = new State();
    request.addValue(AsyncTaskConstants.DEFINITION_DETAILS, markedDefinitionDetailsList);
    request.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    new RxExecutionChain(
        request,
        new DisableDeleteWorkflowTask(
            definitionDetailsRepository, appConnectService, authDetailsService, eventScheduleHelper, schedulingService))
        .executeAsync();
  }

  /**
   * Get's definition details for all BPMN/DMN out of template details
   *
   * @param templateDetails
   * @param deployDefinitionResponse
   * @param ownerId
   * @param model
   * @param ownerId
   * @param version
   * @return
   */
  private List<DefinitionDetails> buildDefinitionDetailsEntityFromTemplate(
      DeployDefinitionResponse deployDefinitionResponse, TemplateDetails templateDetails,
      TemplateModelInstance model, String ownerId, int version) {

    Map<String, DeployDefinitionResponse.DeployedDefinition> processDefinitionsMap =
        deployDefinitionResponse.getDeployedProcessDefinitions();

    // Assuming is we have one BPMN, get the processDefinition
    DeployDefinitionResponse.DeployedDefinition bpmnDefinition =
        getProcessDefinition(processDefinitionsMap);

    // Get the decisionDefinitions reference
    Map<String, DeployDefinitionResponse.DeployedDefinition> decisionDefinitionsMap =
        deployDefinitionResponse.getDeployedDecisionDefinitions();

    DefinitionDetails bpmnDefinitionDetails =
        buildDefinitionDetailsObjectFromTemplate(templateDetails, bpmnDefinition, ownerId,
            BpmnProcessorUtil.convertBpmnModelInstanceToByteArray(model.getBpmnModelInstance()),
            ModelType.BPMN, null, version);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(bpmnDefinitionDetails);

    // Add the dmns in the list which will be saved with the bpmn
    if (MapUtils.isNotEmpty(decisionDefinitionsMap)) {
      definitionDetailsList.addAll(Streams
          .mapWithIndex(decisionDefinitionsMap.values().stream(),
              (decisionDefinition, index) -> buildDefinitionDetailsObjectFromTemplate(
                  templateDetails, decisionDefinition, ownerId,
                  BpmnProcessorUtil.convertDmnModelInstanceToByteArray(
                      model.getDmnModelInstanceList().get((int) (index))),
                  ModelType.DMN, bpmnDefinition.getId(), version))
          .collect(Collectors.toList()));
    }
    return definitionDetailsList;

  }


  /**
   * Get definition details object out of template details
   *
   * @param templateDetails
   * @param deployedDefinition
   * @param ownerId
   * @param definitionData
   * @param modelType
   * @param parentId
   * @param version
   * @return
   */
  private DefinitionDetails buildDefinitionDetailsObjectFromTemplate(
      TemplateDetails templateDetails,
      DeployDefinitionResponse.DeployedDefinition deployedDefinition, String ownerId,
      byte[] definitionData, ModelType modelType, String parentId, int version) {
    return DefinitionDetails.builder()
        .definitionId(deployedDefinition.getId())
        .definitionKey(deployedDefinition.getKey())
        .modelType(modelType)
        .definitionName(templateDetails.getDisplayName())
        .description(templateDetails.getDescription())
        .templateDetails(templateDetails)
        .ownerId(Long.parseLong(ownerId))
        .createdByUserId(templateDetails.getCreatedByUserId())
        .modifiedByUserId(templateDetails.getCreatedByUserId())
        .status(templateDetails.getStatus())
        .parentId(parentId)
        .recordType(templateDetails.getRecordType())
        .lookupKeys(templateDetails.getTag() != null ?Objects.toString(templateDetails.getTag()): null)
        .definitionData(definitionData)
        .version(version)
        .build();
  }

  @Trace
  private List<DefinitionDetails> fetchDefinitionsByStatus(
      @Tracer(key = WASContextEnums.TEMPLATE_ID) String templateId,
      InternalStatus internalStatus) {
    final Optional<List<DefinitionDetails>> byOwnerIdAndTemplateDetails =
        definitionDetailsRepository
            .findByTemplateDetailsAndInternalStatus(
                TemplateDetails.builder().id(templateId).build(), internalStatus);

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .message("Definition fetched by templatedId=%s", templateId)
        .downstreamComponentName(DownstreamComponentName.WAS)
        .downstreamServiceName(DownstreamServiceName.WAS_UPDATE_DEFINITION));

    return byOwnerIdAndTemplateDetails
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));
  }

  /**
   * Get {@link BpmnResponse} from definition details table
   *
   * @param definitionDetails {@link DefinitionDetails}
   * @return {@link BpmnResponse}
   */
  private Optional<BpmnResponse> getBpmnFromDefinitonDetails(
      final DefinitionDetails definitionDetails) {

    return Optional.ofNullable(definitionDetails.getDefinitionData())
        .map(data -> new BpmnResponse(definitionDetails.getDefinitionId(), new String(data, StandardCharsets.UTF_8)));
  }

  /**
   * Returns list of definition data from DB
   *
   * @param dmnDefinitionDetails list of {@link DefinitionDetails}
   * @return list of {@link DmnResponse}
   */
  private List<DmnResponse> getDMNFromDefinitonDetails(
      final List<DefinitionDetails> dmnDefinitionDetails) {

    if (CollectionUtils.isEmpty(dmnDefinitionDetails)) {
      return Collections.emptyList();
    }
    return dmnDefinitionDetails.stream()
        .map(
            definitionDetails ->
                new DmnResponse(
                    definitionDetails.getDefinitionId(),
                    new String(definitionDetails.getDefinitionData(), StandardCharsets.UTF_8)))
        .collect(Collectors.toList());
  }

  public DefinitionDetails getDefinitionDetails(String definitionId) {
    return definitionDetailsRepository.findByDefinitionId(definitionId).orElse(null);
  }

  /**
   * This method finds enabled definition for a given Template id, owner id and record type without
   * definition data xml
   *
   * @param ownerId
   * @param templateId
   * @param recordType
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *     null</>
   */
  public List<DefinitionDetails> findEnabledDefinitionForTemplateAndRecordType(
      String ownerId, String templateId, String recordType) {
    return definitionDetailsRepository
        .findEnabledDefinitionForOwnerIdTemplateIdRecordTypeAndModelType(
            Long.parseLong(ownerId), templateId, RecordType.fromType(recordType), ModelType.BPMN)
        .orElse(Collections.EMPTY_LIST);
  }

  /**
   * If current definition is custom, check if precanned definition is present for same owner and
   * recordtype and action
   *
   * @param ownerId
   * @param templateName
   * @param recordType
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *     empty list</>
   */
  public List<DefinitionDetails> findEnabledDefinitionForPrecannedTemplateAndRecordType(
      String ownerId, String templateName, RecordType recordType) {
    CustomWorkflowType customWorkflowType =
        CustomWorkflowType.getCustomWorkflowForTemplateName(templateName);

    String templateNamePrecanned = recordType.getRecordType().concat(customWorkflowType.getActionKey());
    return definitionDetailsRepository
        .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
            Long.parseLong(ownerId), recordType, templateNamePrecanned, Status.ENABLED, ModelType.BPMN)
        .orElse(Collections.EMPTY_LIST);
    }

  /**
   * If current definition is custom, get all STALE precanned definitions for same owner and
   * recordtype and action
   *
   * @param ownerId
   * @param templateName
   * @param recordType
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *     empty list</>
   */
  public List<DefinitionDetails> findStaleDefinitionForPrecannedTemplateAndRecordType(
      String ownerId, String templateName, RecordType recordType) {
    CustomWorkflowType customWorkflowType =
        CustomWorkflowType.getCustomWorkflowForTemplateName(templateName);

    String templateNamePrecanned = recordType.getRecordType().concat(customWorkflowType.getActionKey());
    return definitionDetailsRepository
        .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatus(
            Long.parseLong(ownerId), recordType, templateNamePrecanned, Status.ENABLED, ModelType.BPMN,
            InternalStatus.STALE_DEFINITION)
        .orElse(Collections.EMPTY_LIST);
    }

  /**
   * If current definition is custom, check if precanned definition is present for same owner and
   * recordtype and action
   *
   * @param ownerId
   * @param templateName
   * @param recordType
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *     empty list</>
   */
  public List<DefinitionDetails> findEnabledDefinitionForCustomTemplateAndRecordType(
      String ownerId, String templateName, RecordType recordType) {
    CustomWorkflowType customWorkflowType = CustomWorkflowType.getCustomWorkflow(templateName.replace(recordType.getRecordType(), ""));

    String templateNameCustom = customWorkflowType.getTemplateName();
    return definitionDetailsRepository
        .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
            Long.parseLong(ownerId), recordType, templateNameCustom, Status.ENABLED, ModelType.BPMN)
        .orElse(Collections.EMPTY_LIST);
  }

  /**
   * Check if there are enabled custom definitions for the record type, If yes and record only
   * supports single definition, throw error.
   *
   * @param ownerId realm id
   * @param templateDetails template details
   * @param recordType record type
   */
  public void checkIfExistsActiveCustomDefinition(
      String ownerId, TemplateDetails templateDetails, String recordType) {
    CustomWorkflowType customWorkflowType =
        CustomWorkflowType.getCustomWorkflowForTemplateName(templateDetails.getTemplateName());

    if (Objects.nonNull(customWorkflowType)
        && !customWorkflowType.canCreateMultipleEnitityDefinitions()) {

      List<DefinitionDetails> definitionDetailsList =
          findEnabledDefinitionForTemplateAndRecordType(
              ownerId, templateDetails.getId(), recordType);
      WorkflowVerfiy.verify(
          !CollectionUtils.isEmpty(definitionDetailsList),
          WorkflowError.ENABLED_DEFINITION_ALREADY_EXISTS);
    }
  }

  /**
   * This method finds enabled definition for a given owner id, definition key and Internal status type
   *
   * @param ownerId
   * @param definitionKey
   * @return : unique definition with given owner-id, definition key and internal status
   */
  public DefinitionDetails findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
      Long ownerId, String definitionKey) {
    return definitionDetailsRepository.findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
        ownerId, definitionKey);
  }

  /**
   * This method returns the child BpmnModelInstance from the parent BpmnModelInstance for a given actionKey
   * @param parentBpmnModelInstance
   * @return
   */
  public BpmnModelInstance getChildBpmnModelInstanceFromParent(
      BpmnModelInstance parentBpmnModelInstance,
      String actionKey) {

    Collection<CallActivity> callActivities = parentBpmnModelInstance.getModelElementsByType(
        CallActivity.class);

    if (CollectionUtils.isEmpty(callActivities)) {
      WorkflowLogger.logError("Call Activity not present in Parent Bpmn");
      throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_MODEL_INSTANCE);
    }

    String calledElementName =
        callActivities.stream().map(CallActivity::getCalledElement)
            .filter(calledElement -> calledElement.toLowerCase().contains(actionKey))
            .findFirst().orElseThrow(() ->
                new WorkflowGeneralException(WorkflowError.CALLED_ELEMENT_ACTION_KEY_MISMATCH));

    Optional<TemplateDetails> optionalCalledElementTemplateDetails =
        templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(
            calledElementName, Status.ENABLED);

    TemplateDetails calledElementTemplateDetails = optionalCalledElementTemplateDetails.orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.INVALID_TEMPLATE_DETAILS));

    return BpmnProcessorUtil.readBPMN(calledElementTemplateDetails.getTemplateData());
  }

  /**
   * This method returns if activityDetails are present or not in the definitionactivityDetails
   * table Will return true only for approvals and reminders Is used to identify old reminder
   * workflows from new
   *
   * @param definitionId
   * @return
   */

  public boolean isActivityDetailsPresent(String definitionId) {
    return definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(definitionId)
        .isPresent();
  }

  /**
   * This method will delete all the definition with supplied parent ids.
   *
   * @param ids
   * @return
   */

  public void deleteAllByParentIdIn(List<String> ids) {
      definitionDetailsRepository.deleteAllByParentIdIn(ids);
  }

}
