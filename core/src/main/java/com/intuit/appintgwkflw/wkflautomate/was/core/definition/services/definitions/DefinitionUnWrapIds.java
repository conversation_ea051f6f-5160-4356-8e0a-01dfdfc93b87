package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;

/**
 * Author: <PERSON><PERSON> <br>
 * Date: 28/01/20 <br>
 * Description: This process un-wrap the ids which is being set by {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler}.
 * Un-Wrap: Id has been updated as defined by {@link
 * com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId} which has
 * entityId_RealmID_UUID
 *
 * <p>This method takes ids in above form and reset to entityId_RealmID_UUID -> entityId
 */

@UtilityClass
public class DefinitionUnWrapIds {

  // NOT needed
  public void updateProcessId(BpmnModelInstance bpmnModelInstance, String realmId) {
    WorkflowLogger.logDebug("Un-wrapping process ID");
    // There would be only one process
    Optional<Process> processOptional =
        bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst();
    // update the name of the definition
    processOptional.ifPresent(
        process -> {
          process.setId(WasUtils.unWrapId(process.getId(), realmId));
        });
  }

  public void updateWorkflowStepId(DefinitionInstance definitionInstance, String realmId) {
    WorkflowLogger.logDebug("Un-wrapping workflow step ID");
    // get dmn instances for the bpmn
    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(workflowStep -> updateWorkflowStepIds(workflowStep, realmId));
  }

  public void updateWorkflowStepIds(WorkflowStep workflowStep, String realmId) {
    GlobalId workflowGlobalId = workflowStep.getId();

    workflowStep.setId(
        workflowGlobalId.setLocalId(WasUtils.unWrapId(workflowGlobalId.getLocalId(), realmId)));

    updateTriggerId(workflowStep.getTrigger(), realmId);
    updateActionIds(workflowStep.getActions(), realmId);
    updateWorkflowConditionId(workflowStep.getWorkflowStepCondition(), realmId);
  }

  public void updateTriggerId(Trigger trigger, String realmId) {
    if (ObjectUtils.isNotEmpty(trigger)) {
      WorkflowLogger.logDebug("Un-wrapping trigger ID");
      GlobalId triggerGlobalId = trigger.getId();
      trigger.setId(
          triggerGlobalId.setLocalId(WasUtils.unWrapId(triggerGlobalId.getLocalId(), realmId)));
    }
  }

  public void updateActionIds(List<WorkflowStep.ActionMapper> actions, String realmId) {
    if (Objects.nonNull(actions)) {

      actions.forEach(actionMapper -> updateActionId(actionMapper.getAction(), realmId));
    }
  }

  public void updateActionId(Action action, String realmId) {

    if (Objects.nonNull(action)) {
      WorkflowLogger.logDebug("Un-wrapping action ID");
      GlobalId globalActionId = action.getId();
      action.setId(
          globalActionId.setLocalId(WasUtils.unWrapId(globalActionId.getLocalId(), realmId)));
    }
  }

  public void updateWorkflowConditionId(
      WorkflowStepCondition workflowStepCondition, String realmId) {
    if (Objects.nonNull(workflowStepCondition)) {
      GlobalId globalConditionId = workflowStepCondition.getId();
      workflowStepCondition.setId(
          globalConditionId.setLocalId(WasUtils.unWrapId(globalConditionId.getLocalId(), realmId)));
      updateRulesMappedActionKeys(workflowStepCondition.getRuleLines(), realmId);
    }
  }

  public void updateRulesMappedActionKeys(List<RuleLine> ruleLines, String realmId) {
    if (ruleLines != null) {
      ruleLines.forEach(
          ruleLine -> {
            Optional<String> mappedKeyOptional =
                ruleLine.getMappedActionKeys().stream().findFirst();
            if (!mappedKeyOptional.isPresent()) {
              return;
            }
            String mappedKey = mappedKeyOptional.get();
            ruleLine.setMappedActionKeys(
                Collections.singletonList(WasUtils.unWrapId(mappedKey, realmId)));
          });
    }
  }
}
