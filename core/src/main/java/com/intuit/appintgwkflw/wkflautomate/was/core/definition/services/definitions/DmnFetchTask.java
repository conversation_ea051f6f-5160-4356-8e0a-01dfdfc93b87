package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

public class DmnFetchTask implements Task {

  private String requestKey;
  private String responseKey;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  public DmnFetchTask(
      String requestKey,
      String responseKey,
      BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest) {
    this.requestKey = requestKey;
    this.responseKey = responseKey;
    this.bpmnEngineDefinitionServiceRest = bpmnEngineDefinitionServiceRest;
  }

  @Override
  public State execute(State inputRequest) {
    State state = new State();
    try {
      state.addValue(
          responseKey,
          bpmnEngineDefinitionServiceRest.getDMNXMLDefinition(inputRequest.getValue(requestKey)));
    } catch (Exception e) {
      throw new WorkflowGeneralException(WorkflowError.DMN_NOT_FOUND, e);
    }
    return state;
  }
}
