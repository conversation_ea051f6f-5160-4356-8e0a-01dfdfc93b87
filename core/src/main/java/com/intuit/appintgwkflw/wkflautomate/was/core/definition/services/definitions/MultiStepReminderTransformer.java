package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CLOSE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CREATE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECUR_FREQUENCY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.START_INDEX_VALUE;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.MultiStepBpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This class has a function to transform the payload for read definition for custom Reminder
 * workflows to recurring reminder workflows
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepReminderTransformer extends BaseDefinitionTransformer {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final TemplateActionBuilder templateActionBuilder;
  private final MultiStepBpmnProcessorImpl multiStepBpmnProcessorImpl;

  /**
   * This function returns true if the read call is from the VD flow and the existing definition is
   * not yet updated to MCR
   *
   * @param transformationDecisionDTO
   * @return
   */
  @Override
  protected boolean isMigrationCase(TransformationDecisionDTO transformationDecisionDTO) {
    return (!transformationDecisionDTO.isActivityDetailsPresent()
        && transformationDecisionDTO.isTemplateDataPassed());
  }


  /**
   * This function transforms the sdef payload for reminders to MCR for VD UI. The definition is not
   * migrated and will be migrated only on update.
   *
   * @param readDefinition    the readPayload for definition we need to migrate
   * @param definitionDetails definitionDetails for the readDefinition
   */
  @Override
  protected void migrate(Definition readDefinition, DefinitionDetails definitionDetails) {
    try {
      WorkflowLogger.logInfo("step=multiStepReminderTransformer status=started definitionId=%s ownerId=%s",
          definitionDetails.getDefinitionId(), definitionDetails.getOwnerId());
      String recordType = definitionDetails.getRecordType().getRecordType();
      List<WorkflowStep> workflowSteps = readDefinition.getWorkflowSteps();

      // building action group for multi condition
      List<WorkflowStep.ActionMapper> actionMappers = workflowSteps.stream().findFirst().get()
          .getActions();
      String actionKey = actionMappers.stream().findFirst().get().getActionKey();
      Action action = buildAction(actionMappers, actionKey, recordType,
          definitionDetails.getOwnerId());
      ActionGroup actionGroup = new ActionGroup();
      actionGroup.setActionKey(actionKey);
      actionGroup.setAction(action);

      // creating composite step
      WorkflowStep compositeStep = buildCompositeStep(actionGroup, definitionDetails);
      workflowSteps.add(compositeStep);

      // building condition step
      WorkflowStep conditionStep = buildConditionStep(workflowSteps);
      Optional<RuleLine> ruleLineForConditionStep = conditionStep.getWorkflowStepCondition()
          .getRuleLines().stream().findFirst();

      // Setting the workflowStepCondition for the Composite step
      if (ruleLineForConditionStep.isPresent()) {
        // Will have exactly 1 day condition
        Optional<RuleLine.Rule> compositeRule = ruleLineForConditionStep.get().getRules().stream()
            .filter(MultiStepUtil::isRuleTypeComposite).findFirst();

        // Removing the 'DAYS' rule from the conditionStep
        ruleLineForConditionStep.get().getRules().remove(compositeRule.get());

        //Add to composite step
        WorkflowStepCondition compositeStepCondition = new WorkflowStepCondition();
        RuleLine ruleLineForCompositeStep = new RuleLine();
        ruleLineForCompositeStep.setRules(Arrays.asList(compositeRule.get()));

        // Set Id for condition
        GlobalId compositeStepConditionId = GlobalId.builder()
            .setRealmId(Long.toString(definitionDetails.getOwnerId()))
            .setTypeId(compositeStep.getTypeId())
            .setLocalId(WorkflowConstants.CUSTOM_DECISION_ELEMENT
                .concat(WorkflowConstants.HYPHEN)
                .concat(UUID.randomUUID().toString()))
            .build();

        compositeStepCondition.setRuleLines(Arrays.asList(ruleLineForCompositeStep));
        compositeStepCondition.setId(compositeStepConditionId);
        compositeStep.setWorkflowStepCondition(compositeStepCondition);
        compositeStep.setStepType(StepTypeEnum.WORFKLOWSTEP);
        compositeStep.setNext(Collections.emptyList());
      }

      // Adding templatedata
      Template template = readDefinition.getTemplate();
      template.setWorkflowSteps(readDefinition.getWorkflowSteps());
      multiStepBpmnProcessorImpl.setTemplateDataForDefinition(template);
      WorkflowLogger.logInfo("Successfully transformed sDef definition to MCR step=multiStepReminderTransformer status=completed");

    } catch (Exception migrateException) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Exception=%s Payload=%s step=multiStepReminderTransformer status=error", migrateException, readDefinition));
    }
  }

  /**
   * This function builds the condition step and links it to the composite step
   *
   * @param workflowSteps
   * @return
   */
  private WorkflowStep buildConditionStep(List<WorkflowStep> workflowSteps) {
    WorkflowStep conditionStep = workflowSteps.stream().findFirst().get();
    WorkflowStep.StepNext yesPath = new WorkflowStep.StepNext();
    yesPath.setWorkflowStepId(workflowSteps.get(1).getId().toString());
    yesPath.setLabel(NextLabelEnum.YES);
    conditionStep.setNext(START_INDEX_VALUE, yesPath);
    conditionStep.setStepType(StepTypeEnum.CONDITION);
    conditionStep.setActions(null);
    return conditionStep;
  }

  /**
   * This function builds the composite step with the action group
   *
   * @param actionGroup
   * @param definitionDetails
   * @return
   */
  private WorkflowStep buildCompositeStep(ActionGroup actionGroup,
      DefinitionDetails definitionDetails) {
    WorkflowStep compositeStep = new WorkflowStep();
    compositeStep.setActionGroup(actionGroup);
    compositeStep.setStepType(StepTypeEnum.ACTION);
    GlobalId actionStepId = GlobalId.builder()
        .setRealmId(Long.toString(definitionDetails.getOwnerId()))
        .setLocalId(definitionDetails.getDefinitionId()).build();
    compositeStep.setId(actionStepId);
    return compositeStep;
  }

  /**
   * This function builds the action for the composite step
   *
   * @param actionMappers
   * @param actionKey
   * @param recordType
   * @param companyId
   * @return
   */
  private Action buildAction(List<WorkflowStep.ActionMapper> actionMappers, String actionKey,
      String recordType, Long companyId) {
    List<Action> subActions = actionMappers.stream()
        .filter(actionMapper -> CustomWorkflowType.REMINDER.getActionKey()
            .equalsIgnoreCase(actionMapper.getActionKey()))
        .map(WorkflowStep.ActionMapper::getAction)
        .collect(Collectors.toList());
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    //Ex. parentActionId = "sendForReminder"
    String parentActionId = MultiStepUtil.getTemplateDataForReadOneTemplate(record, actionKey);
    GlobalId actionId = GlobalId.builder()
        .setRealmId(Long.toString(companyId))
        .setLocalId(parentActionId).build();
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup recordActionGroup = MultiStepUtil.getFilteredRecordActionGroup(
        record, actionKey);
    Action actionForParameters = templateActionBuilder.buildTemplateStepAction(
        record,
        recordActionGroup,
        parentActionId);

    // Value for FilterCloseTask parameter, the task will always exist even if not selected on UI
    // This might not be needed if we keep the filter condition within the recurrence
    Optional<InputParameter> filterCloseTaskParameter = subActions.stream()
        .filter(filterAction -> filterAction.getId().getLocalId().equals(CREATE_TASK))
        .findFirst().get().getParameters().stream()
        .filter(parameter -> parameter.getParameterName().equalsIgnoreCase(CLOSE_TASK))
        .findAny();

    Action action = new Action();
    action.setId(actionId);
    action.setSubActions(subActions);
    action.setSelected(Boolean.TRUE);
    action.setParameters(actionForParameters.getParameters());
    action.setName(actionForParameters.getName());

    // DefaultValues for other parameters
    List<String> filterCloseTaskConditionDefaultValue = filterCloseTaskParameter.isPresent() ?
        filterCloseTaskParameter.get().getFieldValues() : new ArrayList<>();
    Map<String, List<String>> defaultFieldValuesForParameters = new HashMap<>();

    // Setting ZERO for SDEF flow. Setting up ZERO as default value in Config will distort VD View.
    defaultFieldValuesForParameters.put(RECUR_FREQUENCY, Arrays.asList(WorkflowConstants.ZERO));
    defaultFieldValuesForParameters.put(FILTER_CLOSE_TASK_CONDITIONS,
        filterCloseTaskConditionDefaultValue);

    // isRecurring and maxScheduleCount default values are taken from config and recurFrequency and FilterCloseTaskConditions will be overriden from the default map above.
    for (InputParameter inputParameter : action.getParameters()) {
      if(defaultFieldValuesForParameters.containsKey(inputParameter.getParameterName())) {
        inputParameter.setFieldValues(
            defaultFieldValuesForParameters.get(inputParameter.getParameterName()));
      }
    }


    return action;
  }

  /**
   * @param readDefinition    definition which was last working correctly
   * @param definitionDetails definitionDetails for the readDefinition
   */
//  TODO : This needs to be implemented
  @Override
  protected void rollback(Definition readDefinition, DefinitionDetails definitionDetails) {

  }


}
