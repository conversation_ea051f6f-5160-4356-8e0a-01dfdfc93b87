package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.WorkflowDefinitionComparator;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.RecurrenceParserUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.v4.Context;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import static com.intuit.v4.GlobalId.create;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;


@AllArgsConstructor
@Component
public class SingleDefinitionRead implements DefinitionDetailsRead {

    private BpmnProcessorImpl bpmnProcessor;

    private WorkflowDefinitionComparator workflowDefinitionComparator;

    private final CustomWorkflowConfig customWorkflowConfig;

    private MetricLogger metricLogger;

    private static final RequestContext requestContext = new RequestContext(new Context());
    /**
     * For Single definition, read using template details data present in the
     * TemplateDetails table. Post migration to Single Definition we would sunset the
     * definitionData from the definition details table.
     * @param definitionDetails {@link DefinitionDetails}
     * @return {@link BpmnResponse}
     */
    @Override
    public BpmnResponse getBPMNXMLDefinition(DefinitionDetails definitionDetails) {
        // Get the data from WAS DB
        WorkflowLogger.logInfo("Reading single definition for definitionId=%s",
                definitionDetails.getDefinitionId());
        return new BpmnResponse(definitionDetails.getDefinitionId(),
                new String(definitionDetails.getTemplateDetails().getTemplateData(), StandardCharsets.UTF_8));
    }

    /**
     * Substitutes the Definition V4 object from placeholder value in the database.
     * For sample placeholder value JSON see below
     * <a href="file:core/src/test/resources/placeholder/placeholder_value.json">PlaceHolder Value</a>
     * @param definition {@link Definition}
     * @param bpmnDefinitionDetail {@link DefinitionDetails}
     */
    @Override
    public void substitutePlaceHolder(Definition definition, DefinitionDetails bpmnDefinitionDetail) {

        JSONObject placeholder = new JSONObject(bpmnDefinitionDetail.getPlaceholderValue());
        JSONObject userVariables = placeholder.getJSONObject(WorkflowConstants.USER_VARIABLES);
        definition.getWorkflowSteps().forEach(workflowStep -> {
            appendIdForWorkflowStep(workflowStep, bpmnDefinitionDetail);
            workflowStep.getActions().forEach(substituteActions(
                userVariables, bpmnDefinitionDetail.getRecordType().getRecordType(),
                definition.getTemplate()));
            substituteTrigger(workflowStep.getTrigger(), userVariables, definition.getTemplate(), bpmnDefinitionDetail.getRecordType().getRecordType());
        });

    }

    /**
     * Method that substitutes the value of recurrence in definition from userVariables
     * @param definition
     * @param bpmnDefinitionDetail
     */
    @Override
    public void substituteRecurrence(Definition definition, DefinitionDetails bpmnDefinitionDetail){
        if(ObjectUtils.isEmpty(bpmnDefinitionDetail.getPlaceholderValue())){
            return;
        }
        JSONObject placeholder = new JSONObject(bpmnDefinitionDetail.getPlaceholderValue());
        JSONObject userVariables = placeholder.getJSONObject(WorkflowConstants.USER_VARIABLES);
        if(userVariables.has(WorkFlowVariables.RECURRENCE_RULE_KEY.getName())){
            JSONObject recurrenceRuleJsonObject = ObjectConverter.convertObject(
                userVariables.getString(WorkFlowVariables.RECURRENCE_RULE_KEY.getName()), JSONObject.class);
            definition.setRecurrence(RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJsonObject));
        }
    }

    /**
     * Substitute the trigger parameters with placeholder values. Currently since PreCanned workflow supports,
     * Trigger, so condition for it. Custom Workflows doesnt have triggers
     * @param trigger
     * @param userVariables
     * @param template
     * @param recordType
     */
    private void substituteTrigger(Trigger trigger, JSONObject userVariables, Template template, String recordType){
        if (ObjectUtils.isNotEmpty(trigger)) {
            String placeholderActionKey = trigger.getId().getLocalId();
            if (userVariables.has(placeholderActionKey) && userVariables.getJSONObject(placeholderActionKey).has(WorkflowConstants.PARAMETERS)) {
                JSONObject actionData = userVariables.getJSONObject(placeholderActionKey);
                JSONObject parameters = actionData.getJSONObject(WorkflowConstants.PARAMETERS);
                if (parameters.has(WorkflowConstants.ENTITY_OPERATION)) {
                    //converting entityOperations from ["create, update"] to a list.
                    //https://jira.intuit.com/browse/IPPC-7216 bug on AC side.
                    //Target state this code shpould go away with event in.
                    convertEntityOperations(parameters);
                }
                trigger.getParameters().forEach(substituteInputParameters(
                    parameters, recordType, template));
           }
        }
    }
    private void convertEntityOperations(JSONObject parameters) {
        JSONObject entityOperation = parameters.getJSONObject(WorkflowConstants.ENTITY_OPERATION);
        if (entityOperation.has(WorkflowConstants.FIELD_VALUE)) {
            JSONArray operations = entityOperation.getJSONArray(WorkflowConstants.FIELD_VALUE);
            List<String> result = IntStream.range(0, operations.length())
                .mapToObj(operations::getString)
                .flatMap(s -> Stream.of(s.split(WorkflowConstants.COMMA)))
                .collect(Collectors.toList());
            entityOperation.put(WorkflowConstants.FIELD_VALUE, result);
        }
    }
    /**
     * Method to get Actions and parameters substituted from placeholders for Single Definition.
     * @param userVariables
     * @param recordType
     * @return Consumer for ActionMapper
     */
    private Consumer<WorkflowStep.ActionMapper> substituteActions(JSONObject userVariables,
        String recordType, Template template) {
        return actionMapper -> {
            String localId = actionMapper.getAction().getId().getLocalId();
//          For Pre-canned workflows there's no actionKey. Only actionId
            String placeholderActionKey = localId;

//          For Custom workflows placeholderKey is actionKey:actionId
            if (CustomWorkflowUtil.isCustomWorkflow(template)) {
                placeholderActionKey = actionMapper.getActionKey().concat(WorkflowConstants.COLON).concat(localId);
            }

            if (userVariables.has(placeholderActionKey)) {
                JSONObject actionData = userVariables.getJSONObject(placeholderActionKey);
                if (actionData.has(WorkflowConstants.PARAMETERS)) {
                    JSONObject parameters = actionData.getJSONObject(WorkflowConstants.PARAMETERS);
                    actionMapper.getAction().selected(actionData.getBoolean(WorkflowConstants.SELECTED));
                    actionMapper.getAction().getParameters().forEach(substituteInputParameters(
                        parameters, recordType, template));
                }
            }
        };
    }

    /***
     * Method to substitute Input Parameters from placeholders for Single Definition.
     * @param parameters
     * @param recordType
     * @return Consumer for InputParameters
     */
    private Consumer<InputParameter> substituteInputParameters(JSONObject parameters,
        String recordType, Template template) {
        return inputParameter -> {
            Record record = customWorkflowConfig.getRecordObjForType(recordType);
            if (parameters.has(inputParameter.getParameterName())) {
                JSONObject param = parameters.getJSONObject(inputParameter.getParameterName());
                inputParameter.setFieldValues((List<String>) param.toMap().getOrDefault(
                        WorkflowConstants.FIELD_VALUE, new ArrayList<>()));
                //If its custom workflow then replace field values from config.
                if (CustomWorkflowUtil.isCustomWorkflow(template)) {
                    List<String> replacedFieldValues = CustomWorkflowUtil
                        .getReplacedFieldValuesWithDisplayNames(
                            inputParameter.getFieldValues(), record.getHelpVariables());
                    inputParameter.setFieldValues(replacedFieldValues);
                }
            }
        };
    }

    /**
     * In case of Single Definition we read the template data, which doesn't contain the appended ID (realmId-uuid)
     * We utilize the definitionKey in {@link DefinitionDetails} to extract the realmId-uuid and append the IDs for
     * workflowStep and update the same for definition update request to work correctly.
     * @param workflowStep {@link WorkflowStep}
     * @param definitionDetails {@link DefinitionDetails}
     */
    private void appendIdForWorkflowStep(WorkflowStep workflowStep, DefinitionDetails definitionDetails) {
        //Returns "_realmId_uuid" from "customReminder_realm_uuid"
        String keyToAppend = definitionDetails.getDefinitionKey().substring(
                definitionDetails.getDefinitionKey().indexOf(definitionDetails.getOwnerId().toString()) - 1);
        workflowStep.setId(GlobalId.builder().setLocalId(workflowStep.getId().getLocalId().concat(keyToAppend)).
                setRealmId(workflowStep.getId().getRealmId()).build());
    }

    /**
     * This method compares the definition object populated using placeholder values with
     * the definition created using definition data stored in definition table
     *
     * A new Definition object using current definition object and xml data from definition table is constructed
     * This new definition object is compared with the current definition object for any differences
     * @param placeholderDefinition
     */
    @Override
    @Metric(name = MetricName.COMPARE_WORKFLOW_DEFINITION, type = Type.APPLICATION_METRIC)
    public void verifyPlaceholderDefinition(Definition placeholderDefinition, DefinitionDetails bpmnDefinitionDetail, List<String> dmnXmlStrings, String realmId ) {

        WorkflowLogger.logInfo("step=CompareDefinitionStarted Comparing single definition %s with user definition", placeholderDefinition.getName());
        try {
            // Step 1: Construct a deep copy of the current definition object (already populated using placeholders)
            // Constructing a deep copy to not alter the definition object as it is to be returned
            Definition userDefinition = requestContext.marshallFromJson(requestContext.marshallToJson(placeholderDefinition), Definition.class);

            //Step 2: Get Definition data from the definition details table
            BpmnResponse bpmnResponse = getXMLDefinitionData(userDefinition, bpmnDefinitionDetail);

            if(Objects.isNull(bpmnResponse)){
                WorkflowLogger.logError(" step=CompareDefinitionFailed Couldn't compare placeholder definition in readOne, no definition data present definitionName=%s", placeholderDefinition.getName());
                return ;
            }
            //Step 3: Generate the user definition template object using bpmnResponse
            Template template = generateTemplateUsingBpmnResponse(bpmnResponse, bpmnDefinitionDetail, dmnXmlStrings, realmId);

            if(Objects.isNull(template)){
                WorkflowLogger.logError(" step=CompareDefinitionFailed Couldn't compare placeholder definition in readOne, error while generating template definitionName=%s", placeholderDefinition.getName());
                return ;
            }

            //Step 4: Replace the template data and workflow steps stored in the user definition clone with definition data stored in DB
            userDefinition.setTemplate(template);
            userDefinition.setWorkflowSteps(template.getWorkflowSteps());

            //Step 5: Compare the single definition object and user definition object
            boolean isDifferenceDetected = workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(requestContext.marshallToJson(userDefinition), requestContext.marshallToJson(placeholderDefinition));
            if(isDifferenceDetected){
                metricLogger.logErrorMetric(MetricName.COMPARE_WORKFLOW_DEFINITION, Type.APPLICATION_METRIC, new WorkflowGeneralException(WorkflowError.COMPARE_SINGLE_DEFINITION_ERROR));
            }
        } catch (Exception e) {
            WorkflowLogger.logError(" step=CompareDefinitionFailed Failure while comparing single definition and user definition in readOne", e);
        }
    }

    private Template generateTemplateUsingBpmnResponse(BpmnResponse bpmnResponse, DefinitionDetails bpmnDefinitionDetail, List<String> dmnXmlStrings, String realmId) {
        // generate DefinitionInstance object from bpmn and dmn model instances
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                BpmnProcessorUtil.getBpmnModelInstanceFromXml(bpmnResponse.getBpmn20Xml()),
                BpmnProcessorUtil.getDmnModelInstanceListFromXml(dmnXmlStrings),
                bpmnDefinitionDetail.getTemplateDetails());

        Template template = null;
        try {
            template =
                    (Template)
                            bpmnProcessor.processBpmn(
                                    definitionInstance,
                                    create(
                                            realmId,
                                            WorkflowConstants.TEMPLATE_TYPE_ID,
                                            definitionInstance.getTemplateDetails().getId()),
                                    true);
        } catch (Exception e) {
            WorkflowLogger.logError("step=CompareDefinitionFailed Couldn't generate user definition template object for comparison", e);
        }
        return template;
    }

    private BpmnResponse getXMLDefinitionData(Definition definitionClone, DefinitionDetails bpmnDefinitionDetail) {
        //Get definition data from the definition object
        return Optional.ofNullable(bpmnDefinitionDetail.getDefinitionData())
            .map(data -> new BpmnResponse(bpmnDefinitionDetail.getDefinitionId(), new String(data, StandardCharsets.UTF_8))).orElse(null);
    }
}
