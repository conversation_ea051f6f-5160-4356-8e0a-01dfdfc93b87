package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.v4.workflows.Definition;
import lombok.Builder;
import lombok.Data;

/**
 * This DTO is used to decide whether a definition should be transformed or not
 * This class is added here since adding it to was-entity was causing circular dependencies
 * <AUTHOR>
 */

@Data
@Builder
public class TransformationDecisionDTO {

  private Definition definition;
  private DefinitionDetails definitionDetails;
  private String templateId;
  private boolean isActivityDetailsPresent;
  private boolean isTemplateDataPassed;
}
