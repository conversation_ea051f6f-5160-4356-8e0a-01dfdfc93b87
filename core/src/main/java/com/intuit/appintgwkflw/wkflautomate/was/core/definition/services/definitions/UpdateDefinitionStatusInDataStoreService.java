package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.DefinitionDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class UpdateDefinitionStatusInDataStoreService {

  private DefinitionDetailsRepository definitionDetailsRepository;

  private final DefinitionDomainEventHandler definitionDomainEventHandler;
  private final EnabledDefinitionCacheService cacheService;

  /**
   * updates the disable status in definition details and process details
   *
   * @param definitionInstance input definition details
   */
  @Transactional
  public void updateStatusForDisabled(DefinitionInstance definitionInstance) {
    Status status = fetchStatus(definitionInstance);

    InternalStatus definitionInternalStatus =
        Status.DISABLED == status ? InternalStatus.MARKED_FOR_DISABLE : null;

    updateDefinitionStatus(definitionInstance, status, definitionInternalStatus);

  }

  /**
   * updates the enable status in definition details
   *
   * @param definitionInstance input definition details
   */
  @Transactional
  public void updateStatusForEnabled(DefinitionInstance definitionInstance) {
    Status status = fetchStatus(definitionInstance);

    InternalStatus definitionInternalStatus =
        Status.DISABLED == status ? InternalStatus.MARKED_FOR_DISABLE : null;

    updateDefinitionStatus(definitionInstance, status, definitionInternalStatus);
  }

  /**
   * update the status as MARKED_FOR_DELETE for all input the definition details
   *
   * @param definitionInstance input definition details
   * @param ownerId company detail
   */
  public void updateInternalStatusForDowngrade(
      DefinitionInstance definitionInstance, String ownerId) {

    if (CollectionUtils.isEmpty(definitionInstance.getDefinitionDetailsList())) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No definition details present for given ownerId=%s", ownerId)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
      return;
    }

    List<String> definitionIdList =
        definitionInstance
            .getDefinitionDetailsList()
            .stream()
            .map(DefinitionDetails::getDefinitionId)
            .collect(Collectors.toList());

    int count =
        definitionDetailsRepository.updateInternalStatus(
            InternalStatus.MARKED_FOR_DOWNGRADE, definitionIdList);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Marked status=%s for definitionIds=%s count=%s",
                    InternalStatus.MARKED_FOR_DOWNGRADE, definitionIdList, count)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));

    WorkflowVerfiy.verify(count == 0, WorkflowError.DELETE_ALL_WORKFLOWS_FAILED);
  }

  /**
   * @param definitionInstance input definition details
   * @param status status to be updated in DB
   * @param definitionInternalStatus internal status to be updated in DB
   */
  private void updateDefinitionStatus(DefinitionInstance definitionInstance,
      Status status, InternalStatus definitionInternalStatus) {
    int count =
        definitionDetailsRepository.updateInternalStatusAndStatus(
            status,
            definitionInternalStatus,
            definitionInstance.getDefinition().getId().getLocalId());

    // Fetching DMN details as its required for publishing domainEvents when definition gets enabled/disabled
    Optional<List<DefinitionDetails>> definitionDetailsList =  definitionDetailsRepository.findByDefinitionIdOrParentId(definitionInstance.getDefinitionDetails().getDefinitionId(),
            definitionInstance.getDefinitionDetails().getDefinitionId());

    definitionDetailsList.ifPresentOrElse(definitionDetails -> {

      List<DomainEntityRequest<DefinitionDetails>> domainEvents =  definitionDetails.stream()
              .map( definitionDetail -> DomainEntityRequest.<DefinitionDetails>builder()
                              .request(definitionDetail)
                              .entityChangeAction(EntityChangeAction.UPDATE)
                              .build()
              ).collect(Collectors.toList());

      definitionDomainEventHandler.publishAll(domainEvents);

    }, Collections::emptyList);

    // Insert enabled definition details into cache
    definitionDetailsList.ifPresent(definitionDetails -> {
      definitionDetails
            .stream()
            .filter(dd -> dd.getModelType().equals(ModelType.BPMN))
            .forEach(dd-> cacheService.updateCacheWithDefinitionDetails(dd));
    });


    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Marked status=%s for definitionId=%s count=%s",
                    status, definitionInstance.getDefinition().getId().getLocalId(), count)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));

    WorkflowVerfiy.verify(count == 0, WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED);
  }

  /**
   * @param definitionInstance input definition details
   * @return status
   */
  public Status fetchStatus(DefinitionInstance definitionInstance) {
    return Status.DISABLED
            .getStatus()
            .equalsIgnoreCase(definitionInstance.getDefinition().getStatus().value().toLowerCase())
        ? Status.DISABLED
        : Status.ENABLED;
  }
}
