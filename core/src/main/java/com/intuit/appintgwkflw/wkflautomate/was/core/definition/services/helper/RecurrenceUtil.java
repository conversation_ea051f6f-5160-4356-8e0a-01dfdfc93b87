package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_REMINDER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TIME_FORMAT;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.RecurrenceParserUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;

import java.io.ByteArrayInputStream;
import java.util.Objects;
import java.util.Optional;

import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.Template;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.TimerEventDefinitionImpl;
import org.camunda.bpm.model.bpmn.instance.EventDefinition;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.IntermediateCatchEvent;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * Utility class for setting recurrence information in custom and single definition
 */
@UtilityClass
public class RecurrenceUtil {
  private int[] HOURS_BUCKET = {7, 8, 9};
  private int[] MINUTES_BUCKET = {0, 15, 30, 45};

  /**
   * Get the process variable name for startDate and recurEvent activity in the bpmn
   * If the process variable is ${cronExpression}, this method returns cronExpression
   * @param id
   * @param definitionInstance
   * @return
   */
  public String getRecurrenceProcessVariables(String id, DefinitionInstance definitionInstance) {
    if (StringUtils.isBlank(id)) {
      return null;
    }
    Optional<TimerEventDefinitionImpl> timerEventOptional = getTimerEvent(id, definitionInstance);
    String processVariableName = null;
    if (timerEventOptional.isPresent()) {
      TimerEventDefinitionImpl timerEvent = timerEventOptional.get();
      // Get process variable for startDate
      if (Objects.nonNull(timerEvent.getTimeDate())) {
        processVariableName =  stripVariableName(timerEvent.getTimeDate().getTextContent());
      }
      // Get process variable for cron expression
      else if (Objects.nonNull(timerEvent.getTimeCycle())) {
        processVariableName = stripVariableName(timerEvent.getTimeCycle().getTextContent());
      }
    }
    return processVariableName;
  }

  /**
   * Utility method to return startDate to be replaced in camunda activity
   * @param recurrenceRule
   * @return
   */
  public String getFormattedRecurrenceStartDate(RecurrenceRule recurrenceRule){
    DateTime startDate = recurrenceRule.getStartDate();
    DateTimeFormatter fmt = DateTimeFormat.forPattern(TIME_FORMAT);
    String startDateOutput = fmt.print(startDate);
    return startDateOutput;
  }

  /**
   * Utility Method to return cron expression to be replaced in Camunda Element
   *
   * @param recurrenceRule
   * @return
   */
  public String getCronExpression(RecurrenceRule recurrenceRule) {
    return RecurrenceHandler.getHandler(recurrenceRule.getRecurType())
        .getRecurrence(recurrenceRule);
  }

  /**
   * Method to get the timerEvent in then bpmn
   * @param id
   * @param definitionInstance
   * @return
   */
  public Optional<TimerEventDefinitionImpl> getTimerEvent(String id, DefinitionInstance definitionInstance){
    IntermediateCatchEvent intermediateCatchEvent =
        definitionInstance.getBpmnModelInstance().getModelElementById(id);

    Optional<EventDefinition> eventDefinitionOptional =
        intermediateCatchEvent.getEventDefinitions().stream().findFirst();

    TimerEventDefinitionImpl timerEvent = null;

    if (eventDefinitionOptional.isPresent()) {
      timerEvent = (TimerEventDefinitionImpl) eventDefinitionOptional.get();
    }
    return Optional.ofNullable(timerEvent);
  }

  /**
   *  Utility method to get the processVariable name by stripping the parenthesis and dollar sign
   *  Input: ${startDate} Output: startDate
   *  In case of invalid format (does not follow Juel) of process variable, error will be thrown from camunda during save and trigger call.
   *  TODO: add validations in template validate API to make sure activities have proper Juel expressions
   */
  private String stripVariableName(String processVariable){
    String processVariableName = null;
    try {
      processVariableName = processVariable.substring(processVariable.indexOf("{") + 1, processVariable.indexOf("}")).trim();
    } catch (Exception ex) {
      WorkflowLogger.logError("Error while fetching process variable name for recurEvent processVariable=%s cause=%s",
          processVariable, ex.getMessage());
    }
    return processVariableName;
  }

  /**
   * recurrence is considered valid when object is non-null and has values populated for the
   * recurrence type and interval
   *
   * @param recurrenceRule
   * @return boolean
   */
  public boolean isValidRecurrence(RecurrenceRule recurrenceRule) {
    return Objects.nonNull(recurrenceRule)
        && Objects.nonNull(recurrenceRule.getRecurType())
        && Objects.nonNull(recurrenceRule.getInterval())
        && Objects.nonNull(recurrenceRule.getStartDate());
  }
  
  /**
   * recurrence is considered valid when object is non-null and has values populated for the
   * recurrence type,interval,startDate,endDate
   *
   * @param recurrenceRule
   * @return boolean
   */
	public boolean isValidRecurrenceWithEndDate(RecurrenceRule recurrenceRule) {
		return Objects.nonNull(recurrenceRule) && Objects.nonNull(recurrenceRule.getRecurType())
				&& Objects.nonNull(recurrenceRule.getInterval()) && Objects.nonNull(recurrenceRule.getEndDate());
	}


  /**
   * Returns recurrence object
   *
   * @param data
   * @return
   */
  public RecurrenceRule prepareRecurrence(byte[] data) {
    BpmnModelInstance modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(data));
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    FlowElement startEventElement = BpmnProcessorUtil.findStartEventElement(modelInstance);
    Optional<CamundaProperty> camundaPropertyOptional =
            BpmnProcessorUtil.getCamundaProperty(
                    startEventElement, WorkFlowVariables.RECURRENCE_RULE_KEY.getName());

    if (camundaPropertyOptional.isPresent()) {

      JSONObject recurrenceRuleJson =
              ObjectConverter.convertObject(
                      camundaPropertyOptional.get().getCamundaValue(), JSONObject.class);
      recurrenceRule = RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson);
    }
    return recurrenceRule;
  }


  public static RecurrenceRule getRecurrenceForPrecannedTemplates(Template template, FeatureFlagManager featureFlagManager, String realmId) {
    if (StringUtils.isNotEmpty(realmId) && featureFlagManager.getBoolean(WorkflowConstants.TIME_BASED_REMINDERS_FF, realmId) && !ObjectUtils.isEmpty(template.getLabels()) && template.getLabels().stream().anyMatch(label -> CUSTOM_REMINDER.equals(label.getName().toLowerCase()))) {
      long id = Long.parseLong(realmId);
      RecurrenceRule recurrenceRule = new RecurrenceRule();
      recurrenceRule.setRecurType(RecurTypeEnum.DAILY);
      recurrenceRule.setInterval(1);
      recurrenceRule.setTimeZone(EventScheduleConstants.TIMEZONE);
      recurrenceRule.setStartDate(DateTime.now().plusDays(1));
      TimeDuration recurrenceTime = new TimeDuration();
      recurrenceTime.setHours(HOURS_BUCKET[(int) (id % HOURS_BUCKET.length)]);
      recurrenceTime.setMinutes(MINUTES_BUCKET[(int) (id % MINUTES_BUCKET.length)]);
      recurrenceRule.setRecurrenceTime(recurrenceTime);
      return recurrenceRule;
    }
    return template.getRecurrence();
  }
}
