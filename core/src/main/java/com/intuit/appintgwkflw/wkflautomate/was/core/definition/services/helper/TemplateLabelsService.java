package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.PrecannedTemplateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.NameValue;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Template;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Populates labels for custom and precanned templates
 */
@Component
@AllArgsConstructor
public class TemplateLabelsService {

  private TranslationService translationService;
  private CustomWorkflowConfig customWorkflowConfig;
  private PrecannedTemplateConfig precannedTemplateConfig;
  private WASContextHandler wasContextHandler;

  /**
   * Populates labels in the list of templates
   * @param templates list of templates
   */
  public void fill(List<Template> templates){
    templates.forEach(this::fill);
  }

  /**
   * Populate labels in the template object
   * @param template template object
   */
  public void fill(Template template){
    if (TemplateCategory.CUSTOM.name().equals(template.getCategory())){
      populateLabelsForCustomTemplates(template);
    }
    else{
      populateLabelsForPrecannedTemplates(template);
    }
  }

  /**
   * Populate labels for custom templates from custom template config
   */
  private void populateLabelsForCustomTemplates(Template template) {
    String name = template.getName();
    Map<String, ConfigTemplate> configTemplateMap = customWorkflowConfig.getTemplateMap();
    Optional.ofNullable(configTemplateMap.get(name))
        .ifPresent(configTemplate -> template.setLabels(prepareLabels(configTemplate.getLabels())));
  }

  /**
   * Populate labels for precanned templates from precanned template config
   */
  private void populateLabelsForPrecannedTemplates(Template template) {
    String name = template.getName();
    Map<String, ConfigTemplate> configTemplateMap = precannedTemplateConfig.getTemplateConfigMap();
    Optional.ofNullable(configTemplateMap)
        .flatMap(configMap -> Optional.ofNullable(configMap.get(name)))
        .ifPresent(configTemplate -> template.setLabels(prepareLabels(configTemplate.getLabels())));
  }

  /**
   * Prepare list of labels for the template
   * @param labels
   * @return List of v4 NameValue objects
   */
  private List<com.intuit.v4.common.NameValue> prepareLabels(List<NameValue> labels) {
    String locale = wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE);
    return Optional.ofNullable(labels)
        .map(items -> items.stream()
            .map(item ->
                new com.intuit.v4.common.NameValue()
                    .name(item.getName())
                    .value(translationService.getString(item.getValue(), locale)))
            .collect(Collectors.toList())).orElse(null);
  }
}
