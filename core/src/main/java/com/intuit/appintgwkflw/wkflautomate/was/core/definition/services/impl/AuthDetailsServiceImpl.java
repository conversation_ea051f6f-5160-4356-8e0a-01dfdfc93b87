package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import java.util.Optional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuthDetailsServiceImpl implements AuthDetailsService {

  private final AuthDetailsRepository authDetailsRepository;

  private final OfflineTicketClient offlineTicketClient;

  private final AuthHelper authHelper;

  /**
   * Gets the {@link AuthDetails} by realmId
   *
   * @param realmId
   * @return {@link AuthDetails}
   */
  @Override
  public AuthDetails getAuthDetailsFromRealmId(final String realmId) {

    WorkflowVerfiy.verifyNull(realmId, WorkflowError.INVALID_REALM_ID);
    // find the subscriptionId for the realm
    final Optional<AuthDetails> authDetails =
        authHelper.getAuthDetailsFromList(authDetailsRepository.findAuthDetailsByOwnerId(Long.parseLong(realmId)));
    return authDetails.orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.AUTH_DETAILS_NOT_FOUND));
  }

  /**
   * @param processId
   * @return updated header
   */
  @Override
  public String renewOfflineTicketAndUpdateDB(final String processId) {

    final Optional<AuthDetails> authDetailsOptional =
        authDetailsRepository.findAuthByProcessId(processId);

    // throw WORKFLOW_AUTH_DETAILS_ERROR if details not found
    final AuthDetails authDetails =
        authDetailsOptional.orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.AUTH_DETAILS_NOT_FOUND));
    return getOfflineTicketUsingAuthDetails(authDetails);
  }

  @Override
  public String renewOfflineTicketAndUpdateDB(@NonNull AuthDetails authDetails) {
    return getOfflineTicketUsingAuthDetails(authDetails);
  }

  /**
   * Renew the ticket and saves into database in async manner
   *
   * @param ownerId owner of the workflow
   * @return Authorization header
   */
  @Override
  public String renewOfflineTicketAndUpdateDB(final Long ownerId) {
    final Optional<AuthDetails> authDetailsOptional =
        authHelper.getAuthDetailsFromList(authDetailsRepository.findAuthDetailsByOwnerId(ownerId));

    // throw WORKFLOW_AUTH_DETAILS_ERROR if details not found
    final AuthDetails authDetails =
        authDetailsOptional.orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.AUTH_DETAILS_NOT_FOUND));
    return getOfflineTicketUsingAuthDetails(authDetails);
  }

  /**
   * This method fetches offline ticket using V2 api
   * if migration_status = COMPLETED, else performs async task to Save Auth details
   * and return the updated ticket header.
   *
   * @param authDetails : AuthDetails
   * @return
   */
  private String getOfflineTicketUsingAuthDetails(AuthDetails authDetails) {
    return offlineTicketClient
            .getSystemOfflineHeaderWithContextRealmForOfflineJob(String.valueOf(authDetails.getOwnerId()));
  }

  /**
   * Gets the {@link AuthDetails} by realmId and does not throw exception if data not found
   *
   * @param realmId
   * @return {@link AuthDetails}
   */
  @Override
  public AuthDetails getAuthDetailsFromRealmIdSafe(final String realmId) {

    WorkflowVerfiy.verifyNull(realmId, WorkflowError.INVALID_INPUT);
    // find the subscriptionId for the realm
    return authHelper.getAuthDetailsFromList(authDetailsRepository.findAuthDetailsByOwnerId(Long.parseLong(realmId))).orElse(null);
  }


}