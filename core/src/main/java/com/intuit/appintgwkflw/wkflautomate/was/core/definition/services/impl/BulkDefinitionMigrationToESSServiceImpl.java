package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logError;
import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logInfo;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.migration.BulkSingleDefinitionMigrationService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration.SingleDefInputMigration;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> This class is responsible for migrating definition to the ESS for which schedules
 * are not created
 */
@Service(WorkflowConstants.BULK_DEF_MIGRATION_ESS_SERVICE)
@RequiredArgsConstructor
public class BulkDefinitionMigrationToESSServiceImpl implements
    BulkSingleDefinitionMigrationService {

  private final EventScheduleHelper eventScheduleHelper;
  private final DefinitionServiceHelper definitionServiceHelper;

  @Override
  public void migrateDefinition(SingleDefInputMigration singleDefInputMigration) {
    singleDefInputMigration.getBpmnDefinitionIds().forEach(definitionId -> {
      try {
//      Fetch Definition Details
        DefinitionDetails definitionDetails = definitionServiceHelper.findByDefinitionId(
            definitionId);
        logInfo(String.format(
            "step=essDefinitionMigrationStart definitionId=%s", definitionId));
        DefinitionInstance definitionInstance = new DefinitionInstance();
        definitionInstance.setDefinitionDetails(definitionDetails);
        eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
      } catch (Exception e) {
//      For Any Exceptions log it and move forward to next defnid. It should be retried from developer again, after checking from splunk
        logError(String.format(
            "step=essMigrateDefinitionError definitionId=%s error=%s", definitionId,
            e));
        throw e;
      }
    });
  }
}
