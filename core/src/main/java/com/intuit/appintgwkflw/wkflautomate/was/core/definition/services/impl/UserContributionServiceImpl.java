package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.UCSHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.UCSConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeleteUcsTemplateResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.UcsVerifyAccessResponse;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BLANK;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class UserContributionServiceImpl implements UserContributionService {

  private final UCSHttpClient ucsHttpClient;
  private final OfflineTicketClient offlineTicketClient;
  private final UCSConfig ucsConfig;
  private final CustomWorkflowConfig customWorkflowConfig;
  private final WASContextHandler wasContextHandler;
  private final TranslationService translationService;
  private static final String DEFAULT_OFFERING_ID = "workflow";

  /**
   * This call is guarded by the flag. Once UCS is live in production then will remove this if *
   * check.
   *
   * @param resourceOwnerId
   * @param ucsVerifyAccessRequest
   */
  @Override
  public void verifyAccess(
      final String resourceOwnerId, final UcsVerifyAccessRequest ucsVerifyAccessRequest) {
    final Boolean configEnabled = ucsConfig.isEnabled();
    // Case when requester and resource realm is same.
    if ((ObjectUtils.isNotEmpty(configEnabled) && !configEnabled)
        || resourceOwnerId.equalsIgnoreCase(ucsVerifyAccessRequest.getRequesterOwnerId())) {
      return;
    }
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    // Calling via System Offline Ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        this.offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(
            ucsVerifyAccessRequest.getRequesterOwnerId()));
    requestHeaders.set(WorkflowConstants.INTUIT_OFFERING_HEADER, DEFAULT_OFFERING_ID);

    WASHttpRequest<UcsVerifyAccessRequest, UcsVerifyAccessResponse> wasHttpRequest =
        WASHttpRequest.<UcsVerifyAccessRequest, UcsVerifyAccessResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(ucsVerifyAccessRequest)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<UcsVerifyAccessResponse>() {})
            .url(this.ucsConfig.getVerifyAccess())
            .build();

    WASHttpResponse<UcsVerifyAccessResponse> response =
        this.ucsHttpClient.httpResponse(wasHttpRequest);

    boolean checkAccess =
        Boolean.TRUE.equals(
            Optional.ofNullable(response)
                .map(WASHttpResponse::getResponse)
                .map(UcsVerifyAccessResponse::isPermit)
                .orElse(null));

    WorkflowVerfiy.verify(!checkAccess, WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
  }

  @Override
  public void obfuscateDefinitionDetails(
      final Definition definition, final String recordType, boolean isMultiStep) {

    final String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definition);

    final Map<String, Map<String, List<String>>> obfuscateParameterNamesMap =
        CustomWorkflowUtil.getObfuscatedParameterMap(
            customWorkflowConfig.getRecordObjForType(recordType),actionKey);

    final Set<String> obfuscateConditionalParameterNames =
        CustomWorkflowUtil.getObfuscatedParameterSetForConditions(
            customWorkflowConfig.getRecordObjForType(recordType));

    if (Boolean.TRUE.equals(isMultiStep)) {
      obfuscateDefinitionDetailsMultiStep(
          definition,
          obfuscateParameterNamesMap,
          obfuscateConditionalParameterNames);
    } else {
      obfuscateSingleDefinitionDetails(
          definition,
          obfuscateParameterNamesMap,
          obfuscateConditionalParameterNames);
    }
  }

  /**
   * @param definitionId
   */
  @Override
  public void deletePublishedTemplate(final String definitionId) {
    final Boolean configEnabled = ucsConfig.isEnabled();
    // TODO: if config is not enabled then don't call UCS. Post testing in pre-production, we will
    // enable in Production
    if ((ObjectUtils.isNotEmpty(configEnabled) && !configEnabled)) {
      return;
    }
    final String url = String.format("%s/%s", ucsConfig.getDelete(), definitionId);

    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    // Calling via Realm Scoped Offline Ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        this.offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(
            wasContextHandler.get(WASContextEnums.OWNER_ID)));
    requestHeaders.set(WorkflowConstants.INTUIT_OFFERING_HEADER,DEFAULT_OFFERING_ID);

    WASHttpRequest<List<String>, List<DeleteUcsTemplateResponse>> wasHttpRequest =
        WASHttpRequest.<List<String>, List<DeleteUcsTemplateResponse>>builder()
            .httpMethod(HttpMethod.DELETE)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<List<DeleteUcsTemplateResponse>>() {})
            .url(url)
            .build();

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
        this.ucsHttpClient.httpResponse(wasHttpRequest);
    WorkflowLogger.logInfo("Delete records count={}", response.getResponse().size());
  }

  /**
   * Added support for Deleting all the published Templates by a company. It will be invoked in
   * company downgrade flow. This will call User Contribution Service using System Offline Ticket.
   */
  @Override
  public void deleteAllPublishedTemplates(final String realmId) {
    final Boolean configEnabled = ucsConfig.isEnabled();
    // TODO: if config is not enabled then don't call UCS. Post testing in pre-production, we will
    // enable in Production
    if ((ObjectUtils.isNotEmpty(configEnabled) && !configEnabled)) {
      return;
    }

    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    // Calling via Realm Scoped Offline Ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        this.offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(realmId));
    requestHeaders.set(WorkflowConstants.INTUIT_OFFERING_HEADER,DEFAULT_OFFERING_ID);

    WASHttpRequest<String, List<DeleteUcsTemplateResponse>> wasHttpRequest =
        WASHttpRequest.<String, List<DeleteUcsTemplateResponse>>builder()
            .httpMethod(HttpMethod.DELETE)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<List<DeleteUcsTemplateResponse>>() {})
            .url(ucsConfig.getDeleteAll())
            .build();

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
        this.ucsHttpClient.httpResponse(wasHttpRequest);
    WorkflowLogger.logInfo(
        "Delete records count using deleteAll={}", response.getResponse().size());
  }

  /**
   * For Selective and Full Downgrade
   *
   * @param definitionKeys
   */
  @Override
  public void deleteAllPublishedTemplates(final String realmId, final List<String> definitionKeys) {
    final Boolean configEnabled = ucsConfig.isEnabled();
    // TODO: if config is not enabled then don't call UCS. Post testing in pre-production, we will
    // enable in Production
    if ((ObjectUtils.isNotEmpty(configEnabled) && !configEnabled)) {
      return;
    }

    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    // Calling via Realm Scoped Offline Ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        this.offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(realmId));
    requestHeaders.set(WorkflowConstants.INTUIT_OFFERING_HEADER,DEFAULT_OFFERING_ID);

    WASHttpRequest<List<String>, List<DeleteUcsTemplateResponse>> wasHttpRequest =
        WASHttpRequest.<List<String>, List<DeleteUcsTemplateResponse>>builder()
            .httpMethod(HttpMethod.DELETE)
            .request(definitionKeys)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<List<DeleteUcsTemplateResponse>>() {})
            .url(ucsConfig.getDeleteAll())
            .build();

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
        this.ucsHttpClient.httpResponse(wasHttpRequest);
    WorkflowLogger.logInfo(
        "Delete records count using deleteAll={}", response.getResponse().size());
  }

  /**
   * This method will redact/obfuscate Single Definition Workflows that are not multistep
   *
   * @param definition
   * @param obfuscateParameterNames
   * @param obfuscateConditionalParameterNames
   */
  private void obfuscateSingleDefinitionDetails(
      final Definition definition,
      final Map<String, Map<String, List<String>>> obfuscateParameterNames,
      final Set<String> obfuscateConditionalParameterNames) {
    // Obfuscating Actions
    definition
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStep
                    .getActions()
                    .forEach(
                        redactActions(
                            definition.getTemplate(),
                            obfuscateParameterNames,
                            definition.getRecordType())));

    // obfuscate conditions
    definition
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStep
                    .getWorkflowStepCondition()
                    .getRuleLines()
                    .forEach(
                        ruleLine ->
                            ruleLine
                                .getRules()
                                .forEach(
                                    substituteRules(
                                        definition.getTemplate(),
                                        obfuscateConditionalParameterNames))));
  }

  private void obfuscateDefinitionDetailsMultiStep(
          final Definition definition,
          final Map<String, Map<String, List<String>>> obfuscateParameterNames,
          Set<String> obfuscateConditionalParameterNames) {

    // obfuscate actions
    definition.getTemplate().getWorkflowSteps().stream()
        .filter(workflowStep -> StepTypeEnum.ACTION.equals(workflowStep.getStepType()))
        .forEach(
            workflowStep -> {
              workflowStep
                  .getActionGroup()
                  .getAction()
                  .getParameters()
                  .forEach(
                      inputParameter ->
                          substituteInputParameters(
                              inputParameter,
                              definition.getTemplate(),
                              obfuscateParameterNames,
                              definition.getRecordType(),
                              workflowStep.getActionGroup().getAction().getId().getLocalId()));

              workflowStep
                  .getActionGroup()
                  .getAction()
                  .getSubActions()
                  .forEach(
                      actions ->
                          actions
                              .getParameters()
                              .forEach(
                                  inputParameter ->
                                      substituteInputParameters(
                                          inputParameter,
                                          definition.getTemplate(),
                                          obfuscateParameterNames,
                                          definition.getRecordType(),
                                          actions.getId().getLocalId())));
            });

    // obfuscate conditions
    definition.getTemplate().getWorkflowSteps().stream()
        .filter(workflowStep -> StepTypeEnum.CONDITION.equals(workflowStep.getStepType()))
        .forEach(
            workflowStep ->
                workflowStep
                    .getWorkflowStepCondition()
                    .getRuleLines()
                    .forEach(
                        ruleLine ->
                            ruleLine.getRules()
                                .forEach(
                                    substituteRules(
                                        definition.getTemplate(),
                                        obfuscateConditionalParameterNames))));
  }

  /**
   * Substitute Rule lines
   *
   * @param template
   * @param obfuscateConditionalParameterNames
   * @return
   */
  private Consumer<? super RuleLine.Rule> substituteRules(
      Template template, Set<String> obfuscateConditionalParameterNames) {
    return rule -> {
      if (CustomWorkflowUtil.isCustomWorkflow(template)
          && obfuscateConditionalParameterNames.contains(rule.getParameterName())) {
        // For instance, CONTAINS 1,2,3 ,  This will extract CONTAINS keyword and omit rest of the
        // values. Returns the logical operator and not the values
        final String trimRuleOperator =
            Optional.ofNullable(rule.getConditionalExpression())
                .map(rule1 -> rule1.split(WorkflowConstants.WHITESPACE_REGEX)[0].trim())
                .orElse(null);
        rule.setConditionalExpression(trimRuleOperator); // Obfuscate the values
      }
    };
  }

  private void substituteInputParameters(
      InputParameter inputParameter,
      Template template,
      Map<String, Map<String, List<String>>> obfuscateParameterNames,
      String recordType,
      String actionId) {

    if (CustomWorkflowUtil.isCustomWorkflow(template)
        && ObjectUtils.isNotEmpty(obfuscateParameterNames.get(actionId))
        && obfuscateParameterNames.get(actionId).containsKey(inputParameter.getParameterName())) {
      final String localisedValue =
          translationService.getFormattedString(
              obfuscateParameterNames.get(actionId).get(inputParameter.getParameterName()).stream()
                  .findFirst()
                  .orElse(null),
              wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE),
              recordType);

      inputParameter.setFieldValues(
          ObjectUtils.isNotEmpty(localisedValue)
              ? List.of(localisedValue)
              : ObjectUtils.isNotEmpty(inputParameter.getGetOptionsForFieldValue())
                      && ObjectUtils.isNotEmpty(inputParameter.getParameterType())
                      && FieldTypeEnum.LIST.equals(inputParameter.getParameterType())
                  ? Collections.nCopies(
                      inputParameter.getFieldValues().size(),
                      BLANK) // In case user has selected multiple values then return multiple
                  // Empty placeholders on the UI
                  : Collections.EMPTY_LIST); // Redacting the values with Default Values
      inputParameter.setObfuscate(Boolean.TRUE); // Setting in definition Response
    }
  }

  /**
   * Method to get Actions and parameters substituted from placeholders for Single Definition.
   *
   * @param template
   * @param obfuscateParameterNames
   * @param recordType
   * @return Consumer for ActionMapper
   */
  private Consumer<WorkflowStep.ActionMapper> redactActions(
      Template template,
      Map<String, Map<String, List<String>>> obfuscateParameterNames,
      String recordType) {
    return actionMapper -> {
      actionMapper
          .getAction()
          .getParameters()
          .forEach(
              substituteInputParameters(
                  template,
                  obfuscateParameterNames,
                  recordType,
                  actionMapper.getAction().getId().getLocalId()));
    };
  }

  /***
   * Method to substitute Input Parameters from placeholders for Single Definition.
   * @param template
   * @param obfuscateParameterNames
   * @param recordType
   * @return Consumer for InputParameters
   */
  private Consumer<InputParameter> substituteInputParameters(
      Template template,
      Map<String, Map<String, List<String>>> obfuscateParameterNames,
      String recordType,
      String actionId) {
    return inputParameter -> {
      if (CustomWorkflowUtil.isCustomWorkflow(template)
          && ObjectUtils.isNotEmpty(obfuscateParameterNames.get(actionId))
          && obfuscateParameterNames.get(actionId).containsKey(inputParameter.getParameterName())) {

        final String localisedValue =
            translationService.getFormattedString(
                obfuscateParameterNames
                    .get(actionId)
                    .get(inputParameter.getParameterName())
                    .stream()
                    .findFirst()
                    .orElse(null),
                wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE),
                recordType);

        inputParameter.setFieldValues(
            ObjectUtils.isNotEmpty(localisedValue)
                ? List.of(localisedValue)
                : ObjectUtils.isNotEmpty(inputParameter.getGetOptionsForFieldValue())
                        && ObjectUtils.isNotEmpty(inputParameter.getParameterType())
                        && FieldTypeEnum.LIST.equals(inputParameter.getParameterType())
                    ? Collections.nCopies(
                        inputParameter.getFieldValues().size(),
                        BLANK) // In case user has selected multiple values then return
                    // multiple empty placeholders on the UI
                    : Collections.EMPTY_LIST); // Redacting the values with Default Values
        inputParameter.setObfuscate(Boolean.TRUE); // Setting in definition Response
      }
    };
  }
}
