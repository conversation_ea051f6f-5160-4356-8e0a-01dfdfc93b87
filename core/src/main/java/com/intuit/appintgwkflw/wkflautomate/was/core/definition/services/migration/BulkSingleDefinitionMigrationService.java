package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.migration;

import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration.SingleDefInputMigration;

/**
 * <AUTHOR> Interface to be used while Rollbacking Single to User Definition or Migrating
 * existting User definitions to single definitions
 */
public interface BulkSingleDefinitionMigrationService {

  /**
   * @param singleDefInputMigration
   * This is SYNC API. For Definition migration wrt templateName , it will be ASYNC
   * @return Steps: 1) Fetch User Offline Key 2) Set the AuthDetails and RealmUser details in the
   * Context. 3) Call MigrationHelper which Reads Definition and Updates the defnition with the
   * updated TemplateId.
   */
  void migrateDefinition(SingleDefInputMigration singleDefInputMigration);

}

