package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeployDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveSystemDefinitionInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTemplateDetailsTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.WorkflowDefinitionEventPublishTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveDefinitionRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTaskDetailRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTemplateRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * Handles template processing for system definitions
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SystemDefinitionProcessor implements TemplateProcessor {

  private TemplateDetailsRepository templateDetailsRepository;
  private TriggerDetailsRepository triggerDetailsRepository;
  private ActivityDetailsRepository activityDetailsRepository;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private DefinitionServiceHelper definitionServiceHelper;
  private EventPublisherCapability eventPublisherCapability;
  private TemplateDomainEventHandler templateDomainEventHandler;
  private EnabledDefinitionCacheService enabledDefinitionCacheService;

  private EventPublisherUtil eventProducerUtil;
  private WorkflowTaskConfig workflowTaskConfig;

  @Override
  public State executeAction(State inputState, TemplateModelInstance model) {

    // Deploy in Camunda and Save in WAS DB
    RxExecutionChain rxExecutionChain = new RxExecutionChain(inputState);

    State outputState =
        rxExecutionChain
            .next(new DeployDefinitionTask(bpmnEngineDefinitionServiceRest))
            .next(new SaveTemplateDetailsTask(templateDetailsRepository, triggerDetailsRepository, templateDomainEventHandler))
            .next(new SaveTaskDetails(activityDetailsRepository,
            		workflowTaskConfig.isEnable()))
            .next(
                new SaveSystemDefinitionInDataStoreTask(definitionServiceHelper, model),
                new WorkflowDefinitionEventPublishTask(eventPublisherCapability, eventProducerUtil))
            .execute();

    return outputState;

  }

  @Override
  public void checkAndRollBack(State state) {

    boolean saveTemplateTaskFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE));
    boolean saveDefinitionTaskFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_TASK_FAILURE));
    boolean saveTaskDetailsFailed =
        BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE));

    /* Call Roll back's asynchronously */
    if (saveTemplateTaskFailed
        || saveDefinitionTaskFailed || saveTaskDetailsFailed) {
      //	Sequence of Rollback needs to be maintained.
      List<Task> rollbackTask = new ArrayList<>();
      rollbackTask.add(new SaveDefinitionRollBackTask(definitionDetailsRepository, definitionActivityDetailsRepository, enabledDefinitionCacheService));
      rollbackTask
          .add(new SaveTaskDetailRollBackTask(activityDetailsRepository, workflowTaskConfig.isEnable()));
      rollbackTask
          .add(new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository));

      new RxExecutionChain(state, rollbackTask.toArray(new Task[rollbackTask.size()]))
          .executeAsync();

      /* Throw exception for the call */
      if (saveDefinitionTaskFailed) {
          throw new WorkflowGeneralException(
              state.getValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_ERROR_MESSAGE),
              (Exception) state.getValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_EXCEPTION));
      } else if (saveTaskDetailsFailed) {
          throw new WorkflowGeneralException(
              state.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE),
              (Exception) state.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION));
      } else {
        throw new WorkflowGeneralException(
            state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE),
            (Exception) state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION));
      }

    }
  }

  @Override
  public DefinitionType getType() {
    return DefinitionType.SYSTEM;
  }
}
