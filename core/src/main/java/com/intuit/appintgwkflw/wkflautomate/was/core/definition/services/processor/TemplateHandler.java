package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import java.util.EnumMap;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * Process handler to get the template processor for a given definition type
 * 
 * <AUTHOR>
 *
 */
@UtilityClass
public class TemplateHandler {

  private static final Map<DefinitionType, TemplateProcessor> TEMPLATE_HANDLER_MAP =
      new EnumMap<>(
          DefinitionType.class);

  /**
   * Adds handler
   * 
   * @param definitionType
   * @param templateProcessor
   */
  public static void addHandler(DefinitionType definitionType,
      TemplateProcessor templateProcessor) {
    TEMPLATE_HANDLER_MAP.put(definitionType, templateProcessor);
  }

  /**
   * Gets the processor of a particular type
   * 
   * @param definitionType
   * @return
   */
  public static TemplateProcessor getHandler(DefinitionType definitionType) {
    return TEMPLATE_HANDLER_MAP
        .getOrDefault(definitionType, TEMPLATE_HANDLER_MAP.get(DefinitionType.USER));
  }
}
