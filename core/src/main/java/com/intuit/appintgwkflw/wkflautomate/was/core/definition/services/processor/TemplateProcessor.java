package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DefinitionDeploymentUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.request.State;

/**
 * Interface to process different type of templates
 * 
 * <AUTHOR>
 *
 */
public interface TemplateProcessor {

  /**
   * Executes all actions(DB calls, camunda API invocation etc) needed for template processing
   * 
   * @param inputState
   * @param bpmnModel
   * @param dmnModel
   * @return
   */
  State executeAction(State inputState, TemplateModelInstance model);

  /**
   * Checks for failure and rolls back operations if needed
   * 
   * @param inputState
   */
  void checkAndRollBack(State inputState);

  /**
   * Definition Type for the template processor
   * 
   * @return
   */
  DefinitionType getType();

  /**
   * Method which calls both execute & checkAndRollBack
   * @param inputState
   * @param bpmnModel
   * @param dmnModel
   */
  default State execute(State inputState, TemplateModelInstance model) {
	addDeploymentDefinitonRequest(inputState, model);
    inputState = executeAction(inputState, model);
    checkAndRollBack(inputState);
    return inputState;
  }
  
  /**
   * Adds DeplyDefintionRequest to inputState.
   * @param inputState
   * @param model
   */
  default void addDeploymentDefinitonRequest(State inputState, TemplateModelInstance model) {
	  TemplateDetails bpmnTemplateDetails = inputState.getValue(AsyncTaskConstants.BPMN_DETAILS_KEY);
	  
	  inputState.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, CamundaServiceHelper
	        .createDeployDefinitionRequest(model.getBpmnModelInstance(),
	            model.getDmnModelInstanceList(), 
	            DefinitionDeploymentUtil.getDeploymentName(bpmnTemplateDetails)));
  }
  
}
