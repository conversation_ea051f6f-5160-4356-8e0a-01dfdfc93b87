package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTemplateDetailsTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTaskDetailRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTemplateRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * /** Handles template processing for single definition
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class UserDefinitionProcessor implements TemplateProcessor {

  private TemplateDetailsRepository templateDetailsRepository;
  private TriggerDetailsRepository triggerDetailsRepository;
  private ActivityDetailsRepository activityDetailsRepository;
  private TemplateDomainEventHandler templateDomainEventHandler;
  private WorkflowTaskConfig workflowTaskConfig;

  @Override
  public State executeAction(State inputState, TemplateModelInstance model) {

    RxExecutionChain rxExecutionChain = new RxExecutionChain(inputState);
    State outputState = rxExecutionChain
        .next(new SaveTemplateDetailsTask(templateDetailsRepository, triggerDetailsRepository, templateDomainEventHandler))
        .next(new SaveTaskDetails(activityDetailsRepository,
        		workflowTaskConfig.isEnable()))
        .execute();

    return outputState;
  }

	@Override
	public void checkAndRollBack(State inputState) {
		boolean saveTemplateTaskFailed = BooleanUtils
				.isTrue(inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE));
		boolean saveTaskDetailsFailed = BooleanUtils
				.isTrue(inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE));
		if (saveTemplateTaskFailed || saveTaskDetailsFailed) {
			List<Task> rollbackTask = new ArrayList<>();
			rollbackTask.add(new SaveTaskDetailRollBackTask(activityDetailsRepository, workflowTaskConfig.isEnable()));
			rollbackTask.add(new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository));

			/* Call Roll back's asynchronously */
			new RxExecutionChain(inputState, rollbackTask.toArray(new Task[rollbackTask.size()]))
					.executeAsync();

			/* Throw exception for the call */
			if (saveTaskDetailsFailed) {
				throw new WorkflowGeneralException(
						inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE),
						(Exception) inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION));
			} else {
				throw new WorkflowGeneralException(
						inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE),
						(Exception) inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION));
			}
		}
	}

  @Override
  public DefinitionType getType() {
    return DefinitionType.USER;
  }
}
