package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * This service validates the multi condition create definition payload. Google doc mentioning the
 * List of validations :
 * https://docs.google.com/document/d/1oQYAIgqu42vd8vDCK748MWWwYthGAOvtsLaRRV2PAmw
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepValidator {

  private final MultiStepConfig multiStepConfig;

  /**
   * This method is used to validate the Create definition payload for multiCondition scenarios.
   *
   * @param definition      Definition
   * @param workflowStepMap Map<String, WorkflowStep>
   */
  public void validatePayload(Definition definition, Map<String, WorkflowStep> workflowStepMap) {

    List<WorkflowStep> workflowSteps = definition.getWorkflowSteps();

    // There should be at least one step in workflow
    if (CollectionUtils.isEmpty(workflowSteps)) {
      throw new WorkflowGeneralException(WorkflowError.STEP_DETAILS_NOT_FOUND);
    }

    if (workflowSteps.size() > multiStepConfig.getMaxNumberOfSteps()) {
      throw new WorkflowGeneralException(WorkflowError.APPROVAL_WORKFLOWS_STEPS_LIMIT_EXCEEDED);
    }

    // Get all condition steps - excluding the composite workflow steps
    List<WorkflowStep> workflowStepConditions = workflowSteps.stream().filter(
        MultiStepUtil::isConditionStep).collect(Collectors.toList());

    // Get all action steps
    List<WorkflowStep> workflowStepActions = workflowSteps.stream().filter(
        MultiStepUtil::isActionStep).collect(Collectors.toList());

    // Composite workflow steps should be leaf nodes
    workflowSteps.stream().filter(MultiStepUtil::isCompositeStep)
        .forEach(workflowStep -> {
          if (CollectionUtils.isNotEmpty(workflowStep.getNext())) {
            throw new WorkflowGeneralException(WorkflowError.COMPOSITE_WORKFLOW_STEP_NOT_LEAF_NODE);
          }
        });


    // There should be at least one condition step in the workflow.
    // If condition step is not present then only one action step can be present.
    if (CollectionUtils.isEmpty(workflowStepConditions)
        && MultiStepUtil.isSingleStepMultiCondition(definition)
        && workflowStepActions.size() > 1) {
      throw new WorkflowGeneralException(WorkflowError.CONDITION_WORKFLOW_STEP_NOT_FOUND);
    }

    WorkflowLogger.logInfo("step=validateMultiConditionPayload, numberOfApprovalNodes=%s, numberOfConditionNodes=%s",
            workflowStepActions.size(), workflowStepConditions.size());
    
    if (workflowStepActions.size() > multiStepConfig.getMaxNumberOfApprovers()) {
      throw new WorkflowGeneralException(WorkflowError.APPROVAL_WORKFLOWS_APPROVERS_LIMIT_EXCEEDED);
    }

    // Every condition should have at least either yes or no path.
    workflowStepConditions.forEach(workflowStep -> {
      if (CollectionUtils.isEmpty(workflowStep.getNext())) {
        throw new WorkflowGeneralException(WorkflowError.MISSING_NEXT_PATH_ERROR);
      }
    });

    // Every step id present in the yes and no path in the payload should have a workflow step associated with it.
    workflowSteps.forEach(workflowStep -> {
      if (CollectionUtils.isNotEmpty(workflowStep.getNext())) {
        workflowStep.getNext().forEach(nextStep -> {
          if (Objects.isNull(workflowStepMap.get(nextStep.getWorkflowStepId()))) {
            WorkflowLogger.logError(
                "The workflow step id present in the next path of condition i.e. = %s does not have a workflow step associated with it.",
                nextStep.getWorkflowStepId());
            throw new WorkflowGeneralException(WorkflowError.UNUSED_WORKFLOW_STEP);
          }
        });
      }
    });

    // The graph should not be cyclic. Note a loop is different from a cycle. We want to prevent infinite looping.
    if (isGraphCyclic(workflowSteps, workflowStepMap)) {
      throw new WorkflowGeneralException(WorkflowError.CYCLIC_WORKFLOW_STEPS_NOT_ALLOWED);
    }
  }


  /**
   * This method checks whether the workflowStepList (adjacency list) forms a cycle or not. We are
   * considering the BPMN to be a directed graph.
   *
   * @param workflowStepList List<WorkflowStep>
   * @param workflowStepMap  Map<String, WorkflowStep>
   * @return boolean
   */
  private boolean isGraphCyclic(List<WorkflowStep> workflowStepList,
      Map<String, WorkflowStep> workflowStepMap) {

    // Mark all the vertices as not visited and not part of recursion stack
    Set<String> visitedSet = new HashSet<>();
    Set<String> recursionStackSet = new HashSet<>();

    // Call the recursive helper function to detect cycle in different DFS trees
    for (WorkflowStep workflowStep : workflowStepList) {
      if (isGraphCyclicHelperFunction(workflowStepMap, workflowStep, visitedSet,
          recursionStackSet)) {
        return true;
      }
    }

    // no cycle found
    return false;
  }

  /**
   * This helper function recursively performs dfs on the current workflowStep node. visitedSet is
   * used to keep track of the visited nodes in order to prevent performing dfs again on same nodes.
   * recursionStackSet is used to keep track of the visited nodes in current dfs cycle. If cycle is
   * present then return true, otherwise return false.
   *
   * @param workflowStepMap   Map<String, WorkflowStep>
   * @param workflowStep      WorkflowStep
   * @param visitedSet        Set<String>
   * @param recursionStackSet Set<String>
   * @return boolean
   */
  private boolean isGraphCyclicHelperFunction(Map<String, WorkflowStep> workflowStepMap,
      WorkflowStep workflowStep,
      Set<String> visitedSet,
      Set<String> recursionStackSet) {

    // return true if node already present in recursion stack set
    if (recursionStackSet.contains(workflowStep.getId().getLocalId())) {
      return true;
    }

    // we will proceed if node has not been visited in the previous DFS call(s).
    if (visitedSet.contains(workflowStep.getId().getLocalId())) {
      return false;
    }

    visitedSet.add(workflowStep.getId().getLocalId());
    recursionStackSet.add(workflowStep.getId().getLocalId());

    // get the list of children
    if (CollectionUtils.isNotEmpty(workflowStep.getNext())) {
      for (WorkflowStep.StepNext stepNext : workflowStep.getNext()) {
        if (isGraphCyclicHelperFunction(
            workflowStepMap,
            workflowStepMap.get(stepNext.getWorkflowStepId()),
            visitedSet,
            recursionStackSet
        )) {
          return true;
        }
      }
    }

    // remove the current workflow step from recursionStackSet (backtracking)
    recursionStackSet.remove(workflowStep.getId().getLocalId());

    return false;
  }

  /**
   * This method is used to validate the Create definition payload for multiCondition scenarios.
   * @param definition Definition
   * @return boolean
   */
  public boolean validateMultiConditionPayload(Definition definition) {

    if (ObjectUtils.isEmpty(definition.getWorkflowSteps())) {
      return false;
    }

    // todo refactor code to save this map for later use
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
    definition.getWorkflowSteps().forEach(workflowStep ->
            workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    WorkflowLogger.logInfo("step=validateMultiConditionPayload");
    validatePayload(definition, workflowStepMap);

    return true;
  }
}