package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * validates template components
 *
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class TemplateValidator {

  private HistoryConfig historyConfig;

  public void validate(final BpmnModelInstance bpmnModelInstance, TemplateMetadata templateMetadata){
    validateTTL(bpmnModelInstance, templateMetadata);
  }

  /**
   * Validates history time to live is present and less than the permissible value
   *
   * @param bpmnModelInstance
   * @param templateMetadata
   */
  public void validateTTL(final BpmnModelInstance bpmnModelInstance, TemplateMetadata templateMetadata) {
    if(templateMetadata.isValidateHistoryTTL()){
      final Collection<Process> processList =
          bpmnModelInstance.getModelElementsByType(Process.class);
      WorkflowVerfiy.verifyNull(processList, WorkflowError.PROCESS_DETAILS_ERROR);

      processList.forEach(
          process -> {
            String historyTimeToLive = process.getCamundaHistoryTimeToLiveString();
            WorkflowVerfiy.verify(historyTimeToLive, WorkflowError.HISTORY_TTL_ERROR);
            WorkflowLogger.logInfo("History TTL=%s days for workflow=%s", historyTimeToLive, process.getName());
            WorkflowVerfiy.verify(
                Long.valueOf(historyTimeToLive) > historyConfig.getTtl(),
                WorkflowError.HISTORY_TTL_THRESHOLD_ERROR
            );
          });
    }
  }
}
