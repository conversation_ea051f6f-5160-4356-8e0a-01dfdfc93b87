package com.intuit.appintgwkflw.wkflautomate.was.core.definition.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Optional;

@Component
@AllArgsConstructor
public class ThrottleDefinitionsPerWorkflow extends ThrottleService {

    private final DefinitionDetailsRepository definitionDetailsRepository;

    public ThrottleAttribute getAttribute() {
        return ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME;
    }

    /**
     * Return number of definitions created and updated in a window for the workflow
     * Only bpmns of all status are counted in the window
     *
     * @param joinPoint
     * @return
     */
    public Pair<Integer, String> getExecutionCountAndWorkflow(final ProceedingJoinPoint joinPoint) {

        DefinitionInstance definitionInstance = ((DefinitionInstance) joinPoint.getArgs()[0]);

        String workflowName = getWorkflowName(definitionInstance);

        WorkflowLogger.logInfo(ResiliencyConstants.THROTTLE_DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_PREFIX + "Starting throttling check for workflow=%s", workflowName);

        // Find number of definitions created for the template name, from DB in the timeframe
        Integer count = definitionDetailsRepository
                .getCountOfDefinitionsPerWorkflowInTimeframe(
                        workflowName,
                        new Timestamp(System.currentTimeMillis()- throttleHelper.getTimeframeForWorkflow(workflowName, ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)),
                        new Timestamp(System.currentTimeMillis()
                        )) + 1; // Adding 1 to the final count considering the definition which is being created

        return new ImmutablePair<>(count, workflowName);
    }

    @Override
    public Integer getWarnDiff() {
        return Optional.ofNullable(throttleConfigs).map(ThrottleConfigs::getWarnDiffCount)
            .map(y -> y.get(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)).orElse(0);
    }

    /**
     * Checks scenarios in which throttling is not performed
     *
     * Throttling must be enabled for definition at root config level for any further processing.
     * If it has been disabled in the root config (throttle.definitionsPerWorkflowInTimeframe.enabled = false), all other
     * configs are ignored and no further throttling check is performed.
     *
     * Doesn't throttle if definition throttling is not enabled, if thresholds/timeframe values
     * are not present or if throttling has been disabled for the workflow
     *
     * Even if throttling has been enabled for definition at root level, further throttling is performed only if
     * a threshold value AND timeframe have been set for the workflow, or a default threshold AND
     * timeframe value have been configured.
     *
     * @param joinPoint
     * @return
     */
    public boolean isThrottlingEnabled(final ProceedingJoinPoint joinPoint) {
        DefinitionInstance definitionInstance = ((DefinitionInstance) joinPoint.getArgs()[0]);

        final String workflowName = getWorkflowName(definitionInstance);

        return throttleConfigs.isDefinitionsPerWorkflowInTimeframe() &&
            throttleHelper.isThrottlingEnabledForWorkflow(workflowName, ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME) &&
            throttleHelper.isTimeframeDefined(workflowName, ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME);
    }

    /**
     * On breach of threshold, throw throttling exception
     *
     * @param joinPoint
     */
    public void executeFailure(final ProceedingJoinPoint joinPoint, Integer executionCount) {
        // If breaches, send graphql error response
        DefinitionInstance definitionInstance = ((DefinitionInstance) joinPoint.getArgs()[0]);
        WorkflowLogger.logError(ResiliencyConstants.THROTTLE_DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_PREFIX
                + "Definitions Per Workflow threshold has been breached with numDefnsCreated=%s. Rejecting creation/update of definition.",
                executionCount);
        throw new WorkflowGeneralException(WorkflowError.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_THRESHOLD_BREACHED, getWorkflowName(definitionInstance));
    }

    public void executeWarn(final ProceedingJoinPoint joinPoint, Integer executionCount) {
        WorkflowLogger.logWarn(ResiliencyConstants.THROTTLE_DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_PREFIX
                + "Definitions Per Workflow threshold about to be breached with numDefnsCreated=%s.",
                executionCount);
    }

    /**
     * Extract workflow name
     *
     * @param definitionInstance
     * @return
     */
    private String getWorkflowName(DefinitionInstance definitionInstance) {
        return definitionInstance.getTemplateDetails().getTemplateName();
    }
}

