package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MaskedObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.util.DomainEventUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.system.interfaces.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Capabilty class, from where event will be saved/published to Outbox Tables
 */
public abstract class DomainEventPublisherCapability<T, U extends BaseEntity> {

  @Autowired private DomainEventConfig domainEventTopiConfig;
  @Autowired private DomainEventRepository domainEventRepository;

  /**
   * @param domainEntityRequest
   * @throws WorkflowEventException
   */
  @Metric(name = MetricName.DOMAIN_EVENT_PUBLISH, type = Type.EVENT_METRIC)
  public DomainEvent<? extends BaseEntity> publish(
      final DomainEntityRequest<T> domainEntityRequest) {
    DomainEventName domainEventName = DomainEventUtil.getName(domainEntityRequest);
    if (!domainEventTopiConfig.isEnabled()
        || !domainEventTopiConfig.getTopic().get(domainEventName).isEnabled()) {
      WorkflowLogger.logInfo(
          "Domain event publish config is disabled for domainEventName=%s", domainEventName);
      return null;
    }
    DomainEvent<? extends BaseEntity> event = transform(domainEntityRequest);
    event = domainEventRepository.save(event);
    WorkflowLogger.logInfo("Domain Event published successfully=%s", MaskedObjectConverter.toJson(event));
    return event;
  }

  /**
   * @param domainEntityRequest
   * @throws WorkflowEventException
   */
  @Metric(name = MetricName.DOMAIN_EVENT_PUBLISH, type = Type.EVENT_METRIC)
  public List<DomainEvent> publishAll(final List<DomainEntityRequest<T>> domainEntityRequest) {

    DomainEntityRequest<T> domEntityRequest = domainEntityRequest.stream().findFirst().orElse(null);

    DomainEventName domainEventName = DomainEventUtil.getName(domEntityRequest);

    /** If domain events are not enabled, do nothing and return */
    if (!domainEventTopiConfig.isEnabled()
        || !domainEventTopiConfig.getTopic().get(domainEventName).isEnabled()) {
      WorkflowLogger.logInfo(
              "Domain event publish config is disabled for domainEventName=%s", domainEventName);
      return null;
    }

    List<DomainEvent> domainEvents =
        domainEntityRequest.stream().map(this::transform).collect(Collectors.toList());

    List<DomainEvent> event = domainEventRepository.saveAll(domainEvents);

    DomainEvent domainEvent = domainEvents.stream().findFirst().orElse(null);

    WorkflowLogger.logInfo("Domain Event published successfully for all entities=%s", MaskedObjectConverter.toJson(domainEvent));

    return event;
  }

  public abstract DomainEvent<? extends BaseEntity> transform(
      final DomainEntityRequest<T> domainEntityRequest);

  public abstract DomainEventName getName();

  /**
   * Return topic name per domain event
   *
   * @param domainEventName
   * @return
   */
  public String getTopicDetails(DomainEventName domainEventName) {
    return domainEventTopiConfig.getTopic().get(domainEventName).getTopic();
  }

  /**
   * Returns Region information
   *
   * @return
   */
  public String getRegionDetails() {
    return domainEventTopiConfig.getRegion();
  }

  /**
   * Returns information if Topic is active or not.
   *
   * @param domainEventName
   * @return
   */
  public boolean isTopicEnabled(DomainEventName domainEventName) {
    return domainEventTopiConfig.getTopic().get(domainEventName).isEnabled();
  }

  /**
   * Appends new domain event headers to an already formed header builder.
   * This method checks if the feature flag for domain event headers is enabled,
   * and if so, it adds the specified schema version and URN to the headers' builder.
   *
   * @param headersBuilder the builder object used to construct domain event headers
   * @param schemaVersion the version of the schema to be added to the headers
   * @param urn the Uniform Resource Name to be added to the headers
   */
  public void addDomainEventHeaders(DomainEventHeaders.DomainEventHeadersBuilder headersBuilder, String schemaVersion, String urn, EntityChangeAction entityChangeAction, Integer entityVersion) {
    headersBuilder.schemaVersion(schemaVersion)
            .entityVersion(entityVersion)
            .entityChangeAction(entityChangeAction)
            .intuitEntityType(urn);
  }
}
