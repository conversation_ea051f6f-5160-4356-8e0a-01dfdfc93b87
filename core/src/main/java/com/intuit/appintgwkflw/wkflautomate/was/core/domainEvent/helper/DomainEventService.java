package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.util.DomainEventUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COLON;

/**
 * <AUTHOR> <br>
 *     </> Service class containg Helper Method for handlers to enrich headers like idempotency key
 *     etc.
 */
@Component
@AllArgsConstructor
public class DomainEventService {
  private WASContextHandler wasContextHandler;
  private ProcessDomainEventHandler processDomainEventHandler;
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;
  private DomainEventConfig domainEventTopiConfig;
  private ProcessDetailsRepoService processDetailsRepoService;
  private ActivityProgressDetailsRepository activityProgressDetailsRepository;
  private ActivityDetailsRepository activityDetailsRepository;

  private static final String MIGRATION_LOG_PREFIX =
      "Executing command=DomainEvent action=processMigration ";

  private static final String START = "start";
  private static final String END = "end";
  /**
   * Helper method to prepare Entity headers from workerActionRequest
   *
   * @param workerActionRequest
   * @return
   */
  public EventHeaderEntity prepareEntityHeaders(WorkerActionRequest workerActionRequest) {
    String idempotencyKey =
        new StringBuilder(workerActionRequest.getTaskId())
            .append(COLON)
            .append(workerActionRequest.getProcessInstanceId())
            .toString();
    String offeringId = wasContextHandler.get(WASContextEnums.OFFERING_ID);
    String tid = wasContextHandler.get(WASContextEnums.INTUIT_TID);
    return EventHeaderEntity.builder()
        .idempotencyKey(idempotencyKey)
        .tid(tid)
        .offeringId(offeringId)
        .build();
  }

  /**
   * Check if domain events are enabled or not
   *
   * @return
   */
  public boolean isDomainEventPublishEnabled() {
    return domainEventTopiConfig.isEnabled();
  }

  /**
   * Calls repository's update status method based on domain event config
   *
   * @param processDetails
   * @param processStatus
   * @param eventHeaderEntity
   * @return
   */
  public int updateStatus(
          ProcessDetails processDetails,
          ProcessStatus processStatus,
          EventHeaderEntity eventHeaderEntity,
          EntityChangeAction entityChangeAction,
          Map<String, Object> variableMap) {
    // Setting for Domain Events Publish
    processDetails.setProcessStatus(processStatus);
    processDetails.setEntityVersion(processDetails.getEntityVersion() + 1);
    DomainEntityRequest request =
        DomainEventUtil.getDomainEventRequest(
            eventHeaderEntity, processDetails, entityChangeAction, variableMap);
    return isDomainEventEnabled(request)
        ? processDetailsRepoService.updateStatusAndPublishDomainEvent(
            processDetails, processDomainEventHandler.transform(request))
        : processDetailsRepoService.updateStatus(processDetails.getProcessId(), processStatus);
  }

  /**
   * Helper method to facilitate pushing domain events for Migration
   *
   * @param processDetails
   */
  public void publishMigrationEvent(ProcessDetails processDetails) {
    String offeringId = wasContextHandler.get(WASContextEnums.OFFERING_ID);
    DomainEntityRequest request =
        DomainEventUtil.getDomainEventRequest(
            EventHeaderEntity.builder()
                .entityId(processDetails.getRecordId())
                .offeringId(
                    StringUtils.isNotBlank(offeringId)
                        ? offeringId
                        : processDetails
                            .getDefinitionDetails()
                            .getTemplateDetails()
                            .getOfferingId())
                .build(),
            processDetails,
            EntityChangeAction.UPDATE,
            Collections.emptyMap());
    if (isDomainEventEnabled(request)) {
      processDomainEventHandler.publish(request);
      EventingLoggerUtil.logInfo(
          MIGRATION_LOG_PREFIX + "type=%s step=eventPublishStarted processInstanceId=%s",
          this.getClass().getSimpleName(),
          DomainEventName.PROCESS,
          processDetails.getProcessId());
    }
  }

  /**
   * This method publish activity runtime event in case of Start/End Events
   *
   * @param workflowStateTransitionEvent
   * @param eventHeaderEntity
   * @param processDetails
   */
  @Transactional
  public void publishActivityRuntimeEvent(
      WorkflowStateTransitionEvents workflowStateTransitionEvent,
      EventHeaderEntity eventHeaderEntity,
      ProcessDetails processDetails) {

    final String id = getActivityExternalTaskPrimaryId(workflowStateTransitionEvent);
    ActivityDetail activityDetail =
        activityDetailsRepository
            .findByTemplateDetailsAndActivityId(
                processDetails.getDefinitionDetails().getTemplateDetails(),
                workflowStateTransitionEvent.getActivityMetadata().getActivityId())
            .orElse(null);

    if (ObjectUtils.isEmpty(activityDetail)) {
      WorkflowLogger.logInfo("Activity Details are not present");
      return;
    }

    // save a new record in ActivityProgressDetails
    ActivityProgressDetails activityProgressDetails =
        buildActivityProgressDetails(activityDetail, processDetails, workflowStateTransitionEvent);

    if (END.equalsIgnoreCase(workflowStateTransitionEvent.getEventType())) {
      // End Can have 2 use case
      // (1) Activity is only having END event for milestones.
      // (2)Activity is having both START and END event for milestones.
      ActivityProgressDetails activityProgressDetailsResponse =
          activityProgressDetailsRepository.findById(id).orElse(null);

      if (ObjectUtils.isNotEmpty(activityProgressDetailsResponse)) {
        // Updating the event type from start to end here
        activityProgressDetailsResponse.setStatus(workflowStateTransitionEvent.getEventType());
        activityProgressDetails = activityProgressDetailsResponse;
      }
    }

    publishActivityEvent(
        eventHeaderEntity,
        workflowStateTransitionEvent,
        activityProgressDetailsRepository.saveAndFlush(activityProgressDetails));
  }

  private void publishActivityEvent(
      EventHeaderEntity eventHeaderEntity,
      WorkflowStateTransitionEvents workflowStateTransitionEvent,
      ActivityProgressDetails activityProgressDetails) {
    ActivityProgressDetails activityProgressDetailsResponse =
        new ActivityProgressDetails(activityProgressDetails);

    DomainEntityRequest request =
        DomainEventUtil.getDomainEventRequest(
            EventHeaderEntity.builder()
                .entityId(activityProgressDetailsResponse.getId()) // This has to be the unique key
                .offeringId(
                    StringUtils.isNotBlank(eventHeaderEntity.getOfferingId())
                        ? eventHeaderEntity.getOfferingId()
                        : activityProgressDetailsResponse
                            .getProcessDetails()
                            .getDefinitionDetails()
                            .getTemplateDetails()
                            .getOfferingId())
                .build(),
            activityProgressDetails,
            DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
                workflowStateTransitionEvent),
            Collections.emptyMap()
        );

    activityRuntimeDomainEventHandler.publish(request);
  }

  /**
   * Helper Method to Determine if domain event config and topic both are enabled
   *
   * @param domainEntityRequest
   * @return
   * @param <T> : DomainEntityRequest<></>
   */
  public <T> boolean isDomainEventEnabled(final DomainEntityRequest<T> domainEntityRequest) {
    DomainEventName domainEventName = DomainEventUtil.getName(domainEntityRequest);
    boolean result =
        domainEventTopiConfig.isEnabled()
            && domainEventTopiConfig.getTopic().get(domainEventName).isEnabled();

    WorkflowLogger.logInfo(
        "Domain event publish config is for topic=%s is enabled=%s", domainEventName, result);
    return result;
  }

  /**
   * Util method to return id value to be set as Primary Key for ActivityProgressDetails Entity
   *
   * @param workflowStateTransitionEvent
   * @return
   */
  private String getActivityExternalTaskPrimaryId(
      WorkflowStateTransitionEvents workflowStateTransitionEvent) {
    return workflowStateTransitionEvent.getActivityInstanceId();
  }

  /**
   * Utility method to re-construct object to be saved. Row was updated or deleted by another
   * transaction (or unsaved-value mapping was incorrect) > Since @version is used in update
   * timestamp
   *
   * @param activityDetail
   * @param processDetails
   * @param workflowStateTransitionEvent
   * @return
   */
  private ActivityProgressDetails buildActivityProgressDetails(
      ActivityDetail activityDetail,
      ProcessDetails processDetails,
      WorkflowStateTransitionEvents workflowStateTransitionEvent) {
    // External Task id will be null for non-external task (milestone(s)). Hence, setting activity
    // instance id as unique id
    String id = getActivityExternalTaskPrimaryId(workflowStateTransitionEvent);

    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .activityDefinitionDetail(activityDetail)
            .id(id)
            .processDetails(processDetails)
            .status(
                workflowStateTransitionEvent
                    .getEventType()) // Start/End TODO: Setting EventType as status
            .name(workflowStateTransitionEvent.getActivityMetadata().getActivityName())
            .attributes(
                ObjectConverter.toJson(
                    WorkflowActivityAttributes.builder()
                        .modelAttributes(
                            workflowStateTransitionEvent.getActivityMetadata().getProperties())
                        .runtimeAttributes(
                            workflowStateTransitionEvent.getActivityMetadata().getVariables())
                        .build()))
            .build();
    return activityProgressDetails;
  }
}
