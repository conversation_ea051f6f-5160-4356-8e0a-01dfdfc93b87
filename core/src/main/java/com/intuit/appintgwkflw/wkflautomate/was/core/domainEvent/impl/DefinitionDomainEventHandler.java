package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.DomainEventPublisherCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.DomainEventMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.Definition;
import com.intuit.system.interfaces.BaseEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Handler for handling Definition Domain Events
 */
@Component(WorkflowBeansConstants.DEFINITION_HANDLER)
@AllArgsConstructor
public class DefinitionDomainEventHandler extends DomainEventPublisherCapability<DefinitionDetails, BaseEntity> {
  private final WASContextHandler contextHandler;
  private final DomainEventRepository domainEventRepository;

  @Override
  public DomainEventName getName() {
    return DomainEventName.DEFINITION;
  }

  @Override
  public DomainEvent<? extends BaseEntity> transform(
      final DomainEntityRequest<DefinitionDetails> domainEntityRequest) {

    if (Objects.isNull(domainEntityRequest.getEventHeaderEntity())) {
      EventHeaderEntity eventHeaderEntity =
          EventHeaderEntity.builder()
              .tid(
                  Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                      .orElse(UUID.randomUUID().toString()))
              .offeringId(contextHandler.get(WASContextEnums.OFFERING_ID))
              .build();
      domainEntityRequest.setEventHeaderEntity(eventHeaderEntity);
    }

    final DomainEventHeaders domainEventHeaders =
        prepareDefinitionDomainEventHeaders(domainEntityRequest);

    Definition definition =
        DomainEventMapper.mapEntityToDefinitionDomainEvent(domainEntityRequest.getRequest());

    return DomainEvent.builder()
        .topic(getTopicDetails(DomainEventName.DEFINITION))
        .partitionKey(definition.getId())
        .headers(domainEventHeaders)
        .region(getRegionDetails())
        .payload(ObjectConverter.toJson(definition))
        .version(domainEventHeaders.getEntityversion())
        .build();
  }

  private DomainEventHeaders prepareDefinitionDomainEventHeaders(
      DomainEntityRequest<DefinitionDetails> domainEntityRequest) {

    DefinitionDetails definitionDetails = domainEntityRequest.getRequest();

    DomainEventHeaders.DomainEventHeadersBuilder headersBuilder = DomainEventHeaders.builder()
        .intuitTid(
            Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                .orElse(UUID.randomUUID().toString()))
        .entityId(definitionDetails.getDefinitionId())
        .entityType(Definition.class.getName())
        .accountId(String.valueOf(definitionDetails.getOwnerId()))
        // TODO:: update offering id once finalised
        .offeringId(
            StringUtils.isAllBlank(domainEntityRequest.getEventHeaderEntity().getOfferingId())
                ? definitionDetails.getTemplateDetails().getOfferingId()
                : domainEntityRequest.getEventHeaderEntity().getOfferingId())
        .entitychangeaction(domainEntityRequest.getEntityChangeAction())
        .entityversion(definitionDetails.getEntityVersion());

    addDomainEventHeaders(headersBuilder, Definition.SCHEMA_VERSION, Definition.URN,
          domainEntityRequest.getEntityChangeAction(), definitionDetails.getEntityVersion());

    return headersBuilder.build();
  }
}