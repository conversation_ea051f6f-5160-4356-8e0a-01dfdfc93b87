package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.DomainEventPublisherCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.DomainEventMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.Template;
import com.intuit.system.interfaces.BaseEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Handler for handling Template Domain Events
 *
 */
@Component(WorkflowBeansConstants.TEMPLATE_HANDLER)
@AllArgsConstructor
public class TemplateDomainEventHandler
    extends DomainEventPublisherCapability<TemplateDetails, BaseEntity> {
  private final WASContextHandler contextHandler;
  private final DomainEventRepository domainEventRepository;

  @Override
  public DomainEventName getName() {
    return DomainEventName.TEMPLATE;
  }

  @Override
  public DomainEvent<? extends BaseEntity> transform(
      final DomainEntityRequest<TemplateDetails> domainEntityRequest) {

    if (Objects.isNull(domainEntityRequest.getEventHeaderEntity())) {
      EventHeaderEntity eventHeaderEntity =
          EventHeaderEntity.builder()
              .tid(
                  Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                      .orElse(UUID.randomUUID().toString()))
              .offeringId(contextHandler.get(WASContextEnums.OFFERING_ID))
              .build();
      domainEntityRequest.setEventHeaderEntity(eventHeaderEntity);
    }

    final DomainEventHeaders domainEventHeaders =
        prepareTemplateDomainEventHeaders(
            domainEntityRequest.getRequest(),
            domainEntityRequest.getEntityChangeAction(),
            domainEntityRequest.getEventHeaderEntity());

    Template template =
        DomainEventMapper.mapEntityToTemplateDomainEvent(domainEntityRequest.getRequest());

    return DomainEvent.builder()
        .topic(getTopicDetails(DomainEventName.TEMPLATE))
        .partitionKey(template.getId())
        .headers(domainEventHeaders)
        .region(getRegionDetails())
        .payload(ObjectConverter.toJson(template))
        .version(domainEventHeaders.getEntityversion())
        .build();
  }

  private DomainEventHeaders prepareTemplateDomainEventHeaders(
      TemplateDetails templateDetails,
      EntityChangeAction entityChangeAction,
      EventHeaderEntity headerEntity) {
    DomainEventHeaders.DomainEventHeadersBuilder headersBuilder = DomainEventHeaders.builder()
        .intuitTid(Optional.ofNullable(headerEntity.getTid()).orElse(UUID.randomUUID().toString()))
        .offeringId(contextHandler.get(WASContextEnums.OFFERING_ID))
        .entityId(templateDetails.getId()) // Setting template id
        .entityType(Template.class.getName())
        .accountId(String.valueOf(templateDetails.getOwnerId()))
        .offeringId(
            StringUtils.isNotBlank(headerEntity.getOfferingId())
                ? headerEntity.getOfferingId()
                : templateDetails.getOfferingId())
        .entitychangeaction(entityChangeAction)
        .entityversion(templateDetails.getVersion());

    addDomainEventHeaders(headersBuilder, Template.SCHEMA_VERSION, Template.URN, entityChangeAction, templateDetails.getVersion());

    return headersBuilder.build();
  }
}
