package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> MultiCondition Extractor to Fetch User Placeholder values needed for Audit.
 */
@Component
@AllArgsConstructor
public class MultiStepPlaceholderUserVariableExtractor implements
    PlaceholderUserVariableExtractor {

  private final ProcessDetailsRepoService processDetailsRepoService;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  /**
   * Fetches the User Variables for MultiCondition
   *
   * @param activityProgressDetails
   * @param runtimeAttributes
   * @return Map of ParameterNames and their field Values
   */
  @Override
  public Map<String, String> getUserVariablesForActivity(
      ActivityProgressDetails activityProgressDetails, Map<String, String> runtimeAttributes) {
//  Root Process instance id can't be empty, since the factory checks for this if condition
    String parentProcessId = runtimeAttributes.get(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID);
    //  For Eg: createTask
    String activityId = getActivityId(activityProgressDetails.getId());
//  For Eg: sendForApproval-2
    String rootActivityId = runtimeAttributes.get(WorkflowConstants.ACTIVITY_ID);

    ProcessDetails processDetails = processDetailsRepoService.findByProcessId(parentProcessId)
        .orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR)
        );

    activityProgressDetails.getProcessDetails().setParentProcessDetails(processDetails);

    DefinitionActivityDetail currentActivityDetails =
        definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
            processDetails.getDefinitionDetails().getDefinitionId(),
            activityId,
            rootActivityId
        ).orElse(null);

//  If ActivityId doesnt exists, then return empty values.
    if (Objects.isNull(currentActivityDetails)) {
      return new HashMap<>();
    }

    Map<String, Object> userVariablesActionData = ObjectConverter.fromJson(
        currentActivityDetails.getUserAttributes(), new TypeReference<Map<String, Object>>() {
        });

    Map<String, Object> parameters = (Map<String, Object>) userVariablesActionData.get(
        WorkflowConstants.PARAMETERS);

    return populateUserVariableMap(parameters);
  }

  /**
   * Sample Placeholder values -> { "parameters": { "Assignee": { "fieldValue": ["9130359273344206"] },
   * "approvalType": { "fieldValue": ["PARALLEL] }* 	} }
   *
   * @param parameters
   * @return
   */
  private Map<String, String> populateUserVariableMap(Map<String, Object> parameters) {

    Map<String, String> fieldVariableMap = new HashMap<>();
    Map<String, String> resultMap = new HashMap<>();

    parameters.entrySet().stream()
        .filter(paramEntry -> ((Map<String, Object>) Optional.ofNullable(paramEntry.getValue())
            .orElse(Collections.emptyMap())).containsKey(WorkflowConstants.FIELD_VALUE))
        .forEach(paramEntry -> {
          List<String> fieldValues = (List<String>) ((Map<String, Object>) Optional.ofNullable(
              paramEntry.getValue()).orElse(Collections.emptyMap())).get(
              WorkflowConstants.FIELD_VALUE);
          Optional.ofNullable(fieldValues)
              .flatMap(fieldValue -> fieldValue.stream().findFirst()).ifPresent(value -> {
                fieldVariableMap.put(paramEntry.getKey(), value);
              });
        });

    if (MapUtils.isNotEmpty(fieldVariableMap)) {
      resultMap.putAll(fieldVariableMap);
    }
    return resultMap;
  }

  private String getActivityId(String activityProgressDetailsId) {
    String activityId = activityProgressDetailsId.split(WorkflowConstants.COLON)[0];
    return activityId;
  }

  @Override
  public PlaceholderUserVariableExtractorType getName() {
    return PlaceholderUserVariableExtractorType.MULTI_STEP;
  }
}
