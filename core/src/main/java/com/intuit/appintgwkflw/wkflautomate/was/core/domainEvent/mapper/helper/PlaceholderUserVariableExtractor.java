package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;

import java.util.Map;


public interface PlaceholderUserVariableExtractor {
    public Map<String, String> getUserVariablesForActivity(ActivityProgressDetails activityProgressDetails, Map<String, String> runtimeAttributes);
    public PlaceholderUserVariableExtractorType getName();
}