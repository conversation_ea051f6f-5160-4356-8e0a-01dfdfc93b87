package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import java.util.Map;
import java.util.Objects;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PlaceholderUserVariableExtractorUtil {

    public PlaceholderUserVariableExtractorType getPlaceholderExtractor(
        Map<String, String> runTimeAttributes){
        
        if (Objects.nonNull(runTimeAttributes) && runTimeAttributes.containsKey(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID)) {
            return PlaceholderUserVariableExtractorType.MULTI_STEP;
        }
        return PlaceholderUserVariableExtractorType.DEFAULT;
    }
}
