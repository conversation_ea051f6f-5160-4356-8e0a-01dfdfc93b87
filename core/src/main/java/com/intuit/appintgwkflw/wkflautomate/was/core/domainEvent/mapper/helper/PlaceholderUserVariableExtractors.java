package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.EnumMap;
import java.util.Map;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PlaceholderUserVariableExtractors {

    private static final Map<PlaceholderUserVariableExtractorType, PlaceholderUserVariableExtractor> PLACEHOLDER_EXTRACTOR_MAP =
            new EnumMap<>(PlaceholderUserVariableExtractorType.class);

    public static void addHandler(PlaceholderUserVariableExtractorType placeholderUserVariableExtractorType, PlaceholderUserVariableExtractor placeholderUserVariableExtractor) {
        PLACEHOLDER_EXTRACTOR_MAP.put(placeholderUserVariableExtractorType, placeholderUserVariableExtractor);
    }

    public static PlaceholderUserVariableExtractor getHandler(PlaceholderUserVariableExtractorType placeholderUserVariableExtractorType) {
        return PLACEHOLDER_EXTRACTOR_MAP.get(placeholderUserVariableExtractorType);
    }

    public static boolean contains(PlaceholderUserVariableExtractorType placeholderUserVariableExtractorType) {

        return PLACEHOLDER_EXTRACTOR_MAP.containsKey(placeholderUserVariableExtractorType);
    }

}