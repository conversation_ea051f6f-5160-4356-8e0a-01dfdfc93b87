package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.service;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DomainEventTestService {

  DomainEvent getEventById(final String eventId);

  List<DomainEvent> getEventsByPartitionKey(final String partitionKey);

  long getCountByEventsPublishedByPartitionKey(final String partitionKey);
}
