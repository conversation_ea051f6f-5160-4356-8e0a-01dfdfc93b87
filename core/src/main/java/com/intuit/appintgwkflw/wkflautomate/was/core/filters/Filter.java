package com.intuit.appintgwkflw.wkflautomate.was.core.filters;

import com.intuit.v4.query.FilterExpression;

/**
 * The interface Filter.
 *
 * @param <T> the type parameter
 */
public interface Filter<T> {

  /**
   * Name string.
   *
   * @return the string
   */
  String name();

  /**
   * Filter t.
   *
   * @param list the list
   * @param filterExpression the filter expression
   * @return the t
   */
  T filter(T list, FilterExpression filterExpression);
}
