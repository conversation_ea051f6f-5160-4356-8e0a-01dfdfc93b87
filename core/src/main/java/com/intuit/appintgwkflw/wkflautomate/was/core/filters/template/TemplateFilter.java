package com.intuit.appintgwkflw.wkflautomate.was.core.filters.template;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.Filter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.v4.query.FilterExpression;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * The type Template filter.
 */
public abstract class TemplateFilter implements Filter<List<TemplateDetails>> {

  @Override
  public List<TemplateDetails> filter(final List<TemplateDetails> list,
      final FilterExpression filterExpression) {

    WorkflowLogger.info(
        () -> WorkflowLoggerRequest.builder()
            .className(this.getClass().getSimpleName())
            .message("Template details filter type " + name())
            .downstreamComponentName(DownstreamComponentName.WAS)
            .downstreamServiceName(DownstreamServiceName.WAS_TEMPLATE_FILTER_TYPE));

    List<TemplateDetails> result = list.stream()
        .filter(templateDetails -> filterPredicate(filterExpression, templateDetails))
        .collect(Collectors.toList());
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(result), WorkflowError.TEMPLATE_DOES_NOT_EXIST);
    return result;
  }

  /**
   * Filter predicate boolean.
   *
   * @param filterExpression the filter expression
   * @param templateDetails  the template details
   * @return the boolean
   */
  abstract boolean filterPredicate(FilterExpression filterExpression,
      TemplateDetails templateDetails);
}
