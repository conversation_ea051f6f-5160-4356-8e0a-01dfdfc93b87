package com.intuit.appintgwkflw.wkflautomate.was.core.filters.template;

import java.util.HashMap;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * The template filter factory.
 *
 * <AUTHOR>
 */
@UtilityClass
public class TemplateFilters {

  private final Map<String, TemplateFilter> TEMPLATE_FILTERS = new HashMap<>();

  /**
   * Add a template filter.
   *
   * @param filter the filter
   */
  public void add(TemplateFilter filter) {

    TEMPLATE_FILTERS.put(filter.name(), filter);
  }

  /**
   * Get a template filter.
   *
   * @param name the name
   * @return the template filter
   */
  public TemplateFilter get(String name) {

    return TEMPLATE_FILTERS.get(name);
  }
}
