package com.intuit.appintgwkflw.wkflautomate.was.core.filters.template;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_TYPE_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.query.FilterExpression;
import org.springframework.stereotype.Component;

/**
 * This filter uses the template name field to filter a list of templates.
 *
 * <AUTHOR>
 */
@Component
public class TemplateNameFilter extends TemplateFilter {

  @Override
  boolean filterPredicate(
      final FilterExpression filterExpression, final TemplateDetails templateDetails) {

    return filterExpression.getArgs().contains(templateDetails.getTemplateName());
  }

  @Override
  public String name() {

    return FILTER_TYPE_NAME;
  }
}
