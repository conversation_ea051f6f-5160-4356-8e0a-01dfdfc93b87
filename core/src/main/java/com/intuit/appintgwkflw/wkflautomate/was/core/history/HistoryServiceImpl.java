package com.intuit.appintgwkflw.wkflautomate.was.core.history;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowHistoryResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessVariableResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.GetProcessDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * This service deals with the history data of workflows.
 */
@Service
@AllArgsConstructor
public class HistoryServiceImpl implements HistoryService {

	private final BPMNEngineHistoryServiceRest bpmnEngineHistoryServiceRest;

	@Override
	public WorkflowGenericResponse getProcessDetails(final String processInstanceId) {

		final GetProcessDetailsRequest request = new GetProcessDetailsRequest(processInstanceId);
		final WASHttpResponse<WorkflowHistoryResponse> response = bpmnEngineHistoryServiceRest
				.getProcessDetails(request);
		return WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).response(response.getResponse())
				.build();
	}

	@Override
	public WorkflowGenericResponse getProcessVariableDetails(
			ProcessVariableDetailsRequest processVariableDetailsRequest) {
		return WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
				.response(new WorkflowProcessVariableResponse(bpmnEngineHistoryServiceRest
						.getProcessVariableDetails(processVariableDetailsRequest).getResponse()))
				.build();
	}
}
