package com.intuit.appintgwkflw.wkflautomate.was.core.jira.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.JiraConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.Components;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.FieldValueMap;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.IssueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.JiraField;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.JiraRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.Project;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * <AUTHOR> This is utility class for jira service
 */
@UtilityClass
public class JiraServiceHelper {

  private final List<String> DATE_PATTERNS = List.of("MMMM dd, yyyy HH:mm:ss",
      "MMMM d, yyyy HH:mm:ss",
      "MMMM dd, yyyy", "MMMM d, yyyy");

  /**
   * This method converts the username password to basic authentication header
   *
   * @param username
   * @param password
   * @return
   */
  public String getBasicAuthenticationHeader(String username, String password) {
    String valueToEncode = username + ":" + password;
    return "Basic " + Base64.getEncoder().encodeToString(valueToEncode.getBytes());
  }

  /**
   * This method creates the request payload for jira service
   *
   * @param jiraRequest
   * @return
   */
  public String createJiraPayload(JiraRequest jiraRequest) {
    IssueType issueType = IssueType.builder().name(jiraRequest.getIssueType()).build();
    JiraField jiraField = JiraField.builder().summary(jiraRequest.getSummary())
        .description(jiraRequest.getDescription()).project(
            Project.builder().key(jiraRequest.getProject()).build())
        .issuetype(issueType)
        .components(List.of(Components.builder().name(jiraRequest.getComponent()).build()))
        .labels(jiraRequest.getLabel())
        .customfield_11801(List.of(FieldValueMap.builder().value(jiraRequest.getCustomfield_11801())
            .build()))
        .customfield_13505(
            Objects.isNull(jiraRequest.getCustomfield_13505()) ? null
                : FieldValueMap.builder().value(jiraRequest.getCustomfield_13505()).build())
        .customfield_17002(
            Objects.isNull(jiraRequest.getCustomfield_17002()) ? null
                : FieldValueMap.builder().value(jiraRequest.getCustomfield_17002()).build())
        .customfield_13504(jiraRequest.getCustomfield_13504())
        .build();
    return ObjectConverter.toJson(Map.of(JiraConstants.FIELDS, jiraField));
  }

  /**
   * Create all the the headers are required for calling jira api.
   *
   * @param userName
   * @param password
   * @return
   */
  public HttpHeaders prepareRequestHeader(String userName, String password) {
    // prepare request headers
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    // call offline ticket to renew the realm ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        getBasicAuthenticationHeader(userName, password));
    return requestHeaders;
  }

  /**
   * This method gives us the response content for summary creation in jira.
   *
   * @param text
   * @return
   */

  public String getResponseData(String text) {
    if (Objects.isNull(text)) {
      return null;
    }
    String[] responseSpilt = text.split(JiraConstants.SPLIT_MESSAGE_TEXT);
    if (responseSpilt.length == 1) {
      // could be bold text
      responseSpilt = text.split(JiraConstants.BOLD_SPLIT_MESSAGE_TEXT);
    }
    if (responseSpilt.length > 1) {
      return responseSpilt[1].replaceAll("\n", WorkflowConstants.BLANK)
          .replaceFirst(":", WorkflowConstants.BLANK).trim();
    }
    return null;
  }

  /**
   * This method converts the create date from the text to jira due date format.
   *
   * @param date
   * @return
   */
  public String getDueDate(String date) {
    if (Objects.isNull(date)) {
      return null;
    }
    String[] responseSpilt = date.split(JiraConstants.SPLIT_DATE);
    if (responseSpilt.length > 1) {
      String[] dueDate = responseSpilt[1].split("---------");
      String dueDateString = dueDate[0];
      // replace all the \n
      dueDateString = dueDateString.replaceAll("\n", WorkflowConstants.BLANK).trim();
      for (String dateFormat : DATE_PATTERNS) {
        String formattedDate = DateUtils.getDateWithFormat(dueDateString,
            DateTimeFormatter.ofPattern(dateFormat));
        if (Objects.nonNull(formattedDate)) {
          return formattedDate;
        }
      }
    }
    return null;
  }


  /**
   * Get Lables
   *
   * @param label
   * @return
   */
  public List<String> getLabels(String label) {
    if (label == null) {
      return null;
    }
    return Arrays.asList(label.split(","));
  }

  /**
   * This method add the chat link in the description
   *
   * @param description
   * @param link
   * @return
   */
  public String appendChatLinkInDescription(String description, String link) {
    return link == null ? description : (description + JiraConstants.CHAT_LINK_NAME + link);
  }
}
