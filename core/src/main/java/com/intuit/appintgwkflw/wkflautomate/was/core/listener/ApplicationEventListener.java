package com.intuit.appintgwkflw.wkflautomate.was.core.listener;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.PlaceholderUserVariableExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.PlaceholderUserVariableExtractors;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.TaskRequestHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.TaskRequestModifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.DefinitionEventProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.DefinitionEventProcessors;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.WorkflowScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import java.util.List;

import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleServiceHandlers;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.common.retry.RetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WASRetryHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.WorkerRetryStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.TemplateHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.TemplateProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateFilter;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateFilters;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.ComputedVariableEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.ComputedVariableEvaluators;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCommands;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.rxhooks.MdcPropagatingOnScheduleFunction;

import io.reactivex.plugins.RxJavaPlugins;
import lombok.AllArgsConstructor;

/** <AUTHOR> Listen for application events */
@Component
@AllArgsConstructor
public class ApplicationEventListener {

  private final List<WorkflowTaskHandler> workflowTaskHandlerList;

  private final List<RuleEvaluationHandler> ruleEvaluationHandlerList;

  private final List<TriggerHandler> triggerHandlerList;

  private final List<DefinitionCommand> definitionCommandsList;

  private final List<RetryHandler> retryHandlerList;

  private final List<DMNDataTypeTransformer> dataTypeTransformersList;

  private final List<TemplateProcessor> templateProcessHandler;

  private final List<TemplateFilter> templateFilters;

  private final List<WorkflowEventHandler> workflowEventHandlerList;

  private final List<RetryStrategy> retryStrategyList;

  private final List<TaskCompletionHandler> taskCompletionHandler;

  private final List<RecurrenceProcessor> recurrenceProcessorHandler;

  private final List<WorkflowTask> workflowTasks;

  private final List<WorkflowTaskCommand> workflowTaskCommands;

  private final List<CircuitBreakerActionHandler> circuitBreakerActionHandlerList;

  private final List<ThrottleService> throttleServiceHandlers;

  private final List<DefinitionEventProcessor> definitionEventProcessors;

  private final List<WorkflowScheduleActionProcessor> workflowScheduleActionProcessors;

  private final List<PlaceholderUserVariableExtractor> placeholderUserVariableExtractors;

  private final List<TaskRequestModifier> taskRequestModifiers;
  
  private final List<ComputedVariableEvaluator> computedVariableEvaluators;

  @EventListener
  public void handleContextRefresh(ContextRefreshedEvent event) {
    WorkflowLogger.logInfo("Refreshed Context Event received");
    /** populates factory map for workflow task handlers */
    workflowTaskHandlerList.forEach(
        handler -> WorkflowTaskHandlers.addHandler(handler.getName(), handler));

    ruleEvaluationHandlerList.forEach(
        handler -> RuleEvaluationHandlers.addHandler(handler.getName(), handler));

    triggerHandlerList.forEach(handler -> TriggerHandlers.addHandler(handler.getName(), handler));

    definitionCommandsList.forEach(
        command -> DefinitionCommands.addCommand(command.getName(), command));

    retryHandlerList.forEach(handler -> WASRetryHandler.addHandler(handler.getName(), handler));

    templateProcessHandler.forEach(
        processor -> TemplateHandler.addHandler(processor.getType(), processor));

    dataTypeTransformersList.forEach(
        transformer -> DMNDataTypeTransformers.addTransformer(transformer.getName(), transformer));

    templateFilters.forEach(TemplateFilters::add);

    workflowEventHandlerList.forEach(
        handler -> WorkflowEventHandlers.addHandler(handler.getName(), handler));

    retryStrategyList.forEach(
        strategy -> WorkerRetryStrategy.addStrategy(strategy.getName(), strategy));

    taskCompletionHandler.forEach(handler -> TaskCompletionHandlers.addHandler(handler.getName(), handler));

    recurrenceProcessorHandler.forEach(handler -> RecurrenceHandler.addHandler(handler.getName(), handler));

	workflowTasks.forEach(task -> WorkflowTasks.addWorkflowTask(task.type(), task));

	workflowTaskCommands.forEach(
			taskCommand -> WorkflowTaskCommands.addCommand(taskCommand.command(), taskCommand));

    circuitBreakerActionHandlerList.forEach(
            handler -> CircuitBreakerActionHandlers.addHandler(handler.getName(), handler));

    throttleServiceHandlers.forEach(
            handler -> ThrottleServiceHandlers.addHandler(handler.getAttribute(), handler)
    );

    definitionEventProcessors.forEach(
        handler -> DefinitionEventProcessors.addProcessor(handler.getName(), handler));
    workflowScheduleActionProcessors.forEach(
        scheduleProcessor ->
            WorkflowScheduleActionProcessorFactory.addProcessor(
                scheduleProcessor.getWorkflowName(), scheduleProcessor));

    placeholderUserVariableExtractors.forEach(
            handler -> PlaceholderUserVariableExtractors.addHandler(handler.getName(), handler)
    );

    taskRequestModifiers.forEach(
        handler -> TaskRequestHandlers.addHandler(handler.getName(), handler)
    );

    computedVariableEvaluators.forEach(
            evaluator -> ComputedVariableEvaluators.addEvaluator(evaluator.getComputedVariableType(), evaluator)
    );

    // to copy MDC to child threads in rx java context
    RxJavaPlugins.setScheduleHandler(new MdcPropagatingOnScheduleFunction());
  }
}
