package com.intuit.appintgwkflw.wkflautomate.was.core.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulingUseCase;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import lombok.experimental.UtilityClass;

import java.util.Map;

@UtilityClass
public class SchedulingUseCaseMapper {
    private static final Map<WorkflowNameEnum, SchedulingUseCase> workflowNameUseCaseMap = Map.of(
            WorkflowNameEnum.CUSTOM_REMINDER, SchedulingUseCase.WORKFLOW_REMINDER_USECASE,
            WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS, SchedulingUseCase.WORKFLOW_SCHEDULING_USECASE
    );

    public static String getActionsByType(String workflowName) {
        return workflowNameUseCaseMap.get(WorkflowNameEnum.fromName(workflowName)).getName();
    }
}
