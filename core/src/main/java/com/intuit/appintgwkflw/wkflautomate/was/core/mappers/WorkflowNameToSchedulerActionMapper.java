package com.intuit.appintgwkflw.wkflautomate.was.core.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;

/** <AUTHOR> */
public class WorkflowNameToSchedulerActionMapper {
    private static final Map<WorkflowNameEnum, List<SchedulerAction>> actionTypeMap = Map.of(
            WorkflowNameEnum.CUSTOM_REMINDER, new ArrayList<>(EnumSet.of(
                    SchedulerAction.CUSTOM_REMINDER_CUSTOM_START,
                    SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT
            )),
            WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS, new ArrayList<>(EnumSet.of(
                    SchedulerAction.CUSTOM_SCHEDULEDACTIONS_CUSTOM_START
            )),
            WorkflowNameEnum.CUSTOM_SEND_ENTITY, new ArrayList<>(EnumSet.of(
                    SchedulerAction.CUSTOM_SEND_ENTITY_CUSTOM_START
            )),
            WorkflowNameEnum.CUSTOM_UPDATE_ENTITY, new ArrayList<>(EnumSet.of(
                    SchedulerAction.CUSTOM_UPDATE_ENTITY_CUSTOM_START
            ))
    );

    /**
     * Returns a list of SchedulerAction enums that match the given type.
     *
     * @param type The type to match.
     * @return List of SchedulerAction enums.
     */
    public static List<SchedulerAction> getActionsByType(String type) {
        return actionTypeMap.getOrDefault(WorkflowNameEnum.fromName(type), new ArrayList<>());
    }
}
