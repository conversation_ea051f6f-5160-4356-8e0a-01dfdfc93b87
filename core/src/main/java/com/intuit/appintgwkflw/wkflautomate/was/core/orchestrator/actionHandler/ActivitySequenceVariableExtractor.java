package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_TIMER_DURATION_DAYS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_TIMER_DURATION_DAYS_PREFIX;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * This class used to extract the sequence variables of all the child tasks of a particular
 * activity
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class ActivitySequenceVariableExtractor implements Task {


  private final ProcessDetailsRepoService processDetailsRepoService;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final MultiStepConfig multiStepConfig;

  @Override
  public State execute(State state) {
    DefinitionDetails definitionDetails = processDetailsRepoService.
        findByProcessIdWithoutDefinitionData(
            state.getValue(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID))
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

    DefinitionActivityDetail parentTaskDetails = definitionActivityDetailsRepository.
        findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            state.getValue(WorkflowConstants.ACTIVITY_ID),
            definitionDetails.getDefinitionId())
        .orElseThrow(() -> new WorkflowGeneralException(
            WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND));

    List<DefinitionActivityDetail> childTaskDetails = parentTaskDetails.getChildActivityDetails();

    WorkflowVerfiy.verify(childTaskDetails.isEmpty(),
        WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND);

    Map<String, Object> sequenceMap = childTaskDetails.stream().
        filter(childTaskDetail -> Objects.nonNull(childTaskDetail.getActivityId())).
        collect(Collectors.toMap(
            childTaskDetail -> childTaskDetail.getActivityId(),
            childTaskDetail -> extractSequenceVariablesFromChildTask(childTaskDetail)));

    addRuntimeProcessVariablesForCallActivity(sequenceMap, parentTaskDetails, definitionDetails,
        state.getValue(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID),
        state.getValue(WorkflowConstants.ACTIVITY_ID));

    state.addValue(WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP, sequenceMap);
    return state;
  }

  private Object extractSequenceVariablesFromChildTask(DefinitionActivityDetail childTaskDetail) {
    String attributes = childTaskDetail.getUserAttributes();
    WorkflowVerfiy.verifyNull(attributes, WorkflowError.DEFINITION_ACTIVITY_ATTRIBUTE_MISSING);
    JSONObject attributePlaceholder = new JSONObject(attributes);

    WorkflowVerfiy.verify(!attributePlaceholder.has(WorkflowConstants.SELECTED),
        WorkflowError.INVALID_PARAMETER_DETAILS);
    String selected = String.valueOf(attributePlaceholder.get(WorkflowConstants.SELECTED));
    WorkflowVerfiy.verify(!CustomWorkflowUtil.isBoolean(selected),
        WorkflowError.INVALID_PARAMETER_DETAILS);
    return selected;
  }

  /**
   * This method adds runtime process variables for multi condition workflows
   *
   * @param sequenceMap
   * @param parentTaskDetails
   * @param definitionDetails
   */
  private void addRuntimeProcessVariablesForCallActivity(
      Map<String, Object> sequenceMap,
      DefinitionActivityDetail parentTaskDetails,
      DefinitionDetails definitionDetails,
      String rootProcessInstanceId,
      String rootActivityId){

    // get variables for Recurring tasks
    Map<String, Object> runtimeVariableSequenceMap = extractVariablesForRecurringTasks(
        parentTaskDetails, rootProcessInstanceId, rootActivityId);

    // if runtimeVariableSequenceMap is empty -> set default values for non-recurring processes
    if (MapUtils.isEmpty(runtimeVariableSequenceMap)) {
      String processExpiryTimeDuration =
          getProcessExpiryTimeDurationForMultiConditionWorkflow(definitionDetails,
              rootProcessInstanceId, rootActivityId);

      if (StringUtils.isEmpty(processExpiryTimeDuration)) {
        return;
      }
      runtimeVariableSequenceMap = new HashMap<>();
      runtimeVariableSequenceMap.put(
          WorkflowConstants.PROCESS_EXPIRY_TIME_DURATION, processExpiryTimeDuration);
    }

    sequenceMap.putAll(runtimeVariableSequenceMap);
  }

  /**
   * This method extracts the maxScheduleCount from parameters of the call activity required for
   * multi condition recurring reminder workflows. Sample parameter json object stored in the
   * userAttributes of the call activity details object ->
   * {"parameters":{"isRecurring":{"fieldValue":["true"]},"recurFrequency":{"fieldValue":["2"]}}}
   *
   * @param taskDetails DefinitionActivityDetail
   * @return Map<String, Object>
   */
  private Map<String, Object> extractVariablesForRecurringTasks(
      DefinitionActivityDetail taskDetails, String rootProcessInstanceId, String rootActivityId) {
    try {
      String attributes = taskDetails.getUserAttributes();
      if (Objects.isNull(attributes)) {
        return null;
      }

      JSONObject attributePlaceholder = new JSONObject(attributes);
      Object taskParameters = attributePlaceholder.get(WorkflowConstants.PARAMETERS);
      if (Objects.isNull(taskParameters)) {
        return null;
      }

      JSONObject taskParametersJson = new JSONObject(String.valueOf(taskParameters));

      if (!taskParametersJson.toMap().containsKey(WorkflowConstants.IS_RECURRING_ENABLED) ||
          !taskParametersJson.toMap().containsKey(WorkflowConstants.RECUR_FREQUENCY) ||
          !taskParametersJson.toMap().containsKey(WorkflowConstants.MAX_SCHEDULE_COUNT)) {
        return null;
      }

      Object isRecurringObject = taskParametersJson.get(WorkflowConstants.IS_RECURRING_ENABLED);
      Object maxScheduleCountObject = taskParametersJson.get(WorkflowConstants.MAX_SCHEDULE_COUNT);
      Object recurFrequencyObject = taskParametersJson.get(WorkflowConstants.RECUR_FREQUENCY);

      if (Objects.isNull(isRecurringObject) || Objects.isNull(maxScheduleCountObject) ||
          Objects.isNull(recurFrequencyObject)) {
        return null;
      }

      // return null if isRecurring value is false
      JSONObject isRecurringParameter = new JSONObject(String.valueOf(isRecurringObject));
      JSONArray isRecurringFieldValueList = (JSONArray) isRecurringParameter.get(
          WorkflowConstants.FIELD_VALUE);
      if (Objects.isNull(isRecurringFieldValueList) || isRecurringFieldValueList.isEmpty() ||
          !Boolean.parseBoolean(String.valueOf(isRecurringFieldValueList.get(0)))) {
        return null;
      }

      // return null if maxScheduleCount is not present
      JSONObject maxScheduleCountParameter = new JSONObject(String.valueOf(maxScheduleCountObject));
      JSONArray maxScheduleCountFieldValueList = (JSONArray) maxScheduleCountParameter.get(
          WorkflowConstants.FIELD_VALUE);
      if (Objects.isNull(maxScheduleCountFieldValueList)
          || maxScheduleCountFieldValueList.isEmpty()) {
        return null;
      }

      // return null if recurFrequency is not present
      JSONObject recurFrequencyParameter = new JSONObject(String.valueOf(recurFrequencyObject));
      JSONArray recurFrequencyFieldValueList = (JSONArray) recurFrequencyParameter.get(
          WorkflowConstants.FIELD_VALUE);
      if (Objects.isNull(recurFrequencyFieldValueList) || recurFrequencyFieldValueList.isEmpty()) {
        return null;
      }

      Map<String, Object> recurringTaskSequenceMap = new HashMap<>();
      recurringTaskSequenceMap.put(WorkflowConstants.MAX_SCHEDULE_COUNT,
          Integer.valueOf(maxScheduleCountFieldValueList.get(0).toString()));
      recurringTaskSequenceMap.put(WorkflowConstants.PROCESS_EXPIRY_TIME_DURATION,
          calculateProcessExpiryTimeDuration(maxScheduleCountFieldValueList.get(0),
              recurFrequencyFieldValueList.get(0)));
      return recurringTaskSequenceMap;
    } catch (Exception e) {
      WorkflowLogger.logError(
          "Error=Error occurred while extracting variables for recurring tasks, step=ActivitySequenceVariableExtractor, rootActivityId=%s, rootProcessInstanceId=%s, Exception=%s",
          rootActivityId, rootProcessInstanceId, e.getStackTrace());
      return null;
    }
  }

  /**
   * This method calculates the process expiry time duration expression based on the
   * maxScheduleCount and recurFrequency
   *
   * @param maxScheduleCount
   * @param recurFrequency
   * @return
   */
  private String calculateProcessExpiryTimeDuration(Object maxScheduleCount,
      Object recurFrequency) {
    return PROCESS_TIMER_DURATION_DAYS_PREFIX + (Integer.parseInt(String.valueOf(maxScheduleCount))
        *
        Integer.parseInt(String.valueOf(recurFrequency))) + PROCESS_TIMER_DURATION_DAYS;
  }

  /**
   * This method gets the workflow level process expiry time duration for the workflow from config
   *
   * @param definitionDetails
   * @return
   */
  private String getProcessExpiryTimeDurationForMultiConditionWorkflow(
      DefinitionDetails definitionDetails, String rootProcessInstanceId, String rootActivityId) {
    String templateName = definitionDetails.getTemplateDetails().getTemplateName();

    //checking if the latest template for the definition's template is of multi condition type
    if (Objects.nonNull(multiStepConfig.getWorkflowTemplates()) &&
        Objects.nonNull(multiStepConfig.getWorkflowTemplates().get(templateName)) &&
        Objects.nonNull(multiStepConfig.getWorkflowTemplates().get(templateName)
            .getProcessExpiryTimeDuration())) {
      WorkflowLogger.logInfo(
          "message=No recurring tasks found, setting default values. step=ActivitySequenceVariableExtractor, rootActivityId=%s, rootProcessInstanceId=%s",
          rootActivityId, rootProcessInstanceId);
      return multiStepConfig.getWorkflowTemplates().get(templateName)
          .getProcessExpiryTimeDuration();
    }
    return null;
  }
}