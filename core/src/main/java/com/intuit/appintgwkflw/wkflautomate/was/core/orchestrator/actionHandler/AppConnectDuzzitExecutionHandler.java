package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class is implementation for executing an external task of BPMN to execute appConnect duzzit
 * using realm scoped offline ticket
 * using the endpoint of appConnect which does not require WAS definitions to be uploaded to appConnect.
 * This handler will serve the purpose of making appConnect duzzit calls from system templates because
 * system templates are not uploaded to AppConnect.
 * The duzzit to be executed is picked up dynamically from the handlerDetails of the bpmn
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AppConnectDuzzitExecutionHandler extends WorkflowTaskHandler {

  private final AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.EXECUTE_APPCONNECT_DUZZIT_HANDLER;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());

    WorkflowVerfiy.verify(
            !parameterDetailsMap.isPresent() || CollectionUtils.isEmpty(parameterDetailsMap.get()),
            INVALID_PARAMETER_DETAILS);

    return appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(
        workerActionRequest, parameterDetailsMap
    );
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
  }
}
