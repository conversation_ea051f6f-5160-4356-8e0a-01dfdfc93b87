package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.API_SEGMENT;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BACKSLASH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.JSON_EXTENSION;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter.AppConnectOINPBridge;
import com.intuit.v4.workflows.Template;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Handler to call the Appconnect Duzzits via the rest APIs
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AppConnectDuzzitRestExecutionHandler extends WorkflowTaskHandler {

  private final AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;
  private final WorkerUtil workerUtil;

  private final ProcessDetailsRepoService processDetailsRepoService;
  private final AppConnectWorkflowTaskHandlerHelper taskHandlerHelper;

  private final AppConnectParameterDetailsExtractorHelper appConnectParameterDetailsExtractorHelper;
  private final AppConnectOINPBridge appConnectOINPBridge;


  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.APP_CONNECT_DUZZIT_REST_ACTION_HANDLER;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    DefinitionDetails rootDefinitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            workerActionRequest.fetchParentProcessInstanceId()
        ).orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND)
        );

    String templateName = rootDefinitionDetails.getTemplateDetails().getTemplateName();

    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            new Template().name(templateName),
            rootDefinitionDetails,
            workerActionRequest.getInputVariables().get(WorkflowConstants.HANDLER_DETAILS),
            workerActionRequest
        );

    WorkflowLogger.logInfo(
        "step=preParameterDetailExtraction; templateName=%s, handlerId=%s, isCalledProcess=%s",
        templateName,
        workerActionRequest.getHandlerId(),
        workerActionRequest.isCalledProcess()
    );

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
              appConnectParameterDetailExtractor.getParameterDetails(workerActionRequest);


    WorkflowVerfiy.verify(
        parameterDetailsMap.isEmpty() || CollectionUtils.isEmpty(parameterDetailsMap.get()),
        INVALID_PARAMETER_DETAILS);

    workerActionRequest = workerUtil.validate(workerActionRequest);

    AppConnectTaskHandlerRequest actionRequest =
        taskHandlerHelper.prepareTaskHandlerRequest(
            workerActionRequest, parameterDetailsMap, rootDefinitionDetails);

    Map<String, String> bridgeInputMap = Optional.ofNullable(actionRequest.getAction())
        .map(action -> Optional.ofNullable(action.getInputs())
            .map(inputs -> inputs.stream().collect(
                Collectors.toMap(WorkflowTaskHandlerInput::getName,
                    WorkflowTaskHandlerInput::getValue)))
            .orElse(Collections.emptyMap()))
        .orElse(Collections.emptyMap());

    if (appConnectOINPBridge.initiateBridge(workerActionRequest, bridgeInputMap)) {
      try {
        WorkflowLogger.logInfo(
            "Initiating OINP Bridge processId=%s externalTaskId=%s handlerId=%s",
            workerActionRequest.getProcessInstanceId(), workerActionRequest.getTaskId(),
            workerActionRequest.getHandlerId());
        return appConnectOINPBridge.executeNotificationAction(workerActionRequest, bridgeInputMap);
      } catch (Exception e) {
        WorkflowLogger.logError(e,
            "OINP Bridge Failed processId=%s externalTaskId=%s handlerId=%s",
            workerActionRequest.getProcessInstanceId(), workerActionRequest.getTaskId(),
            workerActionRequest.getHandlerId());
        throw e;
      }
    }
    List<WorkflowTaskHandlerInput> additionalTaskHandlerInputs = new ArrayList<>();
    Optional<RecordType> recordTypeOptional = extractRecordType(rootDefinitionDetails, workerActionRequest);
    if (recordTypeOptional.isPresent()) {
      additionalTaskHandlerInputs = getTaskParametersFromConfig(recordTypeOptional.get(), workerActionRequest);
    }

    validateHandlerId(workerActionRequest);

    return appconnectDuzzitRestExecutionHelper.executeWorkflowActionRequest(workerActionRequest, parameterDetailsMap, additionalTaskHandlerInputs);
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    if(workerActionRequest.isCalledProcess()) {
      metricLogger.logErrorMetric(MetricName.MULTI_STEP_INVOKE_DUZZIT,
          Type.APPLICATION_METRIC, exception);
    }
    else {
      metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT,
          Type.APP_CONNECT_METRIC, exception);
    }

  }

  // override handlerId if not found in the correct format
  private void validateHandlerId(WorkerActionRequest workerActionRequest) {
    if (Objects.nonNull(workerActionRequest.getHandlerId())
        && !workerActionRequest.getHandlerId().contains(JSON_EXTENSION)
        && workerActionRequest.getHandlerId().split(BACKSLASH).length == 2) {
      StringBuilder restHandlerId = new StringBuilder();
      restHandlerId
          .append(BACKSLASH)
          .append(workerActionRequest.getHandlerId().split(BACKSLASH)[0])
          .append(BACKSLASH)
          .append(API_SEGMENT)
          .append(BACKSLASH)
          .append(workerActionRequest.getHandlerId().split(BACKSLASH)[1])
          .append(JSON_EXTENSION);

      WorkflowLogger.logInfo("HandlerId is not in correct format, processInstanceId=%s, taskId=%s, oldHandlerId=%s, newHandlerId=%s",
          workerActionRequest.getProcessInstanceId(),
          workerActionRequest.getTaskId(),
          workerActionRequest.getHandlerId(),
          restHandlerId.toString());
      workerActionRequest.setHandlerId(restHandlerId.toString());
    }
  }
}
