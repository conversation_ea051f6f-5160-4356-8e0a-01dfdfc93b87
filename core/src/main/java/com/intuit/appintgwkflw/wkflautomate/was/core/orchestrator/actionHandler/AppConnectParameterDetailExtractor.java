package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Optional;

/**
 * As part of single definition, parameter details are filled using the user placeholder values.
 * Returns the parameter-detail map extracted using corresponding parameter-detail extractor.
 *
 * @param  workerActionRequest
 * @return ParameterDetailsMap
 */
public interface AppConnectParameterDetailExtractor {
  Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest);
}
