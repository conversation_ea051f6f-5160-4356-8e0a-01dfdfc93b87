package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * This class is implementation for executing an external task of bpmn to delete the AppConnect workflow for the
 * provided workflowIds in the definition of the process which executes this external Task
 * It leverage deleteAppconnect method of AppconnectServiceImpl which needs offlineTicket to make the call succesfully
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AppConnectWorkflowDeleteHandler extends WorkflowTaskHandler {
  private final WorkerUtil workerUtil;
  private AuthDetailsService authDetailsService;
  private AppConnectService appConnectService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_APP_CONNECT_WORKFLOW_DELETE_HANDLER;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    /* Throw exception if definition is marked for disable/delete/error */
    workerActionRequest = workerUtil.validate(workerActionRequest);

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    ParameterDetails parameterDetails =
        parameterDetailsMap
            .orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.INVALID_PARAMETER_DETAILS))
            .get(WorkflowConstants.WORKFLOW_ID);
    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(parameterDetails), WorkflowError.INVALID_PARAMETER_DETAILS);
    final List<String> workflowIds = parameterDetails.getFieldValue();
    // Iterate over workflowId values passed in workflowId key of parameterDetails and fire a call to delete that AppConnect WorkflowId
    final String realmId = String.valueOf(workerActionRequest.getOwnerId());
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);
    appConnectService.deleteWorkflows(workflowIds, authDetails);
    return ImmutableMap.of(WorkFlowVariables.RESPONSE.getName(), true);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.DELETE_APPCONNECT_WORKFLOW, Type.APP_CONNECT_METRIC, exception);
  }
}
