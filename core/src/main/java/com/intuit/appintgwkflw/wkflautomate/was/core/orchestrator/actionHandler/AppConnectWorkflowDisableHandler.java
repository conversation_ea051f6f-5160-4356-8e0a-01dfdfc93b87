package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class is implementation for executing an external task of bpmn to disable the AppConnect workflow for the
 * provided workflowIds in the definition of the process which executes this external Task
 * It leverage activateActionWorkflow method of AppconnectServiceImpl with activate value as false and also needs offlineTicket to make the call succesfully
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AppConnectWorkflowDisableHandler extends WorkflowTaskHandler {
  private final WorkerUtil workerUtil;
  private AuthDetailsService authDetailsService;
  private AppConnectService appConnectService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_APP_CONNECT_WORKFLOW_DISABLE_HANDLER;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    /* Throw exception if definition is marked for disable/delete/error */
    workerActionRequest = workerUtil.validate(workerActionRequest);

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());

    WorkflowVerfiy.verify(
            !parameterDetailsMap.isPresent() || CollectionUtils.isEmpty(parameterDetailsMap.get()) || !parameterDetailsMap.get().containsKey(WorkflowConstants.WORKFLOW_ID),
            INVALID_PARAMETER_DETAILS);

    // Iterate over workflowId values passed in workflowId key of parameterDetails and fire a call to disable that AppConnect WorkflowId
    final String realmId = String.valueOf(workerActionRequest.getOwnerId());
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);
    parameterDetailsMap
        .orElseThrow(() -> new WorkflowGeneralException(INVALID_PARAMETER_DETAILS))
        .get(WorkflowConstants.WORKFLOW_ID)
        .getFieldValue()
        .stream()
        .forEach(
            workflowId -> {
              try {
                WorkflowVerfiy.verify(
                    StringUtils.isEmpty(workflowId), WorkflowError.INVALID_WORKFLOW_ID_INPUT);

                appConnectService.disableAppConnectWorkflow(workflowId, authDetails);

                WorkflowLogger.info(
                    () ->
                        WorkflowLoggerRequest.builder()
                            .message(
                                "Workflow disabled from app-connect by workflowId=%s", workflowId)
                            .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                            .downstreamServiceName(
                                DownstreamServiceName.DISABLE_WORKFLOW_APPCONNECT)
                            .className(this.getClass().getName()));
              } catch (WorkflowGeneralException workflowGeneralException) {
                // if workflow is already inactive don't throw the exception.Can be because of retry
                // we are
                // trying again to deactivate the definition
                if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
                    && workflowGeneralException
                        .getMessage()
                        .contains(WorkflowConstants.WORKFLOW_ALREADY_INACTIVE)) {
                  WorkflowLogger.warn(
                      () ->
                          WorkflowLoggerRequest.builder()
                              .className(this.getClass().getName())
                              .message(
                                  "Workflow already inactive for given workflowId=%s", workflowId)
                              .downstreamComponentName(DownstreamComponentName.WAS)
                              .downstreamServiceName(
                                  DownstreamServiceName.DISABLE_WORKFLOW_APPCONNECT));
                } else {
                  throw workflowGeneralException;
                }
              }
            });

    return ImmutableMap.of(WorkFlowVariables.RESPONSE.getName(), true);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.DISABLE_WORKFLOW_DEFINITION, Type.APP_CONNECT_METRIC, exception);
  }
}
