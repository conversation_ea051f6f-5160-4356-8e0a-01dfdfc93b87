package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_ACCESS_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_HANDLER_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType.COMPUTED_VARIABLE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType.PROCESS_VARIABLE;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.AppConnectDuzzitException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.AppConnectDuzzits;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class AppConnectWorkflowTaskHandlerHelper {

  private final AppConnectConfig appConnectConfig;

  private final RestAction appconnectWorkflowHeaderAction;

  private final FieldValueFilterStrategy fieldValueFilterStrategy;

  /**
   * prepare appconnect workflow task handler request
   *
   * @param workerActionRequest input workerActionRequest
   * @param parameterDetailsMap input parameterDetailsMap from task
   * @return AppConnectTaskHandlerRequest
   */
  public AppConnectTaskHandlerRequest prepareTaskHandlerRequest(
          final WorkerActionRequest workerActionRequest,
          final Optional<Map<String, ParameterDetails>> parameterDetailsMap,
          final DefinitionDetails definitionDetails) {

    return AppConnectTaskHandlerRequest.builder()
        .endpoint(appConnectConfig.getTaskHandler())
        .realmId(String.valueOf(workerActionRequest.getOwnerId()))
        .instanceId(workerActionRequest.getProcessInstanceId())
        .providerAppId(appConnectConfig.getProviderAppId())
        .providerWorkflowId(workerActionRequest.getWorkflowId())
        .workflowId(workerActionRequest.getProcessDefinitionId())
        .action(prepareTaskHandlerActions(workerActionRequest, parameterDetailsMap, definitionDetails))
        .externalTaskId(workerActionRequest.getTaskId())
        .build();
  }

  @Trace
  @Metric(name = MetricName.INVOKE_DUZZIT, type = Type.APP_CONNECT_METRIC, metricType = MetricType.LATENCY)
  @Retry(name = ResiliencyConstants.APP_CONNECT_WORKFLOW_TASK_HANDLER)
  public WASHttpResponse<WorkflowTaskHandlerResponse> executeAction(
      final WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest,
      @Tracer(key = WASContextEnums.HANDLER_ID) final String handlerId) {

    // execute app connect workflow realm based action
    WASHttpResponse<WorkflowTaskHandlerResponse> workflowTaskHandlerResponseWASHttpResponse =
        appconnectWorkflowHeaderAction.execute(wasHttpRequest);

    /* When app connect returns GatewayTimeout(504) response then WAS will do an immediate retry.In some cases post retry from WAS app-connect returns response as (202) Request in Progress
    Currently it was logging failure and an alert was generated.With this it will retry 3 times on 202 and if not succeeded will generate an alert. */
    if (HttpStatus.ACCEPTED.value() == workflowTaskHandlerResponseWASHttpResponse.statusCode()) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("The request has been accepted for processing.")
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.APP_CONNECT_WORKFLOW_TASK_HANDLER)
                  .className(this.getClass().getSimpleName()));
      WorkflowError workflowError = WorkflowError.REQUEST_IN_PROGRESS;
      throw new WorkflowRetriableException(workflowError, workflowError.getErrorMessage());
    }

    return workflowTaskHandlerResponseWASHttpResponse;
  }

  /**
   * It sets the response fields which are present in responseFields of the template task
   *
   * @param workerActionRequest {@link WorkerActionRequest}
   * @param response            response to be set in camunda process
   * @return response
   */
  public Map<String, Object> prepareTaskHandlerResponse(
      final WorkerActionRequest workerActionRequest,
      final WASHttpResponse<WorkflowTaskHandlerResponse> response) {

    // Handler details are passed when worker executor calls the respective handler.
    // We use the object to get the response fields for the handler
    final HandlerDetails handlerDetails =
       workerActionRequest.getHandlerDetails();

    // responseFields would be set in taskDetails key in template
    final List<String> responseFields =
        handlerDetails.getResponseFields();

    WorkflowLogger.info(
            () ->
                    WorkflowLoggerRequest.builder()
                            .message(
                                    "External task execution success=%s, taskId=%s, activityId=%s",
                                    response.getResponse().getSuccess(),
                                    workerActionRequest.getTaskId(),
                                    workerActionRequest.getActivityId()
                            )
                            .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                            .className(this.getClass().getSimpleName())
    );
    
    final Map<String, Object> responseMap = new HashMap<>();
    responseMap.put(new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
        .append(RESPONSE.getName())
        .toString(),
        response.getResponse().getSuccess());

    final Map<String, Object> data = response.getResponse().getData();

    // check the fields in response and set it
    if (Objects.nonNull(responseFields) && Objects.nonNull(data)) {
      responseFields.forEach(
          responseField -> {
            if (data.containsKey(responseField)) {
              responseMap.put(responseField, data.get(responseField));
            }
          });
    }
    return responseMap;
  }

  private String getAppconnectTaskId(
      WorkerActionRequest workerActionRequest, DefinitionDetails definitionDetails) {

    if (Objects.nonNull(definitionDetails.getPlaceholderValue())) {
      // In case of single definition, activity id won't contain uuid and realmid.
      // Appconnect on the other hand would contain the definition with activity id of the form
      // activityId_realmId_uuid
      // To trigger the right activity in appconnect, we would need to form the correct activity id.
      String ownerId = workerActionRequest.getOwnerId().toString();
      Integer actionIdIndex = workerActionRequest.getActivityId().indexOf(ownerId);
      String activityId = workerActionRequest.getActivityId();
      String actionId =
          actionIdIndex == -1 ? activityId : activityId.substring(0, actionIdIndex - 1);
      String definitionKey =
          workerActionRequest.getInputVariables().get(WorkflowConstants.DEFINITION_KEY);
      return actionId.concat(definitionKey.substring(definitionKey.indexOf(ownerId) - 1));
    }
    return workerActionRequest.getActivityId();
  }

  /**
   * prepare WorkflowTaskHandlerAction for external task
   *
   * @param workerActionRequest input workerActionRequest
   * @param parameterDetailsMap input parameterDetailsMap from task
   * @return WorkflowTaskHandlerAction
   */
  private WorkflowTaskHandlerAction prepareTaskHandlerActions(
      final WorkerActionRequest workerActionRequest,
      final Optional<Map<String, ParameterDetails>> parameterDetailsMap,
      final DefinitionDetails definitionDetails) {

    String taskId = getAppconnectTaskId(workerActionRequest, definitionDetails);

    WorkflowLogger.logInfo(
        "TaskId generated for activityId=%s, ownerId=%s, definitionKey=%s => taskId=%s ",
        workerActionRequest.getActivityId(),
        workerActionRequest.getOwnerId().toString(),
        workerActionRequest.getInputVariables().get(WorkflowConstants.DEFINITION_KEY),
        taskId);

    return WorkflowTaskHandlerAction.builder()
        .bpmnTaskId(taskId)
        .providerTaskId(workerActionRequest.getHandlerId())
        .inputs(prepareTaskHandlerInputs(workerActionRequest, parameterDetailsMap))
        .build();
  }

  /**
   * prepare list of task handler inputs
   *
   * @param workerActionRequest input worker request
   * @param parameterDetailsMap input parameterDetailsMap from task
   * @return list of WorkflowTaskHandlerInput
   */
  public List<WorkflowTaskHandlerInput> prepareTaskHandlerInputs(
      final WorkerActionRequest workerActionRequest,
      final Optional<Map<String, ParameterDetails>> parameterDetailsMap) {

    return parameterDetailsMap
        .orElseThrow(()->new WorkflowGeneralException(WorkflowError.INVALID_PARAMETER_DETAILS))
        .entrySet()
        .stream()
        .map(
            parameterDetailsEntry -> {
              final WorkflowTaskHandlerInput taskHandlerInput = new WorkflowTaskHandlerInput();
              taskHandlerInput.setName(getHandlerFieldName(parameterDetailsEntry));
              final String paramValue = fetchParamValue(workerActionRequest, parameterDetailsEntry);
              // if parameter value is empty returning null and skipping to add in handler input
              if (isEmpty(paramValue)) {
                return null;
              }

              taskHandlerInput.setValue(paramValue);
              return taskHandlerInput;
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  /**
   * fetch parameter value from process variable if value type is Process variable and value in not
   * present
   *
   * @param workerActionRequest   input worker request
   * @param parameterDetailsEntry input parameterDetailsMap Entry from task
   * @return
   */
  private String fetchParamValue(
      final WorkerActionRequest workerActionRequest,
      final Entry<String, ParameterDetails> parameterDetailsEntry) {

    if (PROCESS_VARIABLE == parameterDetailsEntry.getValue().getValueType()) {
      return fetchValueFromProcessVariables(workerActionRequest, parameterDetailsEntry);
    } else if (COMPUTED_VARIABLE == parameterDetailsEntry.getValue().getValueType()) {
      return ComputedVariableEvaluators.getEvaluator(
              parameterDetailsEntry.getValue().getComputedVariableType())
          .evaluateValue(workerActionRequest, parameterDetailsEntry);
    } else if (!CollectionUtils.isEmpty(parameterDetailsEntry.getValue().getFieldValue())) {
      return fieldValueFilterStrategy.fetchFieldValueFromParameterDetail(
          workerActionRequest,
          parameterDetailsEntry
      );
    }
    return null;
  }

  /**
   * fetches value from process variable
   *
   * @param workerActionRequest   input worker request
   * @param parameterDetailsEntry input parameterDetailsMap Entry from task
   * @return
   */
  private String fetchValueFromProcessVariables(
      final WorkerActionRequest workerActionRequest,
      final Entry<String, ParameterDetails> parameterDetailsEntry) {

    if (!CollectionUtils.isEmpty(workerActionRequest.getInputVariables())
        && isNotEmpty(
        workerActionRequest.getInputVariables().get(parameterDetailsEntry.getKey()))) {
      return workerActionRequest.getInputVariables().get(parameterDetailsEntry.getKey());
    }

    return null;
  }

  /**
   * uses key as the handler field name if handler-field-name is not present in parameter details
   *
   * @param parameterDetailsEntry input handler entry details
   * @return the handler field name
   */
  private String getHandlerFieldName(final Entry<String, ParameterDetails> parameterDetailsEntry) {

    String fieldName = parameterDetailsEntry.getKey();
    if (Optional.ofNullable(parameterDetailsEntry)
        .map(parameterDetailsValue -> parameterDetailsValue.getValue())
        .map(ParameterDetails::getHandlerFieldName)
        .isPresent()) {
      fieldName = parameterDetailsEntry.getValue().getHandlerFieldName();
    }
    return fieldName;
  }

  /**
   *
   * @param handlerId used to fetch the dependency value from AppConnectDuzzits
   * @param response carries the error response
   * @return AppConnectDuzzitException
   */
  public AppConnectDuzzitException getAppConnectDuzzitException(
          String handlerId, WASHttpResponse<WorkflowTaskHandlerResponse> response) {
    handlerId = StringUtils.defaultIfBlank(handlerId, WorkflowConstants.BLANK);
    String dependency =
            StringUtils.defaultIfBlank(AppConnectDuzzits.getDuzzitDependencyMap().get(handlerId), WorkflowConstants.BLANK);
    String responseError = StringUtils.defaultIfBlank(response.getError(), WorkflowConstants.BLANK);
    if (responseError.contains(WorkflowConstants.WAS_ACCESS_ERROR_CODE)) {
      return new AppConnectDuzzitException(
              EXTERNAL_TASK_ACCESS_FAILURE.name(),
              String.format(
                      EXTERNAL_TASK_ACCESS_FAILURE.getErrorMessage(), dependency, handlerId, responseError));
    }
    return new AppConnectDuzzitException(
            String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorCode(),
                    dependency.toUpperCase(),
                    formatHandlerId(handlerId)),
            String.format(
                    EXTERNAL_TASK_HANDLER_ERROR.getErrorMessage(), dependency, handlerId, responseError));
  }

  /**
   * @param handlerId which used to format as per Workflow Error Code
   * @return
   */
  public String formatHandlerId(String handlerId) {
    int splitCount = handlerId.split(WorkflowConstants.BACKSLASH).length - 1;
    if (Integer.parseInt(WorkflowConstants.ZERO) != splitCount) {
      return handlerId
          .split(WorkflowConstants.BACKSLASH)[splitCount]
          .replace(WorkflowConstants.HYPHEN, WorkflowConstants.UNDERSCORE)
          .toUpperCase();
    }
    return WorkflowError.EXTERNAL_TASK_HANDLER_ERROR.name();
  }
}
