package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COLON;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IDEMPOTENCY_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p>create the realm based offline ticket and set the Authorization header before executing
 * call to app connect for external task handler api.
 */
@Component
@AllArgsConstructor
public class AppconnectWorkflowHeaderAction implements RestAction {

  private final AppConnectWASClient wasHttpClient;

  private final AuthDetailsService authDetailsService;

  @Override
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> execute(
      WASHttpRequest<REQUEST, RESPONSE> httpRequest) {

    final AppConnectTaskHandlerRequest actionRequest =
        (AppConnectTaskHandlerRequest) httpRequest.getRequest();

    final HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    requestHeaders.add(IDEMPOTENCY_KEY,
        new StringBuilder(actionRequest.getExternalTaskId())
            .append(COLON)
            .append(actionRequest.getInstanceId())
            .toString());

    // populate retry of response in case of error at appconnect
    requestHeaders.add(WorkflowConstants.RETRY_ON_ERROR, Boolean.TRUE.toString());

    // call offline ticket to renew the realm ticket
    requestHeaders.set(WorkflowConstants.AUTHORIZATION_HEADER,
        authDetailsService.renewOfflineTicketAndUpdateDB(actionRequest.getInstanceId()));

    if (null != httpRequest.getRequestHeaders()) {
      httpRequest
          .getRequestHeaders()
          .toSingleValueMap()
          .forEach(requestHeaders::set);
    }

    httpRequest = httpRequest.toBuilder().requestHeaders(requestHeaders).build();

    return wasHttpClient.httpResponse(httpRequest);
  }
}
