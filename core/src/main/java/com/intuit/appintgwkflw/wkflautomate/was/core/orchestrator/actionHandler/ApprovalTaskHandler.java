package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_WORKFLOW_REQUEST_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ON_DEMAND_APPROVAL_REQUEST_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.THIRD_PARTY_TXN_REQUEST_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector.ConnectorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.approverDetails.ApprovalRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.approverDetails.ApproverDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;


/**
 * Handler for all approval related calls
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ApprovalTaskHandler extends WorkflowTaskHandler implements ConnectorTaskHandler {

  public static final String REQUEST_TYPE = "requestType";
  private final AppConfig appConfig;
  private final ConnectorImpl connector;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final ProcessDetailsRepoService processDetailsRepoService;
  private final OnDemandHelper onDemandHelper;

  /**
   * name by which implementation will be recognized
   *
   * @return bean Name.
   */
  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_APPROVAL_HANDLER;
  }

  /**
   * input process variables.
   *
   * @param inputRequest worker request
   * @return return response map
   */
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    workerActionRequest.getInputVariables().put(REQUEST_TYPE, getRequestType(workerActionRequest));
    executeConnectorAction(workerActionRequest, connector, appConfig.getEnv());
    // setting response with activityid_response
    Map<String, Object> returnMap = new HashMap<>();
    returnMap.put(new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString(),
        Boolean.TRUE.toString());
    return returnMap;
  }

  /**
   * Function to generate the request variables from parameterDetails.
   *
   * @param parameterDetails    : {"selected": false, "parameters": {"Assignee": {"fieldValue":
   *                            ["Level1:9341454046708343", "Level1:93414540466967",
   *                            "Level2:9341454047031381"]}, "approvalType": {"fieldValue":
   *                            ["Level1:Sequential", "Level2:Parallel"]}}}
   * @param workerActionRequest
   * @return
   */
  @Override
  public Map<String, Object> getDerivedVariablesForMultiLevel(WorkerActionRequest workerActionRequest,
      Map<String, ParameterDetails> parameterDetails) {

    Map<String, Object> derivedVariablesForMultiLevel = new HashMap<>();

    // Fetch approval details using the refined function
    Map<String, List<String>> levelApprovalDetailsMap = fetchAssigneeDetails(parameterDetails);

    // Fetch approval type list
    Map<String, String> approvalTypeList = getApprovalTypeList(parameterDetails);

    List<ApprovalRequest> approvalRequestList = constructApprovalRequestList(levelApprovalDetailsMap,approvalTypeList);

    derivedVariablesForMultiLevel.put(ApprovalTaskConstants.ENTITY_TYPE, generateEntityType(workerActionRequest));
    derivedVariablesForMultiLevel.put(ApprovalTaskConstants.APPROVER_REQUEST_LIST, approvalRequestList);

    return derivedVariablesForMultiLevel;
  }

  /**
   * Constructs a list of ApprovalRequest objects.
   *
   * @param levelApprovalDetailsMap a map of level keys and approver IDs
   * @param approvalTypeList a map of level keys and approval types
   * @return a list of ApprovalRequest objects
   */
  private List<ApprovalRequest> constructApprovalRequestList(Map<String, List<String>> levelApprovalDetailsMap, Map<String, String> approvalTypeList) {
    return levelApprovalDetailsMap.entrySet().stream()
        .sorted(Map.Entry.comparingByKey()) // Ensures we maintain a predictable order
        .map(entry -> {
          String level = entry.getKey();
          List<String> approverIds = entry.getValue();
          String upperCaseApprovalType = approvalTypeList.get(level).toUpperCase();

          List<ApproverDetail> approverDetails = approverIds.stream()
              .map(id -> ApproverDetail.builder().approverId(id).build())
              .collect(Collectors.toList());

          return ApprovalRequest.builder()
              .approvalType(upperCaseApprovalType)
              .levelId(level)
              .approverDetails(approverDetails)
              .build();
        })
        .collect(Collectors.toList());
  }

  /**
   * Extracts approver details from parameter data where each entry is expected to contain a map
   * of details, including levels and approver specifics.
   */
    private Map<String, List<String>> fetchAssigneeDetails(Map<String, ParameterDetails> parameterDetails) {
      Map<String, List<String>> levelApprovalDetails = new HashMap<>();

      if (parameterDetails.containsKey(ApprovalTaskConstants.ASSIGNEE)) {
        List<String> assigneeDetails = parameterDetails.get(ApprovalTaskConstants.ASSIGNEE).getFieldValue();
        if (Objects.nonNull(assigneeDetails) && !CollectionUtils.isEmpty(assigneeDetails)) {
          assigneeDetails.forEach(assignee -> {
            String[] assigneeList = assignee.split(":");
            if (assigneeList.length < 2) {
              WorkflowLogger.logError("Assignee list length is less than 2: %s", assigneeList);
              return ; // Skip this entry if it doesn't contain two parts
            }
            String levelKey = assigneeList[0];
            String assigneeId = assigneeList[1];
            levelApprovalDetails.computeIfAbsent(levelKey, k -> new ArrayList<>()).add(assigneeId);
          });
        }
      }
      return levelApprovalDetails;
    }
      /**
       *   Retrieves a mapping of keys to their corresponding ApprovalTypeEnum values.
       */
    private Map<String, String> getApprovalTypeList(Map<String, ParameterDetails> parameterDetails) {
      Map<String, String> approvalTypeDetails = new HashMap<>();

      if (parameterDetails.containsKey(ApprovalTaskConstants.APPROVER_TYPE)) {
        List<String> approvalTypeList = parameterDetails.get(ApprovalTaskConstants.APPROVER_TYPE).getFieldValue();
        if (Objects.nonNull(approvalTypeList) && !CollectionUtils.isEmpty(approvalTypeList)) {
          approvalTypeList.forEach(approval -> {
            String[] approvalParts = approval.split(":");
            if (approvalParts.length < 2) {
              WorkflowLogger.logError("Approval parts length is less than 2: %s", approval);
              return ; // Skip this entry if it does not contain two parts
            }
            String levelKey = approvalParts[0];
            String approvalType = approvalParts[1];
            approvalTypeDetails.put(levelKey, approvalType);
          });
        }
      }
      return approvalTypeDetails;
    }

  /**
   * Function to generate the derived variables from parameterDetails.
   *  * @param parameterDetails : {"selected": false, "parameters": {"Assignee": {"fieldValue": ["9341454046708343", "9341454047031381"]]}, "approvalType": {"fieldValue": []}}}
   * @param workerActionRequest
   * @return
   */
  @Override
  public Map<String, Object> getDerivedVariables(WorkerActionRequest workerActionRequest, Map<String, Object> parameterDetails) {

    Map<String, Object> derivedVariables = new HashMap<>();
    parameterDetails.forEach((paramKey, val) -> derivedVariables.put(paramKey,((Map)val).get(ApprovalTaskConstants.FIELD_VALUE)));

    derivedVariables.put(ApprovalTaskConstants.ENTITY_TYPE, generateEntityType(workerActionRequest));
    derivedVariables.put(ApprovalTaskConstants.APPROVER_DETAILS, generateApprovalDetails(derivedVariables, workerActionRequest));
    derivedVariables.put(ApprovalTaskConstants.APPROVER_TYPE, getApprovalType(derivedVariables));

    return derivedVariables;
  }
  /**
   * Fetch the parameterDetails value from the parent activity's placeholder
   *
   * @param workerActionRequest
   * @return
   */
  //TODO: Move this into a utility for extraction
  //TODO: Handle other definition types
  public String getUserAttributesFromParent(WorkerActionRequest workerActionRequest){
    WorkflowVerfiy.verifyNull(workerActionRequest.getInputVariables(),
        WorkflowError.INVALID_PROCESS_DETAILS);

    String rootActivityId = workerActionRequest.getInputVariables()
        .get(WorkflowConstants.ACTIVITY_ID);

    // ActivityId will be absent in case of on demand approval since there will be no rule evaluation
    if(ObjectUtils.isEmpty(rootActivityId)){
      return null ;
    }

    String rootProcessInstanceId = workerActionRequest.fetchParentProcessInstanceId();
    WorkflowVerfiy.verifyNull(rootProcessInstanceId,
        WorkflowError.INVALID_ROOT_PROCESS_INSTANCE_ID);

    DefinitionDetails definitionDetails = processDetailsRepoService.
        findByProcessIdWithoutDefinitionData(rootProcessInstanceId)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

    DefinitionActivityDetail parentTaskDetails = definitionActivityDetailsRepository.
        findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
            rootActivityId, definitionDetails.getDefinitionId())
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND));

    return parentTaskDetails.getUserAttributes();
  }

  /**
   * Modify the entityType value appropriate to the payload
   *
   * @param workerActionRequest
   * @return
   */
  private String generateEntityType(WorkerActionRequest workerActionRequest) {
    Map<String, String> inputVariables = workerActionRequest.getInputVariables();
    RecordType recordType = RecordType.fromTypeOrValue(inputVariables.get(ApprovalTaskConstants.ENTITY_TYPE));
    WorkflowVerfiy.verifyNull(recordType, WorkflowError.INVALID_VARIABLE_VALUE);

    return Optional.of(recordType.name())
        .orElseThrow(() -> new WorkflowGeneralException(
            WorkflowError.INVALID_VARIABLE_VALUE));
  }

  /**
   * Get the approvalType value appropriate to the payload
   *
   * @param derivedVariables
   * @return
   */
  private String getApprovalType(Map<String, Object> derivedVariables) {
    List<String> approvalType = ObjectConverter.convertObject(derivedVariables.get(ApprovalTaskConstants.TYPE), List.class);

    if(ObjectUtils.isEmpty(approvalType)){
      return null;
    }

    return approvalType.stream().findFirst().orElse(null);
  }
  /**
   * Modify the approverDetails value appropriate to the payload
   * In case of on demand approval for non QBO entities, approver details is fetched from process variables, if present
   * Eg processVariable: [{"approvalType":"SEQUENTIAL","approverDetails":[{"approverId":"9341451993702998"},{"approverId":"9341452549046354"}]},
   * {"approvalType":"ANY_ONE","approverDetails":[{"approverId":"9341451993702998"},{"approverId":"9341452549046354"}]}]
   *
   * @param derivedVariables
   * @return
   */

  private List<Map<String, String>> generateApprovalDetails(Map<String, Object> derivedVariables,
      WorkerActionRequest workerActionRequest) {
    List<String> approverDetails = ObjectConverter.convertObject(
        derivedVariables.get(ApprovalTaskConstants.ASSIGNEE), List.class);
    if (ObjectUtils.isEmpty(approverDetails)) {
      List<ApprovalRequest> processVariableApproverDetails = (List<ApprovalRequest>) Optional.ofNullable(
          workerActionRequest.getInputVariables().get(WorkflowConstants.APPROVER_DETAILS))
          .map(approvers -> ObjectConverter.fromJson(approvers, new TypeReference<List<ApprovalRequest>>() {}))
          .orElse(Collections.emptyList());

      //TODO : Will need handling with AS schema change to support hybrid approval in Create approval request call
      return processVariableApproverDetails.stream().findFirst()
          .filter(approverDetail -> Objects.nonNull(approverDetail.getApproverDetails())).map(approverDetail ->
              approverDetail.getApproverDetails().stream()
                  .map(approver -> Map.of(ApprovalTaskConstants.APPROVER_ID,
                      approver.getApproverId()))
                  .collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    return approverDetails.stream().map(id -> Map.of(ApprovalTaskConstants.APPROVER_ID, id))
        .collect(Collectors.toList());
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.EVENT_EXTERNAL_TASK, Type.EXTERNAL_TASK_METRIC, exception);
  }
  
	/**
	 * @param workerActionRequest in worker request
	 * @return the request type based on evaluating the process variable[IS_THIRD_PARTY_TXN,ON_DEMAND_APPROVAL].
	 */
	private String getRequestType(WorkerActionRequest workerActionRequest) {

		// checks if IS_THIRD_PARTY_TXN process variable is available
		boolean thirdPartyTxnRequestType = (Boolean.TRUE.toString())
				.equalsIgnoreCase(workerActionRequest.getInputVariables()
						.getOrDefault(WorkFlowVariables.IS_THIRD_PARTY_TXN.getName(), Boolean.FALSE.toString()));

		/**
		 * if process variable IS_THIRD_PARTY_TXN is true returns THIRD_PARTY_TXN_REQUEST_TYPE.
		 * if process variable ON_DEMAND_APPROVAL is true and source is QBO return ON_DEMAND_APPROVAL_REQUEST_TYPE.
     * In case of Non QBO entities, we don't send requestType as "On Demand Approval". This is a special handling added as Trigger point for workflow is always WAS.
     *  else returns CUSTOM_WORKFLOW_REQUEST_TYPE.
		 **/
		return onDemandHelper.isRequestTypeOnDemandAproval(workerActionRequest) ? ON_DEMAND_APPROVAL_REQUEST_TYPE
				: thirdPartyTxnRequestType ? THIRD_PARTY_TXN_REQUEST_TYPE : CUSTOM_WORKFLOW_REQUEST_TYPE;
	}
}
