package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Modifies the WorkflowTask request of BATCH_NOTIFICATION_TASK
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class BatchNotificationTaskRequestModifier implements TaskRequestModifier {

  private NotificationTaskRequestModifier notificationTaskRequestModifier;

  public TaskType getName() {
    return TaskType.BATCH_NOTIFICATION_TASK;
  }

  @Override
  public WorkflowTaskRequest getTaskRequest(
      WorkflowTaskRequest taskRequest, WorkerActionRequest workerActionRequest) {
    return notificationTaskRequestModifier.getTaskRequest(taskRequest, workerActionRequest);
  }
}
