package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COMMA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * For a given list of definitions, update the internal status as MARKED_FOR_DELETE and publish
 * corresponding domain events. Deletion of these definitions from the db will be handled by the batch
 * clean up job
 */
@Component
@AllArgsConstructor
public class BulkDeleteDefinitionsHandler extends WorkflowTaskHandler {

  private DefinitionServiceHelper definitionServiceHelper;
  private UserContributionService userContributionService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.BULK_DELETE_DEFINITIONS_HANDLER;
  }

  /**
   * set status for all eligible definitions in db as MARKED_FOR_DELETE
   *
   * @param inputRequest worker request
   * @return
   * @param <T>
   */
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String activityId = workerActionRequest.getActivityId();
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

    final List<DefinitionPendingDeletion> definitionPendingDeletion =
        WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest);

    // update status of given definition ids to MARKED_FOR_DELETE and publish domain events
    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
        WorkflowVariabilityUtil.getDefinitionList(definitionPendingDeletion),
        InternalStatus.MARKED_FOR_DELETE);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Bulk update definition: internalStatus=MARKED_FOR_DELETE definitionIds=%s, definitionKey=%s ownerId=%s",
                    definitionPendingDeletion.stream()
                        .map(DefinitionPendingDeletion::getId)
                        .collect(Collectors.joining(COMMA)),
                    definitionPendingDeletion.stream()
                        .map(DefinitionPendingDeletion::getKey)
                        .collect(Collectors.joining(COMMA)),
                    realmId)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .className(this.getClass().getName()));

    List<String> definitionKeys =
        Optional.ofNullable(definitionPendingDeletion)
            .map(Collection::stream)
            .orElseGet(Stream::empty)
            .map(DefinitionPendingDeletion::getKey)
            .collect(Collectors.toList());
    // call to delete template references in UCS with List of Definition Keys if present
    if (ObjectUtils.isNotEmpty(definitionKeys)) {
      userContributionService.deleteAllPublishedTemplates(realmId, definitionKeys);
    }

    return ImmutableMap.of(
        new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
        Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(
        MetricName.BULK_DEFINITION_DELETE, Type.EXTERNAL_TASK_METRIC, exception);
  }
}
