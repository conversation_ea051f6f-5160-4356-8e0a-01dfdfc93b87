package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteProcessDefinitionKey;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

/**
 * <AUTHOR>
 *     <p>Delete Definitions in Camunda for a Relam Based on Definition Keys.
 */
@Component
@AllArgsConstructor
public class CamundaDeleteTaskHandler extends WorkflowTaskHandler {

  private DefinitionDetailsRepository definitionDetailsRepository;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.CAMUNDA_DELETION_HANDLER;
  }

  @SuppressWarnings("serial")
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    List<String> definitionKeys =
        definitionDetailsRepository.findDistinctByDefinitionKeyAndOwnerIdAndModelType(
            Long.valueOf(realmId), ModelType.BPMN);

    if (Objects.nonNull(definitionKeys)) {
      List<Task> camundaDeleteDefinitionTaskList =
          definitionKeys.stream()
              .map(
                  keys ->
                      new CamundaDeleteProcessDefinitionKey(
                          bpmnEngineDefinitionServiceRest, keys, true, true))
              .collect(Collectors.toList());

      new RxExecutionChain(state)
          .next(
              camundaDeleteDefinitionTaskList.toArray(
                  new Task[camundaDeleteDefinitionTaskList.size()]))
          .execute();
    }
    // setting response with activityid_response
    return ImmutableMap.of(new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString(),
        Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.CAMUNDA_DELETION, Type.CAMUNDA_METRIC, exception);
  }
}
