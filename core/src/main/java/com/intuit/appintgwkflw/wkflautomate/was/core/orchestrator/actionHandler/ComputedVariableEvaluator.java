package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Map.Entry;

/**
 * <AUTHOR> Base class for the evaluator containing the implemenation of computed variable
 *     computation
 */
public abstract class ComputedVariableEvaluator {

  public abstract ComputedVariableType getComputedVariableType();

  public abstract String evaluateValue(
      final WorkerActionRequest workerActionRequest,
      final Entry<String, HandlerDetails.ParameterDetails> parameterDetailsEntry);
}
