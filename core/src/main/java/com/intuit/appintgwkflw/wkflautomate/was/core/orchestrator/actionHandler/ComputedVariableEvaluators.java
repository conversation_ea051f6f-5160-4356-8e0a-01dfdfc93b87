package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import java.util.HashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Class acts as factory to return computed variable evaluators
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ComputedVariableEvaluators {
  private static final Map<ComputedVariableType, ComputedVariableEvaluator>
      COMPUTED_VARIABLE_EVALUATOR_MAP = new HashMap<>();
  /**
   * Adds a evaluator.
   *
   * @param computedVariableType the computed variable type
   * @param computedVariableEvaluator the computed variable evaluator
   */
  public static void addEvaluator(
      ComputedVariableType computedVariableType,
      ComputedVariableEvaluator computedVariableEvaluator) {
    COMPUTED_VARIABLE_EVALUATOR_MAP.put(computedVariableType, computedVariableEvaluator);
  }
  /**
   * Gets evaluator.
   *
   * @param computedVariableType the computed variable type
   * @return computed variable evaluator impl
   */
  public static ComputedVariableEvaluator getEvaluator(ComputedVariableType computedVariableType) {
    return COMPUTED_VARIABLE_EVALUATOR_MAP.get(computedVariableType);
  }
  /**
   * Contains boolean.
   *
   * @param computedVariableType the computed variable type
   * @return true /false if evaluator is present or not
   */
  public static boolean contains(ComputedVariableType computedVariableType) {
    return COMPUTED_VARIABLE_EVALUATOR_MAP.containsKey(computedVariableType);
  }
}
