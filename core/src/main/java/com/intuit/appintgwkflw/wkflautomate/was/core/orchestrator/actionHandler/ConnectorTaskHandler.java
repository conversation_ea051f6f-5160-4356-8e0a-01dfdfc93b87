package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.connector.constants.ParameterVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector.ConnectorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ApprovalUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.json.JSONObject;

/**
 * Interface to be implemented by all handler leveraging the generic connector
 *
 * <AUTHOR>
 */
public interface ConnectorTaskHandler {

  /**
   * Execute request in connector with map of variables
   *
   * @param workerActionRequest
   * @param wasGenericConnector
   * @param env
   * @return
   */
  default Object executeConnectorAction(WorkerActionRequest workerActionRequest,
      ConnectorImpl wasGenericConnector, String env) {
    WorkflowLogger.logInfo("Generating the map of parameter details");

    String handlerId = workerActionRequest.getHandlerId();
    String userAttributes = getUserAttributesFromParent(workerActionRequest);
    Map<String, ParameterDetails> parameterDetails = ApprovalUtil
        .getParameterDetailsMapForMultiConditionWorkflows(userAttributes);
    boolean isMultiLevelHybridApproval = ApprovalUtil.isMultiLevelHybridApproval(parameterDetails);

    Map<String, Map<String, Object>> parameterVariableMaps = generateParameterVariableMaps(
        workerActionRequest.getInputVariables(), workerActionRequest, parameterDetails, isMultiLevelHybridApproval , userAttributes );

        // only append v2 for createApprovalRequest HandlerId with multi-level scenario
    if (isMultiLevelHybridApproval && ApprovalTaskConstants.CREATE_APPROVAL_REQUEST_HANDLER_ID.equalsIgnoreCase(handlerId)){
      handlerId = handlerId + "V2";
      WorkflowLogger.logInfo("Processing Multi-Level Hybrid Approval V2 for handlerId: %s", handlerId);
    } else {
      WorkflowLogger.logInfo("Processing Non-Multi-Level Hybrid Approval V1 for handlerId: %s", handlerId);
    }
    return wasGenericConnector.execute(env, handlerId, parameterVariableMaps);
  }
  /**
   * Create a single map to include both processVariables and derivedVariables
   *
   * @param inputVariables
   * @param workerActionRequest
   * @param parameterDetails
   * @param isMultiLevelHybridApproval
   * @param userAttributes
   * @return
   */
  default Map<String, Map<String, Object>> generateParameterVariableMaps(
      Map<String, String> inputVariables, WorkerActionRequest workerActionRequest,
      Map<String, ParameterDetails> parameterDetails, boolean isMultiLevelHybridApproval , String userAttributes) {
    HashMap<String, Map<String, Object>> parameters = new HashMap<>();
    Map<String, Object> processVariables = new HashMap<>();

    inputVariables.forEach((key, value) -> processVariables.put(key, (Object) value));
    parameters.put(ParameterVariableType.PROCESS_VARIABLE.getParameterVariableType(),
        processVariables);

    if (isMultiLevelHybridApproval) {
      Map<String, Object> derivedVariablesForMultiLevel = getDerivedVariablesForMultiLevel(workerActionRequest, parameterDetails);
      parameters.put(ParameterVariableType.DERIVED_VARIABLE.getParameterVariableType(), derivedVariablesForMultiLevel);
      WorkflowLogger.logInfo("Added derived variables for Multi-Level Hybrid Approval for handlerId: %s", workerActionRequest.getHandlerId());
    } else {
     // userAttributes will be null in onDemand
      Map<String, Object> attributes = Objects.isNull(userAttributes) ? new HashMap<>() : (Map <String, Object>) ObjectConverter.convertObject(userAttributes, JSONObject.class).toMap().get(
          ApprovalTaskConstants.PARAMETERS);
      Map<String, Object> derivedVariables = getDerivedVariables(workerActionRequest, attributes);
      parameters.put(ParameterVariableType.DERIVED_VARIABLE.getParameterVariableType(), derivedVariables);
      WorkflowLogger.logInfo("Added derived variables for Single-Level/Non-Hybrid Approval for handlerId: %s", workerActionRequest.getHandlerId());
    }
    return parameters;
  }

  /**
   * Function to generate the derived variables. If unimplemented, would return an empty map.
   *
   * @param workerActionRequest
   * @return
   */
  default Map<String, Object> getDerivedVariables(WorkerActionRequest workerActionRequest,
      Map<String, Object> parameterDetails) {
    return new HashMap<>();
  }
  /**
   * Function to generate the request variables. If unimplemented, would return an empty map.
   *
   * @param workerActionRequest
   * @return
   */
  default Map<String, Object> getDerivedVariablesForMultiLevel(WorkerActionRequest workerActionRequest,
      Map<String, ParameterDetails> parameterDetails) {
    return new HashMap<>();
  }
  /**
   * Function to generate the parameterDetails. If unimplemented, would return null.
   *
   * @param workerActionRequest
   * @return
   */
  default String getUserAttributesFromParent(WorkerActionRequest workerActionRequest) {
    return null ;
  }
}
