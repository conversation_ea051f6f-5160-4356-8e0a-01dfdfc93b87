package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTaskWithCorrelationKeys;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DeleteAllMessageEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *     </>
 *     <p>Correlate All Message to All the running processes to closure. This task will make
 *     processes in Error state Active and internal status as Already_Error as active processes will
 *     be picked up by the worker. Also, we will correlate all the non-ended active processes. <br>
 *     </>
 *     <p>Step 1 : Find All the processes in Error state and update them accordingly [Internal
 *     Status : ALREADY_ERROR and Process Status : Active]
 *     <p></>Step 2 : Correlate All Message using business Key </>
 */
@Component
public class CorrelateAllMessageTaskHandler extends WorkflowTaskHandler {

  private final ProcessDetailsRepoService processDetailsRepoService;
  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  
  private final FeatureManager featureManager;
  private final OnDemandHelper onDemandHelper;

  public CorrelateAllMessageTaskHandler(
          ProcessDetailsRepoService processDetailsRepoService,
          BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest,
          @Qualifier(WorkflowConstants.IXP_MANAGER_BEAN) FeatureManager featureManager, OnDemandHelper onDemandHelper) {
    this.processDetailsRepoService = processDetailsRepoService;
    this.bpmnEngineRunTimeServiceRest = bpmnEngineRunTimeServiceRest;
    this.featureManager = featureManager;
    this.onDemandHelper = onDemandHelper;
  }

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_CORRELATE_NON_ENDED_PROCESS;
  }

  @SuppressWarnings("serial")
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

    List<ProcessDetails> erroredProcesses = Collections.emptyList();
    List<Task> correlateAllMessageTaskList = Collections.emptyList();

    if (!WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager)) {
      /**
       * TODO: cleanup post 100% rollout of new downgrade flow
       * https://jira.intuit.com/browse/QBOES-22936
       */
      erroredProcesses = getErroredProcessListFromOwnerId(realmId);
      correlateAllMessageTaskList = getCorrelateTaskListFromOwnerId();
    } else {
      List<DefinitionPendingDeletion> definitionsForProcessDeletion = new ArrayList<>();
      definitionsForProcessDeletion.addAll(WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest));

      // System definition for on demand approval won't be deleted on downgrade and hence will not be present in definitionPendingDeletion.
      // Fetching it separately so that we can send correlate call for on demand approval processes as well.
      definitionsForProcessDeletion.addAll(onDemandHelper.fetchOnDemandApprovalDefinition().stream()
          .map(DefinitionPendingDeletion::buildFromDefinitionDetails).collect(
              Collectors.toList()));

      if (ObjectUtils.isNotEmpty(definitionsForProcessDeletion)) {
        erroredProcesses = getErroredProcessListFromDefinitionList(definitionsForProcessDeletion, realmId);

        correlateAllMessageTaskList =
            getCorrelateTaskListFromDefinitionList(definitionsForProcessDeletion, realmId);
      }

    }

    // If error Processes are present then update the status
    if (!CollectionUtils.isEmpty(erroredProcesses)) {
      updateProcessStatusInDB(updateInternalAndProcessStatus(erroredProcesses));
    }

    final State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    new RxExecutionChain(state)
        .next(correlateAllMessageTaskList.toArray(new Task[correlateAllMessageTaskList.size()]))
        .execute();

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "All processes signalled for downgrade processId=%s",
                    workerActionRequest.getProcessInstanceId())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_SIGNAL_PROCESS)
                .className(this.getClass().getSimpleName()));

    // setting response with activityid_response
    return ImmutableMap.of(
        MessageFormat.format("{0}_{1}", workerActionRequest.getActivityId(), RESPONSE.getName()),
        Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.CAMUNDA_CORRELATION, Type.CAMUNDA_METRIC, exception);
  }

  /**
   * Helper method to set Process status as active and Internal status as ALREADY_ERROR
   *
   * @param processDetails
   * @return
   */
  private List<ProcessDetails> updateInternalAndProcessStatus(List<ProcessDetails> processDetails) {
    processDetails.forEach(
        process -> {
          process.setProcessStatus(ProcessStatus.ACTIVE);
          process.setInternalStatus(InternalStatus.ALREADY_ERROR);
        });
    return processDetails;
  }

  private void updateProcessStatusInDB(List<ProcessDetails> processDetails) {
    List<ProcessDetails> updatedProcesses =
        processDetailsRepoService.saveOrUpdateProcess(processDetails);
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "No. of error processes to be correlated for completion=%s",
                    updatedProcesses.size())
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.UPDATE_PROCESSS_STATUS)
                .className(this.getClass().getSimpleName()));
  }

  private List<ProcessDetails> getErroredProcessListFromOwnerId(String ownerId) {
    return processDetailsRepoService
        .findByRealmIdAndProcessStatus(Long.valueOf(ownerId), ProcessStatus.ERROR)
        .orElse(Collections.emptyList());
  }

  private List<Task> getCorrelateTaskListFromOwnerId() {
    // Correlate All message to be sent to all the Processes for Active and Non-ended processes by
    // business key. We can think of mechanism to Correlate based on Correlation keys as well

    // Will Stream over the Message List which are used to Signal processes for cleanup related
    // tasks in the even of downgrade.
    return Arrays.stream(DeleteAllMessageEnum.values())
        .map(t -> new CorrelateAllMessageTask(bpmnEngineRunTimeServiceRest, t.getMessageName()))
        .collect(Collectors.toList());
  }

  private List<ProcessDetails> getErroredProcessListFromDefinitionList(
      List<DefinitionPendingDeletion> definitionPendingDeletion, String realmId) {
    List<ProcessDetails> erroredProcesses = processDetailsRepoService
        .findByDefinitionDetailsListAndProcessStatus(
            WorkflowVariabilityUtil.getDefinitionList(definitionPendingDeletion),
            ProcessStatus.ERROR)
        .orElse(Collections.emptyList());

    // Adding filtering based on realm id, since on demand approval processes are mapped to system definitions, we might get processes from other realms as well
    return erroredProcesses.stream().filter(processDetails -> realmId.equalsIgnoreCase(
        String.valueOf(processDetails.getOwnerId()))).collect(Collectors.toList());
  }

  private List<Task> getCorrelateTaskListFromDefinitionList(
      List<DefinitionPendingDeletion> definitionPendingDeletions, String ownerId) {
    Set<String> definitionKeysToCorrelate =
        WorkflowVariabilityUtil.getDistinctDefinitionKeys(definitionPendingDeletions);
    Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
    return definitionKeysToCorrelate.stream()
        .map(
            definitionKey -> {
              correlateKeysMap.put(
                  CorrelationKeysEnum.DEFINITION_KEY.getName(),
                  new CorrelateAllMessage.CorrelateKey(
                      definitionKey, CorrelationKeysEnum.DEFINITION_KEY.getDataType()));

              CorrelateAllMessage correlateAllMessage =
                  new CorrelateAllMessage(
                      DeleteAllMessageEnum.DELETE_ALL_SIGNAL.getMessageName(),
                      ownerId,
                      new HashMap<>(correlateKeysMap));
              return new CorrelateAllMessageTaskWithCorrelationKeys(
                  bpmnEngineRunTimeServiceRest, correlateAllMessage);
            })
        .collect(Collectors.toList());
  }
}
