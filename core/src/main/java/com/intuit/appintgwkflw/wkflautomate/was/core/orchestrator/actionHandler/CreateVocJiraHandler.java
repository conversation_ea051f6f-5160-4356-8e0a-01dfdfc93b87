package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;


import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.jira.helper.JiraServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.jira.service.JiraServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.slack.service.SlackServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.JiraConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.SlackConstant;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.JiraRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.response.JiraResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.slack.model.SlackRequestModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.slack.api.model.Message;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> This class is responsible for reading voc messages from slack and create jira for
 * it.
 */
@Component
@AllArgsConstructor
public class CreateVocJiraHandler extends WorkflowTaskHandler {

  private final WorkerUtil workerUtil;
  private final SlackServiceManager slackServiceManager;
  private final JiraServiceManager jiraServiceManager;
  private final String TIMER_TASK = "TimerTask";
  private final Integer DEFAULT_LAST_HOURS = 24;

  /**
   * Return the task handler name
   *
   * @return
   */
  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_CREATE_VOC_JIRA;
  }


  /**
   * In this method, -  we are reading Slack messages as per channel details provided in the
   * request. - Getting response text from the slack message - Create a jira as per project key
   * given in input request.
   *
   * @param inputRequest worker request
   * @param <T>
   * @return Summary map for all the jira created voc's.
   */
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    Map<String, Object> summaryMap = new HashMap<>();
    summaryMap.put(TIMER_TASK, "false");
    summaryMap.put(JiraConstants.JIRA_KEYS, new ArrayList<>());
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    workerActionRequest = workerUtil.validate(workerActionRequest);
    // get all the parameters
    Map<String, String> inputVariables = workerActionRequest.getInputVariables();
    String oldest = inputVariables.get(SlackConstant.OLDEST);
    String latest = inputVariables.get(SlackConstant.LATEST);
    String channelId = inputVariables.get(SlackConstant.CHANNEL_ID);
    if (inputVariables.get(TIMER_TASK) != null) {
      WorkflowLogger.logInfo(
          "step=timerTask; processInstanceId=%s",
          workerActionRequest.getProcessInstanceId()
      );
      oldest = String.valueOf(DateUtils.getLastXHoursUnixTimeStamp(DEFAULT_LAST_HOURS));
      latest = DateUtils.getCurrentTimeStamp();
    }
    WorkflowLogger.logInfo(
        "step=fetchSlackMessages; processInstanceId=%s",
        workerActionRequest.getProcessInstanceId()
    );
    List<Message> messageList = slackServiceManager.readAllMessages(
        SlackRequestModel.builder().channelId(channelId)
            .oldest(oldest)
            .latest(latest)
            .limit(SlackConstant.MESSAGE_LIMIT).build());
    WorkflowLogger.logInfo(
        "step=countSlackMessages; processInstanceId=%s, totalCount=%s",
        workerActionRequest.getProcessInstanceId(), messageList.size());
    if (CollectionUtils.isEmpty(messageList)) {
      return summaryMap;
    }
    // get jira details
    // jira
    for (Message message : messageList) {
      String summary = JiraServiceHelper.getResponseData(message.getText());
      if (summary == null) {
        // log
        WorkflowLogger.logInfo(
            "step=emptySummary; processInstanceId=%s",
            workerActionRequest.getProcessInstanceId());
        continue;
      }
      if (summary.length() >= JiraConstants.SUMMARY_LENGTH) {
        summary = summary.substring(0, JiraConstants.SUMMARY_LENGTH - 1);
      }
      String dueDate = JiraServiceHelper.getDueDate(message.getText());
      WorkflowLogger.logInfo(
          "step=dueDate; processInstanceId=%s, dueDate=%s",
          workerActionRequest.getProcessInstanceId(), dueDate);
      WorkflowLogger.logInfo(
          "step=createJira; processInstanceId=%s summary=%s",
          workerActionRequest.getProcessInstanceId(), summary);
      String slackLink = slackServiceManager.getMessageChatLink(channelId, message.getTs());
      WorkflowLogger.logInfo(
          "step=slackLink; processInstanceId=%s slackLink=%s",
          workerActionRequest.getProcessInstanceId(), slackLink);
      try {
        JiraResponse jiraResponse = jiraServiceManager.createJira(
            JiraRequest.builder().project(inputVariables.get(JiraConstants.PROJECT))
                .component(inputVariables.get(JiraConstants.COMPONENT))
                .label(JiraServiceHelper.getLabels(inputVariables.get(JiraConstants.LABEL)))
                .issueType(inputVariables.get(JiraConstants.ISSUE_TYPE))
                .description(
                    JiraServiceHelper.appendChatLinkInDescription(message.getText(), slackLink))
                .summary(summary).customfield_11801(inputVariables.get(JiraConstants.COUNTRY_FIELD))
                .customfield_13504(dueDate)
                .customfield_13505(inputVariables.get(JiraConstants.CUSTOM_FILED_13505))
                .customfield_17002(inputVariables.get(JiraConstants.CUSTOM_FILED_17002))
                .build());
        if (jiraResponse != null) {
          ((ArrayList) summaryMap.get(JiraConstants.JIRA_KEYS)).add(jiraResponse.getKey());
          WorkflowLogger.logInfo(
              "step=jiraCreated; processInstanceId=%s, key=%s",
              workerActionRequest.getProcessInstanceId(), jiraResponse.getKey());
        }
      } catch (Exception exception) {
        WorkflowLogger.logInfo(
            "step=jiraFailed; processInstanceId=%s, error=%s",
            workerActionRequest.getProcessInstanceId(), exception.getMessage());
      }

    }
    return summaryMap;
  }


  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {

  }

}
