package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@AllArgsConstructor
/**
 * This class is responsible for extracting properties details for non-user configured blocks.
 * Non-user-configured blocks are those blocks which are not exposed to UI and use by Camunda after
 * some state change. e.g.
 *
 * <pre>sendApproveNotification_txnApproval</pre>
 *
 * In this block we have some parameters fields values which needs to be localised as per locale is
 * saved into placeholder values while creating the definition.
 */
public class CustomWorkflowNonUserConfiguredParameterDetailsExtractor
    implements AppConnectParameterDetailExtractor {

  private final ProcessDetailsRepoService processDetailsRepoService;
  private final TranslationService translationService;
  /**
   * In case of custom-workflows, all non-configurable parameters are extracted using the input
   * variables in workerActionRequest. The field values are replaced with localised data w.r.t saved
   * locale into placeholder values
   *
   * @param workerActionRequest
   * @return ParameterDetailsMap
   */
  @Override
  @Metric(
      name = MetricName.CUSTOM_WORKFLOW_NON_USER_CONFIGURED_PARAMETER_DETAILS_EXTRACTOR,
      type = Type.APPLICATION_METRIC)
  public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest) {
    Optional<Map<String, HandlerDetails.ParameterDetails>> optionalParameterDetailsMap =
        SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    String recordTypeDisplayValue =
        Optional.ofNullable(
                RecordType.fromTypeOrValue(
                    workerActionRequest.getInputVariables().get(ENTITY_TYPE)))
            .map(RecordType::getDisplayValue)
            .orElse(null);
    String locale = getLocale(workerActionRequest);
    optionalParameterDetailsMap.ifPresent(
        stringParameterDetailsMap ->
            stringParameterDetailsMap
                .values()
                .forEach(
                    parameterDetailsEntry ->
                        translateParameterDetailsFieldValues(
                            parameterDetailsEntry, recordTypeDisplayValue, locale)));
    return optionalParameterDetailsMap;
  }

  /**
   * Fetch locale from placeholder values in case of normal flow and fetches locale from process variables for on demand approval
   *
   * @param workerActionRequest
   * @return
   */
  private String getLocale(WorkerActionRequest workerActionRequest) {
    boolean isOnDemandApproval = (Boolean.TRUE.toString()).equalsIgnoreCase(workerActionRequest
        .getInputVariables().getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, Boolean.FALSE.toString()));

    return isOnDemandApproval ? workerActionRequest
        .getInputVariables().get(WorkflowConstants.INTUIT_WAS_LOCALE) :
        SingleDefinitionUtil.getLocaleFromPlaceHolderValues(
        getDefinitionDetails(workerActionRequest));
  }

  /**
   * Find definition and throw exception if definition not exists;
   *
   * @param workerActionRequest
   * @return
   */
  private DefinitionDetails getDefinitionDetails(WorkerActionRequest workerActionRequest) {
    Optional<DefinitionDetails> definitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            workerActionRequest.fetchParentProcessInstanceId()
        );
    WorkflowVerfiy.verify(!definitionDetails.isPresent(), WorkflowError.DEFINITION_NOT_FOUND);
    return definitionDetails.get();
  }
  /**
   * This method translate all the field values of input parameter into localised string using
   * translation service {@link TranslationService}.
   *
   * @param parameterDetails {@link ParameterDetails}
   * @param recordTypeDisplayValue recordId This parameter used by translation service to localise the token
   *     parameters.
   * @param locale
   */
  private void translateParameterDetailsFieldValues(
      ParameterDetails parameterDetails, String recordTypeDisplayValue, String locale) {
    List<String> fieldValues = parameterDetails.getFieldValue();
    List<String> newFieldValues = new ArrayList<>();
    if (!CollectionUtils.isEmpty(fieldValues)) {
      fieldValues.forEach(
          fieldValue -> {
            newFieldValues.add(
                translationService.getFormattedString(fieldValue, locale, recordTypeDisplayValue));
          });
      parameterDetails.setFieldValue(newFieldValues);
    }
  }
}
