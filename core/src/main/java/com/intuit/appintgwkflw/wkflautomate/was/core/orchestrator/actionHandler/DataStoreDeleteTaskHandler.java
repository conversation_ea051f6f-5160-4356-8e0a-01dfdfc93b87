package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p>Performs Data Store CleanUp during Downgrade for Templates in WAS.
 */
@Component
@AllArgsConstructor
public class DataStoreDeleteTaskHandler extends WorkflowTaskHandler {

  private DataStoreDeleteTaskService dataStoreDeleteService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_DATA_STORE_DELETION_HANDLER;
  }

  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    /**
     * Execution is moved in another class as Proxy instance stubbing
     * can make @Transaction annotation work.
     * @Transactional annotation is required as multiple delete queries taking place.
     * These queries should be rollbacked in case of any Exception occurred at runtime.
     */
    return dataStoreDeleteService.execute((WorkerActionRequest) inputRequest);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.DELETE_WORKFLOW_DEFINITION, Type.WAS_METRIC, exception);
  }

}
