package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class DefaultParameterDetailsExtractor
    implements AppConnectParameterDetailExtractor {
  @Override
  public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest) {
    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
        SchemaDecoder.getParametersForHandler(workerActionRequest.getInputVariables());
    return parameterDetailsMap;
  }
}
