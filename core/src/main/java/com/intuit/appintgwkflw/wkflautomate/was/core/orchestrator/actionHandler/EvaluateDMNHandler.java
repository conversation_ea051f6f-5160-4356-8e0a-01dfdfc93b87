package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;


import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SingleDefinitionDmnEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class EvaluateDMNHandler extends WorkflowTaskHandler {

  private ProcessDetailsRepoService processDetailsRepoService;
  private DefinitionDetailsRepository definitionDetailsRepository;

  private final SingleDefinitionDmnEvaluator singleDefinitionDmnEvaluator;

  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_EVALUATE_DMN_HANDLER;
  }

  /**
   * Overridden method of base class to handle the async request
   * @param inputRequest worker request
   * @param <T>
   * @return
   */
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    List<Map<String, Object>> result = evaluateDMN(workerActionRequest);
    // assuming we are using singleResult for DMN evaluation
    Map<String, Object> resultMap = result.stream().findFirst().orElse(null);
    WorkflowVerfiy.verifyNull(resultMap, WorkflowError.DMN_EVALUATE_ERROR);
    Map.Entry<String, Object> entry = resultMap.entrySet().stream().findFirst().orElse(null);
    WorkflowVerfiy.verifyNull(entry, WorkflowError.DMN_EVALUATE_ERROR);
    return ImmutableMap.of(entry.getKey(), ObjectConverter.convertObject(entry.getValue(),
        Map.class).get("value"));
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.EVALUATE_DMN, Type.APPLICATION_METRIC, exception);
  }

  /**
   * Method to invoke DMN for Single definition.
   * @param workerActionRequest
   * @return
   */
  private List<Map<String, Object>> evaluateDMN(WorkerActionRequest workerActionRequest) {
    DefinitionDetails definitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                workerActionRequest.getProcessInstanceId()).
                orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

    List<DefinitionDetails> dmnDefinitionDetails =
        definitionDetailsRepository.findByParentId(definitionDetails.getDefinitionId()).
                orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DMN_NOT_FOUND));

    // In case of multiple dmns, we will query the activity table to find the dmn that we need to evaluate here.
    // This if condition could have been removed here but that would have created additional latency for
    // all the other workflows
    if (dmnDefinitionDetails.size() > 1) {
      DefinitionActivityDetail definitionActivityDetail = definitionActivityDetailsRepository.
              findByDefinitionDetailsInAndActivityId(dmnDefinitionDetails, workerActionRequest.getActivityId()).
              orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND));
      dmnDefinitionDetails = Collections.singletonList(definitionActivityDetail.getDefinitionDetails());
    }


    /**
     * TODO : In case of single definition, support is available for a single DMN. For multiple
     * externalised DMNs, the support needs to be extended as right now we are just getting the only
     * dmn present in the definition. The same applies to placeholder value extension. *
     */
    DefinitionDetails dmnDefinitionDetail = dmnDefinitionDetails.stream().findFirst().get();
    EvaluateRuleRequest evaluateRuleRequest = new EvaluateRuleRequest(dmnDefinitionDetail.getDefinitionId(),
            workerActionRequest.getVariableMap());

    List<Map<String, Object>> evaluateDmnResponse = singleDefinitionDmnEvaluator.evaluateDMN(dmnDefinitionDetail.getDefinitionData(), evaluateRuleRequest, true);
    return singleDefinitionDmnEvaluator.dmnResponseConvertor(evaluateDmnResponse);
  }
}