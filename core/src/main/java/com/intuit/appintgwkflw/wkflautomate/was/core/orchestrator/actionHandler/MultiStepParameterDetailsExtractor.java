package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for extracting Parameters details for Multistep
 * User Configured workflows
 *
 * User-configured blocks are those blocks which are exposed to UI and are saved in the placeholders.
 *
 * <pre>createTask_Assignee</pre>
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepParameterDetailsExtractor implements AppConnectParameterDetailExtractor {
  private final CustomWorkflowConfig customWorkflowConfig;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final ProcessDetailsRepository processDetailsRepository;
  private final ProcessDetailsRepoService processDetailsRepoService;

  /**
   * Gets the parameter details for the action in the multi step workflow user configured block from placeholder
   *
   * @param workerActionRequest
   * @return
   */
  @Override
  public Optional<Map<String, ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest) {

    String rootActivityId = workerActionRequest.getInputVariables().get(WorkflowConstants.ACTIVITY_ID);

    String processInstanceId = workerActionRequest.fetchParentProcessInstanceId();

    DefinitionDetails definitionDetails =
        processDetailsRepoService
            .findByProcessIdWithoutDefinitionData(processInstanceId).orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND)
            );

    DefinitionActivityDetail currentActivityDetails =
        definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
            definitionDetails.getDefinitionId(),
            workerActionRequest.getActivityId(),
            rootActivityId
        ).orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND)
        );

    //  This should be custom approval
    CustomWorkflowType customWorkflowType =
        CustomWorkflowType.getCustomWorkflowForTemplateName(
            definitionDetails.getTemplateDetails().getTemplateName()
        );

    Map<String, HandlerDetails.ParameterDetails> actionParameters =
        getActionParametersForCurrentActivity(
            workerActionRequest.getInputVariables().get(ENTITY_TYPE),
            customWorkflowType.getActionKey(),
            workerActionRequest.getDefinitionKey(),
            workerActionRequest.getActivityId()
        );

    JSONObject userVariablesActionData =
            new JSONObject(currentActivityDetails.getUserAttributes());

    SingleDefinitionUtil.fillParameterDetails(actionParameters, userVariablesActionData);

    return Optional.of(actionParameters);
  }


  public Map<String, HandlerDetails.ParameterDetails> getActionParametersForCurrentActivity(
      String recordType, String actionKey, String actionId, String subActionId
  ) {
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action recordAction =
        CustomWorkflowUtil.getRecordActionFromConfig(
            customWorkflowConfig, recordType, actionKey, actionId, subActionId);

    Map<String, HandlerDetails.ParameterDetails> actionParameters =
        recordAction.getParameters().stream()
            .collect(
                Collectors.toMap(
                    Parameter::getName,
                    CustomWorkflowUtil::getParameterDetailsFromActionParameter
                )
            );

    Map<String, HandlerDetails.ParameterDetails> helpVariableParamMap =
        CustomWorkflowUtil.getHelpVariableParameterMap(recordAction);

    actionParameters.putAll(helpVariableParamMap);

    return actionParameters;
  }
}
