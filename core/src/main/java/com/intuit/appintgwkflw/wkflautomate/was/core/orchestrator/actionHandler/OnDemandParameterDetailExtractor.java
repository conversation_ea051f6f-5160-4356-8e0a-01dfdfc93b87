package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.APPROVAL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CREATE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEFAULT_LOCALE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_WAS_LOCALE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SEND_COMPANY_EMAIL;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CloseTaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProjectType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for extracting properties details for on-demand blocks.
 * The need for this new class is because the details needed for the external task (like PROJECT_TYPE, TASK_TYPE etc) are not stored in placeholder_values
 * or user_attributes in the database instead are read from config
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class OnDemandParameterDetailExtractor implements AppConnectParameterDetailExtractor{
    private final ProcessDetailsRepoService processDetailsRepoService;
    private final CustomWorkflowConfig customWorkflowConfig;
    private final MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;
    private final TranslationService translationService;

    /**
     * This method is used to get parameter details for on-demand blocks.
     *
     * @param workerActionRequest
     * @return Optional<Map<String, HandlerDetails.ParameterDetails>>
     */
    public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
            WorkerActionRequest workerActionRequest) {
        String rootProcessInstanceId = workerActionRequest.getInputVariables().get(ROOT_PROCESS_INSTANCE_ID);

        DefinitionDetails definitionDetails =
                processDetailsRepoService.findByProcessIdWithoutDefinitionData(rootProcessInstanceId)
                        .orElseThrow(
                                () -> new WorkflowGeneralException((WorkflowError.DEFINITION_NOT_FOUND)));
        CustomWorkflowType customWorkflowType =
                CustomWorkflowType.getCustomWorkflowForTemplateName(
                        definitionDetails.getTemplateDetails().getTemplateName()
                );
        String entityType = Objects.requireNonNull(RecordType.fromTypeOrValue(workerActionRequest.getInputVariables().get(ENTITY_TYPE))).getRecordType();
        String actionKey = customWorkflowType.getActionKey();
        String actionId = workerActionRequest.getDefinitionKey();
        String subActionId = workerActionRequest.getActivityId();

        Map<String, HandlerDetails.ParameterDetails> actionParameters = multiStepParameterDetailsExtractor.getActionParametersForCurrentActivity(entityType, actionKey, actionId, subActionId);
        List<String> subActions = CustomWorkflowUtil.getOnDemandConfiguredActions(customWorkflowConfig, entityType,actionKey);
        if(subActions.contains(subActionId)){
            JSONObject userVariablesActionData = createActionUserVariables(workerActionRequest, subActionId);
            SingleDefinitionUtil.fillParameterDetails(actionParameters, userVariablesActionData);
        } else {
            throw new WorkflowGeneralException(WorkflowError.ACTION_NOT_FOUND);
        }
        return Optional.of(actionParameters);
    }

    /**
     * This method is used to create action user variables for on-demand blocks.
     *
     * @param workerActionRequest
     * @param subActionId
     * @return JSONObject
     */
    private JSONObject createActionUserVariables(WorkerActionRequest workerActionRequest, String subActionId) {
        Map<String, Object> actionUserVariables = new HashMap<>();
        actionUserVariables.put(ACTION_SELECTED, true);

        Record recordObjForType = customWorkflowConfig.getRecordObjForType(Objects.requireNonNull(RecordType.fromTypeOrValue(workerActionRequest.getInputVariables().get("entityType"))).getRecordType());
        Optional<ActionGroup> actionGroup = recordObjForType.getActionGroups().stream().filter(rec -> rec.getId().equals(APPROVAL)).findFirst();
        Optional<Action> action = actionGroup.flatMap(ag -> ag.getActions().stream().filter(act -> act.getId().equals(subActionId)).findFirst());
        Action reqAction = action.orElseThrow(() -> new WorkflowGeneralException(WorkflowError.ACTION_NOT_FOUND));

        Map<String, Map<String, Object>> parameters = new HashMap<>();
        reqAction.getParameters().forEach(y -> addToParameters(y, parameters, workerActionRequest));

        actionUserVariables.put(ACTION_PARAMETERS, parameters);

        return new JSONObject(actionUserVariables);
    }

    /**
     * This method is used to add parameters to the action user variables.
     *
     * @param params
     * @param parameters
     * @param workerActionRequest
     */
    private void addToParameters(Parameter params, Map<String, Map<String, Object>> parameters, WorkerActionRequest workerActionRequest) {
        if(params.getValueType() == ParameterDetailsValueType.PROCESS_VARIABLE){
            return;
        }
        parameters.put(params.getName(), fetchParameterOrSetDefault(params, workerActionRequest.getInputVariables(), workerActionRequest.getActivityId()));
    }

    /**
     * This function fetches all the required parameters for on demand approval sub actions
     * which are otherwise present in user_variables in case of normal approval workflow
     * TaskName(TaskType, ProjectType) is derived
     * Other parameters are fetched from config/process variables.
     *
     * @param params
     * @param inputVariables
     * @param activityId
     * @return Map<String, Object>
     */

    private Map<String, Object> fetchParameterOrSetDefault(Parameter params,
        Map<String, String> inputVariables, String activityId) {
        WorkflowLogger.logInfo("Fetching on demand parameters for activityId=%s entityType=%s",
            activityId, inputVariables.get(ENTITY_TYPE));

        RecordType recordType = Objects.requireNonNull(
            RecordType.fromTypeOrValue(inputVariables.get(ENTITY_TYPE)));
        String fieldName = params.getName();
        ProjectType project = ProjectType.getProject(recordType.getRecordType(),
            CustomWorkflowType.APPROVAL.getActionKey(), CloseTaskType.TXN_APPROVAL_CHANGED.name());

        switch (fieldName) {
            case Constants.PROJECT_TYPE:
                return Map.of(FIELD_VALUE, List.of(project.name()));
            case Constants.TASK_TYPE:
                return Map.of(FIELD_VALUE, List.of(project.getTaskType().name()));
            default:
                return getDefaultParameterFromConfig(params, recordType, inputVariables);
        }
    }

    /**
     * This function fetches the value for the parameters from process variables(if present) else from template config
     * @param params
     * @param recordType
     * @param inputVariables
     * @return
     */
    private Map<String, Object> getDefaultParameterFromConfig(Parameter params,
        RecordType recordType, Map<String, String> inputVariables) {
        if (inputVariables.containsKey(params.getName())) {
            return Map.of(FIELD_VALUE, List.of(inputVariables.get(params.getName())));
        }
        if (!params.getFieldValues().isEmpty()) {
            String locale = inputVariables.getOrDefault(INTUIT_WAS_LOCALE, DEFAULT_LOCALE);
            List<String> localizedFieldValues = getLocalizedFieldValues(params.getFieldValues(),
                locale, recordType);

            return Map.of(FIELD_VALUE, CustomWorkflowUtil.replaceHelpVariableInFieldValues(params, localizedFieldValues));
        }
        return Map.of(FIELD_VALUE, new ArrayList<>());
    }

    /**
     * This function translates the fieldValues as per the provided Locale or en_US as default
     * @param fieldValues
     * @param locale
     * @param recordType
     * @return
     */
    private List<String> getLocalizedFieldValues(List<String> fieldValues, String locale,
        RecordType recordType) {
        return fieldValues.stream()
            .map(fieldValue -> translationService.getFormattedString(fieldValue, locale,
                recordType.getDisplayValue()))
            .collect(Collectors.toList());
    }
}
