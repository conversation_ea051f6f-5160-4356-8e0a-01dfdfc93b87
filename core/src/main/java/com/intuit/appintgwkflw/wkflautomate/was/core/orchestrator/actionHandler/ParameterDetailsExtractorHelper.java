package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.v4.workflows.Template;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.Optional;
import org.springframework.util.ObjectUtils;

/**
 * Helper for extracting the parameter details
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ParameterDetailsExtractorHelper {
  private final AppConnectParameterDetailsExtractorHelper appConnectParameterDetailsExtractorHelper;
  /**
   * As part of single definition, parameter details are filled using the user placeholder values.
   * Returns the parameter-detail map extracted using corresponding parameter-detail extractor.
   *
   * @param workerActionRequest
   * @return ParameterDetailMap
   */
  public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest, DefinitionDetails definitionDetails) {
    TemplateDetails templateDetails = definitionDetails.getTemplateDetails();
    String handlerDetails =
        workerActionRequest.getInputVariables().get(WorkflowConstants.HANDLER_DETAILS);
    if (ObjectUtils.isEmpty(handlerDetails)) {
      handlerDetails =
          workerActionRequest.getExtensionProperties().get(WorkflowConstants.HANDLER_DETAILS);
    }
    WorkflowVerfiy.verifyNull(handlerDetails, WorkflowError.HANDLER_DETAILS_NOT_FOUND);
    AppConnectParameterDetailExtractor appConnectParameterDetailExtractor =
        appConnectParameterDetailsExtractorHelper.getAppConnectParameterDetailExtractor(
            new Template().name(templateDetails.getTemplateName()),
            definitionDetails,
            handlerDetails,
            workerActionRequest);
    String templateName = templateDetails.getTemplateName();
    String activityId = workerActionRequest.getActivityId();
    String ownerId = String.valueOf(workerActionRequest.getOwnerId());
    String parameterDetailsExtractorInstance =
        appConnectParameterDetailExtractor.getClass().getSimpleName();
    WorkflowLogger.logInfo(
        "AppConnect Parameter Detail extraction for activityId=%s, ownerId=%s, templateName=%s using extractor=%s ",
        activityId, ownerId, templateName, parameterDetailsExtractorInstance);
    return appConnectParameterDetailExtractor.getParameterDetails(workerActionRequest);
  }
}
