package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import java.util.HashMap;
import java.util.Map;
/**
 * Class acts as factory to return task request handlers
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskRequestHandlers {
  private static final Map<TaskType, TaskRequestModifier> TASKREQUEST_HANDLER_MAP = new HashMap<>();
  /**
   * Adds a handler.
   *
   * @param taskType the task type
   * @param handler the handler
   */
  public static void addHandler(TaskType taskType, TaskRequestModifier handler) {
    TASKREQUEST_HANDLER_MAP.put(taskType, handler);
  }
  /**
   * Gets handler.
   *
   * @param taskType
   * @return handler impl
   */
  public static TaskRequestModifier getHandler(TaskType taskType) {
    return TASKREQUEST_HANDLER_MAP.get(taskType);
  }
  /**
   * Contains boolean.
   *
   * @param taskType
   * @return true /false if handler is present or not
   */
  public static boolean contains(TaskType taskType) {
    return TASKREQUEST_HANDLER_MAP.containsKey(taskType);
  }
}
