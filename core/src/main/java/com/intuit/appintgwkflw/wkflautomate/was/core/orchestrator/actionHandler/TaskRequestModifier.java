package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
/**
 * Interface for task request modifiers
 *
 * <AUTHOR>
 */
public interface TaskRequestModifier {
  public TaskType getName();

  public WorkflowTaskRequest getTaskRequest(
      WorkflowTaskRequest taskRequest, WorkerActionRequest workerActionRequest);
}
