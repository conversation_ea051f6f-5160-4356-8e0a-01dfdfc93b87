package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;

import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

/**
 * This class invokes app-connect actions.This is deprecated and should not be used anywhere. To be
 * backward compatible with a known issue it is calling {@link AppConnectWorkflowTaskHandler} to
 * execute action.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Deprecated
public class TestAppconnectWorkflowTaskHandler extends WorkflowTaskHandler {

  private final AppConnectWorkflowTaskHandler appConnectWorkflowTaskHandler;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.DUMMY_ACTION_HANDLER;
  }

  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    return appConnectWorkflowTaskHandler.executeAction(inputRequest);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.TEST, Type.APP_CONNECT_METRIC, exception);
  }
}
