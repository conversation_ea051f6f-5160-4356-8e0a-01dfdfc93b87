package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DELAY;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 *         <p>
 *         This is just a no-op Test handler that can be used in the bpmn for
 *         demo's and quick POC.
 */
@Slf4j
@Component
public class TestHandler extends WorkflowTaskHandler {

	@Override
	public TaskHandlerName getName() {
		return TaskHandlerName.TEST_ACTION_HANDLER;
	}

	@SuppressWarnings("serial")
	@Override
	public <T> Map<String, Object> executeAction(T inputRequest) {

		WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

		boolean enableDelay = Boolean.parseBoolean(Optional.ofNullable(workerActionRequest)
				.map(extensions -> extensions.getExtensionProperties()).orElse(Collections.emptyMap())
				.getOrDefault(DELAY, String.valueOf(Boolean.TRUE.booleanValue())));

		if (enableDelay) {
			Mono.delay(Duration.ofMillis(new Random().nextInt(200) + 150)).doOnSuccess(v -> {
				WorkflowLogger.logInfo("Executed Scheduled tasked succesfully");
			}).block();
		}

		return new HashMap<String, Object>() {
			{
				put(WorkFlowVariables.RESPONSE.getName(), true);
			}
		};
	}

	@Override
	protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
		metricLogger.logErrorMetric(MetricName.TEST, Type.APP_CONNECT_METRIC, exception);
	}
}
