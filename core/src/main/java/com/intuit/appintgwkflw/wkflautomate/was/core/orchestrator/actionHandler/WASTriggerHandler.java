package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.downgrade.Unsubscribe;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <br>
 *     </>
 *     <p>Checks the template for disconnection and triggers it.
 */
@Component
public class WASTriggerHandler extends WorkflowTaskHandler {

  private final TemplateDetailsRepository templateDetailsRepository;
  private final RunTimeService runtimeService;
  private final WASContextHandler contextHandler;
  private final UpdateDefinitionStatusInDataStoreService updateDefinitionStatus;
  private final DefinitionServiceHelper definitionServiceHelper;
    
  private final FeatureManager featureManager;

  public WASTriggerHandler(
          TemplateDetailsRepository templateDetailsRepository,
          RunTimeService runtimeService,
          WASContextHandler contextHandler,
          UpdateDefinitionStatusInDataStoreService updateDefinitionStatus,
          DefinitionServiceHelper definitionServiceHelper,
          @Qualifier(WorkflowConstants.IXP_MANAGER_BEAN) FeatureManager featureManager) {
    this.templateDetailsRepository = templateDetailsRepository;
    this.runtimeService = runtimeService;
    this.contextHandler = contextHandler;
    this.updateDefinitionStatus = updateDefinitionStatus;
    this.definitionServiceHelper = definitionServiceHelper;
    this.featureManager = featureManager;
  }

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_TRIGGER_HANDLER;
  }

  @SuppressWarnings("serial")
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    if (WorkflowTaskUtil.isWorkflowFilterPresent(workerActionRequest)) {
      return ImmutableMap.of(
          new StringBuilder(workerActionRequest.getActivityId())
              .append(UNDERSCORE)
              .append(RESPONSE.getName())
              .toString(),
          Boolean.TRUE.toString());
    }
    //Adding realmId to support authorization ahead.
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    contextHandler.addKey(WASContextEnums.OWNER_ID, realmId);

    String templateParams = workerActionRequest.getInputVariables().get(WorkFlowVariables.PARAMETERS_KEY.getName());
    List<String> templateNames = new ArrayList<>();
    Map<String, Unsubscribe.Template> nameTemplateMap = new HashMap<>();

    if (Objects.nonNull(templateParams)) {
      Unsubscribe unsubscribe = ObjectConverter.fromJson(templateParams, Unsubscribe.class);
      if (Objects.nonNull(unsubscribe)
          && Objects.nonNull(unsubscribe.getDowngrade())
          && Objects.nonNull(unsubscribe.getDowngrade().getTemplates())) {
        unsubscribe.getDowngrade().getTemplates().stream()
            .forEach(
                template -> {
                  String templateName = template.getEntityType().concat(template.getWorkflow());
                  templateNames.add(templateName);
                  nameTemplateMap.putIfAbsent(templateName, template);
                });
      }
    }

    Optional<List<TemplateDetails>> templateDetails =
        templateDetailsRepository.findAllValidEligibleDefinitions(templateNames,
                Long.parseLong(realmId));

    if (templateDetails.isPresent()) {
      templateDetails.get().stream()
          .forEach(
              tDetails -> {
                runtimeService.processTriggerMessageV2(
                    TriggerUtil.prepareTriggerPayloadForDisconnection(tDetails, nameTemplateMap, realmId));

                WorkflowLogger.info(
                    () ->
                        WorkflowLoggerRequest.builder()
                            .message("Triggered template with name=%s", tDetails.getTemplateName())
                            .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                            .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS)
                            .className(this.getClass().getName()));
              });

      // Mark the definition of this realm for downgrade
      markDefinitionForDowngrade(workerActionRequest);
    }  else {
      WorkflowLogger.info(
              () ->
                      WorkflowLoggerRequest.builder()
                              .message("No enabled templates found to trigger")
                              .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                              .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS)
                              .className(this.getClass().getName()));
    }

    return ImmutableMap.of(
        new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString(),
        Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.TRIGGER_HANDLER, Type.WAS_METRIC, exception);
  }

  /**
   * Marking the definition status as MARKED_FOR_DOWNGRADE
   *
   * @param ownerId
   */
  private void markDefinitionForDowngrade(WorkerActionRequest workerActionRequest) {

    // TODO : QBOES-23573 : Cleanup of this check after 100% rollout of selective downgrade workflow
    if (!WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager)) {

      String ownerId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

      final List<DefinitionDetails> definitionLists =
          definitionServiceHelper.getAllDefinitionList(Long.parseLong(ownerId));
      final DefinitionInstance definitionInstance = new DefinitionInstance();
      definitionInstance.setDefinitionDetailsList(definitionLists);
      updateDefinitionStatus.updateInternalStatusForDowngrade(definitionInstance, ownerId);
    }
    else {
      List<DefinitionPendingDeletion> definitionPendingDeletion =
          WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest);

      definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
          WorkflowVariabilityUtil.getDefinitionList(definitionPendingDeletion),
          InternalStatus.MARKED_FOR_DOWNGRADE
      );
    }
  }
}
