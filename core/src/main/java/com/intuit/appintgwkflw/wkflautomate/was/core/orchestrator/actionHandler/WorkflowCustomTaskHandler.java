package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_COMPLETE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_CREATED;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowCustomTaskHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowExternalTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskMode;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

/**
 * WorkflowCustomTaskHandler handles the create call of Workflow Task.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WorkflowCustomTaskHandler extends WorkflowTaskHandler {

  private WorkflowCustomTaskHelper customTaskHelper;

  private WorkflowExternalTaskManager taskManager;
  private WorkflowSystemTaskHandler workflowSystemTaskHandler;
  private FeatureFlagManager featureFlagManager;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_WORKFLOW_CUSTOM_TASK_HANDLER;
  }

  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    WorkflowLogger.logInfo(
            "WorkflowCustomTaskHandler execution externalTaskId=%s extensions=%s",
            workerActionRequest.getTaskId(),
            workerActionRequest.getExtensionProperties());

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder = customTaskHelper
        .prepareWorkflowTaskRequest(
            workerActionRequest);
    taskRequestBuilder.command(TaskCommand.CREATE).status(TASK_STATUS_CREATED);
    return processCustomTask(workerActionRequest, taskRequestBuilder);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.WORKFLOW_TASK_EXECUTION, Type.EXTERNAL_TASK_METRIC, exception);
  }

  public boolean isCustomTaskParameterExtractionEnabled(
      FeatureFlagManager featureFlagManager, String realmId, String workflowName) {
    return featureFlagManager.getBoolean(
        WorkflowConstants.CUSTOM_TASK_PLACEHOLDER_EXTRACTION_ENABLED,
        false,
        workflowName,
        NumberUtils.createLong(realmId));
  }

  /**
   * Processes CustomTasks and responds backs with responseMap to either extendLock or mark task
   * Complete.
   *
   * @param workerActionRequest - Worker Request POJO.
   * @param taskRequestBuilder  - TaskRequest being prepared.
   * @return responseMap        - Response Map providing status to Worker.
   */
  private Map<String, Object> processCustomTask(WorkerActionRequest workerActionRequest,
      WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder) {
    String activityType = WorkflowTaskUtil
        .getExtenstionVariable(workerActionRequest,
            ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE);
    if (TaskType.HUMAN_TASK.name().equalsIgnoreCase(activityType)) {
      if (TaskMode.SYNC.equals(WorkflowTaskUtil.fetchTxnMode(workerActionRequest))) {
        return handleSyncHumanTask(taskRequestBuilder);
      } else {
        return handleAsyncModeHumanTask(workerActionRequest, taskRequestBuilder);
      }
    }
    else if (TaskType.SYSTEM_TASK.name().equalsIgnoreCase(activityType)){
      return workflowSystemTaskHandler.execute(taskRequestBuilder, workerActionRequest);
    }
    else{
      return handleOtherSystemTasks(workerActionRequest, taskRequestBuilder);
    }
  }

  /**
   * Process Other Custom SystemTasks such as NotificationTask etc.
   *
   * @param workerActionRequest - Worker Request POJO.
   * @param taskRequestBuilder  - TaskRequest being prepared.
   * @return responseMap        - Response Map providing status to Worker.
   */
  private Map<String, Object> handleOtherSystemTasks(WorkerActionRequest workerActionRequest,
      WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder) {
    Map<String, Object> responseMap = new HashMap<>();
    WorkflowTaskRequest createTaskRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .build();
    if (isCustomTaskParameterExtractionEnabled(
        featureFlagManager,
        workerActionRequest.getInputVariables().get(WorkflowConstants.INTUIT_REALMID),
        workerActionRequest.getDefinitionKey())) {
      // used to modify the task attributes of taskRequest
      createTaskRequest =
          TaskRequestHandlers.getHandler(createTaskRequest.getTaskType())
              .getTaskRequest(createTaskRequest, workerActionRequest);
    }
    WorkflowTaskResponse createReponse = taskManager.execute(createTaskRequest);
    addResponseMap(responseMap, createReponse);

    taskRequestBuilder.txnId(createReponse.getTxnId());
    WorkflowTaskRequest completeTaskRequest = taskRequestBuilder.command(TaskCommand.COMPLETE)
        .status(TASK_STATUS_COMPLETE).skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false).build();
    WorkflowTaskResponse completeReponse = taskManager.execute(completeTaskRequest);
    addResponseMap(responseMap, completeReponse);

    responseMap.put(new StringBuilder(workerActionRequest.getActivityId())
            .append(WorkflowConstants.UNDERSCORE)
            .append(WorkFlowVariables.RESPONSE.getName())
            .toString(),
        Boolean.TRUE);
    return responseMap;
  }

  /**
   * FOR VEP use cases, 1 ExternalTask is mapped to 1 Transaction. Create Command is executed from
   * fetchNLock call. Complete and Update Command is executed from Event Consumption.
   *
   * @param taskRequestBuilder - TaskRequest being prepared.
   * @return responseMap        - Response Map providing status to Worker.
   */
  private Map<String, Object> handleSyncHumanTask(
      WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder) {

    Map<String, Object> responseMap = new HashMap<>();
    WorkflowTaskRequest taskRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(true).publishWorkflowStateTransitionEvent(true)
        .build();
    WorkflowTaskResponse response = taskManager.execute(taskRequest);
    addResponseMap(responseMap, response);
    responseMap.put(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    return responseMap;
  }

  /**
   * To handle Async Mode (QBO use-case) of Transactions. When txnId is not available,
   * skipCallback=false,	create call will make status=created for act_progress and txn_detail both.
   * skipCallback=true, complete call will make status=created for act_progress. Makes no call to
   * Downstream.
   * <p>
   * When txnId is available, skipCallback=true,	create call will make status=created for
   * act_progress. Makes no call to Downstream. skipCallback=false, complete call will make
   * status=complete for act_progress and txn_detail both.
   *
   * @param workerActionRequest - Worker Request POJO.
   * @param taskRequestBuilder  - TaskRequest being prepared.
   * @return responseMap         - Response Map providing status to Worker.
   */
  private Map<String, Object> handleAsyncModeHumanTask(WorkerActionRequest workerActionRequest,
      WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder) {

    Map<String, Object> responseMap = new HashMap<>();
    String txnId = WorkflowTaskUtil.fetchTransactionId(workerActionRequest);

    if (StringUtils.isEmpty(txnId)) {
      WorkflowTaskRequest createTaskRequest = taskRequestBuilder.skipCallback(false)
          .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(true)
          .build();
      WorkflowTaskResponse createReponse = taskManager.execute(createTaskRequest);
      addResponseMap(responseMap, createReponse);

      WorkflowTaskRequest completeTaskRequest = taskRequestBuilder.skipCallback(true)
          .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
          .command(TaskCommand.COMPLETE).status(TASK_STATUS_COMPLETE).build();
      WorkflowTaskResponse completeReponse = taskManager.execute(completeTaskRequest);
      addResponseMap(responseMap, completeReponse);
    } else {
      //set txnId.
      taskRequestBuilder.txnId(txnId);
      WorkflowTaskRequest createTaskRequest = taskRequestBuilder.skipCallback(true)
          .publishExternalTaskEvent(false)
          .publishWorkflowStateTransitionEvent(false).build();
      WorkflowTaskResponse createReponse = taskManager.execute(createTaskRequest);
      addResponseMap(responseMap, createReponse);

      WorkflowTaskRequest completeTaskRequest = taskRequestBuilder.skipCallback(false)
          .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
          .command(TaskCommand.COMPLETE).status(TASK_STATUS_COMPLETE).build();
      WorkflowTaskResponse completeReponse = taskManager.execute(completeTaskRequest);
      addResponseMap(responseMap, completeReponse);

    }
    responseMap.put(new StringBuilder(workerActionRequest.getActivityId())
            .append(WorkflowConstants.UNDERSCORE)
            .append(WorkFlowVariables.RESPONSE.getName())
            .toString(),
        Boolean.TRUE);
    return responseMap;
  }


  /**
   * Sets the value in responseMap received from TaskAdaptor Response.
   *
   * @param responseMap  - Worker Response Map having fields to set in Camunda.
   * @param taskResponse - TaskCommand response.
   */
  public void addResponseMap(Map<String, Object> responseMap, WorkflowTaskResponse taskResponse) {
    if (!MapUtils.isEmpty(taskResponse.getResponseMap())) {
      responseMap.putAll(taskResponse.getResponseMap());
    }
  }

}