package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowExternalTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Handles flow for TASK_TYPE = SYSTEM_TASK
 */
@Component
@AllArgsConstructor
public class WorkflowSystemTaskHandler {

  private WorkflowExternalTaskManager taskManager;

  private PublishEventHandler publishEventHandler;

  private WorkflowTaskConfig workflowTaskConfig;

  /**
   *
   * @param taskRequestBuilder WorkflowTaskRequest builder
   * @param workerActionRequest workerActionRequest of externalTask
   * @return response map
   */
  public Map<String, Object> execute(WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder,
      WorkerActionRequest workerActionRequest) {

    if(isSystemTaskDisabled()){
      return executePublishHandler(workerActionRequest);
    }

    WorkflowTaskRequest taskRequest = taskRequestBuilder.skipCallback(false)
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(true)
        .invokeDownstreamOnRetry(true).build();
    Map<String, Object> responseMap = new HashMap<>();
    WorkflowTaskResponse response = taskManager.execute(taskRequest);
    if (!MapUtils.isEmpty(response.getResponseMap())) {
      responseMap.putAll(response.getResponseMap());
    }
    responseMap.put(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    return responseMap;
  }

  /**
   * Checks if system task is disabled for this flow.
   * @return boolean
   */
  private boolean isSystemTaskDisabled(){
    return Optional.ofNullable(workflowTaskConfig.getTaskConfig().get(TaskType.SYSTEM_TASK))
        .map(WorkflowTaskConfigDetails::isDisable).orElse(false);
  }

  /**
   * Execute PublishEventHandler when SYSTEM_TASK is disabled
   * @param workerActionRequest request
   * @return response map
   */
  private Map<String, Object> executePublishHandler(WorkerActionRequest workerActionRequest){
    WorkflowLogger.logInfo("type=SYSTEM_TASK is disabled. Redirecting flow to PublishEventHandler");
    return publishEventHandler.executeAction(workerActionRequest);
  }
}
