package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.HandlerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.annotations.CreateCalledProcess;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.variable.VariableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/** <AUTHOR> */
@NoArgsConstructor
@AllArgsConstructor
public abstract class WorkflowTaskHandler {

  @Autowired protected MetricLogger metricLogger;

  @Autowired private IncidentTaskManager incidentTaskManager;

  @Autowired private WorkerRetryHelper workerRetryHelper;

  @Autowired private HandlerConfig handlerConfig;

  /**
   * name by which implementation will be recognized
   *
   * @return bean Name.
   */
  public abstract TaskHandlerName getName();

  /**
   * input process variables.
   *
   * @param inputRequest worker request
   * @return return response map
   */
  protected abstract <T> Map<String, Object> executeAction(T inputRequest);

  /**
   * Handles the error, if the task is fatal set success as false, else throw exception
   *
   * @param inputRequest
   * @param <T>
   * @return
   */
  @CreateCalledProcess
  public <T> Map<String, Object> execute(final T inputRequest) {
    try {
      return executeAction(inputRequest);
    } catch (final Exception exception) {
      final WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
      logErrorMetric(exception, workerActionRequest);

      final Optional<HandlerDetails.TaskDetails> taskDetailsOptionalFromVariables =
          SchemaDecoder.getTaskDetails(workerActionRequest.getInputVariables());

      final Optional<HandlerDetails.TaskDetails> taskDetailsOptional =
          taskDetailsOptionalFromVariables.isPresent() ? taskDetailsOptionalFromVariables
              : SchemaDecoder.getTaskDetails(workerActionRequest.getExtensionProperties());

      HandlerDetails.TaskDetails taskDetails = getTaskDetails(exception, taskDetailsOptional);

      ExternalTaskRetryConfig externalTaskRetryConfig =
              workerRetryHelper.getRetryConfig(
                      workerActionRequest.getDefinitionKey(),
                      workerActionRequest.getActivityId(),
                      workerActionRequest.getInputVariables().get(INTUIT_REALMID)
              );

      WorkflowLogger.logInfo(
              "step=fetchExternalTaskRetryConfig " +
                      "externalRetryConfigWorkflowName=%s externalRetryConfigExternalTaskName=%s " +
                      "workflowName=%s externalTaskName=%s", 
              Objects.nonNull(externalTaskRetryConfig) ? externalTaskRetryConfig.getWorkflowName() : null,
              Objects.nonNull(externalTaskRetryConfig) ? externalTaskRetryConfig.getExternalTaskName() : null,
              workerActionRequest.getDefinitionKey(),
              workerActionRequest.getActivityId()
      );

      // Exception is thrown in following cases :
      // 1. Task is set as fatal
      // 2. External task is retriable from config
      // Otherwise swallow and log and mark as error
      if (BooleanUtils.isTrue(taskDetails.getFatal())
          || checkIfExternalTaskRetriableFromRetryConfig(
              exception, workerActionRequest, externalTaskRetryConfig)) {
        throw exception;
      }

      // In case fatal is false, Failed Command is to execute to update DB and send TaskTransition event.
      incidentTaskManager.executeFailedCommand(workerActionRequest);

      WorkflowLogger.info(
              () ->
                      WorkflowLoggerRequest.builder()
                              .message(
                                      "External task execution success=false, taskId=%s, activityId=%s",
                                      workerActionRequest.getTaskId(),
                                      workerActionRequest.getActivityId()
                              )
                              .className(this.getClass().getSimpleName())
      );

      // set success as false
      return new HashMap<>(
          ImmutableMap.of(
              new StringBuilder(workerActionRequest.getActivityId())
                  .append(UNDERSCORE)
                  .append(RESPONSE.getName())
                  .toString(),
              Boolean.FALSE.toString())
      );
    }
  }

  private boolean checkIfExternalTaskRetriableFromRetryConfig(
      Exception exception,
      WorkerActionRequest workerActionRequest,
      ExternalTaskRetryConfig externalTaskRetryConfig) {

    // External task is retriable when any the following cases is true and the exception is WorkflowRetriableException :
    // 1. External retry config is found, & task is not set as fatal on retry exhaust
    // 2. External retry config is found, & this is the first fetch and lock, ie, retries are null
    // 3. External retry config is found, & task is set as fatal on retry exhaust, & this F&L is
    // not the last retry

    return Objects.nonNull(externalTaskRetryConfig)
        && (externalTaskRetryConfig.isFatalOnRetryExhaust()
            || Objects.isNull(workerActionRequest.getRetries())
            || workerActionRequest.getRetries() > 1)
        && exception instanceof WorkflowRetriableException;
  }

  /**
   * Gets TaskDetails provided in handlerDetails and throws exception if not present.
   *
   * @param exception
   * @param taskDetailsOptional
   * @return
   */
  private HandlerDetails.TaskDetails getTaskDetails(final Exception exception,
      final Optional<HandlerDetails.TaskDetails> taskDetailsOptional) {
    return taskDetailsOptional
        .orElseThrow(() -> {
          /**
           * Re-throw Non-retriable exception.
           * Non-Retriable exception should be thrown to mark incident directly.
           * Marking fatal shouldn't be required for re-throwing exception in case of Non-retriable exception.
           * Root reason helps to execute FailedCommand and handling of transaction DB-table.
           */
          if (exception instanceof WorkflowNonRetriableException) {
            throw (WorkflowNonRetriableException) exception;
          }

          WorkflowError workflowError = WorkflowError.TASK_DETAILS_NOT_FOUND;
          if (exception instanceof WorkflowGeneralException
              && null != ((WorkflowGeneralException) exception).getWorkflowError()) {

            workflowError = ((WorkflowGeneralException) exception).getWorkflowError();
          }
          throw new WorkflowGeneralException(workflowError);
        });
  }

  /**
   * Fetches additional parameters from the handler configuration required for making calls to AppConnect or other task handlers.
   *
   * @param recordType The type of record for which parameters are being fetched.
   * @param workerActionRequest The request containing details about the worker action.
   * @return A list of WorkflowTaskHandlerInput objects representing the task parameters.
   */
  protected List<WorkflowTaskHandlerInput> getTaskParametersFromConfig(@NonNull RecordType recordType, @NonNull WorkerActionRequest workerActionRequest) {
      return Optional.ofNullable(handlerConfig.getHandlers())
              .map(handlers -> handlers.get(workerActionRequest.getHandlerId()))
              .map(entities -> entities.get(recordType.getRecordType()))
              .map(entity -> entity.get(WorkflowConstants.PARAMETERS))
              .map(parameters -> parameters.stream()
                      .map(parameter -> createTaskHandlerInput(parameter, workerActionRequest.getVariableMap()))
                      .collect(Collectors.toList()))
              .orElse(Collections.emptyList());
  }

  protected Optional<RecordType> extractRecordType(DefinitionDetails definitionDetails, WorkerActionRequest workerActionRequest) {
    return Optional.ofNullable(definitionDetails.getRecordType())
            .or(() -> {
              if ((Boolean.TRUE.toString()).equalsIgnoreCase(workerActionRequest
                      .getInputVariables().getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, Boolean.FALSE.toString())) &&
                      Objects.nonNull(workerActionRequest.getInputVariables().get(WorkflowConstants.ENTITY_TYPE))) {
                String entityType = workerActionRequest.getInputVariables().get(WorkflowConstants.ENTITY_TYPE);
                RecordType recordType = RecordType.fromType(entityType);
                return Optional.ofNullable(recordType)
                        .or(() -> {
                          WorkflowLogger.logWarn("EntityType not found in workerActionRequest and in definition %s", entityType);
                          return Optional.empty();
                        });
              }
              return Optional.empty();
            });
  }

  /**
   * Prepares input for the given parameter by fetching its value from either the configuration or process variables.
   *
   * @param parameter The parameter for which input is being prepared.
   * @param variableMap The map containing process variables.
   * @return A WorkflowTaskHandlerInput object containing the parameter's name and value.
   */
  protected WorkflowTaskHandlerInput createTaskHandlerInput(Parameter parameter, VariableMap variableMap) {
    String fieldName = StringUtils.defaultIfBlank(parameter.getHandlerFieldName(), parameter.getName());
    String fieldValue = CollectionUtils.isEmpty(parameter.getFieldValues())
            ? String.valueOf(variableMap.get(parameter.getName()))
            : parameter.getFieldValues().stream().findFirst().orElse(null);
    return new WorkflowTaskHandlerInput(fieldName, fieldValue);
  }

  protected abstract void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest);
}
