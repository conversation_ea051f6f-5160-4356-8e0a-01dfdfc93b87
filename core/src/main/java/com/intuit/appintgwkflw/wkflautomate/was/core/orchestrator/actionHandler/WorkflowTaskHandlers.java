package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;

import java.util.EnumMap;
import java.util.Map;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * The type Workflow task handlers.
 *
 * <AUTHOR>  <p>Class acts as factory to return Task Handler
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowTaskHandlers {

  private static final Map<TaskHandlerName, WorkflowTaskHandler> TASK_HANDLER_MAP = new EnumMap<>(
      TaskHandlerName.class);

  /**
   * Adds a handler.
   *
   * @param handlerName the handler name
   * @param taskHandler the task handler
   */
  public static void addHandler(TaskHandlerName handlerName, WorkflowTaskHandler taskHandler) {

    TASK_HANDLER_MAP.put(handlerName, taskHandler);
  }

  /**
   * Gets handler.
   *
   * @param handlerName input handler name
   * @return action handler impl
   */
  public static WorkflowTaskHandler getHandler(TaskHandlerName handlerName) {

    return TASK_HANDLER_MAP.get(handlerName);
  }

  /**
   * Contains boolean.
   *
   * @param handlerName input handler name
   * @return true /false if handler is present or not
   */
  public static boolean contains(TaskHandlerName handlerName) {

    return TASK_HANDLER_MAP.containsKey(handlerName);
  }
}
