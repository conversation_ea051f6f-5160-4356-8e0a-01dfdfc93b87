package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COMMA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.variability.WorkflowFeatureMapping;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.Subscription;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.SubscriptionStatus;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.VariabilityEngineService;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityConfigHelper;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityRecord;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.WorkflowVariabilityRecordConfig;
import com.intuit.v4.WorkflowsBulkAction.WorkflowFilter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.variable.VariableMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class WorkflowVariabilityBasedFilteringHandler extends WorkflowTaskHandler {

  private final DefinitionServiceHelper definitionServiceHelper;
  private final WorkflowVariabilityConfigHelper workflowsVariabilityConfigMapper;
  private final AuthDetailsService authDetailsService;
  private final FeatureManager featureManager;

  public WorkflowVariabilityBasedFilteringHandler(
          DefinitionServiceHelper definitionServiceHelper,
          WorkflowVariabilityConfigHelper workflowsVariabilityConfigMapper,
          AuthDetailsService authDetailsService,
          @Qualifier(WorkflowConstants.IXP_MANAGER_BEAN) FeatureManager featureManager
  ) {
    this.definitionServiceHelper = definitionServiceHelper;
    this.workflowsVariabilityConfigMapper = workflowsVariabilityConfigMapper;
    this.authDetailsService = authDetailsService;
    this.featureManager = featureManager;
  }

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WORKFLOW_VARIABILITY_BASED_FILTER_HANDLER;
  }

  /**
   * utilise subscriptions process variable to validate against the offering config and determine if
   * given realm has workflows that should not be deleted during downgrade/unsubscribe events
   *
   * @param inputRequest worker request
   * @return
   * @param <T>
   */
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String activityId = workerActionRequest.getActivityId();

    final Long ownerId =
        Long.parseLong(
            workerActionRequest.getInputVariables().get(WorkflowConstants.INTUIT_REALMID));

    /**
     * to check if appconnect subscription is found or not. There can be a case in which user does
     * not have any appconnect subscription in that case nothing needs to be done.
     */
    boolean authDetailsAvailable = isAuthDetailsPresent(Long.toString(ownerId));

    Map<String, Object> responseMap = new HashMap<>();
    if (!authDetailsAvailable) {
      responseMap.put(
          new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
          Boolean.TRUE.toString());
      responseMap.put(WorkflowConstants.AUTH_DETAILS_AVAILABLE, Boolean.FALSE);
      return responseMap;
    }

    // filtering deleted definitions from the list of definitions to be processed
    List<DefinitionDetails> realmDefinitions = definitionServiceHelper.getAllDefinitionListForDeletion(ownerId);
    List<DefinitionPendingDeletion> definitionListPendingToBeDeleted =
            getDefinitionsToBeDeleted(realmDefinitions, workerActionRequest);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Filtered list of workflows definitions=%s for realm=%s",
                    definitionListPendingToBeDeleted.stream()
                        .map(DefinitionPendingDeletion::getId)
                        .collect(Collectors.joining(COMMA)),
                    ownerId)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .className(this.getClass().getName()));

    // add and return more fields in this response map to add additional process vars to
    // determine further steps to be processed
    responseMap.put(
        new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
        Boolean.TRUE.toString());
    responseMap.put(WorkflowConstants.AUTH_DETAILS_AVAILABLE, Boolean.TRUE);
    responseMap.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        definitionListPendingToBeDeleted
    );
    return responseMap;
  }

  /**
   * For each subscription, check the workflowVariabilityConfig to retrieve all workflowTypes that
   * need to be retained. The definitions matching these workflowTypes will be filtered out from the
   * complete realmDefinitions and a subset of definitionIds to be deleted is returned
   *
   * @param realmDefinitions
   * @param workerActionRequest
   * @return
   */
  private List<DefinitionPendingDeletion> getDefinitionsToBeDeleted(
      List<DefinitionDetails> realmDefinitions, WorkerActionRequest workerActionRequest) {

    String subscriptionsProcessVariable =
        workerActionRequest.getInputVariables().get(WorkflowConstants.SUBSCRIPTIONS);

    VariableMap variableMap = workerActionRequest.getVariableMap();
    List<HashMap<String, String>> workflowFilters =
        (List<HashMap<String, String>>) variableMap.get(WorkflowConstants.WORKFLOW_FILTER);
    Set<DefinitionDetails> filteredRealmDefinitions;
    if (WorkflowTaskUtil.isWorkflowFilterPresent(workerActionRequest)) {
      ObjectMapper mapper = new ObjectMapper();
      filteredRealmDefinitions =
          realmDefinitions.stream()
              .filter(
                  realmDef -> {
                    List<WorkflowFilter> filters =
                        workflowFilters.stream()
                            .map(filter -> mapper.convertValue(filter, WorkflowFilter.class))
                            .filter(
                                filter -> {
                                  String templateName =
                                      CustomWorkflowType.getTemplateName(filter.getWorkflowType());
                                  return StringUtils.equalsIgnoreCase(
                                          realmDef.getTemplateDetails().getTemplateName(),
                                          templateName)
                                      && StringUtils.equalsIgnoreCase(
                                          filter.getRecordType(),
                                          realmDef.getRecordType().toString());
                                })
                            .collect(Collectors.toList());
                    return !filters.isEmpty();
                  })
              .collect(Collectors.toCollection(HashSet::new));
      return getAllDefinitionDetails(new ArrayList<>(filteredRealmDefinitions));
    }
    // get list of subscriptions for realm
    List<Subscription> subscriptions =
        ObjectUtils.isNotEmpty(subscriptionsProcessVariable)
            ? ObjectConverter.fromJson(
                subscriptionsProcessVariable, new TypeReference<List<Subscription>>() {})
            : Collections.emptyList();

    if (!WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager)
        || ObjectUtils.isEmpty(realmDefinitions)) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No subscription list or definition list available for downgrade")
                  .className(this.getClass().getName())
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));

      /**
       * Adding for backward of templates as the existing downgrade flow will not have a list of
       * subscriptions resulting in incomplete workflows cleanup TODO: cleanup post 100% rollout of
       * new downgrade flow https://jira.intuit.com/browse/QBOES-22936
       */
      return getAllDefinitionDetails(realmDefinitions);
    }

    Set<String> subscriptionSupportedWorkflows = new HashSet<>();
    Set<String> subscriptionUnsupportedWorkflows = new HashSet<>();

    /**
     * based on the provided list of active offerings in subscriptions, get the list of entities and
     * its respective workflows that need to be retained
     */
    subscriptions.stream()
        .filter(subscription -> SubscriptionStatus.ACTIVE.equals(subscription.getStatus()))
        .forEach(
            subscription -> {
              String offering = subscription.getOfferingType();
              Optional<WorkflowVariabilityRecordConfig> offeringWorkflowVariabilityRecords =
                  Optional.ofNullable(
                      workflowsVariabilityConfigMapper.getOfferingWorkflowVariabilityRecords(
                          offering));

              offeringWorkflowVariabilityRecords.ifPresent(
                  records -> {
                    Optional.ofNullable(records.getInclusion())
                        .filter(list -> !list.isEmpty())
                        .ifPresent(
                            inclusion ->
                                inclusion.forEach(
                                    workflowVariabilityRecord -> {
                                      String entityType = workflowVariabilityRecord.getEntityType();
                                      workflowVariabilityRecord
                                          .getWorkflowTypes()
                                          .forEach(
                                              workflowType ->
                                                  subscriptionSupportedWorkflows.add(
                                                      entityType + UNDERSCORE + workflowType));
                                    }));

                    Optional.ofNullable(records.getExclusion())
                        .filter(list -> !list.isEmpty())
                        .ifPresent(
                            exclusion ->
                                exclusion.forEach(
                                    workflowVariabilityRecord -> {
                                      String entityType = workflowVariabilityRecord.getEntityType();
                                      workflowVariabilityRecord
                                          .getWorkflowTypes()
                                          .forEach(
                                              workflowType ->
                                                  subscriptionUnsupportedWorkflows.add(
                                                      entityType + UNDERSCORE + workflowType));
                                    }));
                  });
            });
    // Delete all definitions if subscription doesn't support any workflow
    if (ObjectUtils.isEmpty(subscriptionSupportedWorkflows)
        && ObjectUtils.isEmpty(subscriptionUnsupportedWorkflows)) {
      return getAllDefinitionDetails(realmDefinitions);
    }

    /** filter out the definitions to be deleted based on the workflowTypes populated above */
    Predicate<DefinitionDetails> isDefEligibleForDeletion =
        definition -> {
          String workflowType =
              new StringBuilder(definition.getRecordType().getRecordType())
                  .append(UNDERSCORE)
                  .append(definition.getTemplateDetails().getTemplateName())
                  .toString();
          return !(subscriptionSupportedWorkflows.contains(workflowType))
              || subscriptionUnsupportedWorkflows.contains(workflowType);
        };

    return realmDefinitions.stream()
        .filter(isDefEligibleForDeletion)
        .map(DefinitionPendingDeletion::buildFromDefinitionDetails)
        .collect(Collectors.toList());
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(
        MetricName.WORKFLOW_VARIABILITY_BASED_FILTERING, Type.EXTERNAL_TASK_METRIC, exception);
  }

  private List<DefinitionPendingDeletion> getAllDefinitionDetails(
      List<DefinitionDetails> definitions) {
    return definitions.stream()
        .map(DefinitionPendingDeletion::buildFromDefinitionDetails)
        .collect(Collectors.toList());
  }

  private boolean isAuthDetailsPresent(String ownerId) {
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmIdSafe(ownerId);
    if (isNull(authDetails) || isEmpty(authDetails.getSubscriptionId())) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Subscription downgrade: No auth details found for ownerId=%s", ownerId)
                  .className(this.getClass().getName())
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
      return false;
    }
    return true;
  }
}
