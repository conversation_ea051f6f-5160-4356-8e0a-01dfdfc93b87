package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * <p> This is annotation to add a called process during fetch and lock if not present in the DB.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME) //Spring AOP would not be able to see the annotation if not runtime
public @interface CreateCalledProcess {
}
