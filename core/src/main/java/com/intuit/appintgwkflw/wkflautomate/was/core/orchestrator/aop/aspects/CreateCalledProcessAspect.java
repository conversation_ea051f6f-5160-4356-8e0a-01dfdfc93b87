package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.aspects;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CALLED_PROCESS_CREATION_RESULT;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.annotations.CreateCalledProcess;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p> This is a aspect to capture {@link CreateCalledProcess} and
 * add a called process during fetch and lock if not present in the DB.
 */
@Aspect
@Component
@AllArgsConstructor
public final class CreateCalledProcessAspect {

  private final ProcessDetailsRepository processDetailsRepository;

  private final DefinitionDetailsRepository definitionDetailsRepository;

  private final V3RunTimeHelper runTimeHelper;

  private final DomainEventService domainEventService;

  private final MetricLogger metricLogger;

  /**
   * This advice is to save a called process detail in the DB before the join point if not present.
   * After the join point execution, this advice adds a confirmation for the called process creation after the join point.
   * @param joinPoint - Join Point where the advice is being executed
   * @return - Value returned from the join point.
   */

  @Around("@annotation(createCalledProcess)")
  public Object saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(
      final ProceedingJoinPoint joinPoint, final CreateCalledProcess createCalledProcess
  ) throws Throwable {

    WorkerActionRequest workerActionRequest = (WorkerActionRequest) joinPoint.getArgs()[0];

    String rootProcessInstanceId = workerActionRequest.getInputVariables().get(ROOT_PROCESS_INSTANCE_ID);

    workerActionRequest.setRootProcessInstanceId(rootProcessInstanceId);
    workerActionRequest.setCalledProcess(isCalledProcess(workerActionRequest));

    String calledProcessCreatedResultVariableName = getCalledProcessCreatedResultVariableName(
        workerActionRequest, CALLED_PROCESS_CREATION_RESULT
    );

    boolean isCalledProcessCreated =
        Boolean.parseBoolean(
            workerActionRequest.getInputVariables().get(calledProcessCreatedResultVariableName));

    boolean isCalledProcessPersisted = false;

    try {
      if(workerActionRequest.isCalledProcess() && !isCalledProcessCreated) {

        ProcessDetails calledProcess =
            processDetailsRepository.findByProcessId(workerActionRequest.getProcessInstanceId()).orElse(null);

        if(Objects.isNull(calledProcess)) {
          WorkflowLogger.logInfo(
              "step=prePersistCalledProcess; calledProcessId=%s, rootProcessId=%s",
              workerActionRequest.getProcessInstanceId(),
              rootProcessInstanceId
          );

          ProcessDetails rootProcessDetails =
              processDetailsRepository.findByProcessId(rootProcessInstanceId)
                  .orElseThrow(
                      () -> new WorkflowGeneralException(WorkflowError.PROCESS_NOT_FOUND)
                  );

          // Would throw an error if definition directly uploaded to camunda instead through WAS
          DefinitionDetails calledDefinitionDetails =
              definitionDetailsRepository.findByDefinitionId(workerActionRequest.getProcessDefinitionId()).orElseThrow(
                  () -> new WorkflowGeneralException(WorkflowError.CALLED_DEFINITION_NOT_FOUND)
              );

          endExistingActiveSiblings(workerActionRequest);

          // For on-demand approvals, the record type will not be set from the definition details as it is entity agnostic
          // So the record type needs to be populated in case of on-demand approval
          if((Boolean.TRUE.toString()).equalsIgnoreCase(
                  workerActionRequest.getInputVariables()
                          .getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, Boolean.FALSE.toString()))){
            calledDefinitionDetails.setRecordType(RecordType
                    .fromTypeOrValue(workerActionRequest.getInputVariables().get(WorkflowConstants.ENTITY_TYPE)));
          }
          calledProcess = runTimeHelper.saveProcessDetailsInstance(
              rootProcessDetails.getRecordId(),
              rootProcessDetails.getOwnerId(),
              workerActionRequest.getProcessInstanceId(),
              ProcessStatus.ACTIVE,
              calledDefinitionDetails,
              rootProcessDetails.getProcessId(),
              Collections.emptyMap()
          );

          isCalledProcessPersisted = true;

          WorkflowLogger.logInfo(
              "step=postPersistCalledProcessToDB, calledProcessId=%s, rootProcessId=%s",
              calledProcess.getProcessId(),
              rootProcessDetails.getProcessId()
          );
        }
      }
    }
    catch (WorkflowGeneralException workflowGeneralException) {
      metricLogger.logErrorMetric(MetricName.CREATE_CALLED_PROCESS, Type.APPLICATION_METRIC, workflowGeneralException);
      throw workflowGeneralException;
    }
    catch (Exception exception) {
      metricLogger.logErrorMetric(MetricName.CREATE_CALLED_PROCESS, Type.APPLICATION_METRIC, exception);
      WorkflowLogger.logError(exception, "step=errorPersistingCalledProcessToDB, calledProcessId=%s, rootProcessId=%s",
          workerActionRequest.getProcessInstanceId(),
          rootProcessInstanceId
      );
    }

    Object valueReturned = joinPoint.proceed();

    if(isCalledProcessPersisted) {
      if(valueReturned instanceof Map) {
        ((Map<String, Object>) valueReturned).put(calledProcessCreatedResultVariableName, true);
      }
    }

    return valueReturned;
  }

  /**
   * Currently camunda doesn't allow graceful closure of children (parent ot child communication),
   * will cover this in Call Activity 2.0
   * @param workerActionRequest
   */
  private void endExistingActiveSiblings(WorkerActionRequest workerActionRequest) {

    String rootProcessInstanceId = workerActionRequest.getInputVariables().get(ROOT_PROCESS_INSTANCE_ID);

    Optional<List<ProcessDetails>> existingActiveSiblings =
        processDetailsRepository.findByProcessStatusAndParentIdAndProcessIdIsNot(
            ProcessStatus.ACTIVE, rootProcessInstanceId, workerActionRequest.getProcessInstanceId()
        );

    if(existingActiveSiblings.isPresent() && !existingActiveSiblings.get().isEmpty()) {
      existingActiveSiblings.get().forEach(
          siblingProcess -> {

            WorkflowLogger.logInfo(
                "step=endExistingActiveSiblings, existingActiveSiblingProcessId=%s, rootProcessId=%s",
                siblingProcess.getProcessId(),
                rootProcessInstanceId
            );

            domainEventService.updateStatus(
                siblingProcess,
                ProcessStatus.ENDED,
                domainEventService.prepareEntityHeaders(workerActionRequest),
                EntityChangeAction.UPDATE,
                Collections.emptyMap()
            );
          }
      );
    }
  }

  private boolean isCalledProcess(final WorkerActionRequest workerActionRequest) {
    return Objects.nonNull(
        workerActionRequest.getInputVariables().get(ROOT_PROCESS_INSTANCE_ID)
    );
  }

  private String getCalledProcessCreatedResultVariableName(
      final WorkerActionRequest workerActionRequest,
      final String responseVariable) {
    String processInstanceId = workerActionRequest.getProcessInstanceId();
    return processInstanceId + UNDERSCORE + responseVariable;
  }
}
