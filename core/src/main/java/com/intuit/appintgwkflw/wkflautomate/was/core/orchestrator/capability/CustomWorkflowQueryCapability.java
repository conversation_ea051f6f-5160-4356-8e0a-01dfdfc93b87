package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.workflows.Definition;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * This capability is used for custom workflows where we will fetch the bpmn/dmn from definition
 * data
 */
@Component
@AllArgsConstructor
public class CustomWorkflowQueryCapability implements TemplateQueryCapabilityIf {

  private final ActivityDetailsRepository activityDetailsRepository;
  private final AuthHelper authHelper;
  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * Get template details for the transaction entity payload from the definition
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public List<TemplateDetails> getTemplateDetails(final TransactionEntity transactionEntity) {
    return getTemplateDetailsFromEnabledDefinition(authHelper.getOwnerId(), transactionEntity);
  }

  /**
   * Get template details for enabled definition
   *
   * @param ownerId realm id
   * @param v3TransactionEntity
   * @return template details
   */
  private List<TemplateDetails> getTemplateDetailsFromEnabledDefinition(
      final String ownerId, final TransactionEntity v3TransactionEntity) {
    // get latest enabled definitions ( status = enabled && internalStatus = null)
    List<DefinitionDetails> definitionDetailsList =
        getLatestEnabledDefinitionsList(ownerId, v3TransactionEntity, true);
    if (Objects.nonNull(definitionDetailsList)) {
      return definitionDetailsList.stream()
          .map(DefinitionDetails::getTemplateDetails)
          .collect(Collectors.toList());
    }
    return Collections.emptyList();
  }

  /**
   * Get template data for the transaction entity payload from the definition
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public byte[] getTemplateData(final TransactionEntity transactionEntity) {
    // get latest enabled definitions ( status = enabled && internalStatus = null)
    List<DefinitionDetails> definitionDetailsList =
        getLatestEnabledDefinitionsList(authHelper.getOwnerId(), transactionEntity, true);
    // custom approval supports only one enabled definition currently - filter out the stale
    // definitions

    DefinitionDetails definitionDetail = definitionDetailsList.stream()
        .filter(definitionDetails -> Objects.isNull(definitionDetails.getInternalStatus()))
        .findFirst()
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_NOT_FOUND));

    if(Objects.nonNull(definitionDetail.getDefinitionData())) {
      return definitionDetail.getDefinitionData();
    }

    return definitionDetail.getTemplateDetails().getTemplateData();
  }


  /**
   * Fetches the initial start event activity detail for a given transaction entity.
   * <p>
   * This method retrieves the latest enabled definitions for the specified transaction entity
   * and filters them to find a definition with a null internal status. It then fetches the
   * activity details for the start events associated with the found definition and returns
   * the initial start event activity detail.
   * </p>
   *
   * @param transactionEntity the transaction entity for which to fetch the start event activity detail
   * @return the initial start event activity detail
   * @throws WorkflowGeneralException if no enabled definition with a null internal status is found
   */
  @Override
  public ActivityDetail fetchInitialStartEventActivityDetail(TransactionEntity transactionEntity) {
    List<DefinitionDetails> definitionDetailsList =
            getLatestEnabledDefinitionsList(authHelper.getOwnerId(), transactionEntity, false);

    // custom approval supports only one enabled definition currently - filter out the stale
    // definitions
    DefinitionDetails definitionDetail = definitionDetailsList.stream()
            .filter(definitionDetails -> Objects.isNull(definitionDetails.getInternalStatus()))
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_NOT_FOUND));

    List<ActivityDetail> startEventsActivityDetails = activityDetailsRepository.findByTemplateIdAndActivityType(
            definitionDetail.getTemplateDetails().getId(), BpmnComponentType.START_EVENT.getName());

    return BpmnStartElementUtil.fetchInitialStartEventActivityDetail(startEventsActivityDetails);
  }

  /**
   * Get the DMN template details for the entity payload from the definition table
   *
   * @param id
   * @param enabledDefinitionList enabled definition list
   * @return
   */
  @Override
  public Pair<String, byte[]> getDmnTemplateDetails(
      String id, List<DefinitionDetails> enabledDefinitionList) {
    DefinitionDetails definitionDetails =
        getDmnDetailsForDefinition(getDefinitionId(enabledDefinitionList));
    return Pair.with(
        definitionDetails.getTemplateDetails().getTemplateName(),
        definitionDetails.getDefinitionData());
  }

  /**
   * Get all enabled definitions for the entity payload
   *
   * @param transactionEntity entity payload
   * @param isDefinitionDataRequired whether to return the definition bpmn byte data from database
   * @return
   */
  @Override
  public List<DefinitionDetails> getEnabledDefinitions(
      final TransactionEntity transactionEntity, final boolean isDefinitionDataRequired) {
    // get all enabled definitions ( status = enabled && (internalStatus = null || internalStatus= stale_definitions))
    return getAllEnabledDefinitionsList(authHelper.getOwnerId(), transactionEntity).get();
  }

  /**
   * Fetch dmn details from definition table based on bpmn definition id
   *
   * @param definitionId bpmn definition id
   * @return dmn template details
   */
  private DefinitionDetails getDmnDetailsForDefinition(String definitionId) {
    if (Objects.nonNull(definitionId)) {
      return definitionDetailsRepository.findByParentId(definitionId).orElse(null)
          .stream().findFirst().orElse(null);
    }
    return null;
  }

  /**
   * Get definition id from the definition list
   *
   * @param definitionDetailsList definition list
   * @return definition id
   */
  private String getDefinitionId(List<DefinitionDetails> definitionDetailsList) {
    return definitionDetailsList.stream()
        .findFirst()
        .map(DefinitionDetails::getDefinitionId)
        .orElse(null);
  }

  /**
   * <pre>
   * Get definitions for a given ownerId, model type, recordType and template name with following
   * additional conditions -
   * 1. Status is enabled and internal status is null
   * </pre>
   */
  private List<DefinitionDetails> getLatestEnabledDefinitionsList(
      final String ownerId, final TransactionEntity v3TransactionEntity, boolean isDataDefinitionRequired) {
    final RecordType recordType = v3TransactionEntity.getEntityType();
    String templateName = CustomWorkflowType.getTemplateName(v3TransactionEntity.getWorkflowType());
    return definitionDetailsRepository
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.parseLong(ownerId), ModelType.BPMN, recordType, templateName, isDataDefinitionRequired)
        .orElse(Collections.emptyList());
  }

  /**
   *
   *
   * <pre>
   * Get definitions for a given ownerId, model type, recordType and template name with following
   * additional conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   */
  private Optional<List<DefinitionDetails>> getAllEnabledDefinitionsList(
      final String ownerId, final TransactionEntity v3TransactionEntity) {
    final RecordType recordType = v3TransactionEntity.getEntityType();
    String templateName = CustomWorkflowType.getTemplateName(v3TransactionEntity.getWorkflowType());
    Optional<List<DefinitionDetails>> optionalDefinitionDetailsList = definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
        Long.parseLong(ownerId), ModelType.BPMN, recordType, templateName);
    WorkflowVerfiy.verify(
        !optionalDefinitionDetailsList.isPresent()
            || CollectionUtils.isEmpty(optionalDefinitionDetailsList.get()),
        WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
        ownerId,
        recordType);
    return optionalDefinitionDetailsList;
  }
  /**
   * Check if workflow is custom workflow and if record supports it
   *
   * @param workflow workflow name
   * @param recordType record type
   * @return
   */
  @VisibleForTesting
  boolean isCustomWorkflow(final String workflow, final String recordType) {
    return ObjectUtils.isNotEmpty(CustomWorkflowType.getTemplateName(workflow))
        && customWorkflowConfig.getRecords().stream()
            .anyMatch(record -> isWorkflowSupportedForRecord(workflow, recordType, record));
  }

  /**
   * Check if template is custom workflow and if record supports it
   *
   * @param templateName template name
   * @param recordType record type
   * @return
   */
  public boolean isCustomWorkflow(final String templateName, final RecordType recordType) {
    if(templateName == null || recordType == null) return false;

    if(!CustomWorkflowType.templateNameActionKeyMapping().containsKey(templateName)) return false;

    String workflow = CustomWorkflowType.getActionKey(templateName);
    return customWorkflowConfig.getRecords().stream()
            .anyMatch(record -> isWorkflowSupportedForRecord(workflow, recordType.toString(), record));
  }

  /**
   * Check if Precanned definition present in the company for the workflow : approval and
   * recordType : invoice For invoice approval workflow, invoiceapproval is the template name. If
   * precanned definition is not present, the return list will be empty.
   *
   * @param workflow workflow name
   * @param recordType record type
   * @return
   */
  public boolean isPrecannedDefinitionPresentForWorkflow(final String workflow, final String recordType) {
    final String ownerId = authHelper.getOwnerId();
    String templateName = TriggerUtil.getTemplateName(RecordType.fromType(recordType), workflow);
    // Check if definition exists for precanned template
    final Optional<List<DefinitionDetails>> optionalDefinitionDetailsList =
        definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
            Long.parseLong(ownerId), ModelType.BPMN, templateName, false);
    return !(optionalDefinitionDetailsList.isEmpty()
        || optionalDefinitionDetailsList.get().isEmpty());
  }

  /**
   * Check if custom is active and stale precanned definition present in the company for the workflow : approval and
   * recordType : invoice For invoice approval workflow, invoiceapproval is the template name.
   *
   * @param workflow workflow name
   * @param recordType record type
   * @return
   */
  public boolean isLatestCustomPresentForWorkflow(final String workflow, final String recordType) {
    final String ownerId = authHelper.getOwnerId();

    String customTemplateName = CustomWorkflowType.getTemplateName(workflow);

    final Optional<List<DefinitionDetails>> optionalCustomDefinitionDetailsList =
            definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    Long.parseLong(ownerId), ModelType.BPMN, RecordType.fromType(recordType), customTemplateName,
                    false
            );

    return optionalCustomDefinitionDetailsList.isPresent()
            && !optionalCustomDefinitionDetailsList.get().isEmpty();
  }

  /**
   *  If it is a precanned to custom migration case, then it will return true.
   *
   * @param definition definition
   * @return
   */
  public boolean isPrecannedDefinitionPresentDuringMigration(final Definition definition) {
    if(definition.getId() == null)
      return false;
    Optional<DefinitionDetails> optionalDefinitionDetails = definitionDetailsRepository.findByDefinitionId(definition.getId().getLocalId());
    if(!optionalDefinitionDetails.isEmpty()) {
      TemplateDetails templateDetails = optionalDefinitionDetails.get().getTemplateDetails();
      return templateDetails.getTemplateCategory().equals(TemplateCategory.HUB.toString());
    }
    return false;
  }
  /**
   * Check if record (eg: invoice) supports the workflow
   *
   * @param workflow workflow name
   * @param recordType record type
   * @param recordObj Record object
   * @return
   */
  private boolean isWorkflowSupportedForRecord(
      final String workflow, final String recordType, final Record recordObj) {
    return recordObj.getId().equalsIgnoreCase(recordType)
        && recordObj.getActionGroups().stream()
            .anyMatch(actionGroup -> actionGroup.getId().equalsIgnoreCase(workflow));
  }
}
