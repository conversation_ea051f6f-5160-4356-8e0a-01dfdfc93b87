package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import static com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil.getTemplateName;
import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import liquibase.repackaged.org.apache.commons.collections4.CollectionUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * This capability is used when template data can be retrieved from template table
 * Template details are found using combination of entity type + workflow ( eg: invoiceapproval)
 */
@Component
@AllArgsConstructor
public class DefaultTemplateQueryCapability implements TemplateQueryCapabilityIf {

  private final TemplateService templateService;
  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final TemplateDetailsRepository templateDetailsRepository;
  private final AuthHelper authHelper;
  private final ActivityDetailsRepository activityDetailsRepository;

  /**
   * Get template details for the transaction entity payload based on entity type + workflow
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public List<TemplateDetails> getTemplateDetails(final TransactionEntity transactionEntity) {
    final EventHeaders eventHeaders = transactionEntity.getEventHeaders();
    return getTemplateDetails(eventHeaders.getEntityType(), eventHeaders.getWorkflow())
        .orElse(Collections.emptyList());
  }

  /**
   * Get template data for the transaction entity payload based on entity type + workflow
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public byte[] getTemplateData(final TransactionEntity transactionEntity) {
    // if it is on-demand approval the template name will be "customApproval",
    // in other cases it will be "<EntityType><WorkflowType>"
    String templateName = getTemplateName(transactionEntity.getEntityType(), transactionEntity.getWorkflowType());
    if((boolean)transactionEntity.getV3EntityPayload().getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, false)){
      templateName = WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE;
    }
    TemplateDetails templateDetails = templateService.getTemplateByName(templateName);
    return ObjectUtils.isNotEmpty(templateDetails) ? templateDetails.getTemplateData() : null;
  }

  /**
   * Fetches the initial start event `ActivityDetail` for a given `TransactionEntity`.
   *
   * <p>This method determines the appropriate template name based on the entity type and workflow type
   * of the provided `TransactionEntity`. If the transaction is marked for on-demand approval, a custom
   * approval template is used instead. It then retrieves the corresponding `TemplateDetails` and fetches
   * a list of `ActivityDetail` entities associated with the start event activity type. Finally, it returns
   * the initial start event activity detail from the list.
   *
   * Sample output:
   * {"id":241,"type":"MILESTONE","activityId":"5db0d1fa-a722-4266-9ea5-69c6aa1cb78a","activityName":"startEvent","activityType":"startEvent","parentId":0,"attributes":"{ \"modelAttributes\": { \"stepDetails\": \"{ \\\"startEvent\\\": [ \\\"startEvent\\\" ] }\", \"handlerDetails\": \"{}\", \"startableEvents\": \"[\\\"created\\\"]\", \"processVariablesDetails\": \"[ { \\\"variableName\\\": \\\"entityChangeType\\\", \\\"variableType\\\": \\\"String\\\" }, { \\\"variableName\\\": \\\"Id\\\", \\\"variableType\\\": \\\"String\\\" }, { \\\"variableName\\\": \\\"intuit_userid\\\", \\\"variableType\\\": \\\"String\\\" }, { \\\"variableName\\\": \\\"intuit_realmid\\\", \\\"variableType\\\": \\\"String\\\" }, { \\\"variableName\\\": \\\"DocNumber\\\", \\\"variableType\\\": \\\"string\\\", \\\"overrideIfAbsent\\\": true }, { \\\"variableName\\\": \\\"CompanyName\\\", \\\"variableType\\\": \\\"string\\\", \\\"overrideIfAbsent\\\": true }, { \\\"variableName\\\": \\\"entityType\\\", \\\"variableType\\\": \\\"String\\\", \\\"overrideIfAbsent\\\": true }, { \\\"variableName\\\": \\\"onDemandApproval\\\", \\\"variableType\\\": \\\"String\\\", \\\"overrideIfAbsent\\\": true } ]\", \"ignoreProcessVariablesDetails\": \"true\"}}","templateDetails":{"id":"startEvent","version":0,"definitionType":"USER"}}
   *
   * @param transactionEntity The `TransactionEntity` containing the entity type and workflow type information.
   * @return The initial start event `ActivityDetail` associated with the transaction.
   */
  @Override
  public ActivityDetail fetchInitialStartEventActivityDetail(TransactionEntity transactionEntity) {
    String templateName = getTemplateName(transactionEntity.getEntityType(), transactionEntity.getWorkflowType());
    if((boolean)transactionEntity.getV3EntityPayload().getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, false)){
      templateName = WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE;
    }
    // Startable events will be available only for BPMN templates and we want to fetch enabled templates to trigger a process
    Optional<String> templateId = templateDetailsRepository.findTemplateIdForTopByTemplateNameOrderByVersionDesc(
            FilenameUtils.removeExtension(templateName), Status.ENABLED.getStatus(), ModelType.BPMN.getModelType());
    if(templateId.isEmpty()) {
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST, "Enabled Template not found for template name: " + templateName);
    }
    List<ActivityDetail> startEventsActivityDetails = activityDetailsRepository.findByTemplateIdAndActivityType(
            templateId.get(), BpmnComponentType.START_EVENT.getName());
    return BpmnStartElementUtil.fetchInitialStartEventActivityDetail(startEventsActivityDetails);
  }

  /**
   * Get the DMN template details for the entity payload.
   * Pair returns the template name and template data.
   *
   * @param id
   * @param enabledDefinitionList enabled definition list
   * @return
   */
  @Override
  public Pair<String, byte[]> getDmnTemplateDetails(
      final String id,
      final List<DefinitionDetails> enabledDefinitionList) {
    // ToDo: Assumption-> only one workflow definition allowed. Revisit when allowing multiple
    // definitions
    final TemplateDetails bpmnTemplateDetails =
        TriggerUtil.getBpmnTemplateDetails(enabledDefinitionList);
    final TemplateDetails dmnTemplateDetails = getDmnTemplateDetails(bpmnTemplateDetails.getId());
    WorkflowVerfiy.verifyNull(dmnTemplateDetails, WorkflowError.TEMPLATE_DOES_NOT_EXIST);
    return Pair.with(dmnTemplateDetails.getTemplateName(),
        dmnTemplateDetails.getTemplateData());
  }

  /**
   * Get all enabled definitions for the entity payload
   *
   * @param transactionEntity        entity payload
   * @param isDefinitionDataRequired whether to return the definition bpmn byte data from database
   * @return
   */
  @Override
  public List<DefinitionDetails> getEnabledDefinitions(
      final TransactionEntity transactionEntity, final boolean isDefinitionDataRequired) {
    return getEnabledEligibleDefinitionsList(
        authHelper.getOwnerId(),
        transactionEntity, isDefinitionDataRequired);
  }

  /**
   * Given parent bpmn gives list of dmns templates
   */
  private TemplateDetails getDmnTemplateDetails(final String templateId) {
    // Get referred DMN  for the BPMN template
    final Optional<TemplateDetails> templateDetailsOfDmnOptional =
        templateDetailsRepository.findTopByParentIdOrderByVersionDesc(templateId);

    return templateDetailsOfDmnOptional.orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.RULE_EVALUATION_DMN_NOT_FOUND_ERROR));
  }

  /**
   * Fetches the template details by templateName.
   *
   * @param recordType input record type
   * @param workflow   input workflow name
   */
  @VisibleForTesting
  Optional<List<TemplateDetails>> getTemplateDetails(
      final RecordType recordType, final String workflow) {
    return templateDetailsRepository.findTemplateDetailsExceptTemplateData(
        getTemplateName(recordType, workflow), Status.ENABLED, ModelType.BPMN);
  }

  /**
   * <pre>
   * Get definitions for a given ownerId, model type and template name with following
   * additional conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   */
  @VisibleForTesting
  List<DefinitionDetails> getEnabledEligibleDefinitionsList(
      final String ownerId, final TransactionEntity v3TransactionEntity, final boolean isDefinitionDataRequired) {
    final RecordType recordType = v3TransactionEntity.getEntityType();
    final String templateName = getTemplateName(recordType, v3TransactionEntity.getWorkflowType());
    // Find definitions for template with name => entityType+workflowType and owner id
    final Optional<List<DefinitionDetails>> optionalDefinitionDetailsList =
        definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
            Long.parseLong(ownerId), ModelType.BPMN, templateName, isDefinitionDataRequired);
    WorkflowVerfiy.verify(!optionalDefinitionDetailsList.isPresent() ||
            CollectionUtils.isEmpty(optionalDefinitionDetailsList.get()), WorkflowError.ENABLED_DEFINITION_NOT_FOUND, ownerId, recordType);
    return optionalDefinitionDetailsList.get();
  }
}
