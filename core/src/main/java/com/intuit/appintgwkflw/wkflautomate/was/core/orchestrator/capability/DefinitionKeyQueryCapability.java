package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.util.DefinitionDetailsRepositoryUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import lombok.AllArgsConstructor;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

import javax.persistence.NoResultException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * This capability is used for custom workflows where we will fetch the bpmn/dmn from definition
 * data
 */
@Component
@AllArgsConstructor
public class DefinitionKeyQueryCapability implements TemplateQueryCapabilityIf {

  private final ActivityDetailsRepository activityDetailsRepository;
  private final AuthHelper authHelper;
  private final DefinitionDetailsRepository definitionDetailsRepository;


  /**
   * Fetches list of templateDetails for given workflow details in TransactionEntity
   * @param transactionEntity entity payload
   * @return singleton list of template details
   */
  @Override
  public List<TemplateDetails> getTemplateDetails(TransactionEntity transactionEntity) {
    List<TemplateDetails> templateDetailsList = new ArrayList<>();

    DefinitionDetails definitionDetails =
            getEnabledDefinitionForWorkflow(
                    authHelper.getOwnerId(),
                    transactionEntity.getEventHeaders().getDefinitionKey());

     Optional.ofNullable(definitionDetails)
             .ifPresent( dd -> templateDetailsList.add(dd.getTemplateDetails()));
     return templateDetailsList;
  }

  /**
   * Fetches template data for definition linked to definitionKey info in transactionEntity
   * @param transactionEntity entity payload
   */
  @Override
  public byte[] getTemplateData(TransactionEntity transactionEntity) {

    DefinitionDetails definitionDetails =
            getEnabledDefinitionForWorkflow(
                    authHelper.getOwnerId(),
                    transactionEntity.getEventHeaders().getDefinitionKey());
    return Optional.ofNullable(definitionDetails.getDefinitionData())
            // definitionDetails.definitionData is null in database for multi-condition workflows.
            // The target state of single definition workflows is that there shouldn't be definitionData
            // Hence fetching the details from template.
            .orElse(Optional.ofNullable(definitionDetails.getTemplateDetails())
                    .map(TemplateDetails::getTemplateData).orElse(null));
  }


  /**
   * Fetches the initial start event activity detail for a given transaction entity.
   *
   * <p>This method retrieves the enabled workflow definition associated with the transaction entity
   * by using the owner ID from the authentication header and the definition key from the transaction entity.
   * It then fetches a list of start event activity details based on the template ID and activity type.
   * Finally, it returns the initial start event activity detail from the list.
   *
   * @param transactionEntity the transaction entity containing event headers and workflow information
   * @return the initial start event activity detail
   * @throws WorkflowGeneralException if the owner ID format is invalid or if no enabled definition is found
   */
  @Override
  public ActivityDetail fetchInitialStartEventActivityDetail(TransactionEntity transactionEntity) {
    String templateId =
            getTemplateIdByEnabledDefinitionKeyAndOwnerId(
                    Long.valueOf(authHelper.getOwnerId()),
                    transactionEntity.getEventHeaders().getDefinitionKey());

    List<ActivityDetail> startEventsActivityDetails = activityDetailsRepository.findByTemplateIdAndActivityType(templateId
            , BpmnComponentType.START_EVENT.getName());
    return BpmnStartElementUtil.fetchInitialStartEventActivityDetail(startEventsActivityDetails);
  }

  /**
   * Get the DMN template details for the entity payload from the definition table
   *
   * @param id
   * @param enabledDefinitionList enabled definition list
   * @return Pair of templateName, dmn as byte array
   */
  @Override
  public Pair<String, byte[]> getDmnTemplateDetails(String id, List<DefinitionDetails> enabledDefinitionList) {
    String definitionId = getDefinitionId(enabledDefinitionList, id);
    DefinitionDetails definitionDetails =
            definitionDetailsRepository.findByParentId(definitionId)
                    .orElseThrow( () -> new WorkflowGeneralException(WorkflowError.DMN_NOT_FOUND_ERROR))
            .stream().findFirst()
                    .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DMN_NOT_FOUND_ERROR));
    return Pair.with(
            definitionDetails.getTemplateDetails().getTemplateName(),
            definitionDetails.getDefinitionData());
  }

  /**
   * Get definition id from the definition list
   *
   * @param definitionDetailsList definition list
   * @param definitionKey
   * @return definition id
   */
  public String getDefinitionId(List<DefinitionDetails> definitionDetailsList, String definitionKey) {
    return definitionDetailsList.stream()
            .filter( i -> i.getDefinitionKey().equals(definitionKey))
            .findFirst()
            .map(DefinitionDetails::getDefinitionId)
            .orElse(null);
  }

  /**
   *
   *
   * <pre>
   * Get definitions for a given ownerId and definitionKey with following additional
   * conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * OR
   * 2. internal status is MARKED_FOR_DELETE or MARKED_FOR_DISABLE
   * </pre>
   *
   */
  @Override
  public List<DefinitionDetails> getEnabledDefinitions(TransactionEntity transactionEntity, boolean isDefinitionDataRequired) {
    return definitionDetailsRepository.findDefinitionsByDefinitionKey(
            transactionEntity.getEventHeaders().getDefinitionKey(),
            Long.valueOf(authHelper.getOwnerId()),
            isDefinitionDataRequired
    );
  }

  /**
   * Gets the enabled definition for the workflow.
   *
   * @param ownerId the owner id
   * @param definitionKey the definitionKey
   * @return the enabled definition
   */
  @VisibleForTesting
  private DefinitionDetails getEnabledDefinitionForWorkflow(
          final String ownerId, final String definitionKey) {
    try {
      return definitionDetailsRepository.findEnabledDefinitionForDefinitionKey(
              Long.parseLong(ownerId), definitionKey);
    }
    catch (final NumberFormatException ex) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_OWNER_ID,
              String.format("Invalid owner id format passed: %s", ownerId), ex);
    } catch (final NoResultException ex) {
      throw new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
              String.format("Definition details not found for the given owner id: %s and definition key: %s",
              ownerId, definitionKey), ex);
    }
  }

  private String getTemplateIdByEnabledDefinitionKeyAndOwnerId(Long ownerId, String definitionKey) {
    Map<String, Object> filterParams = new HashMap<>();
    filterParams.put(RepositoryConstants.OWNER_ID, ownerId);
    filterParams.put(RepositoryConstants.DEFINITION_KEY, definitionKey);
    DefinitionDetailsRepositoryUtil.setEnabledStatusPredicate(filterParams);
    List<String> selectParams = new ArrayList<>();
    selectParams.add(RepositoryConstants.TEMPLATE_DETAILS_TEMPLATE_ID);
    Optional<List<DefinitionDetails>> definitionDetails = definitionDetailsRepository
    .findDefinitionsWithCustomFilterAndSelection(filterParams, selectParams);

    return definitionDetails
            .flatMap(details -> details.stream()
                    .filter(detail -> Objects.nonNull(detail.getTemplateDetails()))
                    .findFirst())
            .map(detail -> detail.getTemplateDetails().getId())
            .orElseThrow(() -> new WorkflowGeneralException(
                    WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
                    "Enabled definition not found for the given owner id: %s and definition key: %s",
                    ownerId, definitionKey));
  }

}
