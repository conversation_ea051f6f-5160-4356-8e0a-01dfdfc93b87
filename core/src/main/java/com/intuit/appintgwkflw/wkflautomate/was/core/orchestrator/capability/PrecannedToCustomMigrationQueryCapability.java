package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * This capability is used for custom workflows where we will fetch the bpmn/dmn from definition
 * data
 */
@Component
@AllArgsConstructor
public class PrecannedToCustomMigrationQueryCapability implements TemplateQueryCapabilityIf {

  private final CustomWorkflowQueryCapability customWorkflowQueryCapability;
  private final DefaultTemplateQueryCapability defaultTemplateQueryCapability;

  /**
   * Get template details for the transaction entity payload from the definition
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public List<TemplateDetails> getTemplateDetails(final TransactionEntity transactionEntity) {
    return customWorkflowQueryCapability.getTemplateDetails(transactionEntity);
  }

  /**
   * Get template data for the transaction entity payload from the definition
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public byte[] getTemplateData(final TransactionEntity transactionEntity) {
    return customWorkflowQueryCapability.getTemplateData(transactionEntity);
  }


  @Override
  public ActivityDetail fetchInitialStartEventActivityDetail(TransactionEntity transactionEntity) {
    return customWorkflowQueryCapability.fetchInitialStartEventActivityDetail(transactionEntity);
  }

  /**
   * Get the DMN template details for the entity payload from the definition table
   *
   * @param id
   * @param enabledDefinitionList enabled definition list
   * @return
   */
  @Override
  public Pair<String, byte[]> getDmnTemplateDetails(
      String id, List<DefinitionDetails> enabledDefinitionList) {
    return customWorkflowQueryCapability.getDmnTemplateDetails(id, enabledDefinitionList);
  }

  /**
   * Get all enabled definitions for both the custom and the precanned workflow for the entity payload
   *
   * @param transactionEntity entity payload
   * @param isDefinitionDataRequired whether to return the definition bpmn byte data from database
   * @return
   */
  @Override
  public List<DefinitionDetails> getEnabledDefinitions(
      final TransactionEntity transactionEntity, final boolean isDefinitionDataRequired) {
    List<DefinitionDetails> customDefinitions =
            customWorkflowQueryCapability.getEnabledDefinitions(transactionEntity, isDefinitionDataRequired);
    List<DefinitionDetails> precannedDefinitions =
            defaultTemplateQueryCapability.getEnabledDefinitions(transactionEntity, isDefinitionDataRequired);

    return ListUtils.union(
            customDefinitions,
            precannedDefinitions
    );
  }
}
