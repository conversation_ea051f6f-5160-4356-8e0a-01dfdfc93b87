package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

/** This factory returns the implementation based on the transaction entity payload */
@Component
@AllArgsConstructor
public class TemplateQueryCapabilityFactory {

  private final DefaultTemplateQueryCapability defaultTemplateQueryCapability;
  private final WorkflowProviderTemplateQueryCapability workflowProviderTemplateQueryCapability;
  private final CustomWorkflowQueryCapability customWorkflowQueryCapability;
  private final PrecannedToCustomMigrationQueryCapability precannedToCustomMigrationQueryCapability;
  private final DefinitionKeyQueryCapability definitionKeyQueryCapability;
  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * Get template query capability based on entity payload.
   * In case of migration from precanned to custom, PrecannedToCustomMigrationQueryCapability would be returned.
   *
   * @param eventHeaders
   * @return
   */
  public TemplateQueryCapabilityIf getTemplateQueryCapability(final EventHeaders eventHeaders) {
    /**
     * Trigger the process using the definitionId. This field is added to support 1-1 mapping b/w workflow and trigger event.
     * This is an alternative to providerWorkflowId - sent via appconnect call.
     */
    if (Objects.nonNull(eventHeaders.getDefinitionKey())){
      return definitionKeyQueryCapability;
    }
    /**
     * For workflows with providerWorkflowId (like reminder), we'll fetch definition and template
     * details based on providerWorkflowId
     */
    if (Objects.nonNull(eventHeaders.getProviderWorkflowId())) {
      return workflowProviderTemplateQueryCapability;
    }
    /**
     * This block will execute for the template like invoice approval where we are getting name of the template using actions
     * Here isCustomWorkflow() will return template name using action name i.e approval will check for custom approval
     * Todo: post migration of invoice approval to custom platform https://jira.intuit.com/browse/QBOES-10901
     * check if custom workflow config is present for workflow and entity_type and also
     * if any precanned definition not present for the workflow, entity_type in the company
     */
    else if (customWorkflowQueryCapability.isCustomWorkflow(
            eventHeaders.getWorkflow(), eventHeaders.getEntityType().toString())) {
      if(eventHeaders.isOnDemandApproval()){
        return defaultTemplateQueryCapability;
      }
      else if(!customWorkflowQueryCapability.isPrecannedDefinitionPresentForWorkflow(
              eventHeaders.getWorkflow(), eventHeaders.getEntityType().toString())) {
        return customWorkflowQueryCapability;
      }
      else if(customWorkflowQueryCapability.isLatestCustomPresentForWorkflow(
              eventHeaders.getWorkflow(), eventHeaders.getEntityType().toString())) {
        return precannedToCustomMigrationQueryCapability;
      }
    }
    /** default case */
    return defaultTemplateQueryCapability;
  }
}
