package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import org.javatuples.Pair;

import java.util.List;

public interface TemplateQueryCapabilityIf {

  /**
   * Get template details for the transaction entity payload
   * @param transactionEntity entity payload
   * @return
   */
  List<TemplateDetails> getTemplateDetails(TransactionEntity transactionEntity);

  /**
   * Get template data for the transaction entity payload
   * @param transactionEntity entity payload
   * @return
   */
  byte[] getTemplateData(TransactionEntity transactionEntity);

  /**
   * Fetches the initial start event `ActivityDetail` for a given `TransactionEntity`.
   * <p>
   * This method retrieves the `ActivityDetail` associated with the initial start event
   * of the workflow process, based on the provided `TransactionEntity`. The `TransactionEntity`
   * contains the transaction payload and additional event and workflow-specific information.
   * <p>
   * Example:: If there is any scenario where we would need extension properties of the initial start event
   * or its related activity detail like template id, activity name and type we can call this function.
   * If we want to get startableEvents of a BPMN process, we can call this function as the startableEvents is an
   * extension property and can be found under 'attributes' in @ActivityDetail entity.
   *
   * @param transactionEntity the `TransactionEntity` containing the transaction payload and
   *                          event details from which the initial start event `ActivityDetail`
   *                          is to be fetched.
   * @return the `ActivityDetail` representing the initial start event of the workflow process.
   */
  ActivityDetail fetchInitialStartEventActivityDetail(TransactionEntity transactionEntity);

  /**
   * Get the DMN template details for the entity payload
   * @param id
   * @param enabledDefinitionList enabled definition list
   * @return
   */
  Pair<String, byte[]> getDmnTemplateDetails(String id, List<DefinitionDetails> enabledDefinitionList);

  /**
   * Get all enabled definitions for the entity payload
   * @param transactionEntity entity payload
   * @param isDefinitionDataRequired whether to return the definition bpmn byte data from database
   * @return
   */
  List<DefinitionDetails> getEnabledDefinitions(TransactionEntity transactionEntity, boolean isDefinitionDataRequired);
}
