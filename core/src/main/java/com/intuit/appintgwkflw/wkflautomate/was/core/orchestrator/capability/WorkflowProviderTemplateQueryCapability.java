package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.util.DefinitionDetailsRepositoryUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.persistence.NoResultException;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;


/**
 * This capability is used when template and definition data can be retrieved based on provider workflow id
 */
@Component
@AllArgsConstructor
public class WorkflowProviderTemplateQueryCapability implements TemplateQueryCapabilityIf {

  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final AuthHelper authHelper;
  private final ActivityDetailsRepository activityDetailsRepository;

  /**
   * Get template details for the transaction entity payload from the definition based on provider
   * workflow id
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public List<TemplateDetails> getTemplateDetails(final TransactionEntity transactionEntity) {
    List<TemplateDetails> templateDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails =
            getEnabledDefinitionForWorkflow(
                    authHelper.getOwnerId(),
                    transactionEntity.getEventHeaders().getProviderWorkflowId());

    if (ObjectUtils.isNotEmpty(definitionDetails)) {
      templateDetailsList.add(definitionDetails.getTemplateDetails());
    }
    return templateDetailsList;
  }

  /**
   * Get template data for the transaction entity payload based on provider workflow id
   *
   * @param transactionEntity entity payload
   * @return
   */
  @Override
  public byte[] getTemplateData(final TransactionEntity transactionEntity) {
    /**
     * If the eventHeader contains ProviderWorkflowId, get the definition details stored in the
     * WAS db Else, get template by name
     * In case of BYO, we need to get the definition data from the definition table and not from the templates table.
     * This is because the definition data in the definition table contains additional process variables which gets injected
     * into the bpmn when the definition is created.
     */

    DefinitionDetails definitionDetails =
            getEnabledDefinitionForWorkflow(
                    authHelper.getOwnerId(),
                    transactionEntity.getEventHeaders().getProviderWorkflowId());
    return Optional.ofNullable(definitionDetails.getDefinitionData())
            // definitionDetails.definitionData is null in database for multi-condition workflows.
            // The target state of single definition workflows is that there shouldn't be definitionData
            // Hence fetching the details from template.
            .orElse(Optional.ofNullable(definitionDetails.getTemplateDetails())
                    .map(TemplateDetails::getTemplateData).orElse(null));
  }

  /**
   * If the eventHeader contains ProviderWorkflowId, get the definition details stored in the
   * WAS db Else, get template by name
   * In case of BYO, we need to get the definition data from the definition table and not from the templates table.
   * This is because the definition data in the definition table contains additional process variables which gets injected
   * into the bpmn when the definition is created.
   */
  @Override
  public ActivityDetail fetchInitialStartEventActivityDetail(final TransactionEntity transactionEntity) {
    String templateId =
            getTemplateIdByEnabledDefinitionWithWorkflowIdAndOwnerId(
                    Long.valueOf(authHelper.getOwnerId()),
                    transactionEntity.getEventHeaders().getProviderWorkflowId());
    List<ActivityDetail> startEventsActivityDetails = activityDetailsRepository.findByTemplateIdAndActivityType(
            templateId, BpmnComponentType.START_EVENT.getName());
    return BpmnStartElementUtil.fetchInitialStartEventActivityDetail(startEventsActivityDetails);
  }

  /**
   * Get the DMN template details for the entity payload from the definition based on provider
   * workflow id. Pair returns the template name and template data.
   *
   * @param id                    provider workflow id
   * @param enabledDefinitionList enabled definition list
   * @return
   */
  @Override
  public Pair<String, byte[]> getDmnTemplateDetails(
          final String id,
          final List<DefinitionDetails> enabledDefinitionList) {
    DefinitionDetails definitionDetails =
            getDmnDefinitionDetailsByProviderWorkflowId(
                    id);
    return Pair.with(definitionDetails.getTemplateDetails().getTemplateName(),
            definitionDetails.getDefinitionData());
  }

  /**
   * Get all enabled definitions for the entity payload based on provider workflow id
   *
   * @param transactionEntity        entity payload
   * @param isDefinitionDataRequired whether to return the definition bpmn byte data from database
   * @return
   */
  @Override
  public List<DefinitionDetails> getEnabledDefinitions(
          final TransactionEntity transactionEntity, final boolean isDefinitionDataRequired) {
    return getEnabledAndMarkedDefinitionForWorkflow(
            authHelper.getOwnerId(),
            transactionEntity.getEventHeaders().getProviderWorkflowId(),
            isDefinitionDataRequired);
  }

  /**
   * Given provider workflowId and ownerId gives dmns template details
   * Internally the method appends the current ownerId present in the context to
   * narrow down the query.
   *
   * @param providerWorkflowId
   * @return {@link DefinitionDetails}
   */
  private DefinitionDetails getDmnDefinitionDetailsByProviderWorkflowId(
          final String providerWorkflowId) {
    return definitionDetailsRepository
            .findDmnDefinitionDetailsByWorkflowIdAndOwnerId(
                    providerWorkflowId, WASContext.getOwnerId())
            .orElseThrow(
                    () -> new WorkflowGeneralException(WorkflowError.RULE_EVALUATION_DMN_NOT_FOUND_ERROR));
  }

  /**
   * Gets the enabled definition for the workflow.
   *
   * @param ownerId    the owner id
   * @param workflowId the workflow id
   * @return the enabled definition
   */
  @VisibleForTesting
  public DefinitionDetails getEnabledDefinitionForWorkflow(
          final String ownerId, final String workflowId) {
    try {
      return definitionDetailsRepository.findEnabledDefinitionForWorkflowId(
              Long.parseLong(ownerId), workflowId);
    } catch (final NoResultException ex) {
      throw new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
              String.format("Definition details not found for the given owner id: %s and provider workflow id: %s",
                      ownerId, workflowId), ex);
    }
  }

  /**
   * <pre>
   * Get definitions for a given ownerId and provider workflow id with following additional
   * conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * OR
   * 2. internal status is MARKED_FOR_DELETE or MARKED_FOR_DISABLE
   * </pre>
   *
   * @param ownerId
   * @param workflowId
   * @param isDefinitionDataRequired
   * @return
   */
  @VisibleForTesting
  public List<DefinitionDetails> getEnabledAndMarkedDefinitionForWorkflow(
          final String ownerId, final String workflowId, final boolean isDefinitionDataRequired) {
    try {
      return definitionDetailsRepository.findEnabledAndMarkedDefinitionForWorkflow(
              Long.parseLong(ownerId), workflowId, isDefinitionDataRequired);
    } catch (final NoResultException ex) {
      throw new WorkflowGeneralException(WorkflowError.ENABLED_DEFINITION_NOT_FOUND);
    }
  }

  private String getTemplateIdByEnabledDefinitionWithWorkflowIdAndOwnerId(Long ownerId, String workflowId) {
    Map<String, Object> filterParams = new HashMap<>();
    filterParams.put(RepositoryConstants.OWNER_ID, ownerId);
    filterParams.put(RepositoryConstants.WORKFLOW_ID, workflowId);
    DefinitionDetailsRepositoryUtil.setEnabledStatusPredicate(filterParams);
    List<String> selectParams = new ArrayList<>();
    selectParams.add(RepositoryConstants.TEMPLATE_DETAILS_TEMPLATE_ID);
    Optional<List<DefinitionDetails>> definitionDetails = definitionDetailsRepository
            .findDefinitionsWithCustomFilterAndSelection(filterParams, selectParams);

    return definitionDetails
            .flatMap(details -> details.stream()
                    .filter(detail -> Objects.nonNull(detail.getTemplateDetails()))
                    .findFirst())
            .map(detail -> detail.getTemplateDetails().getId())
            .orElseThrow(() -> new WorkflowGeneralException(
                    WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
                    "Enabled definition not found for the given owner id: %s and provider workflow id: %s",
                    authHelper.getOwnerId(), workflowId));
  }

}
