package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.*;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

// TODO check for steps and recurrence rule mapper for the new configs

/**
 * This class extends from the customWorkflowConfigBase class and add on the new components and
 * methods for config v2
 * */
@Configuration
@Data
@ConfigurationProperties("custom-config-v2")
@EqualsAndHashCode(callSuper = true)
public class CustomConfigV2 extends CustomWorkflowConfigBase
    implements InitializingBean, ICustomWorkflowConfig {
  private List<EntitySet> entitySet = new ArrayList<>();

  private List<ActionGroup> actionGroups = new ArrayList<>();
  private List<EntityActionGroupSet> entityActionGroupSet = new ArrayList<>();
  private List<ParameterOverrideSet> parameterOverrideSet = new ArrayList<>();
  private List<PreCannedSet> preCannedSet = new ArrayList<>();
  private List<PreCannedParamSet> preCannedParamSet = new ArrayList<>();

  private List<PreCannedMultiCondSet> preCannedMultiCondSets = new ArrayList<>();

  @Override
  public void afterPropertiesSet() throws Exception {
    WorkflowLogger.logInfo("Init creating custom workflow V2 config map");
    try {
      CustomWorkflowConfigUtil.createRecordFromConfig(this);
      CustomWorkflowConfigUtil.createTemplateConfig(this);
      populateRecordConfig();
      populateTemplateConfig();
    } catch (Exception e) {
      // Don't throw error as we don't want to stop the service
      WorkflowLogger.logError(e, "Error creating custom workflow V2 config map");
    }

  }
  
}
