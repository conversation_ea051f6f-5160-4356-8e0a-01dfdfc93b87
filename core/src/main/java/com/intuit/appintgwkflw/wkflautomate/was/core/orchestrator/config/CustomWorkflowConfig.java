package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Getter
@Setter
@RequiredArgsConstructor
public class CustomWorkflowConfig {

  @Autowired private  CustomWorkflowConfigFactory customWorkflowConfigFactory;

  @Autowired private OldCustomWorkflowConfig oldConfig;
  @Autowired private CustomConfigV2 newConfig;
  @Autowired private MigratedConfig migratedConfig;

  public Record getRecordObjForType(String recordType) {
    return customWorkflowConfigFactory.getInstanceOf(recordType).getRecordObjForType(recordType);
  }

  public Map<String, ConfigTemplate> getTemplateMap() {
    final Map<String, ConfigTemplate> finalResultMap = oldConfig.getTemplateMap();
    if (Objects.nonNull(newConfig)) {
      final Map<String, ConfigTemplate> newTemplateMap = newConfig.getTemplateMap();
      for (RecordType recordType : RecordType.values()) {
        List<String> templateIds =
            customWorkflowConfigFactory.getRecordTypeForTemplate(recordType.getRecordType());
        if (!CollectionUtils.isEmpty(templateIds)) {
          templateIds.stream().forEach(id -> finalResultMap.put(id, newTemplateMap.get(id)));
        }
      }
    }

    return finalResultMap;
  }

  /** These will be revoked once we have configV2 serving all the values */
  public List<DataType> getDataTypes() {
    List<DataType> dataTypes = oldConfig.getDataTypes();
    // TODO replace this with new config once we move to new config
    return dataTypes;
  }

  public List<Record> getRecords() {
    List<Record> configRecords = new ArrayList<>();
    for (RecordType recordType : RecordType.values()) {
      Record record = getRecordObjForType(recordType.getRecordType());
      if (Objects.nonNull(record)) {
        configRecords.add(record);
      }
    }
    return configRecords;
  }

  public List<Attribute> getAttributes() {
    Set<Attribute> attributes = Set.copyOf(oldConfig.getAttributes());
    //  TODO replace this with new config once we move to new config
    return new ArrayList<>(attributes);
  }

  public List<ConfigTemplate> getConfigTemplates() {

    Map<String, ConfigTemplate> oldTemplateConfigMap = oldConfig.getTemplateMap();
    final Map<String, ConfigTemplate> finalResultMap =
        (CollectionUtils.isEmpty(oldTemplateConfigMap)) ? new HashMap<>() : oldTemplateConfigMap;
    if (Objects.nonNull(newConfig)) {
      final Map<String, ConfigTemplate> newTemplateMap = newConfig.getTemplateMap();
      for (RecordType recordType : RecordType.values()) {
        List<String> templateIds =
            customWorkflowConfigFactory.getRecordTypeForTemplate(recordType.getRecordType());

        if (!CollectionUtils.isEmpty(templateIds)) {
          templateIds.stream().forEach(id -> finalResultMap.put(id, newTemplateMap.get(id)));
        }
      }
    }
    return  finalResultMap.values().stream().collect(Collectors.toList());
  }
}
