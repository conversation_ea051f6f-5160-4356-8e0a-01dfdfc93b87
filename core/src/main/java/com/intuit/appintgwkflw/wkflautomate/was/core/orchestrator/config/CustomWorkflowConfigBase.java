package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Handler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;


/**
 * This base class for config contains all the root components and methods for a template
 *
 * */
@Data
public class CustomWorkflowConfigBase implements ICustomWorkflowConfig{
  
  protected List<Record> records = new ArrayList<>();
  protected List<Attribute> attributes = new ArrayList<>();
  protected List<DataType> dataTypes = new ArrayList<>();
  protected List<ActionGroup> actionGroups = new ArrayList<>();
  protected List<Action> actions = new ArrayList<>();
  protected List<Parameter> parameters = new ArrayList<>();
  protected List<Handler> handlers = new ArrayList<>();
  protected List<ConfigTemplate> configTemplates = new ArrayList<>();


  @Override
  public Record getRecordObjForType(String recordType) {
    RecordType recordTypeEnum = RecordType.fromTypeOrValue(recordType);
    if (ObjectUtils.isNotEmpty(recordTypeEnum)) {
      return records.stream()
          .filter(record -> record.getId().equalsIgnoreCase(recordTypeEnum.getRecordType()))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  public Action getActionById(String actionId) {
    return actions.stream()
        .filter(action -> action.getId().equalsIgnoreCase(actionId))
        .findFirst()
        .orElse(null);
  }

  @Override
  public Map<String, ConfigTemplate> getTemplateMap() {
    return configTemplates.stream()
        .collect(Collectors.toMap(ConfigTemplate::getId, Function.identity()));
  }

  // The logic is moved to this file for unit tests
  // Return true if records are fully populated. False in case of any error.
  @VisibleForTesting
  protected boolean populateRecordConfig() {
    WorkflowLogger.logInfo("Init creating custom workflow config map");
    try {
      CustomWorkflowConfigUtil.populateRecordConfig(this);
    } catch (Exception e) {
      // Don't throw error as we don't want to stop the service
      WorkflowLogger.logError(e, "Error creating custom workflow config map");
      return false;
    }
    WorkflowLogger.logInfo("Finished Creating custom workflow config map");
    return true;
  }

  @VisibleForTesting
  protected boolean populateTemplateConfig() {
    WorkflowLogger.logInfo("Init creating custom workflow config map for Config Templates");
    try {
      CustomWorkflowConfigUtil.populateTemplateConfig(this);
    } catch (Exception e) {
      // Don't throw error as we don't want to stop the service
      WorkflowLogger.logError(e, "Error creating custom workflow config map for Config Templates");
      return false;
    }
    WorkflowLogger.logInfo("Finished Creating custom workflow config map for Config Templates");
    return true;
  }
}
