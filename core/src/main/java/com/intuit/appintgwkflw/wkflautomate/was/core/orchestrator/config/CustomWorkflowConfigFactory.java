package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;


import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * This factory class helps with the controlled rolled-out for the new config along with old-config
 *
 * This class is responsible for returning the correct config based on the entity type
 * */
@Component
@RequiredArgsConstructor
public class CustomWorkflowConfigFactory implements InitializingBean {
  
  private final OldCustomWorkflowConfig oldConfig;
  
  private final CustomConfigV2 newConfig;

  private final MigratedConfig migratedConfig;

  /**
   * This contains the Map of entities which have been migrated along with the pre-canned templates of the migrated entities
  * for example entry for key "invoice" -> ["invoiceoverduereminder", "invoiceunsentreminder"]
   * */
  private Map<String, List<String>> configTemplateToRecordTypeMap;

  public ICustomWorkflowConfig getInstanceOf(String entity) {

    /* return new-config only when the entities are in
    migrated-list and config for entities is not empty we also have an
    additional check for entity not null because of null being a possible value for getRecordObjForType
    function in configs */
    return (StringUtils.isNotEmpty(entity) &&
        migratedConfig.getEntities().contains(entity)) ? newConfig: oldConfig;
  }

  @Override
  public void afterPropertiesSet() {

    /**
     * This contains the Map of entities which have been migrated along with the pre-canned templates of the migrated entities
     * we create a map from using the enabledEntities from
     *  MigratedConfig ->  ["entities", "precanned-templates"]
     *  populates ->   Map<entityId, List<configTemplateIds>>
     * */
    configTemplateToRecordTypeMap =
        newConfig.getTemplateMap().entrySet().stream()
            .filter(entry -> migratedConfig.getEntities().contains(entry.getValue().getRecord()) &&
                migratedConfig.getTemplates().contains(entry.getValue().getId()) )
            .map(Map.Entry::getValue)
            .collect(Collectors.groupingBy(ConfigTemplate::getRecord,
                Collectors.mapping(ConfigTemplate::getId,
                    Collectors.toList())));
  }
  public List<String> getRecordTypeForTemplate(String templateId) {
    return configTemplateToRecordTypeMap.get(templateId);
  }


}
