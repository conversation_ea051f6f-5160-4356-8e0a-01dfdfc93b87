package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import java.util.Set;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "migrated-config")
public class MigratedConfig {
  private final Set<String> entities;

  private  final Set<String> templates;
  
}