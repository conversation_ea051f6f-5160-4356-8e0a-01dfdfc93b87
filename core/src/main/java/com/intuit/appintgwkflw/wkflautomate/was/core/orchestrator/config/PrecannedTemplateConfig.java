package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * Configure properties for precanned templates
 */
@Configuration
@ConfigurationProperties("precanned-config")
@RefreshScope
@Data
public class PrecannedTemplateConfig {

  private List<ConfigTemplate> templates;

  public Map<String, ConfigTemplate> getTemplateConfigMap(){
    return Optional.ofNullable(templates)
        .map(configTemplates -> configTemplates.stream()
            .collect(Collectors.toMap(ConfigTemplate::getId, Function.identity())))
        .orElse(Collections.emptyMap());
  }

}
