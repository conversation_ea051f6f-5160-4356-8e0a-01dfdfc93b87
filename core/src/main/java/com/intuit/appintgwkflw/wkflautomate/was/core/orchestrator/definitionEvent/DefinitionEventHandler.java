package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.DefinitionEvent;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * This class is an implementation to handle definition events for the given definitionId and
 * handlerType.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class DefinitionEventHandler implements WorkflowEventHandler<DefinitionEvent> {

  private DefinitionDetailsRepository definitionDetailsRepository;
  private MetricLogger metricLogger;
  private WASContextHandler wasContextHandler;

  /**
   * This method transform the json string to {@link DefinitionEvent}.
   *
   * @param event processEvent
   * @return {@link DefinitionEvent}
   */
  @Override
  public DefinitionEvent transform(String event) {
    EventingLoggerUtil.logInfo(
        "Transforming json string to DefinitionEvent. step=eventTransformation",
        this.getClass().getSimpleName());
    DefinitionEvent definitionEvent = ObjectConverter.fromJson(event, DefinitionEvent.class);
    WorkflowVerfiy.verifyNull(
        definitionEvent,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse event. payload=%s",
        event);
    return definitionEvent;
  }

  /**
   * This method execute the definitions events on the basis of given handlerId.
   *
   * <pre>
   *   1. Fetch the definitionDetails using definitionId.
   *   2. Process the definition events for the given handlerType
   * </pre>
   *
   * @param definitionEvent
   * @param headers
   */
  @Override
  public void execute(DefinitionEvent definitionEvent, Map<String, String> headers) {
    // validate handlerDefinitionEvent
    validate(definitionEvent);
    // fetch here definitionDetails
    Optional<DefinitionDetails> optionalDefinitionDetails =
        definitionDetailsRepository.findByDefinitionId(definitionEvent.getDefinitionId());

    WorkflowVerfiy.verify(optionalDefinitionDetails.isEmpty(), DEFINITION_NOT_FOUND);

    // add all the header in workflowDefinitionProcessEvent metaData
    if (MapUtils.isNotEmpty(headers)) {
      if (definitionEvent.getMetaData() == null) {
        definitionEvent.setMetaData(new HashMap<>());
      }
      definitionEvent.getMetaData().putAll(headers);
    }

    EventingLoggerUtil.logInfo(
        "Calling definition event processor on the basis of given handlerType=%s . step=calling DefinitionEventProcessor",
        this.getClass().getSimpleName(), definitionEvent.getHandlerType());

    addHandlerIdInContext(definitionEvent.getHandlerType());
    // On the basis of given handler type definition event processor will be called.
    DefinitionEventProcessors.getProcessor(
            DefinitionEventType.fromType(definitionEvent.getHandlerType()))
        .processEvents(
            DefinitionEventDetails.builder()
                .definitionDetails(optionalDefinitionDetails.get())
                .metaData(definitionEvent.getMetaData())
                .build());
  }

  private void validate(DefinitionEvent definitionEvent) {

    WorkflowVerfiy.verifyNull(
        definitionEvent, WorkflowError.INCORRECT_EVENT_PAYLOAD, "definitionEvent can not be null");

    // validate definitionId
    WorkflowVerfiy.verify(
        definitionEvent.getDefinitionId(),
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "definitionEvent do not have definition id");

    // validate handlerType
    WorkflowVerfiy.verify(
        definitionEvent.getHandlerType(),
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "definitionEvent do not have handler type");
  }

  /**
   * This method return the definition event.
   *
   * @return {@link EventEntityType}
   */
  @Override
  public EventEntityType getName() {
    return EventEntityType.DEFINITION_EVENT;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    metricLogger.logErrorMetric(MetricName.DEFINITION_EVENT, Type.EVENT_METRIC, e);
    EventingLoggerUtil.logError(
        "Error while handling definition event. step=definitionEventFailed definitionEvent=%s",
        this.getClass().getSimpleName(), event);
  }

  /**
   * adds handlerId read from payload to context
   * e.g. customReminderFetchTransaction
   * @param handlerId
   */
  private void addHandlerIdInContext(String handlerId) {
    wasContextHandler.addKey(WASContextEnums.HANDLER_ID, handlerId);
  }
}
