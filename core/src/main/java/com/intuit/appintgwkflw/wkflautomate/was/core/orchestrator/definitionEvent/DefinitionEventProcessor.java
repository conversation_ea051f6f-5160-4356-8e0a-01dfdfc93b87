package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;

/**
 * This processor is used to process events of a definition. Events can be fetch transactions for
 * custom reminder definitions, approval status for the custom approval definitions.
 *
 * <AUTHOR>
 */
public interface DefinitionEventProcessor {

  /** @return definition Event handler name */
  DefinitionEventType getName();

  /**
   * This method process all the events for the given definitionDetails
   *
   * @param definitionEventDetails
   */
  void processEvents(DefinitionEventDetails definitionEventDetails);
}
