package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionEventProcessorConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * This is a helper for definitionEventProcessor
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class DefinitionEventProcessorHelper {
  private final DefinitionEventProcessorConfig definitionEventProcessorConfig;

  /**
   * This method returns the handler id on the basis of given handlerType
   *
   * @param handlerType
   * @return
   */
  public String getHandlerId(String handlerType, DefinitionEventType definitionEventType) {
    WorkflowVerfiy.verify(
        MapUtils.isEmpty(definitionEventProcessorConfig.getDefinitionEventType())
            || Objects.isNull(
                definitionEventProcessorConfig.getDefinitionEventType().get(definitionEventType))
            || MapUtils.isEmpty(
                definitionEventProcessorConfig
                    .getDefinitionEventType()
                    .get(definitionEventType)
                    .getHandlerIds())
            || Objects.isNull(
                definitionEventProcessorConfig
                    .getDefinitionEventType()
                    .get(definitionEventType)
                    .getHandlerIds()
                    .get(handlerType)),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        "Handler Details can not be empty or null");
    return definitionEventProcessorConfig
        .getDefinitionEventType()
        .get(definitionEventType)
        .getHandlerIds()
        .get(handlerType);
  }
}
