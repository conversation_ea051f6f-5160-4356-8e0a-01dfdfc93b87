package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_REMINDER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_CHANGE_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IDEMPOTENCY_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ID_KEY;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.DefinitionEventDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.DefinitionEventProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.helpers.DefinitionEventProcessorHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectFetchTransactionsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COLON;
import static java.lang.Boolean.TRUE;
import static org.apache.commons.lang3.StringUtils.isEmpty;

/**
 * This class is an implementation to handle definition events. It will call the appconnect duzzits
 * for fetching the transactions.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CustomReminderFetchTransactionsProcessor implements DefinitionEventProcessor {

  private final AuthDetailsService authDetailsService;
  private final AppConnectWASClient wasHttpClient;
  private final AppConnectConfig appConnectConfig;
  private final DefinitionEventProcessorHelper definitionEventProcessorHelper;
  private final EventPublisherCapability eventPublisherCapability;

  /**
   * This method call app-connect custom reminder duzzits (customStart or customWait) for the of the
   * given entityChangeType and fetches all the transactions. Transactions will be published to the
   * kafka topic for triggering the camunda processes.
   *
   * @param definitionEventDetails
   */
  @Override
  public void processEvents(DefinitionEventDetails definitionEventDetails) {
    validate(definitionEventDetails);
    Map<String, ParameterDetails> parameterDetailsMap =
        getParameterDetailsMap(definitionEventDetails.getDefinitionDetails());
    WorkflowVerfiy.verify(
        MapUtils.isEmpty(parameterDetailsMap),
        WorkflowError.INVALID_PARAMETER_DETAILS,
        "parameterDetails can not be null");
    String appconnectHandlerId =
        definitionEventProcessorHelper.getHandlerId(
            definitionEventDetails.getMetaData().get(ENTITY_CHANGE_TYPE), getName());
    EventingLoggerUtil.logInfo(
        "Calling app-connect duzzit step=call Appconnect duzzit=%s",
        this.getClass().getSimpleName(), appconnectHandlerId);

    // fetch all the transactions and publish all the of them to the trigger topic

    // TODO will add here pagination support for fetching the transactions
    WASHttpResponse<AppConnectFetchTransactionsResponse> response =
        wasHttpClient.httpResponse(
            prepareAppconnectHttpRequest(
                definitionEventDetails, parameterDetailsMap, appconnectHandlerId));

    // verify response
    WorkflowVerfiy.verify(
        (!response.isSuccess2xx()
            || null == response.getResponse()
            || isEmpty(response.getResponse().getSuccess())
            || !TRUE.toString().equals(response.getResponse().getSuccess())),
        WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
        appconnectHandlerId,
        response.getError());
    if (!Objects.isNull(response.getResponse())) {
      AppConnectFetchTransactionsResponse appConnectFetchTransactionsResponse =
          response.getResponse();
      if (CollectionUtils.isNotEmpty(appConnectFetchTransactionsResponse.getOutput())) {
        String filterRecordType =
            getParameterFieldValue(parameterDetailsMap.get(WorkflowConstants.FILTER_RECORD_TYPE));
        EventingLoggerUtil.logInfo(
            "Publish  Transactions Started step=transactionsPublishStarted count=%s filterRecordType=%s",
            this.getClass().getSimpleName(),
            appConnectFetchTransactionsResponse.getOutput().size(),
            filterRecordType);

        for (Map<String, String> data : appConnectFetchTransactionsResponse.getOutput()) {
          try {
            String recordId = data.get(ID_KEY);
            EventingLoggerUtil.logInfo(
                "Publishing Transaction step=publishTransaction recordId=%s",
                this.getClass().getSimpleName(), recordId);
            eventPublisherCapability.publish(
                prepareTriggerEventHeaderEntity(definitionEventDetails, recordId),
                prepareWorkflowTriggerEvent(
                    filterRecordType,
                    definitionEventDetails.getMetaData().get(ENTITY_CHANGE_TYPE),
                    data,
                    definitionEventDetails.getDefinitionDetails().getWorkflowId()));
            EventingLoggerUtil.logInfo(
                "Published Transaction step=publishedTransaction recordId=%s",
                this.getClass().getSimpleName(), recordId);
          } catch (WorkflowEventException e) {
            // Log and Throw when WorkflowEventException is received
            EventingLoggerUtil.logInfo(
                "Publish Transaction Failed step=transactionPublishFailed error=%s filterRecordType=%s",
                this.getClass().getSimpleName(), e, filterRecordType);
            throw e;
          }
        }
        EventingLoggerUtil.logInfo(
            "Publish Transactions Successful step=transactionsPublishSuccessful",
            this.getClass().getSimpleName());
      }
    }
  }

  /**
   * This method prepare the wasHttp request for the appconnect
   *
   * @param definitionEventDetails
   * @param parameterDetailsMap
   * @param handlerId
   * @return
   */
  private WASHttpRequest<MultiValueMap<String, String>, AppConnectFetchTransactionsResponse>
      prepareAppconnectHttpRequest(
          DefinitionEventDetails definitionEventDetails,
          Map<String, ParameterDetails> parameterDetailsMap,
          String handlerId) {
    String entityChangeType = definitionEventDetails.getMetaData().get(ENTITY_CHANGE_TYPE);
    AuthDetails authDetails =
        authDetailsService.getAuthDetailsFromRealmId(
            String.valueOf(definitionEventDetails.getDefinitionDetails().getOwnerId()));
    return WASHttpRequest
        .<MultiValueMap<String, String>, AppConnectFetchTransactionsResponse>builder()
        .url(prepareAppconnectRequestEndpoint(handlerId, authDetails))
        .httpMethod(HttpMethod.POST)
        .requestHeaders(prepareAppconnectRequestHeaders(definitionEventDetails, authDetails))
        .request(prepareAppconnectRequestParamInputs(entityChangeType, parameterDetailsMap))
        .responseType(new ParameterizedTypeReference<AppConnectFetchTransactionsResponse>() {})
        .build();
  }

  /**
   * This method returns all the request params inputs required for calling the appconnect duzzit
   * request.
   *
   * @param entityChangeType
   * @param parameterDetailsMap
   * @return
   */
  private MultiValueMap<String, String> prepareAppconnectRequestParamInputs(
      String entityChangeType, Map<String, ParameterDetails> parameterDetailsMap) {
    MultiValueMap<String, String> inputs = new LinkedMultiValueMap<>();
    addAppconnectRequestInputParameter(
        WorkflowConstants.FILTER_RECORD_TYPE,
        parameterDetailsMap.get(WorkflowConstants.FILTER_RECORD_TYPE),
        inputs);
    // for every entityChange type we have different condition
    if (WorkflowConstants.CUSTOM_WAIT_EVENT.equals(entityChangeType)) {
      addAppconnectRequestInputParameter(
          WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS,
          parameterDetailsMap.get(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS),
          inputs);
    }
    if (WorkflowConstants.NEW_CUSTOM_START.equals(entityChangeType)) {
      addAppconnectRequestInputParameter(
          WorkflowConstants.FILTER_CONDITION,
          parameterDetailsMap.get(WorkflowConstants.FILTER_CONDITION),
          inputs);
    }
    return inputs;
  }

  /**
   * This method builds the appconnect request endpoint
   *
   * @param handlerId
   * @param authDetails
   * @return
   */
  private String prepareAppconnectRequestEndpoint(
      final String handlerId, final AuthDetails authDetails) {
    return appConnectConfig
        .getConnectorEndpoint()
        .concat(handlerId)
        .concat(WorkflowConstants.APPEND_SUBSCRIPTION_ID)
        .concat(authDetails.getSubscriptionId());
  }

  /**
   * This method builds all the request headers required for the calling appconnect duzzits
   *
   * @param definitionEventDetails
   * @param authDetails
   * @return
   */
  private HttpHeaders prepareAppconnectRequestHeaders(
      DefinitionEventDetails definitionEventDetails, final AuthDetails authDetails) {
    // prepare request headers
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    // call offline ticket to renew the realm ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
    requestHeaders.add(IDEMPOTENCY_KEY, getIdempotencyKey(definitionEventDetails));
    // populate retry of response in case of error at appconnect
    requestHeaders.add(WorkflowConstants.RETRY_ON_ERROR, Boolean.TRUE.toString());

    return requestHeaders;
  }

  /**
   * This method find the start event element form the definitionDetails BPMN data. It reads the
   * parameter details from the extension variables of startElement.
   *
   * <pre>
   *     {"FilterCloseTaskConditions":{"fieldValue":["txn_sent"]},
   *     "FilterCondition":{"fieldValue":["{\"rules\":[{\"parameterName\":\"TxnAmount\",\"conditionalExpression\":\"GTE 0\",\"$sdk_validated\":true},{\"parameterName\":\"TxnDueDays\",\"conditionalExpression\":\"AF 1\",\"$sdk_validated\":true}]}"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false},"FilterRecordType":{"fieldValue":["invoice"]}}
   *
   * </pre>
   *
   * @param definitionDetails
   * @return
   */
  private Map<String, ParameterDetails> getParameterDetailsMap(
      DefinitionDetails definitionDetails) {
    // get the start element from the definition BPMN.
    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(
            BpmnProcessorUtil.readBPMN(definitionDetails.getDefinitionData()));
    Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
    // This map contains all the parameterDetails parameter key value in the extension variables for
    // startElement of BPMN.
    Optional<CamundaProperty> propertyMap =
        BpmnProcessorUtil.getCamundaProperty(
            startEventElement, WorkFlowVariables.PARAMETERS_KEY.getName());
    if (propertyMap.isPresent()) {
      parameterDetailsMap.putAll(
          ObjectConverter.fromJson(
              propertyMap.get().getCamundaValue(),
              new TypeReference<Map<String, ParameterDetails>>() {}));
    }
    return parameterDetailsMap;
  }

  /**
   * Adds an request param input
   *
   * @param condition
   * @param parameterDetails
   * @param inputs
   */
  private void addAppconnectRequestInputParameter(
      String condition, ParameterDetails parameterDetails, MultiValueMap<String, String> inputs) {
    inputs.add(condition, getParameterFieldValue(parameterDetails));
  }

  /**
   * This method return the first index value of the parameterDetails field value.
   *
   * @param parameterDetails
   * @return
   */
  private String getParameterFieldValue(ParameterDetails parameterDetails) {
    WorkflowVerfiy.verify(
        parameterDetails == null || CollectionUtils.isEmpty(parameterDetails.getFieldValue()),
        WorkflowError.HANDLER_PARAMETER_NOT_FOUND,
        "parameterDetails can not be null or empty");
    return parameterDetails.getFieldValue().get(0);
  }

  /**
   * This method use to validate all the mandatory fields.
   *
   * @param definitionEventDetails
   */
  private void validate(DefinitionEventDetails definitionEventDetails) {

    WorkflowVerfiy.verifyNull(
        definitionEventDetails,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "processEventDetails can not be null");

    WorkflowVerfiy.verifyNull(
        definitionEventDetails.getDefinitionDetails(),
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "processEventDetails do not have definitionDetails");

    WorkflowVerfiy.verify(
        definitionEventDetails.getMetaData() == null
            || definitionEventDetails.getMetaData().get(ENTITY_CHANGE_TYPE) == null,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "processEventDetails do not have entity change type");
  }

  /**
   * This method prepare trigger event headers.
   *
   * @param definitionEventDetails
   * @param recordId
   * @return
   */
  private EventHeaderEntity prepareTriggerEventHeaderEntity(
      DefinitionEventDetails definitionEventDetails, String recordId) {
    String offeringId =
        StringUtils.isNoneBlank(
                definitionEventDetails.getMetaData().get(EventHeaderConstants.OFFERING_ID))
            ? definitionEventDetails.getMetaData().get(EventHeaderConstants.OFFERING_ID)
            : definitionEventDetails.getDefinitionDetails().getTemplateDetails().getOfferingId();
    return EventHeaderEntity.builder()
        .publishEventType(PublishEventType.TRIGGER)
        .eventEntityType(EventEntityType.TRIGGER)
        .entityId(recordId)
        .idempotencyKey(getIdempotencyKey(definitionEventDetails))
        .tid(definitionEventDetails.getMetaData().get(EventHeaderConstants.INTUIT_TID))
        .ownerId(String.valueOf(definitionEventDetails.getDefinitionDetails().getOwnerId()))
        .offeringId(offeringId)
        .handlerId(getName().getEventType())
        .build();
  }

  /**
   * This method returns the {@link Trigger} class object.
   *
   * @param filterRecordType
   * @param entityChangeType
   * @param data
   * @param providerWorkflowId
   * @return {@link Trigger}
   */
  private Trigger prepareWorkflowTriggerEvent(
      String filterRecordType,
      String entityChangeType,
      Map<String, String> data,
      String providerWorkflowId) {
    MetaData metaData =
        MetaData.builder()
            .workflow(CUSTOM_REMINDER)
            .entityType(filterRecordType)
            .entityChangeIdentifier(new EntityChangeIdentifier(entityChangeType))
            .entityId(data.get(ID_KEY))
            .providerWorkflowId(providerWorkflowId)
            .build();
    return new Trigger(metaData, null, Map.of(filterRecordType, data));
  }

  /**
   * This method used to get the idempotent key for the given processEventDetails. In this method we
   * are concatenating definitionId, entityChangeType and random UUID separated by Colon.
   *
   * <pre>
   *   e.g.
   *   {definitionId}:customStart:{UUID}
   *   {definitionId}:customWait:{UUID}
   *
   * </pre>
   *
   * @param definitionEventDetails
   * @return
   */
  private String getIdempotencyKey(DefinitionEventDetails definitionEventDetails) {
    return new StringBuilder(definitionEventDetails.getDefinitionDetails().getDefinitionId())
        .append(COLON)
        .append(definitionEventDetails.getMetaData().get(ENTITY_CHANGE_TYPE))
        .append(COLON)
        .append(UUID.randomUUID().toString())
        .toString();
  }

  @Override
  public DefinitionEventType getName() {
    return DefinitionEventType.CUSTOM_REMINDER_FETCH_TRANSACTIONS;
  }
}
