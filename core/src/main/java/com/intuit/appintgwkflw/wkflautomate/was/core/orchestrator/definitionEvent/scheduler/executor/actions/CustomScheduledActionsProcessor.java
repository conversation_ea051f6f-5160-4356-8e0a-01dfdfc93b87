package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.TRIGGER_PROCESS_V2;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import org.apache.commons.lang.BooleanUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.RuleLine;

import lombok.AllArgsConstructor;

@Component
@AllArgsConstructor
public class CustomScheduledActionsProcessor implements WorkflowScheduleActionProcessor {
  private WASContextHandler wasContextHandler;
  private DefinitionServiceHelper definitionServiceHelper;
  private RunTimeService runTimeService;
  private MetricLogger metricLogger;
  private AuthDetailsService authDetailsService;
  private SchedulingSvcConfig schedulingSvcConfig;

  /**
   * This method returns the custom scheduled actions workflow name
   *
   * @return
   */
  @Override
  public WorkflowNameEnum getWorkflowName() {
    return WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
  }

  /**
   * This method creates a trigger payload and starts a custom scheduled actions process.
   *
   * @param scheduleDetails
   * @param eventScheduleMessageData
   */
  @Override
  public Map<String, String> process(
      final SchedulerDetails scheduleDetails,
      final EventScheduleMessageData eventScheduleMessageData) {
    try {
      // if the definition is migrated to scheduling service, skip the processing.
      if(schedulingSvcConfig.isNumaflowProcessingEnabled() && BooleanUtils.isTrue(scheduleDetails.getIsMigrated())) {
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "ess schedule based trigger process skipped for custom scheduled actions definitionId=%s | ownerId=%s | scheduleId=%s as it is migrated to scheduling service",
                        scheduleDetails.getDefinitionDetails().getDefinitionId(),
                        scheduleDetails.getOwnerId(),
                        scheduleDetails.getSchedulerId())
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .downstreamServiceName(DownstreamServiceName.WAS_ESS_MESSAGE_PROCESSOR)
                    .className(this.getClass().getSimpleName()));
        return Collections.emptyMap();
      }
      // add all the keys to the context for calling downstream services
      addKeysToContextHandler(scheduleDetails, eventScheduleMessageData);

      DefinitionDetails definitionDetails = scheduleDetails.getDefinitionDetails();
      Optional<List<DefinitionDetails>> optionalDMNDefinitionDetails =
          definitionServiceHelper.findByParentId(definitionDetails.getDefinitionId());
      if (optionalDMNDefinitionDetails.isPresent()) {
        DefinitionDetails dmnDefinition = optionalDMNDefinitionDetails.get().get(0);

        List<RuleLine.Rule> rulesList =
                getRulesFromDMNDefinition(dmnDefinition);

        TransactionEntity transactionEntity =
            TriggerUtil.prepareTransactionEntity(
                definitionDetails.getTemplateDetails(),
                definitionDetails.getRecordType(),
                WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED,
                definitionDetails.getWorkflowId(),
                rulesList,
                wasContextHandler, null);

        /**
         * setting source to determine process triggered in WAS post consumption of ESS message
         * TODO: clean up this post 100% rollout and migration
         * https://jira.intuit.com/browse/QBOES-21483
         */
        transactionEntity.getV3EntityPayload().put(WorkflowConstants.SOURCE, WorkflowConstants.WAS_TRIGGER);
        WorkflowGenericResponse response =
            runTimeService.processTriggerMessageV2(transactionEntity.getV3EntityPayload());
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "ess schedule based trigger process completed for custom scheduled actions definitionId=%s | ownerId=%s | response=%s",
                        definitionDetails.getDefinitionId(),
                        scheduleDetails.getOwnerId(),
                        ObjectConverter.toJson(response))
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .downstreamServiceName(DownstreamServiceName.WAS_ESS_MESSAGE_PROCESSOR)
                    .className(this.getClass().getSimpleName()));
        return Map.of(transactionEntity.getEntityId(),WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED);
      }
      return Collections.emptyMap();
    } catch (Exception exception) {
      WorkflowLogger.logError(
          exception,
          "Error in starting custom scheduled actions process for scheduleId=%s | definitionId=%s | ownerId=%s",
          scheduleDetails.getSchedulerId(),
          scheduleDetails.getDefinitionDetails().getDefinitionId(),
          scheduleDetails.getOwnerId());

      metricLogger.logErrorMetric(TRIGGER_PROCESS_V2, Type.API_METRIC, exception);
      throw exception;
    }
  }

  /**
   * This method adds all keys to the context that the downstream services require.
   *
   * @param schedulerDetails
   * @param eventScheduleMessageData
   */
  private void addKeysToContextHandler(
      SchedulerDetails schedulerDetails, EventScheduleMessageData eventScheduleMessageData) {
    wasContextHandler.addKey(
        WASContextEnums.OWNER_ID, String.valueOf(schedulerDetails.getOwnerId()));
    wasContextHandler.addKey(
        WASContextEnums.IDEMPOTENCY_KEY, eventScheduleMessageData.getMessageId());
    wasContextHandler.addKey(
        WASContextEnums.OFFERING_ID,
        schedulerDetails.getDefinitionDetails().getTemplateDetails().getOfferingId());
    AuthDetails authDetails =
        authDetailsService.getAuthDetailsFromRealmId(
            wasContextHandler.get(WASContextEnums.OWNER_ID));

    Authorization authorization = new Authorization();
    authorization.set(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
    wasContextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
  }

  private List<RuleLine.Rule> getRulesFromDMNDefinition(DefinitionDetails dmnDefinition) {
    JSONObject definitionPlaceholder = new JSONObject(dmnDefinition.getPlaceholderValue());
    @SuppressWarnings({ "unchecked", "rawtypes" })
	List<Object> ruleLinesList =
        (ArrayList) definitionPlaceholder.toMap().get(WorkflowConstants.RULE_LINE_VARIABLES);
    List<RuleLine.Rule> rulesList =
        ruleLinesList.stream()
            .map(
                ruleLine -> {
                  Map<String, String> ruleMap =
                      ObjectConverter.convertObject(
                          ruleLine, new TypeReference<Map<String, String>>() {});
                  RuleLine.Rule rule = new RuleLine.Rule();
                  rule.setParameterName(ruleMap.get(WorkflowConstants.PARAMETER_NAME));
                  rule.setConditionalExpression(ruleMap.get(WorkflowConstants.CONDITIONAL_EXPRESSION));
                  return rule;
                })
            .collect(Collectors.toList());

    return rulesList;
  }
}
