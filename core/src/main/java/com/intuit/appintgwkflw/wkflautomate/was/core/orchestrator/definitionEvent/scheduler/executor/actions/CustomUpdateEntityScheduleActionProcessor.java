package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 */
/**
 * This class is used to poll data for customUpdddateEntity pre-canned reminder.
 */
@Component
@AllArgsConstructor
public class CustomUpdateEntityScheduleActionProcessor implements WorkflowScheduleActionProcessor {

  private CustomReminderScheduleActionProcessor customReminderScheduleActionProcessor;

  @Override
  public WorkflowNameEnum getWorkflowName() {
    return WorkflowNameEnum.CUSTOM_UPDATE_ENTITY;
  }

  @Override
  public Map<String, String> process(SchedulerDetails schedulerDetails,
      EventScheduleMessageData eventScheduleMessageData) {
    return customReminderScheduleActionProcessor.process(schedulerDetails, eventScheduleMessageData);
  }
}
