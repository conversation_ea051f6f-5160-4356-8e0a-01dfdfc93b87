package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.foundation.workflow.scheduling.Execution;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.RuleLine;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.TRIGGER_PROCESS_V2;

/**
 * The `SchedulingScheduledActionsProcessor` class is responsible for processing scheduled actions
 * within a workflow. It implements the `WorkflowScheduleActionProcessor` interface and provides
 * methods to handle workflow scheduling events and trigger processes based on definitions.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SchedulingScheduledActionsProcessor implements WorkflowScheduleActionProcessor {
    private final WASContextHandler wasContextHandler;
    private final AuthDetailsService authDetailsService;
    private final DefinitionServiceHelper definitionServiceHelper;
    private final RunTimeService runTimeService;
    private final MetricLogger metricLogger;
    private final SchedulingService schedulingService;
    private final SchedulingSvcConfig schedulingSvcConfig;

    /**
     * Retrieves the workflow name associated with this processor.
     *
     * @return `WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS` indicating the workflow name.
     */
    @Override
    public WorkflowNameEnum getWorkflowName() {
        return WorkflowNameEnum.SCHEDULED_ACTIONS;
    }

    /**
     * Processes the scheduled actions based on the provided scheduler details and event schedule
     * message data. Currently returns an empty map.
     *
     * @param schedulerDetails the details of the scheduler.
     * @param eventScheduleMessageData the data related to the event schedule message.
     * @return an empty map.
     */
    @Override
    public Map<String, String> process(SchedulerDetails schedulerDetails, EventScheduleMessageData eventScheduleMessageData) {
        return new HashMap<String, String>();
    }

    /**
     * Processes the scheduled actions based on the provided definition details and scheduling event.
     * It prepares the transaction entity, triggers the process, and logs the response.
     *
     * @param definitionDetails the details of the definition.
     * @param schedulingEvent the scheduling event data.
     * @return a map containing the entity ID and change type if successful, otherwise an empty map.
     * @throws Exception if an error occurs during processing.
     */
    @Override
    public Map<String, String> process(
            final DefinitionDetails definitionDetails, final Execution schedulingEvent) {
        try {
            if(!schedulingSvcConfig.isNumaflowProcessingEnabled() && schedulingService.isMigrated(definitionDetails)){
                WorkflowLogger.info(
                        () ->
                                WorkflowLoggerRequest.builder()
                                        .message(
                                                "Scheduling trigger process skipped for custom scheduled actions definitionId=%s | ownerId=%s | referenceId=%s as the processing is disabled",
                                                definitionDetails.getDefinitionId(),
                                                definitionDetails.getOwnerId(),
                                                schedulingEvent.getReferenceId())
                                        .downstreamComponentName(DownstreamComponentName.WAS)
                                        .downstreamServiceName(DownstreamServiceName.WAS_ESS_MESSAGE_PROCESSOR)
                                        .className(this.getClass().getSimpleName()));
                return Collections.emptyMap();
            }
            // add all the keys to the context for calling downstream services
            addKeysToContextHandler(definitionDetails, schedulingEvent);
            Optional<List<DefinitionDetails>> optionalDMNDefinitionDetails =
                    definitionServiceHelper.findByParentId(definitionDetails.getDefinitionId());
            if (optionalDMNDefinitionDetails.isPresent()) {
                DefinitionDetails dmnDefinition = optionalDMNDefinitionDetails.get().get(0);

                List<RuleLine.Rule> rulesList =
                        getRulesFromDMNDefinition(dmnDefinition);

                TransactionEntity transactionEntity =
                        TriggerUtil.prepareTransactionEntity(
                                definitionDetails.getTemplateDetails(),
                                definitionDetails.getRecordType(),
                                WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED,
                                null,
                                rulesList,
                                wasContextHandler, definitionDetails.getDefinitionKey());

                transactionEntity.getV3EntityPayload().put(WorkflowConstants.SOURCE, WorkflowConstants.WAS_TRIGGER);
                WorkflowGenericResponse response =
                        runTimeService.processTriggerMessageV2(transactionEntity.getV3EntityPayload());
                WorkflowLogger.info(
                        () ->
                                WorkflowLoggerRequest.builder()
                                        .message(
                                                "schedule based trigger process completed for custom scheduled actions definitionId=%s | ownerId=%s | response=%s",
                                                definitionDetails.getDefinitionId(),
                                                definitionDetails.getOwnerId(),
                                                ObjectConverter.toJson(response))
                                        .downstreamComponentName(DownstreamComponentName.WAS)
                                        .downstreamServiceName(DownstreamServiceName.WAS_ESS_MESSAGE_PROCESSOR)
                                        .className(this.getClass().getSimpleName()));
                return Map.of(transactionEntity.getEntityId(),WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED);
            }
            return Collections.emptyMap();
        } catch (Exception exception) {
            WorkflowLogger.logError(
                    exception,
                    "Error in starting custom scheduled actions process for scheduleId=%s | definitionId=%s | ownerId=%s",
                    schedulingEvent.getReferenceId(),
                    definitionDetails.getDefinitionId(),
                    definitionDetails.getOwnerId());

            metricLogger.logErrorMetric(TRIGGER_PROCESS_V2, Type.API_METRIC, exception);
            throw exception;
        }
    }

    /**
     * Adds necessary keys to the context handler for downstream service calls.
     *
     * @param definitionDetails the details of the definition.
     * @param schedulingEvent the scheduling event data.
     */
    private void addKeysToContextHandler(
            DefinitionDetails definitionDetails, Execution schedulingEvent) {
        wasContextHandler.addKey(
                WASContextEnums.OWNER_ID, String.valueOf(definitionDetails.getOwnerId()));
        wasContextHandler.addKey(
                WASContextEnums.IDEMPOTENCY_KEY, schedulingEvent.getReferenceId());
        wasContextHandler.addKey(
                WASContextEnums.OFFERING_ID,
                definitionDetails.getTemplateDetails().getOfferingId());
        AuthDetails authDetails =
                authDetailsService.getAuthDetailsFromRealmId(
                        wasContextHandler.get(WASContextEnums.OWNER_ID));

        Authorization authorization = new Authorization();
        authorization.set(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
        wasContextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
    }

    /**
     * Extracts rules from the DMN definition and converts them into a list of `RuleLine.Rule` objects.
     *
     * @param dmnDefinition the DMN definition details.
     * @return a list of rules extracted from the DMN definition.
     */
    private List<RuleLine.Rule> getRulesFromDMNDefinition(DefinitionDetails dmnDefinition) {
        JSONObject definitionPlaceholder = new JSONObject(dmnDefinition.getPlaceholderValue());
        @SuppressWarnings({ "unchecked", "rawtypes" })
        List<Object> ruleLinesList =
                (ArrayList) definitionPlaceholder.toMap().get(WorkflowConstants.RULE_LINE_VARIABLES);
        List<RuleLine.Rule> rulesList =
                ruleLinesList.stream()
                        .map(
                                ruleLine -> {
                                    Map<String, String> ruleMap =
                                            ObjectConverter.convertObject(
                                                    ruleLine, new TypeReference<Map<String, String>>() {});
                                    RuleLine.Rule rule = new RuleLine.Rule();
                                    rule.setParameterName(ruleMap.get(WorkflowConstants.PARAMETER_NAME));
                                    rule.setConditionalExpression(ruleMap.get(WorkflowConstants.CONDITIONAL_EXPRESSION));
                                    return rule;
                                })
                        .collect(Collectors.toList());

        return rulesList;
    }
}
