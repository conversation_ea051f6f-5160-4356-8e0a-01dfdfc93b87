package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.WorkflowScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import java.util.EnumMap;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * Class acts as factory to return Schedule Action processors
 *
 * <AUTHOR>
 */
@UtilityClass
public class WorkflowScheduleActionProcessorFactory {

  private static final Map<WorkflowNameEnum, WorkflowScheduleActionProcessor> SCHEDULE_PROCESSOR_MAP =
      new EnumMap<>(WorkflowNameEnum.class);

  /**
   * Add the processor for the given workflow name
   *
   * @param workflowName
   * @param scheduleProcessor
   */
  public void addProcessor(WorkflowNameEnum workflowName, WorkflowScheduleActionProcessor scheduleProcessor) {
    SCHEDULE_PROCESSOR_MAP.put(workflowName, scheduleProcessor);
  }

  /**
   * Gets the processor for the give workflow name
   *
   * @param workflowName
   * @return
   */
  public WorkflowScheduleActionProcessor getProcessor(WorkflowNameEnum workflowName) {
    return SCHEDULE_PROCESSOR_MAP.get(workflowName);
  }
}
