package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import org.springframework.stereotype.Component;

/** <AUTHOR> */

/** This class is responsible for transforming the ESS message received from the ESS sqs-queue */
@Component
public class ESSScheduleMessageExecutor extends ScheduleMessageExecutor {

  public ESSScheduleMessageExecutor(
      SchedulerDetailsRepository schedulerDetailsRepository, WASContextHandler wasContextHandler) {
    super(schedulerDetailsRepository, wasContextHandler);
  }

  /**
   * This method transform the message to EventScheduleMessageData
   *
   * @param message
   * @return
   */
  @Override
  public EventScheduleMessageData transformMessage(String message) {
    EventingLoggerUtil.logInfo(
        "Transforming ESS Message. step=transform to EventScheduleMessage",
        this.getClass().getSimpleName());

    // convert EventScheduleMessage
    EventScheduleMessage eventScheduleMessage =
        ObjectConverter.fromJson(message, EventScheduleMessage.class);
    if (eventScheduleMessage == null) {
      return null;
    }
    EventingLoggerUtil.logInfo(
        "Transforming ESS Message. step=transform to EventScheduleMessageData",
        this.getClass().getSimpleName());
    // In EventScheduleMessage we are getting EventScheduleMessageData as double quoted string
    // get here EventScheduleMessageData
    EventScheduleMessageData eventScheduleMessageData =
        ObjectConverter.fromJson(
            eventScheduleMessage.getMessageData(), EventScheduleMessageData.class);
    // set messageId
    if (eventScheduleMessageData != null) {
      eventScheduleMessageData.setMessageId(eventScheduleMessage.getMessageId());
    }
    return eventScheduleMessageData;
  }
}
