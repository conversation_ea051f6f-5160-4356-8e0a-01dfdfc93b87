package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;

/** <AUTHOR> */

/** This class process the message received from the sqs-queue */
@AllArgsConstructor
public abstract class ScheduleMessageExecutor {

  private SchedulerDetailsRepository schedulerDetailsRepository;
  private WASContextHandler wasContextHandler;

  public abstract EventScheduleMessageData transformMessage(String message);

  /**
   * This method calls the abstract transformMessage method to transform the message and executes
   * the message.
   *
   * @param message
   */
  public void transformAndExecute(String message) {
    try {
      EventScheduleMessageData eventScheduleMessageData = transformMessage(message);
      Optional.ofNullable(eventScheduleMessageData)
          .ifPresentOrElse(
              messageData -> {
                wasContextHandler.addKey(
                    WASContextEnums.INTUIT_TID, eventScheduleMessageData.getMessageId());
                EventingLoggerUtil.logInfo(
                    "Executing Schedule Message. step=start_executing",
                    this.getClass().getSimpleName());
                execute(messageData);
              },
              () ->
                  EventingLoggerUtil.logWarning(
                      "Unable to parse the message=%s", this.getClass().getSimpleName(), message));
    } catch (Exception exception) {
      WorkflowLogger.logError(
          exception, "Error in processing ESS message for eventScheduleMessageData=%s", message);
      throw exception;
    }
  }

  /**
   * This method retrieves the scheduler details using the schedulerId and calls the
   * workflowScheduleProcessor that is mapped to the workflowName.
   *
   * @param eventScheduleMessageData
   */
  private void execute(EventScheduleMessageData eventScheduleMessageData) {
    // check in database schedulerDetails exists or not
    Optional<SchedulerDetails> schedulerDetailsOptional =
        schedulerDetailsRepository.findById(
            EventScheduleServiceUtil.getLocalId(eventScheduleMessageData.getScheduleId()));

    if (schedulerDetailsOptional.isEmpty()) {
      // This case could have occurred because of the following reason: Let's say WAS
      // deletes event schedule details from the DB and ESS; however, it's possible that
      // ESS already scheduled an event and published it to SQS, before WAS deleted
      // scheduler details from the ESS.
      // ##TODO - Check how we handle this behavior in the case of appconnect.
      EventingLoggerUtil.logWarning(
          "Processing Schedule Message. step=noSchedulerDetailsFound schedulerId=%s",
          this.getClass().getSimpleName(), eventScheduleMessageData.getScheduleId());
      return;
    }
    SchedulerDetails schedulerDetails = schedulerDetailsOptional.get();
    if (WasUtils.isTestDriveRealm(String.valueOf(schedulerDetails.getOwnerId()))) {
      // we should skip processing
      EventingLoggerUtil.logInfo(
          "Processing Schedule Message. step=skipTestDriveRealm schedulerId=%s realmId=%s",
          this.getClass().getSimpleName(), eventScheduleMessageData.getScheduleId(),
          schedulerDetails.getOwnerId());
      return;
    }
    DefinitionDetails definitionDetails = schedulerDetails.getDefinitionDetails();
    /**
     * stop execution if definition is not enabled: !(status = ENABLED and internal_status = null)
     */
    if (!(definitionDetails.getStatus().equals(Status.ENABLED)
        && Objects.isNull(definitionDetails.getInternalStatus()))) {

      EventingLoggerUtil.logInfo(
          "Schedule message processing stopped - definition not enabled. step=stopProcessing schedulerId=%s, scheduleAction=%s, and definitionId=%s",
          this.getClass().getSimpleName(),
          schedulerDetails.getSchedulerId(),
          schedulerDetails.getSchedulerAction(),
          definitionDetails.getDefinitionId());
      return;
    }

    String workflowName = getWorkflowName(definitionDetails);
    wasContextHandler.addKey(WASContextEnums.WORKFLOW, workflowName);
    EventingLoggerUtil.logInfo(
        "Processing Schedule Message. step=startProcessing schedulerId=%s, scheduleAction=%s, and workflowName=%s",
        this.getClass().getSimpleName(),
        schedulerDetails.getSchedulerId(),
        schedulerDetails.getSchedulerAction(),
        workflowName);
    WorkflowScheduleActionProcessorFactory.getProcessor(WorkflowNameEnum.fromName(workflowName))
        .process(schedulerDetails, eventScheduleMessageData);
    EventingLoggerUtil.logInfo(
        "Processing Schedule Message. step=completeProcessing schedulerId=%s, scheduleAction=%s, and workflowName=%s",
        this.getClass().getSimpleName(),
        schedulerDetails.getSchedulerId(),
        schedulerDetails.getSchedulerAction(),
        workflowName);
  }

  private String getWorkflowName(DefinitionDetails definitionDetails) {
    return definitionDetails.getTemplateDetails().getTemplateName();
  }
}
