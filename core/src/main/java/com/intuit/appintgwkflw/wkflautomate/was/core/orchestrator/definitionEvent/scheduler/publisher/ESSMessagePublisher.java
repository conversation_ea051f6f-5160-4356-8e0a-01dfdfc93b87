package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.PublishResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Message publisher for ESS testing
 */
@Component
@RequiredArgsConstructor
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class ESSMessagePublisher {

  private final AmazonSQS amazonSQS;
  private final SchedulerDetailsRepository schedulerDetailsRepository;
  private final DefinitionDetailsRepository definitionDetailsRepository;
  @Value("${ess-sqs.sqsQueueUrl}")
  private String sqsQueueUrl;
  @Value("${ess-sqs.suffix:}")
  private String suffix;
  private final WASContextHandler wasContextHandler;

  /**
   * This method publish a sqs message for given definitionId.
   *
   * @param definitionId
   */
  public WorkflowGenericResponse publish(String definitionId) {
    definitionDetailsRepository.findByDefinitionIdAndOwnerId(definitionId,
            Long.parseLong(wasContextHandler.get(WASContextEnums.OWNER_ID)))
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

    List<SchedulerDetails> schedulerDetailsList = schedulerDetailsRepository.findByDefinitionDetails(
            DefinitionDetails.builder().definitionId(definitionId).build())
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.SCHEDULE_DETAILS_NOT_FOUND));
    Map<String, String> actiontidMap = new HashMap<>();
    schedulerDetailsList.forEach(schedulerDetails -> {
      SendMessageRequest sendMessageStandardQueue = new SendMessageRequest().withQueueUrl(
              suffix + sqsQueueUrl)
          .withMessageBody(prepareSqsMessage(definitionId, schedulerDetails, actiontidMap));
      amazonSQS.sendMessage(sendMessageStandardQueue);

    });
    return WorkflowGenericResponse.builder()
        .response(PublishResponse.builder().actionTidMap(actiontidMap).build()).build();
  }

  /**
   * This method prepares the sqs message
   *
   * @param definitionId
   * @param schedulerDetails
   * @param actiontidMap
   * @return
   */
  private String prepareSqsMessage(String definitionId, SchedulerDetails schedulerDetails,
      Map<String, String> actiontidMap) {
    EventScheduleMessage eventScheduleMessage = new EventScheduleMessage();
    eventScheduleMessage.setMessageId(definitionId + "+" + UUID.randomUUID().toString());
    EventScheduleMessageData eventScheduleMessageData = new EventScheduleMessageData();
    eventScheduleMessageData.setScheduleId(schedulerDetails.getSchedulerId());
    eventScheduleMessage.setMessageData(ObjectConverter.toJson(eventScheduleMessageData));
    actiontidMap.put(schedulerDetails.getSchedulerAction().getAction(),
        eventScheduleMessage.getMessageId());
    return ObjectConverter.toJson(eventScheduleMessage);
  }

}
