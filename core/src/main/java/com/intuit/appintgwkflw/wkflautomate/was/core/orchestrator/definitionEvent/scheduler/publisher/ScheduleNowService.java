package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.PublishResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.v4.workflows.trigger.Trigger;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class ScheduleNowService {

	private final SchedulerDetailsRepository schedulerDetailsRepository;
	private final DefinitionDetailsRepository definitionDetailsRepository;
	private final WASContextHandler wasContextHandler;
	private final SchedulingService schedulingService;
	private final MetricLogger metricLogger;

	/**
	 * This method fetches the Definition & schedule details. Calls the
	 * corresponding duzzit. to fetch the details. Starts the process if details are
	 * found.
	 *
	 *
	 * @param triggerNowRequest
	 */
	@Metric(name = MetricName.TRIGGER_PROCESS_NOW, type = Type.API_METRIC)
	public WorkflowGenericResponse runNow(TriggerNowRequest triggerNowRequest) {
		try {
			String definitionId = triggerNowRequest.getDefinitionId();
			DefinitionDetails definitionDetails = definitionDetailsRepository
					.findByDefinitionIdAndOwnerId(definitionId,
							Long.parseLong(wasContextHandler.get(WASContextEnums.OWNER_ID)))
					.orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

			WorkflowVerfiy.verify(
					!(definitionDetails.getStatus().equals(Status.ENABLED)
							&& Objects.isNull(definitionDetails.getInternalStatus())),
					WorkflowError.ENABLED_DEFINITION_NOT_FOUND);

			String workflowName = Optional.ofNullable(definitionDetails).map(defnDtls -> defnDtls.getTemplateDetails())
					.map(name -> name.getTemplateName()).orElse("NA");

			wasContextHandler.addKey(WASContextEnums.WORKFLOW, workflowName);

			//handle run now for scheduling asynchronously
			final State state = new State();

			//populate state values
			state.addValue(AsyncTaskConstants.TRIGGER_NOW_REQUEST, triggerNowRequest);
			state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
			state.addValue(AsyncTaskConstants.WORKFLOW_NAME_KEY, workflowName);
			boolean isSchedulingFlowEnabled = schedulingService.isEnabled(definitionDetails, wasContextHandler.get(WASContextEnums.OWNER_ID));
			state.addValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED, isSchedulingFlowEnabled);


			//Todo: Remove this check after complete migration to Scheduling Service
			if(isSchedulingFlowEnabled) {
				return handleRunNowForScheduling(state, definitionDetails, definitionId);
			}
			return handleRunNowForESS(state, triggerNowRequest);

		} catch (Exception exception) {
			EventingLoggerUtil.logError("Exception occurred while processing the definitionId=%s exception=%s",
					this.getClass().getSimpleName(), triggerNowRequest.getDefinitionId(), exception.getMessage());
			throw exception;
		}
	}

	/**
	 * Handles the run now process for ESS.
	 * @param state
	 * @param triggerNowRequest
	 * @return a response containing the result of the process trigger
	 */
	private WorkflowGenericResponse handleRunNowForESS(State state, TriggerNowRequest triggerNowRequest){
		String definitionId = triggerNowRequest.getDefinitionId();
		List<SchedulerDetails> schedulerDetails = schedulerDetailsRepository
				.findByDefinitionDetails(DefinitionDetails.builder().definitionId(definitionId).build())
				.orElse(Collections.emptyList());

		WorkflowVerfiy.verify(CollectionUtils.isEmpty(schedulerDetails),
				WorkflowError.SCHEDULE_DETAILS_NOT_FOUND);

		state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, schedulerDetails);

		new RxExecutionChain(state, new SchedulingRunNowTask(wasContextHandler, metricLogger)).executeAsync();

		//TODO: apply a timeout to check for errors

		return WorkflowGenericResponse.builder()
				.response(PublishResponse.builder()
						.actionTidMap(schedulerDetails.stream()
								.collect(Collectors.toMap(detail ->  detail.getSchedulerAction().getAction(),
										schedulerDetail -> definitionId)))
						.build())
				.build();
	}

	/**
	 * Handles the run now process for scheduling service.
	 *
	 * @param state state
	 * @param definitionDetails the details of the definition
	 * @param definitionId the ID of the definition
	 * @return a response containing the result of the process trigger
	 */
	private WorkflowGenericResponse handleRunNowForScheduling(State state, DefinitionDetails definitionDetails, String definitionId) {
		List<String> scheduleIds = SchedulingServiceUtil.getScheduleIds(definitionDetails.getDefinitionKey(), definitionDetails.getTemplateDetails().getTemplateName());
		state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, scheduleIds);


		new RxExecutionChain(state, new SchedulingRunNowTask(wasContextHandler, metricLogger)).executeAsync();

		//TODO: apply a timeout to check for errors

        return WorkflowGenericResponse.builder()
				.response(PublishResponse.builder()
						.actionTidMap(scheduleIds.stream()
								.collect(Collectors.toMap(id -> id,
										schedulerDetail -> definitionId)))
						.build())
				.build();
	}


	/**
	 * This method fetches the Definition & schedule details. Calls the
	 * corresponding duzzit to fetch the details. Starts the process if details are
	 * found based on definitionId.
	 *
	 *
	 * @param triggerDetails
	 */
	@Metric(name = MetricName.TRIGGER_PROCESS_NOW, type = Type.API_METRIC)
	public Trigger runNow(Trigger triggerDetails) {
		Trigger triggerResponse = new Trigger();
		Optional<PublishResponse> essResponseOptional = Optional
				.ofNullable(runNow(new TriggerNowRequest(triggerDetails.getDefinitionId())))
				.map(resp -> (PublishResponse) resp.getResponse()).filter(Objects::nonNull);

		Map<String, String> triggerDetailsMap = essResponseOptional.map(data -> data.getTriggerDetails())
				.orElse(Collections.emptyMap());

		Map<String, String> actionIdDetailsMap = essResponseOptional.map(data -> data.getActionTidMap())
				.orElse(Collections.emptyMap());

		triggerResponse.setDefinitionId(triggerDetails.getDefinitionId());
		triggerResponse.setActions(triggerDetailsMap.entrySet().stream().map(entry -> {
			Trigger.TriggerAction action = new Trigger.TriggerAction();
			action.setActionId(entry.getKey());
			action.setName(entry.getValue());
			return action;
		}).collect(Collectors.toList()));

		triggerResponse.setEntity(actionIdDetailsMap.entrySet().stream().map(entry -> {
			Trigger.TriggerEntity entity = new Trigger.TriggerEntity();
			entity.setChangeType(entry.getKey());
			entity.setEntityReferenceId(entry.getValue());
			return entity;
		}).collect(Collectors.toList()));
		return triggerResponse;
	}
}
