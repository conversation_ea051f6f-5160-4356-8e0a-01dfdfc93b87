package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.SchedulingWorkflowNameMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.foundation.workflow.scheduling.Execution;
import com.intuit.identity.authn.offline.sdk.utils.StringUtils;
import lombok.AllArgsConstructor;

import java.util.*;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.TRIGGER_NOW_ASYNC_TASK;

@AllArgsConstructor
public class SchedulingRunNowTask implements Task {
    private final WASContextHandler wasContextHandler;
    private final MetricLogger metricLogger;

    /**
     * Executes the task with the given state.
     * It processes the on-demand schedule message based on the scheduling flow enabled flag.
     *
     * @param state the state containing the necessary data for execution
     * @return the updated state after execution
     */
    @Metric(name = TRIGGER_NOW_ASYNC_TASK, type = Type.APPLICATION_METRIC)
    @Override
    public State execute(State state) {
        try {
            DefinitionDetails definitionDetails = state.getValue(AsyncTaskConstants.DEFINITION_DETAILS);
            String definitionId = Optional.ofNullable(definitionDetails)
                    .map(DefinitionDetails::getDefinitionId)
                    .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

            String workflowName = state.getValue(AsyncTaskConstants.WORKFLOW_NAME_KEY);
            boolean isSchedulingFlowEnabled = state.getValue(AsyncTaskConstants.IS_SCHEDULING_FLOW_ENABLED);

            EventingLoggerUtil.logInfo(
                    "Processing on-demand Schedule Message. step=startProcessing definitionId=%s, and workflowName=%s",
                    this.getClass().getSimpleName(), definitionId, workflowName);


            Map<String, String> responseDetails = new HashMap<>();

            if(isSchedulingFlowEnabled) {
                responseDetails = handleRunNowForScheduling(state, definitionDetails, workflowName);
            } else {
                responseDetails = handleRunNowForESS(state, state.getValue(AsyncTaskConstants.TRIGGER_NOW_REQUEST), workflowName);
            }

            state.addValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_RESPONSE_KEY, responseDetails);

            EventingLoggerUtil.logInfo(
                    "Processing on-demand Schedule Message. step=completeProcessing definitionId=%s, and workflowName=%s",
                    this.getClass().getSimpleName(), definitionId, workflowName);

        } catch (Exception exception) {
            EventingLoggerUtil.logError("Exception occurred while processing the definition",
                    exception, this.getClass().getSimpleName());
            state.addValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_EXCEPTION_KEY, exception);
            state.addValue(AsyncTaskConstants.TRIGGER_NOW_ASYNC_TASK_FAILURE, true);
            state.addValue(
                    AsyncTaskConstants.TRIGGER_NOW_ASYNC_ERROR_MESSAGE, WorkflowError.TRIGGER_NOW_ASYNC_TASK_ERROR);
            metricLogger.logErrorMetric(TRIGGER_NOW_ASYNC_TASK, Type.APPLICATION_METRIC, exception);
        }

        return state;
    }

    /**
     * Handles the run now process for scheduling flow.
     * It processes each schedule ID and executes the corresponding action processor.
     *
     * @param state the state containing the necessary data for execution
     * @param definitionDetails the details of the definition
     * @return a map containing the response details of the process
     */
    private Map<String, String> handleRunNowForScheduling(State state, DefinitionDetails definitionDetails, String workflowName){
        List<String> scheduleIds = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);

        Map<String, String> responseDetails = new HashMap<>();

        scheduleIds.forEach(id -> {
            Execution execution = new Execution();
            execution.setReferenceId(id);
            execution.setExecutionId(StringUtils.isBlank(wasContextHandler.get(WASContextEnums.INTUIT_TID))
                    ? UUID.randomUUID().toString()
                    : wasContextHandler.get(WASContextEnums.INTUIT_TID));
            responseDetails.putAll(
                    WorkflowScheduleActionProcessorFactory.getProcessor(SchedulingWorkflowNameMapper.getActionsByType(workflowName))
                            .process(definitionDetails, execution));
        });
        return responseDetails;
    }

    /**
     * Handles the run now process for ESS.
     * It processes each scheduler detail and executes the corresponding action processor.
     *
     * @param state the state containing the necessary data for execution
     * @param triggerNowRequest the request containing the trigger details
     * @param workflowName the name of the workflow
     * @return a map containing the response details of the process
     */
    private Map<String, String> handleRunNowForESS(State state, TriggerNowRequest triggerNowRequest, String workflowName){
        List<SchedulerDetails> schedulerDetails = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);
        Map<String, String> responseDetails = new HashMap<>();

        schedulerDetails.forEach(schedulerDetail -> {
            EventScheduleMessageData eventScheduleMessageData = new EventScheduleMessageData();
            eventScheduleMessageData.setScheduleId(schedulerDetail.getSchedulerId());

            // setting it as tid/uuid for uniqueness and avoid getting cached response from app-connect
            eventScheduleMessageData
                    .setMessageId(StringUtils.isBlank(wasContextHandler.get(WASContextEnums.INTUIT_TID))
                            ? UUID.randomUUID().toString()
                            : wasContextHandler.get(WASContextEnums.INTUIT_TID));
            // entityId -> realmId+ScheduleId+recordId
			/*
				 Add scope to the eventScheduleMessageData from TriggerNowRequest, this scope will be used to identify whether to pickup overwatch template or not in trigger and evaluate-and-trigger APIs
				 In general business flows, the scope will be empty, but in case of overwatch, the scope will be set to "test"
				 More details about scope can be found in com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData.getScope()
			*/
            eventScheduleMessageData.setScope(triggerNowRequest.getScope());
            responseDetails.putAll(
                    WorkflowScheduleActionProcessorFactory.getProcessor(WorkflowNameEnum.fromName(workflowName))
                            .process(schedulerDetail, eventScheduleMessageData));
        });

        return responseDetails;
    }

}
