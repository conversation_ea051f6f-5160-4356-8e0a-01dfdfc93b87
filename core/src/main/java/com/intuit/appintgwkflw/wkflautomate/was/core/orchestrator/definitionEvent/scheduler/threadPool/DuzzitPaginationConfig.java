package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Config is used for using Pagination in AppConnect Duzzit for Select Query in QBO. 
 */
@Configuration
@ConfigurationProperties(prefix = "appconnect-duzzit-pagination")
@Getter
@Setter
public class DuzzitPaginationConfig {

  /**
   * Total No. of Pages to be fetched from QBO.
   */
  private int totalPages = 2;
  
  /**
   * No. of Records within a Page being being fetched from QBO.
   */
  private int recordsPerPage = 125;

  /**
   * Total No. of Records to be sent back as response to WAS.
   */
  private int maxResult = 250;
  
  /**
   * Record type based pagination config.
   */
  private Map<RecordType, RecordDuzzitPaginationConfig> recordConfig;
	
}
