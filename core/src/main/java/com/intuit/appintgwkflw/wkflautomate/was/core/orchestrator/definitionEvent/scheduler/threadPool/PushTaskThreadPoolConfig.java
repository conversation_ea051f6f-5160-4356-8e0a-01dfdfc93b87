package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool;

import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.ThreadPoolExecutor;

/** <AUTHOR> This class creates worker thread pool for push task  */
@Configuration
@ConfigurationProperties(prefix = "push-task.thread-pool")
@Getter
@Setter
@ConditionalOnExpression("${push-task.enabled:false}")
public class PushTaskThreadPoolConfig {
  private int minThreads;
  private int maxThreads;
  private int keepAliveTimeInSec;
  private int queueSize;

  @Bean(name = WorkflowConstants.CAMUNDA_PUSH_TASK_EXECUTOR_THREAD_BEAN)
  public ThreadPoolExecutor threadPoolSQSQueue() {
    return ThreadPoolExecutorFactory.createExecutor(
        WorkflowConstants.CAMUNDA_PUSH_TASK_EXECUTOR_THREAD,
        queueSize,
        minThreads,
        maxThreads,
        keepAliveTimeInSec);
  }
}
