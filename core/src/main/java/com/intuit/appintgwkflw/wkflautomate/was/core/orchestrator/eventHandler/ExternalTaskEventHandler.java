package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASK;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ApplyCircuitBreaker;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import java.util.Map;
import java.util.Optional;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@NoArgsConstructor
public class ExternalTaskEventHandler implements WorkflowEventHandler<ExternalTaskCompleted> {

  @Autowired protected ExternalTaskEventHandlerHelper externalTaskEventHandlerHelper;

  @Override
  public ExternalTaskCompleted transform(String event) {
    EventingLoggerUtil.logInfo(
        "Transforming event to ExternalTaskComplete. step=eventTransformation",
        this.getClass().getSimpleName());

    ExternalTaskCompleted externalTaskEvent =
        ObjectConverter.fromJson(event, ExternalTaskCompleted.class);

    WorkflowVerfiy.verify(
        null == externalTaskEvent || StringUtils.isEmpty(externalTaskEvent.getStatus()),
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse event. payload=%s",
        event);
    return externalTaskEvent;
  }

  @Override
  public void execute(ExternalTaskCompleted event, Map<String, String> headers) {

    if (ExternalTaskStatus.EXTEND_LOCK.getStatus().equalsIgnoreCase(event.getStatus())){
      externalTaskEventHandlerHelper.handleExtendLockStatus(event, headers);
    } else if (ExternalTaskStatus.FAIL_WITH_RETRY.getStatus().equalsIgnoreCase(event.getStatus())) {
      externalTaskEventHandlerHelper.handleFailureWithRetryStatus(event, headers, getName());
    } else {
      externalTaskEventHandlerHelper.handleOtherStatus(event, headers, getName());
    }
  }


  @ApplyCircuitBreaker(action = CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT)
  @Override
  public void transformAndExecute(String event, Map<String, String> headers) {
    execute(transformAndValidate(event, headers), headers);
  }


  @Override
  public EventEntityType getName() {
    return EXTERNALTASK;
  }

  /**
   * This method is called after all dlq reties have failed.
   * @param event Event string
   * @param headers Event headers
   * @param e Exception
   */
  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    TaskCompletionHandlers
            .getHandler(Optional.ofNullable(EventEntityType.valueOfEntity(headers.get(EventHeaderConstants.DOMAIN_EVENT)))
                    .orElse(EXTERNALTASK))
            .handleFailure(headers, e);
  }


}
