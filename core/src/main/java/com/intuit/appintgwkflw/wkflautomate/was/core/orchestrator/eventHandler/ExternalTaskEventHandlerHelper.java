package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowCustomTaskHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASK;

@Component
@NoArgsConstructor
public class ExternalTaskEventHandlerHelper {

  @Autowired protected WorkflowCustomTaskHelper customTaskHelper;
  @Autowired protected WASContextHandler wasContextHandler;

  public void handleExtendLockStatus(ExternalTaskCompleted event, Map<String, String> headers){
    getTaskCompletionHandler(headers).extendLock(event, headers);
  }

  public void handleFailureWithRetryStatus(ExternalTaskCompleted event, Map<String, String> headers, EventEntityType eventEntityType){

    WorkflowVerfiy.verifyNull(event.getRetries(), WorkflowError.EXTERNAL_TASK_VALIDATION_ERROR, "retries is null");
    WorkflowVerfiy.verifyNull(event.getExtendDuration(), WorkflowError.EXTERNAL_TASK_VALIDATION_ERROR, "extendDuration is null");
    WorkflowVerfiy.verify(event.getRetries() < 0, WorkflowError.EXTERNAL_TASK_VALIDATION_ERROR, "retries < 0");
    WorkflowVerfiy.verify(event.getExtendDuration() <= 0, WorkflowError.EXTERNAL_TASK_VALIDATION_ERROR, "extendDuration <= 0");
    try {
      getTaskCompletionHandler(headers).invokeFailureWithRetry(event, headers);
    }catch (WorkflowGeneralException e) {
      handleFailWithRetryException(event, headers, e, eventEntityType);
    }
  }

  /**
   * Method should get triggered only when no retries are left and incident should get created in Camunda.
   *  It should invoke the FAILED command in customTaskFramework - as it happens in case of status=FAILED
   * @param event
   * @param headers
   * @param e
   */
  private void handleFailWithRetryException(ExternalTaskCompleted event,
                                            Map<String, String> headers,
                                            WorkflowGeneralException e,
                                            EventEntityType entityType){
    if (WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED != e.getWorkflowError()){
      throw e;
    }
    // Cloning the original event so that original event doesn't get modified.
    ExternalTaskCompleted externalTaskCompleted = ObjectConverter.convertObject(event, ExternalTaskCompleted.class);
    // changing status to invoke create incident flow
    externalTaskCompleted.setStatus(ExternalTaskStatus.FAILED.getStatus());
    // setting error message and details
    externalTaskCompleted.setErrorMessage(Optional.ofNullable(externalTaskCompleted.getErrorMessage())
            .orElse(WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED.getErrorMessage()));
    externalTaskCompleted.setErrorDetails(Optional.ofNullable(externalTaskCompleted.getErrorDetails())
            .orElse(WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED.getErrorDescription()));
    // failed status will be handled by handleOtherStatus method
    handleOtherStatus(externalTaskCompleted, headers, entityType);
  }

  public void handleOtherStatus(ExternalTaskCompleted event, Map<String, String> headers, EventEntityType entityType){
    /**
     * Populate context param for logging.
     */
    populateContextParams(event);
    /**
     * Process Custom task.
     */
    customTaskHelper.checkAndProcessCustomTask(event, headers, entityType);
    /**
     * update Camunda and send event.
     */
    processEvent(event, headers);
  }

  /**
   * Existing handling of Event. This makes call to Camunda for marking task complete, update or
   * failed.
   *
   * @param event   - Event Received through event Bus.
   * @param headers - Event headers received.
   */
  protected void processEvent(ExternalTaskCompleted event, Map<String, String> headers) {

    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(headers.get(EventHeaderConstants.ENTITY_ID)),
        WorkflowError.MISSING_EVENT_HEADERS,
        "Event headers do not have entity id");

    ExternalTaskStatus status = ExternalTaskStatus.fromStatus(event.getStatus());
    TaskCompletionHandler taskCompletionHandler = getTaskCompletionHandler(headers);

    if (ExternalTaskStatus.SUCCESS.equals(status)) {
      try {
        taskCompletionHandler.completeTask(event, headers);
      } catch (Exception e) {
        handleFailure(event, headers, e, taskCompletionHandler);
      }
    } else if (ExternalTaskStatus.FAILED.equals(status)) {
      taskCompletionHandler.invokeFailure(event, headers);
    } else {
        try {
          taskCompletionHandler.updateStatus(event, headers);
        } catch (Exception e) {
          handleFailure(event, headers, e, taskCompletionHandler);
        }
    }

    EventingLoggerUtil.logInfo(
        "Executed ExternalTaskComplete event. step=eventCompleted, status=%s, entityId=%s",
        this.getClass().getSimpleName(), event.getStatus(), headers.get(EventHeaderConstants.ENTITY_ID));
  }

  /**
   * Handles Failure upon execution
   * @param event
   * @param headers
   * @param e
   * @param taskCompletionHandler
   */
  private void handleFailure(
      ExternalTaskCompleted event, Map<String, String> headers, Exception e,
      TaskCompletionHandler taskCompletionHandler) {

    // events with retryable error will be pushed to DLQ to attempt retries
    if (e instanceof WorkflowRetriableException) {
      WorkflowRetriableException ex = (WorkflowRetriableException) e;
      EventingLoggerUtil.logWarning(
          "Retryable error thrown while closing external task in camunda step=eventCompletionFailed entityId=%s",
          this.getClass().getSimpleName(), headers.get(EventHeaderConstants.ENTITY_ID));
      throw ex;
    } else {

      if (e instanceof WorkflowGeneralException) {
        WorkflowGeneralException ex = (WorkflowGeneralException) e;

        // Throw the error if task is not found
        if (WorkflowError.CAMUNDA_TASK_NOT_FOUND.equals(ex.getWorkflowError())
            || WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR.equals(ex.getWorkflowError())
            || WorkflowError.EXTERNAL_TASK_LOG_ERROR.equals(ex.getWorkflowError())) {
          throw ex;
        }
      } else {
        // Handle other failures
        EventingLoggerUtil.logError(
            "Unexpected exception recieved", this.getClass().getSimpleName(), e);

      }
      // If complete task fails, and the task exists create an incident
      taskCompletionHandler.handleFailure(headers, e);
    }
  }
  /**
   * This method is called after all dlq reties have failed.
   * @param event Event string
   * @param headers Event headers
   * @param e Exception
   */
  
  /**
   * Add keys for metric log.
   * @param event - Payload of event.
   */
  protected void populateContextParams(ExternalTaskCompleted event) {
	  switch(event.getStatus()) {
	  case ActivityConstants.TASK_STATUS_SUCCESS:
		  wasContextHandler.addKey(WASContextEnums.EVENT_TYPE, ActivityConstants.TASK_STATUS_COMPLETE);
	      break;
	  case ActivityConstants.TASK_STATUS_FAILED:
		  wasContextHandler.addKey(WASContextEnums.EVENT_TYPE, ActivityConstants.TASK_STATUS_FAILED);
	      break;
	  default:
		  wasContextHandler.addKey(WASContextEnums.EVENT_TYPE, ActivityConstants.TASK_EVENT_TYPE_UPDATE);
		  break;
	  }
  }

  /**
   * Get handler for handling task operation
   * @param headers
   */
  private TaskCompletionHandler getTaskCompletionHandler(Map<String, String> headers){
    return TaskCompletionHandlers.getHandler(
        Optional.ofNullable(
            EventEntityType.valueOfEntity(headers.get(EventHeaderConstants.DOMAIN_EVENT)))
            .orElse(EXTERNALTASK));
  }

}
