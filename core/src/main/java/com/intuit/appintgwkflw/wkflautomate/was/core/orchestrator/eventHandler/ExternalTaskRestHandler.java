package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASKREST;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.v4.workflows.tasks.Task;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component(WorkflowConstants.EXTERNAL_TASK_REST_HANDLER)
@AllArgsConstructor
public class ExternalTaskRestHandler extends ExternalTaskEventHandler {


  public void executeUpdate(Task task, ExternalTaskCompleted event, Map<String, String> headers) {
    // Adding worker id to the headers. We're adding worker id with space so that existing eventing
    // flow remains not impacted.
    headers.put(
        EventHeaderConstants.ENTITY_ID,
        task.getActivityInstanceId()
            .concat(WorkflowConstants.COLON)
            .concat(WorkflowConstants.SPACE));

    execute(event, headers);
  }

  @Override
  public void execute(ExternalTaskCompleted event, Map<String, String> headers) {
      externalTaskEventHandlerHelper.handleOtherStatus(event, headers, getName());
  }

  @Override
  public EventEntityType getName() {
    return EXTERNALTASKREST;
  }
}
