package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import org.json.JSONArray;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASK;


/**
 * This class is an implementation to handle external task test event in VEP test flow.
 * This class should be active in Swimlane A of WAS service to handle the event received from
 * swimlane B. It will publish a message on swimlane B.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ExternalTaskTestEventHandler implements WorkflowEventHandler<ExternalTaskAssigned> {
  private final EventPublisherCapability eventPublisherCapability;

  @Override
  public ExternalTaskAssigned transform(String event) {
    EventingLoggerUtil.logInfo(
        "Transforming test event to ExternalTaskAssigned. step=eventTransformation",
        this.getClass().getSimpleName());
    ExternalTaskAssigned externalTaskEvent = ObjectConverter.fromJson(event, ExternalTaskAssigned.class);

    WorkflowVerfiy.verifyNull(
        externalTaskEvent,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse event. payload=%s",
        event);
    return externalTaskEvent;
  }

  @Override
  public void execute(ExternalTaskAssigned externalTaskAssignedEntity, Map<String, String> headers) {

    EventHeaderEntity eventHeaderEntity =
            InternalEventsUtil.buildEventHeader(
                    headers,
                    PublishEventType.EXTERNAL_TASK_TEST,
                    Optional.ofNullable(EventEntityType.valueOfEntity(headers.get(EventHeaderConstants.DOMAIN_EVENT))).orElse(EXTERNALTASK)
            );
    EventingLoggerUtil.logInfo(
            "Publish Event Test Started step=eventPublishStarted processInstanceId=%s taskName=%s entityId=%s",
            this.getClass().getSimpleName(),
            externalTaskAssignedEntity.getWorkflowMetadata().getProcessInstanceId(),
            externalTaskAssignedEntity.getTaskName(),
            eventHeaderEntity.getEntityId());
    // Create ExternalTaskComplete object to publish to swimlane B complete topic
    try {
      eventPublisherCapability
              .publish(eventHeaderEntity, buildEventPayload(externalTaskAssignedEntity, headers));
    } catch (WorkflowEventException e) {
      // Log and Throw when WorkflowEventException is received
      EventingLoggerUtil.logError(
              "Publish Event Test Failed step=eventPublishFailed processInstanceId=%s taskName=%s entityId=%s error=%s",
              this.getClass().getSimpleName(),
              externalTaskAssignedEntity.getWorkflowMetadata().getProcessInstanceId(),
              externalTaskAssignedEntity.getTaskName(),
              eventHeaderEntity.getEntityId(),
              e);
      throw e;
    }

    EventingLoggerUtil.logInfo(
            "Publish Event Test Successful step=eventPublishSuccessful processInstanceId=%s taskName=%s entityId=%s",
            this.getClass().getSimpleName(),
            externalTaskAssignedEntity.getWorkflowMetadata().getProcessInstanceId(),
            externalTaskAssignedEntity.getTaskName(),
            eventHeaderEntity.getEntityId());

  }

  private ExternalTaskCompleted buildEventPayload(ExternalTaskAssigned externalTaskAssigned, Map<String, String> headers) {
    return ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.name())
            .variables(getProcessVariableMap(headers.get(EventHeaderConstants.TARGET_ASSET_ALIAS)))
            .build();
  }

  @Override
  public EventEntityType getName() {
    return EventEntityType.EXTERNALTASKTEST;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    // Dummy comments added for sonar critical issue check
  }

  private Map<String, Object> getProcessVariableMap(String targetAssetAlias) {
    try {
      JSONArray processVariablesArray = new JSONArray(targetAssetAlias);
      Map<String, Object> processVariablesMap = IntStream
          .range(0, processVariablesArray.length())
          .mapToObj(idx -> processVariablesArray.getJSONObject(idx))
          .collect(Collectors.toMap(processVariableObject -> processVariableObject.keys().next(),
              processVariableObject -> processVariableObject.get(processVariableObject.keys().next())));
      return processVariablesMap;
    } catch (Exception e) {
      EventingLoggerUtil.logError("Cannot fetch process variables from Target Asset Alias of test bpmn", this.getClass().getSimpleName());
      return null;
    }
  }

}
