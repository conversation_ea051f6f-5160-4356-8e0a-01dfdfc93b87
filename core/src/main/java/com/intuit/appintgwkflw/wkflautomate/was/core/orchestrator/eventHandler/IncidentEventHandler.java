package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.IncidentTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.incident.WorkflowIncident;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEventErrorDetails;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;


@Component
@AllArgsConstructor
public class IncidentEventHandler implements WorkflowEventHandler<WorkflowIncident> {

  private final ProcessDetailsRepository processDetailsRepository;
  private final ProcessDetailsRepoService processDetailsRepoService;
  private final EventPublisherCapability eventPublisherCapability;
  private final ProcessDomainEventHandler processDomainEventHandler;
  private MetricLogger metricLogger;
  private IncidentTaskManager incidentTaskManager;
  private WASContextHandler wasContextHandler;

  @Override
  public WorkflowIncident transform(String event) {
    WorkflowIncident workflowIncidentEvent =
        ObjectConverter.fromJson(event, WorkflowIncident.class);

    WorkflowVerfiy.verify(
        workflowIncidentEvent == null || workflowIncidentEvent.getWorkflowMetadata() == null,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse Or missing mandatory fields in event. payload=%s",
        event);
    /**
     * populate context for metric logging.
     */
    populateContextParams(workflowIncidentEvent);
    return workflowIncidentEvent;
  }

  @Override
  public void execute(WorkflowIncident workflowIncident, Map<String, String> headers) {
    try {
      EventingLoggerUtil.logInfo(
          "step=camundaIncidentConsumed processInstanceId=%s",
          this.getClass().getSimpleName(),
          workflowIncident.getWorkflowMetadata().getProcessInstanceId());

      ProcessDetails processDetails =
          processDetailsRepository
              .findByIdWithoutDefinitionData(workflowIncident.getWorkflowMetadata().getProcessInstanceId())
              .orElseThrow(() -> new WorkflowGeneralException(PROCESS_DETAILS_NOT_FOUND_ERROR));

      //  Only fills Parent process details when parentId is present.
      MultiStepUtil.fillParentProcessDetails(processDetails, processDetailsRepository);

      EventHandlerUtil.populateContextFromProcessDetails(wasContextHandler, processDetails);

      enrichIncidentEntity(workflowIncident, processDetails);

      incidentTaskManager.execute(workflowIncident, headers, processDetails);

      eventPublisherCapability.publish(
          InternalEventsUtil.buildEventHeader(headers, PublishEventType.INCIDENT, getName()),
          workflowIncident);
      // Save call to increment process entity version
      saveEntityAndPublishDomainEvent(
          processDetailsRepoService.updateEntityVersion(processDetails), workflowIncident);

      EventingLoggerUtil.logInfo(
          "step=camundaIncidentPublished processInstanceId=%s",
          this.getClass().getSimpleName(),
          workflowIncident.getWorkflowMetadata().getProcessInstanceId());

    } catch (Exception e) {
      metricLogger.logErrorMetric(
          MetricName.INTERNAL_CAMUNDA_CONSUMER_INCIDENT, Type.EVENT_METRIC, e);
      throw e;
    }
  }

  /**
   * Method to publish Domain Event
   *
   * @param processDetails
   * @param workflowIncident
   */
  void saveEntityAndPublishDomainEvent(
      ProcessDetails processDetails, final WorkflowIncident workflowIncident) {
    processDomainEventHandler.publish(
        DomainEntityRequest.<ProcessDetails>builder()
            .request(processDetails)
            .entityChangeAction(EntityChangeAction.UPDATE)
            .domainEventErrorDetails(
                DomainEventErrorDetails.builder()
                    .activityId(workflowIncident.getActivityId())
                    .errorMessage(workflowIncident.getIncidentMsg())
                    .build())
            .build());
  }

  private void enrichIncidentEntity(WorkflowIncident workflowIncident,
      ProcessDetails processDetails) {
    workflowIncident.getWorkflowMetadata().setWorkflowName(
        processDetails.getDefinitionDetails().getTemplateDetails().getTemplateName());
    workflowIncident.getWorkflowMetadata()
        .setWorkflowOwnerId(Long.toString(processDetails.getOwnerId()));
    workflowIncident.setBusinessEntityId(processDetails.getRecordId());
    String entityType = MultiStepUtil.getEntityType(processDetails);
    // For on demand approval, entity type could be null so fetching it from process variables
    if(Objects.isNull(entityType)){
      entityType = Optional.ofNullable(workflowIncident.getVariables())
          .map(vars -> vars.get(ENTITY_TYPE))
          .map(Object :: toString)
          .orElse(null);
      WorkflowLogger.logInfo("Fetching record type from incident for on demand approval for realmId = %s and entity id = %s and entityType = %s", processDetails.getOwnerId(), processDetails.getRecordId()
          , Objects.nonNull(entityType) ? entityType : "unavailable");
    }
    workflowIncident.setBusinessEntityType(entityType);
  }

  @Override
  public EventEntityType getName() {
    return EventEntityType.INCIDENT;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
//     No need for DL Queues. Since the downstream here is Kafka. With atleast-once, kafka wont fail.
  }

  /**
   * Populate context param for metric logging.
   * @param workflowIncidentEvent
   */
  private void populateContextParams(WorkflowIncident workflowIncidentEvent) {
	  wasContextHandler.addKey(WASContextEnums.PROCESS_INSTANCE_ID,
			  workflowIncidentEvent.getWorkflowMetadata().getProcessInstanceId());
	  wasContextHandler.addKey(WASContextEnums.ACTIVITY_ID,
			  workflowIncidentEvent.getActivityId());
	  wasContextHandler.addKey(WASContextEnums.ENTITY_ID,
			  workflowIncidentEvent.getExternalTaskId());
  }
}
