package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.SchedulingWorkflowNameMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.WorkflowScheduleActionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions.factory.WorkflowScheduleActionProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.foundation.workflow.scheduling.Execution;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COLON;

/**
 * The `SchedulingEventHandler` class is responsible for handling scheduling events.
 * It implements the `WorkflowEventHandler` interface for `SchedulingEvent` type.
 * This class processes scheduling events by transforming, executing, and handling failures.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SchedulingEventHandler implements WorkflowEventHandler<Execution>{
    private final DefinitionDetailsRepository definitionDetailsRepository;
    private final WASContextHandler wasContextHandler;
    private final EventScheduleHelper eventScheduleHelper;
    private final MetricLogger metricLogger;

    /**
     * Transforms a JSON string representation of an event into a `SchedulingEvent` object.
     * Validates the transformed event to ensure it is not null.
     *
     * @param event the JSON string representation of the event
     * @return the transformed `SchedulingEvent` object
     * @throws WorkflowGeneralException if the event is null or missing mandatory fields
     */
    @Override
    public Execution transform(String event) {
        Execution schedulingEvent = ObjectConverter.fromJson(event, Execution.class);

        WorkflowVerfiy.verifyNull(
                schedulingEvent,
                WorkflowError.INCORRECT_EVENT_PAYLOAD,
                "Unable to parse Or missing mandatory fields in scheduling event. payload=%s",
                event);

        return schedulingEvent;
    }

    /**
     * Executes the scheduling event by processing the reference ID to determine the definition details
     * and action to be performed. Logs the processing steps and handles invalid reference IDs.
     *
     * @param schedulingEvent the scheduling event to be executed
     * @param headers additional headers for the event
     * @throws WorkflowGeneralException if the reference ID is invalid
     */
    @Override
    public void execute(Execution schedulingEvent, Map<String, String> headers) {
        try {
            String referenceId = schedulingEvent.getReferenceId();
            List<String> definitionKeyAndAction = List.of(referenceId.split(COLON));
            if (definitionKeyAndAction.size() != 2) {
                throw new WorkflowGeneralException(WorkflowError.INVALID_REFERENCE_ID, "Failed to process the scheduling event as the reference id is invalid. referenceId=%s", referenceId);
            }
            String definitionKey = definitionKeyAndAction.get(0);
            String action = definitionKeyAndAction.get(1);
            DefinitionDetails definitionDetails = definitionDetailsRepository.findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(definitionKey, Long.valueOf(wasContextHandler.get(WASContextEnums.OWNER_ID)));
            if (ObjectUtils.isEmpty(definitionDetails)) {
                EventingLoggerUtil.logWarning(
                        "Processing Scheduling Event. step=noDefinitionDetailsFound referenceId=%s",
                        this.getClass().getSimpleName(), referenceId);
                State state = new State();
                state.addValue(AsyncTaskConstants.REALM_ID_KEY, wasContextHandler.get(WASContextEnums.OWNER_ID));
                new RxExecutionChain(state, eventScheduleHelper.prepareSchedulingDeleteTask(state, definitionKey)).executeAsync();
                return;
            }
            /**
             * stop execution if definition is not enabled: !(status = ENABLED and internal_status = null)
             */
            if (!(definitionDetails.getStatus().equals(Status.ENABLED)
                    && Objects.isNull(definitionDetails.getInternalStatus()))) {

                EventingLoggerUtil.logInfo(
                        "Schedule message processing stopped - definition not enabled. step=stopProcessing referenceId=%s, and definitionId=%s",
                        this.getClass().getSimpleName(),
                        referenceId,
                        definitionDetails.getDefinitionId());
                return;
            }
            String workflowName = definitionDetails.getTemplateDetails().getTemplateName();
            wasContextHandler.addKey(WASContextEnums.WORKFLOW, workflowName);
            EventingLoggerUtil.logInfo(
                    "Processing Scheduling Event. step=startProcessing referenceId=%s, and workflowName=%s",
                    this.getClass().getSimpleName(),
                    referenceId,
                    workflowName);
            WorkflowScheduleActionProcessorFactory.getProcessor(SchedulingWorkflowNameMapper.getActionsByType(workflowName)).process(definitionDetails, schedulingEvent);
            EventingLoggerUtil.logInfo(
                    "Processing Scheduling Event. step=completeProcessing referenceId=%s, and workflowName=%s",
                    this.getClass().getSimpleName(),
                    referenceId,
                    workflowName);
        }catch (Exception e) {
            if (e instanceof WorkflowRetriableException) {
                EventingLoggerUtil.logWarning(
                        "Retryable error thrown while executing the schedule event", this.getClass().getSimpleName());
                throw e;
            } else {
                handleFailure(ObjectConverter.toJson(schedulingEvent), headers, e);
                throw e;
            }
        }
    }

    /**
     * Returns the name of the event entity type handled by this handler.
     *
     * @return the `EventEntityType` for scheduling
     */
    @Override
    public EventEntityType getName() {
        return EventEntityType.SCHEDULING;
    }

    /**
     * Handles failures that occur during the processing of an event.
     * Currently, this method does not implement any specific failure handling logic.
     *
     * @param event the event that failed
     * @param headers additional headers for the event
     * @param e the exception that was thrown during processing
     */
    @Override
    public void handleFailure(String event, Map<String, String> headers, Exception e) {
        handleFailure(event, headers, e, MetricName.SCHEDULING_EVENT);
    }

    /**
     * Handles failures that occur during the processing of an event.
     * Currently, this method does not implement any specific failure handling logic.
     *
     * @param event the event that failed
     * @param headers additional headers for the event
     * @param e the exception that was thrown during processing
     * @param metricsName the metric name for the event
     */
    private void handleFailure(
            String event, Map<String, String> headers, Exception e, MetricName metricsName) {
        metricLogger.logErrorMetric(metricsName, Type.EVENT_METRIC, e);
        EventingLoggerUtil.logError(
                "Error while handling scheduling event. step=triggerFailed scheduling event=%s",
                this.getClass().getSimpleName(), event);
    }
}
