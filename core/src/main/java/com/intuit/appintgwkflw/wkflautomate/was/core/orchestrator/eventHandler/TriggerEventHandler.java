package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EVENT_HEADERS;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.Tag;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;

import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.Scope;
import jodd.util.StringUtil;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TriggerEventHandler implements WorkflowEventHandler<Trigger> {

  private RunTimeService runTimeService;
  private WASContextHandler contextHandler;
  private MetricLogger metricLogger;

  @Override
  public Trigger transform(String event) {

    Trigger workflowTriggerEvent = ObjectConverter.fromJson(event, Trigger.class);

    WorkflowVerfiy.verifyNull(
        workflowTriggerEvent,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse Or missing mandatory fields in trigger event. payload=%s",
        event);

    return workflowTriggerEvent;
  }

  @Override
  public void execute(Trigger event, Map<String, String> headers) {

    try {
      // validations and transformations
      validateOwnerId(headers);
      EventHeaders eventHeaders = createEventHeader(event.getMetaData());
      updateEntityIdInContext(eventHeaders.getEntityId());
      Map<String, Object> triggerMessage = populateTriggerMessage(event, eventHeaders);

      // Invoke trigger API
      WorkflowGenericResponse response = getTriggerResponse(eventHeaders, triggerMessage);

      EventingLoggerUtil.logInfo(
          "Trigger process completed. response=%s",
          this.getClass().getSimpleName(), ObjectConverter.toJson(response));

      // handle response
      handleResponse(event, headers, response);

    } catch (Exception e) {
      if (e instanceof WorkflowRetriableException) {
        EventingLoggerUtil.logWarning(
            "Retryable error thrown while triggering", this.getClass().getSimpleName());
        throw e;
      } else {
        handleFailure(ObjectConverter.toJson(event), headers, e);
      }
    }
  }

  /**
   * Invoke appropriate trigger API based on targetAPI header
   *
   * @param eventHeaders
   * @param triggerMessage
   * @return
   */
  private WorkflowGenericResponse getTriggerResponse(EventHeaders eventHeaders, Map<String, Object> triggerMessage) {
    switch (eventHeaders.getTargetAPI()) {
      case TRIGGER_V1:
        return runTimeService.processTriggerMessage(triggerMessage);
      case EVALUATE_AND_TRIGGER_V2:
        return runTimeService.processEvaluateAndTriggerMessage(triggerMessage);
      // Invoke v2 trigger by default
      case TRIGGER_V2:
      default:
        return runTimeService.processTriggerMessageV2(triggerMessage);
    }
  }

  /**
   * handle response from trigger API. Invoke failure action where ever needed
   *
   * @param event
   * @param headers
   * @param response
   */
  private void handleResponse(
      Trigger event, Map<String, String> headers, WorkflowGenericResponse response) {

    Pair<MetricName, WorkflowError> metricNameWorkflowErrorPair = null;


    // set failure to true if response has failure status
    if (ResponseStatus.FAILURE.equals(response.getStatus())) {
      metricNameWorkflowErrorPair =
          Pair.with(MetricName.EVENT_TRIGGER, WorkflowError.TRIGGER_ERROR);
    }

    // set failure to true if no action could be taken OR an error was consumed
    // check if response is of type triggerResponse
    WorkflowResponse workflowResponse = response.getResponse();
    if (null == metricNameWorkflowErrorPair && workflowResponse instanceof WorkflowTriggerResponse) {
      metricNameWorkflowErrorPair = noActionOnTrigger((WorkflowTriggerResponse) workflowResponse);
    }

    // check if response is of type triggerResponses
    if (null == metricNameWorkflowErrorPair && workflowResponse instanceof WorkflowTriggersResponse) {
      WorkflowTriggersResponse triggerResponses = (WorkflowTriggersResponse) workflowResponse;

      if (triggerResponses.getTriggers() != null) {
        for (WorkflowTriggerResponse triggerResponse : triggerResponses.getTriggers()) {
          metricNameWorkflowErrorPair = noActionOnTrigger(triggerResponse);

          // breaking here so that if any other responses in the list are success, it does not
          // override the metricName to null
          if (metricNameWorkflowErrorPair != null) break;
        }
      }
    }

    // if failure is found somewhere, invoke failure action
    if (null != metricNameWorkflowErrorPair) {
      handleFailure(
          ObjectConverter.toJson(event),
          headers,
          new WorkflowGeneralException(metricNameWorkflowErrorPair.getValue1()),
          metricNameWorkflowErrorPair.getValue0());
    }
  }

  private Pair<MetricName, WorkflowError> noActionOnTrigger(
      WorkflowTriggerResponse triggerResponse) {
    if (null != triggerResponse && null != triggerResponse.getStatus()) {
      switch (triggerResponse.getStatus()) {
        case NO_ACTION:
          return Pair.with(MetricName.EVENT_TRIGGER_NO_ACTION, WorkflowError.TRIGGER_NO_ACTION);
        case ERROR_SIGNALING_PROCESS:
          return Pair.with(MetricName.EVENT_TRIGGER_SIGNAL_FAILED, WorkflowError.TRIGGER_ERROR);
        case ERROR_STARTING_PROCESS:
          return Pair.with(MetricName.EVENT_TRIGGER_START_FAILED, WorkflowError.TRIGGER_ERROR);
      }
    }
    return null;
  }

  /**
   * Create metadata using eventHeader's , entity and variables
   *
   * @param event
   * @param eventHeaders
   * @return
   */
  private Map<String, Object> populateTriggerMessage(Trigger event, EventHeaders eventHeaders) {
    Map<String, Object> triggerMessage = new HashMap<>();
    triggerMessage.put(EVENT_HEADERS, eventHeaders);
    triggerMessage.put(WorkflowConstants.ENTITY, event.getEntity());
    triggerMessage.put(WorkflowConstants.VARIABLES, event.getVariables());
    triggerMessage.put(WorkflowConstants.SOURCE, WorkflowConstants.EVENT);
    return triggerMessage;
  }

  /**
   * throws exception if owner id is not present in header
   *
   * @param headers
   */
  private void validateOwnerId(Map<String, String> headers) {
    String ownerId = headers.get(EventHeaderConstants.OWNER_ID);
    WorkflowVerfiy.verifyNull(
        ownerId,
        WorkflowError.MISSING_EVENT_HEADERS,
        "OwnerId not passed as part of trigger header");
  }

  /**
   * Create EventHeaders (used by api) from event metadata
   *
   * @param metaData
   * @return
   */
  private EventHeaders createEventHeader(MetaData metaData) {

    WorkflowVerfiy.verify(
        metaData == null
            || metaData.getEntityId() == null
            || metaData.getEntityChangeIdentifier() == null
            || metaData.getEntityChangeIdentifier().getMessageName() == null
            || metaData.getWorkflow() == null,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Incorrect trigger metadata recieved. metaData=" + metaData);

    EventHeaders eventHeaders = new EventHeaders();
    eventHeaders.setEntityId(metaData.getEntityId());
    eventHeaders.setEntityChangeType(metaData.getEntityChangeIdentifier().getMessageName());
    eventHeaders.setWorkflow(metaData.getWorkflow());
    eventHeaders.setProviderWorkflowId(metaData.getProviderWorkflowId());
    eventHeaders.setDefinitionKey(metaData.getDefinitionKey());
    eventHeaders.setBlockProcessOnSignalFailure(metaData.isBlockProcessOnSignalFailure());
    /**
     * Set Tags to Opt which workflow Version to run.
     */
    if(ObjectUtils.isNotEmpty(metaData.getTags()) 
    		&& StringUtil.isNotEmpty(metaData.getTags().getVersion())) {
    	eventHeaders.setTags(Tag.builder()
    			.version(metaData.getTags().getVersion()).build());
    }
    RecordType recordType = RecordType.fromType(metaData.getEntityType());
    WorkflowVerfiy.verifyNull(
        recordType,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Recordtype is incorrect. recordType=" + recordType);

    eventHeaders.setEntityType(recordType);
    eventHeaders.setTargetAPI(metaData.getTargetApi());

    if(StringUtils.isNotEmpty(metaData.getScope())) {
      WorkflowLogger.logInfo("Scope value for request metadata is: %s for entityId: %s , workflow: %s , entityChangeType: %s",
              metaData.getScope(), metaData.getEntityId(), metaData.getWorkflow(), metaData.getEntityChangeIdentifier().getMessageName());
      eventHeaders.setScope(Scope.fromType(metaData.getScope()));
    }

    return eventHeaders;
  }

  /**
   * Updates enityId read from payload to context & overrides the value received from header
   *
   * @param entityId
   */
  private void updateEntityIdInContext(String entityId) {
    contextHandler.addKey(WASContextEnums.ENTITY_ID, entityId);
  }

  @Override
  public EventEntityType getName() {
    return EventEntityType.TRIGGER;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    handleFailure(event, headers, e, MetricName.EVENT_TRIGGER);
  }

  private void handleFailure(
      String event, Map<String, String> headers, Exception e, MetricName metricsName) {
    metricLogger.logErrorMetric(metricsName, Type.EVENT_METRIC, e);
    EventingLoggerUtil.logError(
        "Error while handling trigger event. step=triggerFailed trigger=%s",
        this.getClass().getSimpleName(), event);
  }
}
