package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.Map;

public interface WorkflowEventHandler<T> {
  T transform(String event);

  default T transformAndValidate(String event, Map<String, String> headers) {
    return transform(event);
  }

  void execute(T t, Map<String, String> headers);

  default void transformAndExecute(String event, Map<String, String> headers) {
    execute(transformAndValidate(event, headers), headers);
  }

  EventEntityType getName();

  void handleFailure(String event, Map<String, String> headers, Exception e);
}
