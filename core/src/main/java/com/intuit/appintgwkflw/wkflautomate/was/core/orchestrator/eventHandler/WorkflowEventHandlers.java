package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.EnumMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * The type WorkflowEventHandler.
 *
 * <AUTHOR>
 *     <p>Class acts as factory to return Workflow event handler
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowEventHandlers {

  private static final Map<EventEntityType, WorkflowEventHandler> EVENT_HANDLER_MAP =
      new EnumMap<>(EventEntityType.class);

  /**
   * Adds a handler.
   *
   * @param eventEntity the entity type for the event
   * @param eventHandler the event handler
   */
  public static void addHandler(EventEntityType eventEntity, WorkflowEventHandler eventHandler) {
    EVENT_HANDLER_MAP.put(eventEntity, eventHandler);
  }

  /**
   * Gets handler
   *
   * @param eventEntity input handler name
   * @return action handler impl
   */
  public static WorkflowEventHandler getHandler(EventEntityType eventEntity) {
    return EVENT_HANDLER_MAP.get(eventEntity);
  }

  /**
   * Contains boolean.
   *
   * @param eventEntity the entity type name
   * @return true /false if handler is present or not
   */
  public static boolean contains(EventEntityType eventEntity) {

    return EVENT_HANDLER_MAP.containsKey(eventEntity);
  }
}
