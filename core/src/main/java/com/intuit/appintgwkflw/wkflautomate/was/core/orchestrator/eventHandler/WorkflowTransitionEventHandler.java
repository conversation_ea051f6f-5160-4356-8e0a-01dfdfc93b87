package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.END;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_END_IDENTIFIER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_IDENTIFIER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.START;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.WORKFLOW_TRANSITION_EVENTS;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import io.github.resilience4j.retry.annotation.Retry;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Event handler to consume workflow state transition events and publish them
 */
@Component
@AllArgsConstructor
public class WorkflowTransitionEventHandler implements WorkflowEventHandler<WorkflowStateTransitionEvents> {

  private final EventPublisherCapability eventPublisherCapability;
  private final ProcessDetailsRepository processDetailsRepository;
  private final WASContextHandler contextHandler;
  private final DomainEventService domainEventService;
  private final WorkflowTaskConfig workflowTaskConfig;

  @Override
  @Retry(name = ResiliencyConstants.EVENT_HANDLER)
  public void transformAndExecute(String event, Map<String, String> headers) {
    execute(transformAndValidate(event, headers), headers);
  }

  @Override
  public WorkflowStateTransitionEvents transform(String event) {
    EventingLoggerUtil.logInfo(
        "Transforming event to WorkflowStateTransitionEvents. step=eventTransformation",
        this.getClass().getSimpleName());

    WorkflowStateTransitionEvents workflowStateTransitionEvents =
        ObjectConverter.fromJson(event, WorkflowStateTransitionEvents.class);

    WorkflowVerfiy.verifyNull(
        workflowStateTransitionEvents,
        WorkflowError.INCORRECT_EVENT_PAYLOAD,
        "Unable to parse event. payload=%s",
        event);
    /**
     * Populate context params for metric logging.
     */
    populateContext(workflowStateTransitionEvents);
    return workflowStateTransitionEvents;
  }

  @Override
  public void execute(
      WorkflowStateTransitionEvents workflowStateTransitionEvent, Map<String, String> headers) {
    Optional<ProcessDetails> processDetailsOpt =
        processDetailsRepository.findByIdWithoutDefinitionData(
            workflowStateTransitionEvent.getWorkflowMetadata().getProcessInstanceId());

    ProcessDetails processDetails =
        processDetailsOpt.orElseThrow(
            () -> new WorkflowRetriableException(PROCESS_DETAILS_NOT_FOUND_ERROR));
    EventHandlerUtil.populateContextFromProcessDetails(contextHandler, processDetails);

    enrichWorkflowStateTransitionEvent(workflowStateTransitionEvent, processDetails);
    // check for duplicate migration event.
    if (isDuplicateMigrationEvent(workflowStateTransitionEvent, processDetails)) {
      EventingLoggerUtil.logInfo(
          "Received duplicate migration event entityId=%s",
          this.getClass().getSimpleName(), workflowStateTransitionEvent.getBusinessEntityId());
      return;
    }

    handleMigrationEvent(workflowStateTransitionEvent, processDetails);

    publishEvent(workflowStateTransitionEvent, headers);

    publishDomainEvent(workflowStateTransitionEvent, headers, processDetails);

    if (isMigrateEvent(workflowStateTransitionEvent)) {
      EventingLoggerUtil.logInfo(
          "Process migration successful fromDefinitionId=%s toDefinitionId=%s entityId=%s",
          this.getClass().getSimpleName(),
          workflowStateTransitionEvent.getMigratedFrom().getProcessDefinitionId(),
          workflowStateTransitionEvent.getWorkflowMetadata().getProcessDefinitionId(),
          workflowStateTransitionEvent.getBusinessEntityId());
    }
  }

  /**
   * @param workflowStateTransitionEvent
   * @param headers
   * @param processDetails
   */
  private void publishDomainEvent(
      WorkflowStateTransitionEvents workflowStateTransitionEvent,
      Map<String, String> headers,
      ProcessDetails processDetails) {
    // Migration event is already handled in handleMigrationEvent() method
    if (isMigrateEvent(workflowStateTransitionEvent)) {
      return;
    }
    EventHeaderEntity eventHeaderEntity =
        InternalEventsUtil.buildEventHeader(
            headers,
            PublishEventType.WORKFLOW_TRANSITION_EVENTS,
            Optional.ofNullable(
                    EventEntityType.valueOfEntity(headers.get(EventHeaderConstants.DOMAIN_EVENT)))
                .orElse(WORKFLOW_TRANSITION_EVENTS));
    EventingLoggerUtil.logInfo(
        "Publish Domain Event Started eventType=%s step=eventPublishStarted entityId=%s",
        this.getClass().getSimpleName(),
        workflowStateTransitionEvent.getEventType(),
        workflowStateTransitionEvent.getBusinessEntityId());

    Map<String,Object> variableMap = Optional.ofNullable(workflowStateTransitionEvent.getActivityMetadata())
        .map(ActivityMetaData::getVariables)
        .orElse(Collections.emptyMap());
    // Case: Process is Externally Terminated from Cockpit
    if (PROCESS_IDENTIFIER.equalsIgnoreCase(workflowStateTransitionEvent.getActivityType())) {
      if (ProcessStatus.CANCELLED
          .getProcessStatus()
          .equalsIgnoreCase(workflowStateTransitionEvent.getEventType())) {
        domainEventService.updateStatus(
            processDetails, ProcessStatus.CANCELLED, eventHeaderEntity, EntityChangeAction.UPDATE, variableMap);
      } else if (PROCESS_END_IDENTIFIER.equalsIgnoreCase(
          workflowStateTransitionEvent.getEventType())) {
        // For SLA, WASUpdateProcessStatus handler marks the process to ended and publishes Domain
        // event, if the
        // process is already ended, we will ignore the transition event received on end(from
        // HistoryHandler) otherwise duplicate
        // domain events will be published.
        if (!ProcessStatus.ENDED.equals(processDetails.getProcessStatus())) {
          //Supplying empty map of variables, requires change at camunda to send variables on process end
          domainEventService.updateStatus(
              processDetails, ProcessStatus.ENDED, eventHeaderEntity, EntityChangeAction.UPDATE, variableMap);
        }
      }
    } else {
      handleActivityDomainEvents(workflowStateTransitionEvent, eventHeaderEntity, processDetails);
    }
  }

  /**
   * @param workflowStateTransitionEvent
   * @param eventHeaderEntity
   * @param processDetails
   */
  private void handleActivityDomainEvents(
      WorkflowStateTransitionEvents workflowStateTransitionEvent,
      EventHeaderEntity eventHeaderEntity,
      ProcessDetails processDetails) {
    // Domain Events are supported for Start/End for both Swimlane(s) and handled in this flow
    if (START.equalsIgnoreCase(workflowStateTransitionEvent.getEventType())
        || END.equalsIgnoreCase(workflowStateTransitionEvent.getEventType())) {
      domainEventService.publishActivityRuntimeEvent(
          workflowStateTransitionEvent, eventHeaderEntity, processDetails);
    }
  }

  /**
   * populate entityId, entityType, ownerId in workflow state transition event from process details
   * @param event
   * @param processDetails
   */
  private void enrichWorkflowStateTransitionEvent(WorkflowStateTransitionEvents event, ProcessDetails processDetails){
    event.setBusinessEntityId(processDetails.getRecordId());
    RecordType recordType = processDetails.getDefinitionDetails().getRecordType();
//  Record type call be null for child Elements.

    String record = Objects.nonNull(recordType) ? recordType.getRecordType() : null;

    // In case of on demand approval record will be null so getting it from ActivityMetaData
    if(Objects.isNull(record)){
           record = Optional.ofNullable(event)
          .map(WorkflowStateTransitionEvents::getActivityMetadata)
          .map(ActivityMetaData::getVariables)
          .map(vars -> vars.get(ENTITY_TYPE))
          .map(Object::toString)
          .orElse(null);
      WorkflowLogger.logInfo("Fetching entity type from event for on demand approval for realmId = %s and entity id = %s and record = %s", processDetails.getOwnerId(), processDetails.getRecordId(), Objects.nonNull(record) ? record : "unavailable");

    }

    event.setBusinessEntityType(record);
    event.getWorkflowMetadata().setWorkflowOwnerId(Long.toString(processDetails.getOwnerId()));
  }

  /**
   * Publish payload to eventbus
   *
   * @param workflowStateTransitionEvent payload
   * @param headers kafka header info
   */
  private void publishEvent(
      WorkflowStateTransitionEvents workflowStateTransitionEvent, Map<String, String> headers) {
    EventHeaderEntity eventHeaderEntity =
        InternalEventsUtil.buildEventHeader(
            headers,
            PublishEventType.WORKFLOW_TRANSITION_EVENTS,
            Optional.ofNullable(
                    EventEntityType.valueOfEntity(headers.get(EventHeaderConstants.DOMAIN_EVENT)))
                .orElse(WORKFLOW_TRANSITION_EVENTS));
    EventingLoggerUtil.logInfo(
        "Publish Workflow Transition Event Started eventType=%s step=eventPublishStarted entityId=%s",
        this.getClass().getSimpleName(),
        workflowStateTransitionEvent.getEventType(),
        workflowStateTransitionEvent.getBusinessEntityId());

    try {
      if (checkStateTransitionPublish(
          workflowStateTransitionEvent.getActivityType(),
          workflowStateTransitionEvent.getEventType())) {
        eventPublisherCapability.publish(eventHeaderEntity, workflowStateTransitionEvent, workflowStateTransitionEvent.getWorkflowMetadata().getProcessInstanceId());
      }
    } catch (WorkflowEventException e) {
      // Log and Throw when WorkflowEventException is received
      EventingLoggerUtil.logError(
          "Publish State Transition Event Failed eventType=%s step=eventPublishFailed entityId=%s",
          this.getClass().getSimpleName(),
          workflowStateTransitionEvent.getEventType(),
          workflowStateTransitionEvent.getBusinessEntityId());
      throw e;
    }

    EventingLoggerUtil.logInfo(
        "Publish State Transition Event Successful eventType=%s step=eventPublishSuccessful entityId=%s",
        this.getClass().getSimpleName(),
        workflowStateTransitionEvent.getEventType(),
        workflowStateTransitionEvent.getBusinessEntityId());
  }

  /**
   * Helper Method to see if state transition needs to be published for Process Cancel/End Events
   *
   * @return
   */
  private boolean checkStateTransitionPublish(String activityType, String eventType) {
    // We're not publishing state transition event for Process Events such as End/Cancel. In case
    // offerings do need it, they would need to switch to domain events. Also, we have removed
    // publishing start
    // event at process level so only use cases that we're publishing events is on Process
    // End/Cancel via History Events.

    Set<String> allowedStatus =
        workflowTaskConfig.getStateTransitionConfig().getEnableProcessEvents();

    Set<String> disallowedActivityEvents =
        workflowTaskConfig.getStateTransitionConfig().getDisableActivityEvents();

    if (PROCESS_IDENTIFIER.equalsIgnoreCase(activityType)) {
      return ObjectUtils.isNotEmpty(allowedStatus) && allowedStatus.contains(eventType);
    }
    // This setting will allow SLA activities to skip publishing State Transition Events (Not valid
    // use case for SLA)
    else if (ObjectUtils.isNotEmpty(disallowedActivityEvents)
        && disallowedActivityEvents.contains(eventType)) {
      return false;
    }
    // if not for process event then return true
    return true;
  }

  /**
   * Populates migratedFrom workflow metadata in payload and updates process details repository with
   * new process definition_id
   *
   * @param event input payload from kafka
   * @param processDetails
   */
  private void handleMigrationEvent(
      WorkflowStateTransitionEvents event, ProcessDetails processDetails) {
    if (isMigrateEvent(event)) {
      EventingLoggerUtil.logInfo(
          "Publish State Transition Event step=migrationEvent entityId=%s",
          this.getClass().getSimpleName(), event.getBusinessEntityId());
      WorkflowMetaData migratedFrom =
          WorkflowMetaData.builder()
              .workflowName(event.getWorkflowMetadata().getWorkflowName())
              .workflowVersion(processDetails.getDefinitionDetails().getVersion())
              .processDefinitionId(processDetails.getDefinitionDetails().getDefinitionId())
              .processInstanceId(processDetails.getProcessId())
              .workflowOwnerId(String.valueOf(event.getWorkflowMetadata().getWorkflowOwnerId()))
              .build();
      event.setMigratedFrom(migratedFrom);

      try {
        processDetailsRepository.updateDefinitionId(
            event.getWorkflowMetadata().getProcessDefinitionId(),
            event.getWorkflowMetadata().getProcessInstanceId());
        DefinitionDetails definitionDetails = processDetails.getDefinitionDetails();
        definitionDetails.setDefinitionId(event.getWorkflowMetadata().getProcessDefinitionId());
        // Setting modified date
        processDetails.setModifiedDate(Timestamp.from(Instant.now()));
        processDetails.setDefinitionDetails(definitionDetails);
        // Incrementing the entityVersion as it is the case of partial update
        processDetails.setEntityVersion(processDetails.getEntityVersion()+1);
        EventingLoggerUtil.logInfo(
            "Publish State Transition Event step=migrationUpdateSuccessful entityId=%s",
            this.getClass().getSimpleName(), event.getBusinessEntityId());
        // Publishing Migration Domain Event here for Process Entity
        domainEventService.publishMigrationEvent(processDetails);
      } catch (Exception e) {
        EventingLoggerUtil.logError(
            "Publish State Transition Event step=migrationUpdateFailed entityId=%s",
            this.getClass().getSimpleName(), event.getBusinessEntityId());
        throw e;
      }
    }
  }

  /**
   * Checks if it is duplicate migration event type.
   *
   * @param event
   * @param processDetails
   * @return boolean
   */
  private boolean isDuplicateMigrationEvent(WorkflowStateTransitionEvents event, ProcessDetails processDetails){
    return isMigrateEvent(event)
        && event.getWorkflowMetadata().getProcessDefinitionId()
        .equals(processDetails.getDefinitionDetails().getDefinitionId());
  }

  /**
   * populate context with workflow name and activity id
   * @param workflowStateTransitionEvent
   */
  private void populateContext(WorkflowStateTransitionEvents workflowStateTransitionEvent){
    Optional.ofNullable(workflowStateTransitionEvent)
        .map(WorkflowStateTransitionEvents::getActivityMetadata)
        .map(ActivityMetaData::getActivityId)
        .ifPresent(activityId -> contextHandler.addKey(WASContextEnums.ACTIVITY_ID, activityId));
    Optional.ofNullable(workflowStateTransitionEvent)
        .map(WorkflowStateTransitionEvents::getWorkflowMetadata)
        .map(WorkflowMetaData::getProcessInstanceId)
        .ifPresent(processInstanceId -> contextHandler.addKey(WASContextEnums.PROCESS_INSTANCE_ID, processInstanceId));
    Optional.ofNullable(workflowStateTransitionEvent)
        .map(WorkflowStateTransitionEvents::getEventType)
        .ifPresent(eventType -> contextHandler.addKey(WASContextEnums.EVENT_TYPE, eventType));
  }

  private boolean isMigrateEvent(WorkflowStateTransitionEvents event){
    return WorkflowConstants.MIGRATE_EVENT_TYPE.equals(event.getEventType());
  }

  @Override
  public EventEntityType getName() {
    return WORKFLOW_TRANSITION_EVENTS;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    //Not needed, this event handler publishes event on to kafka
  }

}