package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.HANDLER_DUZZIT_URL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IDEMPOTENCY_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.OWNER_ID;
import static java.lang.Boolean.TRUE;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.SqsRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/** <AUTHOR> */

/**
 * This class is a RecordListFetcher implementation. It retrieves all records from the appconnect
 * duzzits for the given input request parameters.
 */
@Component(WorkflowBeansConstants.APPCONNECT_RECORD_FETCHER)
@AllArgsConstructor
public class AppconnectRecordListFetcher implements RecordListFetcher {
  private final AppConnectConfig appConnectConfig;
  private final AppConnectWASClient appConnectWASClient;
  private final AuthDetailsService authDetailsService;
  private final WASContextHandler wasContextHandler;
  private final SqsRetryConfig sqsRetryConfig;

  /**
   * This method fetch all records from the appconnect duzzits.
   *
   * @param recordQueryConnectorRequest
   * @return
   */
  @Override
  public RecordQueryConnectorResponse fetchRecords(
      RecordQueryConnectorRequest recordQueryConnectorRequest) {
    logInfo(
        "Fetching Record From Appconnect step=started, connectorId=%s",
        recordQueryConnectorRequest.getConnectorId());
    WASHttpResponse<RecordQueryConnectorResponse> response =
        appConnectWASClient.httpResponse(prepareAppconnectHttpRequest(recordQueryConnectorRequest));
    verifyResponse(response, recordQueryConnectorRequest.getConnectorId());
    logInfo(
        "Fetching Record From Appconnect step=completed, connectorId=%s",
        recordQueryConnectorRequest.getConnectorId());
    return response.getResponse();
  }

  /**
   * This method prepares the http request headers, request parameters and endpoint
   *
   * @param recordQueryConnectorRequest
   * @return
   */
  private WASHttpRequest<MultiValueMap<String, String>, RecordQueryConnectorResponse>
      prepareAppconnectHttpRequest(RecordQueryConnectorRequest recordQueryConnectorRequest) {
    AuthDetails authDetails =
        authDetailsService.getAuthDetailsFromRealmId(
            wasContextHandler.get(WASContextEnums.OWNER_ID));
    return WASHttpRequest.<MultiValueMap<String, String>, RecordQueryConnectorResponse>builder()
        .url(
            prepareRequestEndpoint(
                recordQueryConnectorRequest.getConnectorId(), authDetails.getSubscriptionId()))
        .httpMethod(HttpMethod.POST)
        .requestHeaders(prepareAppconnectRequestHeaders(authDetails))
        .request(prepareRequest(recordQueryConnectorRequest.getRequest()))
        .responseType(new ParameterizedTypeReference<RecordQueryConnectorResponse>() {})
        .build();
  }

  /**
   * This method converts the map to multi value Map
   *
   * @param request
   * @return
   */
  private MultiValueMap<String, String> prepareRequest(Map<String, String> request) {
    MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
    multiValueMap.setAll(request);
    return multiValueMap;
  }

  /**
   * This method prepares the request endpoint for the given connectorId and subscriptionId
   *
   * <pre>
   *   e.g = https://e2e.api.intuit.com/appconnect/connector/intuit-workflows/api/custom-reminder-start-process.json?subscriptionId=***
   * </pre>
   *
   * @param connectorId
   * @param subscriptionId
   * @return
   */
  private String prepareRequestEndpoint(String connectorId, String subscriptionId) {
    return appConnectConfig
        .getConnectorEndpoint()
        .concat(String.format(HANDLER_DUZZIT_URL, WorkflowConstants.INTUIT_WORKFLOWS, connectorId))
        .concat(WorkflowConstants.APPEND_SUBSCRIPTION_ID)
        .concat(subscriptionId);
  }

  /**
   * This method builds all the request headers required for the calling appconnect connectors. It
   * get all the required headers using wasContextHandler
   *
   * @param authDetails
   * @return
   */
  private HttpHeaders prepareAppconnectRequestHeaders(final AuthDetails authDetails) {
    // prepare request headers
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    // call offline ticket to renew the realm ticket
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
    requestHeaders.add(IDEMPOTENCY_KEY, wasContextHandler.get(WASContextEnums.IDEMPOTENCY_KEY));
    requestHeaders.add(INTUIT_TID, wasContextHandler.get(WASContextEnums.INTUIT_TID));
    requestHeaders.add(OWNER_ID, wasContextHandler.get(WASContextEnums.OWNER_ID));
    // populate retry of response in case of error at appconnect
    requestHeaders.add(WorkflowConstants.RETRY_ON_ERROR, Boolean.TRUE.toString());

    return requestHeaders;
  }

  private void logInfo(String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.APPCONNECT_DUZZITS));
  }

  /**
   * This method verify the response and throw retryable exception only for those status codes
   * defined in the sqsRetryConfig
   *
   * @param response
   * @param connectorId
   */
  private void verifyResponse(
      WASHttpResponse<RecordQueryConnectorResponse> response, String connectorId) {
    WorkflowLogger.logInfo("step=verifyResponse statusCode=%s", response.statusCode());
    if (sqsRetryConfig.getStatusCode().contains(response.statusCode())) {
      throw new WorkflowRetriableException(
          WorkflowError.APPCONNECT_DUZZIT_RETRYABLE_EXCEPION, response.getError());
    }
    // non-retryable exception.
    WorkflowVerfiy.verify(
        (!response.isSuccess2xx()
            || null == response.getResponse()
            || isEmpty(response.getResponse().getSuccess())
            || !TRUE.toString().equals(response.getResponse().getSuccess())),
        WorkflowError.APPCONNECT_DUZZIT_CALL_FAILURE,
        connectorId,
        response.getError());
  }
}
