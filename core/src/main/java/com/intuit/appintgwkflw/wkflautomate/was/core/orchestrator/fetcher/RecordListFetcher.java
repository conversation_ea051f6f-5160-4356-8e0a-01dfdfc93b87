package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher;

import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;

/**
 * <AUTHOR>
 */
public interface RecordListFetcher {

  /**
   * This method fetch all the records from the connector.
   *
   * @param recordQueryConnectorRequest
   * @return
   */
  RecordQueryConnectorResponse fetchRecords(
      RecordQueryConnectorRequest recordQueryConnectorRequest);
}
