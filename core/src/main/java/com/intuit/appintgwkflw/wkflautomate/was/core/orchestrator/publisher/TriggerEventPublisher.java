package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/** <AUTHOR> */

/** Publish all the trigger payload to trigger topic */
@Component
@AllArgsConstructor
public class TriggerEventPublisher {

  private final EventPublisherCapability eventPublisherCapability;

  private final WASContextHandler wasContextHandler;
  /**
   * This method publish the trigger event
   *
   * @param trigger
   */
  public void publishTriggerEvent(Trigger trigger) {
    EventingLoggerUtil.logInfo(
        "Publish Trigger Event. step=start_publish payload=%s",
        this.getClass().getSimpleName(), trigger);
    eventPublisherCapability.publish(prepareTriggerEventHeaderEntity(), trigger);
    EventingLoggerUtil.logInfo(
        "Publish Trigger Event. step=published payload=%s",
        this.getClass().getSimpleName(), trigger);
  }

  /**
   * This method prepare trigger event headers.
   *
   * @return
   */
  private EventHeaderEntity prepareTriggerEventHeaderEntity() {
    return EventHeaderEntity.builder()
        .publishEventType(PublishEventType.TRIGGER)
        .eventEntityType(EventEntityType.TRIGGER)
        .entityId(wasContextHandler.get(WASContextEnums.ENTITY_ID))
        .tid(wasContextHandler.get(WASContextEnums.INTUIT_TID))
        .ownerId(wasContextHandler.get(WASContextEnums.OWNER_ID))
        .offeringId(wasContextHandler.get(WASContextEnums.OFFERING_ID))
        .idempotencyKey(wasContextHandler.get(WASContextEnums.IDEMPOTENCY_KEY))
        .intuitUserId(wasContextHandler.get(WASContextEnums.INTUIT_USERID))
        .build();
  }
}
