package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTIVATE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.APP_CONNECT_ACTION_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.HANDLER_DUZZIT_URL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IDEMPOTENCY_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.AppConnectDeleteWorkflowsTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.CreateSubscriptionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.CreateWorkflowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.GetSubscriptionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.RegisterTokenRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.CreateSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.AppConnectWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@AllArgsConstructor
public class AppConnectServiceImpl implements AppConnectService {

  private AppConnectConfig appConnectConfig;

  private AppConnectWASClient wasHttpClient;
  private AuthDetailsService authDetailsService;
  private WASContextHandler wasContextHandler;
  private static final String SUBSCRIPTION="Subscription:";

  /**
   * @param ownerId ownerID
   * @return SubscriptionId
   */
  @Override
  @Trace
  public String getSubscriptionForApp(@Tracer(key = WASContextEnums.OWNER_ID) String ownerId) {
    /* Prepare the request */
    GetSubscriptionRequest getSubscriptionRequest =
        GetSubscriptionRequest.builder()
            .companyId(ownerId)
            .intuitAppId(appConnectConfig.getProviderAppId())
            .endpoint(appConnectConfig.getSubscriptionEndpoint())
            .build();
    /* Get the subscription details from app connect*/
    Map<String, String> requestParams =
        ObjectConverter.convertObject(
            getSubscriptionRequest, new TypeReference<Map<String, String>>() {});
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.setAll(requestParams);
    WASHttpRequest<GetSubscriptionRequest, List<GetSubscriptionResponse>> wasHttpRequest =
        WASHttpRequest.<GetSubscriptionRequest, List<GetSubscriptionResponse>>builder()
            .httpMethod(HttpMethod.GET)
            .request(getSubscriptionRequest)
            .responseType(new ParameterizedTypeReference<List<GetSubscriptionResponse>>() {})
            .url(
                UriComponentsBuilder.fromHttpUrl(getSubscriptionRequest.getEndpoint())
                    .queryParams(queryParams)
                    .toUriString())
            .build();
    WASHttpResponse<List<GetSubscriptionResponse>> subscriptionResponseEntity =
        wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    if (!subscriptionResponseEntity.isSuccess2xx()
        || CollectionUtils.isEmpty(subscriptionResponseEntity.getResponse())) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No subscription details for ownerId=%s", ownerId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.GET_SUBSCRIPTION_DETAILS));
      return null;
    }

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Getting subscription details for ownerId=%s", ownerId));
    // There is only one subscription possible of a company for the app
    return subscriptionResponseEntity.getResponse().get(0).getId();
  }

  /**
   * @param realmId realmId
   * @return SubscriptionId
   */
  @Override
  @Trace
  public String createSubscriptionForApp(@Tracer(key = WASContextEnums.OWNER_ID) String realmId) {
    // Prepare the request
    CreateSubscriptionRequest createSubscriptionRequest =
        CreateSubscriptionRequest.builder()
            .intuitAppId(appConnectConfig.getProviderAppId())
            .companyId(realmId)
            .build();
    // Create the subscription for appconnect
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.add(APP_CONNECT_ACTION_KEY, ACTIVATE);

    WASHttpRequest<CreateSubscriptionRequest, CreateSubscriptionResponse> wasHttpRequest =
        WASHttpRequest.<CreateSubscriptionRequest, CreateSubscriptionResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(createSubscriptionRequest)
            .requestHeaders(createAppSubscriptionRequestHeaders(SUBSCRIPTION + realmId))
            .responseType(new ParameterizedTypeReference<CreateSubscriptionResponse>() {})
            .url(
                UriComponentsBuilder.fromHttpUrl(appConnectConfig.getSubscriptionEndpoint())
                    .queryParams(queryParams)
                    .toUriString())
            .build();
    WASHttpResponse<CreateSubscriptionResponse> subscriptionResponseEntity =
        wasHttpClient.httpResponse(wasHttpRequest);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Creating subscription details for realmId=%s", realmId));
    WorkflowVerfiy.verify(
        !subscriptionResponseEntity.isSuccess2xx(),
        WorkflowError.CREATE_SUBSCRIPTION_FAIL,
        realmId,
        subscriptionResponseEntity.getError());

    // There is only one subscription possible of a company for the app
    return subscriptionResponseEntity.getResponse().getId();
  }

  /**
   * @param subscriptionId app subscription id for the company
   * @param definitionId id of the definition which is deployed in workflow engine
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @return {@link AppConnectSaveWorkflowResponse}
   */
  @Override
  @Trace
  public AppConnectSaveWorkflowResponse createWorkflow(
      String subscriptionId,
      @Tracer(key = WASContextEnums.DEFINITION_ID) String definitionId,
      BpmnModelInstance bpmnModelInstance,
      String definitionName) {
    /* Prepare the request */
    CreateWorkflowRequest.Model model = new CreateWorkflowRequest.Model();
    model.setContent(Bpmn.convertToString(bpmnModelInstance));
    model.setModelType(ModelType.BPMN);
    CreateWorkflowRequest createWorkflowRequest =
        CreateWorkflowRequest.builder()
            .workflowType(AppConnectWorkflowType.BPMN_WAS)
            .externalId(definitionId)
            .name(definitionName)
            .model(model)
            .status(getPollingStatus())
            .endpoint(
                MessageFormat.format(appConnectConfig.getWorkflowCrudEndpoint(), subscriptionId))
            .build();
    WASHttpRequest<CreateWorkflowRequest, AppConnectSaveWorkflowResponse> wasHttpRequest =
        WASHttpRequest.<CreateWorkflowRequest, AppConnectSaveWorkflowResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(createWorkflowRequest)
            .responseType(new ParameterizedTypeReference<AppConnectSaveWorkflowResponse>() {})
            .url(createWorkflowRequest.getEndpoint())
            .build();
    /* Create the workflow in app connect */
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Creating workflow for definitionId=%s", definitionId));

    WASHttpResponse<AppConnectSaveWorkflowResponse> createWorkflowResponseEntity =
        wasHttpClient.httpResponse(wasHttpRequest);
    /* Handle the API error */
    WorkflowVerfiy.verify(
        !createWorkflowResponseEntity.isSuccess2xx(),
        WorkflowError.CREATE_WORKFLOW_FAIL,
        definitionId,
        createWorkflowResponseEntity.getError());
    return createWorkflowResponseEntity.getResponse();
  }

  @Override
  public AppConnectSaveWorkflowResponse activateDeactivateActionWorkflow(
      String workflowId, String subscriptionId, boolean activate) {
    return activateActionWorkflow(workflowId, subscriptionId, activate, null);
  }

  @Override
  public AppConnectSaveWorkflowResponse disableAppConnectWorkflow(
      String workflowId, @NonNull AuthDetails authDetails) {

    return activateActionWorkflow(workflowId, authDetails.getSubscriptionId(), false, authDetails);
  }

  /**
   * @param workflowId for which activation needs to be done
   * @param subscriptionId for that company
   * @param activate
   * @return {@link AppConnectSaveWorkflowResponse}
   */
  @SuppressWarnings("unchecked")
  @Trace
  private AppConnectSaveWorkflowResponse activateActionWorkflow(
      String workflowId, String subscriptionId, boolean activate, AuthDetails authDetails) {

    /* Prepare the request */
    String uri =
        MessageFormat.format(appConnectConfig.getWorkflowCrudEndpoint(), subscriptionId)
            + "/"
            + workflowId;
    String action = activate ? WorkflowConstants.ACTIVATE : WorkflowConstants.DEACTIVATE;

    WorkflowActionRequest workflowActionRequest =
        WorkflowActionRequest.builder().endpoint(uri).action(action).build();
    UriComponentsBuilder uriBuilder =
        UriComponentsBuilder.fromHttpUrl(workflowActionRequest.getEndpoint())
            .queryParam(APP_CONNECT_ACTION_KEY, workflowActionRequest.getAction());
    /* Activate the workflow */
    WASHttpRequest<WorkflowActionRequest, AppConnectSaveWorkflowResponse> wasHttpRequest =
        WASHttpRequest.<WorkflowActionRequest, AppConnectSaveWorkflowResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(workflowActionRequest)
            .responseType(new ParameterizedTypeReference<AppConnectSaveWorkflowResponse>() {})
            .url(uriBuilder.toUriString())
            .build();
    if (null != authDetails) {
      wasHttpRequest = populateOfflineTicketWithRealmId(wasHttpRequest, authDetails);
    }
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Action=%s; for workflow=%s", action, workflowId));
    WASHttpResponse<AppConnectSaveWorkflowResponse> activateWorkflowResponseEntity =
        wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !activateWorkflowResponseEntity.isSuccess2xx(),
        WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL,
        workflowId,
        activateWorkflowResponseEntity.getError());

    return activateWorkflowResponseEntity.getResponse();
  }

  @Override
  public void updateWorkflow(
      @NonNull String workflowId,
      @NonNull String subscriptionId,
      @NonNull String definitionId,
      @NonNull BpmnModelInstance bpmnModelInstance,
      @NonNull String definitionName) {

    /* Prepare the request */
    CreateWorkflowRequest.Model model = new CreateWorkflowRequest.Model();
    model.setContent(Bpmn.convertToString(bpmnModelInstance));
    model.setModelType(ModelType.BPMN);
    CreateWorkflowRequest createWorkflowRequest =
        CreateWorkflowRequest.builder()
            .externalId(definitionId)
            .name(definitionName)
            .model(model)
            .endpoint(
                MessageFormat.format(appConnectConfig.getWorkflowCrudEndpoint(), subscriptionId)
                    + "/"
                    + workflowId)
            .status(getPollingStatus())
            .build();

    WASHttpRequest<CreateWorkflowRequest, Void> wasHttpRequest =
        WASHttpRequest.<CreateWorkflowRequest, Void>builder()
            .httpMethod(HttpMethod.PUT)
            .request(createWorkflowRequest)
            .responseType(new ParameterizedTypeReference<Void>() {})
            .url(createWorkflowRequest.getEndpoint())
            .build();
    /* Update the workflow in app connect */
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Updating workflow for definitionId=%s", definitionId));
    WASHttpResponse<Void> updateWorkflowResponseEntity = wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !updateWorkflowResponseEntity.isSuccess2xx(),
        WorkflowError.UPDATE_WORKFLOW_FAIL,
        definitionId,
        updateWorkflowResponseEntity.getError());
  }

  @Override
  public void deleteWorkflow(@NonNull String workflowId, @NonNull String subscriptionId) {
    deleteWorkflow(workflowId, subscriptionId, null);
  }

  @Override
  public void registerToken(String ownerId, String entityType, String entityOperations) {
    WorkflowVerfiy.verifyNullForList(
        WorkflowError.INVALID_INPUT,
        entityOperations, entityType
    );

    RegisterTokenRequest registerTokenRequest = RegisterTokenRequest.builder()
        .entityType(entityType).entityOperations(entityOperations).companyId(ownerId)
        .appId(appConnectConfig.getProviderAppId())
        .endpoint(prepareRequestEndpoint(WorkflowConstants.INTUIT_WORKFLOWS,
            WorkflowConstants.REGISTER_NOTIFICATIONS_WEBHOOKS)).build();

    Map<String, String> requestParams =
        ObjectConverter.convertObject(
            registerTokenRequest, new TypeReference<Map<String, String>>() {
            });
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.setAll(requestParams);

    WASHttpRequest<RegisterTokenRequest, Void> wasHttpRequest =
        WASHttpRequest.<RegisterTokenRequest, Void>builder()
            .httpMethod(HttpMethod.GET)
            .responseType(new ParameterizedTypeReference<Void>() {
            })
            .url(UriComponentsBuilder.fromHttpUrl(registerTokenRequest.getEndpoint())
                .queryParams(queryParams)
                .toUriString())
            .build();

    WASHttpResponse<Void> registerTokenResponseEntity = wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !registerTokenResponseEntity.isSuccess2xx(),
        WorkflowError.REGISTER_TOKEN_FAIL,
        registerTokenResponseEntity.getError());
  }

  private String prepareRequestEndpoint(String handlerId, String duzzitId) {
    return appConnectConfig.getConnectorEndpoint()
        .concat(String.format(HANDLER_DUZZIT_URL, handlerId, duzzitId));
  }

  @SuppressWarnings("unchecked")
  @Override
  public void deleteWorkflow(
      @NonNull String workflowId, @NonNull String subscriptionId, AuthDetails authDetails) {
    /* Prepare the request */

    CreateWorkflowRequest createWorkflowRequest =
        CreateWorkflowRequest.builder()
            .endpoint(
                MessageFormat.format(appConnectConfig.getWorkflowCrudEndpoint(), subscriptionId)
                    + "/"
                    + workflowId)
            .build();

    WASHttpRequest<Void, Void> wasHttpRequest =
        WASHttpRequest.<Void, Void>builder()
            .httpMethod(HttpMethod.DELETE)
            .url(createWorkflowRequest.getEndpoint())
            .responseType(new ParameterizedTypeReference<Void>() {})
            .build();

    if (null != authDetails) {
      wasHttpRequest = populateOfflineTicketWithRealmId(wasHttpRequest, authDetails);
    }
    /* Delete the workflow in app connect */
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Deleting workflow for workflowId=%s", workflowId));

    WASHttpResponse<Void> updateWorkflowResponseEntity = wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !updateWorkflowResponseEntity.isSuccess2xx(),
        WorkflowError.DELETE_WORKFLOW_FAIL,
        workflowId,
        updateWorkflowResponseEntity.getError());
  }

  /**
   * unsubscribe from app connect using user offline Ticket.
   *
   * @param authDetails : Authorization Details
   * @return AppConnectUnsubscribeResponse return unsubscription response
   */
  @SuppressWarnings("unchecked")
  @Override
  public AppConnectUnsubscribeResponse unsubscribe(
      AuthDetails authDetails, boolean useOfflineTicket) {

    /* Prepare the request */
    String uri =
        new StringBuilder(appConnectConfig.getSubscriptionEndpoint())
            .append("/")
            .append(authDetails.getSubscriptionId())
            .toString();

    String action = WorkflowConstants.UNSUBSCRIBE;

    WorkflowActionRequest workflowActionRequest =
        WorkflowActionRequest.builder().endpoint(uri).action(action).build();
    UriComponentsBuilder uriBuilder =
        UriComponentsBuilder.fromHttpUrl(workflowActionRequest.getEndpoint())
            .queryParam(APP_CONNECT_ACTION_KEY, workflowActionRequest.getAction());

    WASHttpRequest<WorkflowActionRequest, AppConnectUnsubscribeResponse> wasHttpRequest =
        WASHttpRequest.<WorkflowActionRequest, AppConnectUnsubscribeResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(workflowActionRequest)
            .responseType(new ParameterizedTypeReference<AppConnectUnsubscribeResponse>() {})
            .url(uriBuilder.toUriString())
            .build();
    if (useOfflineTicket) {
      wasHttpRequest = populateOfflineTicketWithRealmId(wasHttpRequest, authDetails);
    }
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Action=%s; for subscriptionId=%s", action, authDetails.getSubscriptionId()));
    WASHttpResponse<AppConnectUnsubscribeResponse> unsubscribeSubscriptionResponse =
        wasHttpClient.httpResponse(wasHttpRequest);

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !unsubscribeSubscriptionResponse.isSuccess2xx(),
        WorkflowError.UNSUBSCRIBE_WORKFLOW_FAIL,
        unsubscribeSubscriptionResponse.getError());

    return unsubscribeSubscriptionResponse.getResponse();
  }

  @SuppressWarnings("unchecked")
  @Override
  public List<AppConnectWorkflowResponse> getAppConnectWorkflows(
      String realmId, AuthDetails authDetails) {

    StringBuilder workflowUrl =
        new StringBuilder(
            MessageFormat.format(
                appConnectConfig.getWorkflowCrudEndpoint(), authDetails.getSubscriptionId()));

    WASHttpRequest<Void, List<AppConnectWorkflowResponse>> wasHttpRequest =
        WASHttpRequest.<Void, List<AppConnectWorkflowResponse>>builder()
            .httpMethod(HttpMethod.GET)
            .responseType(new ParameterizedTypeReference<List<AppConnectWorkflowResponse>>() {})
            .url(workflowUrl.toString())
            .build();
    wasHttpRequest = populateOfflineTicketWithRealmId(wasHttpRequest, authDetails);
    WASHttpResponse<List<AppConnectWorkflowResponse>> workflowResponse =
        wasHttpClient.httpResponse(wasHttpRequest);

    return workflowResponse.getResponse();
  }

  /**
   * Populates offline ticket of the realmId in the Auth Header fo the request to AppConnect
   *
   * @param wasHttpRequest request in which to add Authorization Header with value as offlineTicket
   * @return WASHttpRequest with Auth Header populated
   */
  @SuppressWarnings("rawtypes")
  private WASHttpRequest populateOfflineTicketWithRealmId(
      WASHttpRequest wasHttpRequest, AuthDetails authDetails) {
    HttpHeaders requestHeaders = new HttpHeaders();
    // Prepare auth header
    requestHeaders.set(
        WorkflowConstants.AUTHORIZATION_HEADER,
        authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
    return wasHttpRequest.toBuilder().requestHeaders(requestHeaders).build();
  }

  /**
   * This method calls the AppConnect to delete workflow(s). Multiple calls are being made to delete
   * workflow. The tasks are run in parallel.
   *
   * @param workflowIds : List of Workflow ids
   * @param authDetails : auth details of the company
   * @return
   */
  @Override
  public void deleteWorkflows(List<String> workflowIds, @NonNull AuthDetails authDetails) {
    final String realmId = String.valueOf(authDetails.getOwnerId());
    if (CollectionUtils.isEmpty(workflowIds)) {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Empty workflowId list, realmId=%s", realmId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
      return;
    }

    List<Task> deleteAppConnectWorkflowList =
        workflowIds.stream()
            .map(workflowId -> new AppConnectDeleteWorkflowsTask(this, workflowId))
            .collect(Collectors.toList());

    final State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, authDetails.getSubscriptionId());
    inputRequest.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetails);
    new RxExecutionChain(inputRequest)
        .next(deleteAppConnectWorkflowList.toArray(new Task[deleteAppConnectWorkflowList.size()]))
        .execute();
  }

  /** @return polling frequency status */
  private CreateWorkflowRequest.Status getPollingStatus() {

    return new CreateWorkflowRequest.Status(
        new CreateWorkflowRequest.PollingStatus(appConnectConfig.getPollingFrequency()));
  }

  /**
   * This method prepare the request headers for the appconnect create subscription calls
   *
   * @param idempotencyKey
   * @return
   */
  private HttpHeaders createAppSubscriptionRequestHeaders(String idempotencyKey) {
    HttpHeaders requestHeaders = new HttpHeaders();
    if (appConnectConfig.isEnableIdempotency()) {
      requestHeaders.add(IDEMPOTENCY_KEY, idempotencyKey);
    }
    requestHeaders.add(INTUIT_TID, wasContextHandler.get(WASContextEnums.INTUIT_TID));
    return requestHeaders;
  }
}
