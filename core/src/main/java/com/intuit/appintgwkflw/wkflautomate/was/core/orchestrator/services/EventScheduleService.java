package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.payments.schedule.EventSchedule;
import java.util.List;

/** <AUTHOR> */
public interface EventScheduleService {

  /**
   * This method creates schedules
   *
   * @param eventSchedules
   * @param realmId
   * @return Created schedules
   */
  List<EventScheduleResponse> createSchedules(List<EventSchedule> eventSchedules, String realmId);

  /**
   * This method update the schedules
   *
   * @param eventSchedules
   * @param realmId
   * @return
   */
  List<EventScheduleResponse> updateSchedules(List<EventSchedule> eventSchedules, String realmId);
}
