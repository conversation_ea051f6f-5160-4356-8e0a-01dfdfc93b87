package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.EventScheduleClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.payments.schedule.EventSchedule;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class EventScheduleServiceImpl implements EventScheduleService {
  private final EventScheduleConfig eventScheduleConfig;
  private final EventScheduleClient eventScheduleClient;
  private final OfflineTicketClient offlineTicketClient;

  /**
   * This method create schedules
   *
   * @param eventSchedules
   * @return
   */
  @Override
  public List<EventScheduleResponse> createSchedules(
      List<EventSchedule> eventSchedules, String realmId) {
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(eventSchedules),
        WorkflowError.INPUT_INVALID,
        "Event schedules create payload");
    List<EventScheduleResponse> scheduleResponseList = invokeScheduleService(eventSchedules, realmId);
    WorkflowLogger.logInfo("Schedules_created response=%s realmId=%s", scheduleResponseList, realmId);
    return scheduleResponseList;
  }

  /**
   * This method updates the schedules
   *
   * @param eventSchedules
   * @param realmId
   * @return
   */
  @Override
  public List<EventScheduleResponse> updateSchedules(
      List<EventSchedule> eventSchedules, String realmId) {
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(eventSchedules),
        WorkflowError.INPUT_INVALID,
        "Event schedules update payload");
    List<EventScheduleResponse> responses = invokeScheduleService(eventSchedules, realmId);
    WorkflowLogger.logInfo("Schedules_updated response=%s realmId=%s", responses, realmId);
    return responses;
  }

  /**
   * Invoke the schedule service
   *
   * @param eventSchedules
   * @param realmId
   * @return
   */
  private List<EventScheduleResponse> invokeScheduleService(
      List<EventSchedule> eventSchedules, String realmId) {
    WASHttpResponse<List<EventScheduleResponse>> response =
        eventScheduleClient.httpResponse(prepareRequestPayload(eventSchedules, realmId));
    // verify the response code
    WorkflowVerfiy.verify(
        !response.isSuccess2xx() || CollectionUtils.isEmpty(response.getResponse()),
        WorkflowError.EVENT_SCHEDULE_CALL_FAILURE,
        response.getError());
    return response.getResponse();
  }

  /**
   * Prepare the request payload to invoke ESS
   *
   * @param eventSchedules
   * @param realmId
   * @return
   */
  private WASHttpRequest<List<EventSchedule>, List<EventScheduleResponse>> prepareRequestPayload(
      List<EventSchedule> eventSchedules, String realmId) {
    return WASHttpRequest.<List<EventSchedule>, List<EventScheduleResponse>>builder()
        .url(eventScheduleConfig.getUrl())
        .httpMethod(HttpMethod.POST)
        .requestHeaders(prepareHttpHeaders(realmId))
        .request(eventSchedules)
        .responseType(new ParameterizedTypeReference<List<EventScheduleResponse>>() {})
        .build();
  }

  /**
   * Prepare the http headers
   *
   * @param realmId
   * @return
   */
  private HttpHeaders prepareHttpHeaders(String realmId) {
    String requestId = UUID.randomUUID().toString();
    WorkflowLogger.logInfo(
        "Request id for schedule call is=%s for realmId=%s", requestId, realmId);
    HttpHeaders httpHeaders =
        EventScheduleServiceUtil.getHttpHeaders(realmId, requestId);
    // set authHeader
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    return httpHeaders;
  }
}
