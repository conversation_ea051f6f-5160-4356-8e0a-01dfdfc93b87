package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.IdentityGraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * Service layer to abstrct Identity APIs
 */
@Component
@AllArgsConstructor
public class IdentityService {

    private IdentityGraphqlClient identityGraphqlClient;

    /**
     * Get the persona id for current user of the realm
     *
     * @param realmId authId
     * @return Persona id
     */
    public String getPersonaId(final String realmId, final String authId) {
        return Optional.ofNullable(identityGraphqlClient.getPersonaId(realmId, authId))
                // If the personaId is not found in first call, it means that accountant
                // is accessing the client company. So, get the accountant persona details.
                .orElseGet(() -> identityGraphqlClient.getAccountantPersonaId(realmId, authId));
    }

    /**
     * Validates users using authIds and returns map of user->Persona(containing email id, persona id)
     * @param realmId realmId
     * @param userIds list of userIds
     * @return map (k,v) -> (userId, email)
     */
    public Map<String, Persona> getRealmPersonas(final String realmId, Set<String> userIds){
        return identityGraphqlClient.getRealmPersonas(realmId, userIds);
    }

    public void authorizeDelegation(String realmId) {
        identityGraphqlClient.createTrustGrant(realmId);
    }
}
