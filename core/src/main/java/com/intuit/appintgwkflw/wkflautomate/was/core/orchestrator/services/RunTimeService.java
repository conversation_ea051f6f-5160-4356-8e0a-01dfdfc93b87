package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;

import java.util.Map;

public interface RunTimeService {

  /**
   * Process trigger message and start/trigger the process  =>
   * invoke handler based on the payload send by calling app
   *
   * @param triggerMessage
   * @return response indicating status of process
   */
  WorkflowGenericResponse processTriggerMessage(Map<String, Object> triggerMessage);

  /**
   * Process evaluate rules message and evaluate rules for given payload  =>
   * invoke handler based on the payload send by calling app
   *
   * @param evaluateRulesMessage
   * @return response indicating status of rule evaluation
   */
  WorkflowGenericResponse processEvaluateRulesMessage(Map<String, Object> evaluateRulesMessage);

  /**
   * It provides support for triggering processes if a user has single/multiple enabled definition.
   *
   * @param triggerMessage input trigger payload
   * @return {@link WorkflowGenericResponse}
   */
  WorkflowGenericResponse processTriggerMessageV2(Map<String, Object> triggerMessage);

  /**
   * Process evaluate rules message and invoke trigger handler only if the evaluation output is
   * true
   *
   * @param evaluateAndTriggerMessage the evaluate and trigger message
   * @return response indicating status of rule evaluation
   */
  WorkflowGenericResponse processEvaluateAndTriggerMessage(
      Map<String, Object> evaluateAndTriggerMessage);
  
  /**
   * This method fetches the Definition & schedule details.
   * Calls the corresponding duzzit to fetch the Txn details.
   * Starts the process if details are found.
   *
   * @param triggerNowRequest
   */
  WorkflowGenericResponse runNow(TriggerNowRequest triggerNowRequest);
}
