package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.TEMPLATE_DOES_NOT_EXIST;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ONDEMAND_FLOW_ENABLED_FF;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.OnDemandApprovalConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CamundaProcessEngineRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.publisher.ScheduleNowService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.DefaultRuleHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.DefaultTriggerHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.asyncRetry.RetryTriggerEventErrorService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTemplateUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.EvaluationResult;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowEvaluateRuleResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.Authorization;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RunTimeServiceImpl implements RunTimeService {

  private static final String[] DMN_OUTPUTS = {
    "approvalRequired", "sendReminder", "decisionResult"
  };

  private final V3RunTimeHelper runTimeHelper;

  private final WASContextHandler contextHandler;

  private final RetryTriggerEventErrorService retryTriggerEventErrorService;

  private final OnDemandHelper onDemandHelper;

  private final OnDemandApprovalConfig onDemandApprovalConfig;

  private final ProcessDetailsRepository processDetailsRepository;

  private final ScheduleNowService scheduleNowService;

  private final AuthDetailsServiceHelper authDetailsServiceHelper;

  private final IXPManager ixpManager;

  private final TemplateService templateService;


  @Override
  public WorkflowGenericResponse processTriggerMessage(final Map<String, Object> triggerMessage) {

    try {
      TriggerProcessDetails triggerProcessDetails = TriggerProcessDetails.builder().triggerMessage(triggerMessage).build();
      String workflowType = getWorkflowType(triggerProcessDetails);
      // If on-demand approval is enabled, check if the trigger is for on-demand approval
      // To-do : remove the feature flag check for on-demand approval
      boolean isOnDemandFlowEnabled = ixpManager.getBoolean(ONDEMAND_FLOW_ENABLED_FF, false);
      if(onDemandApprovalConfig.isEnabled() && isOnDemandFlowEnabled && WorkflowConstants.APPROVAL_WORKFLOW_TYPE.equalsIgnoreCase(workflowType)){
        return handleWhenOnDemandIsEnabled(triggerProcessDetails);
      }
      return TriggerHandlers.getHandler(DefaultTriggerHandler.class.getSimpleName())
              .executeTrigger(triggerProcessDetails);

    } catch (Exception ex) {
      if (ex instanceof WorkflowRetriableException
          || ex instanceof CamundaProcessEngineRetriableException) {
        retryTriggerEventErrorService.processFailedTriggerEvent(triggerMessage,
            TriggerTargetAPI.TRIGGER_V1);
      }
      throw ex;
    }
  }

  @Override
  public WorkflowGenericResponse processEvaluateRulesMessage(
      final Map<String, Object> evaluateRulesMessage) {

    return RuleEvaluationHandlers.getHandler(DefaultRuleHandler.class.getSimpleName())
        .evaluateRules(evaluateRulesMessage);
  }

  /**
   * Fetches template details and throws {@link WorkflowGeneralException} if details not found else
   * call the trigger matching the definition type.
   */
  @Override
  public WorkflowGenericResponse processTriggerMessageV2(final Map<String, Object> triggerMessage) {

    /**
     * template details will be sorted in desc by version so will fetch the latest template to
     * decide which trigger handler to call.
     */
    try {
      List<TemplateDetails> templateDetails = getTemplateDetails(triggerMessage);

      WorkflowVerfiy.verify(CollectionUtils.isEmpty(templateDetails), TEMPLATE_DOES_NOT_EXIST);

      TemplateDetails latestTemplateVersion = templateDetails.stream().findFirst().get();

      // Check if non-realm system user use case
      SystemTemplateUtil.checkNonRealmSystemUser(latestTemplateVersion);

      // based on the definition Type call the trigger handler
      return TriggerHandlers.getHandler(
              getDefinitionType(latestTemplateVersion).name())
          .executeTrigger(
              TriggerProcessDetails.builder()
                  .triggerMessage(triggerMessage)
                  .templateDetails(templateDetails)
                  .build());
    } catch (Exception ex) {
      if (ex instanceof WorkflowRetriableException
          || ex instanceof CamundaProcessEngineRetriableException) {
        retryTriggerEventErrorService.processFailedTriggerEvent(triggerMessage,
            TriggerTargetAPI.TRIGGER_V2);
      }
      throw ex;
    }
  }

  @Override
  public WorkflowGenericResponse processEvaluateAndTriggerMessage(
      final Map<String, Object> evaluateAndTriggerMessage) {

    final WorkflowGenericResponse evaluationResponse
        = processEvaluateRulesMessage(evaluateAndTriggerMessage);
    final boolean output = getEvaluationOutput(evaluationResponse);
    if (output) {
      return processTriggerMessageV2(evaluateAndTriggerMessage);
    }
    return runTimeHelper.getTriggerResponse(
        ResponseStatus.SUCCESS,
        TriggerStatus.NO_ACTION,
        null);
  }

  /* This flow will be used by AppConnect only for now. AppConnect passes a WorkflowId in the
   * request which will be used to pick the definition corresponding to that specific workflow.
   * In case of multiple enabled definitions, the WorkflowId is mandatory since we need to evaluate
   * and trigger only one workflow. If WorkflowId is not present, then Evaluate Rules will return
   * true if any of the definitions meet the criteria, Trigger V2 will trigger all enabled definitions.
   * TODO: Revisit later to optimize the code to trigger only the workflows which meet the criteria
   */
  private boolean getEvaluationOutput(final WorkflowGenericResponse evaluationResponse) {

    final WorkflowEvaluateRuleResponse evaluateRuleResponse
        = (WorkflowEvaluateRuleResponse) evaluationResponse.getResponse();
    final EvaluationResult evaluationResult = evaluateRuleResponse.getEvaluationResult();
    final List<Map<String, Object>> results = evaluationResult.getResult();
    return results.stream().anyMatch(this::checkResultValue);

  }

  @SuppressWarnings("unchecked")
  private boolean checkResultValue(final Map<String, Object> result) {

    return Optional.ofNullable(result.get("details"))
        .map(details -> ((List<Map<String, Object>>) details).stream()
            .findFirst()
            .map(detail -> Arrays.stream(DMN_OUTPUTS)
                .map(detail::get)
                .filter(Objects::nonNull)
                .findFirst()
                .map(output -> getOutputValue((Map<String, Object>) output))
                .orElse(false)
            ).orElse(false)
        ).orElse(false);
  }

  private boolean getOutputValue(Map<String, Object> output) {

    return Boolean.parseBoolean(Objects.toString(output.get("value")));
  }

  /**
   * returns definition type for a given template.If not found return DefinitionType.USER as
   * default.
   *
   * @param templateDetails template details
   * @return {@link DefinitionType}
   */
  private DefinitionType getDefinitionType(TemplateDetails templateDetails) {

    return Optional.ofNullable(templateDetails.getDefinitionType()).orElse(DefinitionType.USER);
  }

  /**
   * get template details for all versions for a given workflow.
   *
   * @param triggerDetails input trigger detail
   * @return {@link List<TemplateDetails>}
   */
  private List<TemplateDetails> getTemplateDetails(final Map<String, Object> triggerDetails) {
    final TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(triggerDetails, contextHandler);
    return runTimeHelper.getTemplateDetails(transactionEntity);
  }

 /**
 * This method handles the scenario when on-demand approval is enabled.
 * It checks if the trigger is for on-demand approval and executes the appropriate trigger handler.
 *
 * @param triggerProcessDetails The details of the trigger process.
 * @return WorkflowGenericResponse The response of the workflow process.
 */
private WorkflowGenericResponse handleWhenOnDemandIsEnabled(TriggerProcessDetails triggerProcessDetails){
  // Get the authorization header from the context handler
  final Authorization authorization = new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
  final TransactionEntity transactionEntity =
          TransactionEntityFactory.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);

  // Fetch the process details based on the entity ID and owner ID and parentId is Null
  // can get multiple processes, atmost 1 for approval and multiple for reminder/notification for the same record id - owner id combination
  List<ProcessDetails> processDetails = processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndParentIdIsNull(
          transactionEntity.getEntityId(), Long.parseLong(authorization.getRealm()),
          Arrays.asList(ACTIVE, ERROR)).orElse(Collections.emptyList());

  // Filtering out to keep only on demand approval processes
  Optional<ProcessDetails> approvalProcess = processDetails.stream()
      .filter(processDetail -> (
          ObjectUtils.isEmpty(processDetail.getDefinitionDetails().getRecordType()) &&
              StringUtils.equalsIgnoreCase(WorkflowConstants.SYSTEM_OWNER_ID, String.valueOf(processDetail.getDefinitionDetails().getOwnerId()))
              && StringUtils.equalsIgnoreCase(CustomWorkflowType.APPROVAL.getTemplateName(),processDetail.getDefinitionDetails().getDefinitionKey())))
      .findFirst();

  // Check if the transaction is for on-demand approval
  if(onDemandHelper.checkIfOnDemand(transactionEntity, approvalProcess)) {
    WorkflowLogger.logInfo("The trigger request for ownerId=%s and entityId=%s is for on demand approval", authorization.getRealm(),transactionEntity.getEntityId());
    // If it is, add a flag to the trigger message
    triggerProcessDetails.getTriggerMessage().put(WorkflowConstants.ON_DEMAND_APPROVAL, true);
    triggerProcessDetails.setTemplateDetails(
        templateService.getAllEnabledTemplatesByName(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE));

    // Execute the system trigger handler, since on-demand approval will have a system definition

    // In case of no subscription for On demand scenario, create a subscription in appConnect and add locale, if present in case of start process trigger call.
    if(approvalProcess.isEmpty()) {
      WorkflowLogger.logInfo("Creating new subscription for on demand approval for ownerId=%s, entityId=%s", authorization.getRealm(),transactionEntity.getEntityId());
      authDetailsServiceHelper.populateAuthDetailsSync(authorization, true);

      Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).ifPresent(localeString -> triggerProcessDetails.getTriggerMessage().put(WorkflowConstants.INTUIT_WAS_LOCALE, localeString));

    }

    WorkflowGenericResponse workflowGenericResponse= TriggerHandlers.getHandler(DefinitionType.SYSTEM.name())
        .executeTrigger(triggerProcessDetails, approvalProcess);

    // Converting system response to sdef since that is expected by QBO
    return Optional.ofNullable(workflowGenericResponse)
        .map(WorkflowGenericResponse::getResponse)
        .filter(WorkflowTriggersResponse.class::isInstance)
        .map(WorkflowTriggersResponse.class::cast)
        .flatMap(triggersResponse -> triggersResponse.getTriggers().stream().findFirst())
        .map(triggerResponse -> runTimeHelper.getTriggerResponse(workflowGenericResponse.getStatus(), triggerResponse.getStatus(),triggerResponse.getProcessId()))
        .orElse(workflowGenericResponse);

  }

  // If the transaction is not for on-demand approval, execute the default trigger handler
  return TriggerHandlers.getHandler(DefaultTriggerHandler.class.getSimpleName())
          .executeTrigger(triggerProcessDetails);
}


  private String getWorkflowType(TriggerProcessDetails triggerProcessDetails){
    TransactionEntity transactionEntity = TransactionEntityFactory.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);
    return transactionEntity.getWorkflowType();
  }

	/**
	 * Fetched the schedule details and trigger the process. In case of scheduler
	 * Action is process is started right away, for other cases like
	 * Send/Update/Reminder Event is published in trigger topic.
	 * 
	 * @return {@link WorkflowGenericResponse} Which has details of schedule event
	 *         that was executed and record id that was triggered. In case trigger
	 *         details map is empty means process was not triggered as no txn found
	 *         from polling via calling app-connect duzzit.
	 */
	public WorkflowGenericResponse runNow(TriggerNowRequest triggerNowRequest) {
		return scheduleNowService.runNow(triggerNowRequest);
	}
}
