package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.v4.workflows.Definition;

import java.util.List;

/** <AUTHOR> */
public interface SchedulingService {
    /**
     * Creates schedules based on the provided event schedules and realm ID.
     *
     * @param eventSchedules List of event schedules to be created.
     * @param realmId Identifier for the realm in which the schedules are to be created.
     * @return List of created schedules.
     */
    List<SchedulingSvcResponse> createSchedules(List<SchedulingSvcRequest> eventSchedules, String realmId);

    /**
     * Updates existing schedules based on the provided event schedules and realm ID.
     *
     * @param eventSchedules List of event schedules to be updated.
     * @param realmId Identifier for the realm in which the schedules are to be updated.
     * @return List of updated schedules.
     */
    List<SchedulingSvcResponse> updateSchedules(List<SchedulingSvcRequest> eventSchedules, String realmId);

    /**
     * Retrieves schedules based on the provided schedule IDs and realm ID.
     *
     * @param scheduleIds List of schedule IDs to be retrieved.
     * @param realmId Identifier for the realm in which the schedules are to be retrieved.
     * @return List of retrieved schedules.
     */

    List<SchedulingSvcResponse> getSchedules(List<String> scheduleIds, String realmId);

    /**
     * Deletes schedules based on the provided schedule IDs and realm ID.
     *
     * @param scheduleIds List of schedule IDs to be deleted.
     * @param realmId Identifier for the realm in which the schedules are to be deleted.
     * @return List of deleted schedules.
     */
    List<SchedulingSvcResponse> deleteSchedules(List<String> scheduleIds, String realmId);

    /**
     * Checks if the scheduling flow is enabled based on the provided definition details and realm ID.
     *
     * @param definitionDetails the details of the definition
     * @param realmId the ID of the realm
     * @return true if the scheduling flow is enabled, false otherwise
     */
    boolean isEnabled(DefinitionDetails definitionDetails, String realmId);

    /**
     * Checks if the scheduling flow is enabled based on the provided definition and realm ID.
     *
     * @param definition the definition object
     * @param realmId the ID of the realm
     * @return true if the scheduling flow is enabled, false otherwise
     */
    boolean isEnabled(Definition definition, String realmId);

    /**
     * Checks if the given definition details have been migrated.
     *
     * @param definitionDetails The details of the definition to check.
     * @return true if the definition details have been migrated, false otherwise.
     */
    boolean isMigrated(DefinitionDetails definitionDetails);
}
