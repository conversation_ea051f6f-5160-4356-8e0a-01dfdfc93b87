package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingSvcAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.v4.workflows.Definition;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class SchedulingServiceImpl implements SchedulingService {
    private final SchedulingSvcAdapter schedulingSvcAdapter;
    private final FeatureFlagManager featureFlagManager;
    private final SchedulerDetailsRepository schedulerDetailsRepository;
    private final SchedulingSvcConfig schedulingSvcConfig;

    /**
     * Creates schedules based on the provided event schedules and realm ID.
     *
     * @param eventSchedules List of event schedules to be created.
     * @param realmId Identifier for the realm in which the schedules are to be created.
     * @return List of created schedules.
     */
    @Override
    public List<SchedulingSvcResponse> createSchedules(List<SchedulingSvcRequest> eventSchedules, String realmId) {
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(eventSchedules),
                WorkflowError.INPUT_INVALID,
                "Event schedules create payload");
        List<SchedulingSvcResponse> scheduleResponseList = schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(eventSchedules, false, realmId);
        WorkflowLogger.logInfo("Schedules_created response=%s realmId=%s", ObjectConverter.toJson(scheduleResponseList), realmId);
        return scheduleResponseList;
    }

    /**
     * Updates existing schedules based on the provided event schedules and realm ID.
     *
     * @param eventSchedules List of event schedules to be updated.
     * @param realmId Identifier for the realm in which the schedules are to be updated.
     * @return List of updated schedules.
     */
    @Override
    public List<SchedulingSvcResponse> updateSchedules(List<SchedulingSvcRequest> eventSchedules, String realmId) {
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(eventSchedules),
                WorkflowError.INPUT_INVALID,
                "Event schedules update payload");
        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(eventSchedules, true, realmId);
        WorkflowLogger.logInfo("Schedules_updated response=%s realmId=%s", ObjectConverter.toJson(responses), realmId);
        return responses;
    }

    /**
     * Retrieves schedules based on the provided schedule IDs and realm ID.
     *
     * @param scheduleIds List of schedule IDs to be retrieved.
     * @param realmId Identifier for the realm in which the schedules are to be retrieved.
     * @return List of retrieved schedules.
     */
    @Override
    public List<SchedulingSvcResponse> getSchedules(List<String> scheduleIds, String realmId) {
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(scheduleIds),
                WorkflowError.INPUT_INVALID,
                "Event schedules get payload");
        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForGet(scheduleIds, realmId);
        WorkflowLogger.logInfo("Get Schedules response=%s realmId=%s", responses, realmId);
        return responses;
    }

    /**
     * Deletes schedules based on the provided schedule IDs and realm ID.
     *
     * @param scheduleIds List of schedule IDs to be deleted.
     * @param realmId Identifier for the realm in which the schedules are to be deleted.
     * @return List of deleted schedules.
     */
    @Override
    public List<SchedulingSvcResponse> deleteSchedules(List<String> scheduleIds, String realmId) {
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(scheduleIds),
                WorkflowError.INPUT_INVALID,
                "Event schedules delete payload");
        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForDelete(scheduleIds, realmId);
        WorkflowLogger.logInfo("Delete Schedules response=%s realmId=%s", responses, realmId);
        return responses;
    }

    @Override
    public boolean isEnabled(DefinitionDetails definitionDetails, String realmId) {
        String workflowName = definitionDetails.getTemplateDetails().getTemplateName();
        return featureFlagManager.getBoolean(SchedulingServiceUtil.getFFName(workflowName), realmId) || SchedulingServiceUtil.isSchedulingFlowEnabled(definitionDetails.getPlaceholderValue());
    }

    @Override
    public boolean isEnabled(Definition definition, String realmId) {
        String workflowName = definition.getName();
        return featureFlagManager.getBoolean(SchedulingServiceUtil.getFFName(workflowName), realmId) || (ObjectUtils.isNotEmpty(definition.getRecurrence()) && SchedulingServiceUtil.isSchedulingFlowEnabled(definition.getRecurrence()));
    }

    /**
     * Checks if the given definition details have been migrated.
     *
     * @param definitionDetails The details of the definition to check.
     * @return true if the definition details have been migrated, false otherwise.
     */
    @Override
    public boolean isMigrated(DefinitionDetails definitionDetails) {
        Optional<List<SchedulerDetails>> schedulerDetailsList = schedulerDetailsRepository.findByDefinitionDetails(definitionDetails);
        return schedulingSvcConfig.isMigrationEnabled()
                && schedulerDetailsList.isPresent()
                && !schedulerDetailsList.get().isEmpty()
                && schedulerDetailsList.get().stream().allMatch(details -> Boolean.TRUE.equals(details.getIsMigrated()));
    }

}
