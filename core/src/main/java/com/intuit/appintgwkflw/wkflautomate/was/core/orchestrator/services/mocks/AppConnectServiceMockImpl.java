package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.CreateSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import java.util.Arrays;
import java.util.List;
import lombok.NonNull;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by ssingh14 on 27/04/20. This implementation is only used in the prf environment. The
 * intention to use this is to mock the app connect network calls in the prf environment. TODO:
 * Remove this class once app connect is available in prf environment.
 */
public class AppConnectServiceMockImpl implements AppConnectService {

  @Autowired
  AppConnectConfig appConnectConfig;

  @Override
  public String getSubscriptionForApp(String realmId) {
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    String id = "123";
    getSubscriptionResponse.setId(id);
    return getSubscriptionResponse.getId();
  }

  @Override
  public String createSubscriptionForApp(String realmId) {
    CreateSubscriptionResponse createSubscriptionResponse = new CreateSubscriptionResponse();
    String id = "123";
    createSubscriptionResponse.setId(id);
    return createSubscriptionResponse.getId();
  }

  @Override
  public AppConnectSaveWorkflowResponse createWorkflow(
      String subscriptionId,
      String definitionId,
      BpmnModelInstance bpmnModelInstance,
      String definitionName) {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    appConnectSaveWorkflowResponse.setId(appConnectConfig.getWorkflowId());
    return appConnectSaveWorkflowResponse;
  }

  @Override
  public AppConnectSaveWorkflowResponse activateDeactivateActionWorkflow(
      String workflowId, String subscriptionId, boolean activate) {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    appConnectSaveWorkflowResponse.setId(appConnectConfig.getWorkflowId());
    return appConnectSaveWorkflowResponse;
  }

  @Override
  public AppConnectSaveWorkflowResponse disableAppConnectWorkflow(
      String workflowId, AuthDetails authDetails) {
    AppConnectSaveWorkflowResponse appConnectSaveWorkflowResponse =
        new AppConnectSaveWorkflowResponse();
    appConnectSaveWorkflowResponse.setId(appConnectConfig.getWorkflowId());
    return appConnectSaveWorkflowResponse;
  }

  @Override
  public void updateWorkflow(
      @NonNull String workflowId,
      @NonNull String subscriptionId,
      @NonNull String definitionId,
      @NonNull BpmnModelInstance bpmnModelInstance,
      String definitionName) {
    // Dummy comments added for sonar critical issue check
  }

  @Override
  public void deleteWorkflow(@NonNull String workflowId, @NonNull String subscriptionId) {
    // Dummy comments added for sonar critical issue check
  }

  @Override
  public void deleteWorkflow(
      @NonNull String workflowId, @NonNull String subscriptionId, AuthDetails authDetails) {
    // Dummy comments added for sonar critical issue check
  }

  @Override
  public AppConnectUnsubscribeResponse unsubscribe(
      AuthDetails authDetails, boolean useOfflineTicket) {
    AppConnectUnsubscribeResponse appConnectUnsubscribeResponse =
        new AppConnectUnsubscribeResponse();
    appConnectUnsubscribeResponse.setStatusMessage("unsubscribed");
    appConnectUnsubscribeResponse.setSuccess("true");
    return appConnectUnsubscribeResponse;
  }

  @Override
  public List<AppConnectWorkflowResponse> getAppConnectWorkflows(
      String realmId, AuthDetails authDetails) {
    AppConnectWorkflowResponse.Subscription subscription =
        new AppConnectWorkflowResponse.Subscription();
    subscription.setAppConnectAppId("1234");
    subscription.setCompanyId("123");
    subscription.setState("activated");

    AppConnectWorkflowResponse.Status status = new AppConnectWorkflowResponse.Status();
    status.setStatus("active");

    return Arrays.asList(
        AppConnectWorkflowResponse.builder()
            .id("123")
            .subscription(subscription)
            .status(status)
            .workflowType("simple")
            .build());
  }

  @Override
  public void deleteWorkflows(List<String> workflowIds, @NonNull AuthDetails authDetails) {
    // Dummy comments added for sonar critical issue check
  }

  @Override
  public void registerToken(String realmId, String entityType, String entityOperations){
    WorkflowVerfiy.verifyNullForList(
        WorkflowError.INVALID_INPUT,
        entityOperations, entityType
    );
  }
}
