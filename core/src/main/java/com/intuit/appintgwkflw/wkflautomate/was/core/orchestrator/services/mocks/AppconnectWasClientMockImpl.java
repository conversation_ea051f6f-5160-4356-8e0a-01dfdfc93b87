package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import static java.lang.Boolean.TRUE;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectFetchTransactionsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.Random;
import org.springframework.http.HttpStatus;

public class AppconnectWasClientMockImpl extends AppConnectWASClient {

  private AppConnectCustomReminderTransactionsResponseMockImpl transactionsResponseMock =
      new AppConnectCustomReminderTransactionsResponseMockImpl();
  private Random r = new Random();

  @Override
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {

    // mocking time taken by app connect
    try {
      // sleeps between 500-600 ms
      Thread.sleep(r.nextInt(100) + 500);
    } catch (InterruptedException e) {
    }
    if (wasHttpRequest.getUrl().contains("custom-reminder-start-process")) {
      WASHttpResponse<RESPONSE> response =
          (WASHttpResponse<RESPONSE>)
              WASHttpResponse.<AppConnectFetchTransactionsResponse>builder()
                  .isSuccess2xx(true)
                  .response(transactionsResponseMock.fetchTransactionsResponse())
                  .status(HttpStatus.OK)
                  .build();
      return response;
    }

    WorkflowTaskHandlerResponse re = new WorkflowTaskHandlerResponse();
    re.setSuccess(TRUE.toString());

    WASHttpResponse<RESPONSE> response =
        (WASHttpResponse<RESPONSE>)
            WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
                .isSuccess2xx(true)
                .response(re)
                .status(HttpStatus.OK)
                .build();

    return response;
  }
}
