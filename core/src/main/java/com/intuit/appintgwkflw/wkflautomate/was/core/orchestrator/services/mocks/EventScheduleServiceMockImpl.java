package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.ArrayList;
import java.util.List;

/** <AUTHOR> This is the mocking class for eventScheduleService */
public class EventScheduleServiceMockImpl implements EventScheduleService {

  @Override
  public List<EventScheduleResponse> createSchedules(
      List<EventSchedule> eventSchedules, String realmId) {
    List<EventScheduleResponse> responses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    // start
    EventScheduleData startId = new EventScheduleData();
    startId.setId("perf" + System.currentTimeMillis());
    startId.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse.setData(List.of(startId));
    responses.add(eventScheduleResponse);
    // wait
    EventScheduleData waitId = new EventScheduleData();
    waitId.setId("perf" + System.currentTimeMillis());
    waitId.setStatus(ScheduleStatus.ACTIVE);
    eventScheduleResponse = new EventScheduleResponse();
    eventScheduleResponse.setData(List.of(waitId));
    responses.add(eventScheduleResponse);

    return responses;
  }

  @Override
  public List<EventScheduleResponse> updateSchedules(
      List<EventSchedule> eventSchedules, String realmId) {
    // not required as of now
    return null;
  }
}
