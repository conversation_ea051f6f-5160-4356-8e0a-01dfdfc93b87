package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.io.IOUtils;

/**
 * This is the utility class for mocking appconnect calls
 *
 * <AUTHOR>
 */
public class MockHelper {
  public static String readResourceAsString(String path) {
    try (InputStream stream = Resources.getResource(path).openStream()) {
      return IOUtils.toString(stream, Charsets.UTF_8);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
