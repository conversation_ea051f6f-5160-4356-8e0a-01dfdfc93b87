package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.ServiceTaskCompleteRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.Map;

/**
 * Service Task Completion Handler.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ServiceTaskCompletionHandler implements TaskCompletionHandler{
    private CamundaRunTimeServiceRest camundaRest;

    /**
     * Mark service task to completed
     * @param event
     * @param headers
     */
    @Override
    public void completeTask(ExternalTaskCompleted event, Map<String, String> headers) {
        camundaRest.messageServiceTask(ServiceTaskCompleteRequest.builder()
                .executionId(headers.get(EventHeaderConstants.ENTITY_ID))
                .failed(false)
                .payload(event.getVariables())
                .build());
    }

    /**
     * Mark service task as failure
     * @param event
     * @param headers
     */
    @Override
    public void invokeFailure(ExternalTaskCompleted event, Map<String, String> headers) {
        camundaRest.messageServiceTask(ServiceTaskCompleteRequest.builder()
                .executionId(headers.get(EventHeaderConstants.ENTITY_ID))
                .failed(true)
                .failureMessage(event.getErrorMessage())
                .build());
    }

    /**
     * Mark service task as failure
     * @param headers
     * @param e Exception
     */
    @Override
    public void handleFailure(Map<String, String> headers, Exception e) {
        camundaRest.messageServiceTask(ServiceTaskCompleteRequest.builder()
                .executionId(headers.get(EventHeaderConstants.ENTITY_ID))
                .failed(true)
                .failureMessage(String.format("Exception while trying to close task. Creating incident. message=%s", e.getMessage()))
                .build());
    }

    @Override
    public void extendLock(ExternalTaskCompleted event, Map<String, String> headers) {
        throw new UnsupportedOperationException("Operation not supported");
    }

    @Override
    public void updateStatus(ExternalTaskCompleted event, Map<String, String> headers) {
       //TODO: Change in follow up PR - Add handing same as ExternalTaskCompleteionHandler
    }

    @Override
    public EventEntityType getName() {
        return EventEntityType.SERVICE_TASK;
    }

}
