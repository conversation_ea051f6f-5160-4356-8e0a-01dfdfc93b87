package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;

import java.util.Map;

/**
 * Task Completion Handler Interface.
 *
 * <AUTHOR>
 */
public interface TaskCompletionHandler {

    void completeTask(ExternalTaskCompleted event, Map<String, String> headers);

    void invokeFailure(ExternalTaskCompleted event, Map<String, String> headers);
    
    void updateStatus(ExternalTaskCompleted event, Map<String, String> headers);

    void handleFailure(Map<String, String> headers, Exception e);

    void extendLock(ExternalTaskCompleted event, Map<String, String> headers);

    default void invokeFailureWithRetry(ExternalTaskCompleted event, Map<String, String> headers) {
        WorkflowLogger.logWarn("Invoking default TaskCompletionHandler.invokeFailureWithRetry");
    }
    EventEntityType getName();
}
