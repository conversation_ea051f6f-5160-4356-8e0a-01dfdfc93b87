package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.EnumMap;
import java.util.Map;

/**
 * The type TaskCompletionHandler.
 *
 * <AUTHOR>
 *     <p>Class acts as factory to return task completion handler
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskCompletionHandlers {

  private static final Map<EventEntityType, TaskCompletionHandler> TASK_COMPLETION_HANDLER_MAP =
      new EnumMap<>(EventEntityType.class);

  /**
   * Adds a handler.
   *
   * @param eventEntity the entity type for the event
   * @param taskCompletionHandler the task completion handler
   */
  public static void addHandler(EventEntityType eventEntity, TaskCompletionHandler taskCompletionHandler) {
    TASK_COMPLETION_HANDLER_MAP.put(eventEntity, taskCompletionHandler);
  }

  /**
   * Gets handler
   *
   * @param eventEntity input handler name
   * @return action handler impl
   */
  public static TaskCompletionHandler getHandler(EventEntityType eventEntity) {
    return TASK_COMPLETION_HANDLER_MAP.get(eventEntity);
  }

  /**
   * Contains boolean.
   *
   * @param eventEntity the entity type name
   * @return true /false if handler is present or not
   */
  public static boolean contains(EventEntityType eventEntity) {

    return TASK_COMPLETION_HANDLER_MAP.containsKey(eventEntity);
  }
}
