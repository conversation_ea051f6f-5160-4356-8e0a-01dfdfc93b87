package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.ACTIVATE_ACTION_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.SUBSCRIPTION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.WORKFLOW_ID_KEY;
/** This task is for activating the workflow in app-connect */
@AllArgsConstructor
public class ActivateDeActivateAppConnectWorkflowTask implements Task {
  private AuthDetailsService authDetailsService;
  private AppConnectService appConnectService;

  @Override
  public State execute(State inputRequest) {
    String definitionId = inputRequest.getValue(DEFINITION_ID_KEY);
    String workflowId = inputRequest.getValue(WORKFLOW_ID_KEY);
    String subscriptionId = inputRequest.getValue(SUBSCRIPTION_ID_KEY);
    // if activateAction is null, assume activate by default
    final Boolean activateAction =
        BooleanUtils.toBooleanDefaultIfNull(inputRequest.getValue(ACTIVATE_ACTION_KEY), true);
    try {
      WorkflowVerfiy.verify(
          StringUtils.isEmpty(definitionId) || StringUtils.isEmpty(workflowId),
          WorkflowError.INVALID_INPUT);
      if (StringUtils.isEmpty(subscriptionId)) {
        // if subscriptionId is not present in the request, get it from the database by realmId
        String realmId = inputRequest.getValue(REALM_ID_KEY);
        WorkflowVerfiy.verify(StringUtils.isEmpty(realmId), WorkflowError.INVALID_INPUT);
        subscriptionId = authDetailsService.getAuthDetailsFromRealmId(realmId).getSubscriptionId();
      }
      // activate/deactivate the workflow
      inputRequest.addValue(
          AsyncTaskConstants.ACTIVATE_RESPONSE_KEY,
          appConnectService.activateDeactivateActionWorkflow(
              workflowId, subscriptionId, activateAction));
    } catch (WorkflowGeneralException workflowGeneralException) {
      // if workflow is already inactive don't throw the exception.Can be because of retry we are
      // trying again to deactivate the definition
      if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
          && (workflowGeneralException.getMessage().contains(WorkflowConstants.WORKFLOW_ALREADY_INACTIVE)
              || workflowGeneralException.getMessage().contains(WorkflowConstants.WORKFLOW_ALREADY_ACTIVE))) {
        WorkflowLogger.warn(
            () ->
                WorkflowLoggerRequest.builder()
                    .className(this.getClass().getName())
                    .message(
                        activateAction.booleanValue()
                            ? "Workflow already active for given workflowId=%s"
                            : "Workflow already inactive for given workflowId=%s",
                        workflowId)
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
      } else {
        throw workflowGeneralException;
      }
    }
    return inputRequest;
  }
}
