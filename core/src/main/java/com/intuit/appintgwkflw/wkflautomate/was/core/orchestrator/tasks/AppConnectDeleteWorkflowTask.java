package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/** Author: Nitin Gupta Date: 30/01/20 Description: */
@AllArgsConstructor
public class AppConnectDeleteWorkflowTask implements Task {

  private AppConnectService appConnectService;

  private AuthDetailsService authDetailsService;

  @Override
  public State execute(State state) {

    String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
    String subscriptionId = state.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY);
    String workflowId = state.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY);

    WorkflowVerfiy.verify(StringUtils.isEmpty(workflowId), WorkflowError.INVALID_WORKFLOW_ID_INPUT);

    if (StringUtils.isEmpty(subscriptionId)) {
      // if subscriptionId is not present in the request, get it from the database by realmId
      WorkflowVerfiy.verify(StringUtils.isEmpty(realmId), WorkflowError.INVALID_INPUT);
      subscriptionId = authDetailsService.getAuthDetailsFromRealmId(realmId).getSubscriptionId();
      WorkflowVerfiy.verify(
          StringUtils.isEmpty(subscriptionId),
          WorkflowError.INTERNAL_EXCEPTION,
          "Subscription id not found in database by realmId=%s",
          realmId);
    }

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Deleting workflow from app-connect by workflowId=%s", workflowId)
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                .className(this.getClass().getName()));
    try {
      appConnectService.deleteWorkflow(workflowId, subscriptionId);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Workflow deleted from app-connect by workflowId=%s", workflowId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      // if workflow is not found don't throw the exception.Can be because of retry we are
      // trying again to delete the workflow
      if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
          && workflowGeneralException.getMessage().contains(WorkflowConstants.RESOURCE_NOT_FOUND)) {
        WorkflowLogger.warn(
            () ->
                WorkflowLoggerRequest.builder()
                    .className(this.getClass().getName())
                    .message(
                        "Workflow not found for given workflowId=%s, realmId=%s",
                        workflowId, realmId)
                    .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                    .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT));
      } else {
        throw workflowGeneralException;
      }
    }

    return state;
  }
}
