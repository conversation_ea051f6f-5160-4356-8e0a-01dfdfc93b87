package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/** Delete Workflow(s) in AppConnect and in case no workflow, no exception is thrown */
@AllArgsConstructor
public class AppConnectDeleteWorkflowsTask implements Task {

  private AppConnectService appConnectService;

  private String workflowId;

  @Override
  public State execute(State inputRequest) {

    String subscriptionId = inputRequest.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY);
    String realmId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);
    AuthDetails authDetails = inputRequest.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY);
    try {
      appConnectService.deleteWorkflow(workflowId, subscriptionId, authDetails);
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Workflow deleted from app-connect by workflowId=%s", workflowId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
    } catch (WorkflowGeneralException workflowGeneralException) {
      // if workflow is not found don't throw the exception.Can be because of retry we are
      // trying again to delete the workflow
      if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
          && workflowGeneralException.getMessage().contains(WorkflowConstants.RESOURCE_NOT_FOUND)) {
        WorkflowLogger.warn(
            () ->
                WorkflowLoggerRequest.builder()
                    .className(this.getClass().getName())
                    .message(
                        "Workflow not found for given workflowId=%s, realmId=%s",
                        workflowId, realmId)
                    .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                    .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT));
      } else {
        throw workflowGeneralException;
      }
    }
    return inputRequest;
  }
}
