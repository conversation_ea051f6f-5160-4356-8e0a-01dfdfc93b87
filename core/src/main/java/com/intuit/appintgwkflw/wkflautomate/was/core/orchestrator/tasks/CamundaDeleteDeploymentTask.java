package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DELETE_DEFINITION_FAILED;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> Task to delete deployment in Camunda based on process Definition Id.
 * Fetches DeploymentId from Camunda and delete Deployment.
 */
@AllArgsConstructor
public class CamundaDeleteDeploymentTask implements Task {

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private String definitionId;
  private Boolean cascade;
  private Boolean skipListener;

  @Override
  public State execute(State inputRequest) {
    WorkflowVerfiy.verify(
        definitionId == null || cascade == null || skipListener == null,
        WorkflowError.INVALID_INPUT);
    try {
      WASHttpResponse<DeploymentResponse> deploymentResponse = bpmnEngineDefinitionServiceRest.getDeploymentDetails(
          definitionId);

      WorkflowVerfiy.verify(
          !deploymentResponse.isSuccess2xx(), DELETE_DEFINITION_FAILED,
          deploymentResponse.getError());

      String deploymentId = deploymentResponse.getResponse().getDeploymentId();
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Deleting deployment from camunda for definitionId=%s deploymentId=%s with cascade=%s and skipListener=%s",
                      definitionId, deploymentId, cascade, skipListener)
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION));

      DeleteDeploymentRequest deleteDeploymentRequest =
          new DeleteDeploymentRequest(deploymentId, true, true);

      bpmnEngineDefinitionServiceRest.deleteDeployment(deleteDeploymentRequest);
    } catch (WorkflowGeneralException workflowGeneralException) {
      // Do not try if the deployment is already deleted. If not deleted, throw exception
      if (workflowGeneralException.getMessage()
          .contains(String.format(WorkflowConstants.DEPLOYMENT_NOT_FOUND, definitionId))) {
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "Details not found in camunda for given definitionId=%s", definitionId)
                    .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                    .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION));

      } else {
        throw workflowGeneralException;
      }
    }
    return inputRequest;
  }
}
