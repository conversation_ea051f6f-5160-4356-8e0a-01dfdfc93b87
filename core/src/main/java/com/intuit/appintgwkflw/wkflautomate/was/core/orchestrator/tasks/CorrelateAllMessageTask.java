package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;


/**
 * <AUTHOR>
 *     <p>Task to correlate Delete/Cleanup message to all the running process
 */
@AllArgsConstructor
public class CorrelateAllMessageTask implements Task {

  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  private final String messageName;

  @Override
  public State execute(State inputRequest) {

    final String realmId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);

    try {
      WorkflowVerfiy.verify(realmId, WorkflowError.INPUT_INVALID, AsyncTaskConstants.REALM_ID_KEY);
      if (BooleanUtils.isTrue(inputRequest.getValue(AsyncTaskConstants.IS_CORRELATE_ASYNC))) {
        bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(
            new CorrelateAllMessageAsync(
                messageName, inputRequest.getValue(AsyncTaskConstants.PROCESS_INSTANCE_QUERY)));
      } else {
        bpmnEngineRunTimeServiceRest.correlateAllMessage(
            new CorrelateAllMessage(
                messageName, realmId, inputRequest.getValue(AsyncTaskConstants.CORRELATE_KEYS)));
      }

      logInfo(
          "Message Successfully Correlated to all running processes for RealmId=%s and MessageName=%s",
          realmId, messageName);

    } catch (WorkflowGeneralException workflowGeneralException) {
      logError(
          workflowGeneralException,
          "Error occurred while correlating message for RealmId=%s and MessageName=%s",
          realmId,
          messageName);
      inputRequest.addValue(AsyncTaskConstants.CORRELATE_ALL_EXCEPTION, workflowGeneralException);
    }

    return inputRequest;
  }

  /**
   * @param message : Success message
   * @param workflowMessageArgs : workflowMessageArgs varArgs
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_SIGNAL_PROCESS));
  }

  /**
   * @param error : Error of type Throwable
   * @param message : Error message
   * @param workflowMessageArgs : workflowMessageArgs varArgs
   */
  private void logError(
      final Throwable error, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .className(this.getClass().getSimpleName())
                .stackTrace(error)
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_SIGNAL_PROCESS));
  }
}
