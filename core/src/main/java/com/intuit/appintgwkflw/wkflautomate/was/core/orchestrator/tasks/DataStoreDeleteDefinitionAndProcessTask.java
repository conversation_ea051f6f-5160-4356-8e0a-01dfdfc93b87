package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Arrays;
import lombok.AllArgsConstructor;

/** Author: Nitin Gupta Date: 30/01/20 Description: */
@AllArgsConstructor
public class DataStoreDeleteDefinitionAndProcessTask implements Task {

  private DefinitionDetailsRepository definitionDetailsRepository;
  private String definitionId;
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  @Override
  public State execute(State state) {
    WorkflowVerfiy.verifyNull(definitionId, WorkflowError.INVALID_INPUT);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("deleting old entries from database")
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                .className(this.getClass().getName()));

    dataStoreDeleteTaskService
    	.deleteDefinitions(Arrays.asList(DefinitionDetails.builder().definitionId(definitionId).build()));

    // Delete old entries from database
    long definitionDetailsEntriesDeletedCount =
        definitionDetailsRepository.deleteByDefinitionIdOrParentId(definitionId, definitionId);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Definition deleted, totalcount=%s", definitionDetailsEntriesDeletedCount)
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                .className(this.getClass().getName()));

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Delete entries from Database completed")
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                .className(this.getClass().getName()));

    return state;
  }
}
