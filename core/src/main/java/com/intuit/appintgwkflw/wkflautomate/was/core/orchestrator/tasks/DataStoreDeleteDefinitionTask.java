package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.ListUtils;

import java.util.Collections;
import java.util.List;

@AllArgsConstructor
public class DataStoreDeleteDefinitionTask implements Task {

  private DefinitionDetailsRepository definitionDetailsRepository;
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private EnabledDefinitionCacheService cacheService;

  @Override
  public State execute(State inputRequest) {
    State state = new State();

    String definitionId = inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
    WorkflowVerfiy.verifyNull(definitionId, WorkflowError.INVALID_INPUT);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("deleting old entries from database")
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION)
                .className(this.getClass().getName()));

    /**
     * All the DMN mappings in the definition activity details will also get deleted
     * for any future purpose.
     **/
    List<DefinitionDetails> childDefinitionDetails =
            definitionDetailsRepository.findByParentId(definitionId)
            .orElse(Collections.emptyList());

    /** Delete all the definition activity details **/
    definitionActivityDetailsRepository.deleteByDefinitionDetailsIn(
            ListUtils.union(
                    Collections.singletonList(DefinitionDetails.builder().definitionId(definitionId).build()),
                    childDefinitionDetails
            )
    );

    // Delete old entries from database
    long definitionDetailsEntriesDeletedCount =
            definitionDetailsRepository.deleteByDefinitionIdOrParentId(definitionId, definitionId);

    // Re-run populate cache so that definition key added to cache is removed too
    DefinitionInstance definitionInstance = inputRequest.getValue(AsyncTaskConstants.DEFINITION_INSTANCE);
    cacheService.updateCacheWithDefinitionDetails(definitionInstance.getDefinitionDetails());

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Definition deleted, totalCount=%s. Delete entries from Database completed",
                    definitionDetailsEntriesDeletedCount)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION)
                .className(this.getClass().getName()));

    return state;
  }
}
