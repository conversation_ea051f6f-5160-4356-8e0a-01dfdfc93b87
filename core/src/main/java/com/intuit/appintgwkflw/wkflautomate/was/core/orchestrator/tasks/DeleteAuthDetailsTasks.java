package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;

import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *     <p>Deletes auth details for a given company
 */
@AllArgsConstructor
public class DeleteAuthDetailsTasks implements Task {

  private AuthDetailsRepository authDetailsRepository;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private ProcessDetailsRepository processDetailsRepository;

  @Override
  public boolean isFatal() {
    return false;
  }

  @Override
  public State execute(State inputRequest) {
    String ownerId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);

    WorkflowVerfiy.verify(StringUtils.isEmpty(ownerId), WorkflowError.INVALID_INPUT);
    logInfo("Initiating Auth details removal for given OwnerId=%s", ownerId);

    long definitionCount = definitionDetailsRepository.countByOwnerId(Long.parseLong(ownerId));

    long count = 0;

    // if all definition are removed then delete the auth details. The Downgrade process entry will
    // remain in the process details table and hence we are not checking process count.
    if (definitionCount == 0) {
      count = authDetailsRepository.deleteByOwnerId(Long.parseLong(ownerId));
    }

    if (count == 0) {
      logInfo("No Auth details removed for given OwnerId=%s", ownerId);
      return inputRequest;
    }

    logInfo("Auth details removed successfully for given OwnerId=%s", ownerId);

    return inputRequest;
  }

  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DELETE_AUTH_DETAILS));
  }
}
