package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * Task to delete event schedules in the scheduling service.
 * <AUTHOR>
 */
@AllArgsConstructor
public class DeleteEventSchedulingTask implements Task {
    private final SchedulingService schedulingService;

    /**
     * Executes the task to delete event schedules in the scheduling service.
     *
     * @param state The current state of the task execution.
     * @return The updated state after execution.
     */
    @Override
    public State execute(State state) {
        String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
        if (ObjectUtils.isEmpty(realmId)) {
            WorkflowLogger.logError(
                    "Failed to invoke scheduling service to delete schedules as realmId not set");
            return state;
        }
        String definitionKey = state.getValue(AsyncTaskConstants.DEFINITION_KEY);
        String workflowName = state.getValue(AsyncTaskConstants.WORKFLOW_NAME_KEY);
        if (ObjectUtils.isEmpty(definitionKey) || ObjectUtils.isEmpty(workflowName)) {
            WorkflowLogger.logInfo("failed to delete the schedules as definitionKey or workflowName is not set");
            return state;
        }
        List<String> scheduleIds = SchedulingServiceUtil.getScheduleIds(definitionKey, workflowName);
        try {
            //make call to scheduling service
            List<SchedulingSvcResponse> eventScheduleResponses =
                    schedulingService.deleteSchedules(scheduleIds, realmId);
            WorkflowVerfiy.verify(
                    CollectionUtils.isEmpty(eventScheduleResponses),
                    WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                    "No schedules deleted");
            WorkflowVerfiy.verify(
                    eventScheduleResponses.size() != scheduleIds.size(),
                    WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                    "Failed to delete all schedules");
        }catch (Exception e){
            WorkflowLogger.logError(
                    "Failed to delete workflow from scheduling service for definitionKey=%s, scheduleIds=%s", definitionKey, scheduleIds);
        }
        return state;
    }
}