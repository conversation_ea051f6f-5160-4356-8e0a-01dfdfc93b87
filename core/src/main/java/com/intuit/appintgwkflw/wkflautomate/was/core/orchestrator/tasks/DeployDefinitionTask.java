package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import java.io.File;
import java.nio.file.Files;
import java.util.Optional;

/** Task for deploying definition(s) in Camunda */
@AllArgsConstructor
public class DeployDefinitionTask implements Task {

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @Override
  public State execute(State inputRequest) {
    State state = new State();
    try {
      WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Deploying to camunda"));
      DeployDefinition deployDefinition =
          inputRequest.getValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY);
      WASHttpResponse<DeployDefinitionResponse> deployDefinitionResponseEntity =
          bpmnEngineDefinitionServiceRest.deployDefinition(deployDefinition);

      DeployDefinitionResponse deployDefinitionResponse =
          deployDefinitionResponseEntity.getResponse();
      state.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY, deployDefinitionResponse);
      Optional<DeployDefinitionResponse.DeployedDefinition> deployedDefinition =
          deployDefinitionResponse.getDeployedProcessDefinitions().values().stream().findFirst();

      WorkflowVerfiy.verify(
          !deployedDefinition.isPresent(), WorkflowError.CAMUNDA_DEPLOYMENT_FAILED);

      String definitionId = deployedDefinition.get().getId();
      // set the definitionId for the next tasks to execute
      state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionId);
      state.addValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY, deployDefinitionResponse.getId());

      WorkflowLogger.info(
          () -> WorkflowLoggerRequest.builder().message("Deployment done to camunda"));
      deleteTemporaryFiles(deployDefinition);
    } catch (Exception e) {
      throw new WorkflowGeneralException(WorkflowError.CAMUNDA_DEPLOYMENT_FAILED, e);
    }
    return state;
  }

  private void deleteTemporaryFiles(DeployDefinition deployDefinition) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Deleting temporary bpmn and dmn files from the file system"));
    deleteFile(deployDefinition.getBpmnDefinitionFile());
    Optional.ofNullable(deployDefinition.getDmnDefinitionFileList())
        .ifPresent(dmnFilesList -> dmnFilesList.forEach(this::deleteFile));
  }

  private void deleteFile(File file) {
    try {
      Files.deleteIfExists(file.toPath());
    } catch (Exception e) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Failed to delete bpmn and dmn files from file system")
                  .stackTrace(e)
                  .downstreamServiceName(DownstreamServiceName.WAS_DELETE_FILE)
                  .downstreamComponentName(DownstreamComponentName.WAS));
    }
  }
}
