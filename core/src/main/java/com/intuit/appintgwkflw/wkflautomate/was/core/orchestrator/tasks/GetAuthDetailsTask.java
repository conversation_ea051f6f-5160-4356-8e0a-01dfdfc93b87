package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;

import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;

/**
 * This class gets the {@link AuthDetails} from database.
 */
@AllArgsConstructor
public class GetAuthDetailsTask implements Task {
  private AuthDetailsRepository authDetailsRepository;
  private Authorization authorization;
  private final AuthHelper authHelper;

  @Override
  public State execute(State inputRequest) {
    String realm = authorization.getRealm();
    WorkflowVerfiy.verifyNull(realm, WorkflowError.INVALID_REALM_ID);

    final Optional<AuthDetails> authDetails =
        authHelper.getAuthDetailsFromList(authDetailsRepository.findAuthDetailsByOwnerId(Long.parseLong(realm)));
    
    WorkflowVerfiy.verify(!authDetails.isPresent(), WorkflowError.AUTH_DETAILS_NOT_FOUND);
    inputRequest.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetails.get());
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realm);
    return inputRequest;
  }
}
