package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EXISTING_SUBSCRIPTION_FOUND_APPCONNECT_ERROR_CODE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

import java.util.Optional;

import lombok.AllArgsConstructor;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

/** Get the subscriptionId from appconnect. If subscription is not present, create subscription
 * in appconnect. */
@AllArgsConstructor
public class GetSubscriptionDetailsTask implements Task {

  private AppConnectService appConnectService;
  private AuthDetailsRepository authDetailsRepository;
  private final AuthHelper authHelper;

  /**
   * 1. Fetch the subscription id from Appconnect. <br>
   * 2. Check if WAS is having stale subscription id if yes log it and save it in WAS else do
   * nothing.
   */
  @Override
  public State execute(State inputRequest) {
    State state = new State();
    String ownerId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);
    boolean isCreateSubscriptionReqd = BooleanUtils.isTrue(inputRequest.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));

    try {
      WorkflowVerfiy.verify(StringUtils.isEmpty(ownerId), WorkflowError.INVALID_INPUT);
      // check if the data is already there
      Optional<AuthDetails> authDetailsOptional =
          authHelper.getAuthDetailsFromList(authDetailsRepository.findAuthDetailsByOwnerId(Long.parseLong(ownerId)));

      logInfo("Getting subscription details");

      String subscriptionId = getSubscriptionIdFromAppconnect(ownerId, isCreateSubscriptionReqd);

      if (authDetailsOptional.isPresent()) {
        logInfo("Auth details already present");
        state.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetailsOptional.get());

        if (StringUtils.isNotEmpty(subscriptionId)
            && !authDetailsOptional.get().getSubscriptionId().equals(subscriptionId)) {
          logInfo(
              "Subscription ID mismatched.Overwriting subscriptionId from=%s to=%s",
              authDetailsOptional.get().getSubscriptionId(), subscriptionId);
        } else {
          subscriptionId = authDetailsOptional.get().getSubscriptionId();
        }
      }
      state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);

    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .message("Failure=GetSubscription for ownerId=%s", ownerId)
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.GET_SUBSCRIPTION_DETAILS));
    }
    return state;
  }

  private void logInfo(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(message, workflowMessageArgs)
                .className(this.getClass().getSimpleName())
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.GET_SUBSCRIPTION_DETAILS));
  }

  private String getSubscriptionIdFromAppconnect(String ownerId, Boolean isCreateSubscriptionReqd) {
    String subscriptionId = appConnectService.getSubscriptionForApp(ownerId);
    if (StringUtils.isEmpty(subscriptionId) && isCreateSubscriptionReqd) {
      try {
        subscriptionId = appConnectService.createSubscriptionForApp(ownerId);
      } catch (WorkflowGeneralException exception) {
        //In case, if we get error that subscription already exists while creating subscription, then we ignore that error
        //For more details: https://jira.intuit.com/browse/QBOES-21949
        if (exception.getLocalizedMessage()
            .contains(EXISTING_SUBSCRIPTION_FOUND_APPCONNECT_ERROR_CODE)) {
          subscriptionId = appConnectService.getSubscriptionForApp(ownerId);
          WorkflowLogger.logInfo("subscription id after retry=%s for ownerId=%s",
              subscriptionId, ownerId);
        } else {
          throw exception;
        }
      }

    }
    WorkflowVerfiy.verifyNull(subscriptionId, WorkflowError.GET_SUBSCRIPTION_FAIL);
    return subscriptionId;
  }
}
