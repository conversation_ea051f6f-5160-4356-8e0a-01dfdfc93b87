package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * This class is used to store the entries in definition table and activity table for a multi-step
 * definition This class is being made a spring bean in order to use Transactional annotation on
 * this class (as this annotation does not work for non-spring bean classes) and to make all the
 * entries in both the tables, i.e., activity details table and definition details table as
 * transactional.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class MultiStepSaveDefinitionDataStoreTask implements Task {

  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  /**
   * This function handles putting all the entries in the definition details and activity details
   * table
   *
   * @param inputRequest
   * @return
   */
  @Transactional
  @Override
  public State execute(State inputRequest) {
    State state = new State();
    // if definition id is present(created while calling Appconnect), use the same definition id
    if(ObjectUtils.isEmpty(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY))){
      inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, UUID.randomUUID().toString());
    }

    DeployDefinitionResponse deployDefinitionResponse =
        validateAndCreateDeployResponse(inputRequest);

    DefinitionInstance definitionInstance =
        inputRequest.getValue(AsyncTaskConstants.DEFINITION_INSTANCE);

    // set the providerWorkflowId if applicable
    definitionInstance.setWorkflowId(inputRequest.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));

    //save dmn and bpmn definition details into database
    Definition definition = saveDefinitionDetails(inputRequest, deployDefinitionResponse, definitionInstance);
    state.addValue(
        AsyncTaskConstants.SAVE_DEFINITION_RESPONSE_KEY,
        definition);

    //save activity details into database
    state.addValue(
        AsyncTaskConstants.SAVE_DEFINITION_ACTIVITY_RESPONSE_KEY,
        saveActivityDetails(deployDefinitionResponse, definitionInstance.getActivityInstanceMap()));

    // save definition id key
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY,
       inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    state.addValue(AsyncTaskConstants.DEFINITION_KEY, definition.getDefinitionKey());

    WorkflowLogger.logInfo("step=saveDefinitionDetailsAndActivityDetails status=successful");
    return state;
  }

  /**
   * This function is used to create deployed definition map(map of definition id to an object of
   * activityId, definition key and definiton id) for dmn and bpmn for saving them in definition
   * details table
   *
   * @param inputRequest
   * @return
   */
  private DeployDefinitionResponse validateAndCreateDeployResponse(final State inputRequest) {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();

    DefinitionInstance definitionInstance = inputRequest.getValue(
        AsyncTaskConstants.DEFINITION_INSTANCE);
    Authorization authorization = inputRequest.getValue(AsyncTaskConstants.AUTHORIZATION_KEY);

    //During update definition, this definition key will be fetched from the previously saved definition in db
    String bpmnDefinitionKey = ObjectUtils.isNotEmpty(definitionInstance.getDefinitionDetails())
        && StringUtils.isNotEmpty(definitionInstance.getDefinitionDetails().getDefinitionKey()) ?
        definitionInstance.getDefinitionDetails().getDefinitionKey() :
        String.format("%s_%s_%s", definitionInstance.getTemplateDetails().getTemplateName(),
            authorization.getRealm(), inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    //create bpmn deployed definition map
    final Map<String, DeployDefinitionResponse.DeployedDefinition> bpmnDeployedDefinitionMap =
        createDeployedDefinition(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY),
            bpmnDefinitionKey, null);
    deployDefinitionResponse.setDeployedProcessDefinitions(bpmnDeployedDefinitionMap);
    deployDefinitionResponse.setId(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));

    final Map<String, ActivityInstance> activityInstanceMap =
        definitionInstance.getActivityInstanceMap();

    //create dmn deployed definition map
    Map<String, DeployDefinitionResponse.DeployedDefinition> dmnDeployedDefinitionMap =
        new HashMap<>();
    activityInstanceMap.keySet().stream()
        .filter(key -> MultiStepUtil.isDmnTypeActivityInstance(activityInstanceMap.get(key)))
        .forEach(
            activityId -> {
              String dmnDefinitionId = UUID.randomUUID().toString();
              // ex. dmnDefinitionKey = decisionElement_9130357082896236_aafd46af-a148-4946-82b0-12f38842f351
              String dmnDefinitionKey = bpmnDefinitionKey.replaceAll(
                  definitionInstance.getTemplateDetails().getTemplateName(), activityId);
              dmnDeployedDefinitionMap.putAll(
                  createDeployedDefinition(dmnDefinitionId, dmnDefinitionKey, activityId));
            });
    deployDefinitionResponse.setDeployedDecisionDefinitions(dmnDeployedDefinitionMap);

    return deployDefinitionResponse;
  }

  /**
   * This function is used to create deployed definition map(map of definition id to an object of
   * activityId, definition key and definiton id) for any bpmn/dmn definition
   *
   * @param id
   * @param definitionKey
   * @param activityId
   */
  private Map<String, DeployDefinitionResponse.DeployedDefinition> createDeployedDefinition(
      String id, String definitionKey, String activityId) {
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap = new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId(id);
    deployedDefinition.setKey(definitionKey);
    deployedDefinition.setActivityId(activityId);
    deployedDefinitionMap.put(id, deployedDefinition);
    return deployedDefinitionMap;
  }

  /**
   * This function is used to save dmn and bpmn definition details in definition details table
   *
   * @param inputRequest
   * @param deployDefinitionResponse
   * @param definitionInstance
   * @return
   */
  private Definition saveDefinitionDetails(State inputRequest,
      DeployDefinitionResponse deployDefinitionResponse,
      DefinitionInstance definitionInstance) {
    DefinitionServiceHelper definitionServiceHelper =
        inputRequest.getValue(AsyncTaskConstants.DEFINITION_SERVICE_HELPER_OBJECT);
    boolean isUpdate = inputRequest.getValue(AsyncTaskConstants.IS_UPDATE);
    Authorization authorization = inputRequest.getValue(AsyncTaskConstants.AUTHORIZATION_KEY);

    return definitionServiceHelper.saveUpdateDefinitionDetails(
        deployDefinitionResponse, definitionInstance,
        authorization, isUpdate);
  }

  private List<DefinitionActivityDetail> saveActivityDetails(
      DeployDefinitionResponse deployDefinitionResponse,
      Map<String, ActivityInstance> definitionActivityMap) {
    List<DefinitionActivityDetail> definitionActivityDetails = new ArrayList<>();
    try {
      createDmnActivityDetails(deployDefinitionResponse, definitionActivityDetails);
      createActionActivityDetails(deployDefinitionResponse, definitionActivityMap,
          definitionActivityDetails);
      definitionActivityDetailsRepository.saveAll(definitionActivityDetails);
    } catch (Exception e) {
      WorkflowLogger.logError("step=saveDmnActivityDetails error=%s", e);
      throw new WorkflowGeneralException(WorkflowError.MULTI_STEP_DEFINITION_SAVE_EXCEPTION);
    }
    return definitionActivityDetails;
  }

  /**
   * This function is used to save the activity details for dmn in the activity details table of WAS
   * db.
   *
   * @param deployDefinitionResponse
   * @return
   */
  private void createDmnActivityDetails(
      DeployDefinitionResponse deployDefinitionResponse,
      List<DefinitionActivityDetail> definitionActivityDetails) {
    deployDefinitionResponse.getDeployedDecisionDefinitions().values().forEach(
        deployedDefinition -> {
          DefinitionDetails definitionDetails = DefinitionDetails.builder()
              .definitionId(deployedDefinition.getId()).build();
          definitionActivityDetails.add(
              buildDefinitionActivityDetails(deployedDefinition.getActivityId(), null,
                  definitionDetails, null));
        }
    );
  }

  /**
   * This function is used to save call activity details for parent and it's child call activity
   * details in activity details table
   *
   * @param deployDefinitionResponse
   * @param activityInstanceMap
   * @return
   */
  private void createActionActivityDetails(
      DeployDefinitionResponse deployDefinitionResponse,
      Map<String, ActivityInstance> activityInstanceMap,
      List<DefinitionActivityDetail> definitionActivityDetails) {
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId(
        deployDefinitionResponse.getDeployedProcessDefinitions()
            .values().stream().findFirst().get().getId()).build();
    for (Map.Entry<String, ActivityInstance> parentActivity : activityInstanceMap.entrySet()) {
      String activityId = parentActivity.getKey();
      ActivityInstance activityInstance = parentActivity.getValue();
      if (MultiStepUtil.isDmnTypeActivityInstance(activityInstance)) {
        continue;
      }
      DefinitionActivityDetail parentActivityDetail = buildDefinitionActivityDetails(activityId,
          null, definitionDetails, activityInstance.getUserAttributes());

      definitionActivityDetails.add(parentActivityDetail);

      parentActivityDetail.setChildActivityDetails(
          createChildActivityDetails(
              activityInstanceMap.get(activityId).getChildActivityInstances(),
              parentActivityDetail.getId()));
    }
  }

  /**
   * This function is used to create child activity details(ex. create task, send mail notification,
   * send push notification, etc.) for a given parent(ex. action-1)
   *
   * @param childActivityInstanceMap
   * @param parentId
   * @return
   */
  private List<DefinitionActivityDetail> createChildActivityDetails(
      Map<String, ActivityInstance> childActivityInstanceMap, String parentId) {
    List<DefinitionActivityDetail> childDefinitionActivityDetails = new ArrayList<>();
    for (Map.Entry<String, ActivityInstance> childActivity : childActivityInstanceMap.entrySet()) {
      String childActivityId = childActivity.getKey();
      ActivityInstance childActivityInstance = childActivity.getValue();
      childDefinitionActivityDetails.add(buildDefinitionActivityDetails(
          childActivityId, parentId, null,
          childActivityInstance.getUserAttributes()));
    }
    return childDefinitionActivityDetails;
  }

  /**
   * This function is used to build the activity details object using all the parameters passed to
   * it. Parent id will be null in case of parent activity detail(ex. action-1)
   *
   * @param activityId
   * @param parentId
   * @param definitionDetails
   * @param userAttributes
   * @return
   */
  private DefinitionActivityDetail buildDefinitionActivityDetails(String activityId,
      String parentId, DefinitionDetails definitionDetails, Map<String, Object> userAttributes) {
    WorkflowLogger.logInfo(
        "step=buildDefinitionActivityDetails activityId=%s parentId=%s",
        activityId, parentId);
    return DefinitionActivityDetail.builder()
        .id(UUID.randomUUID().toString())
        .activityId(activityId)
        .parentActivityDetail(
            Objects.nonNull(parentId) ?
                DefinitionActivityDetail.builder().id(parentId).build() : null
        )
        .userAttributes(ObjectConverter.toJson((userAttributes)))
        .definitionDetails(definitionDetails)
        .build();
  }

}