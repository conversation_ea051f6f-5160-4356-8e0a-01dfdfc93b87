package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.SUBSCRIPTION_ID_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;

/**
 * This task creates/updates workflow in appconnect. It assumes that
 */
@AllArgsConstructor
public class SaveAndActivateAppConnectWorkflowTask implements Task {

  private final AppConnectService appConnectService;
  private final AuthDetailsService authDetailsService;
  private final BpmnModelInstance bpmnModelInstance;
  private final boolean activate;
  private final boolean isUpdate;
  private final boolean isMigrate;
  private final String workflowId;
  private final String definitionName;

  @Override
  public State execute(State inputRequest) {
    String subscriptionId = inputRequest.getValue(SUBSCRIPTION_ID_KEY);
    if (BooleanUtils.isTrue(inputRequest.getValue(AsyncTaskConstants.IS_SINGLE_DEFINITION)) && ObjectUtils.isEmpty(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY))) {
      // Definition id in normal case is returned by Camunda upto deployment. As in single
      // definition, there is no definition deployment, we will generate the definition if.
      // in update scenario, we are already generating the definition id.
      inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, UUID.randomUUID().toString());
    }
    String definitionId = inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
    String ownerId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);
    String entityType = inputRequest.getValue(WorkflowConstants.ENTITY_TYPE);
    String entityOperation = inputRequest.getValue(AsyncTaskConstants.ENTITY_OPERATION);
    State state = new State();
    try {
      WorkflowVerfiy.verify(StringUtils.isBlank(definitionId), WorkflowError.INVALID_DEFINITION_ID);
      WorkflowVerfiy.verify(
          ObjectUtils.isEmpty(bpmnModelInstance), WorkflowError.INVALID_BPMN_MODEL_INSTANCE);

      if (StringUtils.isBlank(subscriptionId)) {
        // if subscriptionId is not present in the request, get it from the database by realmId
        String realmId = inputRequest.getValue(REALM_ID_KEY);
        WorkflowVerfiy.verify(StringUtils.isBlank(realmId), WorkflowError.INVALID_REALM_ID);
        subscriptionId = authDetailsService.getAuthDetailsFromRealmId(realmId).getSubscriptionId();
      }
      // Add the subscription id for activateTask
      state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);

      // For SaveWorkflowId task
      // TODO (sachin) revisit this statement
      state.addAll(inputRequest);
      WorkflowLogger.logInfo("Creating the workflow for definition=%s", definitionId);
      String workflowId = this.workflowId;
      if (!isUpdate || isMigrate) {
        workflowId =
            appConnectService
                .createWorkflow(subscriptionId, definitionId, bpmnModelInstance, definitionName)
                .getId();
      } else {
        WorkflowVerfiy.verify(
            StringUtils.isBlank(workflowId), WorkflowError.INVALID_WORKFLOW_ID_INPUT);
        boolean isUpdateAppConnectWorkflowDisabled = Objects.isNull(state.getValue(AsyncTaskConstants.IS_APPCONNECT_UPDATE_DISABLED)) ? false : state.getValue(AsyncTaskConstants.IS_APPCONNECT_UPDATE_DISABLED);
        WorkflowLogger.logInfo("Update workflow is disabled for=%s", workflowId);
        if(!isUpdateAppConnectWorkflowDisabled) {
          appConnectService.updateWorkflow(
              workflowId, subscriptionId, definitionId, bpmnModelInstance, definitionName);
        }
      }

      state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);

      // Activate the workflow
      if (activate) {
        WorkflowLogger.logInfo("Activating workflow. workflow=%s", workflowId);
        state.addValue(
            AsyncTaskConstants.ACTIVATE_RESPONSE_KEY,
            appConnectService.activateDeactivateActionWorkflow(workflowId, subscriptionId, true));
      }

      // Registering notification webhooks
      if (isUpdate) {
        if (ObjectUtils.isNotEmpty(entityType) && ObjectUtils.isNotEmpty(entityOperation)) {
          WorkflowLogger.logInfo(
              "Registering webhooks for entityType=%s with entityOperation=%s",
              entityType, entityOperation);
          appConnectService.registerToken(ownerId, entityType,
              entityOperation);
        }
      }
    } catch (WorkflowGeneralException workflowGeneralException) {
      state.addValue(AsyncTaskConstants.APP_CONNECT_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.APP_CONNECT_EXCEPTION, workflowGeneralException);
      state.addValue(
          AsyncTaskConstants.APP_CONNECT_ERROR_MESSAGE,
          workflowGeneralException.getWorkflowError());
    } catch (Exception e) {
      // Catching exception for roll back
      // TODO: Rollback not implemented for update failure
      state.addValue(AsyncTaskConstants.APP_CONNECT_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.APP_CONNECT_EXCEPTION, e);
      state.addValue(
          AsyncTaskConstants.APP_CONNECT_ERROR_MESSAGE, WorkflowError.CREATE_UPDATE_WORKFLOW_FAIL);
    }
    return state;
  }
}
