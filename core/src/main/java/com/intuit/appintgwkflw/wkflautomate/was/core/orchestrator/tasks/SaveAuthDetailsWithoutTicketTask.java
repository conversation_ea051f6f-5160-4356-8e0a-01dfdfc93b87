package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Optional;
import java.util.UUID;

/** This class saves {@link AuthDetails} in database */
@AllArgsConstructor
public class SaveAuthDetailsWithoutTicketTask implements Task {

  private final AuthDetailsRepository authDetailsRepository;

  @Override
  public State execute(State inputRequest) {
    String subscriptionId = inputRequest.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY);
    String realmId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);
    AuthDetails authDetails = inputRequest.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY);

    if (null == authDetails) {
      WorkflowVerfiy.verify(StringUtils.isBlank(realmId), WorkflowError.INVALID_REALM_ID);
      WorkflowVerfiy.verify(
          StringUtils.isBlank(subscriptionId), WorkflowError.INVALID_APPCONNECT_SUBSCRIPTION_ID);
      authDetails = new AuthDetails();
      authDetails.setOwnerId(Long.parseLong(realmId));
      authDetails.setAuthDetailsId(UUID.randomUUID().toString());
    }
    if (StringUtils.isNotEmpty(subscriptionId)) {
      authDetails.setSubscriptionId(subscriptionId);
    }
    State state = new State();
    final long ownerId = authDetails.getOwnerId();
    try {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .message("Saving authDetails for ownerId=%s", ownerId));
      state.addValue(
          AsyncTaskConstants.SAVE_AUTH_DETAILS_RESPONSE_KEY,
          authDetailsRepository.save(authDetails));
    } catch (DataIntegrityViolationException ex) {
      // Concurrent Request resulting in Constraint Violation
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .message("Failure=AUTH_EXISTS for ownerId=%s", ownerId)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.SAVE_AUTH_DETAILS));
    } catch (Exception e) {
      // save authdetails failure
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .message("Failure=DBWriteForOfflineTicket for ownerId=%s", ownerId)
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.SAVE_AUTH_DETAILS));
    }
    return state;
  }
}
