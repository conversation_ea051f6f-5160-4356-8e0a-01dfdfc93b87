package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DmnElement;

/** Saves the deployed definition in definition details table */
@AllArgsConstructor
public class SaveDefinitionInDataStoreTask implements Task {

  private DefinitionServiceHelper definitionServiceHelper;

  private DefinitionInstance definitionInstance;

  private Authorization authorization;

  private boolean isUpdate;

  @Override
  public State execute(State inputRequest) {
    State state = new State();
    try {
      // response from camunda
      DeployDefinitionResponse deployDefinitionResponse =
          validateAndCreateDeployResponse(inputRequest);
      definitionInstance.setWorkflowId(inputRequest.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
      Definition definition = definitionServiceHelper.saveUpdateDefinitionDetails(
              deployDefinitionResponse, definitionInstance, authorization, isUpdate);
      state.addValue(AsyncTaskConstants.SAVE_DEFINITION_RESPONSE_KEY, definition);
      state.addValue(AsyncTaskConstants.DEFINITION_KEY, definition.getDefinitionKey());
    } catch (Exception e) {
      // Catching exception for roll back
      state.addValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.SAVE_DEFINITION_EXCEPTION, e);
      state.addValue(
          AsyncTaskConstants.SAVE_DEFINITION_ERROR_MESSAGE, WorkflowError.DEFINITION_SAVE_ERROR);
    }
    return state;
  }

  private DeployDefinitionResponse validateAndCreateDeployResponse(State inputRequest) {
    final boolean isSingleDefinition =
        BooleanUtils.isTrue(inputRequest.getValue(AsyncTaskConstants.IS_SINGLE_DEFINITION));

    DeployDefinitionResponse deployDefinitionResponse;
    if (!isSingleDefinition) {
      deployDefinitionResponse =
          inputRequest.getValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY);
      WorkflowVerfiy.verifyNull(deployDefinitionResponse, WorkflowError.INVALID_INPUT);
    } else {
      // create deployDefinition for Single definition
      deployDefinitionResponse = createResponseForSingleDefinition(inputRequest);
    }
    return deployDefinitionResponse;
  }

  private DeployDefinitionResponse createResponseForSingleDefinition(final State inputRequest) {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    final Map<String, DeployDefinitionResponse.DeployedDefinition> bpmnDeployedDefinitionMap =
        new HashMap<>();
    //definitionId would be set in state from the appConnect task, since these two would be same

    // When a definition is deployed in Camunda, it uses the id of the process element internally
    // to determine definition key for BPMN and DMN and returned as part of deployed definition
    // response. Since we don't deploy the definition in Camunda for single definition, we are
    // extracting it from documents in similar way.
    String bpmnDefinitionKey = definitionInstance.getBpmnModelInstance().getModelElementsByType(Process.class).stream()
        .findFirst().map(
            BaseElement::getId).orElseThrow(() -> new WorkflowGeneralException(
            WorkflowError.DEFINITION_SAVE_ERROR));
    createDeployedDefinition(
        inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY), bpmnDefinitionKey,
        bpmnDeployedDefinitionMap);
    deployDefinitionResponse.setDeployedProcessDefinitions(bpmnDeployedDefinitionMap);

    // set for dmn
    final List<DmnModelInstance> dmnModelInstanceList =
        definitionInstance.getDmnModelInstanceList();
    if (CollectionUtils.isNotEmpty(dmnModelInstanceList)) {
      Map<String, DeployDefinitionResponse.DeployedDefinition> dmnDeployedDefinitionMap =
          new HashMap<>();
      dmnModelInstanceList.forEach(
          dmnModelInstance -> {
            String dmnDefinitionKey = dmnModelInstance.getModelElementsByType(Decision.class)
                .stream().findFirst().map(DmnElement::getId).orElseThrow(() -> new WorkflowGeneralException(
                    WorkflowError.DEFINITION_SAVE_ERROR));
            createDeployedDefinition(UUID.randomUUID().toString(), dmnDefinitionKey,
                dmnDeployedDefinitionMap);
          });
      deployDefinitionResponse.setDeployedDecisionDefinitions(dmnDeployedDefinitionMap);
    }
    return deployDefinitionResponse;
  }

  private void createDeployedDefinition(
      String id, String definitionKey,
      Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap) {
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId(id);
    deployedDefinition.setKey(definitionKey);
    deployedDefinitionMap.put(id, deployedDefinition);
  }

}
