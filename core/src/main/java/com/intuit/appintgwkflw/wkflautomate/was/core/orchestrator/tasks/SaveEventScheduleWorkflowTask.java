package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

/** <AUTHOR> */
@AllArgsConstructor
public class SaveEventScheduleWorkflowTask implements Task {

  private final EventScheduleService eventScheduleService;
  private final EventScheduleConfig eventScheduleConfig;

  @Override
  public State execute(State state) {
    Optional<List<EventScheduleWorkflowActionModel>> optionalEventScheduleWorkflowActionModels =
        state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
    if (optionalEventScheduleWorkflowActionModels == null
        || optionalEventScheduleWorkflowActionModels.isEmpty()) {
      WorkflowLogger.logInfo("Schedule Actions Models are empty");
      return state;
    }
    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
        optionalEventScheduleWorkflowActionModels.get();
    String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
    try {
      List<EventScheduleResponse> eventScheduleResponses =
          eventScheduleService.createSchedules(
              EventScheduleServiceUtil.getCreateEventSchedulesPayload(
                  eventScheduleWorkflowActionModels, eventScheduleConfig),
              realmId);
      // fetch errors
      List<EventScheduleError> eventScheduleErrors =
          EventScheduleServiceUtil.getErrorsFromResponse(eventScheduleResponses);
      WorkflowVerfiy.verify(
          CollectionUtils.isNotEmpty(eventScheduleErrors),
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE,
          eventScheduleErrors);
      // fetch data
      List<EventScheduleData> eventScheduleData =
          EventScheduleServiceUtil.getDataFromResponse(eventScheduleResponses);
      WorkflowVerfiy.verify(
          CollectionUtils.isEmpty(eventScheduleData),
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE,
          "No schedules created");
      WorkflowVerfiy.verify(
          eventScheduleResponses.size() != eventScheduleWorkflowActionModels.size(),
          WorkflowError.EVENT_SCHEDULE_CALL_FAILURE,
          "Schedules Not created for all actions");
      // convert data to response
      // response has 1*1 mapping with payload data
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE,
          IntStream.range(0, eventScheduleWorkflowActionModels.size())
              .boxed()
              .collect(
                  Collectors.toMap(
                      index -> eventScheduleWorkflowActionModels.get(index).getActionName(),
                      index ->
                          // get the localId
                          EventScheduleServiceUtil.getLocalId(
                              eventScheduleData.get(index).getId()))));

    } catch (WorkflowGeneralException workflowGeneralException) {
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION, workflowGeneralException);
      state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE, WorkflowError.EVENT_SCHEDULE_CALL_FAILURE);
    }

    return state;
  }
}
