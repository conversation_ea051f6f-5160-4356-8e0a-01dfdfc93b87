package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/** <AUTHOR> */
@AllArgsConstructor
public class SaveScheduleDetailsInDataStoreTask implements Task {
  private final SchedulerDetailsRepository schedulerDetailsRepository;

  @Override
  public State execute(State state) {
    String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
    String definitionId = state.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
    try {
      WorkflowVerfiy.verify(
          StringUtils.isBlank(realmId) || StringUtils.isBlank(definitionId),
          WorkflowError.INPUT_INVALID,
          AsyncTaskConstants.REALM_ID_KEY + "," + AsyncTaskConstants.DEFINITION_ID_KEY);

      Map<String, String> actionSchedulerIdMap =
          state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE);
      logInfo(
          "Saving Scheduler details for realmId=%s, definitionId=%s, actionIdMap=%s",
          realmId, definitionId, actionSchedulerIdMap);
      Optional.ofNullable(
              prepareSchedulerDetails(definitionId, realmId, actionSchedulerIdMap, state))
          .ifPresentOrElse(
              schedulerDetails -> {
                schedulerDetailsRepository.saveAll(schedulerDetails);
                logInfo("Scheduler details=%s saved to database successfully ", schedulerDetails);
              }, () -> {
                // schedules are not created
                throw new WorkflowGeneralException(WorkflowError.SCHEDULE_IDS_EMPTY);
              });
    } catch (Exception exception) {
      logError("Exception occurred while saving scheduler details to the database", exception);
      state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_IN_DATASTORE_EXCEPTION, exception);
      state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_ERROR_MESSAGE, WorkflowError.SAVE_SCHEDULE_DETAILS_EXCEPTION);
    }
    return state;
  }

  /**
   * This method prepares the scheduler details for the given definitionId
   *
   * @param defId
   * @param realmId
   * @param actionIdMap
   * @return
   */
  private List<SchedulerDetails> prepareSchedulerDetails(
      String defId, String realmId, Map<String, String> actionIdMap, State state) {
    if (MapUtils.isEmpty(actionIdMap)) {
      return null;
    }
    List<SchedulerDetails> schedulerDetails = actionIdMap.keySet().stream()
        .map(
            key ->
                SchedulerDetails.builder()
                    .definitionDetails(DefinitionDetails.builder().definitionId(defId).build())
                    .ownerId(Long.valueOf(realmId))
                    .schedulerId(actionIdMap.get(key))
                    .schedulerAction(SchedulerAction.fromAction(key))
                    .build())
        .collect(Collectors.toList());
    // add to state in case of rollback
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS,
        schedulerDetails.stream().map(SchedulerDetails::getSchedulerId).collect(
            Collectors.toList()));
    return schedulerDetails;
  }
  /**
   * Log the info
   *
   * @param message
   * @param workflowMessageArgs
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.ESS)
                .downstreamServiceName(DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS));
  }

  /**
   * Log the error
   *
   * @param message
   * @param workflowMessageArgs
   */
  private void logError(String message, Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.ESS)
                .downstreamServiceName(DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS));
  }
}
