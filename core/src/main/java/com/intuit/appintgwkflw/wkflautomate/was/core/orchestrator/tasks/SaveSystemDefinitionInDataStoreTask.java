package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

/** Saves the deployed definition in definition details table */
@AllArgsConstructor
public class SaveSystemDefinitionInDataStoreTask implements Task {

  private DefinitionServiceHelper definitionServiceHelper;
  private TemplateModelInstance model;

  @Override
  public State execute(State inputRequest) {
    try {
      // template details
      TemplateDetails bpmnTemplateDetails =
          inputRequest.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY);

      // If Definition type is SYSTEM or
      // If Definition type is SINGLE and is of CUSTOM_APPROVAL then save the definition details to our database.
      // What happens when different entity custom approval definitions need to get saved ?
      if(WorkflowConstants.SYSTEM.equalsIgnoreCase(bpmnTemplateDetails.getDefinitionType().name()) ||
              (WorkflowConstants.SINGLE.equalsIgnoreCase(bpmnTemplateDetails.getDefinitionType().name()) &&
                      bpmnTemplateDetails.getTemplateName().equalsIgnoreCase(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE))){
        // response from Camunda
        DeployDefinitionResponse deployDefinitionResponse =
                validateAndCreateDeployResponse(inputRequest);
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(deployDefinitionResponse,
                bpmnTemplateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

      }


    } catch (Exception e) {
      // Catching exception for roll back
      inputRequest.addValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_TASK_FAILURE, true);
      inputRequest.addValue(AsyncTaskConstants.SAVE_SYSTEM_DEF_EXCEPTION, e);
      inputRequest.addValue(
          AsyncTaskConstants.SAVE_SYSTEM_DEF_ERROR_MESSAGE, WorkflowError.DEFINITION_SAVE_ERROR);
    }
    return inputRequest;
  }

  private DeployDefinitionResponse validateAndCreateDeployResponse(State inputRequest) {
    DeployDefinitionResponse deployDefinitionResponse
          = inputRequest.getValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY);
    WorkflowVerfiy.verifyNull(deployDefinitionResponse, WorkflowError.INVALID_INPUT,
        "Could not get or save response from camunda");
    return deployDefinitionResponse;
  }
}
