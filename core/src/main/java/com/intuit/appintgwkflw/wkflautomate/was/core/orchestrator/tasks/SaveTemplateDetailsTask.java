package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.DynamicBpmnWasCamundaTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;


/**
 * Task for saving Bpmn, Dmn with camunda definition deployment Id and trigger details during
 * template save/update
 */
@RequiredArgsConstructor
public class SaveTemplateDetailsTask implements Task {

  private final TemplateDetailsRepository templateDetailsRepository;

  private final TriggerDetailsRepository triggerDetailsRepository;

  private final TemplateDomainEventHandler templateDomainEventHandler;

  @Override
  @Transactional
  public State execute(State state) {
    try {
      // Optional field
      DeployDefinitionResponse deployedDefinitionResponse =
          state.getValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY);
      // required field
      TemplateDetails bpmnTemplateDetails = state.getValue(AsyncTaskConstants.BPMN_DETAILS_KEY);
      WorkflowVerfiy.verifyNull(
          bpmnTemplateDetails, WorkflowError.INPUT_INVALID, TemplateDetails.class.getSimpleName());
      // DMN list can be optional
      List<TemplateDetails> dmnTemplateDetailsList = state.getValue(AsyncTaskConstants.DMN_DETAILS_KEY);
      List<TriggerDetails> triggerDetailsList = state.getValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY);
      WorkflowVerfiy.verifyNull(
          triggerDetailsList, WorkflowError.INPUT_INVALID, TriggerDetails.class.getSimpleName());
      if (ObjectUtils.isNotEmpty(deployedDefinitionResponse)) {
        populateDeploymentIdInTemplateDetails(
            deployedDefinitionResponse, bpmnTemplateDetails, dmnTemplateDetailsList);
      }
      List<TemplateDetails> templateDetailsList = new ArrayList<>();
      templateDetailsList.add(bpmnTemplateDetails);
      Optional.ofNullable(dmnTemplateDetailsList).ifPresent(templateDetailsList::addAll);
      saveDetailsInDB(templateDetailsList, triggerDetailsList);

      // saving the templateDetailsList in state
      state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_DETAILS_LIST_KEY, templateDetailsList);

      // Add template details to state
      Optional<TemplateDetails> templateDetails = templateDetailsList.stream()
          .filter(template -> ModelType.BPMN.equals(template.getModelType())).findFirst();

      templateDetails.ifPresent(
          template -> state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template));


    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .stackTrace(e)
                  .message("Error while executing SaveTemplateDetails task")
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));
      state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION, e);
      state.addValue(
          AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE, WorkflowError.TEMPLATE_SAVE_EXCEPTION);
    }
    return state;
  }

  private void populateDeploymentIdInTemplateDetails(
      DeployDefinitionResponse deployedDefinitionResponse,
      TemplateDetails bpmnTemplateDetails,
      List<TemplateDetails> dmnTemplateDetailsList) {

    populateDeploymentIdInBpmn(
        deployedDefinitionResponse.getDeployedProcessDefinitions(), bpmnTemplateDetails);
    populateDeploymentIdInDmn(
        deployedDefinitionResponse.getDeployedDecisionDefinitions(), dmnTemplateDetailsList, bpmnTemplateDetails);
  }

  private void populateDeploymentIdInBpmn(
      Map<String, DeployDefinitionResponse.DeployedDefinition> processDefinitionsMap,
      TemplateDetails bpmnTemplateDetails) {
    // Assuming is we have one BPMN, get the processDefinition
    DeployDefinitionResponse.DeployedDefinition bpmnDefinition =
        processDefinitionsMap.values().stream()
            .findFirst()
            .orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.CAMUNDA_DEPLOYMENT_FAILED));
    // set the deployed definition id
    bpmnTemplateDetails.setDeployedDefinitionId(bpmnDefinition.getId());
  }

  private void populateDeploymentIdInDmn(
      Map<String, DeployDefinitionResponse.DeployedDefinition> decisionDefinitionsMap,
      List<TemplateDetails> dmnTemplateDetailsList,
      TemplateDetails bpmnTemplateDetails) {

    if (MapUtils.isNotEmpty(decisionDefinitionsMap)) {
      // map the dmn name to the TemplateDetails Object.
      // It creates the map of name and reference to original templateDetails object
      Map<String, TemplateDetails> templateDetailsMap = new HashMap<>();
      dmnTemplateDetailsList.stream().forEach( templateDetails -> {
                templateDetailsMap.put(
                        DynamicBpmnWasCamundaTransformer.getTemplateNameCamundaDeployment(
                                templateDetails, bpmnTemplateDetails), templateDetails);
      });

      decisionDefinitionsMap
          .values()
          .forEach(
              decisionDefinition ->
                  templateDetailsMap
                      .get(decisionDefinition.getKey())
                      .setDeployedDefinitionId(decisionDefinition.getId()));
    }
  }

  void saveDetailsInDB(List<TemplateDetails> templateDetails, List<TriggerDetails> triggerDetails) {

    templateDetailsRepository.saveAll(templateDetails);

    TemplateDetails templateDetail = templateDetails.stream().
            filter(tempDetail -> tempDetail.getModelType() == ModelType.BPMN)
            .findFirst()
            .orElse(null);

    List<DomainEntityRequest<TemplateDetails>> domainEvents = org.apache.commons.collections4.CollectionUtils.emptyIfNull(templateDetails)
            .stream().map(
                    tempDetail -> DomainEntityRequest.<TemplateDetails>builder()
                            .request(tempDetail)
                            .entityChangeAction(templateDetail.getVersion() == 1 ? EntityChangeAction.CREATE : EntityChangeAction.UPDATE)
                            .build()
            ).collect(Collectors.toList());

    templateDomainEventHandler.publishAll(domainEvents);

    triggerDetailsRepository.saveAll(triggerDetails);
  }
}
