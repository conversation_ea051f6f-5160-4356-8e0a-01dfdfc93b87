package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.TRIGGER_PROCESS_V2;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/** <AUTHOR> */

/**
 * This Task starts process of recurring nature from within WAS. This uses V1 implementation
 * internally to start the recurring processes. This task is used in Create/ Update and Enable
 * definition use cases.
 */
@AllArgsConstructor
public class StartRecurringProcessTask implements Task {

  private final MetricLogger metricLogger;

  private final RunTimeService runTimeService;

  @Override
  public State execute(State inputRequest) {
    final String ownerId = inputRequest.getValue(REALM_ID_KEY);
    TransactionEntity transactionEntity = inputRequest.getValue(TRIGGER_TRANSACTION_ENTITY);
    String definitionId = inputRequest.getValue(DEFINITION_ID_KEY);
    try {
      /*
       * adding additional check on null definition id to prevent
       * trigger process on other ownerid + entity type matching definitions
       */
      WorkflowVerfiy.verify(
          StringUtils.isEmpty(definitionId), WorkflowError.INVALID_DEFINITION_ID);
      /**
       * Setting provider workflow id in trigger payload - used in create scenario where workflow id
       * is not populated till definition save is completed
       */
      if (ObjectUtils.isEmpty(transactionEntity.getEventHeaders().getProviderWorkflowId())) {
        transactionEntity
            .getEventHeaders()
            .setProviderWorkflowId(inputRequest.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
        transactionEntity
            .getV3EntityPayload()
            .put(WorkflowConstants.EVENT_HEADERS, transactionEntity.getEventHeaders());
      }

      WorkflowGenericResponse response =
          runTimeService.processTriggerMessageV2(transactionEntity.getV3EntityPayload());
      inputRequest.addValue(definitionId, response);
    } catch (WorkflowGeneralException workflowGeneralException) {
      WorkflowLogger.logError(
          workflowGeneralException,
          "Error in starting recurring process for definitionId=%s and ownerId=%s",
          definitionId,
          ownerId);

      metricLogger.logErrorMetric(TRIGGER_PROCESS_V2, Type.API_METRIC, workflowGeneralException);
      inputRequest.addValue(AsyncTaskConstants.RECURRENCE_START_PROCESS_TASK_FAILURE, true);
      inputRequest.addValue(
          AsyncTaskConstants.RECURRENCE_START_PROCESS_EXCEPTION, workflowGeneralException);
      inputRequest.addValue(
          AsyncTaskConstants.START_RECURRING_PROCESS_ERROR_MESSAGE,
          WorkflowError.TRIGGER_START_PROCESS_ERROR);
    }
    return inputRequest;
  }
}
