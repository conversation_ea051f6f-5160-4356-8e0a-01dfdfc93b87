package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.TRIGGER_PROCESS_V2;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_START_EVENTS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus.NO_ACTION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus.PROCESS_STARTED;
import static java.lang.String.valueOf;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3StartProcess;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * <p>Start process for a given definition.
 */
@AllArgsConstructor
public class StartUserDefinitionTask implements Task {

  private final V3StartProcess startProcess;

  private final WASContextHandler contextHandler;

  private final V3RunTimeHelper runTimeHelper;

  private final DefinitionDetails definitionDetails;

  private final MetricLogger metricLogger;

  @Override
  public State execute(State inputRequest) {

    final String ownerId = contextHandler.get(WASContextEnums.OWNER_ID);
    TransactionEntity transactionEntity = inputRequest.getValue(TRIGGER_TRANSACTION_ENTITY);
    Map<String, Object> initialStartEventExtensionPropertiesMap = inputRequest.getValue(BPMN_START_EVENTS);
    Map<String, Object> startProcessResult = null;
    try {
      startProcessResult =
          startProcess.startProcess(transactionEntity, definitionDetails, initialStartEventExtensionPropertiesMap);

      // 6) update processDetails table
      if (ObjectUtils.allNotNull(startProcessResult.get(WorkflowConstants.ID))) {
        runTimeHelper.saveProcessDetailsInstance(
            transactionEntity.getEntityId(),
            Long.parseLong(ownerId),
            (String) startProcessResult.get(WorkflowConstants.ID),
            ProcessStatus.ACTIVE,
            definitionDetails,
            null,
            runTimeHelper.getEntityObjectMap(transactionEntity));
      }
    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .message(
                      "Error in starting process recordId=%s definitionId=%s",
                      transactionEntity.getEntityId(), definitionDetails.getDefinitionId())
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS));
      metricLogger.logErrorMetric(TRIGGER_PROCESS_V2, Type.API_METRIC, e);
    }
    String processId =
        !CollectionUtils.isEmpty(startProcessResult)
            && nonNull(startProcessResult.get(WorkflowConstants.ID))
            ? valueOf(startProcessResult.get(WorkflowConstants.ID))
            : null;

    TriggerStatus status =
        isEmpty(startProcessResult)
            ? TriggerStatus.ERROR_STARTING_PROCESS
            : isBlank(processId) ? NO_ACTION : PROCESS_STARTED;

    inputRequest.addValue(
        definitionDetails.getDefinitionId(),
        WorkflowTriggerResponse.builder()
            .definitionId(getDefinitionId())
            .processId(processId)
            .status(status)
            .definitionName(getDefinitionName())
            .build());
    return inputRequest;
  }

  /**
   * @return definition ID
   */
  private String getDefinitionId() {

    return nonNull(definitionDetails) ? definitionDetails.getDefinitionId() : null;
  }

  /**
   * @return definition Name
   */
  private String getDefinitionName() {

    return nonNull(definitionDetails) ? definitionDetails.getDefinitionName() : null;
  }

  public boolean isFatal() {

    return false;
  }
}
