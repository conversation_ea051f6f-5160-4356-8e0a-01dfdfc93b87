package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;

import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *     <p>task to unsubscribe workflows from app connect
 */
@AllArgsConstructor
public class UnsubscribeAppConnectTask implements Task {

  private AppConnectService appConnectService;

  private AuthDetailsService authDetailsService;

  @Override
  public State execute(State inputRequest) {

    String realmId = inputRequest.getValue(AsyncTaskConstants.REALM_ID_KEY);
    boolean isOfflineTicketPresent =
        ObjectUtils.isNotEmpty(inputRequest.getValue(AsyncTaskConstants.IS_OFFLINE_KEY))
            ? inputRequest.getValue(AsyncTaskConstants.IS_OFFLINE_KEY)
            : false;

    // fetch subscription id and throws exception if realm is empty or not found details
    AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Unsubscribing from app-connect by subscriptionId=%s",
                    authDetails.getSubscriptionId())
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.UNSUBSCRIBE_APPCONNECT)
                .className(this.getClass().getName()));

    try {
      appConnectService.unsubscribe(authDetails, isOfflineTicketPresent);

    } catch (WorkflowGeneralException workflowGeneralException) {
      if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
          && (workflowGeneralException.getMessage()
              .contains(WorkflowConstants.RESOURCE_NOT_FOUND))) {
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message(
                        "Workflow already unsubscribed from app-connect for subscriptionId=%s",
                        authDetails.getSubscriptionId())
                    .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                    .downstreamServiceName(DownstreamServiceName.UNSUBSCRIBE_APPCONNECT)
                    .className(this.getClass().getName()));
      } else {
        throw workflowGeneralException;
      }
    }

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Unsubscribed succesfully from app-connect by subscriptionId=%s",
                    authDetails.getSubscriptionId())
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.UNSUBSCRIBE_APPCONNECT)
                .className(this.getClass().getName()));

    return inputRequest;
  }
}
