package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/** This task updates the definition details like: workflowId */
@AllArgsConstructor
public class UpdateDefinitionDetailsTask implements Task {

  private DefinitionDetailsRepository definitionDetailsRepository;

  @Override
  public State execute(State inputRequest) {
    State state = new State();
    String workflowId = inputRequest.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY);
    String definitionId = inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
    
    state.addAll(inputRequest);
    try {
      WorkflowVerfiy.verify(
          StringUtils.isEmpty(definitionId) || StringUtils.isEmpty(workflowId),
          WorkflowError.INVALID_INPUT);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Setting the workflowId=%s for definitionId=%s", workflowId, definitionId)
                  .className(this.getClass().getName()));

      definitionDetailsRepository.setWorkflowId(definitionId, workflowId);
      
    } catch (Exception e) {
      //Catching exception for roll back
      state.addValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE,true);
      state.addValue(AsyncTaskConstants.UPDATE_DEFINITION_EXCEPTION,e);
      state.addValue(AsyncTaskConstants.UPDATE_DEFINITION_ERROR_MESSAGE,WorkflowError.DEFINITION_UPDATE_ERROR);
    }
    return state;
  }
}
