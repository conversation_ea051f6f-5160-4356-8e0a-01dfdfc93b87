package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/** <AUTHOR> */

/** This class is responsible for updating the schedule (status and recurrence pattern) on ESS. */
@AllArgsConstructor
public class UpdateEventScheduleTask implements Task {

  private final EventScheduleService eventScheduleService;
  private final EventScheduleConfig eventScheduleConfig;

  @Override
  public State execute(State state) {
    String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
    ScheduleStatus scheduleStatus = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS);
    WorkflowVerfiy.verify(
        StringUtils.isBlank(realmId) || scheduleStatus == null,
        WorkflowError.INPUT_INVALID,
        "realmId or schedule status");
    List<String> scheduleIds = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);

    if (CollectionUtils.isEmpty(scheduleIds)) {
      return state;
    }
    logInfo("Updating schedule status. status=%s for scheduleIds=%s", scheduleStatus, scheduleIds);
    // make call to ESS
    List<EventScheduleResponse> eventScheduleUpdateResponse =
        eventScheduleService.updateSchedules(
            prepareEventSchedulePayload(scheduleIds, scheduleStatus, realmId, state), realmId);

    // validate errors
    EventScheduleServiceUtil.validateErrors(
        eventScheduleUpdateResponse,
        scheduleStatus,
        scheduleIds,
        DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS);

    // get all the data and log here
    Optional.ofNullable(EventScheduleServiceUtil.getDataFromResponse(eventScheduleUpdateResponse))
        .stream()
        .findAny()
        .ifPresent(
            data ->
                logInfo(
                    "Updated ESS scheduler status=%s for schedulerIds=%s",
                    scheduleStatus, scheduleIds));
    return state;
  }

  /**
   * Log the info
   *
   * @param message
   * @param workflowMessageArgs
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.ESS)
                .downstreamServiceName(DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS));
  }

  /**
   * This method prepares the event schedule payload for the given scheduleIds and status
   *
   * @param scheduleIds
   * @param scheduleStatus
   * @param realmId
   * @return
   */
  @VisibleForTesting
  List<EventSchedule> prepareEventSchedulePayload(
      List<String> scheduleIds, ScheduleStatus scheduleStatus, String realmId, State state) {
    boolean isUpdateCompleteSchedule =
        BooleanUtils.toBoolean(
            (Boolean) state.getValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE));

    if (!isUpdateCompleteSchedule) {
      return scheduleIds.stream()
          .map(
              id ->
                  EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                      id, scheduleStatus, realmId))
          .collect(Collectors.toList());
    }

    Map<String, EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModelMap =
        getEventScheduleWorkflowActionModelMap(state);

    Map<String, SchedulerDetails> schedulerDetailsMap =
        state.getValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP);
    return scheduleIds.stream()
        .map(
            id -> {
              SchedulerDetails schedulerDetails = schedulerDetailsMap.get(id);
              EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                  ObjectUtils.isNotEmpty(schedulerDetails)
                      ? eventScheduleWorkflowActionModelMap.get(
                          schedulerDetails.getSchedulerAction().getAction())
                      : null;
              return EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                  id, scheduleStatus, realmId, eventScheduleWorkflowActionModel, eventScheduleConfig);
            })
        .collect(Collectors.toList());
  }

  private Map<String, EventScheduleWorkflowActionModel> getEventScheduleWorkflowActionModelMap(
      State state) {
    Optional<List<EventScheduleWorkflowActionModel>> optionalEventScheduleWorkflowActionModels =
        state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
    if (Objects.nonNull(optionalEventScheduleWorkflowActionModels)
        && !optionalEventScheduleWorkflowActionModels.isEmpty()) {
      List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModel =
          optionalEventScheduleWorkflowActionModels.get();
      return eventScheduleWorkflowActionModel.stream()
          .collect(
              Collectors.toMap(
                  EventScheduleWorkflowActionModel::getActionName, Function.identity()));
    }
    return Map.of();
  }
}
