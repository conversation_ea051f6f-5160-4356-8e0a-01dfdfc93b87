package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.WorkflowNameToSchedulerActionMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logInfo;

/**
 * Task to update the migration flag for a given DefinitionDetails entity.
 * This task sets the is_migrated flag to true in the SchedulerDetailsRepository.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class UpdateSchedulerDetailsForSchedulingMigrationTask implements Task {
    private DefinitionDetails definitionDetails;
    private SchedulerDetailsRepository schedulerDetailsRepository;
    private boolean status;

    /**
     * Executes the task to update the migration flag.
     *
     * @param state The current state of the task execution.
     * @return The updated state after execution.
     */
    @Override
    public State execute(State state) {
        WorkflowVerfiy.verifyNull(definitionDetails, WorkflowError.INVALID_INPUT);
        logInfo("Updating the is_migrated flag to true for definitionId=%s", definitionDetails.getDefinitionId());
        int count = schedulerDetailsRepository.setMigrationFlagForDefinitionId(definitionDetails.getDefinitionId(), status);
        WorkflowVerfiy.verify(count != WorkflowNameToSchedulerActionMapper.getActionsByType(definitionDetails.getTemplateDetails().getTemplateName()).size(), WorkflowError.UPDATE_SCHEDULE_DETAILS_EXCEPTION);
        logInfo("is_migrated flag updated to true for definitionId=%s", definitionDetails.getDefinitionId());
        return state;
    }

    /**
     * Handles errors that occur during the execution of the task.
     * Sets the UPDATE_DEFINITION_TASK_FAILURE flag to true in the state.
     *
     * @param state The current state of the task execution.
     * @return The updated state after handling the error.
     */
    @Override
    public State onError(State state){
        state.addValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE,true);
        return state;
    }
}
