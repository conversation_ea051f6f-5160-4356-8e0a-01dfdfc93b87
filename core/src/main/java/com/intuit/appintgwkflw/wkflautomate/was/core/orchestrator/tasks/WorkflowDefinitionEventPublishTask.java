package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.workflowTemplate.WorkflowDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;

/** This task is for publishing an eventbus message for any newly added or updated workflow definition */
@AllArgsConstructor
public class WorkflowDefinitionEventPublishTask implements Task {

    private final EventPublisherCapability eventPublisherCapability;
  private final EventPublisherUtil eventProducerUtil;

    @Override
    public State execute(State inputRequest) {
        try {
            // template details
            TemplateDetails bpmnTemplateDetails =
                    inputRequest.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY);

            if (Objects.isNull(bpmnTemplateDetails) || Objects.isNull(bpmnTemplateDetails.getId())){
                throw new WorkflowEventException(new WorkflowNonRetriableException(WorkflowError.MISSING_TEMPLATE_ID));
            }

            String intuitTid = inputRequest.getValue(WorkflowConstants.INTUIT_TID);
            EventHeaderEntity eventHeaderEntity = InternalEventsUtil
                    .buildEventHeader(buildTemplateEventHeaders(bpmnTemplateDetails, intuitTid), PublishEventType.WORKFLOW_DEFINITION, EventEntityType.WORKFLOW_DEFINITION);
            eventPublisherCapability
                    .publish(eventHeaderEntity, buildTemplateEventPayload(bpmnTemplateDetails));
            EventingLoggerUtil.logInfo(
                    "step=WorkflowDefinitionEventPublished definitionKey=%s definitionVersion=%s",
                    this.getClass().getSimpleName(),
                    bpmnTemplateDetails.getTemplateName(),
                    bpmnTemplateDetails.getVersion());
        } catch (WorkflowEventException ex) {
            // Logging WorkflowEventException workflow definition event publishing
            EventingLoggerUtil.logError(
                    "Workflow Definition Event Publish Failed step=WorkflowDefinitionEventPublishFailed error=%s",
                    this.getClass().getSimpleName(),
                    ex);
            inputRequest.addValue(AsyncTaskConstants.WORKFLOW_DEF_EVENT_PUBLISH_EXCEPTION, ex);
        } catch (Exception e) {
            WorkflowLogger.error(
                    () ->
                            WorkflowLoggerRequest.builder()
                                    .className(this.getClass().getSimpleName())
                                    .stackTrace(e)
                                    .message("Error while executing WorkflowDefinitionEventPublish task"));
            inputRequest.addValue(AsyncTaskConstants.WORKFLOW_DEF_EVENT_PUBLISH_EXCEPTION, e);
        }
        return inputRequest;
    }

    private Map<String, String> buildTemplateEventHeaders(TemplateDetails templateDetails, String intuitTid) {
        Map<String, String> headers = new HashMap<>();
        headers.put(EventHeaderConstants.ENTITY_ID, templateDetails.getId());
        // Setting idempotence key to templateId : templateVersion
        headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, templateDetails.getId().concat(WorkflowConstants.COLON)
                .concat(String.valueOf(templateDetails.getVersion())));
        headers.put(EventHeaderConstants.INTUIT_TID, intuitTid);
        headers.put(EventHeaderConstants.OFFERING_ID, eventProducerUtil.getOfferingId());
        return headers;
    }

    private WorkflowDefinition buildTemplateEventPayload(TemplateDetails templateDetails) {
        return WorkflowDefinition.builder()
                .businessEntityType(templateDetails.getRecordType() != null
                        ? templateDetails.getRecordType().getRecordType() : null)
                .definitionKey(templateDetails.getTemplateName())
                .definitionName(templateDetails.getDisplayName())
                .definitionVersion(templateDetails.getVersion())
                .definitionType(templateDetails.getDefinitionType() != null
                        ? templateDetails.getDefinitionType().name() : null)
                .build();
    }
}
