package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * This task deletes the deployed definition from Camunda
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class DeployDefinitionRollBackTask implements Task {

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @Override
  public State execute(State state) {
    String deploymentId = state.getValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY);
    if (ObjectUtils.isEmpty(deploymentId)) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback Error : Failed to invoke camunda create definition rollback as deploymentId not set")
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION)
                  .className(this.getClass().getName()));
      return state;
    }

    try {
      DeleteDeploymentRequest deleteDeploymentRequest =
          new DeleteDeploymentRequest(deploymentId, true, true);
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback : Performing delete in camunda for deploymentId=%s ", deploymentId)
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION)
                  .className(this.getClass().getName()));

      bpmnEngineDefinitionServiceRest.deleteDeployment(deleteDeploymentRequest);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback: definition entries deleted from camunda with deploymentId=%s",
                      deploymentId)
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION)
                  .className(this.getClass().getName()));

      // log and swallow roll back exceptions
    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .stackTrace(e)
                  .message(
                      "Rollback Error : Failed to delete definition in camunda for deploymentId=%s ",
                      deploymentId)
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION)
                  .className(this.getClass().getName()));
    }

    return state;
  }

  /* Setting fatal to false so that the chain does not break even if the task break */
  @Override
  public boolean isFatal() {
    return false;
  }
}
