package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import org.apache.commons.lang3.ObjectUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.AppConnectDeleteWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import lombok.AllArgsConstructor;

/** 
 * This task deletes the workflow in app connect 
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveAndActivateAppConnectRollBackTask implements Task {

  private final AppConnectService appConnectService;
  private final AuthDetailsService authDetailsService;

  @Override
  public State execute(State state) {

    String workflowId = state.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY);
    String subscriptionId = state.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY);

    if (ObjectUtils.isEmpty(workflowId)) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback Error : Failed to invoke delete api in appconnect as workflowId not set")
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
      return state;
    }

    try {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback : Invoking delete api in appconnect for workflowId=%s and subscriptionId=%s",
                      workflowId, subscriptionId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));

      state =
          new AppConnectDeleteWorkflowTask(appConnectService, authDetailsService).execute(state);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback : AppConnect workflow deleted for workflowId=%s and subscriptionId=%s",
                      workflowId, subscriptionId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
    }

    // log and swallow roll back exceptions
    catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .stackTrace(e)
                  .message(
                      "Rollback Error : Failed to invoke delete api in appconnect for workflowId=%s and subscriptionId=%s",
                      workflowId, subscriptionId)
                  .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                  .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                  .className(this.getClass().getName()));
    }

    return state;
  }
  
  /* Setting fatal to false so that the chain does not break even if the task break */ 
  @Override
  public boolean isFatal() {
    return false;
  }
}
