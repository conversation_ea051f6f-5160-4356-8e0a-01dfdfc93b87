package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import org.apache.commons.lang3.ObjectUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import lombok.AllArgsConstructor;

/** 
 * Delete the deployed definition in definition details table 
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveDefinitionRollBackTask implements Task {

  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final EnabledDefinitionCacheService enabledDefinitionCacheService;

  @Override
  public State execute(State state) {

    String definitionId = state.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
    if (ObjectUtils.isEmpty(definitionId)) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Rollback Error : Failed to invoke DB rollback as definitionId not set")
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                  .className(this.getClass().getName()));
      return state;
    }

    // try deleting definition in DB
    try {

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Rollback : Performing delete in db for definitionId=%s ", definitionId)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                  .className(this.getClass().getName()));

      state = new DataStoreDeleteDefinitionTask(
              definitionDetailsRepository,
              definitionActivityDetailsRepository,
              enabledDefinitionCacheService
      ).execute(state);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Rollback: definition entries deleted from datastore with definitionId=%s",
                      definitionId)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                  .className(this.getClass().getName()));

      // log and swallow roll back exceptions
    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .stackTrace(e)
                  .message(
                      "Rollback Error : Failed to delete definition in db for definitionId=%s ",
                      definitionId)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
                  .className(this.getClass().getName()));
    }

    return state;
  }
  
  /* Setting fatal to false so that the chain does not break even if the task break */ 
  @Override
  public boolean isFatal() {
    return false;
  }
}
