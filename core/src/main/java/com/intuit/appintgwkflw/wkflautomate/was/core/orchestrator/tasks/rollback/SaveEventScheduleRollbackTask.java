package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Delete the event schedules from  ESS and database for the given definitionId.
 *
 * <AUTHOR>
 */

@AllArgsConstructor
public class SaveEventScheduleRollbackTask implements Task {

  private final EventScheduleHelper eventScheduleHelper;

  @Override
  public State execute(State state) {
    String realmId = state.getValue(REALM_ID_KEY);
    if (ObjectUtils.isEmpty(realmId)) {
      WorkflowLogger.logError(
          "Rollback Error : Failed to invoke event schedule ESS and DB rollback as realmId not set");
      return state;
    }
    try {
      // no need to delete from database only delete from ESS
      if (state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE) != null) {
        List<String> scheduleIds = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);
        WorkflowLogger.logInfo(
            "Rollback : Performing delete for event schedule from ESS and DB for scheduleIds=%s ",
            scheduleIds);
        eventScheduleHelper.deleteSchedulesFromESS(
            scheduleIds, realmId);
        return state;
      }

      String definitionId = state.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
      if (ObjectUtils.isEmpty(definitionId)) {
        WorkflowLogger.logError(
            "Rollback Error : Failed to invoke event schedule ESS and DB rollback as definitionId not set");
        return state;
      }

      // try deleting event schedules from ESS and DB.
      WorkflowLogger.logInfo(
          "Rollback : Performing delete for event schedule in ESS and DB for definitionId=%s ",
          definitionId);
      eventScheduleHelper.deleteAllSchedules(
          List.of(DefinitionDetails.builder().definitionId(definitionId).build()), realmId);
      WorkflowLogger.logInfo(
          "Rollback: Event schedules entries deleted from ESS and DB with definitionId=%s",
          definitionId);
      // log and swallow roll back exceptions
    } catch (Exception e) {
      WorkflowLogger.logError(
          "Rollback Error : Failed to delete event schedules entries from ESS and/or DB",
          e);
    }
    return state;
  }
}
