package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;

/**
 * The `SaveEventSchedulingRollBackTask` class is responsible for handling the rollback operation
 * of event scheduling within a workflow. It implements the `Task` interface and provides the
 * `execute` method to perform the rollback logic.
 *
 * This class utilizes the `SchedulingService` to delete event schedules based on the provided
 * state information. The rollback operation is executed when certain conditions are met, such as
 * the presence of a valid `realmId` and `definitionKey` in the state. If these conditions are not
 * met, appropriate error messages are logged using the `WorkflowLogger`.
 *
 * Any exceptions encountered during the rollback process are caught and logged as errors, ensuring
 * that the rollback operation does not disrupt the overall workflow execution.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveSchedulingRollBackTask implements Task {
    private final SchedulingService schedulingService;
    @Override
    public State execute(State state) {
        String realmId = state.getValue(REALM_ID_KEY);
        if (ObjectUtils.isEmpty(realmId)) {
            WorkflowLogger.logError(
                    "Rollback Error : Failed to invoke scheduling service as realmId not set");
            return state;
        }
        try {
            SchedulingMetaData schedulingMetaData = state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA);
            if(ObjectUtils.isEmpty(schedulingMetaData) || ObjectUtils.isEmpty(schedulingMetaData.getDefinitionKey())){
                WorkflowLogger.logError(
                        "Rollback Error : Failed to invoke scheduling service as definitionKey not set");
                return state;
            }

            String definitionKey = schedulingMetaData.getDefinitionKey();
            String definitionId = schedulingMetaData.getDefinitionId();

            // try deleting event schedules from scheduling service
            WorkflowLogger.logInfo(
                    "Rollback : Performing delete for event schedule in Scheduling Service for definitionId=%s ",
                    definitionId);
            Optional<List<EventScheduleWorkflowActionModel>> optionalEventScheduleWorkflowActionModels =
                    state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
            List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
                    optionalEventScheduleWorkflowActionModels.orElse(Collections.emptyList());
            if(!ObjectUtils.isEmpty(eventScheduleWorkflowActionModels)){
                List<String> scheduleIds = SchedulingServiceUtil.getScheduleIds(eventScheduleWorkflowActionModels, definitionKey);
                schedulingService.deleteSchedules(scheduleIds, realmId);
                WorkflowLogger.logInfo(
                        "Rollback: Event schedules entries deleted from scheduling service with definitionId=%s",
                        definitionId);
            }else{
                WorkflowLogger.logError(
                        "Rollback Error : Failed to invoke scheduling service as WorkflowActionModels not set for definitionId=%s",definitionId);
            }
            // log and swallow roll back exceptions
        } catch (Exception e) {
            WorkflowLogger.logError(
                    e, "Rollback Error : Failed to delete event schedules entries from scheduling service");
        }
        return state;
    }
}