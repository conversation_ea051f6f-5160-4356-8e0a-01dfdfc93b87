package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * Delete the save tasked in task definition details table
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveTaskDetailRollBackTask implements Task {

  private final ActivityDetailsRepository activityDetailRepository;
  private final boolean enableTaskDefinitionSave;
  
  @Override
  @Transactional
  public State execute(State state) {
   if (!enableTaskDefinitionSave) {
      return state;
    }

    List<ActivityDetail> activityDetails = state.getValue(AsyncTaskConstants.TASKS_DETAILS_KEY);
    if (CollectionUtils.isEmpty(activityDetails)) {
      logError("Rollback Error : Failed to invoke DB rollback as tasks not set");
      return state;
    }

    TemplateDetails template = state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY);
    if (template == null || StringUtils.isEmpty(template.getId())) {
      logError("Rollback Error : Failed to invoke DB rollback as templateId not set");
      return state;
    }

    String templateId = template.getId();
    // try deleting task definition in DB
    try {
      logInfo("Rollback : Performing delete in tasks definition db for templateId=%s", templateId);
      delete(template);
      logInfo("Rollback: task detail entries deleted from datastore with templateId=%s",
          templateId);

      // log and swallow roll back exceptions
    } catch (Exception e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder().stackTrace(e)
          .message("Rollback Error : Failed to delete tasks definitions from db for templateId=%s ",
              templateId)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_TASKS_DEFINITION)
          .className(this.getClass().getName()));
    }

    return state;
  }

  /*
   * Setting fatal to false so that the chain does not break even if the task
   * break
   */
  @Override
  public boolean isFatal() {
    return false;
  }

  
  void delete(TemplateDetails template) {
    activityDetailRepository.deleteByTemplateDetails(template);
  }

  private void logError(String message) {
    WorkflowLogger.error(() -> WorkflowLoggerRequest.builder().message(message)
        .downstreamComponentName(DownstreamComponentName.WAS_DB)
        .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_TASKS_DEFINITION)
        .className(this.getClass().getName()));
  }

  private void logInfo(String message, Object... args) {
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message(message, args)
        .downstreamComponentName(DownstreamComponentName.WAS_DB)
        .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_TASKS_DEFINITION)
        .className(this.getClass().getName()));
  }

}
