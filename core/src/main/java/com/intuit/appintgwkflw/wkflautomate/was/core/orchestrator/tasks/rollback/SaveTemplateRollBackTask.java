package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;


/**
 * Delete the deployed definition in definition details table
 * 
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveTemplateRollBackTask implements Task {

  private final TemplateDetailsRepository templateDetailsRepository;
  private final TriggerDetailsRepository triggerDetailsRepository;

  @Override
  @Transactional
  public State execute(State state) {

    TemplateDetails template = state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY);
    if (template == null) {
      logError("Rollback Error : Failed to invoke DB rollback as template not set");
      return state;
    }

    String templateId = template.getId();
    if (StringUtils.isEmpty(templateId)) {
      logError("Rollback Error : Failed to invoke DB rollback as templateId not set");
      return state;
    }

    // try deleting definition in DB
    try {
      logInfo("Rollback : Performing delete in db for templateId=%s", templateId);
      delete(template);
      logInfo("Rollback: template entries deleted from datastore with templateId=%s", templateId);

      // log and swallow roll back exceptions
    } catch (Exception e) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder().stackTrace(e)
          .message("Rollback Error : Failed to delete definition in db for templateId=%s ",
              templateId)
          .downstreamComponentName(DownstreamComponentName.WAS_DB)
          .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
          .className(this.getClass().getName()));
    }

    return state;
  }

  /* Setting fatal to false so that the chain does not break even if the task break */
  @Override
  public boolean isFatal() {
    return false;
  }

  
  void delete(TemplateDetails template) {
    triggerDetailsRepository.deleteByTemplateDetails(template);
    templateDetailsRepository.deleteById(template.getId());
  }

  private void logError(String message) {
    WorkflowLogger.error(() -> WorkflowLoggerRequest.builder().message(message)
        .downstreamComponentName(DownstreamComponentName.WAS_DB)
        .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
        .className(this.getClass().getName()));
  }

  private void logInfo(String message, Object... args) {
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message(message, args)
        .downstreamComponentName(DownstreamComponentName.WAS_DB)
        .downstreamServiceName(DownstreamServiceName.WAS_DB_DELETE_DEFINITION)
        .className(this.getClass().getName()));
  }

}
