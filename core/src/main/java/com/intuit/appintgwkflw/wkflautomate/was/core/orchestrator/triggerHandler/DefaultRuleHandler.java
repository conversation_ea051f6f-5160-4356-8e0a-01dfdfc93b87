package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.cronutils.utils.VisibleForTesting;
import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SingleDefinitionDmnEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.EvaluationResult;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowEvaluateRuleResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.javatuples.Pair;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.springframework.stereotype.Component;

@Component(WorkflowBeansConstants.V3_RULE_HANDLER)
@AllArgsConstructor
public class DefaultRuleHandler implements RuleEvaluationHandler {

  private DefinitionDetailsRepository definitionDetailsRepository;

  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private V3RunTimeHelper runtimeHelper;

  private WASContextHandler contextHandler;

  private DefinitionServiceHelper definitionServiceHelper;

  private final SingleDefinitionDmnEvaluator singleDefinitionDmnEvaluator;


  @Override
  public String getName() {
    return DefaultRuleHandler.class.getSimpleName();
  }

  /**
   * Evaluate the rules for given entity, payload and return the evaluation result
   *
   * @param evaluateRulesMessage: contains entity payload map
   * @return rule evaluation result
   */
  @Override
  public WorkflowGenericResponse evaluateRules(final Map<String, Object> evaluateRulesMessage) {
    final WorkflowGenericResponseBuilder response =
        runtimeHelper.getDefaultResponseBuilder(new WorkflowEvaluateRuleResponse());
    final Authorization authorization = new Authorization(
        contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("evaluateRules")
                .message("Begin rule evaluation")
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EVALUATE_RULES));

    // 1) Process and get record type from V3 payload
    final TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(evaluateRulesMessage, contextHandler);

    // 2) Check and get if companyId and record type has enabled definition
    // isDefinitionDataRequired = true is required because rule evaluation gets dmn xml from
    // Definition Data
    final List<DefinitionDetails> definitionDetailsList =
        runtimeHelper.getEligibleDefinitions(transactionEntity, true);
    /*
      It returns the enabled definitions whose internal status as null or stale_definition(older definition which is updated)
      We need to filter out the latest definition i.e. internal status Null,
       otherwise it will evaluate on all the definitions but we need to evaluate on the latest definition only
     */
    List<DefinitionDetails> enabledDefinitionList =
        definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
            definitionDetailsList, authorization.getRealm());
    //Get the definitions which are not stale i.e. internal status as null
    enabledDefinitionList = enabledDefinitionList.stream().filter(definitionDetails ->
        Objects.isNull(definitionDetails.getInternalStatus())).collect(Collectors.toList());
    //If no enabled definition, exit
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(enabledDefinitionList), WorkflowError.ENABLED_DEFINITION_NOT_FOUND);

    // ToDo: Assumption-> only one workflow definition allowed. Revisit when allowing multiple
    // definitions
    final TemplateDetails bpmnTemplateDetails =
        TriggerUtil.getBpmnTemplateDetails(enabledDefinitionList);
    WorkflowVerfiy.verifyNull(bpmnTemplateDetails, WorkflowError.TEMPLATE_DOES_NOT_EXIST);
    Pair<String, byte[]> dmnTemplateDetailsForDefinition = new Pair<>(WorkflowConstants.BLANK, new byte[0]);

    // get the dmn template details
    Map<String, Object> variablesForDMN = new HashMap<>();
    // We go through each of the definitions and get the conditional variables for each
    // of the definitions and append them to the final list that will do the DMN evaluation
    for (DefinitionDetails definitionDetails : enabledDefinitionList) {
      List<DefinitionDetails> definitionDetail = new ArrayList<>();
      definitionDetail.add(definitionDetails);
      dmnTemplateDetailsForDefinition =
          runtimeHelper.getDmnTemplateDetails(transactionEntity, definitionDetail);

      byte[] dmnDefinitionDataForDefinition = dmnTemplateDetailsForDefinition.getValue1();

      // Get variables needed to evaluate each DMN
      final DmnModelInstance dmnModelInstance =
          BpmnProcessorUtil.readDmn(dmnDefinitionDataForDefinition);
      final Collection<DecisionTable> decisionTables =
          dmnModelInstance.getModelElementsByType(DecisionTable.class);
      WorkflowVerfiy.verify(CollectionUtils.isEmpty(decisionTables), WorkflowError.INVALID_INPUT);
      Map<String, Object> currentVariable =
          (HashMap<String, Object>)
              getVariablesToEvaluateRules(transactionEntity, decisionTables).get(WorkflowConstants.BPMN_DMN_VARIABLES);
      ;
      variablesForDMN.putAll(currentVariable);
    }
    ;
    String templateNameDMN = dmnTemplateDetailsForDefinition.getValue0();
    final Map<String, Object> variablesToEvaluateRules =
        new HashMap<>() {
          {
            put(WorkflowConstants.BPMN_DMN_VARIABLES, variablesForDMN);
          }
        };

    final List<String> parentDefIdList =
        enabledDefinitionList.stream()
            .map(DefinitionDetails::getDefinitionId)
            .distinct()
            .collect(Collectors.toList());

    final Optional<List<DefinitionDetails>> optionalDmnDefinitionDetailsList;
    if (!bpmnTemplateDetails.getAllowMultipleDefinitions()) {
      // Get the latest DMN id for evaluation and wrapping it in the list to avoid change in the
      // contract
      optionalDmnDefinitionDetailsList =
          definitionDetailsRepository
              .findByOwnerIdAndModelTypeAndStatusAndParentIdInOrderByCreatedDateDesc(
                  Long.parseLong(authorization.getRealm()),
                  ModelType.DMN,
                  Status.ENABLED,
                  Collections.singletonList(parentDefIdList.stream().findFirst().get()));
    } else {
      // Get list of DMN id  for evaluation
      optionalDmnDefinitionDetailsList =
          definitionDetailsRepository.findByOwnerIdAndModelTypeAndStatusAndParentIdInOrderByCreatedDateDesc(
              Long.parseLong(authorization.getRealm()),
              ModelType.DMN,
              Status.ENABLED,
              parentDefIdList);
    }

    if (optionalDmnDefinitionDetailsList.isPresent()) {
      // Async rule evaluation of multiple DMNs
      final Map<String, Object> responseEntityMap =
          executeAsyncRuleEvaluation(
              variablesToEvaluateRules, optionalDmnDefinitionDetailsList.get());
      // process input Variables of DMN
      final Map<String, Object> dmnVariables =
          ObjectConverter.convertObject(
              variablesToEvaluateRules.get(WorkflowConstants.BPMN_DMN_VARIABLES),
              new TypeReference<Map<String, Object>>() {
              });
      // process response
      final EvaluationResult evaluationResult =
          processAsyncRuleEvaluationResponse(
              response,
              templateNameDMN,
              responseEntityMap,
              dmnVariables.keySet().stream().collect(Collectors.toList()));
      response.response(new WorkflowEvaluateRuleResponse(evaluationResult));
    }

    final RecordType recordType = transactionEntity.getEntityType();
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("evaluateRules")
                .message(
                    "End rule evaluation for recordType=%s recordId=%s status=SUCCESS",
                    recordType.toString(), transactionEntity.getEntityId())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EVALUATE_RULES));
    return response.build();
  }

  /**
   * Process response of all evaluated DMN
   *
   * @param response             final response object to be sent
   * @param templateNameDMN template details required to append rule name in the final
   *                             response
   * @param responseEntityMap    map of response entity from async evaluation of DMN rules
   * @param parameterList        : List of Column Names of DMN
   */
  private EvaluationResult processAsyncRuleEvaluationResponse(
      final WorkflowGenericResponseBuilder response,
      final String templateNameDMN,
      final Map<String, Object> responseEntityMap,
      final List<String> parameterList) {

    response.status(ResponseStatus.SUCCESS);
    final List<Map<String, Object>> resultArr = new ArrayList<>();
    final EvaluationResult evaluationResult =
        new EvaluationResult(templateNameDMN, resultArr, parameterList);
    responseEntityMap.forEach(
        (id, details) -> {
          final Map<String, Object> singleDmnResult = new HashMap<>();
          singleDmnResult.put(WorkflowConstants.ID, id);
          singleDmnResult.put(WorkflowConstants.DETAILS, details);
          resultArr.add(singleDmnResult);
        });
    return evaluationResult;
  }

  /**
   * Send rule evaluation request to camunda  in async manner
   *
   * @param variablesToEvaluateRules input parameters configured in DMN for rule evaluation
   * @param definitionDetailsList    DMN definition list to be evaluated
   */
  private Map<String, Object> executeAsyncRuleEvaluation(
      final Map<String, Object> variablesToEvaluateRules,
      final List<DefinitionDetails> definitionDetailsList) {

    final State requestState = new State();
    final List<RuleEvaluationTask> ruleEvaluationTaskList = new ArrayList<>();

    definitionDetailsList.forEach(definitionDetails -> {
      final String defId = definitionDetails.getDefinitionId();
      final EvaluateRuleRequest evaluateDecisionDmn = new EvaluateRuleRequest(defId,
          variablesToEvaluateRules);
      requestState.addValue(defId, evaluateDecisionDmn);
      final RuleEvaluationTask ruleEvaluationTask = new RuleEvaluationTask(defId, defId,
          bpmnEngineRunTimeServiceRest, definitionDetails, singleDefinitionDmnEvaluator);
      ruleEvaluationTaskList.add(ruleEvaluationTask);
    });
    final State resp = new RxExecutionChain(requestState,
        ruleEvaluationTaskList.toArray(new RuleEvaluationTask[ruleEvaluationTaskList.size()]))
        .execute();
    return resp.getAll();
  }

  /** Process DMN to find the variables configured for rule evaluation */
  @SuppressWarnings("unchecked")
  @VisibleForTesting
  Map<String, Object> getVariablesToEvaluateRules(
      final TransactionEntity v3TransactionEntity, final Collection<DecisionTable> decisionTables) {

    final Map<String, Object> entityObjMap = runtimeHelper.getEntityObjectMap(v3TransactionEntity);
    WorkflowVerfiy.verifyNull(entityObjMap, WorkflowError.INVALID_INPUT);
    final Map<String, Object> varValueMap = new HashMap<>();
    final Map<String, Object> variables = new HashMap<>();

    for (final DecisionTable decisionTable : decisionTables) {
      decisionTable
          .getInputs()
          .forEach(
              input -> {
                final String typeRef =
                    input
                        .getInputExpression()
                        .getAttributeValue(WorkflowConstants.DMN_INPUT_TYPE_REF);
                String variableType = StringUtils.capitalize(typeRef);

                /**
                 * fetch the transformer detail if available and find what is the original variable
                 * type this custom data type is mapped to
                 */
                DMNDataTypeTransformer dataTypeTransformer =
                    DMNDataTypeTransformers.getTransformer(DMNSupportedOperator.value(typeRef));

                if (Objects.nonNull(dataTypeTransformer) && !dataTypeTransformer.getName()
                    .equals(DMNSupportedOperator.DEFAULT)) {
                  variableType = dataTypeTransformer.getDataType();
                }
                String dmnInputStr =
                    input
                        .getInputExpression()
                        .getText()
                        .getRawTextContent()
                        .replace(WorkflowConstants.DMN_VAR_OPEN_BRACE, StringUtils.EMPTY);
                dmnInputStr =
                    dmnInputStr.replace(WorkflowConstants.DMN_VAR_CLOSE_BRACE, StringUtils.EMPTY);
                Map<String, Object> subValueMap = null;
                try {
                  subValueMap =
                      runtimeHelper.extractVariablesFromEntity(
                          entityObjMap, dmnInputStr, variableType, true);
                } catch (final Exception e) {
                  throw new WorkflowGeneralException(
                      WorkflowError.RULE_EVALUATION_PAYLOAD_PROCESSING_ERROR, e);
                }
                // This check makes sure that empty quotes ("") are converted to NULL explicitly since
                // empty quotes may raise exception in rule evaluation for non-string datatypes
                if (!(WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(variableType))
                    && StringUtils.isBlank(
                    Objects.toString(subValueMap.get("value"), StringUtils.EMPTY))) {
                  subValueMap.replace("value", null);
                }
                subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, variableType);
                varValueMap.put(dmnInputStr, subValueMap);
              });
    }
    variables.put(WorkflowConstants.BPMN_DMN_VARIABLES, varValueMap);
    return variables;
  }
}
