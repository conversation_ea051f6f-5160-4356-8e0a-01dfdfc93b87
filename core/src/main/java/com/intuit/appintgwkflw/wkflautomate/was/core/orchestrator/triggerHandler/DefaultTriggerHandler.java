package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ActivityDetailsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.Authorization;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component(WorkflowBeansConstants.V3_TRIGGER_HANDLER)
@AllArgsConstructor
public class DefaultTriggerHandler extends BaseTriggerHandler implements TriggerHandler {

  private ProcessDetailsRepository processDetailsRepository;

  private WASContextHandler contextHandler;

  private V3StartProcess startProcess;

  private V3SignalProcess signalProcess;

  @Override
  public String getName() {
    return DefaultTriggerHandler.class.getSimpleName();
  }

  @Override
  @Trace
  public WorkflowGenericResponse executeTrigger(final TriggerProcessDetails triggerDetails,
                                                Optional<ProcessDetails> processDetails) {
    return executeTrigger(triggerDetails);

  }

  @Override
  @Trace
  public WorkflowGenericResponse executeTrigger(final TriggerProcessDetails triggerDetails) {

    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));

    // 1) Process and get record type from V3 payload
    final TransactionEntity transactionEntity =
        getTransactionEntity(triggerDetails.getTriggerMessage());
    // 2) Check for businessKey
    verifyBusinessKeyNotUsed(transactionEntity);
    /**
     * 3) If the eventHeader contains ProviderWorkflowId, get the corresponding definition stored in
     * the WAS db. Else, check and get if companyId and record type has enabled definition.
     * isDefinitionDataRequired is false here as we don't need definition data of all the versions.
     */
     List<DefinitionDetails> definitionDetailsList =
        runtimeHelper.getEligibleDefinitions(transactionEntity, false);

    definitionDetailsList = pruneIneligibleDefinitions(transactionEntity, definitionDetailsList);

    /*
     * 3) Get processes running for the specific record/entity.
     *
     * <p>Since there will be only one definition enabled for a company it will either return active
     * process or error process.So if process was marked error due to any failure from Downstream,
     * on retry it can be resumed.
     */
    final Optional<List<ProcessDetails>> processDetailsOptionalList =
        fetchProcessDetails(authorization, transactionEntity, definitionDetailsList);

    ActivityDetail activityDetail = runtimeHelper.fetchInitialStartEventActivityDetail(transactionEntity);
    definitionDetailsList = filterDefsOnChangeType(activityDetail, definitionDetailsList, transactionEntity);
    Map<String, Object> initialStartEventExtensionPropertiesMap = ActivityDetailsUtil.getExtensionProperties(activityDetail);

    // 4)  Start new process if no existing running process found
    if (!processDetailsOptionalList.isPresent() || processDetailsOptionalList.get().isEmpty()) {
      List<DefinitionDetails> latestEnabledDefinitions =
          definitionDetailsList.stream()
              .filter(
                  definitionDetails -> Objects.isNull(definitionDetails.getInternalStatus()))
              .collect(Collectors.toList());
      return startNewProcess(transactionEntity, latestEnabledDefinitions, initialStartEventExtensionPropertiesMap,
          authorization);
    } else { // 5) Signal the waiting process
      return signalRunningProcess(
          transactionEntity, definitionDetailsList, processDetailsOptionalList.get(), initialStartEventExtensionPropertiesMap);
    }
  }

  private TransactionEntity getTransactionEntity(final Map<String, Object> triggerMessage) {

    final TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(triggerMessage, contextHandler);
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("executeTrigger")
                .message(
                    "Begin processing trigger message for recordType=%s recordId=%s entityChangeType=%s workflowType=%s",
                    transactionEntity.getEntityType().toString(), transactionEntity.getEntityId(),
                        transactionEntity.getEntityChangeType(), transactionEntity.getWorkflowType())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS));
    return transactionEntity;
  }

  private Optional<List<ProcessDetails>> fetchProcessDetails(final Authorization authorization,
      final TransactionEntity transactionEntity, final List<DefinitionDetails> definitionDetailsList) {

    return processDetailsRepository
        .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
            transactionEntity.getEntityId(),
            Long.parseLong(authorization.getRealm()),
            Arrays.asList(ACTIVE, ERROR),
            definitionDetailsList);
  }

  private WorkflowGenericResponse startNewProcess(final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList,
      final Map<String, Object> initialStartEventExtensionPropertiesMap, final Authorization authorization) {

    // ToDo: revisit when multiple process start are allowed. Get definition Id (assuming single
    // definition per template and first one picked)
    final DefinitionDetails definitionDetails = definitionDetailsList.stream().findFirst().get();
    final Map<String, Object> startProcessResult =
        startProcess.startProcess(transactionEntity, definitionDetails, initialStartEventExtensionPropertiesMap);

    // 6) update processDetails table
    if (ObjectUtils.allNotNull(startProcessResult.get(WorkflowConstants.ID))) {
      runtimeHelper.saveProcessDetailsInstance(transactionEntity.getEntityId(),
          Long.parseLong(authorization.getRealm()),
          (String) startProcessResult.get(WorkflowConstants.ID),
          ProcessStatus.ACTIVE, definitionDetails,
          null, runtimeHelper.getEntityObjectMap(transactionEntity)
      );
    }
    return runtimeHelper.getTriggerResponse(ResponseStatus.SUCCESS,
        Objects.isNull(startProcessResult.get(WorkflowConstants.ID))
            ? TriggerStatus.NO_ACTION
            : TriggerStatus.PROCESS_STARTED,
        (String) startProcessResult.get(WorkflowConstants.ID));
  }

  private WorkflowGenericResponse signalRunningProcess(final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList,
      final List<ProcessDetails> processDetailsList, final Map<String, Object> initialStartEventExtensionPropertiesMap) {

    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));

    final WorkflowGenericResponseBuilder response =
        runtimeHelper.getDefaultResponseBuilder(WorkflowTriggerResponse.builder().build());
    if (processDetailsList.size() > WorkflowConstants.MAX_PROCESS_PER_ENTITY_WORKFLOW) {
      // ToDo: This should not happen. Check with camunda the status of each process and signal the
      // right one, end others.
      WorkflowLogger.warn(() ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("executeTrigger")
                  .message(
                      "Multiple process found for recordId=%s", transactionEntity.getEntityId())
                  .downstreamComponentName(DownstreamComponentName.WAS_DB));
    }
    else {
      runtimeHelper.mergeCalledProcesses(processDetailsList, definitionDetailsList);

      try {

        WorkflowTriggerResponse signalResponse = runtimeHelper.mergeTriggerResponse(
            processDetailsList.stream().map(
                processDetail ->
                    signalProcess(
                        transactionEntity, definitionDetailsList, processDetail, initialStartEventExtensionPropertiesMap, authorization)
            ).collect(Collectors.toList()),
            processDetailsList
        ).stream().findFirst().get();

        return runtimeHelper.getTriggerResponse(ResponseStatus.SUCCESS,
            signalResponse.getStatus(), signalResponse.getProcessId()
        );
      }
      catch (final WorkflowGeneralException ex) {
        /**
         * in case co-relate message API fails, exception is caught, the process & it's called processes
         * are marked error only if blockProcessOnSignalMiss is enabled and error is re-thrown.
         */
        if (transactionEntity.getEventHeaders().isBlockProcessOnSignalFailure()) {
          WorkflowLogger.warn(
              () ->
                  WorkflowLoggerRequest.builder()
                      .className(this.getClass().getSimpleName())
                      .methodName("updateProcessStatus")
                      .message(
                          "Marking process in Error state for recordId=%s",
                          transactionEntity.getEntityId())
                      .downstreamComponentName(DownstreamComponentName.WAS_DB));

          processDetailsList.forEach(
              processDetail -> runtimeHelper.markProcessError(transactionEntity, processDetail)
          );
        }
        throw ex;
      }
    }
    return response.build();
  }

  private WorkflowTriggerResponse signalProcess(final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList, final ProcessDetails processDetail,
      final Map<String, Object> initialStartEventExtensionPropertiesMap, final Authorization authorization) {

    WorkflowVerfiy.verify(Objects.isNull(processDetail), PROCESS_NOT_FOUND);

    final boolean signalProcessSuccess = signalProcess.signalProcess(
            transactionEntity, definitionDetailsList, processDetail, initialStartEventExtensionPropertiesMap);

    String definitionId = processDetail.getDefinitionDetails().getDefinitionId();

    WorkflowVerfiy.verify(definitionId, DEFINITION_NOT_FOUND);

    DefinitionDetails definitionDetails = definitionDetailsList.stream()
            .filter(def -> definitionId.equals(def.getDefinitionId()))
            .findFirst()
            .orElseThrow((() ->
                    new WorkflowGeneralException(
                        WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
                        authorization.getRealm(),
                        transactionEntity.getEntityType())));

    return runtimeHelper.getWorkflowTriggerResponse(
        signalProcessSuccess ? TriggerStatus.PROCESS_SIGNALLED : TriggerStatus.NO_ACTION,
        processDetail.getProcessId(),
        definitionDetails.getDefinitionId(), definitionDetails.getDefinitionName());
  }

}
