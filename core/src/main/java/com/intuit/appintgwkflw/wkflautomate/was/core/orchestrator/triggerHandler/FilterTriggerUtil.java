package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DOMAIN_EVENT_TRIGGER;
import static com.intuit.identity.authn.offline.sdk.utils.StringUtils.isNotBlank;

import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.identity.authn.offline.sdk.utils.StringUtils;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * 
 *         This class checks if for a given workflow trigger needs to be
 *         processed or skipped based on the FF.
 *
 */
@Component
@AllArgsConstructor
public class FilterTriggerUtil {

	private static final String DOMAIN_EVENT = "DOMAIN_EVENT";

	private static final String WEBHOOK = "WEBHOOK";

	private final WASContextHandler contextHandler;

	private final FeatureFlagManager featureFlagManager;

	private final DomainEventConfig domainEventConfig;

	/**
	 * @param transactionEntity input trigger details
	 * @return true if trigger needs to be dropped or processed.
	 */
	public boolean filterNotificationWorkflow(TransactionEntity transactionEntity) {

		// If flag not enabled do nothing
		if (null == domainEventConfig.getConsumer()
				|| !domainEventConfig.getConsumer().isProcessNotificationWorkflow()) {
			return false;
		}
		/**
		 * Fetch source. If provider workflow id is present : source is app-connect
		 * <p>
		 * else-If definition key is present : source is domain event
		 */
		String source = StringUtils.isNotBlank(transactionEntity.getEventHeaders().getProviderWorkflowId()) ? WEBHOOK
				: StringUtils.isNotBlank(transactionEntity.getEventHeaders().getDefinitionKey()) ? DOMAIN_EVENT : null;

		// If source is not identified do not filter the request.
		if (StringUtils.isBlank(source)) {
			return false;
		}

		/**
		 * Fetches CustomWorkflowType from workflow name.
		 * <p>
		 * workflow -> customNotification CustomWorkflowType -> Notification.
		 * <p>
		 * workflow -> customReminder CustomWorkflowType -> Reminder.
		 * <p>
		 * workflow -> notification CustomWorkflowType -> Notification.
		 */
		CustomWorkflowType customWorkflowType = CustomWorkflowType
				.get(transactionEntity.getEventHeaders().getWorkflow());

		/**
		 * Workflow = Project(RecordType) + customNotification
		 */
		String workFlow = null != customWorkflowType
				? String.join("", transactionEntity.getEventHeaders().getEntityType().getRecordType(),
						customWorkflowType.getTemplateName())
				: null;


		if (isNotBlank(workFlow) && customNotification(customWorkflowType)) {

			// should return false to proceed to trigger workflows
			if((DOMAIN_EVENT.equals(source) && domainEventConfig.getConsumer().getCompletedEntities().contains(workFlow.toLowerCase()))){
				return false;
			}

			/**
			 * if trigger is not for custom notification or workflow is not found, do
			 * nothing.
			 */
			boolean eventingTriggerEnabled = featureFlagManager.getBoolean(DOMAIN_EVENT_TRIGGER, false,
					workFlow.toLowerCase(), Long.valueOf(contextHandler.get(WASContextEnums.OWNER_ID)));

			if ((WEBHOOK.equals(source) && eventingTriggerEnabled)
					|| (DOMAIN_EVENT.equals(source) && !eventingTriggerEnabled)) {
				return true;
			}
		}
		return false;

	}

	/**
	 * @param customWorkflowType custom workflow type
	 * @return true if it is custom notification
	 */
	private boolean customNotification(CustomWorkflowType customWorkflowType) {
		return CustomWorkflowType.NOTIFICATION == customWorkflowType;
	}
}
