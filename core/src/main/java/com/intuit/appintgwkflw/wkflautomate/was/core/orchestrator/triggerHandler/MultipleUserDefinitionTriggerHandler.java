package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.info;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_START_EVENTS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants.MULTIPLE_USER_DEFINITION_TRIGGER_HANDLER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.MAX_PROCESS_PER_ENTITY_WORKFLOW;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName.WAS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName.WAS_USER_DEFINITION_TRIGGER_HANDLER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus.SUCCESS;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.util.BpmnOverwatchUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ActivityDetailsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.Scope;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SignalUserDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartUserDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;

import lombok.AllArgsConstructor;

@Component(MULTIPLE_USER_DEFINITION_TRIGGER_HANDLER)
@AllArgsConstructor
public class MultipleUserDefinitionTriggerHandler extends BaseTriggerHandler
    implements TriggerHandler {

  private ProcessDetailsRepository processDetailsRepository;

  private WASContextHandler contextHandler;

  private V3StartProcess startProcess;

  private V3SignalProcess signalProcess;

  private DefinitionServiceHelper definitionServiceHelper;

  private final MetricLogger metricLogger;

  private FilterTriggerUtil filterTriggerUtil;

  private TemplateDetailsRepository templateDetailsRepository;

  @Override
  public String getName() {

    return DefinitionType.USER.name();
  }

  /**
   * It starts the process or signals the process for a given record id.
   *
   * <p>Start Process: Fetches all the enabled definition by owner id,record id,template name and
   * start the process in parallel for all the definitions that are found.
   *
   * <p>Signal Process: Fetches all the active,error processes by owner id,record id,template name
   * and will signal all the processes in parallel if there is no process in error state.
   *
   * <p>If any of the process found is in the error state it will assume it as a retry and will only
   * signal error processes.
   */
  @Override
  @Trace
  public WorkflowGenericResponse executeTrigger(final TriggerProcessDetails triggerDetails) {


    final WorkflowGenericResponseBuilder response =
        runtimeHelper.getDefaultResponseBuilder(WorkflowTriggerResponse.builder().build());

    final String ownerId = contextHandler.get(WASContextEnums.OWNER_ID);

    // 1) Process and get record type from V3 payload
    final TransactionEntity transactionEntity =
        getTransactionEntity(triggerDetails.getTriggerMessage());
    
    // skip web-hook trigger for NF workflow if FF is enabled
    if(filterTriggerUtil.filterNotificationWorkflow(transactionEntity)) {
        return response
                .status(SUCCESS)
                .response(WorkflowTriggerResponse.builder().status(TriggerStatus.NO_ACTION).build())
                .build();
    }
    
    // 2) Check for businessKey
    verifyBusinessKeyNotUsed(transactionEntity);

    // 3) Check and get if companyId  and record type has enabled definition
    final List<DefinitionDetails> definitionDetailsList =
        runtimeHelper.getEligibleDefinitions(transactionEntity, false);
    WorkflowLogger.logInfo("Total Eligible Definitions=%s", definitionDetailsList.size());
    List<DefinitionDetails> enabledDefinitionList =
        definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
            definitionDetailsList, ownerId);
    WorkflowLogger.logInfo("Total Enabled Definitions=%s", enabledDefinitionList.size());
    enabledDefinitionList = pruneIneligibleDefinitions(transactionEntity, enabledDefinitionList);
    if (CollectionUtils.isEmpty(enabledDefinitionList)) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_EVALUATE_RULES)
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .message(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage()));
      return response.build();
    }

    // 3) Get processes running for the specific record/entity
    final Optional<List<ProcessDetails>> processDetailsOptionalList = fetchProcessDetails(
        ownerId, transactionEntity, enabledDefinitionList);

    ActivityDetail activityDetail = runtimeHelper.fetchInitialStartEventActivityDetail(transactionEntity);
    enabledDefinitionList = filterDefsOnChangeType(activityDetail, enabledDefinitionList, transactionEntity);
    Map<String, Object> initialStartEventExtensionPropertiesMap = ActivityDetailsUtil.getExtensionProperties(activityDetail);

    // Logs for multiple process definition use case
    if ((!isEmpty(enabledDefinitionList)
        && enabledDefinitionList.size() > MAX_PROCESS_PER_ENTITY_WORKFLOW)
        || (processDetailsOptionalList.isPresent()
        && processDetailsOptionalList.get().size() > MAX_PROCESS_PER_ENTITY_WORKFLOW)) {
      info(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("executeTrigger")
                  .message(
                      "Multiple process/definition found for recordId=%s",
                      transactionEntity.getEntityId())
                  .downstreamComponentName(WAS)
                  .downstreamServiceName(WAS_USER_DEFINITION_TRIGGER_HANDLER));
    }

    // 4)  Start new process if no existing running process found
    if (!processDetailsOptionalList.isPresent() || processDetailsOptionalList.get().isEmpty()) {
      /*
       Start the process with only latest definitions i.e. excluding stale definitions
      */
      List<DefinitionDetails> latestDefinitions =
          enabledDefinitionList.stream()
              .filter(
                  definitionDetails -> Objects.equals(null, definitionDetails.getInternalStatus()))
              .collect(Collectors.toList());
      if (CollectionUtils.isEmpty(latestDefinitions)) {
        //No enabled definition, return failure
        return response.build();
      }
      response
          .status(SUCCESS)
          .response(
              populateStartProcessResponse(
                  latestDefinitions,
                  startMultipleProcess(transactionEntity, latestDefinitions, initialStartEventExtensionPropertiesMap)));

    }
    // 6 Signal the waiting process
    else {
      List<ProcessDetails> mergedProcessDetails = new ArrayList<>(processDetailsOptionalList.get());

      // filter out parent processes which are started on the same date as today
      runtimeHelper.filterProcessesStartedTodayForRecurringWorkflows(mergedProcessDetails, transactionEntity);

      runtimeHelper.mergeCalledProcesses(mergedProcessDetails, enabledDefinitionList);

      response
          .status(SUCCESS)
          .response(
              populateTriggerProcessResponse(
                  mergedProcessDetails,
                  signalMultipleProcess(
                      transactionEntity,
                      enabledDefinitionList,
                      initialStartEventExtensionPropertiesMap,
                      mergedProcessDetails)));
    }
    return response.build();
  }

  @Override
  public WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails, Optional<ProcessDetails> processDetails) {
    return executeTrigger(triggerDetails);
  }

  private TransactionEntity getTransactionEntity(final Map<String, Object> triggerMessage) {

    final TransactionEntity transactionEntity = TransactionEntityFactory.getInstanceOf(triggerMessage, contextHandler);
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("executeTrigger")
                .message(
                    "Begin processing trigger message for recordType=%s recordId=%s entityChangeType=%s workflowType=%s",
                    transactionEntity.getEntityType().name(), transactionEntity.getEntityId(),
                    transactionEntity.getEntityChangeType(), transactionEntity.getWorkflowType())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS));
    return transactionEntity;
  }

  /**
   * find all the process in error and active state.If error process are not present will return all
   * the active processes else will return only the processes in error state.
   *
   * @param ownerId         input authorization details
   * @param transactionEntity     input transaction details
   * @param definitionDetailsList enabled definition details list
   * @return list of processes
   */
  private Optional<List<ProcessDetails>> fetchProcessDetails(
      final String ownerId,
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList) {

    final Optional<List<ProcessDetails>> processDetails =
        processDetailsRepository
            .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                transactionEntity.getEntityId(),
                Long.parseLong(ownerId),
                Arrays.asList(ACTIVE, ERROR),
                definitionDetailsList);

    // if no process details found return the empty result
    if (!processDetails.isPresent() || processDetails.get().isEmpty()) {
      return processDetails;
    }

    // filter processes in error state
    final List<ProcessDetails> processErrorDetails =
        processDetails
            .get()
            .stream()
            .filter(processDetail -> ERROR == processDetail.getProcessStatus())
            .collect(Collectors.toList());

    // if there any any processes in error state that mean it is a retry so only signal process in
    // error state
    if (!processErrorDetails.isEmpty()) {
      return Optional.of(processErrorDetails);
    }
    return processDetails;
  }

  /**
   * executes signaling of processes in parallel by crating parallel tasks to execute.
   *
   * @param transactionEntity     transaction details
   * @param definitionDetailsList enabled definition details
   * @param initialStartEventExtensionPropertiesMap  start event extension properties details
   * @param processDetailsList    process details
   * @return the signal response for all processes
   */
  private State signalMultipleProcess(
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList,
      final Map<String, Object> initialStartEventExtensionPropertiesMap,
      final List<ProcessDetails> processDetailsList) {

    final RxExecutionChain rxExecutionChain =
        new RxExecutionChain(prepareStartProcessStateRequest(transactionEntity, initialStartEventExtensionPropertiesMap));

    final List<Task> signalProcessTasks =
        prepareSignalMultipleProcessTasks(processDetailsList, definitionDetailsList);

    return rxExecutionChain
        .next(signalProcessTasks.toArray(new Task[signalProcessTasks.size()]))
        .execute();
  }

  /**
   * executes starting of processes in parallel by crating parallel tasks to execute.
   *
   * @param transactionEntity     transaction details
   * @param definitionDetailsList enabled definition details
   * @param initialStartEventExtensionPropertiesMap  start event extension properties details
   * @return the start process response for all processes
   */
  private State startMultipleProcess(
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList,
      final Map<String, Object> initialStartEventExtensionPropertiesMap) {

    final RxExecutionChain rxExecutionChain =
        new RxExecutionChain(prepareStartProcessStateRequest(transactionEntity, initialStartEventExtensionPropertiesMap));

    final List<Task> startProcessTask = prepareStartMultipleProcessTasks(transactionEntity, definitionDetailsList);

    return rxExecutionChain
        .next(startProcessTask.toArray(new Task[startProcessTask.size()]))
        .execute();
  }

  /**
   * prepare the state request for Async execution.
   *
   * @param transactionEntity transaction details
   * @param initialStartEventExtensionPropertiesMap  start event extension properties details
   * @return input state request
   */
  private State prepareStartProcessStateRequest(
      final TransactionEntity transactionEntity, final Map<String, Object> initialStartEventExtensionPropertiesMap) {

    final State inputReq = new State();
    inputReq.addValue(BPMN_START_EVENTS, initialStartEventExtensionPropertiesMap);
    inputReq.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);
    return inputReq;
  }

  /**
   * @param definitionDetailsList enabled definition details
   * @param chainResponse         start process response
   * @return {@link WorkflowTriggersResponse}
   */
  private WorkflowTriggersResponse populateStartProcessResponse(
      final List<DefinitionDetails> definitionDetailsList, final State chainResponse) {

    final List<WorkflowTriggerResponse> triggerResponseList =
        definitionDetailsList
            .stream()
            .map(defnDetails -> (WorkflowTriggerResponse)
                chainResponse.getValue(defnDetails.getDefinitionId()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    return new WorkflowTriggersResponse(triggerResponseList);
  }

  /**
   * @param definitionDetailsList enabled definition details
   * @return list of start process tasks
   */
  private List<Task> prepareStartMultipleProcessTasks(
      final TransactionEntity transactionEntity, final List<DefinitionDetails> definitionDetailsList) {
    List<Task> taskList = new ArrayList<>();
    for(DefinitionDetails definitionDetails: definitionDetailsList) {
      /**
       * Replace Custom Template with Custom Test Template.
       * This is done for Overwatch Test.
       * Template is sample, only Camunda topic are modified.
       */
      if(ObjectUtils.isNotEmpty(transactionEntity.getEventHeaders().getScope()) && Scope.TEST.equals(transactionEntity.getEventHeaders().getScope())) {
        templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(definitionDetails.getTemplateDetails().getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX)
                .ifPresent(templateDetails -> definitionDetails.getTemplateDetails().setDeployedDefinitionId(templateDetails.getDeployedDefinitionId()));
      }
      taskList.add(new StartUserDefinitionTask(startProcess, contextHandler, runtimeHelper, definitionDetails,
              metricLogger));
    }
    return taskList;
  }

  /**
   * @param definitionDetailsList enabled definition details
   * @return list of signal process task
   */
  private List<Task> prepareSignalMultipleProcessTasks(
      final List<ProcessDetails> processDetailsList,
      final List<DefinitionDetails> definitionDetailsList) {

    return processDetailsList
        .stream()
        .map(
            process -> {
              final Optional<DefinitionDetails> defDetailsOptional =
                  definitionDetailsList
                      .stream()
                      .filter(
                          def ->
                              process
                                  .getDefinitionDetails()
                                  .getDefinitionId()
                                  .equals(def.getDefinitionId()))
                      .findFirst();

              return new SignalUserDefinitionTask(
                  defDetailsOptional, process, processDetailsRepository, signalProcess,runtimeHelper);
            })
        .collect(Collectors.toList());
  }

  /**
   * @param processDetails list of process details
   * @param chainResponse  details of all trigger process response
   * @return {@link WorkflowTriggersResponse}
   */
  private WorkflowTriggersResponse populateTriggerProcessResponse(
      final List<ProcessDetails> processDetails, final State chainResponse) {

    final List<WorkflowTriggerResponse> triggerResponseList =
        processDetails
            .stream()
            .map(processDetail -> (WorkflowTriggerResponse)
                chainResponse.getValue(processDetail.getProcessId()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    // if processDetails isMultiConditionWorkflow then merge the trigger response
    boolean isMultiConditionWorkflow = processDetails.stream()
        .anyMatch(processDetail -> MultiStepUtil.isMultiConditionWorkflow(processDetail.getDefinitionDetails()));

    return new WorkflowTriggersResponse(
        isMultiConditionWorkflow
            ? runtimeHelper.mergeTriggerResponse(triggerResponseList, processDetails)
            : triggerResponseList);
  }
}
