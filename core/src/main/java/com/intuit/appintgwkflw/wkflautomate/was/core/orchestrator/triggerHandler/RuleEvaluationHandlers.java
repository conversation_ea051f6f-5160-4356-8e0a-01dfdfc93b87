package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import java.util.HashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <p>Class acts as factory to return Rule Handlers
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RuleEvaluationHandlers {

  private static final Map<String, RuleEvaluationHandler> RULE_EVALUATION_HANDLER_MAP = new HashMap<>();

  /**
   * Adds a handler.
   *
   * @param appId   the app id
   * @param handler the handler
   */
  public static void addHandler(String appId, RuleEvaluationHandler handler) {

    RULE_EVALUATION_HANDLER_MAP.put(appId, handler);
  }

  /**
   * Gets handler.
   *
   * @param appId caller appId
   * @return rule handler impl
   */
  public static RuleEvaluationHandler getHandler(String appId) {

    return RULE_EVALUATION_HANDLER_MAP.get(appId);
  }

  /**
   * Contains boolean.
   *
   * @param appId caller appId
   * @return true /false if handler is present or not
   */
  public static boolean contains(String appId) {

    return RULE_EVALUATION_HANDLER_MAP.containsKey(appId);
  }
}

