package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.NO_FORMAT_VARIABLES_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SingleDefinitionDmnEvaluator;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class RuleEvaluationTask implements Task {

  private String requestKey;
  private String responseKey;
  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  private final DefinitionDetails definitionDetails;
  private final SingleDefinitionDmnEvaluator singleDefinitionDmnEvaluator;

  @Override
  public State execute(State inputRequest) {
    WorkflowLogger.logInfo(
        "Begin methodName=execute requestKey=%s thread=%s",
        inputRequest.getValue(requestKey), Thread.currentThread().getName());
    State state = new State();
    try {
      List<Map<String, Object>> response;
      //In case of Single Definition evaluate DMN using the definition data in WAS
      if (definitionDetails.getTemplateDetails().getDefinitionType().equals(
          DefinitionType.SINGLE)) {
        response = singleDefinitionDmnEvaluator.evaluateDMN(definitionDetails.getDefinitionData(),
            inputRequest.getValue(requestKey), inputRequest.getValue(NO_FORMAT_VARIABLES_KEY));

        // for multi-step, modify the response such that it is either true or false.
        response = singleDefinitionDmnEvaluator.dmnResponseConvertor(
                MultiStepUtil.multiConditionResponseTransformer(response));
      } else {
        response = bpmnEngineRunTimeServiceRest.evaluateDecision(
            inputRequest.getValue(requestKey));
      }
      state.addValue(responseKey, response);
    } catch (Exception e) {
      WorkflowLogger.logError(
          "Error evaluating rules error=%s status=FAILURE",
          WorkflowError.RULE_EVALUATION_ERROR.getErrorMessage());
      throw new WorkflowGeneralException(WorkflowError.RULE_EVALUATION_ERROR);
    }
    WorkflowLogger.logInfo(
        "End methodName=execute requestKey=%s thread=%s",
        inputRequest.getValue(requestKey), Thread.currentThread().getName());
    return state;
  }
}
