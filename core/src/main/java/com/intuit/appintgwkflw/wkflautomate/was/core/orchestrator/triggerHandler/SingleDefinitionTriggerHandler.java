package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
public class SingleDefinitionTriggerHandler implements TriggerHandler {

  private MultipleUserDefinitionTriggerHandler multipleUserDefinitionTriggerHandler;

  @Override
  public String getName() {
    return DefinitionType.SINGLE.name();
  }

  @Override
  public WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails) {
    return multipleUserDefinitionTriggerHandler.executeTrigger(triggerDetails);
  }

  @Override
  public WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails, Optional<ProcessDetails> processDetails) {
    return executeTrigger(triggerDetails);
  }
}
