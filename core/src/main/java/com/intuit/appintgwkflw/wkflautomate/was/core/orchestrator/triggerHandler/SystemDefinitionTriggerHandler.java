package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.getDefinitionFromEventHeaders;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants.SYSTEM_DEFINITION_TRIGGER_HANDLER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.Authorization;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component(SYSTEM_DEFINITION_TRIGGER_HANDLER)
@AllArgsConstructor
public class SystemDefinitionTriggerHandler extends BaseTriggerHandler implements TriggerHandler {

  private ProcessDetailsRepository processDetailsRepository;

  private WASContextHandler contextHandler;

  private V3StartProcess startProcess;

  private V3SignalProcess signalProcess;

  private CamundaRunTimeServiceRest camundaRest;

  @Override
  public String getName() {

    return DefinitionType.SYSTEM.name();
  }

  /**
   * It starts the process or signals the process for a given record id.
   *
   * <p>Start Process: Fetches the enabled system definition for a given system template and starts
   * the process if no existing process details found.
   *
   * <p>Signal Process: Fetches all the active,error processes by given record and definition
   * details and signal the process.
   *
   * <p>If any of the process found is in the error state it will assume it as a retry and will only
   * signal error processes.
   */
  @Override
  @Trace
  public WorkflowGenericResponse executeTrigger(final TriggerProcessDetails triggerDetails, Optional<ProcessDetails> processDetailOpt) {

    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));

    // 1) Process and get TransactionEntity
    final TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(triggerDetails.getTriggerMessage(), contextHandler);

    logInfo(transactionEntity);

    // 2) get enabled definition
    final List<DefinitionDetails> definitionDetail =
        runtimeHelper.getEnabledSystemDefinition(
            transactionEntity, triggerDetails.getTemplateDetails());

    // 3) Get processes running for the specific record/workflow/definition
    if(null == processDetailOpt) {
      processDetailOpt =
              fetchProcessDetails(authorization, transactionEntity, definitionDetail);
    }

    Map<String, Object> initialStartEventExtensionPropertiesMap = getInitialStartEventExtensionPropertiesFromActivityDetail(transactionEntity);

    // 4)  Start new process if no existing running process found else signal the process
    if(processDetailOpt.isEmpty()) {
      return startProcess(transactionEntity, definitionDetail, initialStartEventExtensionPropertiesMap, authorization);
    }
    // 5) Signal the waiting process
    else {
      List<ProcessDetails> processDetailsList = new ArrayList<>();
      processDetailsList.add(processDetailOpt.get());
      runtimeHelper.mergeCalledProcesses(
              processDetailsList,
          definitionDetail
      );
      return WorkflowGenericResponse.builder()
          .status(ResponseStatus.SUCCESS)
          .response(
              new WorkflowTriggersResponse(
                  runtimeHelper.mergeTriggerResponse(
                          processDetailsList.stream().map(
                          processDetail ->
                              signalRunningProcess(
                                  transactionEntity, definitionDetail, processDetail, initialStartEventExtensionPropertiesMap, authorization)
                      ).collect(Collectors.toList()),
                          processDetailsList
                  )
              )
          ).build();
    }
  }

  @Override
  public WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails) {
    return executeTrigger(triggerDetails, null);
  }

  /**
   * find the process details for a given record and definition
   *
   * @param authorization authorization Details
   * @param transactionEntity transaction details
   * @param definitionDetailsList definition details
   * @return {@link Optional<ProcessDetails>}
   */
  private Optional<ProcessDetails> fetchProcessDetails(
      final Authorization authorization,
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList) {

    return processDetailsRepository
        .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
            transactionEntity.getEntityId(),
            Long.parseLong(authorization.getRealm()),
            Arrays.asList(ACTIVE, ERROR),
            definitionDetailsList);
  }

  /**
   * @param transactionEntity
   */
  private void logInfo(final TransactionEntity transactionEntity) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("executeTrigger")
                .message(
                    "Begin processing trigger message for recordType=%s recordId=%s",
                    transactionEntity.getEntityType().name(), transactionEntity.getEntityId())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS));
  }

  /**
   * starts the process for a definition and returns the process details if started else returns
   * {@link TriggerStatus.NO_ACTION} if process already started.
   *
   * @param transactionEntity transaction details
   * @param definitionDetail definition details
   * @param startEvents start event for the template
   * @param authorization authorization details of the caller
   * @throws {@link WorkflowGeneralException} if no definition found or error in starting process.
   * @return {@link WorkflowGenericResponse}
   */
  private WorkflowGenericResponse startProcess(
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetail,
      final Map<String, Object> initialStartEventExtensionPropertiesMap,
      final Authorization authorization) {

    // if there are systemTags in the lookupKeys then get definition matching tags else latest
    DefinitionDetails definitionDetails =  getDefinitionFromEventHeaders(
            definitionDetail, transactionEntity.getEventHeaders());

    final Map<String, Object> startProcessResult =
        startProcess.startProcess(transactionEntity, definitionDetails, initialStartEventExtensionPropertiesMap);

    if (ObjectUtils.allNotNull(startProcessResult.get(WorkflowConstants.ID))) {

      Optional<List<ProcessDetails>> processes =
          runtimeHelper.getProcessDetailsInstance(
              transactionEntity.getEntityId(),
              Long.parseLong(authorization.getRealm()),
              ProcessStatus.ACTIVE,
              definitionDetails);

      boolean processExist = processes.isPresent() && processes.get().size() > 0;

      if (processExist) {
        String redundantProcessId = (String) startProcessResult.get("id");
        camundaRest.deleteProcessInstance(redundantProcessId);
        WorkflowLogger.logInfo(
            "A process already running with this ownerId=%s and recordId=%s, deleted process=%s",
            authorization.getRealm(), transactionEntity.getEntityId(), redundantProcessId);

        throw new WorkflowGeneralException(WorkflowError.TRIGGER_START_PROCESS_ERROR,
            "A process is already running for this ownerId and recordId");
      }

      runtimeHelper.saveProcessDetailsInstance(
          transactionEntity.getEntityId(),
          Long.parseLong(authorization.getRealm()),
          (String) startProcessResult.get(WorkflowConstants.ID),
          ProcessStatus.ACTIVE,
          definitionDetails,
          null,
          Collections.emptyMap());
    }

    return runtimeHelper.getTriggerResponse(
        ResponseStatus.SUCCESS,
        Objects.isNull(startProcessResult.get(WorkflowConstants.ID))
            ? TriggerStatus.NO_ACTION
            : TriggerStatus.PROCESS_STARTED,
        (String) startProcessResult.get(WorkflowConstants.ID),
        definitionDetails.getDefinitionId(),
        definitionDetails.getDefinitionName());
  }

  /**
   * signal the process for a definition and returns the process details if signaled.
   *
   * @param transactionEntity transaction details
   * @param definitionDetail definition details
   * @param processDetail process details
   * @param startEvents start event details for the template
   * @return {@link WorkflowGenericResponse}
   * @throws {@link WorkflowGeneralException} if error in starting process.
   */
  private WorkflowTriggerResponse signalRunningProcess(
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> definitionDetailsList,
      final ProcessDetails processDetail,
      final Map<String, Object> initialStartEventExtensionPropertiesMap,
      final Authorization authorization) {

    try {

      WorkflowVerfiy.verify(Objects.isNull(processDetail), PROCESS_NOT_FOUND);

      String definitionId = processDetail.getDefinitionDetails().getDefinitionId();

      WorkflowVerfiy.verify(definitionId, DEFINITION_NOT_FOUND);

      DefinitionDetails definitionDetails =
          definitionDetailsList.stream()
              .filter(def -> definitionId.equals(def.getDefinitionId()))
              .findFirst()
              .orElseThrow(
                  (() ->
                      new WorkflowGeneralException(
                          WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
                          authorization.getRealm(),
                          transactionEntity.getEntityType())));

      final boolean signalProcessSuccess =
          signalProcess.signalProcessById(
              transactionEntity, Optional.of(definitionDetails), processDetail, initialStartEventExtensionPropertiesMap);

      return runtimeHelper.getWorkflowTriggerResponse(
          signalProcessSuccess ? TriggerStatus.PROCESS_SIGNALLED : TriggerStatus.NO_ACTION,
          processDetail.getProcessId(),
          definitionDetails.getDefinitionId(),
          definitionDetails.getDefinitionName());

    } catch (final WorkflowGeneralException ex) {
      /**
       * in case co-relate message API fails, exception is caught, process is marked error only if
       * blockProcessOnSignalMiss is enabled and error is re-thrown.
       */
      if (transactionEntity.getEventHeaders().isBlockProcessOnSignalFailure()) {
        runtimeHelper.markProcessError(transactionEntity, processDetail);
      }
      throw ex;
    }
  }
}
