package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.Tag;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 */
@UtilityClass
public class TransactionEntityFactory {

  /**
   * Builds instance of TransactionEntity with the given Map.
   *
   * @param triggerMessage : Map<String, Object>
   * @param contextHandler : to add params in context.
   * @return : type TransactionEntity
   */
  public TransactionEntity getInstanceOf(final Map<String, Object> triggerMessage,
		  WASContextHandler contextHandler) {
    TransactionEntity txnEntity = new TransactionEntity(triggerMessage);
    populateContext(txnEntity, contextHandler);
    return txnEntity;
  }

  /**
   * Populates context from the transaction entity.
   *
   * @param txnEntity :: TransactionEntity instance.
   * @param contextHandler :: adding context params using txnEntity instance.
   */
  private void populateContext(TransactionEntity txnEntity,
		  WASContextHandler contextHandler) {
    /**
     * WorkflowName for metric Logging.
     */
    contextHandler.addKey(WASContextEnums.WORKFLOW, getWorkflow(txnEntity));
    contextHandler.addKey(WASContextEnums.TAGS_VERSION, getTagVersion(txnEntity));
  }

  /**
   * @return workflow
   */
  private String getWorkflow(TransactionEntity txnEntity) {
    return txnEntity.getEntityType() + txnEntity.getEventHeaders().getWorkflow();
  }
  /**
   * @return tag version
   */
  private String getTagVersion(TransactionEntity txnEntity) {
    Tag tag = txnEntity.getEventHeaders().getTags();
    return (ObjectUtils.isNotEmpty(tag) && ObjectUtils.isNotEmpty(tag.getVersion()))
        ? tag.getVersion()
        : null;
  }
}
