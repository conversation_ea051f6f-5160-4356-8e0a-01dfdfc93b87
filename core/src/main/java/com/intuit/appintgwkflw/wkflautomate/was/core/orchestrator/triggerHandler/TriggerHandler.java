package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;

import java.util.Optional;

public interface TriggerHandler {

  /** @return trigger handler name */
  String getName();

  /**
   * execute trigger based on template type.
   * 
   * @param triggerDetails {@link TriggerProcessDetails} trigger details
   * @return {@link WorkflowGenericResponse}
   */
  WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails);

  WorkflowGenericResponse executeTrigger(TriggerProcessDetails triggerDetails, Optional<ProcessDetails> processDetails);
}
