package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import java.util.HashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <p>Class acts as factory to return trigger handlers
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TriggerHandlers {

  private static final Map<String, TriggerHandler> TRIGGER_HANDLER_MAP = new HashMap<>();

  /**
   * Adds a handler.
   *
   * @param appId   the app id
   * @param handler the handler
   */
  public static void addHandler(String appId, TriggerHandler handler) {

    TRIGGER_HANDLER_MAP.put(appId, handler);
  }

  /**
   * Gets handler.
   *
   * @param appId caller appId
   * @return rule handler impl
   */
  public static TriggerHandler getHandler(String appId) {

    return TRIGGER_HANDLER_MAP.get(appId);
  }

  /**
   * Contains boolean.
   *
   * @param appId caller appId
   * @return true /false if handler is present or not
   */
  public static boolean contains(String appId) {

    return TRIGGER_HANDLER_MAP.containsKey(appId);
  }
}
