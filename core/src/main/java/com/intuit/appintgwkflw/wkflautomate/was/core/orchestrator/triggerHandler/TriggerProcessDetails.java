package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;

import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@Builder
@Getter
@Setter
public class TriggerProcessDetails {

  private Map<String, Object> triggerMessage;
  private List<TemplateDetails> templateDetails;
}
