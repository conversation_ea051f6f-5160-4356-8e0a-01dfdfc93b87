package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.*;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.CommandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartRecurringProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.RuleLine.Rule;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import liquibase.repackaged.org.apache.commons.collections4.CollectionUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.boot.registry.classloading.spi.ClassLoaderService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TriggerRecurringProcessHelper {

  private final MetricLogger metricLogger;
  private final RunTimeService runTimeService;
  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  private final TriggerDetailsRepository triggerDetailsRepository;
  private final WASContextHandler contextHandler;
  private final EventScheduleHelper eventScheduleHelper;
  private final SchedulingService schedulingService;
  private final FeatureFlagManager featureFlagManager;

  /**
   * Prepare correlate key map based on definition key and add to state Retrieve triggerDetails
   * based on trigger message disable Use the same to signal process using CorrelateAllMessageTask
   *
   * @param definitionInstance
   * @param inputRequest
   * @return CorrelateAllMessageTask
   */
  public CorrelateAllMessageTask prepareCorrelateMessageTask(
      DefinitionInstance definitionInstance, State inputRequest) {
    String ownerId = inputRequest.getValue(REALM_ID_KEY);
    final State inputRequestForDeploy =
        CommandHelper.prepareStateRequestForDisabledAndDelete(definitionInstance, ownerId, false);
    inputRequest.addAll(inputRequestForDeploy);
    List<TriggerDetails> triggerDetailsList =
        triggerDetailsRepository
            .findByTemplateDetails(definitionInstance.getTemplateDetails())
            .orElse(Collections.emptyList());
    String triggerName =
        CommandHelper.getTriggerNameFromMessageName(
            WorkflowConstants.CAMUNDA_DISABLE_MESSAGE_NAME, triggerDetailsList);

    /**
     * Adding a default trigger message for disable flow for custom workflows to handle case where a
     * template update may remove the need for an external trigger for deleting inflight processes
     */
    if (StringUtils.isBlank(triggerName)) {
      if (TemplateCategory.CUSTOM
          .name()
          .equalsIgnoreCase(definitionInstance.getTemplateDetails().getTemplateCategory())) {
        triggerName = CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;
      } else {
        WorkflowLogger.logError("No Disable trigger details found to correlate recurring process");
        throw new WorkflowGeneralException(WorkflowError.TRIGGER_MESSAGE_NAME_ERROR);
      }
    }
    return new CorrelateAllMessageTask(bpmnEngineRunTimeServiceRest, triggerName);
  }

  /**
   * internally create a Transaction entity for process trigger and add to state and create a
   * StartRecurringProcessTask
   *
   * @param definitionInstance
   * @param state
   * @return StartRecurringProcessTask
   */
  public StartRecurringProcessTask getStartRecurringProcessTask(
      DefinitionInstance definitionInstance, State state) {
    TransactionEntity transactionEntity = getTransactionEntity(definitionInstance);
    state.addValue(AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY, transactionEntity);

    return new StartRecurringProcessTask(metricLogger, runTimeService);
  }

  private TransactionEntity getTransactionEntity(DefinitionInstance definitionInstance) {
    List<Rule> rules =
        definitionInstance
            .getDefinition()
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getRuleLines()
            .get(0)
            .getRules();
    RecordType recordType = RecordType.fromType(definitionInstance.getDefinition().getRecordType());
    return TriggerUtil.prepareTransactionEntity(
        definitionInstance.getTemplateDetails(), recordType, ENTITY_CHANGE_TYPE_CREATED, StringUtils.EMPTY,
        rules, contextHandler, null);
  }

  /**
   * for given definition id, if event scheduling is enabled and there are no scheduleIds
   * in the db as well as in Scheduling Service, the definition may be from an older version
   * of the template and would need a recurring process to be triggered in camunda. TODO: remove check after migration
   * https://jira.intuit.com/browse/QBOES-21483
   */
  public boolean isDefinitionSupportingRecurringProcess(DefinitionDetails definitionDetails) {
    if(Objects.isNull(definitionDetails))
      return false;
    boolean isTemplateNameScheduledActions = WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS.getName().equals(definitionDetails.getTemplateDetails().getTemplateName());
    boolean isTemplateNameCustomReminder = WorkflowNameEnum.CUSTOM_REMINDER.getName().equals(definitionDetails.getTemplateDetails().getTemplateName());
    return CollectionUtils.isEmpty(
            eventScheduleHelper.getScheduleIdsForDefinition(definitionDetails.getDefinitionId())) && (!(isTemplateNameScheduledActions || isTemplateNameCustomReminder) || !areScheduleIdsPresentInSchedulingSvc(definitionDetails));
  }

  /**
   * Checks if schedule IDs are present in the Scheduling Service.
   * TODO: remove template name check after migration
   *
   * @param definitionDetails The details of the definition.
   * @return boolean True if schedule IDs are present in the Scheduling Service, false otherwise.
   */
  public boolean areScheduleIdsPresentInSchedulingSvc(DefinitionDetails definitionDetails) {
    return ObjectUtils.isNotEmpty(schedulingService.getSchedules(SchedulingServiceUtil.getScheduleIds(definitionDetails.getDefinitionKey(), definitionDetails.getTemplateDetails().getTemplateName()), definitionDetails.getOwnerId().toString()));
  }
}
