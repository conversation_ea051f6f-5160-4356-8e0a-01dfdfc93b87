package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage.CorrelateMessageBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.ProcessVariableDetails;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class V3SignalProcess {

  private final TriggerDetailsRepository triggerDetailsRepository;

  private final BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private final V3RunTimeHelper runTimeHelper;

  private final ProcessDetailsRepository processDetailsRepository;

  private final MetricLogger metricLogger;

  private final WASContextHandler contextHandler;


  /**
   * Signal the already running process for specific entity encapsulated in payload
   * v3TransactionEntity
   *
   * @param v3TransactionEntity v3 transaction entity with event headers
   * @param definitionDetailsList definitions enabled for the company for the entity and given
   *     workflow type in the v3TransactionEntity payload
   * @param proccessDetails Process entity which has to be signaled
   * @param startEvents
   * @return
   */
  @Trace
  public boolean signalProcess(
      TransactionEntity v3TransactionEntity,
      List<DefinitionDetails> definitionDetailsList,
      ProcessDetails proccessDetails,
      Map<String, Object> initialStartEventExtensionPropertiesMap) {

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("executeTrigger")
                .message(
                    "Process is already running for the recordId=%s",
                    v3TransactionEntity.getEntityId())
                .downstreamComponentName(DownstreamComponentName.WAS_DB));

    boolean signalProcessResponse = false;

    Optional<DefinitionDetails> defDetailsOptional =
        definitionDetailsList.stream()
            .filter(
                def ->
                    proccessDetails
                        .getDefinitionDetails()
                        .getDefinitionId()
                        .equals(def.getDefinitionId()))
            .findAny();

    if (defDetailsOptional.isPresent()) {
      signalProcessResponse =
          signalProcess(
              v3TransactionEntity, proccessDetails, initialStartEventExtensionPropertiesMap, defDetailsOptional.get());
    }
    return signalProcessResponse;
  }

  /**
   * @param transactionEntity transaction entity with event headers * @param proccessDetails Process
   *     entity which has to be signaled
   * @param proccessDetails Process entity which has to be signaled
   * @param startEvents start event details
   * @param definitionDetails input definition Detail
   * @return if process was signaled or not
   */
  @SuppressWarnings("unchecked")
  private boolean signalProcess(
      TransactionEntity transactionEntity,
      ProcessDetails proccessDetails,
      Map<String, Object> initialStartEventExtensionPropertiesMap,
      DefinitionDetails definitionDetails) {

    TemplateDetails templateDetails = definitionDetails.getTemplateDetails();
    // adding process instance id in MDC context
    contextHandler.addKey(WASContextEnums.PROCESS_INSTANCE_ID, proccessDetails.getProcessId());

    boolean signalProcessResponse = false;
    // When definition is created from a template, templateDetails is not null.
    if (templateDetails != null) {

      Optional<List<TriggerDetails>> triggerDetailsOptionalList =
          triggerDetailsRepository.findByTemplateDetails(templateDetails);
      if (!triggerDetailsOptionalList.isPresent() || triggerDetailsOptionalList.get().isEmpty()) {
        markErrorStatusAndLogUnableToTrigger(
            transactionEntity,
            proccessDetails,
            "Can not signal process as there is no trigger information available");
      } else if (!triggerDetailsOptionalList.get().isEmpty()) {

        // filter list of trigger details matching given entity change type
        String entityChangeType = transactionEntity.getEntityChangeType();
        List<TriggerDetails> triggerDetailsFiltered =
            triggerDetailsOptionalList.get().stream()
                // Only match if both parameters are non-null
                .filter(
                    triggerDetails ->
                        StringUtils.containsIgnoreCase(
                            triggerDetails.getTriggerName(), entityChangeType))
                .collect(Collectors.toList());

        // check for unique matching trigger details else log warning
        handleNonUniqueTriggerNames(triggerDetailsFiltered, entityChangeType);

        // check any dynamic expression
        // customWait${intuit_userId}  correlationMessages can be customWait, customWait123
        // 123customWait
        handleDynamicTriggerNamesExpression(
            triggerDetailsFiltered, triggerDetailsOptionalList.get(), entityChangeType);

        if (!triggerDetailsFiltered.isEmpty()) {

          /** Fetch process variables details from entity section */
          Map<String, Object> entityObjectDetails = runTimeHelper.getEntityObjectMap(transactionEntity);

          // Fetch global process variables
          Map<String, Object> globalProcessVariables = getGlobalProcessVariables(
                  entityObjectDetails,
                  transactionEntity,
                  initialStartEventExtensionPropertiesMap,
                  definitionDetails,
                  proccessDetails);

          // fetching local process variables
          Map<String, Object> localProcessVariables = fetchVariables(
                  transactionEntity,
                  initialStartEventExtensionPropertiesMap,
                  definitionDetails,
                  true,
                  proccessDetails);

          CorrelateMessage correlateMessage =
              fetchCorrelateMessage(
                  proccessDetails,
                  getTriggerDetails(triggerDetailsFiltered, entityChangeType),
                  globalProcessVariables,
                  localProcessVariables);

          // determine retries based on if call is via events or not
          String isEvent = contextHandler.get(WASContextEnums.IS_EVENT);

          String businessKey = TriggerUtil.fetchBusinessKey(transactionEntity, contextHandler);
          WorkflowLogger.info(() ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("signalProcess")
                  .message(
                      "Begin signalling process for recordId=%s recordType=%s entityChangeType=%s workflowType=%s definitionId=%s definitionCreatedByUserId=%s businessKey=%s",
                      transactionEntity.getEntityId(),
                      transactionEntity.getEntityType().toString(),
                      transactionEntity.getEntityChangeType(),
                      transactionEntity.getWorkflowType(),
                      definitionDetails.getDefinitionId(),
                      definitionDetails.getCreatedByUserId(),
                      businessKey
                  ).downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_SIGNAL_PROCESS));
          signalProcessResponse =
              isSignalProcessResponse(isEvent, bpmnEngineRunTimeServiceRest, correlateMessage);
        }
      }

    } else {
      // ToDo: revisit when supporting build your own definition, i.e, Definition created from
      // building blocks.As of now log and move forward
      markErrorStatusAndLogUnableToTrigger(
          transactionEntity,
          proccessDetails,
          "Can not signal process as there is no template information available");
    }
    return signalProcessResponse;
  }

  /**
   * This method used to check does any trigger name has any dynamic expression or not. If it has and matched with entityChangeType then set to the triggerDetailsFiltered
   * @param triggerDetailsFiltered
   * @param triggerDetailsList
   * @param entityChangeType
   */
  private void handleDynamicTriggerNamesExpression(
      List<TriggerDetails> triggerDetailsFiltered,
      List<TriggerDetails> triggerDetailsList,
      String entityChangeType) {
    if (triggerDetailsFiltered.isEmpty()) {
      for (TriggerDetails triggerDetails : triggerDetailsList) {
        try {
          if (runTimeHelper.checkStaticMessageExistsInCorrelationMsg(
              entityChangeType, triggerDetails.getTriggerName())) {
            // it will help in building correlation message
            triggerDetails.setTriggerName(entityChangeType);
            triggerDetailsFiltered.add(triggerDetails);
            break;
          }
        } catch (Exception e) {
          WorkflowLogger.logWarn(e, "Exception while handling dynamic trigger name expression");
          return;
        }
      }
    }
  }

  // Find the exact entity change type in triggersDetailsFiltered List else do a findFirst
  // This function will be reachable only if there is an exact or partial match
  private TriggerDetails getTriggerDetails(List<TriggerDetails> triggerDetailsFiltered, String entityChangeType) {
    return triggerDetailsFiltered.stream().
        filter(triggerDetails ->
            StringUtils.equalsIgnoreCase(triggerDetails.getTriggerName(),entityChangeType))
        .findAny()
        .orElseGet(()-> triggerDetailsFiltered.stream().findFirst().get());
  }

  private Map<String, Object> getGlobalProcessVariables(
      Map<String, Object> entityObjectDetails,
      TransactionEntity transactionEntity,
      Map<String, Object> initialStartEventExtensionPropertiesMap,
      DefinitionDetails definitionDetails,
      ProcessDetails processDetails) {

    Map<String, Object> globalProcessVariables = Collections.emptyMap();

    /** populate global process variables from entity section */
    if (MapUtils.isNotEmpty(entityObjectDetails)) {
      globalProcessVariables =
          runTimeHelper.getVariablesMap(
              transactionEntity,
              entityObjectDetails,
              false,
              initialStartEventExtensionPropertiesMap,
              definitionDetails,
              true,
              processDetails,
              false
          );
    }

    /**
     * populate global process variables from variables section if details are not present in entity
     * section.
     */
    if (MapUtils.isEmpty(globalProcessVariables)) {
      globalProcessVariables =
          fetchVariables(transactionEntity, initialStartEventExtensionPropertiesMap, definitionDetails, false, processDetails);
    }
    return globalProcessVariables;
  }

  private boolean isSignalProcessResponse(
      String isEvent,
      BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest,
      CorrelateMessage correlateMessage) {
    if (Boolean.parseBoolean(isEvent)) {
      return bpmnEngineRunTimeServiceRest.correlateMessageEvent(correlateMessage);
    } else {
      return bpmnEngineRunTimeServiceRest.correlateMessage(correlateMessage);
    }
  }

  /**
   * method checks if the matching trigger is either unique either based on single element in list
   * or all elements having same trigger name. If no unique element is available, logs a warning
   *
   * @param triggerDetailsFiltered list of trigger details filtered on matching entitychange type
   * @param entityChangeType transaction entity change type
   */
  private void handleNonUniqueTriggerNames(
      List<TriggerDetails> triggerDetailsFiltered, String entityChangeType) {
    // get list of distinct trigger names to verify unique matching triggers
    Set<String> distinctTriggerNames =
        triggerDetailsFiltered.stream()
            .map(triggerDetails -> triggerDetails.getTriggerName().toLowerCase())
            .collect(Collectors.toSet());
    if (distinctTriggerNames.size() != 1) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("signalProcess")
                  .message(
                      "No unique matching trigger information available for messageEvent=%s | distinctTriggersListSize=%s",
                      entityChangeType, distinctTriggerNames.size())
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS));
    }
  }

  private void markErrorStatusAndLogUnableToTrigger(
      TransactionEntity transactionEntity, ProcessDetails processDetails, final String wanMessage) {

    /**
     * in case co-relate message API fails, exception is caught, process is marked error only if
     * blockProcessOnSignalMiss is enabled and error is re-thrown.
     */
    if (transactionEntity.getEventHeaders().isBlockProcessOnSignalFailure()) {
      runTimeHelper.markProcessError(transactionEntity, processDetails);
    }

    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("signalProcess")
                .message(wanMessage)
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_TRIGGER_PROCESS));
  }

  /**
   * * Signal the already running process for specific entity encapsulated in payload
   * TransactionEntity
   *
   * @param transactionEntity transaction entity with event headers
   * @param defDetailsOptional definitions details enabled for the company for the entity and given
   *     workflow type in the TransactionEntity payload
   * @param proccessDetails Process entity which has to be signaled
   * @param startEvents start event detail
   * @return if process was signaled or not
   */
  @Trace
  public boolean signalProcessById(
      TransactionEntity transactionEntity,
      Optional<DefinitionDetails> defDetailsOptional,
      ProcessDetails proccessDetails,
      Map<String, Object> initialStartEventExtensionPropertiesMap) {

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("executeTrigger")
                .message(
                    "Process is already running for the recordId=%s",
                    transactionEntity.getEntityId())
                .downstreamComponentName(DownstreamComponentName.WAS_DB));

    boolean signalProcessResponse = false;

    if (defDetailsOptional.isPresent()) {
      signalProcessResponse =
          signalProcess(transactionEntity, proccessDetails, initialStartEventExtensionPropertiesMap, defDetailsOptional.get());
    }
    return signalProcessResponse;
  }

  /**
   * prepare CorrelateMessage details for local/global variable scoping.
   *
   * @param proccessDetails process details
   * @param triggerDetails trigger details
   * @param localVariables flag to populate local/global variables
   * @param globalVariables process variables
   * @return {@link CorrelateMessage}
   */
  @SuppressWarnings("unchecked")
  private CorrelateMessage fetchCorrelateMessage(
      ProcessDetails proccessDetails,
      TriggerDetails triggerDetails,
      Map<String, Object> globalVariables,
      Map<String, Object> localVariables) {

    CorrelateMessageBuilder correlateMessageBuilder =
        CorrelateMessage.builder()
            .messageName(triggerDetails.getTriggerName())
            .processInstanceId(proccessDetails.getProcessId());

    // populate global variables
    if (MapUtils.isNotEmpty(globalVariables)) {
      correlateMessageBuilder.processVariables(
          (Map<String, Object>) globalVariables.get(WorkflowConstants.BPMN_DMN_VARIABLES));
    }

    // populate local variables
    if (MapUtils.isNotEmpty(localVariables)) {
      correlateMessageBuilder.processVariablesLocal(
          (Map<String, Object>) localVariables.get(WorkflowConstants.BPMN_DMN_VARIABLES));
    }
    return correlateMessageBuilder.build();
  }

  /**
   * return the local process variable details if present else return empty.
   *
   * @param transactionEntity transaction entity
   * @param definitionDetails definition details
   * @param startEvents start event detail
   * @param local flag for identifying local/global variables
   * @return local/global process variable details
   */
  private Map<String, Object> fetchVariables(
      TransactionEntity transactionEntity,
      Map<String, Object> initialStartEventExtensionPropertiesMap,
      DefinitionDetails definitionDetails,
      boolean local,
      ProcessDetails processDetails) {

    Function<? super ProcessVariableDetails, ? extends Map<String, Object>> processVariableMapper =
        localProcessVariables -> localProcessVariables.getGlobal();

    if (local) {
      processVariableMapper = localProcessVariables -> localProcessVariables.getLocal();
    }

    return Optional.of(transactionEntity)
        .map(processVarDetail -> processVarDetail.getVariables())
        .map(processVariableMapper)
        .filter(MapUtils::isNotEmpty)
        .map(
            localVariables ->
                runTimeHelper.getVariablesMap(
                    transactionEntity, localVariables, false, initialStartEventExtensionPropertiesMap, definitionDetails, false, processDetails, local))
        .orElseGet(() -> Collections.emptyMap());
  }

}
