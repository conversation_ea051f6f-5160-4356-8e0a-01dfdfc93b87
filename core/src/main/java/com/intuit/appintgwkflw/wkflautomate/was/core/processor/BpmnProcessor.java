package com.intuit.appintgwkflw.wkflautomate.was.core.processor;

/**
 * <AUTHOR>
 */

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @param <T> This method processes the template XML and returns a v4 Entity.
 */
public interface BpmnProcessor<T> {

    /**
     * This function generates template object with populated workflowSteps
     * after traversing the bpmn and dmn model instances
     *
     * @param definitionInstance definition instance object
     * @param templateId         template id
     * @param isDefinitionRead   boolean flag
     * @return generic later type cast as template
     * @throws IOException
     */
    T processBpmn(
            DefinitionInstance definitionInstance,
            GlobalId templateId,
            boolean isDefinitionRead) throws IOException;

    List<Template> processBpmn(Map<TemplateDetails, List<TemplateDetails>> map) throws IOException;
}
