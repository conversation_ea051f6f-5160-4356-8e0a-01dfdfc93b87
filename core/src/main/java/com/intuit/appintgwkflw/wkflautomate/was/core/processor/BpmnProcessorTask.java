package com.intuit.appintgwkflw.wkflautomate.was.core.processor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TEMPLATE_REQ;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TEMPLATE_TYPE_ID;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.globalid.GlobalIdV41;
import com.intuit.v4.workflows.Template;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;

public class BpmnProcessorTask implements Task {

  private BpmnProcessorImpl bpmnProcessor;
  private String requestKey;
  private String responseKey;

  public BpmnProcessorTask(String requestKey, String responseKey, BpmnProcessorImpl bpmnProcessor) {
    this.requestKey = requestKey;
    this.responseKey = responseKey;
    this.bpmnProcessor = bpmnProcessor;
  }

  @SuppressWarnings("unchecked")
  @Override
  public State execute(State inputRequest) {
    Map<String, Pair<TemplateDetails, List<TemplateDetails>>> req =
        inputRequest.getValue(TEMPLATE_REQ);

    Pair<TemplateDetails, List<TemplateDetails>> valueMap = req.get(requestKey);
    Template templateResponse = null;

    DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
        BpmnProcessorUtil.readBPMN(valueMap.getLeft().getTemplateData()),
        BpmnProcessorUtil.prepareDmnInstance(valueMap.getRight()),
        valueMap.getLeft());

    try {
      templateResponse =
          (Template)
              bpmnProcessor.processBpmn(
                  definitionInstance,
                  GlobalIdV41.builder()
                      .setRealmId("")
                      .setTypeId(TEMPLATE_TYPE_ID)
                      .setLocalId(valueMap.getLeft().getId())
                      .build(),
                  false);

      inputRequest.addValue(responseKey, templateResponse);
    } catch (Exception e) {
      // Not Adding the incorrect template to the response
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Exception while parsing Template ID=%s",
                      valueMap.getLeft().getId())
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(this.getClass().getSimpleName()));
    }
    return inputRequest;
  }
}