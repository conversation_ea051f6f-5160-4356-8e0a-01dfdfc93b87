package com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.MultiStepBuilderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepTemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.FlowNodeImpl;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * This class parses a multi-step/multi-condition bpmn
 *
 * @param <T> returns a generic which is later typecast to Template
 * <AUTHOR>
 */
@Service(WorkflowBeansConstants.MULTI_STEP_BPMN_PARSER_IMPL)
@AllArgsConstructor
public class MultiStepBpmnProcessorImpl<T> implements BpmnProcessor<T> {

  private final TemplateBuilder templateBuilder;
  private final MultiStepBuilderFactory multiStepBuilderFactory;
  private final MultiStepTemplateBuilder multiStepTemplateBuilder;
  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * This function generates template object with populated workflowSteps after traversing the bpmn
   * and dmn model instances. It also substitutes placeholders and generates the V4 Template Object
   *
   * @param definitionInstance definition instance object
   * @param templateId         template id
   * @param isDefinitionRead   boolean flag - will be false here
   * @return generic later type cast as template
   */
  @Override
  @Metric(name = MetricName.MULTI_STEP_READ_ONE_DEFINITION, type = Type.APPLICATION_METRIC)
  public T processBpmn(
      DefinitionInstance definitionInstance,
      GlobalId templateId,
      boolean isDefinitionRead) {
    TemplateDetails templateDetails = definitionInstance.getTemplateDetails();
    Template template = templateBuilder.buildTemplateDetails(templateId, templateDetails);
    return processMultiStepDefinition(
        definitionInstance,
        template);
  }

  /**
   * This function parses the bpmn starting from the start event element. We get the list of
   * outgoing sequence flows and based on the element type present at the end of each sequence flow
   * we process it either as an action step or as a condition step
   *
   * @param definitionInstance definition instance object
   * @param template           template object
   * @return generic later type cast as template
   */
  private T processMultiStepDefinition(
      DefinitionInstance definitionInstance,
      Template template) {
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    BpmnModelInstance bpmnModelInstance = definitionInstance.getBpmnModelInstance();
    String definitionId = String.valueOf(definitionInstance.getDefinition().getId());
    WorkflowLogger.logInfo("step=parseMultiStepBpmnXml for definitionId=%s", definitionId);
    FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
    Map<String, GlobalId> actionIdToStepIdMap = new HashMap<>();

    // ChildId to WorkflowStepCondition map for Composite(WorkflowStep) stepType
    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();

    if (startEventFlowElement instanceof FlowNodeImpl) {
      // get the list of outgoing sequence flows from bpmn's start event element
      Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
      outgoingSequenceFlows.stream().forEach(sequenceFlow -> {
        // start dfs traversal of bpmn elements
        dfsTraverseBPMN(
            sequenceFlow.getTarget(),
            workflowSteps,
            definitionInstance,
            template,
            actionIdToStepIdMap, compositeStepIdToWorkflowStepConditionMap);
      });
    } else {
      throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_ELEMENT);
    }
    template.setWorkflowSteps(workflowSteps);
    // set templateData
    setTemplateDataForDefinition(template);
    WorkflowLogger.logInfo("step=multiStepBpmnTraversalEnd for definitionId=%s", definitionId);
    return (T) template;
  }

  /**
   * This function recursively traverse over the bpmn elements and based on the type of the element
   * calls a separate handler to build an action step or a condition step
   *
   * @param flowNode            bpmn flowNode element
   * @param workflowSteps       list of workflowSteps
   * @param definitionInstance  definitionInstance object
   * @param template            template object
   * @param actionIdToStepIdMap map of actionId's to workflowStepId's
   */
  private void dfsTraverseBPMN(
      FlowNode flowNode,
      List<WorkflowStep> workflowSteps,
      DefinitionInstance definitionInstance,
      Template template,
      Map<String, GlobalId> actionIdToStepIdMap,
      Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap) {
    WorkflowLogger.logInfo("step=traverseBpmnElementDFS bpmnElementType=%s bpmnElementId=%s",
        flowNode.getElementType().getTypeName(),
        flowNode.getId());
    Map<String, ActivityInstance> activityInstanceMap = definitionInstance.getActivityInstanceMap();
    // stop recursive traversal of bpmn elements, when we reach the end event element
    if (BpmnProcessorUtil.isEndEventElement(flowNode)) {
      return;
    }
    if (activityInstanceMap.containsKey(flowNode.getId())) {
      MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
          .workflowSteps(workflowSteps)
          .activityInstance(activityInstanceMap.get(flowNode.getId()))
          .template(template)
          .actionIdToStepIdMap(actionIdToStepIdMap)
          .globalId(actionIdToStepIdMap.get(flowNode.getId()))
          .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
          .build();
      multiStepBuilderFactory.buildWorkflowStep(multiStepWorkflowEntity, flowNode);
    }
    // after processing the current bpmn element, get the next list of
    // outgoing sequence flows and recursively traverse them
    Collection<SequenceFlow> outgoingSequenceFlows = flowNode.getOutgoing();
    if (outgoingSequenceFlows.isEmpty()) {
      return;
    }
    for (SequenceFlow sequenceFlow : outgoingSequenceFlows) {
      dfsTraverseBPMN(
          sequenceFlow.getTarget(),
          workflowSteps,
          definitionInstance,
          template,
          actionIdToStepIdMap, compositeStepIdToWorkflowStepConditionMap);
    }
  }

  /**
   * This function sets the templateData within the template object
   *
   * @param template template instance
   */
  public void setTemplateDataForDefinition(Template template) {
    WorkflowLogger.logInfo("step=buildTemplateData for templateId=%s definitionName=%s",
        template.getId(), template.getDisplayName());
    Record record = customWorkflowConfig.getRecordObjForType(template.getRecordType());
    // get action type
    String actionKey = CustomWorkflowType.templateNameActionKeyMapping().get(template.getName());
    // generate and set templateData
    template.setTemplateData(
        multiStepTemplateBuilder.generateTemplateData(
            record,
            actionKey));
  }

  @Override
  public List<Template> processBpmn(Map<TemplateDetails, List<TemplateDetails>> map)
      throws IOException {
    return null;
  }
}
