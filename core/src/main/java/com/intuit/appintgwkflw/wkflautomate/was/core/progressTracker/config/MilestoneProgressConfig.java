package com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config;

import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.Setter;


/**
 * 
 * <AUTHOR>
 * This config class is used for serving milestone for Progress Tracker API.
 */

@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "milestone-progress-config")
public class MilestoneProgressConfig {

  List<ZeroStateConfig> zeroStateConfig;
  
}
