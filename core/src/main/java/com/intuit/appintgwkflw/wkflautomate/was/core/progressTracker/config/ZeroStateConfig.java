package com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config;

import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR> 
 * This config class is used for serving milestone for Progress Tracker API.
 */


@Getter
@Setter
@NoArgsConstructor
public class ZeroStateConfig {

  private String workflow;
  private String templateId;
  private List<MilestoneMeta> milestoneMeta;
  private List<ZeroStateConfig> templates;

}
