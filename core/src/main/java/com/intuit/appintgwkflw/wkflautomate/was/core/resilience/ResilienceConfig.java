package com.intuit.appintgwkflw.wkflautomate.was.core.resilience;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import lombok.AllArgsConstructor;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ResilienceConfig {

    private WASCircuitBreakerService wasCircuitBreakerService;
    private WASCircuitBreakerConfiguration wasCircuitBreakerConfiguration;

    /**
     * enable/disable circuit breaker based on changes in circuit breaker config
     *
     * @param event
     */
    @EventListener
    public void onRefreshScopeRefreshed(final RefreshScopeRefreshedEvent event) {
        WorkflowLogger.logDebug(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX +
                "Scope change detected. circuitBreakerEnable=" + wasCircuitBreakerConfiguration.isEnabled());
        if(wasCircuitBreakerConfiguration.isEnabled()) {
            wasCircuitBreakerService.enableAllCircuitBreakers();
        }
        else{
            wasCircuitBreakerService.disableAllCircuitBreakers();
        }
    }

    /**
     * Creates circuit breakers on start up depending on circuit breaker config
     *
     * @param event
     */
    @EventListener
    public void onAppStarted(ApplicationStartedEvent event) {
        if(wasCircuitBreakerConfiguration.isEnabled()) {
            wasCircuitBreakerService.createCircuitBreakers();
        }
    }
}