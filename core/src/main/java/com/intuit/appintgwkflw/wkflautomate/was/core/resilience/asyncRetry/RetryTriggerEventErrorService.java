package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.asyncRetry;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.EntityRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.RetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerEventMetaDataUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class is responsible for preparing and then publishing failed trigger events to Kafka
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class RetryTriggerEventErrorService {

  private final WASContextHandler wasContextHandler;

  private final TriggerEventPublisher triggerEventPublisher;

  private final RetryConfig retryConfig;

  /**
   * This method validates the triggerMessage, prepares workflow trigger event for the enabled
   * workflows.
   *
   * @param triggerMessage   Map<String, Object>
   * @param triggerTargetAPI TriggerTargetAPI
   */
  public void processFailedTriggerEvent(final Map<String, Object> triggerMessage, TriggerTargetAPI triggerTargetAPI) {

    WorkflowLogger.logInfo(
        "Preparing and publishing failed trigger event to Kafka for retry, targetApi: %s, triggerMessage: %s",
        triggerTargetAPI.toString(), triggerMessage);

    // retry for API calls only, not for async events (when trigger is called via API the source field is null)
    if (CollectionUtils.isEmpty(triggerMessage) ||
        WorkflowConstants.EVENT.equals(triggerMessage.get(WorkflowConstants.SOURCE)) ||
        Objects.isNull(retryConfig) || CollectionUtils.isEmpty(retryConfig.getAsyncRetryProcessing())
    ) {
      return;
    }

    EntityRetryConfig triggerRetryConfig = retryConfig.getAsyncRetryProcessing().get(EventEntityType.TRIGGER.getEntityType());

    if (Objects.isNull(triggerRetryConfig) || !triggerRetryConfig.isEnabled()) {
      return;
    }

    TransactionEntity txnEntity = new TransactionEntity(triggerMessage);

    // publish trigger events only for enabled workflows
    if (triggerRetryConfig.getWorkflows().contains(txnEntity.getEventHeaders().getWorkflow())) {
      TriggerEventMetaDataUtil.updateWASContext(txnEntity, wasContextHandler);
      Trigger trigger = TriggerEventMetaDataUtil.prepareTriggerEvent(txnEntity, triggerTargetAPI);
      triggerEventPublisher.publishTriggerEvent(trigger);
    }
  }

}
