package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;

public interface CircuitBreakerActionHandler {
    /** @return trigger handler name */
    CircuitBreakerActionType getName();

    void handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition stateTransitionEvent);
}
