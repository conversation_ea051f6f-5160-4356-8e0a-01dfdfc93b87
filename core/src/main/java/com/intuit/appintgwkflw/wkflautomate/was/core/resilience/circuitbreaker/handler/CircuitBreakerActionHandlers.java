package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class CircuitBreakerActionHandlers {
    private static final Map<CircuitBreakerActionType, CircuitBreakerActionHandler> CIRCUIT_BREAKER_ACTION_HANDLER_MAP = new HashMap<>();

    /**
     * Adds a handler.
     *
     * @param action   the action circuit breaker is used for
     * @param handler the handler
     */
    public static void addHandler(CircuitBreakerActionType action, CircuitBreakerActionHandler handler) {

        CIRCUIT_BREAKER_ACTION_HANDLER_MAP.put(action, handler);
    }

    /**
     * Gets handler.
     *
     * @param action the action circuit breaker is used for e.g. ExternalTaskCompleteEvent
     * @return the circuit breaker state transition handler for the action
     */
    public static Optional<CircuitBreakerActionHandler> getHandler(CircuitBreakerActionType action) {

        return Optional.ofNullable(CIRCUIT_BREAKER_ACTION_HANDLER_MAP.get(action));
    }
}
