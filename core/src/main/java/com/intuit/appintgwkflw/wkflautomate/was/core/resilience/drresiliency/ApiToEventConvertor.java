package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.drresiliency;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.V1;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerEventMetaDataUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.AsyncProcessingResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.event.config.AsyncProcessingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * Convert API triggers calls to events and publish in eventbus.
 */
@Component
@RequiredArgsConstructor
@ConditionalOnExpression("${event.producer.enabled:false}")
public class ApiToEventConvertor {
  private final WASContextHandler wasContextHandler;
  private final TriggerEventPublisher triggerEventPublisher;
  private final AsyncProcessingConfig asyncProcessingConfig;

  public void processTriggerEvent(HttpServletRequest request, HttpServletResponse httpServletResponse){
    try {
      WorkflowVerfiy.verify(!(asyncProcessingConfig.isEnabled() && checkTriggerEndpoints(request)), WorkflowError.REGION_INACTIVE_ERROR);
      final WorkflowGenericResponseBuilder response =
          WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE);

      String body = request.getReader().lines().reduce("", (acc, line) -> acc + line);
      final Map<String, Object> requestBodyMap = ObjectConverter.fromJson(body, Map.class);

      WorkflowVerfiy.verify(!asyncProcessingEnabled(requestBodyMap, request), WorkflowError.REGION_INACTIVE_ERROR);

      TransactionEntity txnEntity = new TransactionEntity(requestBodyMap);
      TriggerEventMetaDataUtil.updateWASContext(txnEntity, wasContextHandler);
      Trigger trigger = TriggerEventMetaDataUtil.prepareTriggerEvent(txnEntity, getTargetTriggerAPI(request));
      triggerEventPublisher.publishTriggerEvent(trigger);

      response
          .status(ResponseStatus.SUCCESS)
          .response(new AsyncProcessingResponse(TriggerStatus.REQUEST_IN_PROGRESS));
      String res = ObjectConverter.toJson(response.build());
      httpServletResponse.getWriter().println(res);
      httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
      httpServletResponse.setStatus(HttpStatus.ACCEPTED.value());
    } catch (WorkflowGeneralException ex){
      throw ex;
    } catch (Exception ex) {
      WorkflowLogger.logError("Publish trigger API to eventbus failed. Error= " , ExceptionUtils.getStackTrace(ex));
      throw new WorkflowGeneralException(WorkflowError.ERROR_ADDING_TRIGGER_TO_EVENT, ex);
    }
  }

  /**
   * Get the target trigger API according to the trigger http request
   * @param request
   * @return
   */
  private TriggerTargetAPI getTargetTriggerAPI(HttpServletRequest request) {
    if (request.getRequestURI().contains(V1)) return TriggerTargetAPI.TRIGGER_V1;
    else return TriggerTargetAPI.TRIGGER_V2;
  }

  /**
   * Check if API calls are for trigger.
   *
   * @param request
   * @return
   */
  private boolean checkTriggerEndpoints(HttpServletRequest request) {
    return asyncProcessingConfig.getEndpoints().contains(request.getRequestURI());
  }

  /**
   * Check if the async processing is enabled.
   *
   * @param requestBodyMap
   * @param request
   * @return
   */
  private boolean asyncProcessingEnabled(Map<String, Object> requestBodyMap, HttpServletRequest request){
    if(asyncProcessingConfig.isEnableForAllWorkflow()) return true;
    TransactionEntity txnEntity = new TransactionEntity(requestBodyMap);

    return asyncProcessingConfig.getWorkflows().contains(txnEntity.getEntityType() + txnEntity.getEventHeaders().getWorkflow());
  }

}
