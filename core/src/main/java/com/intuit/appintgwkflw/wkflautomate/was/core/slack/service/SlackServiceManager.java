package com.intuit.appintgwkflw.wkflautomate.was.core.slack.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.JiraConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.slack.model.SlackRequestModel;
import com.slack.api.Slack;
import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.SlackApiException;
import com.slack.api.methods.request.chat.ChatGetPermalinkRequest;
import com.slack.api.methods.request.conversations.ConversationsHistoryRequest;
import com.slack.api.methods.response.chat.ChatGetPermalinkResponse;
import com.slack.api.methods.response.conversations.ConversationsHistoryResponse;
import com.slack.api.model.Message;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> This class is responsible for perfrominng operations related to slack.
 */
@Component
@AllArgsConstructor
public class SlackServiceManager {

  private final JiraConfig vocJiraConfig;

  /**
   * This method read all the slack messages for given latest and oldest unix time stamp duration.
   *
   * @param slackRequestModel
   * @return
   */
  public List<Message> readAllMessages(SlackRequestModel slackRequestModel) {
    MethodsClient methods = getMethodsClient();
    String nextCursor = "";
    boolean hasMore = true;
    List<Message> messageList = new ArrayList<>();
    while (hasMore) {
      try {
        WorkflowLogger.logInfo(
            "step=readSlackMessages; latest=%s, oldest=%s, channelId=%s",
            slackRequestModel.getLatest(), slackRequestModel.getOldest(),
            slackRequestModel.getChannelId());
        ConversationsHistoryResponse conversationsHistoryResponse = methods.conversationsHistory(
            ConversationsHistoryRequest.builder().token(vocJiraConfig.getSlackToken())
                .channel(slackRequestModel.getChannelId())
                .oldest(slackRequestModel.getOldest()).cursor(nextCursor)
                .latest(getLatestUnixTimeStamp(slackRequestModel.getLatest())
                )
                .includeAllMetadata(true)
                .limit(slackRequestModel.getLimit()).build());
        if (!conversationsHistoryResponse.isOk()) {
          return messageList;
        }
        if (conversationsHistoryResponse.isHasMore()) {
          nextCursor = conversationsHistoryResponse.getResponseMetadata().getNextCursor();
        }
        hasMore = conversationsHistoryResponse.isHasMore();
        messageList.addAll(conversationsHistoryResponse.getMessages());
      } catch (Exception e) {
        // convert these exceptions to general exception
        WorkflowLogger.logError(
            "step=readSlackMessagesException", e);
        throw new WorkflowGeneralException(WorkflowError.SLACK_API_CALL_FAILURE, e.getMessage());
      }

    }
    return messageList;
  }

  /**
   * This method return the slack link of the conversation
   *
   * @param channelId
   * @param messageTs
   * @return
   */
  public String getMessageChatLink(String channelId, String messageTs) {
    MethodsClient methods = getMethodsClient();
    try {
      ChatGetPermalinkResponse chatGetPermalinkResponse = methods.chatGetPermalink(
          ChatGetPermalinkRequest.builder().token(vocJiraConfig.getSlackToken()).channel(channelId)
              .messageTs(messageTs).build());
      return chatGetPermalinkResponse.getPermalink();
    } catch (Exception e) {
      // in case of exception do nothing
      WorkflowLogger.logError(
          "step=messageChatLinkException", e);
    }
    return null;
  }

  private MethodsClient getMethodsClient() {
    return Slack.getInstance().methods(vocJiraConfig.getSlackToken());
  }

  private String getLatestUnixTimeStamp(String latest) {
    return latest == null ? DateUtils.getCurrentTimeStamp()
        : latest;
  }

}
