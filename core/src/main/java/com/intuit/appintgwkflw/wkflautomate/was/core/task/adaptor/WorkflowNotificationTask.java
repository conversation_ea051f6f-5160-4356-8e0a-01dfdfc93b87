package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.OINPAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers.OINPAdaptorMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> class is responsible for sending single OINP notification
 */
@AllArgsConstructor
@Component
public class WorkflowNotificationTask extends WorkflowTask<NotificationTask> {

  private OINPAdaptor oinpAdaptor;

  @Override
  public TypeReference<NotificationTask> typeReference() {
    return new TypeReference<NotificationTask>() {
    };
  }

  @Override
  public TaskType type() {
    return TaskType.NOTIFICATION_TASK;
  }

  /**
   * Invokes OINP to send batch notification
   *
   * @param notificationTask notification info
   * @return success or failure.
   */
  @Override
  public WorkflowTaskResponse create(NotificationTask notificationTask) {
    oinpAdaptor.sendEvent(
        OINPAdaptorMapper.mapOINPRequestData(notificationTask), getTaskConfig());
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_CREATED)
        .txnId(OINPAdaptorMapper.getIdempotencyKey(notificationTask)).build();
  }

  @Override
  public WorkflowTaskResponse update(NotificationTask notificationTask) {
    throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
  }

  /**
   * Simply ack the request with no action status
   *
   * @param notificationTask notification details
   * @return command status
   */
  @Override
  public WorkflowTaskResponse complete(NotificationTask notificationTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_COMPLETE).build();
  }

  @Override
  public WorkflowTaskResponse failed(NotificationTask notificationTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }

  @Override
  public WorkflowTaskResponse get(NotificationTask notificationTask) {
	  return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }
}
