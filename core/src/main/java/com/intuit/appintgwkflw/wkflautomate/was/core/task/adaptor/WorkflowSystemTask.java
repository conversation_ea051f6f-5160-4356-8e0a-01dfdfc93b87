package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.MISSING_HANDLER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ExternalTaskPublishHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.SystemTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.ExternalTaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Publishes external task assigned event
 */
@AllArgsConstructor
@Component
public class WorkflowSystemTask extends WorkflowTask<SystemTask> {

  private static final String LOG_PREFIX = "Executing command=WorkflowSystemTask ";

  private EventPublisherUtil eventPublisherUtil;
  private WASContextHandler wasContextHandler;
  private EventPublisherCapability eventPublisherCapability;
  private ProcessDetailsRepository processDetailsRepository;

  @Override
  public TypeReference<SystemTask> typeReference() {
    return new TypeReference<SystemTask>() {
    };
  }

  @Override
  public TaskType type() {
    return TaskType.SYSTEM_TASK;
  }

  @Override
  public WorkflowTaskResponse create(SystemTask systemTask) {

    ExternalTaskAttributes externalTaskAttributes = ExternalTaskAttributes.builder()
        .extensionAttributes(systemTask.getTaskAttributes().getModelAttributes())
        .variableMap(systemTask.getTaskAttributes().getVariables())
        .build();

    EventHeaderEntity eventHeaderEntity = buildEventHeader(systemTask, externalTaskAttributes);

    try {
      EventingLoggerUtil.logInfo(
          LOG_PREFIX + "type=%s step=eventPublishStarted processInstanceId=%s taskId=%s entityId=%s",
          this.getClass().getSimpleName(),
          type().name(),
          systemTask.getProcessInstanceId(),
          systemTask.getId(),
          eventHeaderEntity.getEntityId());
      // Send result can be used in the future for logging producer record and adding logic
      eventPublisherCapability
          .publish(eventHeaderEntity, buildEventPayload(systemTask, externalTaskAttributes));
    } catch (WorkflowEventException e) {
      // Log and Throw when WorkflowEventException is received
      EventingLoggerUtil.logError(
          LOG_PREFIX + "type=%s, step=eventPublishFailed processInstanceId=%s taskId=%s entityId=%s error=%s",
          this.getClass().getSimpleName(),
          type().name(),
          systemTask.getProcessInstanceId(),
          systemTask.getId(),
          eventHeaderEntity.getEntityId(),
          e);
      throw e;
    }

    EventingLoggerUtil.logInfo(
        LOG_PREFIX + "type=%s step=eventPublishSuccessful processInstanceId=%s taskId=%s entityId=%s",
        this.getClass().getSimpleName(),
        type().name(),
        systemTask.getProcessInstanceId(),
        systemTask.getId(),
        eventHeaderEntity.getEntityId());

    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_CREATED)
        .txnId(systemTask.getId()).build();
  }

  @Override
  public WorkflowTaskResponse update(SystemTask systemTask) {
    return WorkflowTaskResponse.builder().status(systemTask.getStatus()).build();
  }

  @Override
  public WorkflowTaskResponse complete(SystemTask systemTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_COMPLETE).build();
  }

  @Override
  public WorkflowTaskResponse failed(SystemTask systemTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }

  @Override
  public WorkflowTaskResponse get(SystemTask systemTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }

  /**
   * Build kafka headers
   * @param systemTask systemTask details
   * @param externalTaskAttributes contains external Task extensions and variableMap
   * @return eventHeaderEntity
   */
  private EventHeaderEntity buildEventHeader(SystemTask systemTask, ExternalTaskAttributes externalTaskAttributes) {

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OFFERING_ID, eventPublisherUtil.getOfferingId());
    headers.put(EventHeaderConstants.ENTITY_ID,
        ExternalTaskPublishHelper.transformEntityId(systemTask.getId(), systemTask.getWorkerId()));
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, systemTask.getId());
    headers.put(EventHeaderConstants.INTUIT_TID, wasContextHandler.get(WASContextEnums.INTUIT_TID));

    Optional<HandlerDetails> handlerDetails = WorkflowTaskUtil.getHandlerDetails(externalTaskAttributes);
    String handlerId = handlerDetails.map(HandlerDetails::getHandlerId).orElse(null);
    WorkflowVerfiy.verify(
        StringUtils.isBlank(handlerId),
        () -> {
          throw new WorkflowEventException(new WorkflowNonRetriableException(MISSING_HANDLER_ID));
        });
    headers.put(EventHeaderConstants.TARGET_ASSET_ALIAS, handlerId);
    String handlerScope = handlerDetails.map(HandlerDetails::getHandlerScope).orElse(null);
    return InternalEventsUtil.buildEventHeader(
        headers,
        PublishEventType.getPublishEventType(PublishEventType.EXTERNAL_TASK, handlerScope),
        EventEntityType.EXTERNALTASK);
  }

  /**
   * Build publish payload for ExternalTaskAssigned event topic
   * @param systemTask payload metadata
   * @param externalTaskAttributes contains external Task extensions and variableMap
   * @return ExternalTaskAssigned event.
   */
  private ExternalTaskAssigned buildEventPayload(SystemTask systemTask, ExternalTaskAttributes externalTaskAttributes){
    // Throw WorkflowRetriableException if process details are not available
    Optional<ProcessDetails> processDetailsOptional = processDetailsRepository
        .findByIdWithoutDefinitionData(systemTask.getProcessInstanceId());
    ProcessDetails processDetail = processDetailsOptional.orElseThrow(() ->
        new WorkflowRetriableException(PROCESS_DETAILS_NOT_FOUND_ERROR));

    return ExternalTaskPublishHelper.buildEventPayload(processDetail, externalTaskAttributes);
  }

}
