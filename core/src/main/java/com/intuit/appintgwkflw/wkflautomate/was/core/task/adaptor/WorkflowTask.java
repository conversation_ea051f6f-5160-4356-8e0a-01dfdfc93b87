package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4GraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WASClientType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Every new type of activity may have there own set of implementation to call relevant downstream
 * service.
 *
 * @param <REQUEST extends Task>
 * <AUTHOR>
 */
public abstract class WorkflowTask<REQUEST extends Task> {

  @Autowired
  private WorkflowTaskConfig workflowTaskConfig;

  @Autowired
  private WASHttpClient wasHttpClient;

  @Autowired
  private V4GraphqlClient v4GraphqlClient;

  /**
   * TaskConfig providing config related to downstream service. 
   * @return - WorkflowTaskConfigDetails
   */
  public WorkflowTaskConfigDetails getTaskConfig() {
    return workflowTaskConfig.getTaskConfig().get(type());
  }

  /**
   * Returns ClientAdaptor required by Task for integrating with downstream service.
   * @param <T>
   * @param type - WASClientType type of clientAdaptor required.
   * @return - V4GraphqlClient or WASHttpClient depending upon type requested.
   */
  @SuppressWarnings("unchecked")
  public <T> T getClient(WASClientType type) {
    if (WASClientType.GRAPHQL == type) {
      return (T) v4GraphqlClient;
    } else if (WASClientType.HTTP == type) {
      return (T) wasHttpClient;
    }
    return null;
  }

  public abstract TypeReference<REQUEST> typeReference();

  public abstract TaskType type();

  public abstract WorkflowTaskResponse create(REQUEST request);

  public abstract WorkflowTaskResponse update(REQUEST request);

  public abstract WorkflowTaskResponse complete(REQUEST request);

  public abstract WorkflowTaskResponse failed(REQUEST request);

  public abstract WorkflowTaskResponse get(REQUEST request);
  
  public String getWASActivityStatus(String status) {
	  return status;
  }

}