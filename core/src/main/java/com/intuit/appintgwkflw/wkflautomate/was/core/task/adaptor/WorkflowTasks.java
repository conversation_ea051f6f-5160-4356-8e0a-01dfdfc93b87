package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import java.util.EnumMap;
import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * This class contains a static variable which keeps a mapping of TaskType and
 * its related taskHandler. The map filled using Spring refresh context handler,
 * refer {ApplicationEventListener.class}
 * 
 * <AUTHOR>
 *
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowTasks {

	@SuppressWarnings("rawtypes")
	private static final Map<TaskType, WorkflowTask> WORKFLOW_TASK_TYPE_MAP = new EnumMap<>(TaskType.class);

	/**
	 * Adds WorkflowTask to the corresponding type.
	 * 
	 * @param type
	 * @param task
	 */
	@SuppressWarnings("rawtypes")
	public static void addWorkflowTask(TaskType type, WorkflowTask task) {

		WORKFLOW_TASK_TYPE_MAP.put(type, task);
	}

	/**
	 * 
	 * @param type - type of the task
	 * @return WorkflowTask - related Task Adaptor registered for the given type.
	 */
	@SuppressWarnings("rawtypes")
	public static WorkflowTask getWorkflowTask(TaskType type) {

		return WORKFLOW_TASK_TYPE_MAP.get(type);
	}

	/**
	 * Check whether the given taskType is registered or not.
	 * 
	 * @param type
	 * @return boolean.
	 */
	public static boolean contains(TaskType type) {

		return WORKFLOW_TASK_TYPE_MAP.containsKey(type);
	}

}
