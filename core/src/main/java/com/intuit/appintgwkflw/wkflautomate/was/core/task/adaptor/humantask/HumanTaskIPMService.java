package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.ProjectTaskAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers.IPMMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.v4.work.Project;
import com.intuit.v4.work.definitions.ExternalReference;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Contains the implementation of various human task lifecyle methods with Project
 *     service downstream
 */
@Component
@AllArgsConstructor
public class HumanTaskIPMService extends HumanTaskService {

  private ProjectTaskAdaptor projectTaskAdaptor;

  @Override
  public HumanTaskServiceType getServiceType() {
    return HumanTaskServiceType.PROJECT_SERVICE;
  }

  @Override
  public WorkflowTaskResponse create(HumanTask humanTask) {
    Project project = createOrUpdateHumanTask(humanTask, Operation.CREATE);
    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put(humanTask.getTxnVariable(), project.getId().toString());
    return WorkflowTaskResponse.builder()
        .txnId(project.getId().toString())
        .status(project.getStatus())
        .responseMap(responseMap)
        .build();
  }

  @Override
  public WorkflowTaskResponse update(HumanTask humanTask) {
    createOrUpdateHumanTask(humanTask, Operation.UPDATE);
    return WorkflowTaskResponse.builder()
        .txnId(humanTask.getTxnId())
        .status(humanTask.getStatus())
        .build();
  }

  @Override
  public WorkflowTaskResponse complete(HumanTask humanTask) {
    Project project = createOrUpdateHumanTask(humanTask, Operation.COMPLETE);
    return WorkflowTaskResponse.builder()
        .txnId(humanTask.getTxnId())
        .status(project.getStatus())
        .build();
  }

  /**
   * Makes Create or Update Call to ProjectService.
   *
   * @param humanTask - HumanTask request pojo.
   * @param operation - Create/Update and Complete Operation.
   * @return Project - project response.
   */
  private Project createOrUpdateHumanTask(HumanTask humanTask, Operation operation) {
    final Map<String, ExternalReference> additionalTaskReference =
        getAdditionalTaskReference(humanTask, operation);
    String ownerId = getOwnerId(humanTask);
    Project project =
        IPMMapper.mapProjectServiceData(
            humanTask, operation, contextHandler, additionalTaskReference, ownerId);
    Map<String, String> customHeader = prepareCustomHeader(humanTask);
    return projectTaskAdaptor.executeProject(
        project, customHeader, getTaskConfig(), operation, ownerId);
  }

  /**
   * Custom Header sent with request.
   *
   * @param humanTask - HumanTask request.
   * @return Map<String, String> - customHeader map.
   */
  Map<String, String> prepareCustomHeader(HumanTask humanTask) {
    final Map<String, String> customHeader = new HashMap<>();
    if (!StringUtils.isEmpty(humanTask.getApp())) {
      customHeader.put(TARGET_APP, humanTask.getApp());
    }
    if (!StringUtils.isEmpty(humanTask.getDomain())) {
      customHeader.put(TARGET_DOMAIN, humanTask.getDomain());
    }
    if (!StringUtils.isEmpty(humanTask.getUsecase())) {
      customHeader.put(TARGET_USECASE, humanTask.getUsecase());
    }
    return customHeader;
  }

  /**
   * Gets project additionalTask externalRef propertybag to amend the same.
   *
   * @param humanTask - HumanTask request.
   * @param operation - Operation Create/Update.
   * @return
   */
  Map<String, ExternalReference> getAdditionalTaskReference(
      HumanTask humanTask, Operation operation) {
    Set<String> additionalTaskAttributeKeys = additionalTaskAttributes(humanTask);
    final Map<String, ExternalReference> additionalTaskAttribute = new HashMap<>();
    if (!Operation.CREATE.equals(operation) && !additionalTaskAttributeKeys.isEmpty()) {
      final WorkflowTaskResponse projectResponse = get(humanTask);
      final Project project = (Project) projectResponse.getResponse();
      project.getExternalReferences().stream()
          .filter(
              externalRef ->
                  ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES.equals(externalRef.getExternalKey()))
          .findAny()
          .ifPresent(
              externalRef ->
                  additionalTaskAttribute.put(externalRef.getExternalKey(), externalRef));
      humanTask.setTaskName(project.getName());
    }
    return additionalTaskAttribute;
  }

  /**
   * Returns additional task attribute key set present in taskRequest.variables
   *
   * @param humanTask
   * @return
   */
  private Set<String> additionalTaskAttributes(HumanTask humanTask) {
    Map<String, Object> taskMap =
        ObjectConverter.convertObject(humanTask, new TypeReference<Map<String, Object>>() {});
    Set<String> attributesKeySet =
        new HashSet<>(
            Optional.ofNullable(humanTask.getTaskAttributes().getVariables())
                .orElse(new HashMap<>())
                .keySet());
    attributesKeySet.removeAll(taskMap.keySet());
    return attributesKeySet;
  }

  @Override
  public WorkflowTaskResponse failed(HumanTask humanTask) {
    createOrUpdateHumanTask(humanTask, Operation.FAILED);
    return WorkflowTaskResponse.builder()
        .txnId(humanTask.getTxnId())
        .status(humanTask.getStatus())
        .build();
  }

  @Override
  public WorkflowTaskResponse get(HumanTask humanTask) {
    Map<String, Object> responseMap = new HashMap<>();
    Map<String, String> customHeader = prepareCustomHeader(humanTask);
    Project project =
        projectTaskAdaptor.readProjectByIdempotencyId(
            humanTask.getId(), getTaskConfig(), getOwnerId(humanTask), customHeader);
    if (project != null) {
      responseMap.put(humanTask.getTxnVariable(), project.getId().toString());
      return WorkflowTaskResponse.builder()
          .txnId(project.getId().toString())
          .status(project.getStatus())
          .response(project)
          .responseMap(responseMap)
          .build();
    }
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }
}
