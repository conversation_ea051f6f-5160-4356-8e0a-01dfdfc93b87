package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.INTUIT_HEADER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.ORIGINATING_ASSETALIAS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TARGET_DOMAIN;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TARGET_REALM;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TARGET_USECASE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_COMPLETE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.ITMTaskAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers.ITMMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Contains the implementation of various human task lifecyle methods with ITM
 *     service (Task Service) downstream
 */
@Component
@AllArgsConstructor
public class HumanTaskITMService extends HumanTaskService {

  private ITMTaskAdaptor itmTaskAdaptor;

  @Override
  public HumanTaskServiceType getServiceType() {
    return HumanTaskServiceType.TASK_SERVICE;
  }

  @Override
  public WorkflowTaskResponse create(HumanTask humanTask) {
    String ownerId = getOwnerId(humanTask);
    TaskManagement_CreateTaskInput taskManagementCreateTaskInput =
        ITMMapper.createITMCreateTaskInput(humanTask);

    TaskManagementCreateTaskMutation.Task createdITMTask =
        itmTaskAdaptor.createTask(
            taskManagementCreateTaskInput, prepareCustomHeader(humanTask), ownerId);

    Map<String, Object> responseMap = new HashMap<>();

    responseMap.put(humanTask.getTxnVariable(), createdITMTask.id().toString());

    return WorkflowTaskResponse.builder()
        .txnId(createdITMTask.id().toString())
        .status(createdITMTask.status())
        .responseMap(responseMap)
        .build();
  }

  @Override
  public WorkflowTaskResponse update(HumanTask humanTask) {
    String ownerId = getOwnerId(humanTask);
    TaskManagement_UpdateTaskInput taskManagementUpdateTaskInput =
        ITMMapper.createITMUpdateTaskInput(humanTask);

    TaskManagementUpdateTaskMutation.Task updatedITMTask =
        itmTaskAdaptor.updateTask(
            taskManagementUpdateTaskInput, prepareCustomHeader(humanTask), ownerId);

    return WorkflowTaskResponse.builder()
        .txnId(updatedITMTask.id().toString())
        .status(updatedITMTask.status())
        .build();
  }

  @Override
  public WorkflowTaskResponse complete(HumanTask humanTask) {
    String ownerId = getOwnerId(humanTask);

    TaskManagementUpdateTaskMutation.Task completedITMTask =
        itmTaskAdaptor.updateTask(
            TaskManagement_UpdateTaskInput.builder()
                .id(humanTask.getTxnId())
                .status(ITM_STATUS_COMPLETE)
                .build(),
            prepareCustomHeader(humanTask),
            ownerId);

    return WorkflowTaskResponse.builder()
        .txnId(completedITMTask.id().toString())
        .status(completedITMTask.status())
        .build();
  }

  /**
   * Custom Header sent with request.
   *
   * @param humanTask - HumanTask request.
   * @return Map<String, String> - customHeader map.
   */
  private Map<String, String> prepareCustomHeader(HumanTask humanTask) {
    final Map<String, String> customHeader = new HashMap<>();

    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(humanTask.getTaskAttributes().getModelAttributes())
            || StringUtils.isEmpty(
                humanTask
                    .getTaskAttributes()
                    .getModelAttributes()
                    .get(ActivityConstants.ORIGINATING_ASSET_ALIAS)),
        WorkflowError.ITM_REQUEST_HEADERS_MISSING,
        ActivityConstants.ORIGINATING_ASSET_ALIAS);
    customHeader.put(
            INTUIT_HEADER + ORIGINATING_ASSETALIAS,
        humanTask
            .getTaskAttributes()
            .getModelAttributes()
            .get(ActivityConstants.ORIGINATING_ASSET_ALIAS));

    WorkflowVerfiy.verify(
        StringUtils.isEmpty(humanTask.getDomain()),
        WorkflowError.ITM_REQUEST_HEADERS_MISSING,
            INTUIT_HEADER + TARGET_DOMAIN);
    customHeader.put(INTUIT_HEADER + TARGET_DOMAIN, humanTask.getDomain());

    WorkflowVerfiy.verify(
        StringUtils.isEmpty(humanTask.getUsecase()),
        WorkflowError.ITM_REQUEST_HEADERS_MISSING,
            INTUIT_HEADER + TARGET_USECASE);
    customHeader.put(INTUIT_HEADER + TARGET_USECASE, humanTask.getUsecase());

    String ownerId = getOwnerId(humanTask);
    WorkflowVerfiy.verify(
        StringUtils.isEmpty(ownerId), WorkflowError.ITM_REQUEST_HEADERS_MISSING, INTUIT_HEADER + TARGET_REALM);
    customHeader.put(INTUIT_HEADER + TARGET_REALM, ownerId);
    return customHeader;
  }

  @Override
  public WorkflowTaskResponse failed(HumanTask humanTask) {
    return null;
  }

  @Override
  public WorkflowTaskResponse get(HumanTask humanTask) {
    Map<String, Object> responseMap = new HashMap<>();
    TaskManagementTaskQuery.TaskManagementTask itmTask =
        itmTaskAdaptor.getTask(
            humanTask.getTxnId(), prepareCustomHeader(humanTask), getOwnerId(humanTask));
    if (Objects.nonNull(itmTask)) {
      responseMap.put(humanTask.getTxnVariable(), itmTask.id().toString());
      return WorkflowTaskResponse.builder()
          .txnId(itmTask.id().toString())
          .status(itmTask.status())
          .response(itmTask)
          .responseMap(responseMap)
          .build();
    }
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }
}
