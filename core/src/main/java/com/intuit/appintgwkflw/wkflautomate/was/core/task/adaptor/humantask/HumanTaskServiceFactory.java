package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Factory to choose which downstream service to choose for a human task request
 */
@Component
@AllArgsConstructor
public class HumanTaskServiceFactory {

  private HumanTaskIPMService humanTaskIPMService;

  private HumanTaskITMService humanTaskITMService;

  /**
   * Would return which downstream service to use for execution of a provided human task
   * @param humanTask : Human task for which the downstream service is to be found for
   * @return : Human task downstream service which would execute the provided human task
   */
  public HumanTaskService getService(HumanTask humanTask) {
    if (humanTask.isProject()) {
      return humanTaskIPMService;
    }
    return humanTaskITMService;
  }
}
