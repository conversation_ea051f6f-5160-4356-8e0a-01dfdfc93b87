package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;


import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StateTransitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Base class for different type of command to support. Each command has specific structure to
 * follow, hence different strategy for each command needs to be followed.
 *
 * <AUTHOR>
 */

public abstract class WorkflowTaskCommand {

  @Autowired
  private EventPublisherCapability eventPublisherCapability;

  @Autowired
  private WASContextHandler contextHandler;

  @Autowired
  private PublishEventHandler publishEventHandler;
  
  @Autowired
  protected StateTransitionService stateTransitionService;

  /**
   * @return Specific Command for execution.
   */
  public abstract TaskCommand command();

  /**
   * Executes the Command.
   *
   * @param taskRequest - Details of task on which the operation has to occur.
   */
  public abstract WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest);

  /**
   * This method converts taskRequest into corresponding TaskAdaptor admissible request.
   *
   * @param taskRequest - WorkflowTaskRequest having details of task and command to execute.
   * @return
   */
  public Task prepareTaskRequest(WorkflowTaskRequest taskRequest) {
    Map<String, Object> taskRequestMap = prepareTaskRequestMap(taskRequest);
    Task task = ObjectConverter
        .convertObject(taskRequestMap,
            WorkflowTaskUtil.taskTypeReference(taskRequest.getTaskType()));
    if (!isCreateCommand(taskRequest)) {
    	/**
    	 * Adds mandatory params to Task which are required for downstream request. 
    	 */
    	task.addMandatoryParams();
    }
    if (!task.isValid()) {
    	/**
    	 * Checks the task have all mandatory params and is Valid. 
    	 */
    	throw new WorkflowGeneralException(WorkflowError.MANDATORY_TASK_PARAMS_MISSING);
    }
    return task;
  }

  /**
   * Converting WorkflowTaskRequest to map removing unwanted keys causing issue to cast to Task.
   *
   * @param taskRequest - WorkflowTaskRequest having details of task and command to execute.
   * @return - Map<String,Object> converted map of taskRequest.
   */
  private Map<String, Object> prepareTaskRequestMap(WorkflowTaskRequest taskRequest) {
    Task task = Task.builder().id(taskRequest.getId()).activityId(taskRequest.getActivityId())
        .txnId(taskRequest.getTxnId()).processInstanceId(taskRequest.getProcessInstanceId())
        .activityName(taskRequest.getActivityName()).type(taskRequest.getTaskType())
        .activityType(taskRequest.getActivityType())
        .status(taskRequest.getStatus()).recordId(taskRequest.getRecordId())
        .workerId(taskRequest.getWorkerId())
        .taskAttributes(taskRequest.getTaskAttributes()).build();

    Map<String, Object> taskRequestMap = ObjectConverter.convertObject(task,
        new TypeReference<Map<String, Object>>() {
        });
    if (isCreateCommand(taskRequest)) {
      taskRequestMap.putAll(taskRequest.getTaskAttributes().getModelAttributes());
    }
    if (MapUtils.isNotEmpty(taskRequest.getTaskAttributes().getVariables())) {
      taskRequestMap.putAll(filterKeyVariables(taskRequest.getTaskAttributes().getVariables()));
    }
    return taskRequestMap;
  }
  
	/**
	 * Filter Task primary fields. In Approvals, for Single Definition BPMN,
	 * activityId is set based on callActivity Elements it flow after evaluation of
	 * decision.
	 *  activityId = sendForApproval-2 
	 * The current ActivityId (sendNotification in case of Non-QBO Approvals) 
	 * set in Task shouldn't get override. 
	 * 
	 * This overriding makes DB reference failure.
	 * 
	 * Hence, keys as such "activityId", "id", "activtyName" and "type" in
	 * the processVariables need to be filtered. 
	 * 
	 * @param variables
	 * @return
	 */
  private Map<String, Object> filterKeyVariables(Map<String, Object> variables){
	  /**
	   * Cloned Map to not make impact on original Map.
	   */
	  Map<String, Object> filteredVariables = new HashMap<>(variables);
	  filteredVariables.remove(ActivityConstants.ACTIVITY_CONSTANTS_TASK_ID);
	  filteredVariables.remove(ActivityConstants.ACTIVITY_CONSTANTS_TASK_ACTIVITY_ID);
	  filteredVariables.remove(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME);
	  filteredVariables.remove(ActivityConstants.TASK_TYPE);
	  return filteredVariables;
  }

  /**
   * Checks if Request is for Create Command and ModelAttributes are present.
   * @param taskRequest
   * @return
   */
  private boolean isCreateCommand(WorkflowTaskRequest taskRequest) {
	return TaskCommand.CREATE.equals(command()) &&
        MapUtils.isNotEmpty(taskRequest.getTaskAttributes().getModelAttributes());
  }


  /**
   * Check for corresponding flag and publish event.
   *
   * @param taskRequest    : Command Request POJO having publish flags.
   * @param processDetails : Keeps processDetail info.
   */
  protected void checkAndPublish(WorkflowTaskRequest taskRequest, ProcessDetails processDetails) {
    if (taskRequest.isPublishExternalTaskEvent()) {
      //published External Task Event.
      publish(taskRequest, processDetails, PublishEventType.EXTERNAL_TASK);
    }
    if (taskRequest.isPublishWorkflowStateTransitionEvent() &&
        StateTransitionServiceHelper
            .isStateTransitionEventPublishEnabled(InternalEventsUtil.commandToEventType(command()),
                taskRequest.getTaskAttributes().getModelAttributes())) {
      //published Workflow State Transition Event.
      publish(taskRequest, processDetails,
          PublishEventType.WORKFLOW_TRANSITION_EVENTS);
    }
  }

  /**
   * Publish transition or external task create.
   *
   * @param taskRequest    - Details of Workflow Task executed.
   * @param processDetails - Workflow Process details.
   * @param publishEvent   - Type of Event Type :: Workflow State Transition or ExternalTask
   *                       Create.
   */
  protected void publish(WorkflowTaskRequest taskRequest, ProcessDetails processDetails,
      PublishEventType publishEvent) {

    final String handlerScope = WorkflowTaskUtil.getHandlerScope(taskRequest.getTaskAttributes());

    PublishEventType publishEventType = PublishEventType
        .getPublishEventType(publishEvent, handlerScope);

    EventHeaderEntity eventHeaderEntity = InternalEventsUtil
        .buildEventHeader(taskRequest, processDetails,
            contextHandler, publishEventType);

    if (PublishEventType.EXTERNAL_TASK.getType().equals(publishEventType.getType())) {
      Map<String, String> inputVariables = new HashMap<>();
      inputVariables.put(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE,
          taskRequest.getTxnId());
      WorkerActionRequest workerActionRequest = WorkerActionRequest.builder()
          .processInstanceId(taskRequest.getProcessInstanceId())
          .taskId(taskRequest.getId())
          .workerId(taskRequest.getWorkerId())
          .variableMap(new VariableMapImpl(taskRequest.getTaskAttributes().getVariables()))
          .inputVariables(inputVariables)
          .extensionProperties(taskRequest.getTaskAttributes().getModelAttributes()).build();

      eventPublisherCapability.publish(eventHeaderEntity,
          publishEventHandler.buildEventPayload(workerActionRequest, processDetails));
    } else if (PublishEventType.WORKFLOW_TRANSITION_EVENTS.equals(publishEventType)) {
      eventPublisherCapability.publish(eventHeaderEntity,
          InternalEventsUtil.buildTaskStateTransitionEventPayload(taskRequest,
        		  processDetails, Instant.now().toEpochMilli()));
    } else {
      throw new WorkflowGeneralException(WorkflowError.INVALID_EVENT_CONFIG_ERROR);
    }
  }

}