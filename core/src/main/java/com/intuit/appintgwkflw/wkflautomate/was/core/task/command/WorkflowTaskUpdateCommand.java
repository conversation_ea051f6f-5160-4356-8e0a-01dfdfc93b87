package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Update Command is responsible to update entry of transaction and activity in DB and Camunda to
 * maintain state and update downstream accordingly.
 *
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WorkflowTaskUpdateCommand extends WorkflowTaskCommand {

  public TaskCommand command() {
    return TaskCommand.UPDATE;
  }

  private ActivityProgressDetailsRepository progressDetailRepo;

  private WorkflowTaskDBOperationManager dbOperationManager;

  @SuppressWarnings({"unchecked"})
  @Override
  public WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest) {

    ActivityProgressDetails activityProgressDetails = progressDetailRepo
        .findById(taskRequest.getId())
        .orElseThrow(() -> new WorkflowNonRetriableException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));

    Task task = prepareTaskRequest(taskRequest);
    WorkflowTaskResponse response = WorkflowTasks.getWorkflowTask(task.getType()).update(task);

    // Update status in Transaction and Activityprogress DB.
    dbOperationManager.updateStatusInDB(task, activityProgressDetails);

    // Publish event of task update.
    checkAndPublish(taskRequest, activityProgressDetails.getProcessDetails());
    return response;

  }

}
