package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.incident.WorkflowIncident;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class IncidentTaskManager {

  private ActivityDetailsRepository activityDetailRepo;
  private WorkflowExternalTaskManager workflowTaskManager;
  private WorkflowTaskConfig workflowTaskConfig;
  private WorkflowCustomTaskHelper customTaskHelper;

  /**
   * Check for ProcessInstance and activityDetail for processing Failed Command for Custom Task
   * Framework. This flow is used to maintain the failure lifecycle of the Custom Task Framework. If
   * task type is defined other than value=MILESTONE then activity details status would be updated in the
   * database otherwise nothing is saved in the DB.
   *
   * @param workflowIncident - Incident payload.
   * @param headers - Incident event header.
   * @param processDetails - ProcessDetails record from DB.
   */
  public void execute(
      WorkflowIncident workflowIncident,
      Map<String, String> headers,
      ProcessDetails processDetails) {
    final String processInstanceId = workflowIncident.getWorkflowMetadata().getProcessInstanceId();
    try {

      if (!workflowTaskConfig.isEnable()
          || StringUtils.isEmpty(workflowIncident.getExternalTaskId())) {
        EventingLoggerUtil.logInfo(
            "Incident processing, CustomTaskHandle disabled  processInstanceId=%s.",
            this.getClass().getSimpleName(), processInstanceId);
        return;
      }
      final String taskId = workflowIncident.getExternalTaskId();
      activityDetailRepo
          .findByTemplateDetailsAndActivityId(
              processDetails.getDefinitionDetails().getTemplateDetails(),
              workflowIncident.getActivityId())
          .filter(activityDetail -> Objects.nonNull(activityDetail.getType()) && !TaskType.MILESTONE.equals(activityDetail.getType()))
          .ifPresentOrElse(
              activityDetail -> {
                EventingLoggerUtil.logInfo(
                    "workflowTaskFailedCommand step=start processInstanceId=%s taskId=%s ",
                    this.getClass().getSimpleName(), processInstanceId, taskId);

                prepareFailedCommandAndExecute(
                    taskId, activityDetail, processDetails, workflowIncident);

                EventingLoggerUtil.logInfo(
                    "workflowTaskFailedCommand step=complete processInstanceId=%s taskId=%s ",
                    this.getClass().getSimpleName(), processInstanceId, taskId);
              },
              () ->
                  EventingLoggerUtil.logInfo(
                      "workflowTaskFailedCommand step=noack processInstanceId=%s taskId=%s not found",
                      this.getClass().getSimpleName(), processInstanceId, taskId));

    } catch (Exception ex) {
      // SplunkLog Alert on this.
      EventingLoggerUtil
          .logError("Incident customTask handle failure. processInstanceId=%s", ex,
              this.getClass().getSimpleName(), processInstanceId);
    }
  }

  /**
   * Prepares WorkflowTaskRequest using info available in WorkflowIncident and IncidentHeader. Then
   * call for execution of failed command.
   *
   * @param taskId           - ExternalTaskId
   * @param activityDetail   - Activity Definition from DB.
   * @param processDetails   - Workflow record from DB.
   * @param workflowIncident
   */
  private void prepareFailedCommandAndExecute(String taskId, ActivityDetail activityDetail,
      ProcessDetails processDetails, WorkflowIncident workflowIncident) {

    WorkflowActivityAttributes activityAttributes = WorkflowTaskUtil
        .getActivityAttributes(activityDetail);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder = WorkflowTaskRequest
        .builder().id(taskId)
        .processInstanceId(processDetails.getProcessId()).activityId(activityDetail.getActivityId())
        .activityName(activityDetail.getActivityName()).taskType(activityDetail.getType())
        .activityType(activityDetail.getActivityType())
        .skipTxnDBUpdate(
            null == WorkflowError.value(workflowIncident.getIncidentMsg())
                .getDownstreamComponentName())
        .taskAttributes(TaskAttributes.builder().modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(workflowIncident.getVariables())
        		.build());
    executeCommand(taskRequestBuilder);
  }

  /**
   * Add actions and command info on TaskRequest and execute.
   *
   * @param taskRequestBuilder
   */
  private void executeCommand(WorkflowTaskRequest.WorkflowTaskRequestBuilder taskRequestBuilder) {
    WorkflowTaskRequest taskRequest = taskRequestBuilder.command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED).publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(true)
        .skipCallback(true) // SkipCallback is true. No DownstreamAdaptor to invoke.
        .build();
    workflowTaskManager.execute(taskRequest);
  }

  /**
   * Execute failed command when fatal = false.
   *
   * @param workerActionRequest - Worker Request used by handlers.
   */
  public void executeFailedCommand(WorkerActionRequest workerActionRequest) {
    if (!workflowTaskConfig.isEnable()) {
      return;
    }
    try {
      Optional
          .ofNullable(customTaskHelper.prepareWorkflowTaskRequestForIncident(workerActionRequest))
          .ifPresent(this::executeCommand);
    } catch (Exception ex) {
      /**
       *  We need to swallow the exception.
       */
      EventingLoggerUtil
          .logError("Failed Command execution failure. taskId=%s and processInstanceId=%s", ex,
              this.getClass().getSimpleName(), workerActionRequest.getTaskId(),
              workerActionRequest.getProcessInstanceId());
    }
  }

}