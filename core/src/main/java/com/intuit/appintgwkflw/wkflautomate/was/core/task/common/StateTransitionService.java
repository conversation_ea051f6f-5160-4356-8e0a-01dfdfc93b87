package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;


import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StateTransitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class StateTransitionService {

  private final CamundaRunTimeServiceRest camundaRest;

  private final CamundaHistoryServiceRest camundaHistoryRest;

  private final WorkflowTaskConfig workflowTaskConfig;

  private EventPublisherCapability eventPublishCapability;

  private WASContextHandler wasContextHandler;

  /**
   * Variables are fetched for provided executionId and processInstanceId
   *
   * @param executionId - ExecutionId of the process, externalTask etc.
   * @param processId   - ProcessInstanceId of Workflow.
   * @return variables typed Map<String,Object>
   */
  public Map<String, Object> getExternalTaskVariable(final String executionId,
      final String processId) {
    Map<String, Object> externalTaskVariable = new HashMap<>();
    ProcessVariableDetailsRequest processVariableDetailsRequest =
        ProcessVariableDetailsRequest.builder().executionIdIn(new String[]{executionId, processId})
            .processInstanceId(processId)
            .maxResults(workflowTaskConfig.getStateTransitionConfig().getMaxResult())
            .deserializeValues(true).build();

    WASHttpResponse<List<ProcessVariableDetailsResponse>> response =
        camundaHistoryRest.getProcessVariableDetails(processVariableDetailsRequest);
    Optional.ofNullable(response.getResponse()).ifPresent(processVariableDetails -> {

      Map<String, List<ProcessVariableDetailsResponse>> executionProcessVariableMap =
          processVariableDetails.stream()
              .collect(Collectors.groupingBy(ProcessVariableDetailsResponse::getExecutionId));

      externalTaskVariable.putAll(Optional.ofNullable(executionProcessVariableMap.get(processId))
          .map(processVariables ->
              processVariables.stream().collect(Collectors
                  .toMap(ProcessVariableDetailsResponse::getName,
                      ProcessVariableDetailsResponse::getValue)))
          .orElse(Collections.emptyMap()));

      externalTaskVariable.putAll(Optional.ofNullable(executionProcessVariableMap.get(executionId))
          .map(executionVariables ->
              executionVariables.stream().collect(Collectors
                  .toMap(ProcessVariableDetailsResponse::getName,
                      ProcessVariableDetailsResponse::getValue)))
          .orElse(Collections.emptyMap()));
    });

    return externalTaskVariable;
  }


  /**
   * Variables are fetched for provided externalTaskId
   *
   * @param externalTaskId - external Task Id.
   * @return variables typed Map<String,Object>
   */
  public Map<String, Object> getExternalTaskVariable(final String externalTaskId) {
    ExternalTaskDetail externalTaskDetail = camundaRest.getExtenalTaskDetails(externalTaskId);
    return getExternalTaskVariable(externalTaskDetail.getExecutionId(),
        externalTaskDetail.getProcessInstanceId());

  }

  /**
   * Checks if activityDetail record present in DB and publish.
   *
   * @param activityProgressDetail :: Activity DB instance.
   * @param headers        :: headers map to prepare EventHeader.
   * @param externalTaskId ::  To Fetch ExternalTask variables.
   * @param transitionTime :: time on which Camunda API was invoked for transition of task.
   */
  @Metric(name = MetricName.EVENT_WORKFLOW_TRANSITION_EVENT, type = Type.EVENT_METRIC)
  public void publishCompleteEvent(ActivityProgressDetails activityProgressDetail,
    Map<String, String> headers, String externalTaskId, Long transitionTime) {
      WASHttpResponse<List<ExternalTaskLog>> wasResponse = camundaHistoryRest
          .getExternalTaskLogs(ExternalTaskLogRequest.builder()
        	  .processInstanceId(activityProgressDetail.getProcessDetails().getProcessId())
              .maxResults(1).externalTaskId(externalTaskId).build());
      wasResponse.getResponse().stream().findFirst()
          .map(externalTaskLog ->
              ExternalTaskDetail.builder().executionId(externalTaskId)
                  .executionId(externalTaskLog.getExecutionId())
                  .processDefinitionId(externalTaskLog.getProcessDefinitionId())
                  .processInstanceId(externalTaskLog.getProcessInstanceId()).build())
          .ifPresent(externalTaskDetail ->
              publishStateTransitionEvent(headers,
                  externalTaskDetail, activityProgressDetail,
                  ActivityConstants.TASK_STATUS_COMPLETE, transitionTime));
  }


  /**
   * Checks if activityDetail record present in DB and publish.
   *
   * @param headers            :: headers map to prepare EventHeader.
   * @param externalTaskDetail ::  To Fetch ExternalTask variables.
   * @param transitionTime     :: time on which Camunda API was invoked for transition of task.
   */
  @Metric(name = MetricName.EVENT_WORKFLOW_TRANSITION_EVENT, type = Type.EVENT_METRIC)
  public void publishUpdateEvent(Map<String, String> headers,
      ExternalTaskDetail externalTaskDetail, ActivityProgressDetails activityProgressDetail, 
      Long transitionTime) {
      publishStateTransitionEvent(headers, externalTaskDetail, activityProgressDetail,
            ActivityConstants.TASK_EVENT_TYPE_UPDATE, transitionTime);
  }


  /**
   * Checks if state Transition is enabled for the given event type
   *
   * @param activityProgressDtl :: DB Record of Activity.
   * @param event               :: Event for state transition.
   * @return typed boolean.
   */
  public boolean isStateTransitionEnabled(ActivityProgressDetails activityProgressDtl,
      String event) {
    /**
     * Checks state transition event enabled.
     */
    WorkflowActivityAttributes activityAttributes = ObjectConverter
        .fromJson(activityProgressDtl.getActivityDefinitionDetail().getAttributes(),
            new TypeReference<WorkflowActivityAttributes>() {
            });
    return StateTransitionServiceHelper
        .isStateTransitionEventPublishEnabled(event,
            activityAttributes.getModelAttributes());
  }

  /**
   * Prepares an publishes State Transition update event.
   *
   * @param headers                :: headers map to prepare EventHeader.
   * @param externalTaskDetail     :: To Fetch ExternalTask variables.
   * @param activityProgressDetail :: Task details in DB.
   * @param eventType              :: eventType for which stateTransition event is to publish.
   * @param transitionTime         :: time on which Camunda API was invoked for transition of task.
   */
  public void publishStateTransitionEvent(Map<String, String> headers,
      ExternalTaskDetail externalTaskDetail,
      ActivityProgressDetails activityProgressDetail, String eventType,
      Long transitionTime) {
	wasContextHandler.addKey(WASContextEnums.EVENT_TYPE, eventType);
	wasContextHandler.addKey(WASContextEnums.WORKFLOW, activityProgressDetail.getProcessDetails()
			.getDefinitionDetails().getTemplateDetails().getTemplateName());
    Map<String, Object> camundaVariableMap = getExternalTaskVariable(
        externalTaskDetail.getExecutionId(),
        externalTaskDetail.getProcessInstanceId());

    eventPublishCapability.publish(
        StateTransitionServiceHelper
            .eventHeaderEntity(externalTaskDetail.getExecutionId(), headers),
        StateTransitionServiceHelper.prepareWorkflowStateTransitionEvent(externalTaskDetail,
            activityProgressDetail, camundaVariableMap, eventType, transitionTime));
  }


  /**
   * Check and Publish State Transition event using WorkflowTaskRequest
   *
   * @param taskRequest    :: Task request from ExternalTaskFramework.
   * @param processDetails :: Process Details from DB.
   * @param eventType      :: Event type.
   */
  @Metric(name = MetricName.EVENT_WORKFLOW_TRANSITION_EVENT, type = Type.EVENT_METRIC)
  public void publishEvent(WorkflowTaskRequest taskRequest, ProcessDetails processDetails,
		  String eventType) {
    wasContextHandler.addKey(WASContextEnums.EVENT_TYPE, eventType);
    wasContextHandler.addKey(WASContextEnums.WORKFLOW, processDetails.getDefinitionDetails()
    		.getTemplateDetails().getTemplateName());
    final String handlerScope = WorkflowTaskUtil.getHandlerScope(taskRequest.getTaskAttributes());
    EventHeaderEntity eventHeaderEntity = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
           wasContextHandler,
           PublishEventType
              .getPublishEventType(PublishEventType.WORKFLOW_TRANSITION_EVENTS, handlerScope));
    eventPublishCapability.publish(eventHeaderEntity,
          InternalEventsUtil.buildTaskStateTransitionEventPayload(taskRequest, processDetails, Instant.now().toEpochMilli()));
  }

}