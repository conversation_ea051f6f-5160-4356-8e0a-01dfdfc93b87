package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.EventHandlerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.TXN_DETAILS_NOT_FOUND_FOR_ACTIVITY_ID;

/**
 * Helper for CustomTask processing.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WorkflowCustomTaskHelper {

  private ProcessDetailsRepository processDetailRepo;

  private ActivityDetailsRepository activityDetailRepo;

  private ActivityProgressDetailsRepository progressDetailRepo;

  private WorkflowExternalTaskManager workflowTaskManager;

  private WorkflowTaskConfig workflowTaskConfig;
  
  private WASContextHandler wasContextHandler;

  /**
   * Returns WorkflowTaskRequest created from WorkerActionRequest without specifiying command,
   * status and actions to take care of.
   * <p>
   * different commands.
   *
   * @param workerActionRequest
   * @return
   */
  public WorkflowTaskRequest.WorkflowTaskRequestBuilder prepareWorkflowTaskRequest(
      WorkerActionRequest workerActionRequest) {
    ProcessDetails processDetails = fetchWorkflowDetails(workerActionRequest).orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR));
    ActivityDetail activityDetail = getActivityDetail(workerActionRequest, processDetails)
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));
    return prepareWorkflowTaskRequest(workerActionRequest, processDetails, activityDetail);
  }

  /**
   * Returns WorkflowTaskRequest created from WorkerActionRequest without specifying command, status
   * and actions to take care of.
   *
   * <p>It filters Milestone Events
   *
   * <p>
   *
   * @param workerActionRequest
   * @return
   */
  public WorkflowTaskRequest.WorkflowTaskRequestBuilder prepareWorkflowTaskRequestForIncident(
      WorkerActionRequest workerActionRequest) {
    return fetchWorkflowDetails(workerActionRequest)
        .map(
            processDetails ->
                getActivityDetail(workerActionRequest, processDetails)
                    .filter(
                        activityDetail ->
                            Objects.nonNull(activityDetail.getType())
                                && !TaskType.MILESTONE.equals(activityDetail.getType()))
                    .map(
                        activityDetail ->
                            prepareWorkflowTaskRequest(
                                workerActionRequest, processDetails, activityDetail))
                    .orElse(null))
        .orElse(null);
  }

  /**
   * Returns WorkflowTaskRequest created from WorkerActionRequest without specifiying command,
   * status and actions to take care of.
   *
   * @param workerActionRequest
   * @param processDetails
   * @param activityDetail
   * @return
   */
  private WorkflowTaskRequest.WorkflowTaskRequestBuilder prepareWorkflowTaskRequest(
      WorkerActionRequest workerActionRequest,
      final ProcessDetails processDetails, final ActivityDetail activityDetail) {
	  
    WorkflowActivityAttributes activityAttributes = WorkflowTaskUtil
        .getActivityAttributes(activityDetail);
    
    TaskAttributes taskAttributes = 
    		TaskAttributes.builder().variables(workerActionRequest.getVariableMap())
    		.modelAttributes(activityAttributes.getModelAttributes())
    		.runtimeAttributes(activityAttributes.getRuntimeAttributes()).build(); 
    
    return WorkflowTaskRequest.builder()
        .processInstanceId(workerActionRequest.getProcessInstanceId())
        .id(workerActionRequest.getTaskId())
        .activityId(activityDetail.getActivityId()).activityName(activityDetail.getActivityName())
        .activityType(activityDetail.getActivityType())
        .taskType(activityDetail.getType())
        .workerId(workerActionRequest.getWorkerId())
        .recordId(processDetails.getRecordId())
        .taskAttributes(taskAttributes);
  }
  

  /**
   * @param workerActionRequest - Worker Request POJO.
   * @return runtimeAttributes Map<String,Object>
   */
  private Optional<ProcessDetails> fetchWorkflowDetails(WorkerActionRequest workerActionRequest) {
    return processDetailRepo
        .findById(workerActionRequest.getProcessInstanceId());
        
  }

  /**
   * @param workerActionRequest - Worker Request POJO.
   * @return ActivityDetail - definition level info.
   */
  private Optional<ActivityDetail> getActivityDetail(WorkerActionRequest workerActionRequest,
      ProcessDetails processDetails) {

    TemplateDetails templateDetails = processDetails.getDefinitionDetails().getTemplateDetails();
    return activityDetailRepo
        .findByTemplateDetailsAndActivityId(templateDetails, workerActionRequest.getActivityId());
        
  }


  /**
   * Processes event. Checks if it is a CustomTask and if true, processes the same in DB and
   * Downstream through WorkflowTaskManager.
   *
   * @param event   - Event Received through event Bus.
   * @param headers - Event headers received.
   */
  public void checkAndProcessCustomTask(ExternalTaskCompleted event, Map<String, String> headers,
      EventEntityType entityType) {
      if (!workflowTaskConfig.isEnable()) {
        /**
         * In case any exception occurred while executing TaskCommand, DLQ Retry will be attempted.
         */
        return;
      }
      String entityId = headers.get(EventHeaderConstants.ENTITY_ID);
      Pair<String, String> taskIdAndWorkerId = EventConsumerUtil
          .getTaskIdAndWorkerId(entityId, entityType);
      String taskId = taskIdAndWorkerId.getLeft();
      progressDetailRepo.findById(taskId).ifPresentOrElse(activityProgressDetail -> {
    	
    	EventHandlerUtil.populateContextFromProcessDetails(wasContextHandler, 
    			activityProgressDetail.getProcessDetails());

        WorkflowVerfiy.verify(
                Objects.isNull(activityProgressDetail.getTxnDetails()),
                () -> {
                  WorkflowLogger.logError("Retrying as transaction details not found for processId=%s", activityProgressDetail.getProcessDetails().getProcessId());

                  throw new WorkflowRetriableException(TXN_DETAILS_NOT_FOUND_FOR_ACTIVITY_ID);
                });

        processCustomTask(event, taskIdAndWorkerId, activityProgressDetail);
        //Set txnId in header to publish WorkflowStateTransition event header.
        headers.put(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE,
            activityProgressDetail.getTxnDetails().getTxnId());
      }, () ->
          EventingLoggerUtil
              .logInfo("CustomTaskHandling not supported for the given ExternalTask :: %s",
                  this.getClass().getSimpleName(), entityId));
  }

  /**
   * Prepares WorkflowTaskRequest from attributes received in event and headers and executes the
   * same.
   *
   * @param event                   - Event Received through event Bus.
   * @param taskIdAndWorkerId       - EntityId received in event header.
   * @param activityProgressDetails - ActivityProgress record from DB.
   */
  private void processCustomTask(ExternalTaskCompleted event,
      Pair<String, String> taskIdAndWorkerId,
      ActivityProgressDetails activityProgressDetails) {
	  
    ProcessDetails processDetails = activityProgressDetails.getProcessDetails();
    ActivityDetail activityDetail = activityProgressDetails.getActivityDefinitionDetail();

    WorkflowActivityAttributes activityAttributes = WorkflowTaskUtil
        .getActivityAttributes(activityDetail);

    Map<String, Object> eventAttributes = WorkflowTaskUtil.populateAttributes(event);
    
	WorkflowTaskRequest.WorkflowTaskRequestBuilder requestBuilder = WorkflowTaskRequest.builder()
        .id(taskIdAndWorkerId.getKey()).processInstanceId(processDetails.getProcessId())
        .txnId(activityProgressDetails.getTxnDetails().getTxnId())
        .activityId(activityDetail.getActivityId()).activityName(activityDetail.getActivityName())
        .taskType(activityDetail.getType())
        .activityType(activityDetail.getActivityType())
        .workerId(taskIdAndWorkerId.getRight())
        .taskAttributes(TaskAttributes.builder().variables(eventAttributes)
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.build());

    /**
     * There are 3 commands which maps based on status received in event.
     * An event can be for just update or mark complete.
     * There can be cases where we can receive failed status as well.
     * Depending on these status will be making call to downstream to update status and do the book-keeping of same at WAS end as well.
     * Will also publish the event based on status. Since, state transition event in case of success or failed in made by Camunda, will set flag as false.
     */
    ExternalTaskStatus status = ExternalTaskStatus.fromStatus(event.getStatus());
    /**
     * status enum can be null as status is allowed as free text string.
     * So, switch will give NPE. Hence, using if-else here.
     */
    if(ExternalTaskStatus.SUCCESS.equals(status)) {
        requestBuilder.command(TaskCommand.COMPLETE).status(ActivityConstants.TASK_STATUS_COMPLETE)
            .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false);
    } else if (ExternalTaskStatus.FAILED.equals(status)) {
        requestBuilder.command(TaskCommand.FAILED).status(status.getStatus())
            .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false);
    } else {
        requestBuilder.command(TaskCommand.UPDATE).status(event.getStatus())
            .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false);
    }
    workflowTaskManager.execute(requestBuilder.build());
  }

}
