package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;


import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCommands;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.RecoverableDataAccessException;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;

/**
 * Manages the workflow task execution using command provided in request.
 *
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WorkflowExternalTaskManager {

  private WASContextHandler contextHandler;

  /**
   * Common starting point for Task Command execution.
   *
   * @param taskRequest - WorkflowTaskRequest having workflow task details.
   * @throws WorkflowNonRetriableException - For scenarios such as ProcessNotFound or TaskNotFound
   *                                       in DB
   *                                       <p> {@link WorkflowRetriableException}, {@link
   *                                       WorkflowGeneralException} - if it is caught during
   *                                       execution</p>
   * @throws WorkflowGeneralException      in case of any other exception, having actual error in
   *                                       throwable clause.
   */
  @Metric(name = MetricName.WORKFLOW_TASK_EXECUTION, type = Type.EXTERNAL_TASK_METRIC)
  public WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest) {
    try {
      contextHandler.addKey(WASContextEnums.TASK_TYPE, taskRequest.getTaskType().name());
      contextHandler.addKey(WASContextEnums.PROCESS_INSTANCE_ID, taskRequest.getProcessInstanceId());
      WorkflowLogger.logInfo(
          "WORKFLOW_TASK_EXECUTION command=%s taskId=%s processInstanceId=%s step=start",
          taskRequest.getCommand(), taskRequest.getId(),
          taskRequest.getProcessInstanceId(), taskRequest.getTaskType().name());
      WorkflowTaskResponse response = WorkflowTaskCommands.getCommand(taskRequest.getCommand())
          .execute(taskRequest);
      WorkflowLogger.logInfo(
          "WORKFLOW_TASK_EXECUTION command=%s taskId=%s processInstanceId=%s step=success",
          taskRequest.getCommand(), taskRequest.getId(),
          taskRequest.getProcessInstanceId(), taskRequest.getTaskType().name());
      return response;
    } catch (WorkflowGeneralException| TransientDataAccessException| 
    		RecoverableDataAccessException ex) {
    	/**
    	 * IO and Timeout exceptions are checked exceptions, hence cannot be applied for retry.
    	 * TransientDataAccessException, RecoverableDataAccessException and ScriptException are 
    	 * Spring Data exception which may occur due to distributed environment. 
    	 * Hence, on retry of entire transaction it may work.
    	 */
      logException(taskRequest, ex);
      if (ex instanceof DataAccessException) {
        if (ex instanceof ObjectOptimisticLockingFailureException) {
          throw new WorkflowRetriableException(WorkflowError.OBJECT_OPTIMISTIC_LOCKING_FAILURE);
        }
        throw new WorkflowRetriableException(ex);
      }
      throw ex;
    } catch (Exception ex) {
      logException(taskRequest, ex);
      throw new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_WORKFLOW_TASK);
    }
  }

  /**
   * Log error.
   *
   * @param taskRequest - WorkflowTaskRequest having workflow task details.
   * @param ex          - Exception
   */
  private void logException(WorkflowTaskRequest taskRequest, Throwable ex) {
    WorkflowLogger.logError(ex,
        "WORKFLOW_TASK_EXECUTION command=%s taskId=%s processInstanceId=%s step=error",
        taskRequest.getCommand(), taskRequest.getId(),
        taskRequest.getProcessInstanceId(), taskRequest.getTaskType().name());

  }

}
