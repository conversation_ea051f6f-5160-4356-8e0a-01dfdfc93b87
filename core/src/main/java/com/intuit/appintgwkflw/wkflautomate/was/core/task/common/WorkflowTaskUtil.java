package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_MODE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_VARIABLE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskMode;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.ExternalTaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;

/**
 * Helper Util class to fetch details from extension, model or attribute map. This help avoid
 * duplicate code.
 *
 * <AUTHOR>
 */
@UtilityClass
public class WorkflowTaskUtil {

  private static final String VALUE = "value";

  /**
   * Fetches value from extensionAttributes.
   *
   * @param workerActionRequest : FetchNLock Request POJO.
   * @param varName             : field to be fetched from extension attribute.
   * @return
   */
  public String getExtenstionVariable(WorkerActionRequest workerActionRequest, String varName) {
    return workerActionRequest.getExtensionProperties().get(varName);
  }

  /**
   * Checks value exists in extensionAttributes.
   *
   * @param workerActionRequest : FetchNLock Request POJO.
   * @param varName             : field to be fetched from extension attribute.
   * @return
   */
  public boolean containsExtenstionVariable(WorkerActionRequest workerActionRequest,
      String varName) {
    return workerActionRequest.getExtensionProperties()
        .containsKey(varName);
  }

  /**
   * Checks value exists in modelAttributes.
   *
   * @param taskRequest : Request pojo of TaskCommand Handler.
   * @param varName     : variableName to check in ModelAttributes.
   * @return
   */
  public boolean containsRequestVariable(WorkflowTaskRequest taskRequest, String varName) {
    return taskRequest.getTaskAttributes().getModelAttributes().containsKey(varName);
  }

  /**
   * Fetched value from modelAttributes.
   *
   * @param taskRequest : Request pojo of TaskCommand Handler.
   * @param varName     : variableName to check in ModelAttributes.
   * @return
   */
  public String getRequestVariable(WorkflowTaskRequest taskRequest, String varName) {
    return taskRequest.getTaskAttributes().getModelAttributes().get(varName);
  }

  /**
   * Fetched transactionId field on which txnId will be saved or fetched from.
   *
   * @param taskRequest : Request pojo of TaskCommand Handler.
   * @return
   */
  public String getTransactionVariable(WorkflowTaskRequest taskRequest) {
    String transactionVariable = WORKFLOW_DEFAULT_TRANSACTION_VARIABLE;
    if (containsRequestVariable(taskRequest,
        EXTENSION_PROPERTY_TRANSACTION_VARIABLE)) {
      transactionVariable = getRequestVariable(taskRequest,
          EXTENSION_PROPERTY_TRANSACTION_VARIABLE);
    }
    return transactionVariable;
  }

  /**
   * Fetches TaskMode using extension Properties.
   *
   * @param workerActionRequest : FetchNLock Request POJO.
   * @return TaskMode
   */
  public TaskMode fetchTxnMode(WorkerActionRequest workerActionRequest) {
    TaskMode mode = TaskMode.SYNC;
    if (containsExtenstionVariable(workerActionRequest,
        EXTENSION_PROPERTY_TRANSACTION_MODE)) {

      mode = TaskMode.valueOf(getExtenstionVariable(workerActionRequest,
          EXTENSION_PROPERTY_TRANSACTION_MODE));
    }
    return mode;
  }

  /**
   * Fetches txnId using txnVariable defined in extension Properties.
   *
   * @param workerActionRequest : FetchNLock Request POJO.
   * @return txnId
   */
  public String fetchTransactionId(WorkerActionRequest workerActionRequest) {
    String transactionVariable = WORKFLOW_DEFAULT_TRANSACTION_VARIABLE;
    if (containsExtenstionVariable(workerActionRequest,
        EXTENSION_PROPERTY_TRANSACTION_VARIABLE)) {
      transactionVariable = getExtenstionVariable(workerActionRequest,
          EXTENSION_PROPERTY_TRANSACTION_VARIABLE);
    }
    return (String) workerActionRequest.getVariableMap().get(transactionVariable);
  }

  /**
   * Attributes populated when received from failed, success or update status events.
   *
   * @param event - ExternalTaskEvent.
   * @return Map<String, Object> - attributes.
   */
  @SuppressWarnings("unchecked")
  public Map<String, Object> populateAttributes(ExternalTaskCompleted event) {
    Map<String, Object> attributes = new HashMap<>();
    if (MapUtils.isNotEmpty(event.getVariables())) {
      for (Map.Entry<String, Object> entry : event.getVariables().entrySet()) {
        attributes.put(entry.getKey(), ((Map<String, Object>) entry.getValue()).get(VALUE));
      }
    }
    if (MapUtils.isNotEmpty(event.getLocalVariables())) {
      for (Map.Entry<String, Object> entry : event.getLocalVariables().entrySet()) {
        attributes.put(entry.getKey(), ((Map<String, Object>) entry.getValue()).get(VALUE));
      }
    }
    return attributes;
  }

  /**
   * Returns workflowActivityAttributes keeping the definiton of ExternalTask from BPMN.
   *
   * @param activityDetail - Definition saved while parsing BPMN.
   * @return WorkflowActivityAttributes keeping model and i/o attributes.
   */
  public WorkflowActivityAttributes getActivityAttributes(ActivityDetail activityDetail) {
    return getActivityAttributes(activityDetail.getAttributes());
  }

  /**
   * convert attributes in string to POJO WorkflowActivityAttributes
   * @param attributes
   * @return WorkflowActivityAttributes
   */
  public WorkflowActivityAttributes getActivityAttributes(String attributes) {
	return ObjectConverter.fromJson(attributes,
        new TypeReference<WorkflowActivityAttributes>() {
        });
}

  /**
   * Used for fetching typeReference instance for casting into relevant TaskAdaptor pojo.
   *
   * @param type - TaskType.
   * @return TypeReference instance of TaskAdaptor POJO.
   */
  @SuppressWarnings({"unchecked", "rawtypes"})
  public TypeReference<Task> taskTypeReference(TaskType type) {
    WorkflowTask workflowTask = WorkflowTasks.getWorkflowTask(type);
    WorkflowVerfiy.verifyNull(workflowTask, WorkflowError.ERROR_NO_TASK_HANDLER_REGISTERED);
    return workflowTask.typeReference();
  }

  /**
   * Get handlerScope from handlerDetails using ActivityAttributes.
   *
   * @param taskAttributes :: containing runtime and model definitions.
   * @return handlerScope (string).
   */
  public String getHandlerScope(TaskAttributes taskAttributes) {
    ExternalTaskAttributes externalTaskAttributes = ExternalTaskAttributes.builder()
        .extensionAttributes(taskAttributes.getModelAttributes())
        .variableMap(taskAttributes.getRuntimeAttributes())
        .build();
    return getHandlerDetails(externalTaskAttributes).map(HandlerDetails::getHandlerScope)
        .orElse(null);
  }

  /**
   * Fetch handlerDetails from externalTask
   *
   * @param externalTaskAttributes
   * @return handlerDetails
   */
  public Optional<HandlerDetails> getHandlerDetails(ExternalTaskAttributes externalTaskAttributes) {
    Map<String, String> extensionAttributes = externalTaskAttributes.getExtensionAttributes();
    Map<String, Object> variableMap = externalTaskAttributes.getVariableMap();
    HandlerDetails handlerDetails = null;
    if (MapUtils.isNotEmpty(extensionAttributes)
        && extensionAttributes.containsKey(WorkFlowVariables.HANDLER_DETAILS_KEY.getName())) {
      handlerDetails = SchemaDecoder.getHandlerDetails(extensionAttributes).orElse(null);
    } else if (MapUtils.isNotEmpty(variableMap)
        && variableMap.containsKey(WorkFlowVariables.HANDLER_DETAILS_KEY.getName())) {
      handlerDetails = SchemaDecoder
          .getHandlerDetails(Map.of(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
              (String) variableMap.get(WorkFlowVariables.HANDLER_DETAILS_KEY.getName())))
          .orElse(null);
    }
    return Optional.ofNullable(handlerDetails);
  }


  /**
   * Get events enabled to publish state transition event
   *
   * @param extensionProperties :: extension of BPMN model.
   * @return List<String> events enabled to publish state transition event
   */
  @SuppressWarnings("unchecked")
  public Set<String> getTransitionEnabledEvents(Map<String, String> extensionProperties) {
    return (Set<String>) Optional.ofNullable(extensionProperties).map(
    		extensionProps -> Optional.ofNullable(extensionProperties.get(WorkFlowVariables.EVENTS.getName()))
        .map(eventList -> ObjectConverter.fromJson(eventList, new TypeReference<HashSet<String>>() {
        })).orElse(Collections.emptySet()))
    .orElse(Collections.emptySet());
  }

  public boolean isWorkflowFilterPresent(WorkerActionRequest workerActionRequest) {
    Object workflowFilter = workerActionRequest.getVariableMap()
        .get(WorkflowConstants.WORKFLOW_FILTER);
    if (!Objects.isNull(workflowFilter) &&
        ((((List) workflowFilter).size()) > 0)) {
      return true;
    }
    return false;
  }
}
