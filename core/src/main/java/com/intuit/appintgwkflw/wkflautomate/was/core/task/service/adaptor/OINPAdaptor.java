package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.SENDING_NOTIFICATION_FAILED;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MaskedObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.OINPHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Adaptor to invoke OINP APIs
 */
@Component
@RequiredArgsConstructor
public class OINPAdaptor {

  private final String SINGLE_NOTIFICATION = "single";
  private final String BATCH_NOTIFICATION = "batch";

  private final OINPHttpClient oinpHttpClient;

  @Value("${intuit.pii.log.enable:false}")
  private boolean piiLogEnable;

  private final OfflineTicketClient offlineTicketClient;

  /**
   * Invokes OINP send event API.
   * @param oinpEventRequest request payload
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException if request fails with {@link #retryStatus}  codes.
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException if request fails with 4xx.
   */
  public void sendEvent(OINPEventRequest oinpEventRequest,
      WorkflowTaskConfigDetails workflowTaskConfigDetails){

    WASHttpRequest<OINPEventRequest, Object> wasHttpRequest =
        WASHttpRequest.<OINPEventRequest, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(oinpEventRequest)
            .requestHeaders(populateAuthorization())
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(workflowTaskConfigDetails.getEndpoint())
            .build();

    logEvent(oinpEventRequest);
    logPayload(oinpEventRequest, SINGLE_NOTIFICATION);
    WASHttpResponse<Object> response = oinpHttpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), () -> {
          throw new WorkflowNonRetriableException(SENDING_NOTIFICATION_FAILED, response.getError());
        });
  }

  /**
   * Invokes OINP send batch event API.
   * @param oinpEventRequest request payload
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException if request fails with 5xx.
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException if request fails with 4xx.
   */
  public void sendBatchEvent(List<OINPEventRequest> oinpEventRequest,
      WorkflowTaskConfigDetails workflowTaskConfigDetails){

    WASHttpRequest<List<OINPEventRequest>, Object> wasHttpRequest =
        WASHttpRequest.<List<OINPEventRequest>, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(oinpEventRequest)
            .requestHeaders(populateAuthorization())
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(workflowTaskConfigDetails.getEndpoint())
            .build();

    oinpEventRequest.forEach(this::logEvent);
    logPayload(oinpEventRequest, BATCH_NOTIFICATION);
    WASHttpResponse<Object> response = oinpHttpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), () -> {
          throw new WorkflowNonRetriableException(SENDING_NOTIFICATION_FAILED, response.getError());
        });
  }

  /** Use System offline ticket */
  private HttpHeaders populateAuthorization() {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    return httpHeaders;
  }

  /**
   * Logging tid and sourceObjectId
   */
  private void logEvent(OINPEventRequest oinpEventRequest){
    WorkflowLogger.logInfo("Invoking OINP send event sourceObjectId=%s oinpTid=%s",
        oinpEventRequest.getSourceObjectId(), oinpEventRequest.getEventMetaData().getIntuitTid());
  }

  /**
   * Logging can be enabled to debug the OINP request issues in higher envs.
   * @param payload request payload
   * @param flow batch or single notification
   */
  private <T> void logPayload(T payload, String flow){
    String eventPayload = piiLogEnable ? ObjectConverter.toJson(payload)
        : MaskedObjectConverter.toJson(payload);
    WorkflowLogger.logInfo("OinpRequest :: flow=%s, payload=%s", flow, eventPayload);
  }
}
