package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4AuthType;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4GraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4QueryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.Filter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.V4QueryRequest;
import com.intuit.v4.Error;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.work.Project;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> Invokes ProjectService for CRUD operations.
 */
@AllArgsConstructor
@Component
public class ProjectTaskAdaptor {

  private static final String UNKOWN_ERROR_CODE = "UNKOWN_ERROR_CODE";

  private static final String IDEMPOTENCE_ID = "idempotenceId";

  private V4GraphqlClient v4GraphqlClient;

  private V4QueryHelper v4QueryHelper;
  
  /**
   * Invokes ProjectService or Create/Update operation
   *
   * @param project            payload
   * @param customHeaders      - targetApp and targetDomain to be sent as headers.
   * @param taskConfigDetail - TaskConfigDetails having Adaptor configs.
   * @return Project
   */
  public Project executeProject(Project project,
      Map<String, String> customHeaders, 
      WorkflowTaskConfigDetails taskConfigDetail, Operation operation,
      String taskOwner) {
	  
    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.write(
            WASV4GraphqlRequest.builder()
                .url(taskConfigDetail.getEndpoint())
                .request(project)
                .targetRealmId(taskOwner)
                .authType(V4AuthType.SYSTEM_OFFLINE)
                .customHeaders(customHeaders)
                .serviceName(ServiceName.HUMAN_TASK)
                .build());

    verifyError(creationResponse, taskOwner, taskConfigDetail.getNonRetryErrorCodes());

    Project projectResponse = creationResponse.getResponse();

    WorkflowLogger
        .logInfo("Project create/update successful. projectId=%s, projectName=%s, targetRealm=%s operation=%s",
            projectResponse.getId(), projectResponse.getName(), taskOwner, operation.name());
    return projectResponse;

  }

  /**
   * Invokes READ call to project service based on idempotenceId
   *
   * @param idempotenceId     : unique id for idempotency.
   * @param taskConfigDetails - TaskConfigDetails having Adaptor configs.
   * @return project : Project matching idempotenceId.
   */
  public Project readProjectByIdempotencyId(String idempotenceId,
      WorkflowTaskConfigDetails taskConfigDetails, String taskOwner, Map<String, String> customHeader) {

    List<Object> idempotencyIds = new ArrayList<>();
    idempotencyIds.add(idempotenceId);

    List<Filter> whereFilter = new ArrayList<>();
    whereFilter.add(Filter.generateFilterObj(
        IDEMPOTENCE_ID,
        idempotencyIds,
        V4QueryHelper.FILTER_OPERATOR_DOUBLE_EQUAL));

    V4QueryRequest v4QueryRequest = V4QueryRequest.builder()
        .type(new Project().getTypeId())
        .whereFilters(whereFilter)
        .build();
    Query query = v4QueryHelper.buildV4Query(v4QueryRequest);
    WASV4GraphqlResponse<List<Project>> creationResponse =
        v4GraphqlClient.readList(
            WASV4GraphqlRequest.builder()
                .url(taskConfigDetails.getEndpoint())
                .query(query)
                .targetRealmId(taskOwner)
                .serviceName(ServiceName.HUMAN_TASK)
                .customHeaders(customHeader)
                .build());

    verifyError(creationResponse, taskOwner, taskConfigDetails.getNonRetryErrorCodes());

    WorkflowLogger.logDebug(WorkflowConstants.RESPONSE_PAYLOAD, creationResponse.getResponse());
    return CollectionUtils.isEmpty(creationResponse.getResponse())
        ? null : creationResponse.getResponse().stream().findFirst().orElse(null);
  }

  /**
   * Invokes read call using GlobalId
   *
   * @param id                - GlobalId in String format
   * @param taskConfigDetails - TaskConfigDetails having Adaptor configs.
   * @return project : Project matching Id.
   */
  public Project readProjectById(String id, 
		  WorkflowTaskConfigDetails taskConfigDetails, String taskOwner) {
    Query query = new Query();
    query.setId(GlobalId.from(id));
    WASV4GraphqlResponse<Project> creationResponse =
        v4GraphqlClient.read(
            WASV4GraphqlRequest.builder()
                .url(taskConfigDetails.getEndpoint())
                .query(query)
                .targetRealmId(taskOwner)
                .serviceName(ServiceName.HUMAN_TASK)
                .build());

    verifyError(creationResponse, taskOwner, 
    		taskConfigDetails.getNonRetryErrorCodes());

    WorkflowLogger.logDebug(WorkflowConstants.RESPONSE_PAYLOAD, creationResponse.getResponse());
    return creationResponse.getResponse();
  }

  /**
   * Checks for Error and throw corresponding exception.
   *
   * @param creationResponse   : ProjectService response.
   * @param targetRealmId      : targetRealm for logging.
   * @param nonRetryErrorCodes : ErrorCodes for non-retryable exception.
   */
  private void verifyError(WASV4GraphqlResponse<?> creationResponse, String targetRealmId,
      Set<String> nonRetryErrorCodes) {
    if (CollectionUtils.isNotEmpty(creationResponse.getErrors())) {
      creationResponse.getErrors().stream()
          .filter(error -> null != error.getCode())
          .filter(error -> nonRetryErrorCodes.contains(error.getCode()))
          .findAny().ifPresent(error -> logAndThrowNonRetriableException(targetRealmId, error));
      throw new WorkflowRetriableException(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE,
          creationResponse.getErrors().stream().filter(error -> null != error.getCode()).findFirst()
              .orElse(unknownError()).getCode());
    }
  }

  /**
   * For errors without any code.
   *
   * @return error : default error with UNKNOWN_ERROR code.
   */
  private Error unknownError() {
    Error error = new Error();
    error.setCode(UNKOWN_ERROR_CODE);
    return error;
  }

  /**
   * Log and throw nonRetriable errorcodes.
   *
   * @param targetRealmId
   * @param error
   */
  private void logAndThrowNonRetriableException(String targetRealmId, Error error) {
    WorkflowLogger
        .logError("Project create/update failed with error %s targetRealm=%s",
            error.getCode(), targetRealmId);
    throw new WorkflowNonRetriableException(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE,
         error.getCode());
  }

}