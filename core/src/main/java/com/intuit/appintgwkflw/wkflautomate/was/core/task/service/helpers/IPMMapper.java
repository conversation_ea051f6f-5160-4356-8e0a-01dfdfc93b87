package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils.DateFormat;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.Metadata;
import com.intuit.v4.integration.App;
import com.intuit.v4.network.Contact;
import com.intuit.v4.practice.Client;
import com.intuit.v4.work.Comment;
import com.intuit.v4.work.Project;
import com.intuit.v4.work.Template;
import com.intuit.v4.work.definitions.ExternalReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * Maps different task requests to Project Service request pojo
 */
@UtilityClass
public class IPMMapper {


  public static final String PROJECT_SERVICE_STATUS_OPEN = "Open";
  public static final String PROJECT_SERVICE_STATUS_COMPLETE = "Complete";
  public static final Map<String, String> statusMap = new ConcurrentHashMap<>();
  public static final Map<String, String> reverseStatusMap = new ConcurrentHashMap<>();

  private Set<String> wasSpecificVariables = new HashSet<>();


  static {
    for (WorkFlowVariables variable : WorkFlowVariables.values()) {
      wasSpecificVariables.add(variable.getName());
    }
    statusMap.put(ActivityConstants.TASK_STATUS_CREATED, PROJECT_SERVICE_STATUS_OPEN);
    statusMap.put(ActivityConstants.TASK_STATUS_COMPLETE, PROJECT_SERVICE_STATUS_COMPLETE);
    reverseStatusMap.put(PROJECT_SERVICE_STATUS_OPEN, ActivityConstants.TASK_STATUS_CREATED);
    reverseStatusMap.put(PROJECT_SERVICE_STATUS_COMPLETE, ActivityConstants.TASK_STATUS_COMPLETE);
  }


  /**
   * Converts the humanTask pojo to Project pojo
   *
   * @param taskRequest    HumanTask request
   * @param operation      Create or Update
   * @param contextHandler Context data.
   * @return project
   */
  public Project mapProjectServiceData(HumanTask taskRequest,
      Operation operation, WASContextHandler contextHandler,
      final Map<String, ExternalReference> additionalTaskReference,
      String ownerId) {

    String offeringId = contextHandler.get(WASContextEnums.OFFERING_ID);

    Project project = new Project();

    project.setName(taskRequest.getTaskName());

    project.setStatus(getMappedStatus(taskRequest));

    project.setDescription(taskRequest.getDescription());

    if (taskRequest.getPriority() != null) {
      project.setPriority(taskRequest.getPriority());
    }

    if (!StringUtils.isBlank(taskRequest.getTaskType())) {
      project.setType(taskRequest.getTaskType());
    }

    setAssigneeId(taskRequest, project, ownerId);

    setComments(taskRequest, project);

    setCreatedBy(taskRequest, project, offeringId);

    populateExternalReferences(taskRequest, project, operation, additionalTaskReference);

    setClientProfiles(taskRequest, project, ownerId);

    setWorkTemplate(taskRequest, project, ownerId);

    setEngagementId(taskRequest, project);
    
    setIdempotenceId(taskRequest, project);

    if (Operation.CREATE != operation) {
      if (StringUtils.isBlank(taskRequest.getTxnId())) {
        throw new WorkflowGeneralException(WorkflowError.INVALID_TASK_TXN_ID);
      } else {
        project.setId(GlobalId.from(taskRequest.getTxnId()));
      }
      if (taskRequest.isDueDateStrPresent()) {
        setDueDate(taskRequest, project);
      }
    } else {
      setDueDate(taskRequest, project);
    }

    return project;

  }

  /**
   * Populate external references
   */
  private void populateExternalReferences(HumanTask taskRequest, Project project,
      Operation operation, final Map<String, ExternalReference> additionalTaskReferenceMap) {
    List<ExternalReference> externalReferenceList = new ArrayList<>();
    if (Operation.CREATE == operation) {
      Map<String, String> mapExternalReferences = new HashMap<>();
      mapExternalReferences.put(PROCESS_ID, taskRequest.getProcessInstanceId());
      mapExternalReferences.put(ActivityConstants.EXTERNAL_TASK_ID, taskRequest.getId());
      mapExternalReferences.put(ENTITY_ID, taskRequest.getRecordId());
      String jsonResponse = ObjectConverter.toJson(mapExternalReferences);

      ExternalReference mandatoryExternalReference = new ExternalReference()
          .externalKey(ActivityConstants.MANDATORY_EXTERNAL_REFERENCES).externalBlob(jsonResponse);
      externalReferenceList.add(mandatoryExternalReference);
    }

    ExternalReference additionalTaskAttributeRef = new ExternalReference();
    Map<String, Object> additonalAttributes = new HashMap<>();
    if (MapUtils.isNotEmpty(additionalTaskReferenceMap)
        && additionalTaskReferenceMap.containsKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES)) {
      ExternalReference additionalTaskReference =
          additionalTaskReferenceMap.get(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES);
      additionalTaskAttributeRef
          .externalReferenceId(additionalTaskReference.getExternalReferenceId());
      final Map<String, Object> additionTaskRefMap = ObjectConverter
          .fromJson(additionalTaskReference.getExternalBlob(),
              new TypeReference<Map<String, Object>>() {
              });
      additonalAttributes.putAll(additionTaskRefMap);
    }
    additonalAttributes.putAll(additionalAttributes(taskRequest, operation));
    if (MapUtils.isNotEmpty(additonalAttributes)) {
      additionalTaskAttributeRef.externalKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES)
          .externalBlob(ObjectConverter.toJson(additonalAttributes));
      externalReferenceList.add(additionalTaskAttributeRef);
    }
    if (!CollectionUtils.isEmpty(externalReferenceList)) {
      project.setExternalReferences(externalReferenceList);
    }
  }

  /**
   * Prepares additional attributes using taskAttributes.variables.
   *
   * @param taskRequest :: Task pojo
   * @param operation   :: Operation create, update. Logic differs based on operation.
   * @return Map<String, Object> of additionalAttributes.
   */
  private Map<String, Object> additionalAttributes(HumanTask taskRequest, Operation operation) {
    TaskAttributes taskAttributes = taskRequest.getTaskAttributes();
    if (MapUtils.isEmpty(taskAttributes.getVariables())) {
      return Collections.emptyMap();
    }
    Map<String, Object> taskMap = ObjectConverter.convertObject(taskRequest,
        new TypeReference<Map<String, Object>>() {
        });
    Set<String> attributesKeySet = null;

    if (Operation.CREATE == operation) {
      /**
       * For Create Operation,
       * keys(additionalAttributes) = keys(model+runtime) - keys(Task)
       **/
      attributesKeySet = new HashSet<>(taskAttributes.getModelAttributes().keySet());
      attributesKeySet.addAll(taskAttributes.getRuntimeAttributes().keySet());
      attributesKeySet.removeAll(wasSpecificVariables);
      attributesKeySet.removeAll(taskMap.keySet());
    } else {
      /**
       * For Update, Complete Operation,
       * keys(additionalAttributes) = keys(variables) - keys(Task)
       **/
      attributesKeySet = new HashSet<>(taskAttributes.getVariables().keySet());
      attributesKeySet.removeAll(taskMap.keySet());
    }
    /**
     * Fill additionalAttributes keys with values from
     * taskAttribute.variables and taskAttribute.modelAttributes
     */
    return attributesKeySet.stream()
        .collect(Collectors.toMap(attributeKey -> attributeKey,
            attributeKey -> taskAttributes.getVariables().getOrDefault(attributeKey,
                taskAttributes.getModelAttributes().get(attributeKey))));
  }

  /**
   * convert string date to Datetime
   */
  private void setDueDate(HumanTask taskRequest, Project project) {
    if (!StringUtils.isBlank(taskRequest.getDueDate())) {
      project
          .setDueDate(DateUtils.getDateWithFormat(taskRequest.getDueDate(), DateFormat.YYYY_MM_DD));
    }
  }

  /**
   * Set assignee Id
   */
  private void setAssigneeId(HumanTask taskRequest, Project project, String targetRealmId) {
    if (!StringUtils.isBlank(taskRequest.getAssigneeId())) {
      Contact assignee = new Contact();
      assignee.setId(GlobalId.create(targetRealmId, taskRequest.getAssigneeId()));
      project.setAssignee(assignee);
    }
  }

  /**
   * Set comments as list
   */
  private void setComments(HumanTask taskRequest, Project project) {
    if (!StringUtils.isBlank(taskRequest.getComments())) {
      Comment comment = new Comment();
      comment.setContent(taskRequest.getComments());
      project.setComments(Collections.singletonList(comment));
    }
  }

  /**
   * Set CreatedBy App
   */
  private void setCreatedBy(HumanTask taskRequest, Project project, String ownerId) {
    if (!StringUtils.isBlank(taskRequest.getCreatedBy())) {
      project.setMeta(new Metadata().createdByApp(new App().id(taskRequest.getCreatedBy())));
    } else {
      project.setMeta(new Metadata().createdByApp(new App().id(ownerId)));
    }
  }

  /**
   * Set client profiles
   */
  private void setClientProfiles(HumanTask taskRequest, Project project,
      String targetRealmId) {
    if (!StringUtils.isBlank(taskRequest.getCustomerId())) {
      Contact client1 = new Contact();
      client1.setId(GlobalId.create(targetRealmId, taskRequest.getCustomerId()));
      project.setClient(client1);
      Client client = new Client();
      client.setRelationshipId(taskRequest.getCustomerId());
      Contact.Profiles profiles = new Contact.Profiles();
      profiles.setClient(client);
      client1.setProfiles(profiles);
    }
  }

  /**
   * Set Project Template Id
   */
  private void setWorkTemplate(HumanTask taskRequest, Project project,
      String targetRealmId) {
    if (!StringUtils.isBlank(taskRequest.getTemplateId())) {
      project.setWorkTemplate(new Template().id(GlobalId.create(targetRealmId,
          taskRequest.getTemplateId())));
    }
  }


  /**
   * Set Engagement Id
   */
  private static void setEngagementId(HumanTask taskRequest, Project project) {
    if (!StringUtils.isBlank(taskRequest.getEngagementId())) {
      project.setEngagementId(taskRequest.getEngagementId());
    }
  }

  /**
   * Set Idempotence Id
   */
  private static void setIdempotenceId(HumanTask taskRequest, Project project) {
    if (!StringUtils.isBlank(taskRequest.getId())) {
      project.setIdempotenceId(taskRequest.getId());
    }
  }
  
  /**
   * Get Mapped Status
   */
  public static String getMappedStatus(HumanTask taskRequest) {
	  return statusMap.getOrDefault(taskRequest.getStatus(), taskRequest.getStatus());
  }
  
  /**
   * Get Reversed Map Status
   */
  public static String getReversedStatus(String projectStatus) {
	  return reverseStatusMap.getOrDefault(projectStatus, projectStatus);
  }
  
}
