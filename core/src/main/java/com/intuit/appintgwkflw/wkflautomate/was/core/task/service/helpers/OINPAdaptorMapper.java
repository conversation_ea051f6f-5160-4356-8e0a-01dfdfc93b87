package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * OINPAdaptor mapper class to map request payloads
 */
@UtilityClass
public class OINPAdaptorMapper {

  /**
   * Maps request payload for OINP send single event API
   * @param notificationTask Notification info
   * @return OINP sendEvent request payload
   */
  public OINPEventRequest mapOINPRequestData(NotificationTask notificationTask){
    return OINPEventRequest.builder()
        .eventData(notificationTask.getNotificationData())
        .eventMetaData(notificationTask.getNotificationMetaData())
        .name(notificationTask.getNotificationName())
        .attachmentData(notificationTask.getAttachment())
        .sourceObjectType(notificationTask.getNotificationDataType())
        .sourceServiceName(notificationTask.getServiceName())
        .maskedFields(notificationTask.getMaskedFields())
        .sourceObjectId(getIdempotencyKey(notificationTask))
        .build();
  }

  /**
   * Maps request payload for OINP send batch event API
   * @param batchNotificationTask Batch Notification info
   * @return OINP sendBatchEvent request payload
   */
  public List<OINPEventRequest> mapOINPRequestData(BatchNotificationTask batchNotificationTask){
    return Optional.ofNullable(batchNotificationTask.getNotificationTaskList()).map(
        notificationTasks -> notificationTasks.stream()
        .map(notificationTask -> {
          OINPEventRequest oinpEventRequest = mapOINPRequestData(notificationTask);
          oinpEventRequest.setSourceObjectId(
              Optional.ofNullable(oinpEventRequest.getSourceObjectId())
                  .orElse(batchNotificationTask.getId()));
          return oinpEventRequest;
        })
        .collect(Collectors.toList())).orElse(Collections.emptyList());
  }

  /**
   * Get idempotency key
   * @param notificationTask contains info
   */
  public String getIdempotencyKey(NotificationTask notificationTask){
    return Optional.ofNullable(notificationTask.getIdempotencyKey())
        .orElse(notificationTask.getId());
  }
}
