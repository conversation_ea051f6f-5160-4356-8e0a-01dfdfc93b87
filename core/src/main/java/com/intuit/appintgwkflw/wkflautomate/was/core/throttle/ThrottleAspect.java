package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttle;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttles;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


@Aspect
@Component
@AllArgsConstructor
public class ThrottleAspect {

        /**
         * Checks if the execution is to be continued or not.
         * Might throw throttling exception if threshold has been breached.
         * In some cases, if threshold is breached, exception might not be thrown. Then this method
         * returns false - which indicates not to proceed with execution
         *
         */
        public boolean canContinueExecution(final ProceedingJoinPoint joinPoint, final Throttle throttle)
            throws Throwable {
                return ThrottleServiceHandlers.getHandler(
                    throttle.attribute()
                ).canContinueExecution(joinPoint);
        }

        /**
         * Performs throttling when only one @Throttle annotation is used on a method
         *
         */
        @SuppressWarnings("unchecked")
        @Around("@annotation(throttle)")
        public Object execute(final ProceedingJoinPoint joinPoint, final Throttle throttle)
            throws Throwable {
                if(canContinueExecution(joinPoint, throttle)) {
                        return joinPoint.proceed();
                }
                return null;
        }

        /**
         * Performs throttling when more than one @Throttle annotations are used on a method
         *
         */
        @SuppressWarnings("unchecked")
        @Around("@annotation(throttles)")
        public Object execute(final ProceedingJoinPoint joinPoint, final Throttles throttles)
            throws Throwable {
                for (Throttle throttle : throttles.value()) {
                        if(!canContinueExecution(joinPoint, throttle)) {
                                return null;
                        }
                }
                return joinPoint.proceed();
        }
}
