package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
public class ThrottleHelper {
    private final ThrottleConfigs throttleConfigs;
    private final static String DEFAULT_CONFIG = "default";

    /**
     * Checks that throttling has not been disabled for the workflow
     * And if workflow-specific threshold is not present, checks if default threshold is present
     *
     * @param definitionKey
     * @param throttleAttribute
     * @return
     */
    public boolean isThrottlingEnabledForWorkflow(final String definitionKey, ThrottleAttribute throttleAttribute) {
        if(throttleConfigs.getWorkflow() == null) {
            return false;
        };

        if(isThrottlingDisabledForWorkflow(definitionKey, throttleAttribute)){
            return false;
        }

        if(isThresholdPresentForWorkflow(definitionKey, throttleAttribute) ||
                isDefaultThresholdPresent(throttleAttribute)){
            return true;
        }

        return false;
    }

    /**
     * Checks if throttling has been disabled for the workflow (with the passed definitionKey) in the configs
     *
     * @param definitionKey
     * @return
     */
    private boolean isThrottlingDisabledForWorkflow(final String definitionKey, ThrottleAttribute throttleAttribute) {
        return throttleConfigs.getWorkflow().containsKey(definitionKey) &&
                throttleConfigs.getWorkflow().get(definitionKey).getDisable() != null &&
                throttleConfigs.getWorkflow().get(definitionKey).getDisable().containsKey(throttleAttribute) &&
                Boolean.TRUE.equals(throttleConfigs.getWorkflow().get(definitionKey).getDisable().get(throttleAttribute));
    }

    /**
     * Checks if thresholds are present specifically for the workflow.
     * If default thresholds are not present, throttling is still performed for the workflows which are specifically mentioned in the configs.
     *
     * @param definitionKey
     * @return
     */
    private boolean isThresholdPresentForWorkflow(final String definitionKey, ThrottleAttribute throttleAttribute) {
        return throttleConfigs.getWorkflow().containsKey(definitionKey) &&
                throttleConfigs.getWorkflow().get(definitionKey).getThreshold() != null &&
                throttleConfigs.getWorkflow().get(definitionKey).getThreshold().containsKey(throttleAttribute);
    }

    /**
     * Checks if default throttling thresholds are present for the attribute (e.g. of attribute: externalTasksPerActivity, definitionsPerWorkflow, etc.)
     *
     * @param throttleAttribute
     * @return
     */
    private boolean isDefaultThresholdPresent(ThrottleAttribute throttleAttribute) {
        return ((throttleConfigs.getWorkflow().containsKey(DEFAULT_CONFIG)) &&
                throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getThreshold()!=null &&
                throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getThreshold().containsKey(throttleAttribute));
    }


    /**
     * Retrieve threshold for the specific definition, else fallback to default threshold
     *
     * @param definitionKey
     * @return
     */
    public Integer getThreshold(final String definitionKey, ThrottleAttribute throttleAttribute) {
        if(isThresholdPresentForWorkflow(definitionKey, throttleAttribute)) {
            return throttleConfigs.getWorkflow().get(definitionKey).getThreshold().get(throttleAttribute);
        }
        return throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getThreshold().get(throttleAttribute);
    }

    /**
     * Fetches the throttling timeframe for the workflow based on the attribute.
     * A default timeframe MUST be present if timeframe is not set for each individual workflow
     *
     * @param definitionKey
     * @param throttleAttribute
     * @return
     */
    public Integer getTimeframeForWorkflow(final String definitionKey, ThrottleAttribute throttleAttribute) {
        if(isTimeframeDefinedForWorkflow(definitionKey, throttleAttribute)) {
            return throttleConfigs.getWorkflow().get(definitionKey).getTimeframeMillis().get(throttleAttribute);
        }
        return throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getTimeframeMillis().get(throttleAttribute);
    }

    /**
     * Checks if timeframe has been defined in config - either specific to the workflow or a default timeframe - for the given attribute
     *
     * @param definitionKey
     * @param throttleAttribute
     * @return
     */
    public boolean isTimeframeDefined(final String definitionKey, ThrottleAttribute throttleAttribute) {
        return isTimeframeDefinedForWorkflow(definitionKey, throttleAttribute) || isDefaultTimeframeDefined(throttleAttribute);
    }

    /**
     * Checks if timeframe specific to the workflow has been defined
     *
     * @param definitionKey
     * @param throttleAttribute
     * @return
     */
    private boolean isTimeframeDefinedForWorkflow(final String definitionKey, ThrottleAttribute throttleAttribute) {
        return (throttleConfigs.getWorkflow().containsKey(definitionKey) &&
                throttleConfigs.getWorkflow().get(definitionKey).getTimeframeMillis() != null &&
                throttleConfigs.getWorkflow().get(definitionKey).getTimeframeMillis().containsKey(throttleAttribute));
    }

    /**
     * Checks if a default timeframe config is present
     *
     * @param throttleAttribute
     * @return
     */
    private boolean isDefaultTimeframeDefined(ThrottleAttribute throttleAttribute) {
        return ((throttleConfigs.getWorkflow().containsKey(DEFAULT_CONFIG)) &&
                throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getTimeframeMillis()!=null &&
                throttleConfigs.getWorkflow().get(DEFAULT_CONFIG).getTimeframeMillis().containsKey(throttleAttribute));
    }
}
