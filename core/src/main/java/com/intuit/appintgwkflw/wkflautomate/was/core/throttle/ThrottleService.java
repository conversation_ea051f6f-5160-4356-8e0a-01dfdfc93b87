package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Handles throttling of the specific attribute
 */
public abstract class ThrottleService {

    @Autowired
    protected ThrottleConfigs throttleConfigs;
    @Autowired
    protected ThrottleHelper throttleHelper;

    /**
     * Attribute being throttled by the throttle handler
     *
     * @return
     */
    public abstract ThrottleAttribute getAttribute();

    /**
     * Returns whether the execution can be continued or not depending on whether threshold has been breached.
     * Checks if throttling is enabled for the attribute; if not, throttling not performed
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    public boolean canContinueExecution(final ProceedingJoinPoint joinPoint) throws Throwable {
        if(!isThrottlingEnabled(joinPoint)) {
            return true;
        }
        return execute(joinPoint);
    };

    /**
     * Specifies whether throttling is to be performed
     *
     * @return
     */
    public abstract boolean isThrottlingEnabled(final ProceedingJoinPoint proceedingJoinPoint);

    /**
     * The main handler that handles throttling of the attribute
     *
     * If error threshold is breached, further execution is not done - returns false
     * If warning threshold is breached, further execution is done - returns true
     *
     * @param joinPoint
     * @throws Throwable
     */
    public boolean execute(final ProceedingJoinPoint joinPoint) throws Throwable {
        Pair<Integer, String> countAndWorkflowPair = getExecutionCountAndWorkflow(joinPoint);

        // Retrieve threshold for the specific definition, else fallback to default threshold
        Integer errorThreshold = throttleHelper.getThreshold(countAndWorkflowPair.getRight(), getAttribute());
        Integer executionCount = countAndWorkflowPair.getLeft();

        // Do not proceed after executing failure, if threshold is breached. Hence, return null.
        if(executionCount > errorThreshold) {
            executeFailure(joinPoint, executionCount);
            return false;
        }
        // Carry out necessary steps (e.g. log warning) and then proceed if warning threshold is breached
        else if(executionCount > (errorThreshold - getWarnDiff())) {
            executeWarn(joinPoint, executionCount);
        }
        return true;
    }

    /**
     * Gets count of the number of executions of the attribute - on which throttling is to be performed.
     * Also returns the definition key
     *
     * @param joinPoint
     * @return
     */
    public abstract Pair<Integer, String> getExecutionCountAndWorkflow(final ProceedingJoinPoint joinPoint);

    public abstract Integer getWarnDiff();

    /**
     * Handler that decides what's to be done when threshold is breached
     * @param joinPoint
     */
    public abstract void executeFailure(final ProceedingJoinPoint joinPoint, Integer executionCount);

    /**
     * Handler that carries out necessary steps when warning threshold is breached
     * @param joinPoint
     */
    public abstract void executeWarn(final ProceedingJoinPoint joinPoint, Integer executionCount);
}
