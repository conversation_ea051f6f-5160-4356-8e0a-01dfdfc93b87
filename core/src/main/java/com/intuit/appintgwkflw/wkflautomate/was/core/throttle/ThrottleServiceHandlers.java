package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import java.util.HashMap;
import java.util.Map;

public class ThrottleServiceHandlers {
    private static final Map<ThrottleAttribute, ThrottleService> THROTTLE_SERVICE_HANDLER_MAP = new HashMap<>();

    /**
     * Maps attribute to its throttle handler.
     *
     * @param attribute The attribute which is being throttled
     * @param processor The processor handling the throttling of the specific attribute
     */
    public static void addHandler(ThrottleAttribute attribute, ThrottleService processor) {

        THROTTLE_SERVICE_HANDLER_MAP.put(attribute, processor);
    }

    /**
     * Gets throttle handler for the attribute.
     *
     * @param attribute The attribute which is being throttled
     * @return The processor handling the throttling of the specific attribute
     */
    public static ThrottleService getHandler(ThrottleAttribute attribute) {
        return THROTTLE_SERVICE_HANDLER_MAP.get(attribute);
    }
}
