package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

@UtilityClass
public class ApprovalUtil {

    /**
     * Checks if the 'approvalType' within parameterDetails is not empty.
     *
     * @param parameterDetailsMap the map containing approval-related parameters
     * @return true if 'approvalType' exists and contains a non-empty value, false otherwise
     */
    public boolean isMultiLevelHybridApproval(Map<String, ParameterDetails> parameterDetailsMap) {

      boolean isApprovalTypePresent = MapUtils.isNotEmpty(parameterDetailsMap) &&
          parameterDetailsMap.containsKey(ApprovalTaskConstants.APPROVER_TYPE) &&
          Objects.nonNull(parameterDetailsMap.get(ApprovalTaskConstants.APPROVER_TYPE)) &&
          CollectionUtils.isNotEmpty(parameterDetailsMap.get(ApprovalTaskConstants.APPROVER_TYPE).getFieldValue());

      if (!isApprovalTypePresent) {
        return false;
      }

      List<String> assigneeList = null;
      if (parameterDetailsMap.containsKey(ApprovalTaskConstants.ASSIGNEE) &&
          Objects.nonNull(parameterDetailsMap.get(ApprovalTaskConstants.ASSIGNEE)) &&
          CollectionUtils.isNotEmpty(parameterDetailsMap.get(ApprovalTaskConstants.ASSIGNEE).getFieldValue())) {
        assigneeList = parameterDetailsMap.get(ApprovalTaskConstants.ASSIGNEE).getFieldValue();
      }

      if (CollectionUtils.isEmpty(assigneeList)) {
        return false;
      }

      return assigneeList.stream().findFirst().toString().contains(":");
    }
    /**
     * This method extracts all parameters details from the userAttributes field of the callActivity
     * @param userAttributes String
     * @return
     */
    public Map<String, ParameterDetails> getParameterDetailsMapForMultiConditionWorkflows(
        String userAttributes) {

      Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
      if( Objects.isNull(userAttributes)) {
        return parameterDetailsMap;
      }
      Map<String, Object> userAttributesMap = ObjectConverter.fromJson(userAttributes,
          new TypeReference<Map<String, Object>>() {});

      if (MapUtils.isEmpty(userAttributesMap) || !userAttributesMap.containsKey(WorkflowConstants.PARAMETERS)) {
        return parameterDetailsMap;
      }

      Object parametersMap = userAttributesMap.get(WorkflowConstants.PARAMETERS);

      parameterDetailsMap = ObjectConverter.fromJson(ObjectConverter.toJson(parametersMap),
          new TypeReference<Map<String, ParameterDetails>>() {});

      return parameterDetailsMap;
    }
}



