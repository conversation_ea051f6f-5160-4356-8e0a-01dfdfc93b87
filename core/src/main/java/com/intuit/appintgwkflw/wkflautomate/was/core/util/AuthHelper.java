package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_USERID;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@AllArgsConstructor
public class AuthHelper {
  private final WASContextHandler contextHandler;

  /**
   * Gets the associated value for the auth key
   *
   * @param key
   * @return the auth details
   */
  public String getAuthValueForKey(final String key) {
    String result = null;
    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
    if (StringUtils.isNotEmpty(key)) {
      switch (key) {
        case INTUIT_REALMID:
          result =
              StringUtils.isNotEmpty(authorization.getRealm())
                  ? authorization.getRealm()
                  : contextHandler.get(WASContextEnums.OWNER_ID);
          break;
        case INTUIT_USERID:
          result = authorization.get(key);
          break;
        default:
          return null;
      }
    }
    return result;
  }

  /**
   * Returns the owner id from the auth header
   *
   * @return
   */
  public String getOwnerId() {
    return getAuthValueForKey(WorkflowConstants.INTUIT_REALMID);
  }

  /**
   * Checks whether inputVar matches any authorization keys
   *
   * @param inputVar
   * @return
   */
  public boolean canExtractVariablesFromAuth(String inputVar) {
    return Objects.nonNull(AuthParameters.getAuthParameter(inputVar));
  }

  // Method to fetch first auth details from the list if multiple entries exist
  public Optional<AuthDetails> getAuthDetailsFromList(Optional<List<AuthDetails>> authDetailsListOptional){
    authDetailsListOptional.ifPresent(authDetailsList -> {
      if(authDetailsList.size() > WorkflowConstants.VALUE_ONE)
        WorkflowLogger.logWarn("More than one Auth details found for given ownerID");
    });
    return authDetailsListOptional.flatMap(authDetailsList -> authDetailsList.stream().filter(Objects::nonNull).findFirst());
  }

  @Getter
  private enum AuthParameters {
    INTUIT_USERID("intuit_userid"),
    INTUIT_REALMID("intuit_realmid");

    private final String parameterName;

    AuthParameters(String parameterName) {
      this.parameterName = parameterName;
    }

    public static AuthParameters getAuthParameter(String parameterName) {
      return Arrays.stream(AuthParameters.values())
          .filter(param -> param.getParameterName().equalsIgnoreCase(parameterName))
          .findFirst()
          .orElse(null);
    }
  }
}
