package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_TASKS;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.MockHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BooleanOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DateOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DaysOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.NumberOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.StringOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ListOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowStepTypeEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.v4.workflows.definitions.Operator;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaIn;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaOut;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.springframework.util.ObjectUtils;

/** A Utility Class */
@UtilityClass
public class BpmnProcessorUtil {

  public Map<String, String> getMapOfInputOutputParameters(ExtensionElements extensionElements) {
    if (null == extensionElements) return null;
    return extensionElements.getElementsQuery().filterByType(CamundaInputOutput.class).list()
        .stream()
        .filter(camundaInputOutput -> !ObjectUtils.isEmpty(camundaInputOutput))
        .map(CamundaInputOutput::getCamundaInputParameters)
        .flatMap(Collection::stream)
        .collect(
            Collectors.toMap(
                CamundaInputParameter::getCamundaName, CamundaInputParameter::getRawTextContent));
  }

  public Map<String, String> getMapOfCamundaProperties(ExtensionElements extensionElements) {
    if (null == extensionElements) return null;
    return extensionElements.getElementsQuery().filterByType(CamundaProperties.class).list()
        .stream()
        .filter(camundaProperties -> !ObjectUtils.isEmpty(camundaProperties))
        .map(CamundaProperties::getCamundaProperties)
        .flatMap(Collection::stream)
        .collect(Collectors.toMap(name -> name.getCamundaName(), name -> name.getCamundaValue()));
  }

  public String getElementType(String type) {
    return WorkflowStepTypeEnum.getType(type);
  }

  public List<CamundaIn> getListOfCamundaInParameters(ExtensionElements extensionElements) {
    if (null == extensionElements) {
      return null;
    }
    return extensionElements.getElementsQuery().filterByType(CamundaIn.class).list();
  }

  public List<CamundaOut> getListOfCamundaOutParameters(ExtensionElements extensionElements) {
    if (null == extensionElements) {
      return null;
    }
    return extensionElements.getElementsQuery().filterByType(CamundaOut.class).list();
  }

  /**
   * Helper method to update the BPMN name to provided Definition Display Name
   *
   * @param bpmnModelInstance ; BPMN Model instance object
   * @param displayName       : User provided definition display name
   */
  public void updateBpmnName(final BpmnModelInstance bpmnModelInstance, final String displayName) {
    final Optional<Process> processOptional =
        bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst();
    processOptional.ifPresent(process -> process.setName(displayName));
  }

  public Map<String, String> getSupportedOperator(String type) {
    switch (type) {
      case "STRING":
        return StringOperator.possibleOperatorValueMap();
      case "LIST":
        return ListOperator.possibleOperatorValueMap();
      case "DOUBLE":
      case "INTEGER":
      case "LONG":
        return NumberOperator.possibleOperatorValueMap();
      case "DATE":
        return DateOperator.possibleOperatorValueMap();
      case "BOOLEAN":
        return BooleanOperator.possibleOperatorValueMap();
      case "DAYS":
        return DaysOperator.possibleOperatorValueMap();
      default:
        return new HashMap<>();
    }
  }

  public List<Operator> prepareOperatorList(Map<String, String> operatorMap) {
    List<Operator> operators = new ArrayList<>();
    operatorMap.forEach(
        (symbol, description) -> {
          Operator operator = new Operator();
          operator.setSymbol(symbol);
          operator.setDescription(description);
          operators.add(operator);
        });
    return operators;
  }

  /**
   * @param templateData : Byte Array of BPMN Template Data
   * @return : BpmnModelInstance object
   */
  public BpmnModelInstance readBPMN(byte[] templateData) {
    try (ByteArrayInputStream byteStream = new ByteArrayInputStream(templateData)) {
      return Bpmn.readModelFromStream(byteStream);
    } catch (Exception e) {
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_READ_EXCEPTION, e);
    }
  }

  /**
   * @param fileName : The path of the bpmn file as String
   * @return : BPMN Model Instance
   */
  public BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(IOUtils.toInputStream(MockHelper.readResourceAsString(fileName), StandardCharsets.UTF_8));
  }

  /**
   * @param templateData : Byte Array of DMN Template Data
   * @return : DmnModelInstance object
   */
  public DmnModelInstance readDmn(byte[] templateData) {
    try (ByteArrayInputStream byteStream = new ByteArrayInputStream(templateData)) {
      return Dmn.readModelFromStream(byteStream);
    } catch (Exception e) {
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_READ_EXCEPTION, e);
    }
  }

  /**
   * @param fileName : The path of the dmn file as String
   * @return : DMN Model Instance
   */
  public DmnModelInstance readDMNFile(String fileName) {
    return Dmn.readModelFromStream(IOUtils.toInputStream(MockHelper.readResourceAsString(fileName), StandardCharsets.UTF_8));
  }

  /**
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @return byte array of bpmn
   */
  public byte[] convertBpmnModelInstanceToByteArray(BpmnModelInstance bpmnModelInstance) {
    try (ByteArrayOutputStream byteStream = new ByteArrayOutputStream()) {
      Bpmn.writeModelToStream(byteStream, bpmnModelInstance);
      return byteStream.toByteArray();
    } catch (Exception exception) {
      throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, exception);
    }
  }

  /**
   * @param dmnModelInstance {@link BpmnModelInstance}
   * @return byte array of dmn
   */
  public byte[] convertDmnModelInstanceToByteArray(DmnModelInstance dmnModelInstance) {
    try (ByteArrayOutputStream byteStream = new ByteArrayOutputStream()) {
      Dmn.writeModelToStream(byteStream, dmnModelInstance);
      return byteStream.toByteArray();
    } catch (Exception exception) {
      throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, exception);
    }
  }

  /**
   * @param dmnList : List of Template Details object representing DMNs
   * @return : List of DMN Instances
   */
  public List<DmnModelInstance> prepareDmnInstance(List<TemplateDetails> dmnList) {
    return dmnList.stream()
        .map(
            templateDetails ->
                Dmn.readModelFromStream(
                    new ByteArrayInputStream(templateDetails.getTemplateData())))
        .collect(Collectors.toList());
  }

  /**
   * @param bpmnXmlString : String XML of Bpmn
   * @return : BpmnModelInstance throws EMPTY_BPMN_EXCEPTION : Error when bpmnXmlString returned
   *     from Camunda Service is Empty
   */
  public BpmnModelInstance getBpmnModelInstanceFromXml(String bpmnXmlString) {
    WorkflowVerfiy.verify(ObjectUtils.isEmpty(bpmnXmlString), WorkflowError.EMPTY_BPMN_EXCEPTION);
    return Bpmn.readModelFromStream(
        new ByteArrayInputStream(bpmnXmlString.getBytes(StandardCharsets.UTF_8)));
  }

  /**
   * @param dmnXmlStrings : String XMls of DMNs associated with Bpmn Diagram
   * @return : List<DmnModelInstance></>
   */
  public List<DmnModelInstance> getDmnModelInstanceListFromXml(List<String> dmnXmlStrings) {
    if (ObjectUtils.isEmpty(dmnXmlStrings)) {
      return null;
    }
    return dmnXmlStrings.stream()
        .map(
            dmnXmlString ->
                Dmn.readModelFromStream(
                    new ByteArrayInputStream(dmnXmlString.getBytes(StandardCharsets.UTF_8))))
        .collect(Collectors.toList());
  }

  /**
   * Get dmnTasks list which are service tasks. The list which is the id of the DMNs to be set as
   * key dmnTasks in the template
   *
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @return list of dmn id
   */
  public List<String> getDmnServiceTasks(BpmnModelInstance bpmnModelInstance) {
    Process process =
        bpmnModelInstance.getModelElementsByType(Process.class).stream()
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_BPMN_PROCESS_DETAILS));
    Map<String, String> properties =
        BpmnProcessorUtil.getMapOfCamundaProperties(
            process
                .getExtensionElements());
    if (MapUtils.isEmpty(properties) || Objects.isNull(properties.get(DMN_TASKS))) {
      return new ArrayList<>();
    }
    return ObjectConverter.fromJson(
        properties.get(DMN_TASKS), new TypeReference<List<String>>() {});
  }

  /**
   * A Utility Method to create and set variable Object
   *
   * @param baseElement
   * @param variable
   * @param processor
   */
  public void setVariableInElement(
      BaseElement baseElement, WorkFlowVariables variable, IVariableProcessor processor) {
    String variableName = variable.getName();
    String jsonString = ObjectConverter.toJson(processor.getVariableValue());

    // get handler details from input/output parameter
    Optional<CamundaInputParameter> inputParameterOptional =
        getCamundaInputParam(baseElement, variableName);

    inputParameterOptional.ifPresent(inputParameter -> inputParameter.setTextContent(jsonString));

    // get handler details from camunda property parameter
    Optional<CamundaProperty> propertyOptional = getCamundaProperty(baseElement, variableName);

    propertyOptional.ifPresent(property -> property.setCamundaValue(jsonString));
  }

  /**
   * Method to create and set ParameterDetails Object
   *
   * @param baseElement {@link BaseElement}
   * @return @return Updated HandlerDetails
   */
  public void setParametersInElement(BaseElement baseElement, IVariableProcessor processor) {
    Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
    parameterDetailsMap.putAll((Map<String, ParameterDetails>) processor.getVariableValue());

    // get parameter details from input/output parameter
    Optional<CamundaInputParameter> inputParameterOptional =
        getCamundaInputParam(baseElement, WorkFlowVariables.PARAMETERS_KEY.getName());

    inputParameterOptional.ifPresent(
        inputParameter -> {
          parameterDetailsMap.putAll(
              ObjectConverter.fromJson(
                  inputParameter.getTextContent(),
                  new TypeReference<Map<String, ParameterDetails>>() {}));
          String jsonString = ObjectConverter.toJson(parameterDetailsMap);
          inputParameter.setTextContent(jsonString);
        });

    // get parameter details from camunda property parameter
    Optional<CamundaProperty> propertyOptional =
        getCamundaProperty(baseElement, WorkFlowVariables.PARAMETERS_KEY.getName());

    propertyOptional.ifPresent(
        property -> {
          parameterDetailsMap.putAll(
              ObjectConverter.fromJson(
                  property.getCamundaValue(),
                  new TypeReference<Map<String, ParameterDetails>>() {}));
          String jsonString = ObjectConverter.toJson(parameterDetailsMap);
          property.setCamundaValue(jsonString);
        });
  }

  // get parameterdetails parameter from input/output extension element. input/output can have
  // list of parameters
  public Optional<CamundaInputParameter> getCamundaInputParam(
      BaseElement baseElement, String variableName) {
    ExtensionElements elements = baseElement.getExtensionElements();
    return elements.getElementsQuery().filterByType(CamundaInputOutput.class).list().stream()
        .map(CamundaInputOutput::getCamundaInputParameters)
        .flatMap(Collection::stream)
        .filter(parameter -> variableName.equals(parameter.getCamundaName()))
        .findFirst();
  }

  // get parameterdetails property from extensions extension element. Extensions can have list of
  // properties
  public Optional<CamundaProperty> getCamundaProperty(
      BaseElement baseElement, String variableName) {
    ExtensionElements elements = baseElement.getExtensionElements();
    return elements.getElementsQuery().filterByType(CamundaProperties.class).list().stream()
        .map(CamundaProperties::getCamundaProperties)
        .flatMap(Collection::stream)
        .filter(parameter -> variableName.equals(parameter.getCamundaName()))
        .findFirst();
  }

  /**
   * Returns StartEvent element. StartEvent will always come from uber template
   *
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @return
   */
  public FlowElement findStartEventElement(BpmnModelInstance bpmnModelInstance) {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    AtomicReference<FlowElement> startEventElement = new AtomicReference<>();
    processes.forEach(
        process -> {
          process
              .getFlowElements()
              .forEach(
                  element -> {
                    if (BpmnComponentType.START_EVENT
                        .getName()
                        .equalsIgnoreCase(element.getElementType().getTypeName())) {
                      startEventElement.set(element);
                      return;
                    }
                  });
          // If element found, break the process loop
          if (startEventElement.get() != null) {
            return;
          }
        });

    return startEventElement.get();
  }

    /**
     * This function checks if the given bpmn flow node element is of type business rule task
     *
     * @param targetElement
     * @return boolean
     */
    public boolean isBusinessRuleTaskElement(FlowNode targetElement) {
        return targetElement.getElementType().getTypeName().equals(BpmnComponentType.BUSINESS_RULE_TASK.getName());
    }

    /**
     * This function checks if the given bpmn flow node element is of type call activity
     *
     * @param targetElement
     * @return boolean
     */
    public boolean isCallActivityElement(FlowNode targetElement) {
        return targetElement.getElementType().getTypeName().equals(BpmnComponentType.CALL_ACTIVITY.getName());
    }

    /**
     * This function checks if the given bpmn flow node element is of type end event
     *
     * @param targetElement
     * @return boolean
     */
    public boolean isEndEventElement(FlowNode targetElement) {
        return targetElement.getElementType().getTypeName().equals(BpmnComponentType.END_EVENT.getName());
    }

  /**
   * This method validates whether triggerDetailsList contains duplicate trigger names or not.
   * <AUTHOR>
   * @param triggerDetailsList List<TriggerDetails>
   */
  public void validateDuplicateTriggerNames (List<TriggerDetails> triggerDetailsList) {
    Set<String> distinctTriggersSet =
        triggerDetailsList.stream()
            .map(triggerDetails -> triggerDetails.getTriggerName().toLowerCase())
            .collect(Collectors.toSet());

    WorkflowVerfiy.verify(
        distinctTriggersSet.size() != triggerDetailsList.size(),
        WorkflowError.TEMPLATE_CONTAINS_DUPLICATE_TRIGGER_NAMES);
  }
}
