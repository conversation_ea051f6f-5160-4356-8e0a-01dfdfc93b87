package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import lombok.experimental.UtilityClass;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@UtilityClass
public class ConfigurationDefinitionUtil {

  /**
   * @param flowElement : A Camunda component
   * @return : This method returns all the configurations in the the particular Camunda Element with
   *     * key as ID of the Element and value of all the Extension Elements present. This is
   *     required by Schema Decoder to decode and encode various parameters provided by the user as
   *     configuration
   */
  public Map<String, String> getConfigurationsAsMap(FlowElement flowElement) {
    Map<String, String> configurations = new HashMap<>();
    return prepareConfigurationMapForSchemaDecoder(
        configurations, flowElement.getExtensionElements());
  }

  /**
   * @param configurations : Map<String,String> that stores the configuration details(Input/Output
   *     Parameter and Property)</>
   * @param extensionElements : Extension Elements object holds Camunda Property and Input Parameter
   *     for Camunda Elements
   * @return : Map of Configurations where key is the name of the object and value are the related
   *     details.
   */
  private Map<String, String> prepareConfigurationMapForSchemaDecoder(
      Map<String, String> configurations, ExtensionElements extensionElements) {
    Map<String, String> propertyMap =
        BpmnProcessorUtil.getMapOfCamundaProperties(extensionElements);
    Map<String, String> inputParameterMap =
        BpmnProcessorUtil.getMapOfInputOutputParameters(extensionElements);

    if (ObjectUtils.isEmpty(inputParameterMap) && !ObjectUtils.isEmpty(propertyMap)) {
      return propertyMap;
    } else if (!ObjectUtils.isEmpty(inputParameterMap) && ObjectUtils.isEmpty(propertyMap)) {
      return inputParameterMap;
    } else if (!ObjectUtils.isEmpty(inputParameterMap) && !ObjectUtils.isEmpty(propertyMap)) {
      for (Map.Entry<String, String> entry : inputParameterMap.entrySet()) {
        configurations.put(entry.getKey(), entry.getValue());
      }
      for (Map.Entry<String, String> entry : propertyMap.entrySet()) {
        configurations.put(entry.getKey(), entry.getValue());
      }
    }

    return configurations;
  }
}
