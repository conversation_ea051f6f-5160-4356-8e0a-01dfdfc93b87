package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CLOSE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CREATE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_OWNER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType.REMINDER;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Handler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Operator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.OperatorNegation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProjectType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.Incoming;
import org.camunda.bpm.model.bpmn.instance.ConditionExpression;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Output;
import org.camunda.bpm.model.xml.ModelInstance;
import org.camunda.bpm.model.xml.impl.instance.ModelElementInstanceImpl;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * Utility Class for custom workflow related helper methods
 *
 * <AUTHOR>
 */
@UtilityClass
public class CustomWorkflowUtil {
    /**
     * Helper method to create v4 operator from operator object
     *
     * @param op : Operator object
     * @return V4 operator object
     */

    public com.intuit.v4.workflows.definitions.Operator transformOperator(Operator op) {
        com.intuit.v4.workflows.definitions.Operator operator =
                new com.intuit.v4.workflows.definitions.Operator();
        operator.setTypeId(op.getId());
        operator.setDescription(op.getName());
        operator.setSymbol(op.getId());
        return operator;
    }

    /**
     * Helper method to get default value from global attribute list in config
     *
     * @param id
     * @return
     */
    public String getAttributesDefaultValue(CustomWorkflowConfig customWorkflowConfig, String id) {
        return customWorkflowConfig.getAttributes().stream()
                .filter(bmoAttribute -> bmoAttribute.getId().equalsIgnoreCase(id))
                .map(Attribute::getDefaultValue)
                .collect(Collectors.joining());
    }

    /**
     * Helper method to get default operator from global attribute list in config
     *
     * @param id
     * @return
     */
    public String getAttributesDefaultOperator(CustomWorkflowConfig customWorkflowConfig, String id) {
        return customWorkflowConfig.getAttributes().stream()
                .filter(attribute -> attribute.getId().equalsIgnoreCase(id))
                .map(Attribute::getDefaultOperator)
                .collect(Collectors.joining());
    }

    /**
     * Helper method to get default display name from global attribute list in config
     *
     * @param id
     * @return
     */
    public String getAttributesGlobalDisplayName(
            CustomWorkflowConfig customWorkflowConfig, String id) {
        return customWorkflowConfig.getAttributes().stream()
                .filter(bmoAttribute -> bmoAttribute.getId().equalsIgnoreCase(id))
                .map(Attribute::getName)
                .collect(Collectors.joining());
    }

    /**
     * Transform config parameter to v4 inputParameter type
     *
     * @param parameter Parameter object
     * @return
     */
    public InputParameter transformActionToInputParameter(Parameter parameter) {
        InputParameter inputParameter = new InputParameter();
        inputParameter.setParameterName(parameter.getName());
        inputParameter.setParameterType(
                !ObjectUtils.isEmpty(parameter.getFieldType())
                        ? FieldTypeEnum.fromValue(parameter.getFieldType().toUpperCase())
                        : null);
        inputParameter.setRequired(parameter.getRequiredByUI());
        if (null != parameter.getActionByUI())
            inputParameter.setGetOptionsForFieldValue(parameter.getActionByUI().name());
        inputParameter.setMultiSelect(parameter.getMultiSelect());
        inputParameter.setConfigurable(parameter.getConfigurable());
        inputParameter.setFieldValues(parameter.getFieldValues());
        inputParameter.setPossibleFieldValues(parameter.getPossibleFieldValues());
        inputParameter.setHelpVariables(parameter.getHelpVariableDisplayNames());
        return inputParameter;
    }

    /**
     * Transform config parameter to ParameterDetails type
     *
     * @param parameter Parameter object
     * @return
     */
    public ParameterDetails getParameterDetailsFromActionParameter(Parameter parameter) {
        ParameterDetails parameterDetails = new ParameterDetails();
        parameterDetails.setFieldValue(parameter.getFieldValues());
        parameterDetails.setPossibleFieldValues(parameter.getPossibleFieldValues());
        parameterDetails.setConfigurable(BooleanUtils.toBoolean(parameter.getConfigurable()));
        parameterDetails.setRequiredByHandler(BooleanUtils.toBoolean(parameter.getRequiredByHandler()));
        parameterDetails.setRequiredByUI(BooleanUtils.toBoolean(parameter.getRequiredByUI()));
        parameterDetails.setActionByUI(parameter.getActionByUI());
        parameterDetails.setMultiSelect(BooleanUtils.toBoolean(parameter.getMultiSelect()));
        parameterDetails.setHelpVariables(parameter.getHelpVariables());
        parameterDetails.setFieldType(parameter.getFieldType());
        parameterDetails.setValueType(parameter.getValueType());
        parameterDetails.setComputedVariableType(parameter.getComputedVariableType());
        parameterDetails.setComputedVariableDetails(parameter.getComputedVariableDetails());
        parameterDetails.setHandlerFieldName(parameter.getHandlerFieldName());
        return parameterDetails;
    }

    /**
     * Check if custom workflow
     *
     * @param template
     * @return boolean value if template is custom template
     */
    public boolean isCustomWorkflow(Template template) {
        if (ObjectUtils.isEmpty(template)) {
            // If no template info then return true
            return true;
        }
        if (StringUtils.isNotEmpty(template.getName())) {
            // Return true if template has name and name is one of custom template name
            return CustomWorkflowType.templateNameActionKeyMapping().containsKey(template.getName());
        }
        return ObjectUtils.isEmpty(template.getId())
                || TemplateCategory.CUSTOM.name().equals(template.getCategory());
    }

    /**
     * Checks if the definition is a system definition for a custom workflow template
     * In case of start call - process details is null and definitionDetails has system definition information
     * In case of signal call - is called for both parent and child process.
     * @param processDetails
     * @return
     */
    public boolean isCustomWorkflowSystemDefinition(ProcessDetails processDetails, DefinitionDetails definitionDetails){
        DefinitionDetails parentDefinitionDetails;
        if (Objects.isNull(processDetails)) {
            parentDefinitionDetails = definitionDetails;
        } else {
            parentDefinitionDetails = Objects.nonNull(processDetails.getParentProcessDetails())
                ? processDetails.getParentProcessDetails().getDefinitionDetails()
                : processDetails.getDefinitionDetails();
        }

        TemplateDetails templateDetails =  parentDefinitionDetails.getTemplateDetails();
        return org.apache.commons.lang3.ObjectUtils.isNotEmpty(CustomWorkflowType.getActionKey(templateDetails.getTemplateName()))
            && Objects.isNull(parentDefinitionDetails.getRecordType())
            && SYSTEM_OWNER_ID.equalsIgnoreCase(String.valueOf(parentDefinitionDetails.getOwnerId()));
    }

    /**
     * Check if custom approval workflow
     *
     * @param template
     * @return boolean value if template is custom approval template
     */
    public boolean isCustomApprovalWorkflow(Template template) {
        if (ObjectUtils.isEmpty(template)) {
            // If no template info then return false
            return false;
        }
        if (StringUtils.isNotEmpty(template.getName())) {
            // Return true if the template belongs to custom approval category
            return CustomWorkflowType.APPROVAL.getTemplateName().equals(template.getName());
        }
        return false;
    }

    /**
     * Check if custom reminder workflow
     * @param definition Definition
     * @return boolean value
     */
    public boolean isCustomReminderWorkflow(Definition definition) {
        String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definition);
        CustomWorkflowType customWorkflowType = CustomWorkflowType.get(actionKey);
        if (Objects.nonNull(customWorkflowType)) {
            return REMINDER.equals(customWorkflowType);
        }
        return false;
    }

    /**
     * Get action selected based on the conditional expression of the connector
     *
     * @param flowElement
     * @param modelInstance
     * @return
     */
    public boolean getActionSelected(FlowElement flowElement, ModelInstance modelInstance) {
        Collection<Incoming> incomingSequenceElement =
                flowElement.getChildElementsByType(Incoming.class);
        String condition = getConditionalExpression(modelInstance, incomingSequenceElement);
        if (StringUtils.isNotEmpty(condition)) {
            condition = condition.trim().replaceAll("[^a-zA-Z0-9]", "");
            return BooleanUtils.toBoolean(condition);
        }
        return false;
    }

    /**
     * Get conditional expression from the gateway from which the action branch is forked
     *
     * @param modelInstance bpmn model instance {@link org.camunda.bpm.model.bpmn.BpmnModelInstance}
     * @param incomingSequenceElement Collection of Incoming Sequence Flows {@link
     *     Collection<Incoming>}
     * @return
     */
    private String getConditionalExpression(
            ModelInstance modelInstance, Collection<Incoming> incomingSequenceElement) {
        String previousGateWayId = getParentGateway(modelInstance, incomingSequenceElement);
        if (BpmnComponentType.INCLUSIVE_GATEWAY
                .getName()
                .equalsIgnoreCase(
                        modelInstance.getModelElementById(previousGateWayId).getElementType().getTypeName())) {
            // then read the value from the payload and mutate the branch
            String incomingSequenceElementId =
                    incomingSequenceElement.stream()
                            .map(ModelElementInstanceImpl::getTextContent)
                            .collect(Collectors.joining(WorkflowConstants.COMMA));
            SequenceFlow sequenceElement = modelInstance.getModelElementById(incomingSequenceElementId);
            ConditionExpression conditionExpression =
                    Objects.nonNull(sequenceElement.getConditionExpression())
                            ? sequenceElement.getConditionExpression()
                            : modelInstance.newInstance(ConditionExpression.class);
            return conditionExpression.getTextContent();
        }
        return org.apache.commons.lang3.StringUtils.EMPTY;
    }

    /**
     * Gives the Gateway Id from which action branch is forked</>
     *
     * @param modelInstance : bpmn model instance {@link org.camunda.bpm.model.bpmn.BpmnModelInstance}
     * @param incomings : Collection of Incoming Sequence Flows {@link Collection<Incoming>}
     * @return
     */
    public String getParentGateway(ModelInstance modelInstance, Collection<Incoming> incomings) {
        String previousReferenceStateId =
                incomings.stream()
                        .map(ModelElementInstanceImpl::getTextContent)
                        .collect(Collectors.joining(WorkflowConstants.COMMA));

        SequenceFlow nextStates = modelInstance.getModelElementById(previousReferenceStateId);

        return nextStates.getSource().getId();
    }

    /**
     * This function gets list of rule lines from the workflow step
     * @param definition
     * @return
     */

    public List<Rule> getRulesFromDefinition(Definition definition) {

        // Get list of rule lines from the workflow step
        List<RuleLine> ruleLines =
                definition.getWorkflowSteps().stream()
                        .filter(step -> Objects.nonNull(step.getWorkflowStepCondition()))
                        .map(step -> step.getWorkflowStepCondition().getRuleLines())
                        .flatMap(List::stream)
                        .collect(Collectors.toList());

        // RuleLine has set of rules. Get uber list of parameter names from all such rules
        return ruleLines.stream()
                .map(RuleLine::getRules)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * This function gets the rules for the current call activity recurssively from the parent node
     * with negation for MCR reminders
     *
     * @param definition
     * @param workflowStepMap
     * @param currentStepId
     * @return
     */
    public List<Rule> getRulesFromDefinition(Definition definition,
        Map<String, WorkflowStep> workflowStepMap, String currentStepId) {

        if (Objects.isNull(currentStepId) && MapUtils.isEmpty(workflowStepMap)) {
            return getRulesFromDefinition(definition);
        }

        WorkflowLogger.logInfo("Fetching rules for currentStepId=%s entity=%s, step=getRulesFromDefinition",
            currentStepId, definition.getRecordType());

        WorkflowStep rootNode = MultiStepUtil.findFirstWorkflowStep(definition.getWorkflowSteps());

        // List with all nodes in the path in the order from root to the current node
        List<WorkflowStep> stepsInPath = MultiStepUtil.fetchListOfNodesInPath(rootNode,
            currentStepId, workflowStepMap);

        // Ignore the last node since that will be the leaf action node
        List<Rule> rulesList = new ArrayList<>();
        for (int i = 0; i < stepsInPath.size() - 1; i++) {

            WorkflowStep currentStep = stepsInPath.get(i);
            String nextStepWorkflowId = String.valueOf(stepsInPath.get(i + 1).getId());
            boolean isInNoPath = currentStep.getNext().stream().anyMatch(
                next -> (nextStepWorkflowId.equalsIgnoreCase(next.getWorkflowStepId())
                    && NextLabelEnum.NO.equals(next.getLabel())));
            List<RuleLine> ruleLines = currentStep.getWorkflowStepCondition().getRuleLines();

            List<Rule> rules = ruleLines.stream()
                .map(RuleLine::getRules)
                .flatMap(List::stream)
                .collect(Collectors.toList());

            if (isInNoPath) {
                rules = negateRules(rules);
            }
            rulesList.addAll(rules);
        }
        return rulesList;
    }

    /**
     * It fetches all the rules of the current workflow step
     *
     * @param workflowStep
     * @return
     */
    public List<RuleLine.Rule> getRulesOfCurrentStep(WorkflowStep workflowStep) {

        List<RuleLine.Rule> currentWorkflowStepRules = new ArrayList<>();

        // RuleLine has set of rules. Get uber list of parameter names from all such rules
        currentWorkflowStepRules.addAll(
            workflowStep.getWorkflowStepCondition().getRuleLines().stream()
                .map(RuleLine::getRules)
                .flatMap(List::stream)
                .collect(Collectors.toList()));

        return currentWorkflowStepRules;
    }

    /**
     * Get the values of trigger from definition for entity operations
     * Input:-
     * <pre>
     * [{
     * 	"parameterType": null,
     * 	"helpVariables": [],
     * 	"getOptionsForFieldValue": null,
     * 	"parameterName": "entityOperation",
     * 	"fieldValues": ["created, updated"],
     * 	"configurable": null,
     * 	"multiSelect": null,
     * 	"possibleFieldValues": []
     *    }]
     * </pre>
     * Output: Map of ParameterName and ParameterDetails.
     * @param definition
     * @return
     */
    public Map<String, ParameterDetails> getTriggerFromDefinition(Definition definition) {
        Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
        definition.getWorkflowSteps().forEach(step -> {
            if (!ObjectUtils.isEmpty(step.getTrigger())) {
                step.getTrigger().getParameters().forEach( parameter -> {
                    ParameterDetails parameterDetails = new ParameterDetails();
                    parameterDetails.setFieldValue(parameter.getFieldValues());
                    parameterDetailsMap.putIfAbsent(parameter.getParameterName(), parameterDetails);
                });
            }
        });
        return parameterDetailsMap;
    }


    public Handler getHandler(List<Handler> handlers, String handlerId) {
        return handlers.stream()
                .filter(handler -> handler.getId().equalsIgnoreCase(handlerId))
                .findFirst()
                .get();
    }


    /**
     * Returns StartEvent element. StartEvent will always come from uber template
     *
     * @param bpmnModelInstance {@link BpmnModelInstance}
     * @return
     */
    public FlowElement findStartEventElement(BpmnModelInstance bpmnModelInstance) {
        Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
        AtomicReference<FlowElement> startEventElement = new AtomicReference<>();
        processes.forEach(
                process -> {
                    process
                            .getFlowElements()
                            .forEach(
                                    element -> {
                                        if (BpmnComponentType.START_EVENT
                                                .getName()
                                                .equalsIgnoreCase(element.getElementType().getTypeName())) {
                                            startEventElement.set(element);
                                            return;
                                        }
                                    });
                    // If element found, break the process loop
                    if (startEventElement.get() != null) {
                        return;
                    }
                });

        return startEventElement.get();
    }

    // In case of custom workflow, we scan through the action-parameters passed by the client. We process the
    // field values of the action-parameters ( eg. replacing the email-default message with payload
    // value and processing over it to bring it in desired format )  that are required by UI and drop
    // others. We will never override the fields other than field-value as they are extracted from the
    // config. For example client should not be able to override fieldtype. In custom workflow, we
    // are using the info stored in configuration.
    /**
     * Returns parameter details with the replaced field values of UI-required parameters
     *
     * @param customWorkflowConfig {@link BpmnModelInstance}
     * @param actionId {@link String}
     * @param actionParameters {@link List<InputParameter>}
     * @param actionKey {@link String}
     * @param recordType {@link String}
     * @return
     */
    public Map<String, ParameterDetails> processActionParameterDetails(
            CustomWorkflowConfig customWorkflowConfig,
            String actionId,
            List<InputParameter> actionParameters,
            String actionKey,
            String recordType) {
        // Creating a map of parameter name and parameter for a faster access.
        // action key is passed to filter the action groups based on what's given in create def payload
        // (approval or reminder)
        Map<String, Parameter> parameterNameMap =
                customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
                        .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
                        .map(ActionGroup::getActions)
                        .flatMap(Collection::stream)
                        .filter(action -> actionId.startsWith(action.getId()))
                        .map(Action::getParameters)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toMap(Parameter::getName, Function.identity()));
        // Consider only those parameters which can be set by UI
        Predicate<InputParameter> predicate =
                inputParameter ->
                        parameterNameMap.containsKey(inputParameter.getParameterName())
                                && BooleanUtils.toBoolean(
                                parameterNameMap.get(inputParameter.getParameterName()).getRequiredByUI());

        return actionParameters.stream()
                .filter(predicate)
                .collect(
                        Collectors.toMap(
                                InputParameter::getParameterName,
                                param ->
                                        buildParameterDetailsFromInputParameter(
                                                parameterNameMap.get(param.getParameterName()), param)));
    }


    /**
     * Mapper Method to Map Inputparameter to ParameterDetails
     *
     * @param inputParameter {@link InputParameter}
     * @return
     */
    private ParameterDetails buildParameterDetailsFromInputParameter(
            Parameter configParam, InputParameter inputParameter) {
        ParameterDetails param = new ParameterDetails();

        // Use the value provided by client
        param.setFieldValue(
                replaceHelpVariableInFieldValues(configParam, inputParameter.getFieldValues()));

        // Rest all should come from the config
        param.setFieldType(configParam.getFieldType());
        param.setRequiredByUI(BooleanUtils.toBoolean(configParam.getRequiredByUI()));
        param.setRequiredByHandler(BooleanUtils.toBoolean(configParam.getRequiredByHandler()));
        param.setMultiSelect(BooleanUtils.toBoolean(configParam.getMultiSelect()));
        param.setConfigurable(BooleanUtils.toBoolean(configParam.getConfigurable()));
        param.setPossibleFieldValues(configParam.getPossibleFieldValues());
        if (StringUtils.isNotEmpty(configParam.getHandlerFieldName())) {
            param.setHandlerFieldName(configParam.getHandlerFieldName());
        }
        return param;
    }

    /**
     * This method replaces help variables display name with help variable equivalent process variable
     * name in action parameters.
     *
     * <p>For example [[Invoice Number]] or [[Bill Number]] is replaced with [[DocNumber]]
     */
    public List<String> replaceHelpVariableInFieldValues(
            Parameter parameter, List<String> paramFieldValues) {
        if (ObjectUtils.isEmpty(parameter.getHelpVariables())) {
            return paramFieldValues;
        }

        Map<String, String> helpVariableMap = new HashMap<>();
        for (String helpVariable : parameter.getHelpVariables()) {
            String[] tokens = helpVariable.split(WorkflowConstants.COLON);
            helpVariableMap.put(tokens[0], tokens[1]);
        }
        return paramFieldValues.stream()
                .map(fieldValue -> replaceHelpVariable(helpVariableMap, fieldValue))
                .collect(Collectors.toList());
    }

    public List<String> replaceHelpVariableInFieldValuesFromConfigMap(Map<String, String> helpVariableMap, List<String> paramFieldValues) {
        return paramFieldValues.stream()
            .map(fieldValue -> replaceHelpVariable(helpVariableMap, fieldValue))
            .collect(Collectors.toList());
    }

    /**
     * Replacing help variables with proper context to be replaced at the time of trigger eg. [[CustomerEmail]]
     * @param helpVariableMap
     * @param fieldValue
     * @return String
     */


    private String replaceHelpVariable(Map<String, String> helpVariableMap, String fieldValue) {
        for (Map.Entry<String, String> helpVariable : helpVariableMap.entrySet()) {
            fieldValue =
                    fieldValue.replace(
                            String.format("[[%s]]", helpVariable.getKey()),
                            String.format("[[%s]]", helpVariableMap.get(helpVariable.getKey())));
        }
        return fieldValue;
    }


    /**
     * Returns Action parameters for the specified action and record type from the custom-workflow config
     *
     * @param customWorkflowConfig custom-workflow config to extract action configurations
     * @param recordType           record type of the entity
     * @param actionKey         actionmapper to fetch actions for particular action id
     * @param actionId             actionid of the action
     * @return action {@link com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action}
     */
    public com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action getConfigActionEntry(
            CustomWorkflowConfig customWorkflowConfig, String recordType, String actionKey, String actionId) {
        return customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
                .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
                .map(ActionGroup::getActions)
                .flatMap(Collection::stream)
                .filter(action -> action.getId().equals(actionId))
                .findFirst()
                .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));
    }

    /**
     * Returns All actions that are associated with the record type and actionKey from the custom-workflow config.
     * @param customWorkflowConfig custom-workflow config to extract action configurations
     * @param recordType record type of the entity
     * @param actionKey  actionKey such as approval or reminder
     * @return List<Action> {@link com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action}
     */
    public List<com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action> getAllConfigActionsForRecordTypeAndActionKey(
        CustomWorkflowConfig customWorkflowConfig, String recordType, String actionKey) {
        return customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
            .map(ActionGroup::getActions)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    /**
     * Fetches on demand configured actions for the given recordType and actionKey
     * @param customWorkflowConfig
     * @param recordType
     * @param actionKey
     * @return
     */
    public List<String> getOnDemandConfiguredActions(
        CustomWorkflowConfig customWorkflowConfig, String recordType, String actionKey) {
        return Optional.ofNullable(customWorkflowConfig.getRecordObjForType(recordType))
            .map(Record :: getActionGroups)
            .orElse(Collections.emptyList())
            .stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
            .map(ActionGroup::getOnDemandActionIds)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    /**
     * Extracting project meta-data for the action like project type and task type
     *
     * @param recordAction
     * @param actionMapper
     * @param recordType
     * @return Map<String,ParameterDetails>
     */
    public Map<String, ParameterDetails> extractProjectMetaDataForAction(
            Action recordAction, WorkflowStep.ActionMapper actionMapper, String recordType) {

        // If the action is not selected by the user, return empty project meta data.
        if (!actionMapper.getAction().isSelected()) {
            return Collections.emptyMap();
        }

        Map<String, ParameterDetails> additionalParams =
                recordAction.getParameters().stream()
                        .filter(param -> !BooleanUtils.toBoolean(param.getRequiredByUI()))
                        .collect(
                                Collectors.toMap(
                                        Parameter::getName,
                                        CustomWorkflowUtil::getParameterDetailsFromActionParameter));
        // Set project/task type if required
        setProjectMetadataForAction(actionMapper, additionalParams, recordType, recordAction);
        return additionalParams;
    }

    /**
     * Updates parameter value for special parameters like project/task type. For example, invoice
     * reminder
     *
     * @param actionMapper
     * @param actionParameters
     * @return
     */
    private void setProjectMetadataForAction(
            WorkflowStep.ActionMapper actionMapper,
            Map<String, ParameterDetails> actionParameters,
            String recordType, Action recordAction) {
        ProjectType project = getProjectTypeForAction(actionMapper, recordType, recordAction);
        if (project == null) {
            return;
        }
        actionParameters.forEach(
                (actionKey, parameterDetails) -> {
                    switch (actionKey) {
                        case Constants.PROJECT_TYPE:
                            parameterDetails.setFieldValue(Arrays.asList(project.name()));
                            break;
                        case Constants.TASK_TYPE:
                            parameterDetails.setFieldValue(Arrays.asList(project.getTaskType().name()));
                            break;
                        default:
                            break;
                    }
                });
    }

    /**
     * Sets project type for action
     *
     * @param actionMapper
     * @param recordType
     * @param recordAction
     * @return projectType
     */
    public ProjectType getProjectTypeForAction(
            WorkflowStep.ActionMapper actionMapper, String recordType, Action recordAction) {
        // See if there is  a close task parameter in action. Based on it, find the project and task
        Optional<InputParameter> closeTaskParameter =
                actionMapper.getAction().getParameters().stream()
                        .filter(
                                parameter ->
                                        WorkflowConstants.CLOSE_TASK.equalsIgnoreCase(parameter.getParameterName()))
                        .findFirst();

        // If close task parameter is not found or doesn't have non null field value, then check
        // if config close task parameter has any default values, otherwise return null
        if (closeTaskParameter.isEmpty() ||
            CollectionUtils.isEmpty(closeTaskParameter.get().getFieldValues()) ||
            closeTaskParameter.get().getFieldValues().get(0) == null){

            if (CREATE_TASK.equalsIgnoreCase(
                actionMapper.getAction().getId().getLocalId().split(WorkflowConstants.UNDERSCORE)[0])) {
                // check if there is close task parameter in config with default values
                closeTaskParameter = getInputParamFromConfig(recordAction, CLOSE_TASK);

                // throw exception if close task is not found except for
                // reminder workflows as it will be overridden in CreateMultiActionStepProcessor
                if (!REMINDER.getActionKey().equals(actionMapper.getActionKey())) {
                    WorkflowVerfiy.verify(closeTaskParameter.isEmpty(),
                        WorkflowError.CLOSE_TASK_ACTION_NOT_FOUND);
                }
            }
            if (closeTaskParameter.isEmpty()) {
                return null;
            }
        }

        // Find project type to be used based on record, custom workflow usecase and close task value
        ProjectType project =
                ProjectType.getProject(
                        recordType,
                        actionMapper.getActionKey(),
                        closeTaskParameter.get().getFieldValues().get(0));
        return project;
    }

    /**
     * Get close task parameter from config record action
     * @param recordAction
     * @return
     */
    private Optional<InputParameter> getInputParamFromConfig(Action recordAction, String paramName) {
        Parameter configCloseTaskParameter =
                recordAction.getParameters().stream()
                        .filter(parameter -> paramName.equalsIgnoreCase(parameter.getName()))
                        .findFirst()
                        .orElse(null);
        return getCloseTaskInputParameter(configCloseTaskParameter);
    }

    /**
     * Get close task input parameter from config parameter if the config parameter has default values
     *
     * @param configCloseTaskParameter
     * @return
     */
    public Optional<InputParameter> getCloseTaskInputParameter(Parameter configCloseTaskParameter) {
        if (Objects.nonNull(configCloseTaskParameter)
                && !configCloseTaskParameter.getFieldValues().isEmpty()
                && Objects.nonNull(configCloseTaskParameter.getFieldValues().get(0))) {
            return Optional.of(transformActionToInputParameter(configCloseTaskParameter));
        }
        return Optional.empty();
    }

    /**
     * Get replacement for fieldValues wherein we replace help variable
     * names to display names. For example [[DocNumber]]
     * is replaced with [[Invoice Number]] or [[Bill Number]]
     * @param fieldValues
     * @param helpVariables
     */
    public List<String> getReplacedFieldValuesWithDisplayNames(List<String> fieldValues, List<String> helpVariables) {
        Map<String, String> displayToProcessVarNameMap = helpVariables.stream()
                .map(helpVariable -> helpVariable.split(WorkflowConstants.COLON))
                .collect(Collectors.toMap(token -> token[0], token -> token[1]));
        List<String> replacedFieldValues = new ArrayList<>();
        fieldValues.forEach(fieldValue -> {
            for (Map.Entry<String, String> displayName : displayToProcessVarNameMap.entrySet()) {
                fieldValue = fieldValue.replace(String.format("[[%s]]", displayName.getValue()),
                        String.format("[[%s]]", displayName.getKey()));
            }
            replacedFieldValues.add(fieldValue);
        });
        return replacedFieldValues;
    }

    public Map<String, ParameterDetails> getHelpVariableParameterMap(Action recordAction) {
        Map<String, ParameterDetails> helpVariableParamMap = new HashMap<>();
        for (Parameter parameter : recordAction.getParameters()) {
            if (!ObjectUtils.isEmpty(parameter.getHelpVariables())) {
                for (String helpVariable : parameter.getHelpVariables()) {
                    // helpVariable is a string which contains following in order separated by COLON
                    // displayName : handlerFieldName : fieldType
                    // eg.  Company Name:CompanyName:string
                    String[] tokens = helpVariable.split(WorkflowConstants.COLON);
                    String handlerFieldName = tokens[1];
                    if (!helpVariableParamMap.containsKey(handlerFieldName)) {
                        ParameterDetails parameterDetails = new ParameterDetails();
                        parameterDetails.setHandlerFieldName(handlerFieldName);
                        parameterDetails.setValueType(ParameterDetailsValueType.PROCESS_VARIABLE);
                        parameterDetails.setRequiredByHandler(true);
                        parameterDetails.setFieldType(tokens[2]);
                        helpVariableParamMap.put(handlerFieldName, parameterDetails);
                    }
                }
            }
        }
        return helpVariableParamMap;
    }

    public Action getRecordActionFromConfig(
            CustomWorkflowConfig customWorkflowConfig,
            String recordType,
            String actionKey,
            String actionId) {
        return customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
                .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
                .map(ActionGroup::getActions)
                .flatMap(Collection::stream)
                .filter(action -> action.getId().equals(actionId))
                .findFirst()
                .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));
    }

    public Action getRecordActionFromConfig(
            CustomWorkflowConfig customWorkflowConfig,
            String recordType,
            String actionKey,
            String actionId,
            String subActionId) {

        ActionGroup actionGroupForRecordAndActionKey =
            customWorkflowConfig.getRecordObjForType(recordType).getActionGroups()
                .stream().filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
                .findFirst().orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));

        WorkflowVerfiy.verify(
            !actionGroupForRecordAndActionKey.getActionIdMapper().getActionId().equals(actionId)
            , WorkflowError.INVALID_ACTION_KEY
        );

        List<String> subActionIdsForAction = actionGroupForRecordAndActionKey.getActionIdMapper().getSubActionIds();

        return actionGroupForRecordAndActionKey.getActions().stream()
            .filter(action -> subActionIdsForAction.contains(action.getId()))
            .filter(subAction -> subAction.getId().equals(subActionId))
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));
    }

    /**
     * Get a map of help variables from action parameters.
     *
     * @param processVariableDataMap
     * @param parameter
     * @return HelpVariable Map , which is used in substituting placeholders
     */
    public Map<String, String> populateHelpProcessVariables(
        Map<String, ProcessVariableData> processVariableDataMap, Parameter parameter) {

        if (org.apache.commons.lang3.ObjectUtils.isEmpty(parameter.getHelpVariables())) {
            return Collections.emptyMap();
        }
        Map<String, String> helpVariables = new HashMap<>();

        for (String helpVariable : parameter.getHelpVariables()) {
            String[] tokens = helpVariable.split(WorkflowConstants.COLON);
//      Help Variable example in config: Customer Email:CustomerEmail:string
            processVariableDataMap.putIfAbsent(tokens[1],
                ProcessVariableData.builder().type(tokens[2]).build());
            helpVariables.put(tokens[0], tokens[1]);
        }

        return helpVariables;
    }

    public String getActionKeyFromWorkflowSteps(Definition definition) {
        return definition.getWorkflowSteps().stream()
            .filter(workflowStep -> Objects.nonNull(workflowStep.getActionGroup()))
            .map(workflowStep -> workflowStep.getActionGroup().getActionKey())
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));
    }

    /**
     * Checks whether the given string value is of boolean type or not
     * @param value
     */
    public Boolean isBoolean(String value) {
        return isFalse(value) || isTrue(value);
    }

    /**
     * Checks whether the given string value is equals to false or not
     * @param value
     */
    public Boolean isFalse(String value) {
        return Boolean.FALSE.toString().equalsIgnoreCase(value);
    }

    /**
     * Checks whether the given string value is equals to true or not
     * @param value
     */
    private Boolean isTrue(String value) {
        return Boolean.TRUE.toString().equalsIgnoreCase(value);
    }

    /**
     * This function generates definitionInstance object
     *
     * @param bpmnModelInstance bpmn model instance
     * @param dmnModelInstances list of dmn model instances
     * @param templateDetails   templateDetails object
     * @return definitionInstance object
     */
    public DefinitionInstance generateDefinitionInstanceForBpmnProcessing(
        BpmnModelInstance bpmnModelInstance,
        List<DmnModelInstance> dmnModelInstances,
        TemplateDetails templateDetails) {
        final Definition definition = new Definition();
        DefinitionInstance definitionInstance = new DefinitionInstance(
            definition,
            bpmnModelInstance,
            dmnModelInstances,
            templateDetails);
        return definitionInstance;
    }

    /**
     * This function returns the defaultAttributes that is used to build ruleLines inside
     * workflowStepCondition. In case of precanned template, there can be steps defined in the config
     * and these steps can also include defaultAttributes.
     * <b>Priority of getting defaultAttributes from config</b>
     * <p>
     * 1. DefaultAttributes defined inside config steps 2. We will pick defaultAttributes defined at
     * the config template level (outside the step) defined in templates-default.yml 3. We will pick
     * the attributes from the  actionGroup level defined in templateconfig-default.yml 4. We will
     * pick defaultAttributes defined at the record level
     *
     * @param recordAttributeMap   map of record attributes
     * @param customWorkflowConfig custom workflow config
     * @param record               record object
     * @param actionGroup          record action group
     * @param configStep           configStep from precanned template config
     * @return set of defaultAttributes
     */
    public Set<String> getDefaultAttributes(
        Map<String, Attribute> recordAttributeMap,
        CustomWorkflowConfig customWorkflowConfig,
        Record record,
        ActionGroup actionGroup,
        Steps configStep) {
        if (Objects.nonNull(configStep) && !CollectionUtils.isEmpty(
            configStep.getAttributes())) {
            return recordAttributeMap.keySet();
        }
        if (Objects.nonNull(record) && (record instanceof ConfigTemplate)
            && !CollectionUtils.isEmpty(
            record.getDefaultAttributes())) {
            return record.getDefaultAttributes();
        }
        if (Objects.nonNull(actionGroup) && !CollectionUtils.isEmpty(
            actionGroup.getDefaultAttributes())) {
            return actionGroup.getDefaultAttributes();
        }
        Record recordInstance = customWorkflowConfig.getRecordObjForType(record.getId());
        return Objects.nonNull(recordInstance) && !CollectionUtils.isEmpty(
            recordInstance.getDefaultAttributes()) ?
            recordInstance.getDefaultAttributes() : new HashSet<>();
    }

    /**
     * It sets the data type of output column as string
     *
     * @param modelInstance
     */
    public void setOutputDmnColumnType(DmnModelInstance modelInstance) {
        Collection<Output> dmnOutputColumn = modelInstance.getModelElementsByType(Output.class);
        dmnOutputColumn.stream().findFirst().get().setTypeRef(WorkflowConstants.STRING_TYPE_CAMUNDA);
    }

    /**
     * @param dmnModelInstance
     * @return {@Link DecisionTable}
     */
    public DecisionTable getDecisionTable(DmnModelInstance dmnModelInstance) {

        DecisionTable decisionTable =
            dmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
                .findFirst()
                .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DECISION_TABLE_MISSING));

        return decisionTable;
    }

    /**
     * Find the DmnModelInstance from the list of DmnModelInstance which is linked to the current
     * dmnElementId
     *
     * @param definitionInstance
     * @param dmnElementId
     * @return
     */
    public DmnModelInstance getDmnModelInstance(DefinitionInstance definitionInstance, String dmnElementId) {
        DmnModelInstance dmnModelInstance =
            definitionInstance.getDmnModelInstanceList().stream()
                .filter(
                    modelInstance ->
                        Objects.nonNull(
                            modelInstance.getModelElementById(dmnElementId)))
                .findFirst()
                .orElseThrow(() -> new WorkflowGeneralException((WorkflowError.DMN_NOT_FOUND)));
        return dmnModelInstance;
    }

    /**
     * Evaluates whether Feel expression is supported. Feel expression can be used only for
     * single definition right now.
     * @param featureFlagManager
     * @param templateDetails
     * @return
     */
    public boolean shouldUseFeelExpr(FeatureFlagManager featureFlagManager,
        TemplateDetails templateDetails) {
        if (Objects.isNull(templateDetails) || Objects.isNull(
            templateDetails.getDefinitionType())) {
            WorkflowLogger.logInfo("Step=Check_FEEL_Expr_Eligibility Result=ERROR");
        }
        return DefinitionType.SINGLE.equals(templateDetails.getDefinitionType())
            && featureFlagManager.getBoolean(WorkflowConstants.FEEL_EXPR_SUPPORTED, Boolean.FALSE);
    }

    /**
     * Gets right language expression determined by a Feature Flag
     * @param useFeelExpr
     */
    public String getInputExpressionLanguage(boolean useFeelExpr) {
        return useFeelExpr
            ? WorkflowConstants.FEEL_EXPRESSION_LANGUAGE
            : WorkflowConstants.JUEL_EXPRESSION_LANGUAGE;
    }

  /**
   * Returns the set of Parameter Names for a record containing attributes that are marked
   * obfuscated in the config.
   *
   * @param record
   * @param actionKey : For instance reminder, approval etc
   *     <p>Returns output in format
   *     <p>{ "sendForApproval" : { "assignee" : "~invoice.AssignedTo" }, "createTask" : {
   *     "taskName" : "~invoice.TaskName" } }
   * @return
   */
  public Map<String, Map<String, List<String>>> getObfuscatedParameterMap(
      Record record, String actionKey) {
    final Map<String, Map<String, List<String>>> obfuscateParameterNames = new HashMap<>();
    record.getActionGroups().stream()
        .filter(actionGroup -> actionKey.equalsIgnoreCase(actionGroup.getId()))
        .forEach(
            actionGroup ->
                actionGroup
                    .getActions()
                    .forEach(
                        action ->
                            obfuscateParameterNames.putAll(prepareActionParameterSet(action))));
    return obfuscateParameterNames;
  }

  /**
   * Returns the set of Conditional Parameter Names for a record containing attributes that are
   * marked obfuscated in the config.
   *
   * @param record
   * @return
   */
  public Set<String> getObfuscatedParameterSetForConditions(Record record) {
    return record.getAttributes().stream()
        .filter(
            attribute ->
                Objects.nonNull(attribute.getObfuscate())
                    && attribute.getObfuscate().equals(Boolean.TRUE))
        .map(Attribute::getName)
        .collect(Collectors.toSet());
  }

  /**
   * This method prepares the set of Parameters with their name, later to be used to redact values
   * </>
   *
   * @param action : Action Attributes from the config
   * @return
   */
  private Map<String, Map<String, List<String>>> prepareActionParameterSet(Action action) {
    final String actionId = action.getId();
    Map<String, List<String>> value =
        action.getParameters().stream()
            .filter(
                parameter ->
                    org.apache.commons.lang3.ObjectUtils.isNotEmpty(parameter.getObfuscate())
                        && Boolean.valueOf(parameter.getObfuscate()))
            .collect(Collectors.toMap(Parameter::getName, Parameter::getFieldValues));
    return Map.of(actionId, value);
  }

    /**
     * This function negates the rules if the current node is in the no path of the parent.
     *
     * If number of rules is greater than 1, then the negation will have an OR eg: !(A && B) = !A || !B OR is
     * not supported by v3 queries, hence these conditions wont be added to filter conditions and
     * extra records from app duzzit will be removed while dmn evaluation
     *
     * @param rules
     * @return
     */
    // Rules will never be empty
    private List<Rule> negateRules(List<Rule> rules) {
        List<Rule> negatedRuleList = new ArrayList<>();
        if (rules.size() > 1) {
            return negatedRuleList;
        }
        Rule rule = rules.stream().findFirst().get();
        String conditionExpressionElements[] = rule.getConditionalExpression()
            .split(WorkflowConstants.WHITESPACE_REGEX);
        WorkflowVerfiy.verify(conditionExpressionElements.length == 0 || Objects.isNull(OperatorNegation.getNegation(
            StringUtils.trim(conditionExpressionElements[0]))), WorkflowError.INVALID_CONDITIONAL_EXPRESSION);
        conditionExpressionElements[0] = OperatorNegation.getNegation(
            StringUtils.trim(conditionExpressionElements[0]));
        Rule newRule = new Rule(rule);
        newRule.setConditionalExpression(
            String.join(WorkflowConstants.SPACE, conditionExpressionElements));
        return Arrays.asList(newRule);
    }
}
