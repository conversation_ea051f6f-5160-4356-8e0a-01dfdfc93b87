package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DefinitionDeploymentUtil {


	private final String UNDERSCORE = "_";
	  
	/**
	  * Using Template Details, deployment-name is prepared.
	  * The method will be called for SINGLE and SYSTEM Definitions.
	  * @param templateDetails
	  * @return
	  */
	public String getDeploymentName(TemplateDetails templateDetails){
	   return new StringBuilder().append(templateDetails.getTemplateName()).append(UNDERSCORE) 
			   .append(templateDetails.getVersion()).toString();
	}
	
	/**
	 * The method will be called for User Definition : Custom or PreCanned.
	 * There is only customReminder which has multiple Definition allowed and is going to migrate to SDEF.
	 * Hence, templateName can be used with recordType.
	 * @param definitionDetails
	 * @return
	 */
	public String getDeploymentName(DefinitionInstance definitionInstance){
		Definition definition = definitionInstance.getDefinition();
		DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
		StringBuilder sBuilder = new StringBuilder().append(definition.getName()).append(UNDERSCORE)
				.append(definition.getRecordType());
		String version = "1";
		if(null != definitionDetails) {
			version = Integer.toString(definitionDetails.getVersion() + 1);
		} 
		sBuilder.append(UNDERSCORE).append(WASContext.getOwnerId())
			.append(UNDERSCORE).append(version);
		
		return sBuilder.toString();
	}
	
}