package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;

/**
 * <AUTHOR>
 * Util method for Batch jobs
 */

public class DeploymentUtil {
  public static final String APP_NAME = "APP_NAME";
  public static final String WORKFLOW_APP_SERVICE_NAME = "wkflatmnsvc";

  /**
   *
   * For Multiple Deployments such as Chaos Deployment pods, Dont let the batch jobs run there
   * @return
   */
  public static boolean isAppDeploymentPod() {
    String appName = System.getenv(APP_NAME);
    WorkflowLogger.logInfo("appName " + appName);
    return isNotEmpty(appName) && (WORKFLOW_APP_SERVICE_NAME.equals(appName));
  }
}
