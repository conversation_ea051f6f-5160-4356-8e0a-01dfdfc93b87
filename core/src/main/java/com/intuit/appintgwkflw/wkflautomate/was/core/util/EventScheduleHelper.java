package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.*;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveSchedulingRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleWorkflowAction;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import com.intuit.v4.workflows.Definition;

import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Helper class for event scheduler
 */
@Component
@AllArgsConstructor
public class EventScheduleHelper {

  private final EventScheduleConfig eventScheduleConfig;
  private final EventScheduleService eventScheduleService;
  private final SchedulingService schedulingService;
  private final SchedulerDetailsRepository schedulerDetailsRepository;
  private final FeatureFlagManager featureFlagManager;
  private final ActionModelToScheduleRequestMapper actionModelToScheduleRequestMapper;
  private final DefinitionDetailsRepository definitionDetailsRepository;

  /**
   * checks if event scheduling has been enabled
   *
   * @return
   */
  public boolean isEventSchedulingConfigEnabled() {
    return eventScheduleConfig.isEnabled();
  }

  /**
   * check if event scheduling is enabled for workflow and ownerId
   *
   * @param workflow
   * @return
   */
  public boolean isEventSchedulingEnabledForWorkflow(String workflow, String ownerId) {
    if (StringUtils.isBlank(ownerId) || !isEventSchedulingConfigEnabled()) {
      return false;
    }
    // adding check for customScheduledActions, because it is 100% rollout.
    //#TODO clean this up after LD to IXP migration
    // https://jira.intuit.com/browse/QBOES-21483
    if (CUSTOM_SCHEDULED_ACTIONS.getName().equals(workflow)) {
      return true;
    }
    return featureFlagManager.getBoolean(
        WorkflowConstants.ESS_TRIGGER_ENABLED, false, workflow, Long.valueOf(ownerId));
  }

  /**
   * Use config to get list of all the schedule actions with start date for the given workflow
   *
   * @param workflowName
   * @return
   */
  public Optional<List<EventScheduleWorkflowActionModel>> getWorkflowScheduleActions(
      Definition definition, String workflowName) {
    if (MapUtils.isEmpty(eventScheduleConfig.getWorkflow())
        || eventScheduleConfig.getWorkflow().get(workflowName) == null
        || MapUtils.isEmpty(
        eventScheduleConfig.getWorkflow().get(workflowName).getScheduleActions())) {
      return Optional.empty();
    }

    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActions =
        eventScheduleConfig.getWorkflow().get(workflowName).getScheduleActions().entrySet().stream()
            .map(
                scheduleWorkflowActionEntry -> {
                  EventScheduleWorkflowAction eventscheduleWorkflowAction =
                      scheduleWorkflowActionEntry.getValue();
                  RecurrenceRule recurrenceRule =
                      definition == null ? null : definition.getRecurrence();
                  String startDateStr =
                      RecurrenceUtil.isValidRecurrence(recurrenceRule)
                          ? recurrenceRule.getStartDate().toLocalDate().toString()
                          : eventscheduleWorkflowAction.getStartDate();
                  LocalDate startDate =
                      StringUtils.isEmpty(startDateStr) ? new LocalDate() : new LocalDate(startDateStr);
                  startDate = startDate.plusDays(
                      eventscheduleWorkflowAction.getNoOfDaysToBeAddedToStartDate());

                  String endDateStr =
                          RecurrenceUtil.isValidRecurrenceWithEndDate(recurrenceRule)
                              ? recurrenceRule.getEndDate().toLocalDate().toString()
                              : null;
                  
                  LocalDate endDate =
                          StringUtils.isEmpty(endDateStr) ? null : new LocalDate(endDateStr).plusDays(
                                  eventscheduleWorkflowAction.getNoOfDaysToBeAddedToEndDate());
                      
                  return new EventScheduleWorkflowActionModel(
                      workflowName + WorkflowConstants.UNDERSCORE
                          + scheduleWorkflowActionEntry.getKey(), startDate, endDate, recurrenceRule);
                })
            .collect(Collectors.toList());
    return Optional.of(eventScheduleWorkflowActions);
  }

  /**
   * Use config to get list of all the schedule actions with start date for the given workflow
   *
   * @param schedulingMetaData
   * @return
   */
  public Optional<List<EventScheduleWorkflowActionModel>> getWorkflowScheduleActionsForScheduling(
          SchedulingMetaData schedulingMetaData) {
    String workflowName = schedulingMetaData.getWorkflowName();
    if (MapUtils.isEmpty(eventScheduleConfig.getWorkflow())
            || eventScheduleConfig.getWorkflow().get(workflowName) == null
            || MapUtils.isEmpty(
            eventScheduleConfig.getWorkflow().get(workflowName).getScheduleActions())) {
      return Optional.empty();
    }

    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActions =
            eventScheduleConfig.getWorkflow().get(workflowName).getScheduleActions().entrySet().stream()
                    .map(
                            scheduleWorkflowActionEntry -> {
                              EventScheduleWorkflowAction eventscheduleWorkflowAction =
                                      scheduleWorkflowActionEntry.getValue();
                              RecurrenceRule recurrenceRule =
                                      schedulingMetaData.getRecurrenceRule();
                              String startDateStr =
                                      RecurrenceUtil.isValidRecurrence(recurrenceRule)
                                              ? recurrenceRule.getStartDate().toLocalDate().toString()
                                              : eventscheduleWorkflowAction.getStartDate();
                              LocalDate startDate =
                                      StringUtils.isEmpty(startDateStr) ? new LocalDate() : new LocalDate(startDateStr);
                              startDate = startDate.plusDays(
                                      eventscheduleWorkflowAction.getNoOfDaysToBeAddedToStartDate());

                              String endDateStr =
                                      RecurrenceUtil.isValidRecurrenceWithEndDate(recurrenceRule)
                                              ? recurrenceRule.getEndDate().toLocalDate().toString()
                                              : null;

                              LocalDate endDate =
                                      StringUtils.isEmpty(endDateStr) ? null : new LocalDate(endDateStr).plusDays(
                                              eventscheduleWorkflowAction.getNoOfDaysToBeAddedToEndDate());

                              return new EventScheduleWorkflowActionModel(
                                      workflowName + WorkflowConstants.UNDERSCORE
                                              + scheduleWorkflowActionEntry.getKey(), startDate, endDate, recurrenceRule);
                            })
                    .collect(Collectors.toList());
    return Optional.of(eventScheduleWorkflowActions);
  }

   /**
   * <pre>
   * If event schedule is enabled for a given workflow, prepares two tasks
   * 1. Save schedule details in ESS
   * 2. Save schedule details in database
   *
   * @param state
   * @param workflowName
   * @return list of tasks
   * </pre>
   */

  public List<Task> prepareScheduleCreateTasks(State state, String workflowName) {
    /* TODO: ESS is still using FFs to determine if ESS schedule task should be created, Will be taken care during migration */
    if (state == null
        || !isEventSchedulingEnabledForWorkflow(
            workflowName, state.getValue(AsyncTaskConstants.REALM_ID_KEY))) {
      return Collections.emptyList();
    }

    if (state != null) {
      DefinitionInstance definitionInstance = state.getValue(
          AsyncTaskConstants.DEFINITION_INSTANCE);
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
          getWorkflowScheduleActions(definitionInstance.getDefinition(), workflowName));
    }
    //TODO:  What if any of these tasks fails, in that case we have to write down the
    // rollback strategy.
    return List.of(
        new SaveEventScheduleWorkflowTask(eventScheduleService, eventScheduleConfig),
        new SaveScheduleDetailsInDataStoreTask(schedulerDetailsRepository));
  }

  public Task prepareSchedulingCreateTask(State state, SchedulingMetaData schedulingMetaData) {
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
            getWorkflowScheduleActionsForScheduling(schedulingMetaData));
    return new SaveEventSchedulingTask(schedulingService, actionModelToScheduleRequestMapper);
  }

  /**
   * This method prepare the schedule status task for the given schedule status and definitionId.
   * Using definitionId we are fetching all the schedulers ids and adding it into the state.
   *
   * @param state
   * @return
   */
  public UpdateEventScheduleTask prepareScheduleStatusUpdateTask(
      State state, ScheduleStatus scheduleStatus, String definitionId) {
    if (state != null) {
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, scheduleStatus);
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS,
          getScheduleIdsForDefinition(definitionId));
    }
    return new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig);
  }

  public DeleteEventSchedulingTask prepareSchedulingDeleteTask(State state, String definitionKey) {
    state.addValue(AsyncTaskConstants.DEFINITION_KEY, definitionKey);
    String workflowName = List.of(definitionKey.split(UNDERSCORE)).get(0);
    state.addValue(AsyncTaskConstants.WORKFLOW_NAME_KEY, workflowName);
    return new DeleteEventSchedulingTask(schedulingService);
  }

  /**
   * Prepares a list of update tasks for enabling or disabling scheduling commands based on the
   * current state and definition details. This method checks if the scheduling flow is enabled or
   * if the schedule has been migrated to the Scheduling Service. Depending on these conditions,
   * it prepares the appropriate update tasks.
   *
   * @param state the current execution state
   * @param definitionInstance the instance containing definition details
   * @param eventScheduleMetaData metadata related to event scheduling
   * @param schedulingMetaData metadata related to scheduling
   * @return a list of tasks to update the event scheduling
   */
  public List<Task> prepareUpdateTasksForEnableDisableCommand(State state, DefinitionInstance definitionInstance, EventScheduleMetaData eventScheduleMetaData, SchedulingMetaData schedulingMetaData){
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    //Todo: Remove this check after complete migration to Scheduling Service
    if(schedulingService.isEnabled(definitionInstance.getDefinitionDetails(), definitionInstance.getDefinitionDetails().getOwnerId().toString())){
      // Update schedule status to inactive in Scheduling Service
      updateEventScheduleTasks.add(prepareSchedulingUpdateTask(state, schedulingMetaData, false));
    }else if(schedulingService.isMigrated(definitionInstance.getDefinitionDetails())){
      // if schedule is migrated to Scheduling Service, update the schedule in both scheduling and ESS
      updateEventScheduleTasks.add(prepareSchedulingUpdateTask(state, schedulingMetaData, true));
      updateEventScheduleTasks.add(prepareScheduleStatusUpdateTask(state, eventScheduleMetaData));
    }else{
      // Create update schedule task
      updateEventScheduleTasks.add(prepareScheduleStatusUpdateTask(state, eventScheduleMetaData));
    }
    return updateEventScheduleTasks;
  }

  /**
   * This method fetches schedule ids for a definition.
   *
   * @param definitionId
   * @return List of schedule ids. Empty list if there are no schedules for a definition
   */
  public List<String> getScheduleIdsForDefinition(String definitionId) {
    if (Objects.isNull(definitionId)) {
      return Collections.emptyList();
    }
    Optional<List<SchedulerDetails>> optionalSchedulerDetails =
        schedulerDetailsRepository.findByDefinitionDetails(
            DefinitionDetails.builder().definitionId(definitionId).build());
    if (optionalSchedulerDetails.isEmpty()) {
      WorkflowLogger.logInfo("No Schedule exists for definitionId=%s", definitionId);
      return Collections.emptyList();
    }
    return getScheduleIdsForSchedules(optionalSchedulerDetails.get());
  }

  private List<String> getScheduleIdsForSchedules(List<SchedulerDetails> schedulerDetailsList) {
    if (schedulerDetailsList.isEmpty()) {
      WorkflowLogger.logInfo("No Scheduler details exists");
      // send empty list
      return Collections.emptyList();
    }
    return schedulerDetailsList.stream().map(SchedulerDetails::getSchedulerId)
        .collect(Collectors.toList());
  }

  //Map of schedulerId and details for a definition
  private Map<String, SchedulerDetails> getScheduleDetailsMapForDefinition(String definitionId) {
    // fetch here all the scheduler details
    Optional<List<SchedulerDetails>> optionalScheduleDetails =
        schedulerDetailsRepository.findByDefinitionDetails(
            DefinitionDetails.builder().definitionId(definitionId).build());
    if (optionalScheduleDetails.isEmpty()) {
      WorkflowLogger.logInfo("No Scheduler details exists for definitionId=%s", definitionId);
      // send empty list
      return Collections.emptyMap();
    }
    return optionalScheduleDetails.get().stream()
        .collect(Collectors.toMap(SchedulerDetails::getSchedulerId, Function.identity()));
  }

  /**
   * If event schedule is enabled for a given workflow, prepare the update schedule task to update
   * status for all schedules for a definition.
   *
   * @param state
   * @param eventScheduleMetaData
   * @return
   */
  public UpdateEventScheduleTask prepareScheduleStatusUpdateTask(
      State state, EventScheduleMetaData eventScheduleMetaData) {
      /* TODO: ESS is still using FFs to determine if ESS schedule task should be updated, Will be taken care during migration */
    if (eventScheduleMetaData == null
        || state == null
        || !isEventSchedulingEnabledForWorkflow(
            eventScheduleMetaData.getWorkflowName(),
            state.getValue(AsyncTaskConstants.REALM_ID_KEY))) {
      return null;
    }
    if (state != null) {
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_STATUS, eventScheduleMetaData.getScheduleStatus());
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_IDS,
          getScheduleIdsForDefinition(eventScheduleMetaData.getDefinitionId()));
    }
    return new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig);
  }

  /**
   * This method return the UpdateSchedulerDetailsInDataStoreTask for the enabled workflow.
   *
   * @param state
   * @param eventScheduleMetaData
   * @return
   */
  public List<Task> prepareScheduleUpdateTasks(
      State state, EventScheduleMetaData eventScheduleMetaData) {
    /* TODO: ESS is still using FFs to determine if ESS schedule task should be updated, Will be taken care during migration */
    if (eventScheduleMetaData == null
        || state == null
        || !isEventSchedulingEnabledForWorkflow(
            eventScheduleMetaData.getWorkflowName(),
            state.getValue(AsyncTaskConstants.REALM_ID_KEY))) {
      return Collections.emptyList();
    }
    if (state != null) {
      Map<String, SchedulerDetails> schedulerDetailsMap =
          getScheduleDetailsMapForDefinition(eventScheduleMetaData.getDefinitionId());
      state.addValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP, schedulerDetailsMap);
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_IDS,
          getScheduleIdsForSchedules(new ArrayList<>(schedulerDetailsMap.values())));
      DefinitionInstance definitionInstance = state.getValue(
          AsyncTaskConstants.DEFINITION_INSTANCE);
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
          getWorkflowScheduleActions(
              definitionInstance.getDefinition(),
              eventScheduleMetaData.getWorkflowName()));
      state.addValue(
          AsyncTaskConstants.EVENT_SCHEDULE_STATUS, eventScheduleMetaData.getScheduleStatus());
      state.addValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE, true);
    }

    return List.of(
        new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig),
        new UpdateSchedulerDetailsInDataStoreTask(schedulerDetailsRepository));
  }

  /**
   * Prepares the scheduling update task for the given state and scheduling metadata.
   * If the scheduling metadata does not have a definition key but has a definition ID,
   * it fetches the definition details and sets the definition key in the scheduling metadata.
   * Adds the scheduling metadata and workflow schedule actions to the state.
   *
   * @param state the state object containing the current execution state
   * @param schedulingMetaData the metadata for scheduling
   * @return the task to update the event scheduling
   */
  public Task prepareSchedulingUpdateTask(
          State state, SchedulingMetaData schedulingMetaData, boolean isMigratedToScheduling) {
    WorkflowVerfiy.verify(
            ObjectUtils.isEmpty(schedulingMetaData.getDefinitionKey()) || ObjectUtils.isEmpty(schedulingMetaData.getDefinitionId()),
            WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, "DefinitionKey or DefinitionId not set");
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
    state.addValue(
            AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
            getWorkflowScheduleActionsForScheduling(schedulingMetaData));
    state.addValue(AsyncTaskConstants.IS_MIGRATED_TO_SCHEDULING, isMigratedToScheduling);

    return new UpdateEventSchedulingTask(schedulingService, actionModelToScheduleRequestMapper);
  }

  /**
   * This method deletes all the schedules from ESS and DB for the given definitionList
   *
   * @param definitionDetails
   */
  public void deleteAllSchedules(List<DefinitionDetails> definitionDetails, String realmId) {
    if (CollectionUtils.isEmpty(definitionDetails)) {
      return;
    }
    schedulerDetailsRepository.findByDefinitionDetailsIn(definitionDetails)
        .ifPresentOrElse(scheduleDetails -> {
          deleteSchedulesFromESS(scheduleDetails.stream().map(SchedulerDetails::getSchedulerId)
              .collect(Collectors.toList()), realmId);
          schedulerDetailsRepository.deleteAll(scheduleDetails);
        }, () -> WorkflowLogger.logError("Schedule Details not found for definitionIds=%s",
            definitionDetails.stream().map(DefinitionDetails::getDefinitionId)
                .collect(Collectors.toList())));
  }

  /**
   * This method deletes all the schedules from the ESS
   * @param scheduleIds
   * @param realmId
   */
  public void deleteSchedulesFromESS(List<String> scheduleIds, String realmId) {
    if (CollectionUtils.isEmpty(scheduleIds)) {
      WorkflowLogger.logError("ScheduleIds are not received");
      return;
    }
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS,
        scheduleIds); /* execute delete operation*/
    WorkflowLogger.logInfo("Deleting Event Schedules from ESS for schedulesIds=%s", scheduleIds);
    new RxExecutionChain(state).next(new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig))
        .execute();
  }

  /**
   * This method used to migrate the definition to the ESS. It first create schedules and save the
   * schedule ids into the database. After that, determine whether a rollback is required.
   *
   * @param definitionInstance
   */
  public void migrateWorkflowScheduleActionsToESS(DefinitionInstance definitionInstance) {

    DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
    State state = initialiseMigration(definitionInstance);
    migrate(state, definitionDetails);
    checkAndRollbackMigration(state, definitionDetails.getDefinitionId());
  }

  /**
   * Migrates schedule actions from ESS to Scheduling Service.
   * This method initializes the migration state, performs the migration, and checks for any failures
   * to perform necessary rollback operations.
   *
   * @param definitionInstance The instance of the definition to be migrated.
   */
  public void migrateScheduleActionsFromEssToSchedulingSvc(DefinitionInstance definitionInstance) {
    DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
    State state = initialiseMigrationToScheduling(definitionInstance);
    try{
      migrateToScheduling(state, definitionDetails);
    }catch (Exception e){
      checkAndRollbackSchedulingMigration(state, definitionDetails.getDefinitionId(), definitionDetails, e);
    }

  }

  /**
   * Cleans up the scheduling service migration for the given definition instance.
   * Initializes the migration state and attempts to clean up the scheduling migration.
   * If an exception occurs, it checks and rolls back the scheduling migration cleanup.
   *
   * @param definitionInstance The instance of the definition to be cleaned up.
   */
  public void cleanUpSchedulingSvcMigration(DefinitionInstance definitionInstance) {
    DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
    State state = initialiseMigrationToScheduling(definitionInstance);
    try{
      cleanUpSchedulingMigration(state, definitionDetails);
    }catch (Exception e){
      checkAndRollbackSchedulingMigrationCleanup(state, definitionDetails.getDefinitionId(), e);
    }
  }

  /**
   * Executes the cleanup of the scheduling migration.
   * Logs the execution step, prepares the tasks for updating the event schedule status to deleted,
   * and updates the definition for scheduling migration.
   *
   * @param state The state object containing the current execution state.
   * @param definitionDetails The details of the definition being cleaned up.
   */
  private void cleanUpSchedulingMigration(State state, DefinitionDetails definitionDetails) {
    WorkflowLogger.logInfo(
            "step=cleanupSchedulingSvcMigration, status=execute, definitionId=%s",
            definitionDetails.getDefinitionId());
    RxExecutionChain rxExecutionChain = new RxExecutionChain(state);
    UpdateEventScheduleTask updateEventScheduleTask = prepareScheduleStatusUpdateTask(state, ScheduleStatus.DELETED, definitionDetails.getDefinitionId());
    UpdateDefinitionForSchedulingMigrationTask updateDefinitionForSchedulingMigrationTask = new UpdateDefinitionForSchedulingMigrationTask(definitionDetails, this);
    rxExecutionChain.next(updateEventScheduleTask).next(updateDefinitionForSchedulingMigrationTask).executeWithRollBack();
  }

  /**
   * Checks the migration state for any failures and performs rollback operations if necessary.
   * This method checks if the schedule creation, ESS schedule update, or definition update tasks
   * have failed and logs appropriate error messages. If any task has failed, it performs rollback
   * operations to revert the changes.
   *
   * @param state The state object containing the current execution state.
   * @param definitionId The ID of the definition being migrated.
   */
  private void checkAndRollbackSchedulingMigration(State state, String definitionId, DefinitionDetails definitionDetails,  Exception e) {
    boolean createSchedulingTaskFailed = BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE));
    boolean updateDefinitionTaskFailed = BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
    if(updateDefinitionTaskFailed){
      WorkflowLogger.logError(
              e, "step=ScheduledActionsESSToSchedulingSvcMigration, failed to update the definition for definitionId=%s", definitionId);
      new RxExecutionChain(state).next(new SaveSchedulingRollBackTask(schedulingService)).execute();
    }
    else if(createSchedulingTaskFailed){
      if(!(e instanceof WorkflowRetriableException)){
        new RxExecutionChain(state)
                .next(new UpdateSchedulerDetailsForSchedulingMigrationTask(definitionDetails, schedulerDetailsRepository, false))
                .execute();
      }
      WorkflowLogger.logError(
              e, "step=ScheduledActionsESSToSchedulingSvcMigration, schedules are not created for definitionId=%s", definitionId);
    }
    else{
      WorkflowLogger.logError(
              e, "step=ScheduledActionsESSToSchedulingSvcMigration, migration failed due to unknown error definitionId=%s", definitionId);
    }
    throw new WorkflowGeneralException(WorkflowError.SCHEDULING_MIGRATION_FAILED, e);
  }

  /**
   * Checks the migration cleanup state for any failures and performs rollback operations if necessary.
   * This method checks if the schedule update in ESS or definition update tasks have failed and logs appropriate error messages.
   * If the definition update task has failed, it performs rollback operations to revert the changes.
   *
   * @param state The state object containing the current execution state.
   * @param definitionId The ID of the definition being cleaned up.
   * @param e The exception that occurred during the cleanup process.
   */
  private void checkAndRollbackSchedulingMigrationCleanup(State state, String definitionId, Exception e) {
    boolean updateSchedulesInESSTaskFailed = BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.UPDATE_EVENT_SCHEDULE_TASK_FAILURE));
    boolean updateDefinitionTaskFailed = BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
    if(updateDefinitionTaskFailed){
      WorkflowLogger.logError(
              e, "step=cleanupSchedulingSvcMigration, failed to update the definition for definitionId=%s", definitionId);
      DefinitionDetails definitionDetails = state.getValue(AsyncTaskConstants.DEFINITION_DETAILS);
      ScheduleStatus scheduleStatus =
              definitionDetails.getStatus() == Status.ENABLED
                      ? ScheduleStatus.ACTIVE
                      : ScheduleStatus.INACTIVE;
      new RxExecutionChain(state).next(prepareScheduleStatusUpdateTask(state, scheduleStatus, definitionId)).execute();
    }
    else if(updateSchedulesInESSTaskFailed){
      WorkflowLogger.logError(
              e, "step=cleanupSchedulingSvcMigration, failed to delete the schedules from ESS for definitionId=%s", definitionId);
    }
    else{
      WorkflowLogger.logError(
              e, "step=cleanupSchedulingSvcMigration, migration failed due to unknown error definitionId=%s", definitionId);
    }
    throw new WorkflowGeneralException(WorkflowError.SCHEDULING_MIGRATION_FAILED, e);
  }

  /**
   * Initialise the inputRequest for RxExecutionChain creation for migration
   *
   * @param definitionInstance The details of the definition to be migrated
   * @return Initializes the inputState needed for RxChain to execute and also initializes the
   *     context.
   */
  private State initialiseMigration(DefinitionInstance definitionInstance) {
    final State inputRequest = new State();
    DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
    WorkflowLogger.logInfo(
        "step=migrateWorkflowScheduleActionsToESS, status=init, definitionId=%s",
        definitionDetails.getDefinitionId());
    inputRequest.addValue(
        AsyncTaskConstants.REALM_ID_KEY, Long.toString(definitionDetails.getOwnerId()));
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);
    inputRequest.addValue(
        AsyncTaskConstants.DEFINITION_ID_KEY, definitionDetails.getDefinitionId());
    return inputRequest;
  }

  /**
   * Initializes the migration state for migrating schedule actions from ESS to Scheduling Service.
   * This method sets up the initial state required for the migration process,
   * the initiation step, adding necessary values to the state, and populating the time.
   *
   * @param definitionInstance The instance of the definition to be migrated.
   * @return The initialized state object containing the necessary values for the migration process.
   */
  private State initialiseMigrationToScheduling(DefinitionInstance definitionInstance){
    final State inputRequest = new State();
    DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();
    WorkflowLogger.logInfo(
            "step=migrateWorkflowScheduleActionsToESS, status=init, definitionId=%s",
            definitionDetails.getDefinitionId());
    inputRequest.addValue(
            AsyncTaskConstants.REALM_ID_KEY, Long.toString(definitionDetails.getOwnerId()));
    inputRequest.addValue(
            AsyncTaskConstants.DEFINITION_ID_KEY, definitionDetails.getDefinitionId());
    inputRequest.addValue(
            AsyncTaskConstants.DEFINITION_KEY, definitionDetails.getDefinitionKey());
    inputRequest.addValue(
            AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
    inputRequest.addValue(AsyncTaskConstants.IS_ESS_TO_SCHEDULING_MIGRATION, true);
    return inputRequest;
  }

  /**
   * Migrates scheduled actions from ESS to Scheduling Service.
   * This method logs the migration step, prepares the necessary tasks for creating schedules,
   * creating schedules in Scheduling Service, updating schedules with deleted statues in ESS
   * and updating the definition for migration.
   * them in sequence using an RxExecutionChain.
   *
   * @param state The state object containing the current execution state.
   * @param definitionDetails The details of the definition being migrated.
   */
  private void migrateToScheduling(State state, DefinitionDetails definitionDetails){
    WorkflowLogger.logInfo(
            "step=migrateScheduledActionFromESSToSchedulingSvc, status=execute, definitionId=%s",
            definitionDetails.getDefinitionId());
    RxExecutionChain rxExecutionChain = new RxExecutionChain(state);
    SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionDetails, SchedulingServiceUtil.getStatus(definitionDetails.getStatus()));
    Task createSchedulingTask = prepareSchedulingCreateTask(state, schedulingMetaData);
    UpdateSchedulerDetailsForSchedulingMigrationTask updateSchedulerDetailsForSchedulingMigrationTask = new UpdateSchedulerDetailsForSchedulingMigrationTask(definitionDetails, schedulerDetailsRepository, true);
    rxExecutionChain.next(createSchedulingTask).next(updateSchedulerDetailsForSchedulingMigrationTask).executeWithRollBack();
  }

  /**
   * Migrate the workflow to ESS.
   * This method prepare the schedule create tasks and execute in sync using rxExecution chain
   * @param state
   * @param definitionDetails
   */
  private void migrate(State state, DefinitionDetails definitionDetails) {
    WorkflowLogger.logInfo(
        "step=migrateWorkflowScheduleActionsToESS, status=execute, definitionId=%s",
        definitionDetails.getDefinitionId());
    RxExecutionChain rxExecutionChain = new RxExecutionChain(state);
    List<Task> createScheduleTasks =
        prepareScheduleCreateTasks(state, definitionDetails.getTemplateDetails().getTemplateName());
    if (CollectionUtils.isEmpty(createScheduleTasks)) {
      // ff flag could be disabled
      WorkflowLogger.logError(
          "step=migrateWorkflowScheduleActionsToESS, status=createScheduleTasksNotCreated, definitionId=%s",
          definitionDetails.getDefinitionId());
      throw new WorkflowGeneralException(WorkflowError.ESS_MIGRATION_CREATE_SCHEDULE_TASKS);
    }
    createScheduleTasks.forEach(task -> rxExecutionChain.next(task));
    rxExecutionChain.execute();
  }

  /**
   * This method checks if migration failed if yes then rollback the created schedules on the ESS.
   * We can determine whether schedules are created or not by using the AsyncTaskConstants
   * EVENT_SCHEDULE_TASK_FAILURE and SAVE_SCHEDULE_DETAILS_TASK_FAILURE.
   *
   * <pre>
   *   SAVE_SCHEDULE_DETAILS_TASK_FAILURE - Schedules are created
   *   SAVE_SCHEDULE_DETAILS_TASK_FAILURE - Schedules are created on the ESS but failed to update the database;
   *   in this case, the schedules must be deleted from the ESS.
   * </pre>
   *
   * @param state
   * @param definitionId
   */
  private void checkAndRollbackMigration(State state, String definitionId) {

    // check state and rollback if possible
    if (Objects.isNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE))
        && Objects.isNull(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE))) {
      WorkflowLogger.logInfo(
          "step=migrateWorkflowScheduleActionsToESS, status=success definitionId=%s", definitionId);
      return;
    }
    List<String> eventScheduleIds =
        state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);
    if (Objects.isNull(eventScheduleIds)) {
      WorkflowLogger.logWarn(
          "step=migrateWorkflowScheduleActionsToESS, schedules are not created for definitionId=%s",
          definitionId);
      return;
    }
    WorkflowLogger.logError(
        "step=migrateWorkflowScheduleActionsToESS, status=rollback, definitionId=%s", definitionId);
    // Update schedule status to delete
    deleteSchedulesFromESS(eventScheduleIds, state.getValue(AsyncTaskConstants.REALM_ID_KEY));
  }

  @Transactional
  public void updateDefinitionForSchedulingMigration(String definitionId, DefinitionDetails definitionDetails, byte[] updatedDefinitionData, String updatedPlaceholderValues){
    WorkflowLogger.info(
            () ->
                    WorkflowLoggerRequest.builder()
                            .message("Deleting Scheduler Details for definitionId=%s", definitionId)
                            .downstreamComponentName(DownstreamComponentName.WAS_DB)
                            .className(this.getClass().getName()));
    schedulerDetailsRepository.deleteByDefinitionDetailsIn(Arrays.asList(definitionDetails));
    WorkflowLogger.info(
            () ->
                    WorkflowLoggerRequest.builder()
                            .message("Updating definition with modified placeholder values and definition data")
                            .downstreamComponentName(DownstreamComponentName.WAS_DB)
                            .downstreamServiceName(DownstreamServiceName.WAS_DB_UPDATE_DEFINITION)
                            .className(this.getClass().getName()));
    definitionDetailsRepository.setDefinitionDataAndPlaceholderValues(definitionId, updatedDefinitionData, updatedPlaceholderValues);
  }
}
