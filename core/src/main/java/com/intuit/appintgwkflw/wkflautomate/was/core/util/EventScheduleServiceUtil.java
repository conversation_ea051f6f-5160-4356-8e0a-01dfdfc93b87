package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.Recurrence;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.RecurrenceRange;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.LocalDate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 */
@UtilityClass
public class EventScheduleServiceUtil {

  private static final String INVALID_SCHEDULE_ID = "scheduleId is invalid";
  /**
   * Generates http headers for CRUD operations on Event schedules
   *
   * @param realmId
   * @param requestId
   * @return HttpHeaders
   */
  public HttpHeaders getHttpHeaders(String realmId, String requestId) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.add(EventScheduleConstants.COMPANY_ID, realmId);
    // requestId is idempotent key
    requestHeaders.add(EventScheduleConstants.REQUEST_ID, requestId);
    requestHeaders.add(
        EventScheduleConstants.SCHEDULE_TYPE, EventScheduleConstants.SCHEDULE_TYPE_VALUE);
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    requestHeaders.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
    requestHeaders.add(HttpHeaders.ACCEPT_CHARSET, StandardCharsets.UTF_8.name());
    return requestHeaders;
  }

  /**
   * Generates request body for creating Event schedule based on start dates
   *
   * @param eventScheduleWorkflowActionModels
   * @return List of eventSchedules
   */
  public List<EventSchedule> getCreateEventSchedulesPayload(
          List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels, EventScheduleConfig eventScheduleConfig) {
    List<EventSchedule> eventSchedules = new ArrayList<>();
    for (EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel :
        eventScheduleWorkflowActionModels) {
      eventSchedules.add(getCreateEventSchedulesPayload(eventScheduleWorkflowActionModel, eventScheduleConfig));
    }
    return eventSchedules;
  }

  /**
   * Generates request body for creating Event schedule based on start date
   *
   * @param eventScheduleWorkflowActionModel
   * @return EventSchedule
   */
  private EventSchedule getCreateEventSchedulesPayload(
      EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel, EventScheduleConfig eventScheduleConfig) {
    EventSchedule eventSchedule = new EventSchedule();
    eventSchedule.set(EventScheduleConstants.PROVIDER_TYPE, eventSchedule.getTypeId());
    eventSchedule.setRecurrence(setRecurrence(eventScheduleWorkflowActionModel, eventScheduleConfig, false));
    return eventSchedule;
  }

  /**
   * Updates the event schedule payload with the given schedule ID, status, realm ID, and workflow action model.
   *
   * @param scheduleId The ID of the schedule to be updated.
   * @param scheduleStatus The new status to be set for the schedule.
   * @param realmId The realm ID associated with the schedule.
   * @param eventScheduleWorkflowActionModel The workflow action model containing the recurrence details.
   * @param eventScheduleConfig The configuration settings for the event schedule.
   * @return The updated EventSchedule object.
   */
  public EventSchedule getUpdateEventSchedulePayload(String scheduleId, ScheduleStatus scheduleStatus, String realmId,
                                                     EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel,
                                                     EventScheduleConfig eventScheduleConfig) {
    EventSchedule eventSchedule = getUpdateEventSchedulePayload(scheduleId, scheduleStatus, realmId);
    if (eventScheduleWorkflowActionModel != null) {
      eventSchedule.setRecurrence(setRecurrence(eventScheduleWorkflowActionModel, eventScheduleConfig, true));
    }
    return eventSchedule;
  }

  /**
   * Updates the event schedule payload with the given schedule ID, status, and realm ID.
   *
   * @param scheduleId The ID of the schedule to be updated.
   * @param scheduleStatus The new status to be set for the schedule.
   * @param realmId The realm ID associated with the schedule.
   * @return The updated EventSchedule object.
   * @throws Error if the scheduleId, scheduleStatus, or realmId is invalid.
   */
  public EventSchedule getUpdateEventSchedulePayload(String scheduleId, ScheduleStatus scheduleStatus, String realmId) {
    WorkflowVerfiy.verify(
            StringUtils.isBlank(scheduleId) || scheduleStatus == null || StringUtils.isBlank(realmId),
            WorkflowError.INPUT_INVALID,
            "Invalid ScheduleId, status or realmId");
    EventSchedule eventSchedule = new EventSchedule();
    eventSchedule.setId(GlobalId.create(realmId, eventSchedule.getTypeId(), scheduleId));
    eventSchedule.set(EventScheduleConstants.PROVIDER_TYPE, eventSchedule.getTypeId());
    eventSchedule.setStatus(scheduleStatus);
    return eventSchedule;
  }

  /**
   * Generates recurrence which includes timezone,start and end date of schedule and recurrence
   * pattern according to events and start date received
   *
   * @param eventScheduleWorkflowActionModel
   * @param isUpdate
   */
  private Recurrence setRecurrence(
      EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel, EventScheduleConfig eventScheduleConfig,
      boolean isUpdate) {
    Recurrence recurrence =
        new Recurrence()
            .range(setRecurrenceRange(eventScheduleWorkflowActionModel))
            .pattern(setRecurrencePattern(eventScheduleWorkflowActionModel.getRecurrenceRule()));
    if (!isUpdate) {
      recurrence.setTimeZone(eventScheduleConfig != null && eventScheduleConfig.getTimezone() != null ?
              eventScheduleConfig.getTimezone() : EventScheduleConstants.TIMEZONE);
    }
    return recurrence;
  }

  /**
   * Generates recurrence range which includes start and end date of schedule
   *
   * @param eventScheduleWorkflowActionModel
   */
  private RecurrenceRange setRecurrenceRange(EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel) {
    RecurrenceRange recurrenceRange = new RecurrenceRange();
    /** if start date is a past date, adjust it to current date */
    LocalDate adjustedStartDate = eventScheduleWorkflowActionModel.getStartDate().isBefore(LocalDate.now()) ? LocalDate.now() : eventScheduleWorkflowActionModel.getStartDate();
    recurrenceRange.setStartDate(adjustedStartDate);
    
	/**
	 * if end date is null it gets initialized with EventScheduleConstants.END_DATE which is
	 * 9999-12-31
	 */
	LocalDate endDate = Objects.isNull(eventScheduleWorkflowActionModel.getEndDate())
			? new LocalDate(EventScheduleConstants.END_DATE)
			: eventScheduleWorkflowActionModel.getEndDate();

	// if end date is less than todays date + 1 setting it as current date + 1 as ESS throws error if we pass current date as end date
	recurrenceRange.setEndDate(endDate.isBefore(LocalDate.now().plusDays(1)) ? LocalDate.now().plusDays(1) : endDate);

	 return recurrenceRange;
  }

  /** Set Daily RecurrencePattern */
  private RecurrencePattern setDailyRecurrencePattern() {
    RecurrencePattern recurrencePattern = new RecurrencePattern();
    recurrencePattern.setInterval(EventScheduleConstants.INTERVAL);
    recurrencePattern.setType(RecurrencePatternType.DAILY);
    return recurrencePattern;
  }

  private RecurrencePattern setRecurrencePattern(RecurrenceRule recurrenceRule) {
    if (ObjectUtils.isEmpty(recurrenceRule)) {
      return setDailyRecurrencePattern();
    }
    return RecurrenceHandler.getHandler(recurrenceRule.getRecurType())
        .buildESSRecurrencePattern(recurrenceRule);
  }

  /**
   * This method get errors from the response
   *
   * @param eventScheduleResponses
   * @return
   */
  public List<EventScheduleError> getErrorsFromResponse(
      List<EventScheduleResponse> eventScheduleResponses) {
    if (CollectionUtils.isEmpty(eventScheduleResponses)) {
      return null;
    }
    return eventScheduleResponses.stream()
        .filter(response -> (response != null && CollectionUtils.isNotEmpty(response.getErrors())))
        .map(EventScheduleResponse::getErrors)
        .flatMap(Collection::stream)
        .filter(eventScheduleError -> (eventScheduleError != null))
        .collect(Collectors.toList());
  }

  /**
   * This method returns data from the response
   *
   * @param eventScheduleResponses
   * @return
   */
  public List<EventScheduleData> getDataFromResponse(
      List<EventScheduleResponse> eventScheduleResponses) {
    if (CollectionUtils.isEmpty(eventScheduleResponses)) {
      return null;
    }
    return eventScheduleResponses.stream()
        .filter(response -> (response != null && CollectionUtils.isNotEmpty(response.getData())))
        .map(EventScheduleResponse::getData)
        .flatMap(Collection::stream)
        .filter(eventScheduleData -> (eventScheduleData != null))
        .collect(Collectors.toList());
  }

  /**
   * This method create event schedule metadata for the given definition and schedule
   * status
   *
   * @param definitionDetails
   * @param scheduleStatus
   * @return
   */
  public EventScheduleMetaData getScheduleMetaData(
      DefinitionDetails definitionDetails, ScheduleStatus scheduleStatus) {
    if(definitionDetails != null) {
      return EventScheduleMetaData.builder()
          .definitionId(definitionDetails.getDefinitionId())
          .scheduleStatus(scheduleStatus)
          .workflowName(definitionDetails.getTemplateDetails().getTemplateName())
          .build();
    }
    return null;
  }

  /**
   * This method returns the localId from the GlobalId. First, it will check the Id is global or
   * not. If it is then it will split the Id using GlobalId.V4_Separator and returns the second
   * index value.
   *
   * <pre>e.g.
   * getLocalId(abc:id-123) = id-123
   * getLocalId(id-123) = id-123
   *
   * @param globalId
   * @return
   */
  public String getLocalId(String globalId) {
    if (StringUtils.isBlank(globalId) || !globalId.contains(GlobalId.V4_Separator)) {
      return globalId;
    }
    return globalId.split(GlobalId.V4_Separator)[1];
  }

  /**
   * Validate and throw an exception if error message are other than INVALID_SCHEDULE_ID.
   * INVALID_SCHEDULE_ID error could occur if trying to update the status of
   * already deleted schedule.
   *
   * @param eventScheduleResponses
   * @param scheduleStatus
   * @param scheduleIds
   */
  public void validateErrors(
      List<EventScheduleResponse> eventScheduleResponses,
      ScheduleStatus scheduleStatus,
      List<String> scheduleIds,
      DownstreamServiceName downstreamServiceName) {
    List<EventScheduleError> errorsFromResponse = getErrorsFromResponse(eventScheduleResponses);

    if (CollectionUtils.isNotEmpty(errorsFromResponse)) {
      Predicate<EventScheduleError> isInvalidError = eventScheduleError ->
          eventScheduleError != null && StringUtils.isNotBlank(eventScheduleError.getMessage())
              && !eventScheduleError.getMessage().contains(INVALID_SCHEDULE_ID);
      boolean hasErrorSansInvalidSchedule = errorsFromResponse.stream().filter(isInvalidError)
          .findFirst()
          .isPresent();
      WorkflowVerfiy.verify(hasErrorSansInvalidSchedule, WorkflowError.EVENT_SCHEDULE_CALL_FAILURE,
          new Exception(
              errorsFromResponse.toString()));

      if (!hasErrorSansInvalidSchedule) {
        // if all the errors are "INVALID_SCHEDULE_ID" log it as warn
        WorkflowLogger.warn(() -> WorkflowLoggerRequest.builder().message(
                "Error updating ESS schedules. schedulerIds=%s status=%s errors=%s",
                scheduleIds, scheduleStatus, errorsFromResponse)
            .downstreamComponentName(DownstreamComponentName.ESS)
            .downstreamServiceName(downstreamServiceName));
      }
    }
  }
}
