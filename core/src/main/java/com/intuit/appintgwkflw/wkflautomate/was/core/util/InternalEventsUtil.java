package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;

/**
 * A Utility Class for Internal Events between Camunda and WAS.
 * Author: ragarwal7
 **/
@UtilityClass
public class InternalEventsUtil {

    /**
     * Build Event Header Entity from headers
     * @param headers
     * @param eventType
     * @param eventEntityType
     * @return EventHeaderEntity
     */
    public EventHeaderEntity buildEventHeader(Map<String, String> headers,
    		PublishEventType eventType, EventEntityType eventEntityType) {
        return EventHeaderEntity.builder()
                .offeringId(headers.get(EventHeaderConstants.OFFERING_ID))
                .publishEventType(eventType)
                .eventEntityType(eventEntityType)
                .entityId(headers.get(EventHeaderConstants.ENTITY_ID))
                .idempotencyKey(headers.get(EventHeaderConstants.IDEMPOTENCY_KEY))
                .tid(headers.get(EventHeaderConstants.INTUIT_TID))
                .targetAssetAlias(headers.get(EventHeaderConstants.TARGET_ASSET_ALIAS))
                .ownerId(headers.get(EventHeaderConstants.OWNER_ID))
                .build();
    }


  /**
   * Build Event Header using WorkflowTaskRequest for publishing update on the same.
   *
   * @param taskRequest     - WorkflowTaskRequest having details of task under execution.
   * @param processDetails  - Detail of Process to which task belong.
   * @param contextHandler  - Thread context handling the request.
   * @param eventEntityType - Entity type.
   * @return
   */
  public EventHeaderEntity buildEventHeader(WorkflowTaskRequest taskRequest,
      ProcessDetails processDetails,
      WASContextHandler contextHandler, PublishEventType eventType) {

    String entityId = taskRequest.getId();

    String offeringId = processDetails.getDefinitionDetails().getTemplateDetails().getOfferingId();

    String tid = !StringUtils.isEmpty(contextHandler.get(WASContextEnums.INTUIT_TID))
        ? contextHandler.get(WASContextEnums.INTUIT_TID)
        : UUID.randomUUID().toString();

    EventEntityType eventEntityType = null;
    if (PublishEventType.WORKFLOW_TRANSITION_EVENTS.equals(eventType)) {
      eventEntityType = EventEntityType.WORKFLOW_TRANSITION_EVENTS;
    } else {
      eventEntityType = EventEntityType.EXTERNALTASK;
      entityId = entityId.concat(WorkflowConstants.COLON)
      .concat(taskRequest.getWorkerId());
    }

    return EventHeaderEntity.builder().offeringId(offeringId).publishEventType(eventType)
        .eventEntityType(eventEntityType).entityId(entityId)
        .idempotencyKey(taskRequest.getId())
        .tid(tid).build();
  }

  /**
   * Build Event Payload of TaskTransition using WorkflowTaskRequest for publishing update on the same.
   *
   * @param taskRequest    - WorkflowTaskRequest having details of task under execution.
   * @param processDetails - Detail of Process to which task belong.
   * @param transitionTime - time on which Camunda API was invoked for transition of task.
   * @return
   */
  public WorkflowStateTransitionEvents buildTaskStateTransitionEventPayload(
      WorkflowTaskRequest taskRequest, ProcessDetails processDetails, Long transitionTime) {
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder()
        .processInstanceId(taskRequest.getProcessInstanceId())
        .processDefinitionId(processDetails.getDefinitionDetails().getDefinitionId())
        .workflowName(processDetails.getDefinitionDetails().getDefinitionName())
        .workflowOwnerId(Long.toString(processDetails.getOwnerId()))
        .workflowVersion(processDetails.getDefinitionDetails().getVersion()).build();

    String activityId = taskRequest.getActivityId();
    String activityName = taskRequest.getActivityName();

    ActivityMetaData activityMetaData = ActivityMetaData.builder()
        .scope(ActivityConstants.SCOPE_ACTIVITY)
        .activityId(activityId)
        .activityName(activityName)
        .properties(taskRequest.getTaskAttributes().getModelAttributes())
        .variables(taskRequest.getTaskAttributes().getVariables())
        .externalTaskId(taskRequest.getId())
        .build();

    return WorkflowStateTransitionEvents.builder().workflowMetadata(workflowMetaData)
        .businessEntityId(processDetails.getRecordId())
        .businessEntityType(processDetails.getDefinitionDetails().getRecordType().getRecordType())
        .activityType(taskRequest.getActivityType())
        .activityMetadata(activityMetaData)
        .eventType(commandToEventType(taskRequest.getCommand()))
        .timestamp(null != transitionTime? transitionTime: Instant.now().toEpochMilli())
        .txnId(taskRequest.getTxnId())
        .status(taskRequest.getStatus().toLowerCase())
        .build();
  }

  /**
   * Converts to eventType based on TaskCommand.
   *
   * @param command :: TaskCommand for which corresponding eventType is required.
   * @return String eventType.
   */
  public String commandToEventType(TaskCommand command) {
    switch (command) {
      case UPDATE:
        return ActivityConstants.TASK_EVENT_TYPE_UPDATE;
      case COMPLETE:
        return ActivityConstants.TASK_STATUS_COMPLETE;
      case FAILED:
        return ActivityConstants.TASK_STATUS_FAILED;
      case CREATE:
        return ActivityConstants.TASK_STATUS_CREATED;
    }
    return null;
  }
}
