package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.experimental.UtilityClass;

/**
 * Utility class to merge action and parameter level help variables
 * with the record level help variables for multi-step workflows.
* */
@UtilityClass
public class MultiStepMergeUtil {

  public List<String> mergeRecordParameters(
      Record record, InputParameter inputParameter, String action) {
    Set<String> helpVars = new HashSet<>();
    for (com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup
        actionGroup : record.getActionGroups()) {
      if (actionGroup.getActions().stream()
          .filter(action1 -> action1.getId().equals(action))
          .findFirst()
          .isPresent()) {
        helpVars.addAll(
            mergeRecordParameterUtil(
                record,
                actionGroup.getActions().stream()
                    .filter(action1 -> action1.getId().equals(action))
                    .findFirst()
                    .get(),
                inputParameter));
      }
    }
    return new ArrayList<>(helpVars);
  }

  private Set<String> mergeRecordParameterUtil(
      Record record,
      com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action action,
      InputParameter inputParameter) {

    Set<String> finalHelpVariabesList = new HashSet<>(record.getHelpVariables());
    action.getParameters().stream()
        .forEach(
            actionParameter -> {
              if (actionParameter.getName().equals(inputParameter.getParameterName())) {
                finalHelpVariabesList.addAll(actionParameter.getHelpVariables());
              }
            });
    return finalHelpVariabesList;
  }

}
