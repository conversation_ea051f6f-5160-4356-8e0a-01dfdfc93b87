package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.QBO;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.approval.ApprovalRestHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.TemplateServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalGroupResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * This is a Helper class used for On-demand Approval.
 * It checks if a customApproval definition is present.
 * If not, approval trigger is considered to be on-demand approval.
 * The logic is applied to self deduce if the given trigger call is for OnDemand Approval.
 */
@Component
@AllArgsConstructor
public class OnDemandHelper {

    private final ApprovalRestHandler approvalRestHandler;
    private final V3RunTimeHelper v3RunTimeHelper;
    private final DefinitionDetailsRepository definitionDetailsRepository;
    private final TemplateServiceImpl templateService;
    private final CustomWorkflowConfig customWorkflowConfig;

    /**
     * Checks if the given transaction is for OnDemand Approval.
     *
     * @param transactionEntity The transaction entity to check.
     * @param processDetails The process details of the transaction.
     * @return true if the transaction is for OnDemand Approval, false otherwise.
     */
    public boolean checkIfOnDemand(TransactionEntity transactionEntity, Optional<ProcessDetails> processDetails){
        // Only for approval workflow type, we have to check if there is a customApproval definition present
            return (processDetails.isPresent()) ||
                (!processDetails.isPresent() && !isCustomApprovalSingleDefinitionPresent(transactionEntity));
    }

    /**
     * Checks if a custom approval single definition is present for the given owner id and record type
     * @param transactionEntity
     * @return
     */
    public boolean isCustomApprovalSingleDefinitionPresent(TransactionEntity transactionEntity) {
        try {
            List<DefinitionDetails> enabledDefinitionsList = v3RunTimeHelper.getEligibleDefinitions(
                transactionEntity, false);

            // Removing stale definitions
            enabledDefinitionsList =
                enabledDefinitionsList.stream()
                    .filter(
                        definitionDetails -> Objects.isNull(definitionDetails.getInternalStatus()))
                    .collect(Collectors.toList());

            return (!enabledDefinitionsList.isEmpty());
        } catch (final WorkflowGeneralException e) {
            WorkflowLogger.logError(e, "Custom approval single definition not found");
            return false;
        }
    }

    /**
     * Fetches enabled ondemand approval(stale and active) system definition.
     * @return list of definitions
     */
    public List<DefinitionDetails> fetchOnDemandApprovalDefinition(){
        return definitionDetailsRepository.findEnabledSystemDefinitionForOnDemandApproval(ModelType.BPMN,
            Collections.singletonList(templateService.getTemplateByName(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE))).orElse(
            Collections.emptyList());
    }

    /**
     * Checks if the request type is on demand approval for the current process
     * In case of Non QBO entities, we don't send requestType as "On Demand Approval". This is a special handling added as Trigger point for workflow is always WAS.
     * @param workerActionRequest
     * @return
     */
    public boolean isRequestTypeOnDemandAproval(WorkerActionRequest workerActionRequest) {
        Optional<String> entityType = Optional.ofNullable(workerActionRequest.getInputVariables().get(ENTITY_TYPE)).map(type -> RecordType.fromTypeOrValue(type)).map(
            RecordType::getRecordType);
        // entityType is null in case of old approval templates like invoiceApproval
        if(entityType.isEmpty()){
            return false;
        }
        Record record = customWorkflowConfig.getRecordObjForType(entityType.get());
        return QBO.equalsIgnoreCase(record.getSource())
            && (Boolean.TRUE.toString()).equalsIgnoreCase(workerActionRequest
            .getInputVariables()
            .getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, Boolean.FALSE.toString()));
    }


    /**
     * Checks if approver details are present for the given transaction entity.
     *
     * @param transactionEntity The transaction entity to check.
     * @return true if approver details are present, false otherwise.
     */
    private boolean isApproverDetailsPresent(TransactionEntity transactionEntity){
        try {
            // Fetch the approval group for the given entity ID and entity type
            ApprovalGroupResponse approvalGroupResponse = approvalRestHandler.fetchApprovalGroup(transactionEntity.getEntityId(), transactionEntity.getEntityType().getRecordType().toUpperCase());
            // Check if the approval group response and approval detail response are not empty and the size of approval detail response is greater than 0
            if(!ObjectUtils.isEmpty(approvalGroupResponse) &&
                    !ObjectUtils.isEmpty(approvalGroupResponse.getApprovalRequestResponse()) &&
            !CollectionUtils.isEmpty(approvalGroupResponse.getApprovalRequestResponse().getApprovalDetailResponse())
            && approvalGroupResponse.getApprovalRequestResponse().getApprovalDetailResponse().size() > 0 ){
                return true;
            }
            return false;

        } catch (IOException e) {
            // Throw a WorkflowGeneralException if an I/O error occurs
            throw new WorkflowGeneralException(e);
        }

    }



}