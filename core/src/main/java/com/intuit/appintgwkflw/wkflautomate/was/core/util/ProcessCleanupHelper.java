package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * Helper class for ended process
 */
@Component
@AllArgsConstructor
public class ProcessCleanupHelper {

    private final DataStoreDeleteTaskService dataStoreDeleteTaskService;

    private final HistoryConfig historyConfig;


    /**
     *
     * This method deletes the ended process based on the value of TTL in history config
     * @param processDetails
     */
    public void deleteEndedProcess(ProcessDetails processDetails) {

        LocalDateTime maxModifiedDate = LocalDateTime.now().minusDays(historyConfig.getTtl());

        if (processDetails.getModifiedDate().before(Timestamp.valueOf(maxModifiedDate))) {

            dataStoreDeleteTaskService.deleteProcess(Collections.singletonList(processDetails.getProcessId()));

            WorkflowLogger.logInfo(
                    "step=deletedEndedProcess processId=%s processStatus=%s", processDetails.getProcessId(), processDetails.getProcessStatus());
        }

    }

}