package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CALENDAR_DAY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CALENDAR_MONTH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CALENDAR_YEAR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_ACTIVE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_DAYS_OF_MONTH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_DAYS_OF_WEEK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_DAY_OF_MONTH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_INTERVAL;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_MONTHS_OF_YEAR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_MONTH_OF_YEAR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_RECUR_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_START_DATE_KEYWORD;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_END_DATE_KEYWORD;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_WEEKS_OF_MONTH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_WEEK_OF_MONTH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_TIME_KEYWORD;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_HOURS_KEYWORD;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_MINUTES_KEYWORD;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_TIME_ZONE_KEYWORD;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.experimental.UtilityClass;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.json.JSONArray;
import org.json.JSONObject;

/** <AUTHOR> */
@UtilityClass
public class RecurrenceParserUtil {

  /**
   * Helper method to parse RecurrenceRule object json and build a {@link RecurrenceRule} object
   *
   * @param recurrenceRuleJson : {@link JSONObject}
   * @return RecurrenceRule
   */
  public RecurrenceRule toRecurrenceRule(JSONObject recurrenceRuleJson) {
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRuleJson
        .toMap()
        .forEach(
            (key, value) -> {
              if (!JSONObject.NULL.equals(recurrenceRuleJson.get(key))) {
                switch (key) {
                  case RECURRENCE_DAY_OF_MONTH:
                    recurrenceRule.setDayOfMonth(recurrenceRuleJson.getInt(key));
                    break;
                  case RECURRENCE_DAYS_OF_MONTH:
                    JSONArray jsonArray = (JSONArray) recurrenceRuleJson.get(key);
                    recurrenceRule.setDaysOfMonth(
                        IntStream.range(0, jsonArray.length())
                            .mapToObj(jsonArray::getInt)
                            .collect(Collectors.toList()));
                    break;
                  case RECURRENCE_DAYS_OF_WEEK:
                    jsonArray = (JSONArray) recurrenceRuleJson.get(key);
                    recurrenceRule.setDaysOfWeek(getValues(jsonArray, DayOfWeekEnum::fromValue));
                    break;
                  case RECURRENCE_WEEK_OF_MONTH:
                    recurrenceRule.setWeekOfMonth(
                        ObjectConverter.convertObject(
                            recurrenceRuleJson.get(key), new TypeReference<WeekOfMonthEnum>() {}));
                    break;
                  case RECURRENCE_WEEKS_OF_MONTH:
                    jsonArray = (JSONArray) recurrenceRuleJson.get(key);
                    recurrenceRule.setWeeksOfMonth(
                        getValues(jsonArray, WeekOfMonthEnum::fromValue));
                    break;
                  case RECURRENCE_MONTHS_OF_YEAR:
                    jsonArray = (JSONArray) recurrenceRuleJson.get(key);
                    recurrenceRule.setMonthsOfYear(
                        getValues(jsonArray, MonthsOfYearEnum::fromValue));
                    break;
                  case RECURRENCE_MONTH_OF_YEAR:
                    recurrenceRule.setMonthOfYear(
                        ObjectConverter.convertObject(
                            recurrenceRuleJson.get(key), new TypeReference<MonthsOfYearEnum>() {}));
                    break;
                  case RECURRENCE_START_DATE_KEYWORD:
                    Map<String, Object> dateMap = recurrenceRuleJson.getJSONObject(key).toMap();
                    recurrenceRule.setStartDate(
                        new DateTime(
                            new LocalDate(
                                    (Integer) dateMap.get(CALENDAR_YEAR),
                                    (Integer) dateMap.get(CALENDAR_MONTH),
                                    (Integer) dateMap.get(CALENDAR_DAY))
                                .toDate()));
                    break;
                    
                  case RECURRENCE_END_DATE_KEYWORD:
                      Map<String, Object> endDateMap = recurrenceRuleJson.getJSONObject(key).toMap();
                      recurrenceRule.setEndDate(
                          new DateTime(
                              new LocalDate(
                                      (Integer) endDateMap.get(CALENDAR_YEAR),
                                      (Integer) endDateMap.get(CALENDAR_MONTH),
                                      (Integer) endDateMap.get(CALENDAR_DAY))
                                  .toDate()));
                      break;
                   
                  case RECURRENCE_TIME_KEYWORD:
                      Map<String, Object> recurrenceTimeMap = recurrenceRuleJson.getJSONObject(key).toMap();
                      TimeDuration recurrenceTime = new TimeDuration();
                      recurrenceTime.setMinutes((Integer)recurrenceTimeMap.get(RECURRENCE_MINUTES_KEYWORD));
                      recurrenceTime.setHours((Integer) recurrenceTimeMap.get(RECURRENCE_HOURS_KEYWORD));
                      recurrenceRule.setRecurrenceTime(recurrenceTime);
                      break;

                  case RECURRENCE_TIME_ZONE_KEYWORD:
                      recurrenceRule.setTimeZone(recurrenceRuleJson.getString(key));
                      break;
                      
                  case RECURRENCE_ACTIVE:
                    recurrenceRule.setActive(recurrenceRuleJson.getBoolean(key));
                    break;

                  case RECURRENCE_INTERVAL:
                    recurrenceRule.setInterval(recurrenceRuleJson.getInt(key));
                    break;

                  case RECURRENCE_RECUR_TYPE:
                    recurrenceRule.setRecurType(
                        RecurTypeEnum.fromValue(recurrenceRuleJson.get(key).toString()));
                    break;
                  default:
                    break;
                }
              }
            });
    return recurrenceRule;
  }

  /**
   * Helper method to parse the data given a jsonArray and given mapper function
   *
   * @param jsonArray : {@link JSONArray}
   * @param mapper : {@link Function}
   * @param <R> : Return Type
   * @return
   */
  private <R> List<R> getValues(JSONArray jsonArray, Function<String, R> mapper) {
    return IntStream.range(0, jsonArray.length())
        .mapToObj(jsonArray::getString)
        .map(mapper)
        .collect(Collectors.toList());
  }
}
