package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.SchedulingUseCaseMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.WorkflowNameToSchedulerActionMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.RecurrenceType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingError;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants.TIMEZONE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.*;

/** <AUTHOR> */
@UtilityClass
public class SchedulingServiceUtil {
    private final int[] HOURS_BUCKET = {7, 8, 9};
    private final int[] MINUTES_BUCKET = {0, 15, 30, 45};

    /**
     * Generates a list of SchedulingSvcRequest payloads from a list of EventScheduleWorkflowActionModel.
     *
     * @param eventScheduleWorkflowActionModels List of EventScheduleWorkflowActionModel.
     * @param mapper ActionModelToScheduleRequestMapper to convert models to requests.
     * @param schedulingMetaData metadata
     * @param state
     * @return List of SchedulingSvcRequest.
     */
    public List<SchedulingSvcRequest> getSchedulingSvcRequestsPayload(
            List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels,
            ActionModelToScheduleRequestMapper mapper, SchedulingMetaData schedulingMetaData, State state) {
        return eventScheduleWorkflowActionModels.stream()
                .map(eventScheduleWorkflowActionModel -> getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, schedulingMetaData, state))
                .collect(Collectors.toList());
    }

    /**
     * Generates a SchedulingSvcRequest payload from an EventScheduleWorkflowActionModel.
     *
     * @param eventScheduleWorkflowActionModel EventScheduleWorkflowActionModel to convert.
     * @param mapper ActionModelToScheduleRequestMapper to convert model to request.
     * @param schedulingMetaData metadata
     * @param state
     * @return SchedulingSvcRequest.
     */
    public SchedulingSvcRequest getSchedulingSvcRequestPayload(EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel,
                                                               ActionModelToScheduleRequestMapper mapper, SchedulingMetaData schedulingMetaData,State state) {
        SchedulingSvcRequest eventScheduleCreateRequest = mapper.convertToScheduleRequest(eventScheduleWorkflowActionModel, schedulingMetaData.getStatus());
        eventScheduleCreateRequest.setReferenceId(schedulingMetaData.getDefinitionKey() + COLON + eventScheduleWorkflowActionModel.getActionName());

        Map<String, String> metadata = new HashMap<>();
        metadata.put(ACTION_NAME, eventScheduleWorkflowActionModel.getActionName());
        eventScheduleCreateRequest.setMetadata(new JSONObject(metadata).toString());
        eventScheduleCreateRequest.setUseCase(SchedulingUseCaseMapper.getActionsByType(schedulingMetaData.getWorkflowName()));
        if(BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.IS_ESS_TO_SCHEDULING_MIGRATION))){
            if(ObjectUtils.isEmpty(eventScheduleCreateRequest.getScheduleInfo())){
                eventScheduleCreateRequest.setScheduleInfo(createDailyScheduleInfo());
            }
            eventScheduleCreateRequest.getScheduleInfo().setTime(getTime(state.getValue(AsyncTaskConstants.REALM_ID_KEY)));
            eventScheduleCreateRequest.setMigration(true);
        }else if(BooleanUtils.isTrue(state.getValue(AsyncTaskConstants.IS_MIGRATED_TO_SCHEDULING))){
            if(ObjectUtils.isEmpty(eventScheduleCreateRequest.getScheduleInfo())){
                eventScheduleCreateRequest.setScheduleInfo(createDailyScheduleInfo());
            }
            eventScheduleCreateRequest.getScheduleInfo().setTime(getTime(state.getValue(AsyncTaskConstants.REALM_ID_KEY)));
        }

        if(ObjectUtils.isNotEmpty(eventScheduleCreateRequest.getScheduleInfo()) && ObjectUtils.isEmpty(eventScheduleCreateRequest.getScheduleInfo().getStartDate())){
            mapper.addStartDateFromActionModel(eventScheduleCreateRequest.getScheduleInfo(), eventScheduleWorkflowActionModel);
        }
        //adding default timezone for all the migrated schedules
        if(ObjectUtils.isNotEmpty(eventScheduleCreateRequest.getScheduleInfo()) && ObjectUtils.isEmpty(eventScheduleCreateRequest.getScheduleInfo().getZoneId())){
            eventScheduleCreateRequest.getScheduleInfo().setZoneId(ZoneId.of(TIMEZONE));
        }
        return eventScheduleCreateRequest;
    }

    /**
     * Generates a SchedulingSvcRequest payload for partial update from an EventScheduleWorkflowActionModel.
     *
     * @param model         EventScheduleWorkflowActionModel to convert.
     * @param mapper        ActionModelToScheduleRequestMapper to convert model to request.
     * @param definitionKey Definition key for the schedule.
     * @param status        Status of the schedule.
     * @return SchedulingSvcRequest.
     */
    public SchedulingSvcRequest getSchedulingSvcRequestPayloadForPartialUpdate(EventScheduleWorkflowActionModel model, ActionModelToScheduleRequestMapper mapper, String definitionKey, Status status) {
        SchedulingSvcRequest eventScheduleCreateRequest = mapper.convertToScheduleRequest(null, status);

        eventScheduleCreateRequest.setReferenceId(definitionKey + COLON + model.getActionName());

        JSONObject metadata = new JSONObject();
        metadata.put(ACTION_NAME, model.getActionName());
        eventScheduleCreateRequest.setMetadata(metadata.toString());
        eventScheduleCreateRequest.setUseCase("workflows");
        return eventScheduleCreateRequest;
    }

    /**
     * Generates a list of schedule IDs from a list of EventScheduleWorkflowActionModel.
     *
     * @param eventScheduleWorkflowActionModels List of EventScheduleWorkflowActionModel.
     * @param definitionIdKey                   Definition ID key for the schedule.
     * @return List of schedule IDs.
     */
    public List<String> getScheduleIds(List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels, String definitionIdKey) {
        if (eventScheduleWorkflowActionModels.isEmpty()) {
            WorkflowLogger.logInfo("No event schedule action models");
            // send empty list
            return Collections.emptyList();
        }
        return eventScheduleWorkflowActionModels.stream()
                .map(model -> definitionIdKey + COLON + model.getActionName())
                .collect(Collectors.toList());
    }

    /**
     * Generates a list of schedule IDs from a definition key and workflow name.
     *
     * @param definitionKey The definition key for the schedule.
     * @param workflowName  The name of the workflow.
     * @return List of schedule IDs.
     */
    public List<String> getScheduleIds(String definitionKey, String workflowName) {
        WorkflowVerfiy.verify(
                StringUtils.isBlank(definitionKey) || StringUtils.isBlank(workflowName),
                WorkflowError.INPUT_INVALID,
                "definitionKey and WorkflowName should be present");
        List<String> scheduleIds = new ArrayList<>();
        WorkflowNameToSchedulerActionMapper.getActionsByType(workflowName).forEach(action -> scheduleIds.add(definitionKey + COLON + action));
        return scheduleIds;
    }

    public String getFFName(String workflowName) {
        return String.format(WorkflowConstants.INVOKE_SCHEDULING_FLOW_FF, workflowName);
    }

    /**
     * Checks if the scheduling flow is enabled based on the recurrence rule.
     *
     * @param recurrenceRule the recurrence rule to check
     * @return true if the scheduling flow is enabled, false otherwise
     */
    public boolean isSchedulingFlowEnabled(RecurrenceRule recurrenceRule) {
        return Optional.ofNullable(recurrenceRule)
                .map(rule -> (ObjectUtils.isNotEmpty(rule.getRecurrenceTime()) && (ObjectUtils.isNotEmpty(rule.getRecurrenceTime().getHours()) && ObjectUtils.isNotEmpty(rule.getRecurrenceTime().getMinutes()))))
                .orElse(false);
    }

    /**
     * Checks if the scheduling flow is enabled based on the recurrence rule extracted from the placeholder values.
     *
     * @param placeholderValues the placeholder values containing the recurrence rule in JSON format
     * @return true if the scheduling flow is enabled, false otherwise
     */
    public boolean isSchedulingFlowEnabled(String placeholderValues){
        RecurrenceRule recurrenceRule = getRecurrenceRule(placeholderValues);
        return isSchedulingFlowEnabled(recurrenceRule);
    }

    /**
     * Extracts the recurrence rule from the placeholder values.
     *
     * @param placeholderValues the placeholder values containing the recurrence rule in JSON format
     * @return the extracted RecurrenceRule, or null if not found
     */
    public RecurrenceRule getRecurrenceRule(String placeholderValues){
        if(StringUtils.isBlank(placeholderValues)) {
            return null;
        }
        JSONObject userVariables = new JSONObject(placeholderValues)
                .optJSONObject(WorkflowConstants.USER_VARIABLES);
        if (userVariables == null || !userVariables.has(WorkFlowVariables.RECURRENCE_RULE_KEY.getName())) {
            return null;
        }
        return RecurrenceParserUtil.toRecurrenceRule(ObjectConverter.convertObject(userVariables.getString(WorkFlowVariables.RECURRENCE_RULE_KEY.getName()), JSONObject.class));
    }

    /**
     * Converts a WorkflowStatusEnum to a Status.
     *
     * @param status the WorkflowStatusEnum to convert
     * @return Status.ACTIVE if the input status is ENABLED, otherwise Status.INACTIVE
     */
    public Status getStatus(WorkflowStatusEnum status) {
        return status == WorkflowStatusEnum.ENABLED ? Status.ACTIVE : Status.INACTIVE;
    }

    /**
     * Converts a com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status to a Status.
     *
     * @param status the com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status to convert
     * @return Status.ACTIVE if the input status is ENABLED, otherwise Status.INACTIVE
     */
    public Status getStatus(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status status) {
        return status == com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED ? Status.ACTIVE : Status.INACTIVE;
    }

    /**
     * Creates a SchedulingMetaData object from a Definition and Status.
     *
     * @param definition the Definition to use for creating the SchedulingMetaData
     * @param status     the Status to use for creating the SchedulingMetaData
     * @return a SchedulingMetaData object containing the definition key, status, workflow name, and recurrence rule
     */
    public static SchedulingMetaData getSchedulingMetaData(Definition definition, Status status) {
        return SchedulingMetaData.builder()
                .definitionKey(definition.getDefinitionKey())
                .definitionId(ObjectUtils.isNotEmpty(definition.getId()) ? definition.getId().getLocalId() : null)
                .status(status)
                .workflowName(definition.getName())
                .recurrenceRule(definition.getRecurrence())
                .build();
    }

    /**
     * Creates a SchedulingMetaData object from DefinitionDetails and Status.
     *
     * @param definitionDetails the DefinitionDetails to use for creating the SchedulingMetaData
     * @param status            the Status to use for creating the SchedulingMetaData
     * @return a SchedulingMetaData object containing the definition key, status, workflow name, and recurrence rule
     */
    public static SchedulingMetaData getSchedulingMetaData(DefinitionDetails definitionDetails, Status status) {
        return SchedulingMetaData.builder()
                .definitionKey(definitionDetails.getDefinitionKey())
                .definitionId(definitionDetails.getDefinitionId())
                .status(status)
                .workflowName(ObjectUtils.isNotEmpty(definitionDetails.getTemplateDetails()) ? definitionDetails.getTemplateDetails().getTemplateName() : null)
                .recurrenceRule(getRecurrenceRule(definitionDetails.getPlaceholderValue()))
                .build();
    }

    /**
     * Extracts a SchedulingError object from a given error string.
     *
     * @param error The error string containing the scheduling error details.
     * @return A SchedulingError object if the error string matches the pattern, otherwise null.
     */
    public static SchedulingError getSchedulingError(String error) {
        if (ObjectUtils.isEmpty(error))
            return null;
        Matcher matcher = Pattern.compile(WorkflowConstants.SCHEDULING_ERROR_PATTERN).matcher(error);
        return matcher.find() ? ObjectConverter.fromJson(matcher.group(1), SchedulingError.class) : null;
    }

    /**
     * Updates the definition data with the specified time.
     * This method reads the BPMN model from the input data, finds the start event element,
     * and updates the recurrence rule with the specified hours and minutes.
     *
     * @param data The byte array containing the BPMN model data.
     * @param realmId The realm ID to use for generating the time.
     * @return The updated byte array containing the modified BPMN model data.
     */
    public static byte[] getUpdateDefinitionDataWithTime(byte[] data, String realmId) {
        long id = Long.parseLong(realmId);
        int hours = HOURS_BUCKET[(int) (id % HOURS_BUCKET.length)];
        int minutes = MINUTES_BUCKET[(int) (id % MINUTES_BUCKET.length)];
        BpmnModelInstance modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(data));
        FlowElement startEventElement = BpmnProcessorUtil.findStartEventElement(modelInstance);
        Optional<CamundaProperty> camundaPropertyOptional =
                BpmnProcessorUtil.getCamundaProperty(
                        startEventElement, WorkFlowVariables.RECURRENCE_RULE_KEY.getName());

        RecurrenceRule recurrenceRule;
        //recurrence rule is present only in scheduled actions workflow
        if (camundaPropertyOptional.isPresent()) {
            JSONObject recurrenceRuleJson =
                    ObjectConverter.convertObject(
                            camundaPropertyOptional.get().getCamundaValue(), JSONObject.class);
            recurrenceRule = updateRecurrenceRuleWithTime(RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson), hours, minutes);
            String updatedRecurrenceRuleString = ObjectConverter.toJson(recurrenceRule);
            camundaPropertyOptional.get().setCamundaValue(updatedRecurrenceRuleString);
        } else {
            //populate the recurrence rule with daily interval 1 for reminders, send and update entities
            recurrenceRule = getRecurrenceForReminderMigration(new RecurrenceRule(), hours, minutes);
            String updatedRecurrenceRuleString = ObjectConverter.toJson(recurrenceRule);

            // Ensure extension elements container exists
            ExtensionElements extensionElements = startEventElement.getExtensionElements();
            if (extensionElements == null) {
                extensionElements = modelInstance.newInstance(ExtensionElements.class);
                startEventElement.setExtensionElements(extensionElements);
            }

            // Ensure CamundaProperties container exists
            CamundaProperties camundaProperties = extensionElements.getElementsQuery().filterByType(CamundaProperties.class).singleResult();
            if (camundaProperties == null) {
                camundaProperties = modelInstance.newInstance(CamundaProperties.class);
                extensionElements.addChildElement(camundaProperties);
            }

            CamundaProperty recurrenceCamundaProperty = modelInstance.newInstance(CamundaProperty.class);
            recurrenceCamundaProperty.setCamundaName(WorkFlowVariables.RECURRENCE_RULE_KEY.getName());
            recurrenceCamundaProperty.setCamundaValue(updatedRecurrenceRuleString);

            // Add new property to CamundaProperties
            camundaProperties.addChildElement(recurrenceCamundaProperty);
        }

        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        Bpmn.writeModelToStream(byteStream, modelInstance);
        return byteStream.toByteArray();
    }

    /**
     * Updates the placeholder values with the specified time.
     * This method updates the recurrence rule in the placeholder values with the specified hours and minutes.
     *
     * @param placeholderValue The placeholder value string containing the recurrence rule.
     * @param realmId The realm ID to use for generating the time.
     * @return The updated placeholder value string with the modified recurrence rule.
     */
    public static String getUpdatedPlaceholderValuesWithTime(String placeholderValue, String realmId) {
        long id = Long.parseLong(realmId);
        int hours = HOURS_BUCKET[(int) (id % HOURS_BUCKET.length)];
        int minutes = MINUTES_BUCKET[(int) (id % MINUTES_BUCKET.length)];
        if (ObjectUtils.isEmpty(placeholderValue)) {
            return placeholderValue;
        }

        JSONObject placeholderValueJson = new JSONObject(placeholderValue);
        JSONObject userVariables = placeholderValueJson.optJSONObject(WorkflowConstants.USER_VARIABLES);
        if (userVariables == null) {
            return placeholderValue;
        }
        RecurrenceRule recurrenceRule;
        //recurrence rule is present only in scheduled actions workflow
        if(userVariables.has(WorkFlowVariables.RECURRENCE_RULE_KEY.getName())){
            JSONObject recurrenceRuleJson = ObjectConverter.convertObject(userVariables.get(WorkFlowVariables.RECURRENCE_RULE_KEY.getName()), JSONObject.class);
            recurrenceRule = updateRecurrenceRuleWithTime(RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson), hours, minutes);
        }else{
            //populate the recurrence rule with daily interval 1 for reminders, send and update entities
            recurrenceRule = getRecurrenceForReminderMigration(new RecurrenceRule(), hours, minutes);
        }
        userVariables.put(WorkFlowVariables.RECURRENCE_RULE_KEY.getName(), ObjectConverter.toJson(recurrenceRule));
        placeholderValueJson.put(WorkflowConstants.USER_VARIABLES, userVariables);

        return placeholderValueJson.toString();
    }

    /**
     * Generates a LocalTime based on the given realmId.
     * The time is generated based on the realmId and is used for scheduling.
     *
     * @param realmId
     * @return A LocalTime object representing the generated time.
     */
    public static LocalTime getTime(String realmId) {
        long id = Long.parseLong(realmId);
        return LocalTime.of(HOURS_BUCKET[(int) (id % HOURS_BUCKET.length)], MINUTES_BUCKET[(int) (id % MINUTES_BUCKET.length)]);
    }

    /**
     * Updates the recurrence rule for reminder migration with the specified hours and minutes.
     * Sets the recurrence type to daily and interval to 1.
     *
     * @param recurrenceRule the recurrence rule to update
     * @param hours the hours to set in the recurrence rule
     * @param minutes the minutes to set in the recurrence rule
     * @return the updated recurrence rule with the specified time
     */
    private RecurrenceRule getRecurrenceForReminderMigration(RecurrenceRule recurrenceRule, int hours, int minutes){
        recurrenceRule.setRecurType(RecurTypeEnum.DAILY);
        recurrenceRule.setInterval(1);
        return updateRecurrenceRuleWithTime(recurrenceRule, hours, minutes);
    }

    /**
     * Updates the recurrence rule with the specified hours and minutes.
     * Sets the recurrence time and nullifies the timezone.
     *
     * @param recurrenceRule the recurrence rule to update
     * @param hours the hours to set in the recurrence rule
     * @param minutes the minutes to set in the recurrence rule
     * @return the updated recurrence rule with the specified time
     */
    private RecurrenceRule updateRecurrenceRuleWithTime(RecurrenceRule recurrenceRule, int hours, int minutes) {
        TimeDuration time = new TimeDuration();
        time.setHours(hours);
        time.setMinutes(minutes);
        recurrenceRule.setRecurrenceTime(time);
        // Setting the timezone to null in the recurrence rule, this will get auto modified when the definition gets updated
        recurrenceRule.setTimeZone(null);
        return recurrenceRule;
    }

    /**
     * Creates a ScheduleInfo object with daily recurrence type and interval of 1.
     *
     * @return A ScheduleInfo object with daily recurrence type and interval of 1.
     */
    private ScheduleInfo createDailyScheduleInfo() {
        return ScheduleInfo.builder()
                .recurrenceType(RecurrenceType.DAILY)
                .interval(1)
                .build();
    }
}
