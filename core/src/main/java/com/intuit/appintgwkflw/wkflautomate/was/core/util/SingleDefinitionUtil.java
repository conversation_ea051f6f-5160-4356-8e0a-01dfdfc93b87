package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DOLLAR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DOT_OPERATOR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_OPERATION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_SCHEDULE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_START_DATE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.STRING_TYPE_CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ParameterDetailsConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.jayway.jsonpath.JsonPath;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;

/**
 * Utility class for Single Definition helper methods.
 */
@UtilityClass
public class SingleDefinitionUtil {

  private static final String EVALUATE_FLAG_LOG_STRING = "Evaluated flag=%s for ownerId=%s. Result=%s";

  /**
   * Helper method to evaluate a flag with workflow name
   *
   * @param featureFlagManager
   * @param flagName
   * @return True/False based on flag evaluation.
   */
  private Boolean evaluateFlag(FeatureFlagManager featureFlagManager, String flagName,
      String workflowName) {
    Boolean result = featureFlagManager.getBoolean(flagName, false, workflowName);
    WorkflowLogger.logInfo(EVALUATE_FLAG_LOG_STRING, flagName, WASContext.getOwnerId(), result);
    return result;
  }

  /**
   * Check whether single definition create is allowed for the precanned template. If the template
   * is not custom and workflow name is present in ff workflowname list
   *
   * @param flagManager
   * @param workflowTemplate
   * @return
   */
  public Boolean isSingleDefPrecannedCreateAllowed(
      FeatureFlagManager flagManager, Template workflowTemplate) {
    return !CustomWorkflowUtil.isCustomWorkflow(workflowTemplate)
        && evaluateFlag(flagManager, WorkflowConstants.SINGLE_DEF_WORKFLOW_CREATE_PRECANNED,
        workflowTemplate.getName());
  }

  /**
   * Gets whether Single Definition Trigger is feasible for custom-workflows or not based on the
   * following 1) Workflow type is custom
   * 2) Placeholder value is present in database for the Definition Detail
   * 3) Placeholder value is null but is On demand approvals case i.e null record type, custom approval, SYSTEM owner id
   *
   * @param workflowTemplate
   * @param definitionDetails  {@link DefinitionDetails}
   * @return True/False
   */
  public Boolean isSingleDefCustomTriggerFeasible(
      Template workflowTemplate, DefinitionDetails definitionDetails) {
    if (!CustomWorkflowUtil.isCustomWorkflow(workflowTemplate)) {
      return false;
    }

    return Objects.nonNull(definitionDetails.getPlaceholderValue()) ||
        (CustomWorkflowUtil.isCustomApprovalWorkflow(workflowTemplate)
            && Objects.isNull(definitionDetails.getRecordType())
            && WorkflowConstants.SYSTEM_OWNER_ID.equalsIgnoreCase(
            Long.toString(definitionDetails.getOwnerId())));
  }

  /**
   * Gets whether Single Definition Trigger is feasible for precanned-workflows or not based on the
   * following 1) Workflow type is precanned
   * 2) Placeholder value is present in database for the Definition Detail
   *
   * @param workflowTemplate
   * @param definitionDetails  {@link DefinitionDetails}
   * @return True/False
   */
  public Boolean isSingleDefPrecannedTriggerFeasible(
      Template workflowTemplate, DefinitionDetails definitionDetails) {

    //If its a custom workflow, then its not pre-canned
    if (CustomWorkflowUtil.isCustomWorkflow(workflowTemplate)) {
      return false;
    }
    return Objects.nonNull(definitionDetails.getPlaceholderValue());
  }

  /**
   * Get the rule-lines for the workflow step condition
   *
   * @param definition
   * @return List of Rule lines from workflow step.
   */
  // TODO : This function holds good for single rule-line based evaluation, which is currently in
  // use. In case you decide to have multiple rulelines, this function will need to be modified.
  public List<RuleLine.Rule> getRuleLinesForWorkflowCondition(Definition definition) {

    if (Objects.nonNull(definition)) {
      return CustomWorkflowUtil.getRulesFromDefinition(definition);
    }
    return Collections.EMPTY_LIST;
  }

  /**
   * @param startEventElement : startevent element of the definition instance.
   * @return process variable map of the start event.
   */
  public Map<String, ProcessVariableData> getProcessVariablesFromStartEvent(
      FlowElement startEventElement) {
    String processVariableDetailsData =
        BpmnProcessorUtil.getMapOfCamundaProperties(startEventElement.getExtensionElements())
            .get(WorkFlowVariables.PROCESS_VARIABLE_DETAILS_KEY.getName());

    List<ProcessVariableDetail> processVariableDetails =
        ObjectConverter.fromJson(
            processVariableDetailsData, new TypeReference<List<ProcessVariableDetail>>() {
            });

    return processVariableDetails.stream()
        .collect(
            Collectors.toMap(
                ProcessVariableDetail::getVariableName,
                processVariableDetail ->
                    ProcessVariableData.builder()
                        .type(processVariableDetail.getVariableType())
                        .build()));
  }

  /**
   * @param definitionDetails
   * @param actionKey
   * @param actionId
   * @return returns the specific json object from the user placeholder values, containing the user
   * filled details for the action
   */
  public JSONObject getActionPlaceholderValue(
      DefinitionDetails definitionDetails, String actionKey, String actionId) {
    JSONObject definitionPlaceholder = new JSONObject(definitionDetails.getPlaceholderValue());
    JSONObject definitionUserVariables =
        definitionPlaceholder.getJSONObject(WorkflowConstants.USER_VARIABLES);
    return definitionUserVariables.getJSONObject(actionKey + WorkflowConstants.COLON + actionId);
  }

  /**
   * @param definitionDetails
   * @param actionId
   * @return returns the specific json object from the user placeholder values, containing the user
   * filled details for the action
   */
  public JSONObject getActionPlaceholderValuePreCanned(
      DefinitionDetails definitionDetails, String actionId) {
    JSONObject definitionPlaceholder = new JSONObject(definitionDetails.getPlaceholderValue());
    JSONObject definitionUserVariables =
        definitionPlaceholder.getJSONObject(WorkflowConstants.USER_VARIABLES);
    return definitionUserVariables.has(actionId) ? definitionUserVariables.getJSONObject(actionId) : null;
  }

  /**
   * @param definitionDetails
   * @return returns the intuit_was_locale value from the placeholder values, null if
   *     intuit_was_locale not present.
   */
  public String getLocaleFromPlaceHolderValues(DefinitionDetails definitionDetails) {
    if(definitionDetails.getPlaceholderValue() == null) {
      return null;
    }
    JSONObject definitionPlaceholder = new JSONObject(definitionDetails.getPlaceholderValue());
    if (definitionPlaceholder.isNull(WorkflowConstants.USER_META_DATA)) {
      return null;
    }
    JSONObject userMetaData = definitionPlaceholder.getJSONObject(WorkflowConstants.USER_META_DATA);
    return userMetaData.isNull(WASContextEnums.INTUIT_WAS_LOCALE.getValue())
        ? null
        : userMetaData.getString(WASContextEnums.INTUIT_WAS_LOCALE.getValue());
  }

  /**
   * Fills action parameter details with user filled values.
   *
   * @param actionParameters
   * @param userVariablesActionData
   */
  public void fillParameterDetails(
      Map<String, HandlerDetails.ParameterDetails> actionParameters,
      JSONObject userVariablesActionData) {
    actionParameters.forEach(
        (parameterKey, parameterDetails) -> {
          JSONObject parameters =
              userVariablesActionData.getJSONObject(WorkflowConstants.PARAMETERS);
          if (parameters.has(parameterKey)) {
            JSONObject param = parameters.getJSONObject(parameterKey);
            List<String> parameterFieldValues =
                (List<String>)
                    param.toMap().getOrDefault(WorkflowConstants.FIELD_VALUE, new ArrayList<>());
            parameterDetails.setFieldValue(parameterFieldValues);
          }
        });
  }

  /**
   * Set recurrenceRule in userVariables placeholders
   */
  public void setRecurrenceRuleInUserVariables(DefinitionInstance definitionInstance, Map<String, Object> userVariables){
    RecurrenceRule recurrenceRule = definitionInstance.getDefinition().getRecurrence();
    if(Objects.isNull(recurrenceRule))
       return;
    String recurrenceRuleJson = ObjectConverter.toJson(recurrenceRule);
    userVariables.put(WorkFlowVariables.RECURRENCE_RULE_KEY.getName(), recurrenceRuleJson);
  }

  /**
   * Set recurrenceVariables in placeholders
   * startDate and CronExpression in processVariables
   */
  public void setRecurrenceVariablesInProcessVariables(DefinitionInstance definitionInstance, Map<String, ProcessVariableData> processVariables, FlowElement startEventElement) {
    RecurrenceRule recurrenceRule = definitionInstance.getDefinition().getRecurrence();
    if(Objects.isNull(recurrenceRule))
      return;
    Optional<Map<String, String>> optionalRecurrenceDetails =
        SchemaDecoder.getRecurrenceElementDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(startEventElement));

    Map<String, String> recurrenceDetailsMap = optionalRecurrenceDetails.orElse(Collections.emptyMap());

    if(CollectionUtils.isEmpty(recurrenceDetailsMap))
       return;

    //get the process variables for recurrence
    String startDateProcessVariable = RecurrenceUtil.getRecurrenceProcessVariables(recurrenceDetailsMap.get(RECURRENCE_START_DATE), definitionInstance);
    String cronExpressionProcessVariable = RecurrenceUtil.getRecurrenceProcessVariables(recurrenceDetailsMap.get(RECURRENCE_SCHEDULE), definitionInstance);

    if(StringUtils.isBlank(startDateProcessVariable) || StringUtils.isBlank(cronExpressionProcessVariable)){
      WorkflowLogger.logError("step=RecurrenceFailure Could not set recurrence variables in the placeholder variables");
      return;
    }

    //set value of placeholders for startDate and cronExpression and add in processVariables
    processVariables.put(startDateProcessVariable, ProcessVariableData.builder()
        .value(RecurrenceUtil.getFormattedRecurrenceStartDate(recurrenceRule))
        .type(STRING_TYPE_CAMUNDA).build());

    processVariables.put(cronExpressionProcessVariable, ProcessVariableData.builder()
        .value(RecurrenceUtil.getCronExpression(recurrenceRule))
        .type(STRING_TYPE_CAMUNDA).build());

  }

  /**
   * It fetches the parameter from the input parameter of trigger.
   * For Eg: if fieldValues is passed as 7, and we store it as P7D, where this variable is passed to camunda while trigger.
   * @param inputParameter
   * @return
   */
  public Map<String, ProcessVariableData> getParameterForTrigger(
      InputParameter inputParameter) {
    if (ParameterDetailsConstants.WAIT_TIME.getValue().equals(inputParameter.getParameterName())) {
      return Map.of(inputParameter.getParameterName(),
          ProcessVariableData.builder().type(STRING_TYPE_CAMUNDA).value(
                  MessageFormat.format("P{0}D", inputParameter.getFieldValues().stream().findFirst().get()))
              .build());
    }
    return Map.of(inputParameter.getParameterName(),
        ProcessVariableData.builder().type(STRING_TYPE_CAMUNDA)
                .value(String.valueOf(inputParameter.getFieldValues().stream().findFirst().get()))
                .build());
  }

  /**
   *  Utility method to get the boolean value name by stripping the parenthesis and dollar sign
   *  Input: ${true} Output: true
   */
  public String stripBooleanFromVariable(String variable){
    return variable.substring(variable.indexOf("{") + 1, variable.indexOf("}")).trim();
  }

  /**
   * Get the user variable map from action
   *
   * @param trigger
   * @return map of user variable and its corresponding parameters
   */
  public Map<String, Object> getTriggerVariables(Trigger trigger) {
    // Get user parameters and their field values.
    Map<String, Object> consolidatedTriggerVariables = getConsolidatedTriggerVariables(trigger);
    //Converting the trigger to a single list of string due to https://jira.intuit.com/browse/IPPC-7216
    // TODO remove the code when notification moves to event in.
    if (consolidatedTriggerVariables.containsKey(AsyncTaskConstants.ENTITY_OPERATION)) {
      // Deep cloning the map as the internal one is a singleton map.
      Map<String, List<String>> convertedMap = new HashMap<>();
      Map<String, List<String>> map = (Map<String, List<String>>) consolidatedTriggerVariables.get(AsyncTaskConstants.ENTITY_OPERATION);
      convertedMap.put(FIELD_VALUE, Arrays.asList(String.join(",", map.get(FIELD_VALUE))));
      consolidatedTriggerVariables.put(AsyncTaskConstants.ENTITY_OPERATION, convertedMap);
    }
    Map<String, Object> userVariables = new HashMap<>();
    // Add user parameters corresponding to the action id
    userVariables.put(ACTION_PARAMETERS, consolidatedTriggerVariables);
    // All parameters passed via trigger are always selected
    userVariables.put(ACTION_SELECTED, Boolean.TRUE);
    return userVariables;
  }

  /**
   * Get the field values for the trigger such as waitTime.
   *
   * @param trigger
   * @return map of user variable and its corresponding field values.
   */
  private Map<String, Object> getConsolidatedTriggerVariables(
      Trigger trigger) {

    return
        trigger.getParameters().stream()
            .collect(
                Collectors.toMap(
                    InputParameter::getParameterName,
                    param -> Collections.singletonMap(FIELD_VALUE, param.getFieldValues())));
  }

  /**
   * Gets the entity operations from placeholder values for a start element
   *
   * @param startElement
   * @param placeholder
   * @return List<String> of entityOperations
   */
  @Deprecated
  public List<String> getEntityOperations(FlowElement startElement, String placeholder) {
      if (Objects.isNull(placeholder)) {
          return Collections.EMPTY_LIST;
      }
    return parsedOperations(placeholder, startElement);
  }

  /**
   * Retrieves a list of entity operations based on the provided activity detail and placeholder.
   * <p>
   * This method checks if the placeholder is null and returns an empty list if so. Otherwise, it
   * parses the operations using the provided activity detail and placeholder.
   * </p>
   *
   * @param startEventActivityDetail the activity detail containing information about the start event.
   * @param placeholder a JSON string used to extract operations via JSON path.
   * @return a list of entity operations extracted from the placeholder, or an empty list if the placeholder is null.
   */
  public List<String> getEntityOperations(ActivityDetail startEventActivityDetail, String placeholder) {
    if (Objects.isNull(placeholder)) {
      return Collections.emptyList();
    }
    return parsedOperations(startEventActivityDetail, placeholder);
  }

  /**
   * Parses the entoty operations from the placeholder values and retruns it as a list For e.g.
   * placeholder = "user_variables": { "customStart": { "selected": true, "parameters": {
   * "entityOperation": { "fieldValue": ["Create", "Update"] }* 			} } return ["Create", "Update"]
   *
   * @param placeholder
   * @param startElement
   * @return
   */
  private List<String> parsedOperations(String placeholder, FlowElement startElement) {
    String extractedStartElement = startElement.getId().split(WorkflowConstants.UNDERSCORE)[0];
    String path = String.join(DOT_OPERATOR, DOLLAR, USER_VARIABLES, extractedStartElement,
        PARAMETERS,
        ENTITY_OPERATION, FIELD_VALUE);
    try {
      return (List<String>) JsonPath.parse(placeholder).read(path);
    } catch (Exception ex) {
      return Collections.EMPTY_LIST;
    }
  }

  private List<String> parsedOperations(ActivityDetail startEventActivityDetail, String placeholder) {
    // Extract the start element ID
    String extractedStartElement = extractStartElementId(startEventActivityDetail);
    // Construct the JSON path
    String jsonPath = constructJsonPath(extractedStartElement);
    // Parse the JSON and read the path
    try {
        WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
              .message("extractedStartElement: %s and JsonPath formed: %s", extractedStartElement, jsonPath));
        return JsonPath.parse(placeholder).read(jsonPath);
    } catch (Exception ex) {
      WorkflowLoggerRequest.builder().stackTrace(ex)
              .message("Error parsing operations");
      return Collections.emptyList();
    }
  }

  private String extractStartElementId(ActivityDetail startEventActivityDetail) {
    return startEventActivityDetail.getActivityId().split(WorkflowConstants.UNDERSCORE)[0];
  }

  private String constructJsonPath(String extractedStartElement) {
    return String.join(DOT_OPERATOR, DOLLAR, USER_VARIABLES, extractedStartElement, PARAMETERS, ENTITY_OPERATION, FIELD_VALUE);
  }

}

