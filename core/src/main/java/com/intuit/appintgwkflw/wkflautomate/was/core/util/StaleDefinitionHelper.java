package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@AllArgsConstructor
public class StaleDefinitionHelper {

    private final ProcessDetailsRepository processDetailsRepository;

    private final DataStoreDeleteTaskService dataStoreDeleteTaskService;

    private final DefinitionDetailsRepository definitionDetailsRepository;

    private final HistoryConfig historyConfig;

    /**
     *
     * This method taking care of deleting the stale definitions and the ended process
     * as this data is obsolete and impacting WAS DB
     */
    public void deleteStaleDefinitionAndEndedProcesses(DefinitionDetails definitionDetails) {

        if (definitionDetails.getVersion() == 0) {
            populateOriginalSetupValuesDefinitionDetails(definitionDetails);
        }

        long processCount = processDetailsRepository.countByDefinitionDetails(definitionDetails);

        if (processCount == 0) {
            dataStoreDeleteTaskService.deleteDefinitions(Collections.singletonList(definitionDetails));

            WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Deleting stale definitionId=%s " +
                            " definitionKey=%s version=%s as no process is existing"
                    , definitionDetails.getDefinitionId(), definitionDetails.getDefinitionKey(), definitionDetails.getVersion()));
        }

        /* Once the stale definitions with no ended process is deleted. Will un-comment this.
        List<String> endedProcesses = processDetailsRepository.findEndedProcessesModifiedBefore(definitionDetails.getDefinitionId(),
                        Timestamp.valueOf(LocalDateTime.now().minusDays(historyConfig.getTtl())))
                .orElse(Collections.emptyList())
                .stream()
                .map(ProcessDetails::getProcessId)
                .collect(Collectors.toList());

        WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Ended Process Deleted=%s, definitionId=%s" +
                        " processDeletedCount=%s definitionKey=%s"
                , endedProcesses, definitionDetails.getDefinitionId(), endedProcesses.size(), definitionDetails.getDefinitionKey()));
    */
         //Updating modified date so it doesn't get read again in the same batch
        definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));

//        dataStoreDeleteTaskService.deleteProcess(endedProcesses); */
    }

    /**
     * This method is populating the 2 new columns that have been added in the definition details
     * to keep track of when the definition was originally created and by whom
     * @param definitionDetails
     */
    private void populateOriginalSetupValuesDefinitionDetails(DefinitionDetails definitionDetails) {

        DefinitionDetails bpmnDefinitionDetails = definitionDetailsRepository
                .findTopByDefinitionKeyOrderByVersionDesc(definitionDetails.getDefinitionKey());

        if(!Objects.nonNull(bpmnDefinitionDetails.getOriginalSetupDate())) {

            bpmnDefinitionDetails.setOriginalSetupDate(definitionDetails.getCreatedDate());
            bpmnDefinitionDetails.setOriginalSetupUser(definitionDetails.getCreatedByUserId());

            List<DefinitionDetails> dmnDefDetails = definitionDetailsRepository.findByParentId(bpmnDefinitionDetails.getDefinitionId()).orElse(Collections.emptyList());

            List<DefinitionDetails> updatedDefinitionDetails = new ArrayList<>();
            updatedDefinitionDetails.add(bpmnDefinitionDetails);

            for (DefinitionDetails defDetails : dmnDefDetails) {
                defDetails.setOriginalSetupDate(definitionDetails.getCreatedDate());
                defDetails.setOriginalSetupUser(definitionDetails.getCreatedByUserId());
                updatedDefinitionDetails.add(defDetails);
            }

            definitionDetailsRepository.saveAll(updatedDefinitionDetails);
        }
    }

}