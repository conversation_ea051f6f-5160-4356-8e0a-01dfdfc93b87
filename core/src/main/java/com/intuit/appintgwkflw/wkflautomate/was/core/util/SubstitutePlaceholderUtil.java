package com.intuit.appintgwkflw.wkflautomate.was.core.util;


import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.text.StrSubstitutor;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * Replace the placeholders in the input using key/value in map
 */
@UtilityClass
public class SubstitutePlaceholderUtil {

  private static final String PLACEHOLDER_SUFFIX = "]]";
  private static final String PLACEHOLDER_PREFIX = "[[";

  /**
   * Returns input map after substitution of placeholders
   * @param inputMap contains key/value which have to be evaluated and replaced
   * @param isNestedSubstitutionEnabled true-> will substitute the values recursively
   *                             false-> will simply substitute the values
   * @return map of substituted values
   */
  public Map<String, String> substitutePlaceholder(Map<String, String> inputMap, boolean isNestedSubstitutionEnabled){
    if (CollectionUtils.isEmpty(inputMap)){
      return Collections.emptyMap();
    }
    StrSubstitutor strSubstitutor = new StrSubstitutor(inputMap, PLACEHOLDER_PREFIX, PLACEHOLDER_SUFFIX);
    strSubstitutor.setEnableSubstitutionInVariables(isNestedSubstitutionEnabled);
    return inputMap.entrySet().stream()
        .collect(Collectors.toMap(Entry::getKey,
            entry -> strSubstitutor.replace(entry.getValue())));
  }
}
