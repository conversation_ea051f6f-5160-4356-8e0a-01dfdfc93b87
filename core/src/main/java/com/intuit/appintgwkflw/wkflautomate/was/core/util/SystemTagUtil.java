package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.NO_EXISTING_DEFINITION_WITH_TAG;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import com.vdurmont.semver4j.Semver;
import java.util.function.Predicate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import lombok.experimental.UtilityClass;

@UtilityClass
public class SystemTagUtil {

  public static void validateTemplateTagVersion(String tagVersion) {
    final Semver validTagVersion = new Semver(tagVersion);
    WorkflowVerfiy.verify(
        Objects.isNull(validTagVersion), WorkflowError.INVALID_TEMPLATE_TAG_VERSION);
  }

  public static void checkAndSetSystemTagObjectFromTemplate(
      SystemTags systemTag, String templateVersion) {
    if (StringUtils.isNotEmpty(templateVersion)) {
      systemTag.populateSystemTagFromString(templateVersion);
    }
  }

  /**
   * Creates a predicate for the stream filter to find definitions with tags in lookup keys
   *
   * @param tagVersion
   * @return Predicate<TemplateDetails>
   */
  public static Predicate<TemplateDetails> templateTagFilterPredicate(String tagVersion) {
    Predicate<TemplateDetails> templateDetailsPredicate =
        templateDetails -> getVersionFromTag(templateDetails.getTag()).equals(tagVersion);
    return templateDetailsPredicate;
  }

  /**
   * Creates a predicate for the stream filter to find definitions with tags in lookup keys
   *
   * @param tagVersion
   * @return Predicate<DefinitionDetails>
   */
  public static Predicate<DefinitionDetails> definitionDetailsPredicate(String tagVersion) {
    Predicate<DefinitionDetails> definitionDetailsPredicate =
        definitionDetails ->
            getVersionFromTag(definitionDetails.getLookupKeys()).equals(tagVersion);
    return definitionDetailsPredicate;
  }

  public static String getVersionFromTag(String tag) {
    String version = StringUtils.EMPTY;
    if (Objects.nonNull(tag)) {
      SystemTags sysTag = new SystemTags();
      sysTag.populateSystemTagFromString(tag);
      version = sysTag.getTag(SYSTEM_TAG).getVersion();
    }
    return version;
  }

  /**
   * Creates a semver comparator for two version strings
   *
   * @param left
   * @param right
   * @return right< left 1/-1 based in comparison (by default return reverse sorted semver)
   */
  public static int tagSemverVersionComparator(String left, String right) {
    SystemTags leftTag = new SystemTags(), rightTag = new SystemTags();
    leftTag.populateSystemTagFromString(left);
    rightTag.populateSystemTagFromString(right);
    Semver leftVersion = new Semver(leftTag.getTag(SYSTEM_TAG).getVersion());
    Semver rightVersion = new Semver(rightTag.getTag(SYSTEM_TAG).getVersion());
    return leftVersion.isGreaterThan(rightVersion) ? -1 : 1;
  }

  /**
   * Creates a comparator to sort and compare DefinitionDetails based on system_tags in look_up keys
   *
   * @param first
   * @param second
   * @return second< first 1/-1 based in comparison (by default return reverse sorted semver)
   */
  public static int definitionLookupComparator(DefinitionDetails first, DefinitionDetails second) {

    if (StringUtils.isEmpty(first.getLookupKeys())) return 1;
    if (StringUtils.isEmpty(second.getLookupKeys())) return -1;
    return tagSemverVersionComparator(first.getLookupKeys(), second.getLookupKeys());
  }
  /**
   * Returns a definition based on the version matching in the look_up keys or latest according to
   * semver or latest by date
   *
   * @param definitionsDetails
   * @param eventHeaders
   * @return optional definitionDetails
   */
  public static DefinitionDetails getDefinitionFromEventHeaders(
      final List<DefinitionDetails> definitionsDetails, final EventHeaders eventHeaders) {

    if (Objects.nonNull(eventHeaders.getTags())) {
      return definitionsDetails.stream().filter(definitionDetailsPredicate(eventHeaders.getTags().getVersion())).findFirst().orElseThrow(()-> new WorkflowGeneralException(NO_EXISTING_DEFINITION_WITH_TAG));
    } else {
      return definitionsDetails.stream()
          .sorted(SystemTagUtil::definitionLookupComparator)
          .collect(Collectors.toList())
          .stream()
          .findFirst().orElseThrow(() -> new WorkflowGeneralException(DEFINITION_NOT_FOUND));
    }
  }
}
