package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.vdurmont.semver4j.Semver;
import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * Utility class to add checks related to system template
 */
@UtilityClass
public class SystemTemplateUtil {

  /**
   * If authorization belongs to a non-realm system-user, only system definitions must be allowed
   * @param templateDetails
   */
  public static void checkNonRealmSystemUser(TemplateDetails templateDetails){

    WorkflowVerfiy.verify(
        templateDetails.getDefinitionType() != DefinitionType.SYSTEM
            && WASContext.isNonRealmSystemUser(), WorkflowError.INVALID_REALM_ID);
  }

}
