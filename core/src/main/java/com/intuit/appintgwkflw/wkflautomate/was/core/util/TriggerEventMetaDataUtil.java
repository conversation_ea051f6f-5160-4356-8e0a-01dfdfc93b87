package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Objects;
import java.util.Optional;
import lombok.experimental.UtilityClass;

@UtilityClass
public class TriggerEventMetaDataUtil {

  /**
   * Update the WAS context with unavailable data from API payload.
   * @param txnEntity
   * @param wasContextHandler
   */
  public void updateWASContext(TransactionEntity txnEntity, WASContextHandler wasContextHandler){

    wasContextHandler.addKey(WASContextEnums.ENTITY_ID, txnEntity.getEntityId());
    //TODO - offeringId can be taken from APP_ID_KEY in the Authorization header
    wasContextHandler.addKey(WASContextEnums.OFFERING_ID, Optional.ofNullable(wasContextHandler.get(WASContextEnums.OFFERING_ID)).orElse("default"));
    wasContextHandler.addKey(WASContextEnums.IDEMPOTENCY_KEY, String.format("%s_%s", txnEntity.getEventHeaders().getWorkflow(), txnEntity.getEntityId()));
  }

  /**
   * Prepare trigger event payload from API.
   * @param txnEntity
   * @param triggerTargetAPI
   * @return
   */
  public Trigger prepareTriggerEvent(TransactionEntity txnEntity, final TriggerTargetAPI triggerTargetAPI) {

    WorkflowLogger.logInfo(
        String.format("Preparing Trigger event payload for workflow=%s and changeType=%s",
            txnEntity.getEventHeaders().getEntityType().getRecordType()
                + txnEntity.getEventHeaders().getWorkflow(),
            txnEntity.getEventHeaders().getEntityChangeType()));

    MetaData metaData =
        MetaData.builder()
            .workflow(txnEntity.getEventHeaders().getWorkflow())
            .entityType(txnEntity.getEventHeaders().getEntityType().getRecordType())
            .entityChangeIdentifier(
                new EntityChangeIdentifier(
                    txnEntity.getEventHeaders().getEntityChangeType()))
            .entityId(txnEntity.getEntityId())
            .providerWorkflowId(txnEntity.getEventHeaders().getProviderWorkflowId())
            .targetApi(Objects.nonNull(triggerTargetAPI) ? triggerTargetAPI : TriggerTargetAPI.TRIGGER_V2)
            .blockProcessOnSignalFailure(false)
            .build();
    return new Trigger(
        metaData, null, txnEntity.getEntityObj());
  }

}
