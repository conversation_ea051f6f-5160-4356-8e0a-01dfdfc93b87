package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EVENT_HEADERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EVENT_TYPE_DOWNGRADED;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.downgrade.Unsubscribe;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.v4.WorkflowsBulkAction.WorkflowBulkActionDetails;
import com.intuit.v4.workflows.RuleLine.Rule;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 *     <p>This is a utility class for Trigger Related Classes and methods.
 */
@UtilityClass
public class TriggerUtil {

  /**
   * @param ownerId : Realm Id
   * @return : Trigger Payload required for Downgrade
   */
  public Map<String, Object> prepareTriggerPayloadForDowngrade(String ownerId,
      WorkflowBulkActionDetails workflowBulkActionDetails) {
    Map<String, Object> map = new HashMap<>();
    EventHeaders eventHeaders = new EventHeaders();
    eventHeaders.setEntityChangeType(EVENT_TYPE_DOWNGRADED);
    eventHeaders.setEntityType(RecordType.SUBSCRIPTION);
    eventHeaders.setWorkflow("downgrade");
    map.put(EVENT_HEADERS, eventHeaders);

    Map<String, Object> entityTypeMap = new HashMap<>();
    Map<String, Object> entityObject = new HashMap<>();
    entityObject.put(WorkflowConstants.ID_KEY, ownerId);
    /**
     * Adding for backward of templates as the new template requires a process var "subscriptions"
     * of type "Object" and not providing the value causes trigger failure
     * TODO: cleanup post 100% rollout of new downgrade flow
     * https://jira.intuit.com/browse/QBOES-22936
     */
    entityObject.put(WorkflowConstants.SUBSCRIPTIONS, Collections.emptyList());
    entityTypeMap.put(RecordType.SUBSCRIPTION.getDisplayValue(), entityObject);
    entityObject.put(WorkflowConstants.WORKFLOW_FILTER, workflowBulkActionDetails.getWorkflowFilter());
    map.put(ENTITY, entityTypeMap);
    return map;
  }

  public static Map<String, Object> prepareTriggerPayloadForDisconnection(
      TemplateDetails templateDetails,
      Map<String, Unsubscribe.Template> nameTemplateMap,
      String realmId) {
    Map<String, Object> map = new HashMap<>();
    EventHeaders eventHeaders = new EventHeaders();
    String entityChangeType =
        nameTemplateMap.get(templateDetails.getTemplateName()).getEntityChangeType();
    eventHeaders.setEntityChangeType(StringUtils.lowerCase(entityChangeType));
    eventHeaders.setEntityType(templateDetails.getRecordType());
    eventHeaders.setEntityId(realmId);
    eventHeaders.setWorkflow(nameTemplateMap.get(templateDetails.getTemplateName()).getWorkflow());
    map.put(WorkflowConstants.EVENT_HEADERS, eventHeaders);

    Map<String, Object> entityTypeMap = new HashMap<>();
    Map<String, Object> entityObject = new HashMap<>();
    entityObject.put(WorkflowConstants.ID, realmId);
    entityTypeMap.put(
        StringUtils.capitalize(templateDetails.getRecordType().toString()), entityObject);
    map.put(WorkflowConstants.ENTITY, entityTypeMap);
    return map;
  }

  /**
   * Get the template name from record type and workflow type ToDo: Revisit when supporting BYOAW ,
   * same template will support multiple entities. The convention of template name would change.
   *
   * @param recordType Type of record/entity example invoice , bill, estimate etc
   * @param workflow Type of workflow such as approval , reminder etc.
   * @return the template name
   */
  public static String getTemplateName(final RecordType recordType, final String workflow) {
    return recordType.toString() + workflow;
  }

  /**
   * Get template details from the definition list
   * @param definitionDetailsList
   * @return
   */
  public static TemplateDetails getBpmnTemplateDetails(
      final List<DefinitionDetails> definitionDetailsList) {
    return definitionDetailsList.stream().findFirst()
        .map(DefinitionDetails::getTemplateDetails)
        .orElse(null);
  }

  /**
   * Helper method to generate and return Trigger Payload based on Entity Change Type, RecordType
   * and Workflow.
   *
   * @param recordType : {@link RecordType}
   * @param workflow : {@link String}
   * @param entityChangeType {@link String}
   * @return
   */
  public static TransactionEntity prepareTransactionEntity(
      RecordType recordType,
      String workflow,
      String entityChangeType,
      String workflowId,
      List<Rule> rulesForDefinition,
      WASContextHandler contextHandler, String definitionKey) {
    String entityId = UUID.randomUUID().toString();
    Map<String, Object> v3EntityPayload = new HashMap<>();
    EventHeaders eventHeaders = new EventHeaders();
    eventHeaders.setEntityChangeType(entityChangeType);
    eventHeaders.setEntityType(recordType);
    eventHeaders.setWorkflow(workflow);
    eventHeaders.setProviderWorkflowId(workflowId);
    eventHeaders.setDefinitionKey(definitionKey);
    /**
     * We're using UUID as entityId as we're starting a recurring process (A long running process)
     * and it is not specific to a specific record.
     */
    eventHeaders.setEntityId(entityId);
    v3EntityPayload.put(EVENT_HEADERS, eventHeaders);

    Map<String, Object> entityTypeMap = new HashMap<>();
    Map<String, Object> entityObject = new HashMap<>();
    entityObject.put(WorkflowConstants.ID_KEY, entityId);
    entityTypeMap.put(recordType.getDisplayValue(), entityObject);
    v3EntityPayload.put(WorkflowConstants.ENTITY, entityTypeMap);
    return TransactionEntityFactory.getInstanceOf(
    		populateProcessVariablesForDmn(rulesForDefinition, v3EntityPayload, recordType),
    		contextHandler);
  }

  /**
   * Helper method to generate Trigger Payload based on Entity Change Type, RecordType and Workflow.
   * It also evaluates the workflow key to be passed to the overloaded method based on the fact that
   * the underlying template is a custom template or not.
   *
   * @param templateDetails : {@link TemplateDetails}
   * @param recordType : {@link RecordType}
   * @param entityChangeType : {@link String}
   * @param rulesForDefinition
   * @return TransactionEntity
   */
  public TransactionEntity prepareTransactionEntity(
      TemplateDetails templateDetails,
      RecordType recordType,
      String entityChangeType,
      String workflowId,
      List<Rule> rulesForDefinition,
      WASContextHandler contextHandler, String definitionKey) {
    boolean isCustomWorkflow =
        TemplateCategory.CUSTOM.name().equalsIgnoreCase(templateDetails.getTemplateCategory());
    String workflow = null;
    if (isCustomWorkflow) {
      // For custom workflow, since recordType is null (Template is not specific to an entity and
      // hence we will
      // get the TemplateName (For ex. customScheduledActions (Custom being the recordType and
      // ScheduledActions being the workflowType)).
      workflow =
          CustomWorkflowType.templateNameActionKeyMapping().get(templateDetails.getTemplateName());
    } else {
      // TODO : For Legacy Pre-canned templates, recurrence is not supported. The logic to get the
      // workflowName for older pre-canned templates will be supported here.
      WorkflowVerfiy.verify(!isCustomWorkflow, WorkflowError.RECURRENCE_NOT_SUPPORTED);
    }
    return prepareTransactionEntity(recordType, workflow, entityChangeType, workflowId, rulesForDefinition, contextHandler, definitionKey);
  }

  /**
   * Helper method which builds trigger payload in the desired format. We're basically
   * pre-populating condition values in the start trigger payload as this call will be invoked
   * locally to WAS.
   *
   * <p>NOTE : DMN rule values need to be populated without fail. Other values can be fetched using
   * respective duzzits (like company name, company email for instance)
   *
   * @param ruleLine : {@link com.intuit.v4.workflows.RuleLine}
   * @param v3EntityPayloadMap : { "eventHeaders": { "workflow": "workflow_mame",
   *     "entityChangeType": "created", "entityType": "Record_type_Value" }, "entity": {
   *     "<Entity_Display_Name>": { "condition1": "value1" }}
   * @param recordType : {@link RecordType} value
   * @return
   */
  public Map<String, Object> populateProcessVariablesForDmn(
      List<Rule> ruleLine, Map<String, Object> v3EntityPayloadMap, RecordType recordType) {
    Map<String, String> ruleParamToValueMap =
        ruleLine.stream()
            .collect(
                Collectors.toMap(
                    Rule::getParameterName,
                    rule -> getConditionalExpressionValue(rule.getConditionalExpression())));

    Map<String, Object> entityMap = (Map<String, Object>) v3EntityPayloadMap.get(ENTITY);
    // Get the entity map
    Map<String, String> entities =
        (Map<String, String>) entityMap.get(recordType.getDisplayValue());
    // Adding condition and value map to existing  entity map
    entities.putAll(ruleParamToValueMap);
    entityMap.put(recordType.getDisplayValue(), entities);
    return v3EntityPayloadMap;
  }

  /**
   * Example: parameterName = Customer and conditionExpression = CONTAINS 1, then we're getting the
   * value after the operator and returning it.
   *
   * @param conditionExpression
   * @return
   */
  public String getConditionalExpressionValue(String conditionExpression) {
    return conditionExpression
        .substring(conditionExpression.indexOf(WorkflowConstants.SPACE))
        .trim();
  }

  /**
   * Pick businessKey from {@link com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders}
   * if present, else pick ownerId from contextHandler
   * @param transactionEntity contains EventHeaders info.
   * @return businessKey
   */
  public String fetchBusinessKey(TransactionEntity transactionEntity, WASContextHandler contextHandler){
    return StringUtils.isBlank(transactionEntity.getEventHeaders().getBusinessKey()) ?
        contextHandler.get(WASContextEnums.OWNER_ID) : transactionEntity.getEventHeaders().getBusinessKey();
  }
}
