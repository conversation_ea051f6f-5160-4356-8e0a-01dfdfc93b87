package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DEFINITION_NOT_FOUND;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WorkerUtil {

  private DefinitionDetailsRepository definitionRepository;

  private ProcessDetailsRepository processDetailsRepository;

  /**
   * validates if given definition and process exist in WAS DB and validates definition should not
   * be marked for DELETE pr DISABLE and process status should not be error. Updates
   * workerActionRequest with ownerId and workflowId for given definition.
   *
   * @param workerActionRequest input worker request
   * @return {@link WorkerActionRequest}
   */
  public WorkerActionRequest validate(WorkerActionRequest workerActionRequest) {

    ProcessDetails processDetails =
        processDetailsRepository
            .findById(workerActionRequest.getProcessInstanceId())
            .orElseThrow(() -> new WorkflowRetriableException(WorkflowError.PROCESS_NOT_FOUND));

    // if process is in error state or process details not found throw exception
    WorkflowVerfiy.verify(
        ProcessStatus.ERROR == processDetails.getProcessStatus(),
        () -> {
          throw new WorkflowNonRetriableException(WorkflowError.PROCESS_IN_ERROR_STATE);
        });

    //Get definition details from process details itself.
    DefinitionDetails definitionDetails = processDetails.getDefinitionDetails();

    WorkflowVerfiy.verify(
        Objects.isNull(definitionDetails),
        () -> {
          throw new WorkflowRetriableException(DEFINITION_NOT_FOUND);
        });

    return workerActionRequest
        .toBuilder()
        .ownerId(processDetails.getOwnerId())
        .workflowId(definitionDetails.getWorkflowId())
        .build();
  }
}
