package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.Subscription;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;

@UtilityClass
public class WorkflowVariabilityUtil {
  public static boolean isSubscriptionDataAvailable(WorkerActionRequest workerActionRequest, FeatureManager featureManager) {

    if(!featureManager.getBoolean(
        WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF,
        workerActionRequest.getInputVariables().get(INTUIT_REALMID)
    )) {
      return false;
    }

    String subscriptions =
        workerActionRequest.getInputVariables().get(WorkflowConstants.SUBSCRIPTIONS);
    // verify if subscriptions is a valid non-empty list
    return ObjectUtils.isNotEmpty(subscriptions)
        && ObjectUtils.isNotEmpty(
            ObjectConverter.fromJson(subscriptions, new TypeReference<List<Subscription>>() {}));
  }

  public static List<DefinitionPendingDeletion> getDefinitionsPendingDeletion(
      WorkerActionRequest workerActionRequest) {
    return (List<DefinitionPendingDeletion>)
        Optional.ofNullable(
                ObjectConverter.fromJson(
                    workerActionRequest
                        .getInputVariables()
                        .get(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION),
                    new TypeReference<List<DefinitionPendingDeletion>>() {}))
            .orElse(Collections.emptyList());
  }

  public static List<DefinitionDetails> getDefinitionList(
      List<DefinitionPendingDeletion> definitionPendingDeletion) {
    return definitionPendingDeletion.stream()
        .map(DefinitionPendingDeletion::convertToDefinitionDetails)
        .collect(Collectors.toList());
  }

  public static Set<String> getDistinctDefinitionKeys(
      List<DefinitionPendingDeletion> definitionListPendingDeletion) {
    return definitionListPendingDeletion.stream()
        .map(DefinitionPendingDeletion::getKey)
        .collect(Collectors.toSet());
  }
}
