package com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.services;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.WFAIConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.objects.AdminDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * Contains the logic of Populating Assignees for workflow AI experiment
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class WFAIService {

    private WFAIConfig wfaiConfig;
    private final WASContextHandler wasContextHandler;

    private static final List<String> ranges = Arrays.asList("BTW 0,1000", "BTW 1001,5000", "BTW 5001,10000");
    /**
     * To pre-populate the assignee in template's actions steps
     *
     * @param template for which assignee needs to be pre-populated
     */
    public void populateAssignee(@NotNull Template template) {

        Set<String> featureTemplates = wfaiConfig.getTemplates();
        if(CollectionUtils.isEmpty(featureTemplates) || !featureTemplates.contains(template.getId().getLocalId())){
            WorkflowLogger.logInfo("Empty WF AI feature templates");
            return;
        }

        WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("step=pre_processing_assignee").downstreamComponentName(DownstreamComponentName.WAS));
        Map<String, List<AdminDetails>> companyAdminMap = wfaiConfig.getCompanyAdminMap();

        List<AdminDetails> adminsList = companyAdminMap.get(wasContextHandler.get(WASContextEnums.OWNER_ID));

        if(CollectionUtils.isEmpty(adminsList)) {
            WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Empty Admins for WF AI assignee use case").downstreamComponentName(DownstreamComponentName.WAS));
            return;
        }

        adminsList.sort(Comparator.comparingInt(AdminDetails::getCountOfLogins));

        Map<String, WorkflowStep> workflowStepIdMap = new HashMap<>();

        template.getWorkflowSteps().forEach(workflowStep -> {
            workflowStepIdMap.put(workflowStep.getId().toString(), workflowStep);
        });


        template.getWorkflowSteps().forEach(workflowStep -> {

            if(MultiStepUtil.isConditionStep(workflowStep)) {
                workflowStep.getWorkflowStepCondition().getRuleLines().forEach(ruleLine -> ruleLine.getRules().forEach(rule -> {
                    if(rule.getParameterName().contains("Amount") && rule.getConditionalExpression().contains("BTW")) {
                        findConditionStepAndAssign(workflowStep, workflowStepIdMap, adminsList, rule);
                    }
                }));

            }
        });

    }

    /**
     * this method finds the child step against the rule expression and assigns adds an assignee
     *
     * @param workflowStep to find it's next step
     * @param workflowStepIdMap workflow id map
     * @param adminsList list of admins
     * @param rule to find the range of amount of condition
     */
    private void findConditionStepAndAssign(WorkflowStep workflowStep, Map<String, WorkflowStep> workflowStepIdMap, List<AdminDetails> adminsList, RuleLine.Rule rule){
        workflowStep.getNext().forEach(nextStep -> {
            WorkflowStep wfNextStep = workflowStepIdMap.get(nextStep.getWorkflowStepId());
            if(Objects.nonNull(wfNextStep)
                    && MultiStepUtil.isActionStep(wfNextStep)) {
                getParameterAndAssign(wfNextStep, nextStep, adminsList, rule);
            }
        });
    }

    private void getParameterAndAssign(WorkflowStep wfNextStep, WorkflowStep.StepNext nextStep, List<AdminDetails> adminsList, RuleLine.Rule rule){
        wfNextStep.getActionGroup().getAction().getParameters().forEach(parameter -> {
            if(WorkflowConstants.ASSIGNEE.equalsIgnoreCase(parameter.getParameterName())) {
                if(NextLabelEnum.NO.equals(nextStep.getLabel())){
                    parameter.addFieldValues(adminsList.get(adminsList.size()-1).getUserId());
                }else {
                    int index = ranges.indexOf(rule.getConditionalExpression());
                    if (index!=-1 && index <  adminsList.size()){
                        parameter.addFieldValues(adminsList.get(ranges.indexOf(rule.getConditionalExpression())).getUserId());
                    }
                }
            }
        });
    }
}
