package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import java.util.HashMap;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * The dmn data type transformers.
 *
 * <AUTHOR>
 *     <p>Class acts as factory to return DmnDataTypeTransformer
 */
@UtilityClass
public class DMNDataTypeTransformers {

  private final Map<DMNSupportedOperator, DMNDataTypeTransformer> DMN_DATA_TYPE_TRANSFORMER_MAP =
      new HashMap<>();

  /**
   * Adds a data type transformer.
   *
   * @param dmnSupportedOperator the data type name
   * @param dataTypeTransformer the data type transformer
   */
  public void addTransformer(
      DMNSupportedOperator dmnSupportedOperator, DMNDataTypeTransformer dataTypeTransformer) {
    DMN_DATA_TYPE_TRANSFORMER_MAP.put(dmnSupportedOperator, dataTypeTransformer);
  }

  /**
   * Gets a Data type transformer.
   *
   * @param dmnSupportedOperator input operator name
   * @return DMNDataTypeTransformer for given operator
   */
  public DMNDataTypeTransformer getTransformer(DMNSupportedOperator dmnSupportedOperator) {
    return DMN_DATA_TYPE_TRANSFORMER_MAP.get(dmnSupportedOperator);
  }
}
