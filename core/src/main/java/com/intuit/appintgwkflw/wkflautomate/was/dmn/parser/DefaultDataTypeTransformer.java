package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.NumberOperator;
import java.text.MessageFormat;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> sbaliarsing
 * For parameterType except LIST, DAYS OR STRING, this class is called
 * <pre>
 *   ex.
 *    parameterType : DOUBLE
 *    parameterName : TxnAmount
 * </pre>
 */
@Component
@AllArgsConstructor
public class DefaultDataTypeTransformer implements DMNDataTypeTransformer {

  private final WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private static Pattern exprValPattern = Pattern.compile("\\[(\\d*?)..(\\d*?)\\]");

  /**
   * Generates FEEL or JUEL expression depending on feature flag enabling
   * @param userFriendlyExpr input expression
   * @param parameterName parameter name in DMN
   * @param parameterType
   * @return
   */
  @Override
  public String transformToDmnFriendlyExpression(
      String userFriendlyExpr, String parameterName, String parameterType, boolean useFeelExpr) {
    if (useFeelExpr) {
      return prepareDmnExpression(parameterType, userFriendlyExpr);
    }
    return prepareLegacyDmnExpression(parameterName, parameterType, userFriendlyExpr);
  }

  @Override
  public DMNSupportedOperator getName() {
    return DMNSupportedOperator.DEFAULT;
  }

  /**
   * Transforms DMN friendly expression(JUEL or FEEL) to user friendly expression
   * Juel Expr -> Amount > 99.99, Amount == 100
   * Feel Wxpr ->
   * @param dmnFriendlyExpr input rule saved in DMN
   * @param parameterName
   * @return
   */
  @Override
  public String transformToUserFriendlyExpression(String dmnFriendlyExpr, String parameterName) {
    if(StringUtils.isEmpty(dmnFriendlyExpr)) {
      return null;
    }
    String conditionalExpressionForUI = "";
    boolean isJuelExpr = dmnFriendlyExpr.contains(parameterName);
    if(!isJuelExpr) {
      //Match [1..2]
      Matcher matcher = exprValPattern.matcher(dmnFriendlyExpr);
      if (matcher.find()) {
        return MessageFormat.format(
            "{0} {1} && {2} {3}", WorkflowConstants.GTE_OP, matcher.group(1),
            WorkflowConstants.LTE_OP, matcher.group(2));
      }
    }

    Map<String, String> symbolToOperatorMap =
        workflowGlobalConfiguration.getOperatorToSymbolMap();
    dmnFriendlyExpr = dmnFriendlyExpr.replaceAll(parameterName.replaceAll("\\s", ""), "").trim();
    String[] stringTokens = dmnFriendlyExpr.split(WorkflowConstants.SPACE);

    for (String token : stringTokens) {
      // In case of >, < etc
      String symbolValue = symbolToOperatorMap.get(token);
      if (stringTokens.length == 1 && symbolValue == null && !isJuelExpr) {
        conditionalExpressionForUI = WorkflowConstants.EQ;
      }
      conditionalExpressionForUI =
          MessageFormat.format(
                  WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE,
                  conditionalExpressionForUI,
                  ObjectUtils.isEmpty(symbolValue) ? token : symbolValue)
              .trim();
    }
    return conditionalExpressionForUI;
  }

  @Override
  public String defaultRule(String parameterName, String defaultValue) {
    if (StringUtils.isNotEmpty(defaultValue)) {
      return new StringBuilder()
          .append(NumberOperator.getDefaultNumberOperator().getSymbol())
          .append(WorkflowConstants.SPACE)
          .append(defaultValue)
          .toString();
    } else {
      /* Setting default value to Zero for number types. */
      return WorkflowConstants.DEFAULT_NUMBER_CONDITION;
    }
  }

  @Override
  public String getDataType() {
    throw new UnsupportedOperationException("Data type can not be queried");
  }

  /**
   * Assumption : UI should not allow user to enter rule, if none is entered, empty input should
   * come.
   * Tokenizing the expression
   * In the DMN expression, the label can with spaces, but dmnExpression variable can't have space
   */
  private String handleSpecialOperator(String expression, boolean isJuelExpr) {
    if (expression.startsWith(WorkflowConstants.OPERATOR_BTW)) {
      String[] tokens = expression.trim().split(WorkflowConstants.SPACE);
      String[] values = tokens[1].trim().split(WorkflowConstants.COMMA);
      WorkflowVerfiy.verify(values.length != 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
      // The first value should be smaller than second value.
      Double lowerValue = Double.parseDouble(values[0].trim());
      Double higherValue = Double.parseDouble(values[1].trim());
      if (isJuelExpr) {
        // BTW 100,200 -> GTE 100 && LTE 200
        return MessageFormat.format(
            (lowerValue <= higherValue) ? "GTE {0} && LTE {1}" : "GTE {1} && LTE {0}",
            values[0].trim(),
            values[1].trim());
      } else {
        // BTW 100,200 -> [100..200]
        return MessageFormat.format(
            (lowerValue <= higherValue) ? "[{0}..{1}]" : "[{1}..{0}]",
            values[0].trim(),
            values[1].trim());
      }
    } else {
      return expression;
    }
  }

  /**
   * Tokenizes the expression
   * Since in the DMN expression, the label can have spaces,
   * but dmnexpression variable can't have space.
   * @param type
   * @param userFriendlyExpression
   * @return
   */
  private String prepareDmnExpression(String type, String userFriendlyExpression) {
    String userFriendlyExpressionPostHandle = handleSpecialOperator(userFriendlyExpression, Boolean.FALSE);
    if (!userFriendlyExpressionPostHandle.equalsIgnoreCase(userFriendlyExpression)) {
      return userFriendlyExpressionPostHandle;
    }
    String[] stringToken = userFriendlyExpression.split(WorkflowConstants.SPACE);
    Map<String, String> supportedOperator =
        BpmnProcessorUtil.getSupportedOperator(type.toUpperCase());
    String dmnFriendlyExpression = "";
    for (String token : stringToken) {
      if (null != supportedOperator.get(token)) {
        String operatorEquivalent = workflowGlobalConfiguration.getOperatorMap().get(token);
        if (token.equalsIgnoreCase(WorkflowConstants.EQ)) {
          operatorEquivalent = "";
        }
        dmnFriendlyExpression =
            MessageFormat.format("{0}{1} ", dmnFriendlyExpression, operatorEquivalent);
      } else if (WorkflowConstants.AND_MODIFIER.equalsIgnoreCase(token)) {
        dmnFriendlyExpression = MessageFormat.format("{0}{1}", dmnFriendlyExpression, token);
      } else {
        dmnFriendlyExpression = MessageFormat.format("{0}{1} ", dmnFriendlyExpression, token);
      }
    }
    return dmnFriendlyExpression.trim();
  }

  /**
   * Prepare expression for non string data types For example a) Amount GT 500 -> Amount > 500 b)
   * Amount GT 500 && LT 1000 -> Amount > 500 && Amount < 1000 c) Amount GT 500 && LT 1000 && EQ
   * 2000 -> Amount > 500 && Amount < 1000 && Amount == 2000
   *
   * @param parameterName : Parameter Name of the DMN
   * @param type : Type of Parameter Name
   * @param userFriendlyExpression : Returned Parsed/Transformed Expression
   * @return
   */
  private String prepareLegacyDmnExpression(
      String parameterName, String type, String userFriendlyExpression) {
    String legacyDmnExpr = handleSpecialOperator(userFriendlyExpression, true);
    String[] stringToken = legacyDmnExpr.split(WorkflowConstants.SPACE);
    String dmnFriendlyExpression =
        MessageFormat.format(
            "{0} ", parameterName.replaceAll(WorkflowConstants.WHITESPACE_REGEX, ""));

    Map<String, String> supportedOperator =
        BpmnProcessorUtil.getSupportedOperator(type.toUpperCase());
    for (String token : stringToken) {
      if (null != supportedOperator.get(token)) {
        // Result x >
        String operatorEquivalent = workflowGlobalConfiguration.getOperatorMap().get(token);
        dmnFriendlyExpression =
            MessageFormat.format("{0}{1} ", dmnFriendlyExpression, operatorEquivalent);
      } else if (WorkflowConstants.AND_MODIFIER.equalsIgnoreCase(token)) {
        // Result x > 100 && x
        dmnFriendlyExpression =
            MessageFormat.format(
                "{0}{1} {2} ",
                dmnFriendlyExpression,
                token,
                parameterName.replaceAll(WorkflowConstants.WHITESPACE_REGEX, ""));
      } else {
        dmnFriendlyExpression = MessageFormat.format("{0}{1} ", dmnFriendlyExpression, token);
      }
    }
    return dmnFriendlyExpression.trim();
  }
}
