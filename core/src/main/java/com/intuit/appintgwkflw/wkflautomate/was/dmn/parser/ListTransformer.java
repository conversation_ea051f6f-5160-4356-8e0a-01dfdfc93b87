package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.dmn.engine.impl.spi.type.DmnDataTypeTransformer;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.TypedValue;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ListTransformer implements DMNDataTypeTransformer, DmnDataTypeTransformer {
  /**
   * @param userFriendlyExpr input expression
   * @param parameterName parameter name in DMN
   *<pre>
   *    <p>
   *    parameterName: Location expression: "CONTAINS 1,2" returns Location.equals("1") || Location.equals("2")
   *    </p>
   *    <p>
   *    parameterName: xyz expression: "CONTAINS a b,cd" returns xyz.equals("a b") || xyz.equals("cd")
   *    </p>
   *    <p>
   *    parameterName: customer expression: "CONTAINS 1,2 && NOT_CONTAINS 3" returns customer.equals("a b") && xyz.equals("cd")
   *    </p>
   * </pre>
   */
  @Override
  public String transformToDmnFriendlyExpression(String userFriendlyExpr, String parameterName,
      String parameterType, boolean useFeelExpr) {
    if (StringExpressionHelper.isSelectAllRule(parameterName, userFriendlyExpr)) {
      // In case of Select All, return the expression as it is
      return userFriendlyExpr;
    }

    String[] expressions = userFriendlyExpr.split(WorkflowConstants.AND_MODIFIER);
    List<String> dmnExpressionList = new ArrayList<>();

    for (int i = 0; i < expressions.length; i++) {
      //limit = 2 added to handle cases of string operands having space char
      String[] exprTokens = expressions[i].trim().split(WorkflowConstants.SPACE, 2);
      String operator = exprTokens[0];
      List<String> values = Arrays.asList(exprTokens[1].split(WorkflowConstants.COMMA));
      String dmnExpressionForValue = "";
      if (useFeelExpr) {
        dmnExpressionForValue = StringExpressionHelper.prepareContainsAnyElementExpressionForList(
            parameterName, operator, values);
      } else {
        //TODO: Remove this when code is fully deployed and FF is enabled
        dmnExpressionForValue = prepareEqualsExpressionForList(
            parameterName, operator, values);
      }
      if (StringUtils.isNotEmpty(dmnExpressionForValue)) {
        dmnExpressionList.add(dmnExpressionForValue);
      }
    }

    return StringUtils.join(dmnExpressionList,
        WorkflowConstants.SPACE + WorkflowConstants.AND_MODIFIER + WorkflowConstants.SPACE);
  }

  @Override
  public DMNSupportedOperator getName() {
    return DMNSupportedOperator.LIST;
  }

  @Override
  public String transformToUserFriendlyExpression(String dmnFriendlyExpr, String parameterName) {
    if(dmnFriendlyExpr.contains(WorkflowConstants.CONTAINS_ANY_ELEMENT)) {
      return StringExpressionHelper.transformListContainsUserFriendlyExpression(dmnFriendlyExpr);
    }
    return StringExpressionHelper.transformEqualsUserFriendlyExpression(dmnFriendlyExpr);
  }

  @Override
  public String defaultRule(String parameterName, String defaultValue) {
    return defaultValue;
  }

  @Override
  public String getDataType() {
    return String.class.getSimpleName();
  }

  public String prepareEqualsExpressionForList(
      String parameterName, String operatorKeyword, List<String> values) {
    String dmnExpression = "";
    switch (operatorKeyword) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        dmnExpression =
            StringExpressionHelper.createContainsLegacyDmnExpression(
                dmnExpression, parameterName, values);
        break;
      case WorkflowConstants.NOT_CONTAINS:
        dmnExpression =
            StringExpressionHelper.createNotContainsLegacyDmnExpression(
                dmnExpression, parameterName, values);
        break;
      default:
        throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
    }

    return dmnExpression;
  }

  //Used by engine to transform variable values
  @Override
  public TypedValue transform(Object value) throws IllegalArgumentException {
    String valueAsStr;
    if (value instanceof String) {
      valueAsStr = (String) value;
    } else if (value instanceof List) {
      valueAsStr = ((List<String>) value).stream().map(Object::toString)
          .collect(Collectors.joining(","));
    } else {
      WorkflowLogger.logWarn("Untyped_Value value=" + value);
      return Variables.untypedValue(value);
    }

    if (StringUtils.isEmpty(valueAsStr)) {
      return Variables.untypedNullValue();
    } else {
      return Variables.stringValue(valueAsStr);
    }
  }
}