package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.StringOperator;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class StringDataTypeTransformer implements DMNDataTypeTransformer {

  /**
   * For String (Possible Cases)
   * <pre>
   *  ex,
   *  a) "CONTAINS 1"
   *  b) "NOT_CONTAINS 3"
   *  c) "CONTAINS 1,2,4"
   *  d) "CONTAINS 1,2,43 && NOT_CONTAINS 3"
   *  e) "CONTAINS 1,2,43 && NOT_CONTAINS 3,4"
   *  f) "CONTAINS 1 && NOT_CONTAINS 3"
   *  g) "CONTAINS 1 && NOT_CONTAINS 3,4"
   * </pre>
   *
   */
  @Override
  public String transformToDmnFriendlyExpression(
      String userFriendlyExpr, String parameterName, String parameterType, boolean useFeelExpr) {
    if (StringExpressionHelper.isSelectAllRule(parameterName, userFriendlyExpr)) {
      // In case of Select All, return the expression as it is
      return userFriendlyExpr;
    }

    return prepareDmnRuleExpression(userFriendlyExpr, parameterName, useFeelExpr);
  }

  private final List<String> opList =
      List.of(
          WorkflowConstants.CONTAINS_OPERATOR,
          WorkflowConstants.NOT_CONTAINS,
          WorkflowConstants.ANY_MATCH,
          WorkflowConstants.NO_MATCH);

  /**
   * This method takes care of String Expression in case of no mutual exclusion. i.e.
   * Expression having both contains and not contains operator
   *
   * <p>For example "CONTAINS 1,2,43 && NOT_CONTAINS 3"
   *
   * @param expression : Expression containing both Contains and Not-Contains query
   * @param parameterName : Parameter Name of the DMN
   * @return
   */
  public String prepareDmnRuleExpression(String expression, String parameterName, boolean useFeelExpr) {
    String dmnRuleExpression = "";
    String[] expressions = expression.split(WorkflowConstants.AND_MODIFIER);
    for (int i = 0; i < expressions.length; i++) {
      /**
       * limit = 2 added to handle cases of string operands having space char Example: "CONTAINS
       * ab,cd ef && NOT_CONTAINS pq,wx yz"
       *
       * <p>TODO: handle special chars in operand values by encoding during read/decoding during
       * create
       */
      String[] exprTokens = expressions[i].trim().split(WorkflowConstants.SPACE, 2);
      WorkflowVerfiy.verify(
          exprTokens  .length < 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
      List<String> values = Arrays.asList(exprTokens[1].split(WorkflowConstants.COMMA));

      if (opList.stream().anyMatch(exprTokens[0]::equalsIgnoreCase)) {
        if (useFeelExpr) {
          dmnRuleExpression =
              prepareDmnExpressionForString(
                  values, parameterName, exprTokens[0], dmnRuleExpression);
        } else {
          dmnRuleExpression =
              prepareLegacyDmnExpressionForString(
                  values, parameterName, exprTokens[0], dmnRuleExpression);
        }
      }
    }
    // Removing Trailing spaces
    return dmnRuleExpression.trim();
  }

  /**
   * This method generates JUEL expression from
   * String Expression having either contains or not contains operator
   *
   * <p>For example "CONTAINS 1,2"
   * is converted to customer.equals("1") || customer.equals("2")
   *
   *  @param dmnFriendlyExpression : Expression containing either Contains or Not-Contains query IN JUEL
   *  @param parameterName : Parameter Name of the DMN
   *  @return
   */
  public String prepareLegacyDmnExpressionForString(
      List<String> values,
      String parameterName,
      String operatorKeyword,
      String dmnFriendlyExpression) {
    switch (operatorKeyword) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        dmnFriendlyExpression =
            StringExpressionHelper.createContainsLegacyDmnExpression(
                dmnFriendlyExpression, parameterName, values);
        break;
      case WorkflowConstants.NOT_CONTAINS:
        dmnFriendlyExpression =
            StringExpressionHelper.createNotContainsLegacyDmnExpression(
                dmnFriendlyExpression, parameterName, values);
        break;
      case WorkflowConstants.ANY_MATCH:
        dmnFriendlyExpression =
            StringExpressionHelper.createAnyMatchLegacyDmnExpression(
                dmnFriendlyExpression, parameterName, values);
        break;
      case WorkflowConstants.NO_MATCH:
        dmnFriendlyExpression =
            StringExpressionHelper.createNoMatchLegacyDmnExpression(
                dmnFriendlyExpression, parameterName, values);
        break;
      default:
        throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
    }

    if (StringUtils.isEmpty(dmnFriendlyExpression)) {
      return dmnFriendlyExpression;
    }
    return dmnFriendlyExpression.trim();
  }

  /**
   * This method generates FEEL expression from
   * String Expression having either contains or not contains operator
   *
   * <p>For example "NOT_CONTAINS 1,2"
   * is converted to not("1","2")
   *
   *  @param dmnFriendlyExpression : Expression containing either Contains or Not-Contains query IN FEEL
   *  @param parameterName : Parameter Name of the DMN
   *  @return
   */
  public String prepareDmnExpressionForString(
      List<String> values,
      String parameterName,
      String operatorKeyword,
      String dmnFriendlyExpression) {
    switch (operatorKeyword) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        dmnFriendlyExpression = StringExpressionHelper.createContainsDmnExpression(values);
        break;
      case WorkflowConstants.NOT_CONTAINS:
        dmnFriendlyExpression = StringExpressionHelper.createNotContainsDmnExpression(values);
        break;
      case WorkflowConstants.ANY_MATCH:
        dmnFriendlyExpression =
            StringExpressionHelper.createAnyMatchDmnExpression(parameterName, values);
        break;
      case WorkflowConstants.NO_MATCH:
        dmnFriendlyExpression =
            StringExpressionHelper.createNoMatchDmnExpression(
                dmnFriendlyExpression, parameterName, values);
        break;
      default:
        throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
    }

    if (StringUtils.isEmpty(dmnFriendlyExpression)) {
      return dmnFriendlyExpression;
    }
    return dmnFriendlyExpression.trim();
  }

  /**
   * Generates String expression from DMN expression
   * For expression having equals or contains operator
   * @param dmnFriendlyExpr input rule saved in DMN
   * @param parameterName : Parameter Name of the DMN
   * @return
   */
  @Override
  public String transformToUserFriendlyExpression(String dmnFriendlyExpr, String parameterName) {
    if(StringUtils.isEmpty(dmnFriendlyExpr)) {
      return null;
    }

    boolean isJuelExpr = dmnFriendlyExpr.contains(parameterName);
    if (!isJuelExpr){
      return StringExpressionHelper.createUserFriendlyRuleExpressionForFeelExp(dmnFriendlyExpr);
    }

    String userFriendlyExpr = "";

    if (dmnFriendlyExpr.contains(WorkflowConstants.EQUALS_OPERATOR.toLowerCase())) {
      userFriendlyExpr =
          StringExpressionHelper.transformEqualsUserFriendlyExpression(dmnFriendlyExpr);
    } else if (dmnFriendlyExpr.contains(WorkflowConstants.CONTAINS_OPERATOR.toLowerCase())) {
      userFriendlyExpr =
          StringExpressionHelper.transformContainsUserFriendlyExpression(dmnFriendlyExpr);
    }
    return userFriendlyExpr;
  }

  /**
   * For String data types, default values will be Contains ALL_
   * <parameterName> In case of Invoice Approval
   * @param parameterName
   * @param defaultValue default value from DMN
   * @return
   */
  @Override
  public String defaultRule(String parameterName, String defaultValue) {
    if (StringUtils.isEmpty(defaultValue)) {
      return MessageFormat.format(
          "{0} {1}_{2}",
          StringOperator.WITHIN.getSymbol(), WorkflowConstants.KEYWORD_ALL, parameterName);
    } else {
      return MessageFormat.format(
          WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, WorkflowConstants.EQ, defaultValue);
    }
  }

  public String getDataType() {
    return String.class.getSimpleName();
  }

  @Override
  public DMNSupportedOperator getName() {
    return DMNSupportedOperator.STRING;
  }
}
