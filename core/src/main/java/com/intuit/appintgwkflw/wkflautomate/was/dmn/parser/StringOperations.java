package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.text.MessageFormat;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;
import java.util.List;
/**
 * The expression received is of the form CONTAINS 1,2. Such an expression needs to be saved in DMN.
 * So needs to be converted to dmnFriendlyExpression. This class provides the utility methods to
 * achieve this.
 * Ex. @ParameterName Customer
 *
 * @expression CONTAINS 1,2
 *     <p>The dmnFriendlyExpresion is converted to Customer.equals("1") || Customer.equals("2")
 */
@UtilityClass
public class StringOperations {

  private String expressionCreatorForContainsAndAnyMatch(
      String token, String dmnFriendlyExpression, String parameterName, String operation) {
    String result =
        StringUtils.isEmpty(dmnFriendlyExpression)
            ? MessageFormat.format(
                "{0}{1}{2}(\"{3}\")",
                parameterName, WorkflowConstants.DOT_OPERATOR, operation, token)
            : MessageFormat.format(
                "{0} {1} {2}{3}{4}(\"{5}\")",
                dmnFriendlyExpression,
                WorkflowConstants.OR_MODIFIER,
                parameterName,
                WorkflowConstants.DOT_OPERATOR,
                operation,
                token);
    return result;
  }

  private String expressionCreatorForNotContainsAndNoMatch(
      String token, String dmnFriendlyExpression, String parameterName, String operation) {
    String result =
        StringUtils.isEmpty(dmnFriendlyExpression)
            ? MessageFormat.format(
                "{0}{1}{2}{3}(\"{4}\")",
                WorkflowConstants.NOT_OPERATOR,
                parameterName,
                WorkflowConstants.DOT_OPERATOR,
                operation,
                token)
            : MessageFormat.format(
                "{0} {1} {2}{3}{4}{5}(\"{6}\")",
                dmnFriendlyExpression,
                WorkflowConstants.AND_MODIFIER,
                WorkflowConstants.NOT_OPERATOR,
                parameterName,
                WorkflowConstants.DOT_OPERATOR,
                operation,
                token);
    return result;
  }

  public String createContainsExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {

    /**
     * If dmnFriendlyExpression is empty - Parameter name is Customer and value to be contained is
     * 123. So, rule will be framed like this : {0} = Customer, {1} = ".", {2} = equals, {3} = 123
     * Resultant Expression = Customer.equals("123")
     */

    /**
     * If dmnFriendlyExpression is not empty - Parameter name is Customer and value to be contained
     * is 1234. So, rule will be framed like this : {0} = Customer.equals("123")[Existing
     * expression] , {1} = ||, {2} = Customer, {3} = . ,{4} = equals, {5} = 1234 Resultant
     * Expression = Customer.equals("123") || Customer.equals("1234")
     */
    for (String token : values) {
      dmnFriendlyExpression =
          expressionCreatorForContainsAndAnyMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.EQUALS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  public String createNotContainsExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    /**
     * If dmnFriendlyExpression is empty - Parameter name is Customer and value to be contained is
     * 123. So, rule will be framed like this : {0} = !, {1} = Customer, {2} = . ,{3} = equals, {4}
     * = 1234 Resultant Expression = !Customer.equals("123")
     */

    /**
     * If dmnFriendlyExpression is not empty - Parameter name is Customer and value to be contained
     * is 123. So, rule will be framed like this : {0} = !Customer.equals("123")[Existing
     * expression] , {1} = || ,{2} = !, {3} = Customer, {4} = . ,{5} = equals, {6} = 123 Resultant
     * Expression = !Customer.equals("123") || !Customer.equals("1234")
     */
    for (String token : values) {
      dmnFriendlyExpression =
          expressionCreatorForNotContainsAndNoMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.EQUALS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  public String createAnyMatchExpression(
      /**
       * If dmnFriendlyExpression is empty - Parameter name is Customer and value to be contained is
       * 123. So, rule will be framed like this : {0} = !, {1} = Customer, {2} = . ,{3} = equals,
       * {4} = 1234 Resultant Expression = !Customer.contains("123")
       */

      /**
       * If dmnFriendlyExpression is not empty - Parameter name is Customer and value to be
       * contained is 123. So, rule will be framed like this : {0} =
       * !Customer.equals("123")[Existing expression] , {1} = || ,{2} = !, {3} = Customer, {4} = .
       * ,{5} = equals, {6} = 123 Resultant Expression = !Customer.contains("123") ||
       * !Customer.contains("1234")
       */
      String dmnFriendlyExpression, String parameterName, List<String> values) {

    for (String token : values) {
      dmnFriendlyExpression =
          expressionCreatorForContainsAndAnyMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.CONTAINS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  /**
   * If dmnFriendlyExpression is empty - Parameter name is Customer and value to be contained is
   * 123. So, rule will be framed like this : {0} = !, {1} = Customer, {2} = . ,{3} = equals, {4} =
   * 1234 Resultant Expression = !Customer.contains("123")
   */

  /**
   * If dmnFriendlyExpression is not empty - Parameter name is Customer and value to be contained is
   * 123. So, rule will be framed like this : {0} = !Customer.equals("123")[Existing expression] ,
   * {1} = || ,{2} = !, {3} = Customer, {4} = . ,{5} = equals, {6} = 123 Resultant Expression =
   * !Customer.contains("123") || !Customer.contains("1234")
   */
  public String createNoMatchExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    for (String token : values) {
      dmnFriendlyExpression =
          expressionCreatorForNotContainsAndNoMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.CONTAINS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }
}
