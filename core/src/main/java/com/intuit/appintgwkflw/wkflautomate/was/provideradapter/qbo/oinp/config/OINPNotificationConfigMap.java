package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.oinpqbo.bridge.NotificationGroup;
import java.io.InputStream;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Loads the notification config map at service startup
 */
@Component
public class OINPNotificationConfigMap {

  private static final String FILE_PATH = "oinpqbo/qboOinpNotificationConfig.json";

  private Map<String, NotificationGroup> notificationGroupMap;

  /**
   * Load the config file
   */
  @PostConstruct
  public void initConfigMap(){
    InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(FILE_PATH);
    notificationGroupMap = ObjectConverter.fromJsonStream
        (inputStream, new TypeReference<Map<String, NotificationGroup>>() {});
  }

  /**
   * Get notification mapping key
   * @param key notification key
   * @return value
   */
  public NotificationGroup getNotificationGroup(String key){
    return notificationGroupMap.get(key);
  }
}
