package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * evaluates the condition to send notification through OINP
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "oinp")
public class OINPSendNotificationConfig {

  private Map<String, WorkflowConstraints> sendNotification;

  /**
   * Evaluates condition based on workflowName, notificationType and ownerId.
   * if config contains -1 in ownerId, all ownerIds are whitelisted.
   * @param workflowName name of workflow
   * @param notificationType type of notification
   * @param ownerId ownerId of the workflow
   */
  public boolean isNotificationEnabled(String workflowName, String notificationType, Long ownerId) {
    return Optional.ofNullable(sendNotification)
        .map(sendNotificationMap -> sendNotificationMap.containsKey(workflowName)
            && sendNotificationMap.get(workflowName).getNotificationTypes().contains(notificationType)
            && (sendNotificationMap.get(workflowName).getOwnerIds().contains(-1L)
            || sendNotificationMap.get(workflowName).getOwnerIds().contains(ownerId)))
        .orElse(false);

  }

  @Getter
  @Setter
  public static class WorkflowConstraints {

    private Set<String> notificationTypes;

    private Set<Long> ownerIds;

  }
}




