package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.math.NumberUtils;

/**
 * <AUTHOR>
 * Modifies the keys in the appconnect as per bridging requirements.
 */
@RequiredArgsConstructor
public class CustomHandlingTask implements Task {

  private final IdentityService identityService;

  private String realmId;

  private boolean isEmail;

  private State state;

  @Override
  public State execute(State state) {

    WorkerActionRequest workerActionRequest = state.getValue(
        OinpBridgeConstants.WORKER_ACTION_REQUEST);

    this.state = state;
    this.realmId = String.valueOf(workerActionRequest.getOwnerId());
    this.isEmail = state.getValue(OinpBridgeConstants.IS_EMAIL);

    Map<String, String> outputMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    customValueHandling(outputMap);

    return this.state;
  }

  /**
   * Replace values in mapping based on custom logic
   * @param map input variables map
   */
  private void customValueHandling(Map<String, String> map){
    modifyToField(map);
  }

  /**
   * Modifies "To" field.
   * Invokes IUS to replace auth-id with emails-id
   * Puts all valid authIds in context
   * @param map contains "To" field
   */
  private void modifyToField(Map<String, String> map){
    String[] toField = Optional.ofNullable(map.get(OinpBridgeConstants.TO_FIELD))
        .map(field -> field.split(OinpBridgeConstants.TO_FIELD_DELIMITER))
        .orElse(new String[0]);
    Set<String> authIds = new HashSet<>();
    Set<String> emailIds = new HashSet<>();
    for (String to : toField){
      if (NumberUtils.isDigits(to)){
        authIds.add(to);
      }
      else if(to.contains(OinpBridgeConstants.EMAIL_AT)){
        emailIds.add(to);
      }
    }
    Map<String, Persona> iusResponseMap = identityService.getRealmPersonas(realmId, authIds);

    if (isEmail) {
      emailIds.addAll(
          iusResponseMap.values().stream()
              .map(persona -> persona.getEmail().getEmailId())
              .collect(Collectors.toList()));
      map.put(
          OinpBridgeConstants.TO_FIELD,
          String.join(OinpBridgeConstants.TO_FIELD_DELIMITER, emailIds));
    } else {
      map.put(
          OinpBridgeConstants.TO_FIELD,
          String.join(OinpBridgeConstants.TO_FIELD_DELIMITER, iusResponseMap.keySet()));
    }
    state.addValue(OinpBridgeConstants.AUTH_IDS, new TreeSet<>(iusResponseMap.keySet()));
    state.addValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP, iusResponseMap);

    logInvalidAuthIds(authIds);
  }

  /**
   * Log all invalid authIds
   * @param authIds input authIds
   */
  private void logInvalidAuthIds(Set<String> authIds){
    Set<String> validAuthIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    if (authIds.size() == validAuthIds.size()) {
      return;
    }
    Set<String> invalidAuths = authIds.stream()
        .filter(auth -> !validAuthIds.contains(auth))
        .collect(Collectors.toSet());
    if (!invalidAuths.isEmpty()){
      WorkflowLogger.logWarn("OinpCustomHandlingTask invalid authIds=%s received in input", invalidAuths);
    }
  }
}
