package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.oinpqbo.bridge.NotificationGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPNotificationConfigMap;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.*;
/**
 * Maps the keys in input to the keys in config
 * and adds notification name/type in state variable
 */
@RequiredArgsConstructor
public class OINPEventMappingTask implements Task {

  private final OINPNotificationConfigMap oinpNotificationConfigMap;
  private State state;
  private NotificationGroup notificationGroup;

  @Override
  public State execute(State state) {

    boolean isEmail = state.getValue(IS_EMAIL);
    boolean consolidateNotifications = state.getValue(CONSOLIDATE_NOTIFICATIONS);

    this.state = state;

    String templateName = state.getValue(TEMPLATE_NAME);
    Map<String, String> variableMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);
    String flowName;

    if (consolidateNotifications) {
      flowName = CONSOLIDATE_NOTIFICATIONS;
    } else if (isEmail) {
      flowName = IS_EMAIL;
    } else {
      flowName = IS_MOBILE;
    }

    String configKey =
        Optional.ofNullable(templateName)
            .orElse("")
            .concat(WorkflowConstants.UNDERSCORE)
            .concat(flowName);

    notificationGroup =
        Optional.ofNullable(oinpNotificationConfigMap.getNotificationGroup(configKey))
            .orElse(oinpNotificationConfigMap.getNotificationGroup(flowName));
    /*
     * pick notification name and notification type from custom workflow config if available
     * else default will be picked from the oinpNotificationConfigMap
     */
    state.addValue(
        NOTIFICATION_NAME,
        variableMap.getOrDefault(NOTIFICATION_NAME, notificationGroup.getNotificationName()));
    state.addValue(
        NOTIFICATION_DATA_TYPE,
        variableMap.getOrDefault(NOTIFICATION_TYPE, notificationGroup.getNotificationDataType()));

    modifyOutputMapping();

    return this.state;
  }

  /**
   * Modify the key mapping of notification eventData as per mapping mentioned in the config
   */
  private void modifyOutputMapping(){
    Map<String, Object> outputMap = state.getValue(BRIDGE_OUTPUT_MAP);

    Map<String, List<String>> mapper = notificationGroup.getMappingAttributes();

    Map<String, Object> mappedOutput = new HashMap<>();

    outputMap.forEach(
        (k, v) -> {
          List<String> keys = Optional.ofNullable(mapper.get(k))
              .orElse(Collections.singletonList(k));
          keys.forEach(
              key ->
                  mappedOutput.put(key, v));
        }
    );

    state.addValue(BRIDGE_OUTPUT_MAP, mappedOutput);
  }
}
