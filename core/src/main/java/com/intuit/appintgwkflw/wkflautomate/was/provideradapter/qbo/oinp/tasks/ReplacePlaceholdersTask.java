package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.SubstitutePlaceholderUtil;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Map;

/**
 * <AUTHOR>
 * Adds placeholder map in the RxExecutionContext
 */
public class ReplacePlaceholdersTask implements Task {

  @Override
  public State execute(State state) {
    Map<String, String> inputMap = state.getValue(OinpBridgeConstants.BRIDGE_INPUTS_MAP);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP,
        SubstitutePlaceholderUtil.substitutePlaceholder(inputMap, false));
    return state;
  }
}
