package com.intuit.appintgwkflw.wkflautomate.was.rxhooks;

import com.intuit.v4.Authorization;
import java.util.Map;
import java.util.Optional;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import org.slf4j.MDC;

/**
 * Copies MDC from Parent thread to child thread
 *
 * <AUTHOR>
 */
public class MdcPropagatingRunnable implements Runnable {
  private final Runnable runnable;
  private final Map<String, String> context;
  private final Optional<String> offeringId;
  private final Authorization authorization;
  private final Boolean isSystemUser;
  private final Boolean isRealmSystemUser;
  private final Boolean isMigration;


  /**
   * Decorates an {@link Runnable} so that it executes with the current {@link MDC} as its context.
   * Copies {@link ThreadLocal} variables from {@link WASContext}
   *
   * @param runnable the {@link Runnable} to decorate.
   */
  public MdcPropagatingRunnable(final Runnable runnable) {
    this.runnable = runnable;
    this.context = MDC.getCopyOfContextMap();
    this.offeringId = WASContext.getOfferingId();
    this.authorization = WASContext.getAuthContext();
    this.isSystemUser = WASContext.isNonRealmSystemUser();
    this.isRealmSystemUser = WASContext.isRealmSystemUser();
    this.isMigration = WASContext.isMigrationContext();
  }

  @Override
  public void run() {
    if (context != null) {
      MDC.setContextMap(context);
      WASContext.setOfferingId(this.offeringId.orElse(null));
      WASContext.setRealmSystemUserContext(this.isRealmSystemUser);
      WASContext.setMigrationContext(this.isMigration);
      /**
       * In case you come up with an reflective approach to copy variables from one thread to another
       * like here: https://stackoverflow.com/questions/7259906/propagating-threadlocal-to-a-new-thread-fetched-from-a-executorservice
       * Do test it with {@link RxExecutionChain#executeAsync()} method, by running assertion tests in loop(like 100 times) on same thread.
       * Above approach was tried and it failed in executeAsync(), but worked fine with execute().
       */
      setAllThreadLocal();
    }
    try {
      this.runnable.run();
    } finally {
      MDC.clear();
      WASContext.clear();
    }
  }

  /**
   * Set values in threadLocal of runnable thread.
   * To be invoked from {@link #run()} only.
   * {@link #offeringId} is not included, as it is critical variable. Adding only new variables.
   */
  private void setAllThreadLocal(){
    WASContext.setAuthContext(authorization);
    WASContext.setNonRealmSystemUserContext(isSystemUser);
  }
}
