package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_DETAILS;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.Map;
import java.util.Objects;

/** <AUTHOR> */
/** Get handler details from input variables **/
public class CamundaInputVariablesHandlerDetailsExtractor implements Task {

  private HandlerDetails getHandlerDetailsFromInputVariables(
      Map<String, String> inputVariablesMap) {
    HandlerDetails handlerDetails =
        SchemaDecoder.getHandlerDetails(inputVariablesMap)
            .orElseThrow(() -> new WorkflowGeneralException(UNSUPPORTED_HANDLER_DETAILS));
    return handlerDetails;
  }

  /**
   * Get handler details from input variables. If not present, try to get it from the config.
   *
   * @param inputRequest
   * @return
   */
  @Override
  public State execute(State inputRequest) {
    // if handler details are already filled, skip the chain.
    if (Objects.nonNull(inputRequest.getValue(AsyncTaskConstants.HANDLER_DETAILS))) {
      return inputRequest;
    }
    Map<String, String> inputVariablesMap =
        inputRequest.getValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP);
    HandlerDetails actionHandlerDetailsfromInputVariables =
        ObjectConverter.fromJson(
            inputVariablesMap.get(WorkflowConstants.HANDLER_DETAILS), HandlerDetails.class);
    if (Objects.nonNull(actionHandlerDetailsfromInputVariables)
        && Objects.nonNull(actionHandlerDetailsfromInputVariables.getTaskHandler())) {
      HandlerDetails handlerDetails = getHandlerDetailsFromInputVariables(inputVariablesMap);
      inputRequest.addValue(AsyncTaskConstants.HANDLER_DETAILS, handlerDetails);
    }
    return inputRequest;
  }
}
