package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;

import java.util.Map;
import java.util.Objects;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

/** <AUTHOR> */
/** Get handler details from Custom Workflow Config **/
public class CustomWorkflowConfigHandlerDetailsExtractor implements Task {

  /**
   * Getting handler details from the config, assuming handler details are part of a custom-workflow
   * action, which don't have handler details defined in the template.
   *
   * @param inputRequest
   * @return
   */
  @Override
  @Metric(name = MetricName.CUSTOM_WORKFLOW_CONFIG_HANDLER_DETAILS_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public State execute(State inputRequest) {
    if (Objects.nonNull(inputRequest.getValue(AsyncTaskConstants.HANDLER_DETAILS))) {
      return inputRequest;
    }
    ExternalTaskEvent externalTask = inputRequest.getValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK);
    Map<String, String> inputVariablesMap =
        inputRequest.getValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP);
    CustomWorkflowConfig customWorkflowConfig =
        inputRequest.getValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG);

    try {
      String recordType = inputVariablesMap.get(ENTITY_TYPE);
      CustomWorkflowType customWorkflowType =
          CustomWorkflowType.getCustomWorkflowForTemplateName(
              externalTask.getProcessDefinitionKey());

      /**
       * In case of called process, the handler details not fetched here, would be
       * fetched and set in the {@link ActionGroupConfigHandlerExtractor}
       */
      if(Objects.isNull(customWorkflowType) && isCalledProcess(externalTask)) {
        return inputRequest;
      }

      String actionKey = customWorkflowType.getActionKey();
      String actionId = externalTask.getActivityId();
      com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
          recordAction =
              CustomWorkflowUtil.getRecordActionFromConfig(
                  customWorkflowConfig, recordType, actionKey, actionId);
      HandlerDetails handlerDetails = recordAction.getHandler().getHandlerDetail();
      inputRequest.addValue(AsyncTaskConstants.HANDLER_DETAILS, handlerDetails);
    }
    catch (final Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No handler details found in the config")
                  .stackTrace(e)
                  .className(this.getClass().getSimpleName()));
      throw new WorkflowGeneralException(UNSUPPORTED_HANDLER_DETAILS);
    }
    return inputRequest;
  }

  private boolean isCalledProcess(ExternalTaskEvent externalTask) {
    return Objects.nonNull(externalTask.getVariables().get(ROOT_PROCESS_INSTANCE_ID));
  }
}
