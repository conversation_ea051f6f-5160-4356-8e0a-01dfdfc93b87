package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_DETAILS;

import java.util.Map;

import org.springframework.util.CollectionUtils;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

/** <AUTHOR> */
/** Get handler details from Extension attributes of the BPMN * */
public class ExtensionAttributesHandlerDetailsExtractor implements Task {

  /**
   * Get handler details from extension attributes. If not present try to get it from input
   * variables
   *
   * @param inputRequest
   * @return
   */
  @Override
  public State execute(State inputRequest) {

	  ExternalTaskEvent externalTaskEvent = inputRequest.getValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK);
    Map<String, String> extensionAttributes = externalTaskEvent.getExtensionProperties();
    // 1. Checking if handler details are present in the extension attributes.
    if (!CollectionUtils.isEmpty(extensionAttributes)
        && extensionAttributes.containsKey(WorkFlowVariables.HANDLER_DETAILS_KEY.getName())) {
      HandlerDetails handlerDetails =
          SchemaDecoder.getHandlerDetails(extensionAttributes)
              .orElseThrow(() -> new WorkflowGeneralException(UNSUPPORTED_HANDLER_DETAILS));
      inputRequest.addValue(AsyncTaskConstants.HANDLER_DETAILS, handlerDetails);
    }
    return inputRequest;
  }
}
