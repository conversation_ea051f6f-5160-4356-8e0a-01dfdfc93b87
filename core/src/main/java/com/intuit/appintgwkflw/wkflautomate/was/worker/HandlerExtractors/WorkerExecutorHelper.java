package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;

import lombok.experimental.UtilityClass;
import org.camunda.bpm.client.task.ExternalTask;

@UtilityClass
public class WorkerExecutorHelper {
  /**
   * Get handler details from extension attributes if present else get from input variables. If not
   * present in both, try to get it from the config.
   *
   * @param externalTask
   * @param inputVariablesMap
   * @param customWorkflowConfig
   * @return workerExecutorHelper
   */
  public HandlerDetails fetchHandlerDetails(
		  ExternalTaskEvent externalTask,
      Map<String, String> inputVariablesMap,
      CustomWorkflowConfig customWorkflowConfig,
      ProcessDetailsRepoService processDetailsRepoService) {

    final State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, inputVariablesMap);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);

    new RxExecutionChain(inputRequest)
        .next(new ExtensionAttributesHandlerDetailsExtractor())
        .next(new CamundaInputVariablesHandlerDetailsExtractor())
        .next(new CustomWorkflowConfigHandlerDetailsExtractor())
        .next(new ActionGroupConfigHandlerExtractor(processDetailsRepoService))
        .execute();
    return inputRequest.getValue(AsyncTaskConstants.HANDLER_DETAILS);
  }
  /**
   * concat and return the handler name
   *
   * @param handlerDetails input handler details object from bpmn
   * @return handler name
   */
  public String prepareHandlerName(HandlerDetails handlerDetails) {
    return handlerDetails.getTaskHandler() + "_" + handlerDetails.getActionName();
  }

  public Map<String, String> createInputVariablesMap(Map<String, Object> inputVariables) {
    return inputVariables.entrySet().stream()
            .filter(entry -> entry.getValue() != null)
            .collect(
                    Collectors.toMap(
                            Map.Entry::getKey,
                            entry ->
                                    // in case of collection type process variable, convert to JSON string
                                    entry.getValue() instanceof Collection
                                            ? ObjectConverter.toJson(entry.getValue())
                                            : entry.getValue().toString()));
  }

  public ExternalTaskEvent getExternalTaskEvent(ExternalTask externalTask) {
    return ExternalTaskEvent.builder()
            .activityId(externalTask.getActivityId())
            .activityInstanceId(externalTask.getActivityInstanceId())
            .businessKey(externalTask.getBusinessKey())
            .id(externalTask.getId())
            .executionId(externalTask.getExecutionId())
            .lockExpirationTime(externalTask.getLockExpirationTime())
            .processDefinitionId(externalTask.getProcessDefinitionId())
            .processDefinitionKey(externalTask.getProcessDefinitionKey())
            .processInstanceId(externalTask.getProcessInstanceId())
            .retries(externalTask.getRetries())
            .workerId(externalTask.getWorkerId())
            .topicName(externalTask.getTopicName())
            .tenantId(externalTask.getTenantId())
            .processDefinitionVersionTag(externalTask.getProcessDefinitionVersionTag())
            .priority(externalTask.getPriority())
            .extensionProperties(externalTask.getExtensionProperties())
            .variables(externalTask.getAllVariables()).build();
  }

}
