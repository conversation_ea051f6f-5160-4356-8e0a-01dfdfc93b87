package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.CreateWorkflowRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DefinitionPendingDeletion {
  private String id;
  private String key;

  public static DefinitionPendingDeletion buildFromDefinitionDetails(
      DefinitionDetails definitionDetails) {
    return DefinitionPendingDeletion.builder()
        .id(definitionDetails.getDefinitionId())
        .key(definitionDetails.getDefinitionKey())
        .build();
  }

  public DefinitionDetails convertToDefinitionDetails() {
    return DefinitionDetails.builder().definitionId(getId()).definitionKey(getKey()).build();
  }
}
