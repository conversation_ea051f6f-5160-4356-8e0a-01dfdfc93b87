package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import lombok.Getter;

@Getter
public enum SubscriptionStatus {
  ACTIVE("ACTIVE"),
  INACTIVE("INACTIVE");

  private String status;

  SubscriptionStatus(String status) {
    this.status = status;
  }

  public static SubscriptionStatus lookupStatus(String type) {
    for (SubscriptionStatus status : SubscriptionStatus.values()) {
      if (status.getStatus().equals(type)) {
        return status;
      }
    }
    throw new UnsupportedOperationException("SubscriptionStatus= " + type + " is not supported!");
  }
}
