package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.intuit.variability.entity.graphql.GetAttributeQuery;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowVariabilityClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.VariabilityConnector;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * 
 *         Variability Engine class to get the decision for a given feature.
 *
 */
@Component
@AllArgsConstructor
public class VariabilityEngineClient {

	private WASContextHandler contextHandler;

	private WorkflowVariabilityClientConfig workflowVariabilityClientProperties;

	private VariabilityConnector variabilityConnector;

  /**
   * Returns if the feature is enabled for the given variability request context.
   *
   * @param featureNames name of the feature
   * @return true/false
   */
  @Metric(name = MetricName.VARIABILITY, type = Type.SERVICE_METRIC)
  public Map<String, Boolean> getVariabilityDecision(Set<String> featureNames) {
    try {

      // TODO replace it with variability call.
      // checks if -1 exists or given realm is allowed.
      boolean enabled =
          workflowVariabilityClientProperties
                  .getEnabledRealms()
                  .contains(Long.valueOf(contextHandler.get(WASContextEnums.OWNER_ID)))
              || workflowVariabilityClientProperties.getEnabledRealms().contains(-1L);

      if (enabled) {
        return featureNames.stream()
            .collect(Collectors.toMap(Function.identity(), decision -> enabled));
      }
      return variabilityConnector.getDecisions(
          featureNames.stream().collect(Collectors.toList()), Collections.emptyMap());

    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("getVariabilityDecision")
                  .message("Exception getting variability decision. error=%s", e.getMessage()));
    }
    /** In case of failure in invoking variability, will set value of each decision to false */
    return featureNames.stream().collect(Collectors.toMap(Function.identity(), decision -> false));
  }
}
