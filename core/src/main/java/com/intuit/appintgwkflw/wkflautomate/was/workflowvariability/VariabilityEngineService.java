package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowVariabilityClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.variability.WorkflowFeatureMapping;
import com.intuit.v4.workflows.Template;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 *     <p>Variability Engine class to get the decision for a given set of feature.
 */
@Component
@AllArgsConstructor
public class VariabilityEngineService {

  private VariabilityEngineClient variabilityEngineClient;

  private WorkflowVariabilityClientConfig workflowVariabilityClientProperties;

  /**
   * Applies the Variability rules and remove the templates
   *
   * @param templates list of templates
   */
  public void filterTemplates(List<Template> templates) {

    try {
      // skip processing if filter is disabled
      if (!workflowVariabilityClientProperties.isEnable()) {
        return;
      }

      Set<String> featureNames = WorkflowFeatureMapping.getAllFeatures();

      Map<String, Boolean> decisionMap =
          variabilityEngineClient.getVariabilityDecision(featureNames);

      // checks if given record type has an entry for feature name if yes checks for
      // the decision on the variability decision call if decision is false it is
      // removed.
      templates.removeIf(template -> evaluateWorkflowDecision(decisionMap, template));
    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("filterTemplates")
                  .message(
                      "Exception executing variability decision. error=%s",
                      ExceptionUtils.getStackTrace(e)));
    }
  }

  /**
   * Applies the Variability rules and remove the templates. Throws exception if the list is empty
   *
   * @param template
   */
  public void filterTemplate(Template template) {
    List<Template> templateList = Arrays.asList(template);
    filterTemplates(templateList);
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(templateList), WorkflowError.FEATURE_NOT_SUPPORTED);
  }

  /**
   * @param decisionMap decision returned by Variability engine
   * @param template template details
   * @return true/false to filter out the template or not
   */
  private boolean evaluateWorkflowDecision(Map<String, Boolean> decisionMap, Template template) {
    try {
      // get the decision based on record type
      boolean entityDecision =
          decisionMap.getOrDefault(
              WorkflowFeatureMapping.getEntityFeatureMapping()
                  .getOrDefault(
                      Objects.nonNull(template.getRecordType())
                          ? template.getRecordType().toLowerCase()
                          : template.getRecordType(),
                      StringUtils.EMPTY),
              true);

      // get the decision based on workflow type
      boolean workflowTypeDecision =
          decisionMap.getOrDefault(
              WorkflowFeatureMapping.getEntityFeatureMapping()
                  .getOrDefault(
                      Objects.nonNull(template.getId())
                              && Objects.nonNull(template.getId().getLocalId())
                          ? template.getId().getLocalId().toLowerCase()
                          : StringUtils.EMPTY,
                      StringUtils.EMPTY),
              true);
      return !(entityDecision && workflowTypeDecision);

    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("evaluateWorkflowDecision")
                  .message(
                      "Exception evaluating variability decision. error=%s",
                      ExceptionUtils.getStackTrace(e)));
      return false;
    }
  }
}
