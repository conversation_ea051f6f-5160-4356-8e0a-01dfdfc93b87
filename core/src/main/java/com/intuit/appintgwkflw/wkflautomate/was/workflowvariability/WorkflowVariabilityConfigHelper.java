package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;

import java.io.InputStream;
import java.util.Map;
import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

@Component
public class WorkflowVariabilityConfigHelper {

  private static final String FILE_PATH = "workflowVariability/workflowVariabilityConfig.json";
  private Map<String, WorkflowVariabilityRecordConfig> workflowVariabilityRecord;

  /** Load the config file */
  @PostConstruct
  public void initConfigMap() {
    try {
      InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(FILE_PATH);
      workflowVariabilityRecord =
          ObjectConverter.fromJsonStream(inputStream, new TypeReference<>() {});

      inputStream.close();
    } catch (Exception e) {
      throw new WorkflowGeneralException(e);
    }
  }

  /**
   * Get offeringType to workflows mapping key
   *
   * @param offering offeringType
   * @return value
   */
  public WorkflowVariabilityRecordConfig getOfferingWorkflowVariabilityRecords(String offering) {
    return workflowVariabilityRecord.getOrDefault(offering, null);
  }
}
