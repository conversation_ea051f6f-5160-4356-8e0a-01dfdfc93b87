<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1oo65x9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.13.0">
  <bpmn:process id="customApproval" name="Multi Condition Approval" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:startEvent id="startEvent" name="transaction custom event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{}" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[     {         &#34;variableName&#34;: &#34;entityChangeType&#34;,         &#34;variableType&#34;: &#34;String&#34;     },     {         &#34;variableName&#34;: &#34;Id&#34;,         &#34;variableType&#34;: &#34;String&#34;     },     {         &#34;variableName&#34;: &#34;intuit_userid&#34;,         &#34;variableType&#34;: &#34;String&#34;     },     {         &#34;variableName&#34;: &#34;intuit_realmid&#34;,         &#34;variableType&#34;: &#34;String&#34;     },         {         &#34;variableName&#34;: &#34;DocNumber&#34;,         &#34;variableType&#34;: &#34;string&#34;,         &#34;overrideIfAbsent&#34;: true     },         {         &#34;variableName&#34;: &#34;CompanyName&#34;,         &#34;variableType&#34;: &#34;string&#34;,         &#34;overrideIfAbsent&#34;: true     },         {         &#34;variableName&#34;: &#34;entityType&#34;,         &#34;variableType&#34;: &#34;String&#34;,         &#34;overrideIfAbsent&#34;: true     } ]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;,&#34;updated&#34;]" />
          <camunda:property name="ignoreNonEntityProcessVariablesDetails" value="true" />
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" sourceRef="startEvent" targetRef="businessRuleTask-1" />
    <bpmn:businessRuleTask id="businessRuleTask-1" name="Txn Rule Evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="custom-approvals">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"decision_customApproval","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585</bpmn:incoming>
      <bpmn:outgoing>Flow_05ykizl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cciuj0</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:callActivity id="sendForApproval" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:out source="" target="" variables="all" />
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05ykizl</bpmn:incoming>
      <bpmn:outgoing>Flow_1lcm64c</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_05ykizl" sourceRef="businessRuleTask-1" targetRef="sendForApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'act1234'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1cciuj0" name="Auto Approved" sourceRef="businessRuleTask-1" targetRef="endEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'false'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1lcm64c" sourceRef="sendForApproval" targetRef="endEvent" />
    <bpmn:endEvent id="endEvent" name="State change occured">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cciuj0</bpmn:incoming>
      <bpmn:incoming>Flow_1lcm64c</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0pqgwen" escalationRef="Escalation_211pfog" />
    </bpmn:endEvent>
    <bpmn:subProcess id="Activity_0rx7x5h" name="baseTemplateSubProcess" triggeredByEvent="true">
      <bpmn:startEvent id="Event_0r1g7mz" name="Close current process">
        <bpmn:outgoing>Flow_1bfgnpa</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0rasiag" escalationRef="Escalation_211pfog" camunda:escalationCodeVariable="close_parent" />
      </bpmn:startEvent>
      <bpmn:callActivity id="closeApproval" name="Close Approval" calledElement="closeApproval">
        <bpmn:extensionElements>
          <camunda:in businessKey="#{execution.processBusinessKey}" />
          <camunda:in variables="all" />
          <camunda:inputOutput>
            <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1bfgnpa</bpmn:incoming>
        <bpmn:outgoing>Flow_0ljpt75</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:endEvent id="Event_1xgfu8b" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ljpt75</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_15131hb" camunda:type="external" camunda:topic="custom-approvals" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1bfgnpa" sourceRef="Event_0r1g7mz" targetRef="closeApproval" />
      <bpmn:sequenceFlow id="Flow_0ljpt75" sourceRef="closeApproval" targetRef="Event_1xgfu8b" />
    </bpmn:subProcess>
  </bpmn:process>
  <bpmn:message id="Message_0oflesh" name="customWait" />
  <bpmn:escalation id="Escalation_025wo89" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_00owd9n" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04xqo3b" name="process_ended_message" />
  <bpmn:message id="Message_0j828dx" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_0d2bkim" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0ye464b" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0rubrjz" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04yyvx2" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_01zakh4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0hvjxp3" name="approved_rejected" />
  <bpmn:message id="Message_1r625qy" name="process_ended_message" />
  <bpmn:message id="Message_0tj20j4" name="custom_deleted" />
  <bpmn:escalation id="Escalation_1uy1le9" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_211pfog" name="close_parent" escalationCode="close_parent" />
  <bpmn:message id="Message_15131hb" name="process_ended_message" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customApproval">
      <bpmndi:BPMNShape id="Event_0u1c60a_di" bpmnElement="startEvent">
        <dc:Bounds x="172" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="315" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e0x7c8_di" bpmnElement="businessRuleTask-1">
        <dc:Bounds x="330" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11phofd_di" bpmnElement="sendForApproval">
        <dc:Bounds x="710" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1c9ytji_di" bpmnElement="endEvent">
        <dc:Bounds x="1092" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1078" y="305" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_0rx7x5h" isExpanded="true">
        <dc:Bounds x="530" y="490" width="350" height="200" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tkfy0j_di" bpmnElement="Event_0r1g7mz">
        <dc:Bounds x="570" y="572" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="556" y="615" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0iqmdk1_di" bpmnElement="closeApproval">
        <dc:Bounds x="645" y="550" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_1xgfu8b">
        <dc:Bounds x="802" y="572" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="789" y="615" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_1bfgnpa">
        <di:waypoint x="606" y="590" />
        <di:waypoint x="645" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_015hyoo_di" bpmnElement="Flow_0ljpt75">
        <di:waypoint x="745" y="590" />
        <di:waypoint x="802" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zrvk3w_di" bpmnElement="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
        <di:waypoint x="208" y="290" />
        <di:waypoint x="330" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05ykizl_di" bpmnElement="Flow_05ykizl">
        <di:waypoint x="430" y="290" />
        <di:waypoint x="710" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cciuj0_di" bpmnElement="Flow_1cciuj0">
        <di:waypoint x="380" y="250" />
        <di:waypoint x="380" y="100" />
        <di:waypoint x="1110" y="100" />
        <di:waypoint x="1110" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="82" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lcm64c_di" bpmnElement="Flow_1lcm64c">
        <di:waypoint x="810" y="290" />
        <di:waypoint x="1092" y="290" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>