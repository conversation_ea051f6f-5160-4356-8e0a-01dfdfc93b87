{"header": {"name": "custom-reminder-start-process [v22]", "status": "success", "message": ""}, "success": "true", "error": "", "Output": [{"TxnApprovalDays": "", "Customer": "1", "TxnPaymentStatus": "UNPAID", "TxnAmount": "1111.00", "CompanyName": "TEST-COMPANY", "VendorEmail": "<EMAIL>", "CustomerEmail": "<EMAIL>", "DocNumber": "1005", "CustomerName": "<PERSON>", "TxnExpirationDays": "", "TxnApprovalStatus": "", "TxnEmailAvailabilityStatus": "AVAILABLE", "VendorName": "", "TxnSendDays": "", "TxnDate": "2022-07-23", "CompanyEmail": "<EMAIL>", "TxnExpirationDate": "", "Term": "3", "Vendor": "", "TxnDueDays": "0", "TxnBalanceAmount": "1111.00", "SyncToken": "2", "TxnCreateDays": "0", "TxnDueDate": "2022-08-21", "Id": "1", "TxnStatus": "", "TxnDays": "29", "Location": "", "TxnUpdateStatus": "UPDATED", "TxnSendStatus": "UNSENT"}, {"TxnApprovalDays": "", "Customer": "1", "TxnPaymentStatus": "UNPAID", "TxnAmount": "1111.00", "CompanyName": "TEST-COMPANY", "VendorEmail": "<EMAIL>", "CustomerEmail": "<EMAIL>", "DocNumber": "1005", "CustomerName": "<PERSON>", "TxnExpirationDays": "", "TxnApprovalStatus": "", "TxnEmailAvailabilityStatus": "AVAILABLE", "VendorName": "", "TxnSendDays": "", "TxnDate": "2022-07-23", "CompanyEmail": "<EMAIL>", "TxnExpirationDate": "", "Term": "3", "Vendor": "", "TxnDueDays": "0", "TxnBalanceAmount": "1111.00", "SyncToken": "2", "TxnCreateDays": "0", "TxnDueDate": "2022-08-21", "Id": "2", "TxnStatus": "", "TxnDays": "29", "Location": "", "TxnUpdateStatus": "UPDATED", "TxnSendStatus": "UNSENT"}]}