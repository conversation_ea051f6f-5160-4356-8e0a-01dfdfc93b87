{"IsMobile": {"notificationName": "InvoiceApproval", "notificationDataType": "InvoiceApproval", "mappingAttributes": {"NotificationAction": ["action"], "Subject": ["title"], "Message": ["subject"]}}, "IsEmail": {"notificationName": "testOINPSks", "notificationDataType": "test2", "mappingAttributes": {"To": ["To"], "CC": ["CC"], "BCC": ["BCC"], "CompanyName": ["FromName", "ReplyToName"], "CompanyEmail": ["FromAddress", "ReplyToEmail"], "Subject": ["Subject"], "Message": ["Message"]}}, "consolidateNotifications": {"notificationName": "customWorkflow", "notificationDataType": "customWorkflowConsolidated", "mappingAttributes": {"To": ["To"], "CC": ["CC"], "BCC": ["BCC"], "CompanyName": ["FromName", "ReplyToName"], "CompanyEmail": ["FromAddress", "ReplyToEmail"], "Subject": ["Subject"], "Message": ["Message"], "TxnAmount": ["Amount"], "RecordType": ["RecordType"], "TxnId": ["TxnId"], "TxnDate": ["TxnDate"], "TxnDueDate": ["DueDate"], "CustomerName": ["CustomerName"], "TxnDueDays": ["TxnDueDays"], "TxnStatus": ["TxnStatus"], "TxnSendStatus": ["TxnSendStatus"], "TxnPaymentStatus": ["TxnPaymentStatus"], "TxnExpirationDate": ["TxnExpirationDate"], "DocNumber": ["DocNumber"], "VendorName": ["VendorName"]}}, "taskcustomreminder_IsEmail": {"notificationName": "testTaskOinp", "notificationDataType": "testTaskOinp", "mappingAttributes": {"To": ["To"], "CC": ["CC"], "BCC": ["BCC"], "CompanyName": ["FromName", "ReplyToName"], "CompanyEmail": ["FromAddress", "ReplyToEmail"], "Subject": ["Subject"], "Message": ["Message"], "Id": ["TaskId"], "TaskType": ["TaskType"], "TaskName": ["TaskName"], "Assignee": ["TaskAssignee"], "TxnDueDate": ["TaskDueDate"]}}}