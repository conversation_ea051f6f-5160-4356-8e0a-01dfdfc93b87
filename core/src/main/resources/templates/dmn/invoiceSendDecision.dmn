<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" id="Definitions_1qui2om" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.2.3">
  <decision id="invoiceSendDecision" name="invoiceSendDecision">
    <extensionElements>
      <biodi:bounds x="157" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="amountEvaluation">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${invoice.amount}</text>
        </inputExpression>
      </input>
      <input id="InputClause_13q46an" label="createdByUsers">
        <inputExpression id="LiteralExpression_153c9i7" typeRef="string">
          <text>${ invoice.createdByUsers }</text>
        </inputExpression>
      </input>
      <input id="InputClause_10vl7tp" label="discountEvaluation">
        <inputExpression id="LiteralExpression_10svu8w" typeRef="double">
          <text>${ invoice.discountPercent }</text>
        </inputExpression>
      </input>
      <output id="output_1" label="sendAdminEmail" name="sendAdminEmail" typeRef="boolean" />
      <output id="OutputClause_0qnh9m8" label="sendAdminNotification" name="sendAdminNotification" typeRef="boolean" />
      <output id="OutputClause_18bcki6" label="updateInvoiceApproved" name="updateInvoiceApproved" typeRef="boolean" />
      <rule id="DecisionRule_0wnu7zh">
        <inputEntry id="UnaryTests_024suza">
          <text> &gt; 100</text>
        </inputEntry>
        <inputEntry id="UnaryTests_0kaaftn">
          <text>"nsehgal"</text>
        </inputEntry>
        <inputEntry id="UnaryTests_0k8lmbl">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_08atpch">
          <text>true</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0b4e7gn">
          <text>false</text>
        </outputEntry>
        <outputEntry id="LiteralExpression_0pl8bm4">
          <text>true</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
