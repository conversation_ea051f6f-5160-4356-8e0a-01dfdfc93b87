[{"name": "E-Mail", "id": "com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegate", "appliesTo": ["bpmn:ServiceTask"], "properties": [{"label": "Implementation Type", "type": "String", "value": "com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.CamundaTaskDelegate", "editable": false, "binding": {"type": "property", "name": "camunda:class"}}, {"label": "SCHEMA", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "schema"}, "constraints": {"notEmpty": true}, "value": "ACTION_ID,ACTION_URL,QUERY_Token~QUERY_To,QUERY_Subject~OUTPUT_response", "description": "name of id to be called"}, {"label": "ID", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "ACTION_ID"}, "constraints": {"notEmpty": true}, "value": "sendEmail", "description": "name of id to be called"}, {"label": "URL", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "ACTION_URL"}, "constraints": {"notEmpty": true}, "value": "https://stage.connector.appconnect.intuit.com/504916/api/send-html-email.json", "description": "REST URL"}, {"label": "To", "type": "String", "value": "${appConnect_sendAdminMail.to}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_To"}, "constraints": {"notEmpty": true}, "description": "receiver email address", "InputType": "blah"}, {"label": "Token", "type": "String", "value": "k1n4k0di2rcy2jd", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Token"}, "description": "Token Details", "constraints": {"notEmpty": true}}, {"label": "Subject", "type": "String", "value": "${appConnect_sendAdminMail.subject}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Subject"}, "constraints": {"notEmpty": true}, "description": "Subject line of email"}, {"label": "Message", "description": "message body of email.You can use freemarker tags as well.", "value": "&lt;html&gt;\n            &lt;p&gt;Hi,&lt;/p&gt;\n            &lt;p&gt;Approve or reject the estimate with below details &lt;/p&gt;\n            &lt;p&gt;--------------------------------------------------&lt;/p&gt;\n            &lt;p&gt;Id       - ${id?c}&lt;/p&gt;\n            &lt;p&gt;Amount   - ${amount}&lt;/p&gt;\n            &lt;p&gt;Quantity - ${quantity}&lt;/p&gt;\n            &lt;p&gt;Date     - ${estimateDate?date}&lt;/p&gt;\n            &lt;p&gt;--------------------------------------------------&lt;/p&gt;\n            \n            &lt;table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"&gt;\n              &lt;tr&gt;\n                  &lt;td&gt;\n                      &lt;table cellspacing=\"0\" cellpadding=\"0\"&gt;\n                          &lt;tr&gt;\n                              &lt;td style=\"border-radius: 2px;\" bgcolor=\"#008000\" padding: 10px&gt;\n                                  &lt;a href=https://qboavworkflow-qal.api.intuit.com/v1/redirect?&amp;isApproved=true&amp;estimateId=${id?c}   target=\"_blank\" style=\"padding: 8px 12px; border: 1px solid #008000;border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;\"&gt;\n                                      Approve\n                                  &lt;/a&gt;\n                              &lt;/td&gt;\n                              &lt;td padding: 10px&gt; &lt;a style=\"padding: 8px 12px;  border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;\"&gt;\n                              &lt;/a&gt;&lt;/td&gt;\n                              &lt;td style=\"border-radius: 2px;\" bgcolor=\"#ED2939\" padding: 10px&gt;\n                                  &lt;a href=https://qboavworkflow-qal.api.intuit.com/v1/redirect?&amp;isApproved=false&amp;estimateId=${id?c}  target=\"_blank\" style=\"padding: 8px 12px; border: 1px solid #ED2939;border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;\"&gt;\n                                      Reject             \n                                  &lt;/a&gt;\n                              &lt;/td&gt;\n                          &lt;/tr&gt;\n                      &lt;/table&gt;\n                  &lt;/td&gt;\n              &lt;/tr&gt;\n            &lt;/table&gt;\n&lt;/html&gt;", "type": "Text", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Message", "scriptFormat": "freemarker"}, "constraints": {"notEmpty": true}}, {"label": "OUTPUT_response", "description": "The process variable to which to assign the send result to", "type": "String", "value": "OUTPUT_response", "binding": {"type": "camunda:outputParameter", "source": "${appConnect_sendAdminMail.response}"}}]}, {"name": "Update-Invoice", "id": "com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegate.invoice", "appliesTo": ["bpmn:ServiceTask"], "properties": [{"label": "Implementation Type", "type": "String", "value": "com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.CamundaTaskDelegate", "editable": false, "binding": {"type": "property", "name": "camunda:class"}}, {"label": "SCHEMA", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "schema"}, "constraints": {"notEmpty": true}, "value": "ACTION_ID,ACTION_URL,QUERY_Token~QUERY_Invoice_Id,QUERY_Memo_Action,QUERY_Memo,QUERY_Ignore_If~OUTPUT_response", "description": "name of id to be called"}, {"label": "ACTION_ID", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "ACTION_ID"}, "constraints": {"notEmpty": true}, "value": "updateInvoice", "description": "name of id to be called"}, {"label": "ACTION_URL", "type": "String", "binding": {"type": "camunda:inputParameter", "name": "ACTION_URL"}, "constraints": {"notEmpty": true}, "value": "https://stage.connector.appconnect.intuit.com/504916/api/send-html-email.json", "description": "REST URL"}, {"label": "Token", "type": "String", "value": "k1n4k0di2rcy2jd", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Token"}, "description": "Token Details", "constraints": {"notEmpty": true}}, {"label": "Invoice_Id", "type": "String", "value": "${appConnect_updateInvoice.id}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Invoice_Id"}, "constraints": {"notEmpty": true}, "description": "Invoice Id"}, {"label": "Memo_Action", "type": "String", "value": "${appConnect_updateInvoice.memoAction}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Memo_Action"}, "description": "Memo Action", "constraints": {"notEmpty": true}}, {"label": "Memo", "type": "String", "value": "${appConnect_updateInvoice.memo}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Memo"}, "constraints": {"notEmpty": true}, "description": "Memo details"}, {"label": "Ignore_If", "type": "String", "value": "${appConnect_updateInvoice.ignore}", "binding": {"type": "camunda:inputParameter", "name": "QUERY_Ignore_If"}, "description": "Memo Action", "constraints": {"notEmpty": true}}, {"label": "response", "description": "The process variable to which to assign the response result to", "type": "String", "value": "OUTPUT_response", "binding": {"type": "camunda:outputParameter", "source": "${appConnect_updateInvoice.response}"}}]}]