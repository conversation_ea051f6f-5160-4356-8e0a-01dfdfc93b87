package com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations;

import com.intuit.appintgwkflw.wkflautomate.was.core.cache.schema.EnabledDefinitionDetails;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.junit.Assert;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;


@RunWith(MockitoJUnitRunner.class)
public class RedissonClientOperationImplTest {

    @Mock
    private RedissonClient redissonClient;
    @InjectMocks
    private RedissonClientOperationImpl redissonClientOperation;

    private static final String serializedEnabledDefinitionDetails = "{\"definitionKeys\":[\"customReminder_9130352403806696_e3c08f93-7c1f-448a-942a-1624871ce648\"]}";

    @Test
    public void testIsFieldPresentInHashWhenFieldIsPresent() {
        Map<String, String> hashObject = new HashMap<>();
        hashObject.put("field1", "value1");
        assertTrue(redissonClientOperation.containsKeyInMap(hashObject, "field1"));
    }

    // 2. Field is not present in the hash
    @Test
    public void testIsFieldPresentInHashWhenFieldIsNotPresent() {
        Map<String, String> hashObject = new HashMap<>();
        hashObject.put("field1", "value1");
        assertFalse(redissonClientOperation.containsKeyInMap(hashObject, "field2"));
    }

    @Test
    public void testIsFieldPresentInHashWhenHashIsEmpty() {
        Map<String, String> hashObject = new HashMap<>();
        assertFalse(redissonClientOperation.containsKeyInMap(hashObject, "field1"));
    }


    @Test
    public void testGetFieldValueFromHashWhenRightTypeIsPassed() {
        Map<String, String> hashObject = new HashMap<>();
        hashObject.put("field1", serializedEnabledDefinitionDetails);
        EnabledDefinitionDetails ed = redissonClientOperation.getValueFromMap(hashObject, "field1", EnabledDefinitionDetails.class);
        ed.getDefinitionKeys()
                .stream()
                .forEach(
                    dk -> {
                        if(!dk.equals("customReminder_9130352403806696_e3c08f93-7c1f-448a-942a-1624871ce648")){
                            Assert.fail();
                        }
                    }
                );
    }

    @Test
    public void testGetFieldValueFromHashWhenRightTypeIsNotPassed() {
        Map<String, String> hashObject = new HashMap<>();
        hashObject.put("field1", "value1");
        assertNull(redissonClientOperation.getValueFromMap(hashObject, "field1", EnabledDefinitionDetails.class));
    }

}
