package com.intuit.appintgwkflw.wkflautomate.was.core.cache.service;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DummyEnabledDefinitionCacheServiceImplTest {
    @Mock
    private CustomWorkflowQueryCapability customWorkflowQueryCapability;
    @InjectMocks
    private DummyEnabledDefinitionCacheServiceImpl dummyEnabledDefinitionCacheService;

    @Test
    public void testUpdateCache() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        dummyEnabledDefinitionCacheService.updateCacheWithDefinitionDetails(definitionDetails);
        Mockito.verify(customWorkflowQueryCapability, Mockito.times(0)).isCustomWorkflow(Mockito.anyString(), Mockito.any());
    }
}
