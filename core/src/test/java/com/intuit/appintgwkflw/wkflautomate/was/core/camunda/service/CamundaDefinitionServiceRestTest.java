package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static org.mockito.ArgumentMatchers.any;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CamundaRestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDefinitionKeyRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.v4.Authorization;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@RunWith(MockitoJUnitRunner.class)
public class CamundaDefinitionServiceRestTest {

  private static final String INVOICE_APPROVAL_BPMN =
      "src/test/resources/bpmn/InvoiceApproval.bpmn";
  private static final String INVOICE_APPROVAL_DMN =
      "src/test/resources/dmn/invoiceSendDecision.dmn";
  
  @InjectMocks private CamundaDefinitionServiceRest camundaDefinitionServiceRest;
  @Mock private CamundaWASClient httpClient;
  @Mock private WorkflowCoreConfig workflowCoreConfig;
  @Mock private WASContextHandler contextHandler;
  @Mock private OfflineTicketClient offlineTicketClient;
  @Mock private CamundaRestUtil camundaRestUtil;

  @Test
  public void testDeployBPMNWithoutDMNSuccess() {
    List<File> dmnDefinitionFileList = new ArrayList<>();
    DeployDefinition deployDefinition =
        new DeployDefinition(
            dmnDefinitionFileList, new File(INVOICE_APPROVAL_BPMN), null, String.class,
            "tId_ownerId_1");
    MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("deployment-name", "tId_ownerId_1");
    requestBody.add(
        "InvoiceApproval.bpmn", new FileSystemResource(new File(INVOICE_APPROVAL_BPMN)));
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
    HttpEntity<MultiValueMap<String, Object>> requestEntity =
        new HttpEntity<>(requestBody, requestHeaders);
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointDefinitionDeployment())
        .thenReturn("/testDeploy");
    Mockito.when(httpClient.postResponse("testPrefix/testDeploy", requestEntity, String.class))
        .thenReturn(
            WASHttpResponse.<String>builder().status(HttpStatus.OK).isSuccess2xx(true).build());
   
    WASHttpResponse<String> response =
        camundaDefinitionServiceRest.deployDefinition(deployDefinition);
    Assert.assertEquals(HttpStatus.OK, response.getStatus());
  }

  @Test
  public void testDeployBPMAndDMNSuccess() {
    List<File> dmnDefinitionFileList = new ArrayList<>();
    dmnDefinitionFileList.add(new File(INVOICE_APPROVAL_DMN));
    DeployDefinition deployDefinition =
        new DeployDefinition(
            dmnDefinitionFileList, new File(INVOICE_APPROVAL_BPMN), null, String.class,
            "tId_ownerId_1");
    MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("deployment-name", "tId_ownerId_1");
    requestBody.add(
        "InvoiceApproval.bpmn", new FileSystemResource(new File(INVOICE_APPROVAL_BPMN)));
    requestBody.add(
        "invoiceSendDecision.dmn", new FileSystemResource(new File(INVOICE_APPROVAL_DMN)));
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
    HttpEntity<MultiValueMap<String, Object>> requestEntity =
        new HttpEntity<>(requestBody, requestHeaders);
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointDefinitionDeployment())
        .thenReturn("/testDeploy");
    Mockito.when(httpClient.postResponse("testPrefix/testDeploy", requestEntity, String.class))
        .thenReturn(
            WASHttpResponse.<String>builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    WASHttpResponse<String> response =
        camundaDefinitionServiceRest.deployDefinition(deployDefinition);
    Assert.assertEquals(HttpStatus.OK, response.getStatus());
  }

  @Test
  public void testUpdateProcessDefinitionStatus() {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("process-definition");
    Mockito.when(workflowCoreConfig.getRestEndpointSuspendInstance()).thenReturn("suspended");

    final Map<String, Object> requestBody = new HashMap<>();
    requestBody.put(CamundaRestConstants.SUSPENDED, false);
    requestBody.put(CamundaRestConstants.INCLUDE_PROCESS_INSTANCES, false);
    Mockito.when(httpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.<String>builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    camundaDefinitionServiceRest.updateProcessDefinitionStatus("definitionId", false);
    Mockito.verify(httpClient).httpResponse(any(WASHttpRequest.class));
  }

  @Test
  public void testdeleteDefinitionByDefinitionId() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("process-definition");

    WASHttpResponse<Object> response = WASHttpResponse.builder().isSuccess2xx(true).build();
    Mockito.when(httpClient.httpResponse(Mockito.any())).thenReturn(response);
    try {
      camundaDefinitionServiceRest.deleteDefinition(
          new DeleteDeploymentRequest("defId", true, true));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testdeleteDeployment() {
    Mockito.when(workflowCoreConfig.getRestEndpointDefinitionDeploymentCrud())
        .thenReturn("deployment");

    WASHttpResponse<Object> response = WASHttpResponse.builder().isSuccess2xx(true).build();
    Mockito.when(httpClient.httpResponse(Mockito.any())).thenReturn(response);
    try {
      camundaDefinitionServiceRest.deleteDeployment(
          new DeleteDeploymentRequest("defId", true, true));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteDefinitionByKey() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("/process-definition");
    Mockito.when(workflowCoreConfig.getProcessKey()).thenReturn("/key");
    Mockito.when(workflowCoreConfig.getDeleteEndPoint()).thenReturn("/delete");

    final Map<String, Object> requestBody = new HashMap<>();
    requestBody.put(CamundaRestConstants.SUSPENDED, false);
    requestBody.put(CamundaRestConstants.INCLUDE_PROCESS_INSTANCES, false);

    Mockito.when(httpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.<String>builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    DeleteDefinitionKeyRequest deleteDefinitionKeyRequest =
        new DeleteDefinitionKeyRequest("defKey", true, true);
    try {
      camundaDefinitionServiceRest.deleteDefinitionByKey(deleteDefinitionKeyRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteDefinitionByKeyOffline() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("/process-definition");
    Mockito.when(workflowCoreConfig.getProcessKey()).thenReturn("/key");
    Mockito.when(workflowCoreConfig.getDeleteEndPoint()).thenReturn("/delete");

    final Map<String, Object> requestBody = new HashMap<>();
    requestBody.put(CamundaRestConstants.SUSPENDED, false);
    requestBody.put(CamundaRestConstants.INCLUDE_PROCESS_INSTANCES, false);

    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);

    Mockito.when(httpClient.httpResponse(any(WASHttpRequest.class)))
        .thenReturn(
            WASHttpResponse.<String>builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    DeleteDefinitionKeyRequest deleteDefinitionKeyRequest =
        new DeleteDefinitionKeyRequest("defKey", true, true);
    try {
      camundaDefinitionServiceRest.deleteDefinitionByKey(deleteDefinitionKeyRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteDefinitionByKeyException() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("/process-definition");
    Mockito.when(workflowCoreConfig.getProcessKey()).thenReturn("/key");
    Mockito.when(workflowCoreConfig.getDeleteEndPoint()).thenReturn("/delete");

    WASHttpResponse<Object> response = WASHttpResponse.builder().isSuccess2xx(false).build();
    Mockito.when(httpClient.httpResponse(Mockito.any())).thenReturn(response);
    camundaDefinitionServiceRest.deleteDefinitionByKey(
        new DeleteDefinitionKeyRequest("defKey", true, true));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testdeleteDefinitionByDefinitionIdException() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("process-definition");

    WASHttpResponse<Object> response = WASHttpResponse.builder().isSuccess2xx(false).build();
    Mockito.when(httpClient.httpResponse(Mockito.any())).thenReturn(response);
    camundaDefinitionServiceRest.deleteDefinition(new DeleteDeploymentRequest("defId", true, true));
  }

  @Test
  public void testgetBPMNXMLDefinition() {
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("prefix/");
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("process-definition");

    WASHttpResponse<BpmnResponse> response =
        WASHttpResponse.<BpmnResponse>builder().isSuccess2xx(true).build();
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);
    Mockito.when(
            httpClient.getResponse(
                "prefix/process-definition/defId/xml", requestEntity, BpmnResponse.class))
        .thenReturn(response);

    WASHttpResponse<BpmnResponse> resp = camundaDefinitionServiceRest.getBPMNXMLDefinition("defId");
    Assert.assertNotNull(resp);
  }

  @Test
  public void testgetDMNXMLDefinition() {
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("prefix/");
    Mockito.when(workflowCoreConfig.getRestEndpointDecisionDefinition())
        .thenReturn("decision-definition");

    WASHttpResponse<DmnResponse> response =
        WASHttpResponse.<DmnResponse>builder().isSuccess2xx(true).build();
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);
    Mockito.when(
            httpClient.getResponse(
                "prefix/decision-definition/defId/xml", requestEntity, DmnResponse.class))
        .thenReturn(response);

    WASHttpResponse<DmnResponse> resp = camundaDefinitionServiceRest.getDMNXMLDefinition("defId");
    Assert.assertNotNull(resp);
  }

  @Test
  public void testGetDeploymentDetails() {
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("prefix/");
    Mockito.when(workflowCoreConfig.getRestEndpointProcessDefinition())
        .thenReturn("deployment");

    WASHttpResponse<DeploymentResponse> response =
        WASHttpResponse.<DeploymentResponse>builder().isSuccess2xx(true).build();
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);

    Mockito.when(
            httpClient.getResponse(
                "prefix/deployment/defId", requestEntity, DeploymentResponse.class))
        .thenReturn(response);

    WASHttpResponse<DeploymentResponse> resp = camundaDefinitionServiceRest.getDeploymentDetails("defId");
    Assert.assertNotNull(resp);
    Assert.assertTrue(resp.isSuccess2xx());
  }
}
