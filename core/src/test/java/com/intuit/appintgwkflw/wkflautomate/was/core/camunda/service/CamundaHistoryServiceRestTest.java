package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowHistoryResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.GetProcessDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;

@RunWith(MockitoJUnitRunner.class)
public class CamundaHistoryServiceRestTest {

  private static final String PROCESS_INSTANCE_ID = UUID.randomUUID().toString();

  @Mock private CamundaWASClient camundaWASClient;
  @Mock private WorkflowCoreConfig workflowCoreConfig;
  @Mock private CamundaRestUtil camundaRestUtil;
  @Mock private OfflineTicketClient offlineTicketClient;
  @InjectMocks private CamundaHistoryServiceRest camundaHistoryServiceRest;

  @Test(expected = WorkflowGeneralException.class)
  public void whenGetProcessDetails_andCamundaReturnsError_thenThrowException() {

    doReturn(WASHttpResponse.builder().isSuccess2xx(false).build())
        .when(camundaWASClient).getResponse(anyString(), any(HttpEntity.class), any());

    camundaHistoryServiceRest.getProcessDetails(new GetProcessDetailsRequest(PROCESS_INSTANCE_ID));
  }

  @Test
  public void whenGetProcessDetails_thenSuccess() {

    doReturn(WASHttpResponse.builder().isSuccess2xx(true).build())
        .when(camundaWASClient).getResponse(anyString(), any(HttpEntity.class), any());

    final WASHttpResponse<WorkflowHistoryResponse> response = camundaHistoryServiceRest
        .getProcessDetails(new GetProcessDetailsRequest(PROCESS_INSTANCE_ID));

    assertNotNull(response);
  }
  
	@Test(expected = WorkflowGeneralException.class)
	public void tesGetProcessVariableDetails_Exception() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/variable-instance").when(workflowCoreConfig).getHistoryEndpoint();
		
		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();
		
		doReturn(WASHttpResponse.builder().isSuccess2xx(false).build()).when(camundaWASClient).httpResponse(any());

		camundaHistoryServiceRest.getProcessVariableDetails(new ProcessVariableDetailsRequest());
	}

	@Test
	public void tesGetProcessVariableDetails_Success() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/variable-instance").when(workflowCoreConfig).getHistoryEndpoint();
		
		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();

		doReturn(WASHttpResponse.builder().isSuccess2xx(true).build()).when(camundaWASClient).httpResponse(any());

		final WASHttpResponse<List<ProcessVariableDetailsResponse>> response = camundaHistoryServiceRest
				.getProcessVariableDetails(new ProcessVariableDetailsRequest());

		assertNotNull(response);
	}
	
	
	@Test(expected = WorkflowGeneralException.class)
	public void testGetExternalTaskLogs_Exception() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/history").when(workflowCoreConfig).getHistoryEndpoint();
		
		doReturn("/external-task-log").when(workflowCoreConfig).getExternalTaskLogHistoryEndpoint();
		
		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();
		
		doReturn(WASHttpResponse.builder().isSuccess2xx(false).build()).when(camundaWASClient).httpResponse(any());

		camundaHistoryServiceRest.getExternalTaskLogs(ExternalTaskLogRequest
				.builder().externalTaskId("extId").maxResults(1).build());
	}

	@Test
	public void testGetExternalTaskLogs_Success() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/history").when(workflowCoreConfig).getHistoryEndpoint();
		
		doReturn("/external-task-log").when(workflowCoreConfig).getExternalTaskLogHistoryEndpoint();
		
		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();

		doReturn(WASHttpResponse.builder().isSuccess2xx(true).build()).when(camundaWASClient).httpResponse(any());

		final WASHttpResponse<List<ExternalTaskLog>> response = camundaHistoryServiceRest
				.getExternalTaskLogs(ExternalTaskLogRequest
						.builder().externalTaskId("extId").maxResults(1).build());

		assertNotNull(response);
	}

	@Test(expected = WorkflowGeneralException.class)
	public void testGetExternalTaskCount_Exception() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/history").when(workflowCoreConfig).getHistoryEndpoint();

		doReturn("/external-task-log/count").when(workflowCoreConfig).getExternalTaskLogCountHistoryEndpoint();

		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();

		doReturn(WASHttpResponse.builder().isSuccess2xx(false).build()).when(camundaWASClient).httpResponse(any());

		camundaHistoryServiceRest.getExternalTaskCount(ExternalTaskLogRequest
				.builder().activityIdIn(Arrays.asList("activityId")).creationLog(true).processInstanceId("processId").build());
	}

	@Test
	public void testGetExternalTaskCount_Success() {

		doReturn("http://localhost:8080").when(camundaRestUtil).getCamundaBaseURL();

		doReturn("/history").when(workflowCoreConfig).getHistoryEndpoint();

		doReturn("/external-task-log/count").when(workflowCoreConfig).getExternalTaskLogCountHistoryEndpoint();

		doReturn("AUTH").when(offlineTicketClient).getSystemOfflineHeadersForOfflineJob();

		doReturn(WASHttpResponse.builder().isSuccess2xx(true).build()).when(camundaWASClient).httpResponse(any());

		final WASHttpResponse<Map<String, Integer>> response = camundaHistoryServiceRest.getExternalTaskCount(ExternalTaskLogRequest
				.builder().activityIdIn(Arrays.asList("activityId")).creationLog(true).processInstanceId("processId").build());

		assertNotNull(response);
	}
}