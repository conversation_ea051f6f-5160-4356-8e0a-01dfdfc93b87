package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.CamundaRequestResponseLoggerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.ServiceTaskCompleteRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowErrorDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskFailure;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskSuccess;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.StartProcessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.Variable;
import com.intuit.v4.Authorization;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.simple.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;

@Import(CamundaRunTimeServiceRest.class)
@RunWith(SpringRunner.class)
public class CamundaRunTimeServiceRestTest {

  private static final String MISMATCH_EXCEPTION =
      "[{\"type\":\"RestException\""
          + ",\"message\":\"org.camunda.bpm.engine.MismatchingMessageCorrelationException: "
          + "Cannot correlate message 'approved_rejected': No process definition or execution matches the parameters\"}]";
  @Autowired private CamundaRunTimeServiceRest camundaRunTimeServiceRest;
  @MockBean private CamundaWASClient httpClient;
  @MockBean private OfflineTicketClient offlineTicketClient;
  @MockBean private WASContextHandler contextHandler;
  @MockBean private WorkflowCoreConfig workflowCoreConfig;
  @MockBean private CamundaRestUtil camundaRestUtil;
  @MockBean private CamundaRequestResponseLoggerConfig camundaRequestResponseLoggerConfig;

  @Test
  public void correlateMessageWithAllData() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());
    Assert.assertTrue(camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage));
  }

  @Test(expected = WorkflowRetriableException.class)
  public void correlateMessageWithAllData_RetryException() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .error(MISMATCH_EXCEPTION)
                .build());
    camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage);
  }

  @Test
  public void correlateMessageWithAllData_BadRequest() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.BAD_REQUEST)
                .isSuccess2xx(false)
                .error(MISMATCH_EXCEPTION)
                .build());
    try {
      camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage);
      Assert.fail();
    } catch (WorkflowRetriableException e) {
      Assert.assertEquals(WorkflowError.TRIGGER_PROCES_DEFINITION_ERROR, e.getWorkflowError());
      Assert.assertEquals(HttpStatus.BAD_REQUEST, e.getWorkflowError().getStatus());
    }
  }

  @Test
  public void correlateMessageWithAllData_TriggerSignalProcessError() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("Some Error Occured")
                .build());
    try {
      camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR, e.getWorkflowError());
      Assert.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, e.getWorkflowError().getStatus());
    }
  }

  @Test
  public void correlateMessageWithAllData_BadRequestWithNonMismatchrror() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.BAD_REQUEST)
                .isSuccess2xx(false)
                .error("Some Other Error Occured")
                .build());
    try {
      camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR, e.getWorkflowError());
      Assert.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, e.getWorkflowError().getStatus());
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void correlateMessageWithAllData_Exception() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .error("123")
                .build());
    camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage);
  }

  @Test
  public void testServiceTaskWithAllData() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    ServiceTaskCompleteRequest serviceTaskCompleteRequest =
            ServiceTaskCompleteRequest.builder()
                    .executionId("testExecutionId")
                    .failed(false)
                    .payload(testVariableMap)
                    .build();
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getServiceTask()).thenReturn("/testServiceTask");
    Mockito.when(httpClient.httpResponse(any()))
            .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true)
                    .response(WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
                            .errorDetails(WorkflowErrorDetails.builder().build()).build()).build());
    camundaRunTimeServiceRest.messageServiceTask(serviceTaskCompleteRequest);
    Mockito.verify(httpClient).httpResponse(any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testServiceTaskHTTPSFailure() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    ServiceTaskCompleteRequest serviceTaskCompleteRequest =
            ServiceTaskCompleteRequest.builder()
                    .executionId("testExecutionId")
                    .failed(false)
                    .payload(testVariableMap)
                    .build();
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getServiceTask()).thenReturn("/testServiceTask");
    Mockito.when(httpClient.httpResponse(any()))
            .thenReturn(WASHttpResponse.builder().status(HttpStatus.BAD_REQUEST).isSuccess2xx(false)
                    .response(WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
                            .errorDetails(WorkflowErrorDetails.builder().build()).build()).build());
    camundaRunTimeServiceRest.messageServiceTask(serviceTaskCompleteRequest);
    Mockito.verify(httpClient).httpResponse(any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testServiceTaskFailure() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    ServiceTaskCompleteRequest serviceTaskCompleteRequest =
            ServiceTaskCompleteRequest.builder()
                    .executionId("testExecutionId")
                    .failed(false)
                    .payload(testVariableMap)
                    .build();
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getServiceTask()).thenReturn("/testServiceTask");
    Mockito.when(httpClient.httpResponse(any()))
            .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true)
                    .response(WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE)
                            .errorDetails(WorkflowErrorDetails.builder().build()).build()).build());
    camundaRunTimeServiceRest.messageServiceTask(serviceTaskCompleteRequest);
    Mockito.verify(httpClient).httpResponse(any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testStartProcessException() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    JSONObject requestBody = new JSONObject();
    requestBody.put("definitionId", "def-id");
    requestBody.put("processVariables", testVariableMap);
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    StartProcessRequest startProcessRequest = new StartProcessRequest("def-id", "1234", variables);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix())
        .thenReturn(startProcessRequest.getDefinitionId());
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/start");
    Map<String, Object> responseMap = new HashMap<>();
    // Response has no variables returned. Process start will fail.
    responseMap.put(WorkflowConstants.BPMN_DMN_VARIABLES, null);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(responseMap)
                .build());
    camundaRunTimeServiceRest.startProcess(startProcessRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testStartProcessFailure() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    JSONObject requestBody = new JSONObject();
    requestBody.put("messageName", "testMessage");
    requestBody.put("processInstanceId", "testProcessInstanceId");
    requestBody.put("processVariables", testVariableMap);
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    StartProcessRequest startProcessRequest = new StartProcessRequest("def-id", "1234", variables);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix())
        .thenReturn(startProcessRequest.getDefinitionId());
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/start");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(false).build());
    camundaRunTimeServiceRest.startProcess(startProcessRequest);
  }

  @Test
  public void testEvaluateDecision() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    JSONObject requestBody = new JSONObject();
    requestBody.put("definitionId", "def-id");
    requestBody.put("processVariables", testVariableMap);
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    EvaluateRuleRequest evaluateRuleRequest = new EvaluateRuleRequest("def-id", variables);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix())
        .thenReturn(evaluateRuleRequest.getDefinitionId());
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/evaluate");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    try {
      camundaRunTimeServiceRest.evaluateDecision(evaluateRuleRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEvaluateDecisionFailure() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    JSONObject requestBody = new JSONObject();
    requestBody.put("definitionId", "def-id");
    requestBody.put("processVariables", testVariableMap);
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    EvaluateRuleRequest evaluateRuleRequest = new EvaluateRuleRequest("def-id", variables);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix())
        .thenReturn(evaluateRuleRequest.getDefinitionId());
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/evaluate");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(false).build());
    camundaRunTimeServiceRest.evaluateDecision(evaluateRuleRequest);
  }

  @Test
  public void testStartProcess() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    JSONObject requestBody = new JSONObject();
    requestBody.put("definitionId", "def-id");
    requestBody.put("processVariables", testVariableMap);
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    StartProcessRequest startProcessRequest = new StartProcessRequest("def-id", "1234", variables);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix())
        .thenReturn(startProcessRequest.getDefinitionId());
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/start");
    Map<String, Object> responseMap = new HashMap<>();
    // Some variables are returned.
    responseMap.put(WorkflowConstants.BPMN_DMN_VARIABLES, "variables");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(responseMap)
                .build());

    try {
      camundaRunTimeServiceRest.startProcess(startProcessRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void correlateAllMessageWithAllData() {
    Map<String, CorrelateAllMessage.CorrelateKey> correlationKeys = new HashMap<>();
    correlationKeys.put(
        "testVariable1", new CorrelateAllMessage.CorrelateKey("testValue1", "String"));
    correlationKeys.put(
        "testVariable2", new CorrelateAllMessage.CorrelateKey("testValue2", "String"));
    CorrelateAllMessage testCorrelateMessage =
        new CorrelateAllMessage("testMessage", "ownerId", correlationKeys);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(new HashMap<>())
                .build());
    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessage(testCorrelateMessage));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void correlateAllMessageWithAllDataWithException() {
    Map<String, CorrelateAllMessage.CorrelateKey> correlationKeys = new HashMap<>();
    correlationKeys.put(
        "testVariable1", new CorrelateAllMessage.CorrelateKey("testValue1", "String"));
    correlationKeys.put(
        "testVariable2", new CorrelateAllMessage.CorrelateKey("testValue2", "String"));
    CorrelateAllMessage testCorrelateMessage =
        new CorrelateAllMessage("testMessage", "ownerId", correlationKeys);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .build());
    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessage(testCorrelateMessage));
  }

  @Test
  public void testCompleteTask() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();
    camundaRunTimeServiceRest.completeTask(task, "taskId");
    Mockito.verify(httpClient).httpResponse(Mockito.any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCompleteTaskNon2xx() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();
    camundaRunTimeServiceRest.completeTask(task, "taskId");
  }

  @Test
  public void testCompleteTask404() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION)))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();

    try {
      camundaRunTimeServiceRest.completeTask(task, "taskId");
      Assert.fail("Exception expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.CAMUNDA_TASK_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void testCompleteTaskForSuspended() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("Error in making rest call. Error={\"type\":\"RestException\",\"message\":\"ENGINE-03043 ExternalTask with id 'gfgfhgf-8797g-ygh87tg8-78t8yt8g8y8' is suspended.\"}")
                .build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().workerId("workerId").variables(Collections.EMPTY_MAP).build();
    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
      camundaRunTimeServiceRest.completeTask(task, "taskId");
    });
    assertEquals(WorkflowError.CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED,
        exception.getWorkflowError());
  }

  @Test
  public void testFailureTask() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .workerId("workerId")
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    camundaRunTimeServiceRest.failureTask(task, "taskId");
    Mockito.verify(httpClient).httpResponse(Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFailureTaskNon2xx() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .workerId("workerId")
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    camundaRunTimeServiceRest.failureTask(task, "taskId");
  }

  @Test
  public void testFailureTask404() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .workerId("workerId")
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();

    try {
      camundaRunTimeServiceRest.failureTask(task, "taskId");
      Assert.fail("Exception expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.CAMUNDA_TASK_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void testFailureTaskForSuspended() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("Error in making rest call. Error={\"type\":\"RestException\",\"message\":\"ENGINE-03043 ExternalTask with id 'gfgfhgf-8797g-ygh87tg8-78t8yt8g8y8' is suspended.\"}")
                .build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .workerId("workerId")
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
      camundaRunTimeServiceRest.failureTask(task, "taskId");
    });
    assertEquals(WorkflowError.CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED,
        exception.getWorkflowError());
  }

  public void correlateAllMessageWithAllDataOffline() {
    Map<String, CorrelateAllMessage.CorrelateKey> correlationKeys = new HashMap<>();
    correlationKeys.put(
        "testVariable1", new CorrelateAllMessage.CorrelateKey("testValue1", "String"));
    correlationKeys.put(
        "testVariable2", new CorrelateAllMessage.CorrelateKey("testValue2", "String"));
    CorrelateAllMessage testCorrelateMessage =
        new CorrelateAllMessage("testMessage", "ownerId", correlationKeys);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(new HashMap<>())
                .build());

    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessage(testCorrelateMessage));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void correlateAllMessageWithAllDataOfflineError() {
    Map<String, CorrelateAllMessage.CorrelateKey> correlationKeys = new HashMap<>();
    correlationKeys.put(
        "testVariable1", new CorrelateAllMessage.CorrelateKey("testValue1", "String"));
    correlationKeys.put(
        "testVariable2", new CorrelateAllMessage.CorrelateKey("testValue2", "String"));
    CorrelateAllMessage testCorrelateMessage =
        new CorrelateAllMessage("testMessage", "ownerId", correlationKeys);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .build());

    camundaRunTimeServiceRest.correlateAllMessage(testCorrelateMessage);
  }

  @Test
  public void correlateMessageWithoutSystemOffline() {
    Map<String, Object> testVariableMap = new HashMap<String, Object>();
    testVariableMap.put("testVariable1", "testValue1");
    testVariableMap.put("testVariable2", "testValue2");
    CorrelateMessage testCorrelateMessage =
        CorrelateMessage.builder()
            .messageName("testMessage")
            .processInstanceId("testProcessInstanceId")
            .processVariables(testVariableMap)
            .build();
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    String authHeader =
        "Intuit_IAM_Authentication intuit_appid=xxx,intuit_app_secret=xxxx,intuit_token=xxxx,intuit_userid=9130347715753436,intuit_token_type=IAM-Ticket,intuit_realmid=9130347798120106";

    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(authHeader);

    Assert.assertTrue(camundaRunTimeServiceRest.correlateMessage(testCorrelateMessage));

    Mockito.verify(offlineTicketClient, Mockito.times(0)).getSystemOfflineHeadersForOfflineJob();
  }
  
  @SuppressWarnings("unchecked")
  @Test
  public void test_getExtenalTaskDtls_success() {
	 
	 String extTaskId = "extTaskId1";
	 String externalTaskGetResponseStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
	  
	 ExternalTaskDetail extGetResp = ObjectConverter.fromJson(externalTaskGetResponseStr, ExternalTaskDetail.class);
	  
	 WASHttpResponse<ExternalTaskDetail> response = WASHttpResponse.<ExternalTaskDetail>builder()
			  .isSuccess2xx(true).status(HttpStatus.OK).response(extGetResp).build();
	  
	 Mockito.when(httpClient.getResponse(Mockito.anyString(), Mockito.any(), Mockito.any(ExternalTaskDetail.class.getClass())))
	  	.thenReturn(response);
	  
	 ExternalTaskDetail actual = camundaRunTimeServiceRest.getExtenalTaskDetails(extTaskId);
	 Mockito.verify(httpClient, Mockito.times(1)).getResponse(Mockito.anyString(), Mockito.any(), Mockito.any(ExternalTaskDetail.class.getClass()));
	  
	 Assert.assertNotNull(actual);
  }
  
  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void test_getExtenalTaskDtls_httpFailure() {
	 
	 String extTaskId = "extTaskId1";
	 WASHttpResponse<ExternalTaskDetail> response = WASHttpResponse.<ExternalTaskDetail>builder()
			  .isSuccess2xx(false).status(HttpStatus.NO_CONTENT).build();
	  
	 Mockito.when(httpClient.getResponse(Mockito.anyString(), Mockito.any(), Mockito.any(ExternalTaskDetail.class.getClass())))
	  	.thenReturn(response);
	  
	 camundaRunTimeServiceRest.getExtenalTaskDetails(extTaskId);
  }


  @SuppressWarnings("unchecked")
  @Test
  public void test_updateExecutionVariables_success() {
    String executionId = "execId1";
    Map<String,Object> updateMap = Map.of("extActivityStatus", "{\"status\":\"blocked\"}");
    CamundaUpdateRequest execUpdtReq = CamundaServiceHelper.prepareExecutionUpdateRequest(executionId, updateMap);

    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()).thenReturn(Authorization.AUTH_VERFIED);

	WASHttpResponse<WorkflowGenericResponse> response = WASHttpResponse.<WorkflowGenericResponse>builder()
		.isSuccess2xx(true).status(HttpStatus.ACCEPTED).build();

	Mockito.when(httpClient.httpResponse(Mockito.any(WASHttpRequest.class))).thenReturn(response);
	camundaRunTimeServiceRest.updateExecutionVariables(execUpdtReq);

	Mockito.verify(httpClient, Mockito.times(1)).httpResponse(Mockito.any(WASHttpRequest.class));
  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void test_updateExecutionVariables_httpFailure() {

    String executionId = "execId1";
    Map<String, Object> updateMap = Map.of("extActivityStatus", "{\"status\":\"blocked\"}");
    CamundaUpdateRequest execUpdtReq =
        CamundaServiceHelper.prepareExecutionUpdateRequest(executionId, updateMap);

    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);

    WASHttpResponse<ExternalTaskDetail> response =
        WASHttpResponse.<ExternalTaskDetail>builder()
            .isSuccess2xx(false)
            .status(HttpStatus.NO_CONTENT)
            .build();

    Mockito.when(httpClient.httpResponse(Mockito.any(WASHttpRequest.class))).thenReturn(response);

    camundaRunTimeServiceRest.updateExecutionVariables(execUpdtReq);
  }

  @SuppressWarnings("unchecked")
  @Test
  public void test_updateProcessInstanceVariables_success() {

    Mockito.when(workflowCoreConfig.getRestEndpointProcessInstance())
        .thenReturn("/process-instance");
    Mockito.when(workflowCoreConfig.getRestEndpointProcessInstanceVariables())
        .thenReturn("/variables");
    Mockito.when(camundaRestUtil.getCamundaBaseURL())
        .thenReturn("http://camunda-engine.intuit.com");

    String processInstanceId = "pId1";
    Map<String, Variable> modifications = new HashMap<>();
    modifications.put("txnId", Variable.builder().value("t1").build());
    CamundaUpdateRequest updtReq = CamundaUpdateRequest.builder()
        .processInstanceId(processInstanceId)
        .modifications(modifications)
        .build();

    WASHttpResponse<WorkflowGenericResponse> response = WASHttpResponse
        .<WorkflowGenericResponse>builder()
        .isSuccess2xx(true).status(HttpStatus.ACCEPTED).build();

    Mockito.when(httpClient.httpResponse(Mockito.any(WASHttpRequest.class))).thenReturn(response);

    camundaRunTimeServiceRest.updateProcessInstanceVariables(updtReq);

    Mockito.verify(httpClient, Mockito.times(1)).httpResponse(Mockito.any(WASHttpRequest.class));

  }

  @SuppressWarnings("unchecked")
  @Test(expected = WorkflowGeneralException.class)
  public void test_updateProcessInstanceVariables_httpFailure() {
	  
    Mockito.when(workflowCoreConfig.getRestEndpointProcessInstance())
        .thenReturn("/process-instance");
    Mockito.when(workflowCoreConfig.getRestEndpointProcessInstanceVariables())
        .thenReturn("/variables");
    Mockito.when(camundaRestUtil.getCamundaBaseURL())
        .thenReturn("http://camunda-engine.intuit.com");

    String processInstanceId = "pId1";
    Map<String, Variable> modifications = new HashMap<>();
    modifications.put("txnId", Variable.builder().value("t1").build());
    CamundaUpdateRequest updtReq = CamundaUpdateRequest.builder()
        .processInstanceId(processInstanceId)
        .modifications(modifications)
        .build();

    WASHttpResponse<WorkflowGenericResponse> response = WASHttpResponse
        .<WorkflowGenericResponse>builder()
        .isSuccess2xx(false).status(HttpStatus.NO_CONTENT).build();

    Mockito.when(httpClient.httpResponse(Mockito.any(WASHttpRequest.class))).thenReturn(response);

    camundaRunTimeServiceRest.updateProcessInstanceVariables(updtReq);
  }

  @Test
  public void testCustomCompleteTask() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getV1EndPoint()).thenReturn("/v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().variables(Collections.EMPTY_MAP).build();
    camundaRunTimeServiceRest.customCompleteTask(task, "taskId");
    Mockito.verify(httpClient).httpResponse(Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCustomCompleteTaskNon2xx() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getV1EndPoint()).thenReturn("/v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().variables(Collections.EMPTY_MAP).build();
    camundaRunTimeServiceRest.customCompleteTask(task, "taskId");
  }

  @Test
  public void testCustomCompleteTask404() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getV1EndPoint()).thenReturn("/v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().variables(Collections.EMPTY_MAP).build();

    try {
      camundaRunTimeServiceRest.customCompleteTask(task, "taskId");
      Assert.fail("Exception expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.CAMUNDA_TASK_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void testCustomCompleteTaskForSuspended() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getV1EndPoint()).thenReturn("/v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/complete");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .error("Error in making rest call. Error={\"type\":\"RestException\",\"message\":\"ENGINE-03043 ExternalTask with id 'gfgfhgf-8797g-ygh87tg8-78t8yt8g8y8' is suspended.\"}")
                .isSuccess2xx(false)
                .build());

    ExternalTaskSuccess task =
        ExternalTaskSuccess.builder().variables(Collections.EMPTY_MAP).build();
    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
      camundaRunTimeServiceRest.customCompleteTask(task, "taskId");
    });
    assertEquals(WorkflowError.CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED,
        exception.getWorkflowError());
  }

  @Test
  public void testCustomFailureTask() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    camundaRunTimeServiceRest.customFailureTask(task, "taskId");
    Mockito.verify(httpClient).httpResponse(Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCustomFailureTaskNon2xx() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    camundaRunTimeServiceRest.customFailureTask(task, "taskId");
  }

  @Test
  public void testCustomFailureTask404() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskComplete()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();

    try {
      camundaRunTimeServiceRest.customFailureTask(task, "taskId");
      Assert.fail("Exception expected");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.CAMUNDA_TASK_NOT_FOUND, e.getWorkflowError());
    }
  }

  @Test
  public void testCustomFailureTaskForSuspended() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("v1/external-task");
    Mockito.when(workflowCoreConfig.getTaskFailure()).thenReturn("/failure");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .error("Error in making rest call. Error={\"type\":\"RestException\",\"message\":\"ENGINE-03043 ExternalTask with id 'gfgfhgf-8797g-ygh87tg8-78t8yt8g8y8' is suspended.\"}")
                .isSuccess2xx(false)
                .build());

    ExternalTaskFailure task =
        ExternalTaskFailure.builder()
            .errorDetails("errorDetails")
            .errorMessage("errorMessage")
            .build();
    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
      camundaRunTimeServiceRest.customFailureTask(task, "taskId");
    });
    assertEquals(WorkflowError.CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED,
        exception.getWorkflowError());
  }

  @Test
  public void testExtendLockSuccess() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("external-task");
    Mockito.when(workflowCoreConfig.getExtendLock()).thenReturn("/extendLock");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExtendExternalTask task =
        ExtendExternalTask.builder()
            .workerId("workerId")
            .newDuration(1234L)
            .build();
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.NO_CONTENT).isSuccess2xx(true).build());
    camundaRunTimeServiceRest.extendLock(task, "taskId");
    Mockito.verify(httpClient, Mockito.times(1)).httpResponse(Mockito.any());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExtendLock_NotFound() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("external-task");
    Mockito.when(workflowCoreConfig.getExtendLock()).thenReturn("/extendLock");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExtendExternalTask task =
        ExtendExternalTask.builder()
            .workerId("workerId")
            .newDuration(1234L)
            .build();
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());
    camundaRunTimeServiceRest.extendLock(task, "taskId");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExtendLock_HTTP_500() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getExternalTask()).thenReturn("external-task");
    Mockito.when(workflowCoreConfig.getExtendLock()).thenReturn("/extendLock");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

    ExtendExternalTask task =
        ExtendExternalTask.builder()
            .workerId("workerId")
            .newDuration(1234L)
            .build();
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(WASHttpResponse.builder().status(HttpStatus.INTERNAL_SERVER_ERROR).isSuccess2xx(false).build());
    camundaRunTimeServiceRest.extendLock(task, "taskId");
  }
  
	@Test
	public void testTriggerProcessV2() {
		Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

		Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
				.thenReturn(WASHttpResponse.builder().status(HttpStatus.OK).isSuccess2xx(true).build());

		Assert.assertTrue(camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage()));
		Mockito.verify(httpClient).httpResponse(Mockito.any(),
				Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE));
	}

	@Test(expected = WorkflowGeneralException.class)
	public void testTriggerProcessV2Non2xx() {
		Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

		Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
				.thenReturn(
						WASHttpResponse.builder().status(HttpStatus.INTERNAL_SERVER_ERROR).isSuccess2xx(false).build());

		camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage());
	}

	@Test
	public void testTriggerProcessV2404() {
		Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

		Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
				.thenReturn(WASHttpResponse.builder().status(HttpStatus.NOT_FOUND).isSuccess2xx(false).build());

		try {
			camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage());
			Assert.fail("Exception expected");
		} catch (WorkflowGeneralException e) {
			Assert.assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR, e.getWorkflowError());
			Mockito.verify(httpClient).httpResponse(Mockito.any(),
					Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE));
		}
	}

  @Test
  public void testTriggerProcessV2NonRetry() {
	  Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("{\"type\":\"ProcessEngineException\",\"message\":\"Execution with id '0fd15e70-bbb4-11ed-9812-4e3c047f222b' does not have a subscription to a message event with name 'filing_milestone_in_progress': eventSubscriptions is empty\",\"code\":0}")
                .build());

    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
    	camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage());
    });
    assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR,
        exception.getWorkflowError());
  }
  
  @Test
  public void testTriggerProcessV2Deadlock() {
	  Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":10000}")
                .build());

    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
    	camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage());
    });
    Mockito.verify(httpClient).httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE));
    assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR,
        exception.getWorkflowError());
  }
  
  @Test
  public void testTriggerProcessV2FKVoilation() {
	  Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
		Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");

    Mockito.when(httpClient.httpResponse(any(), Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE)))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .isSuccess2xx(false)
                .error("{\"type\":\"ProcessEngineException\",\"message\":\"An exception occurred in the persistence layer. Please check the server logs for a detailed message and the entire exception stack trace.\",\"code\":10001}")
                .build());

    WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
    	camundaRunTimeServiceRest.correlateMessage(prepareCorrelateMessage());
    });
    Mockito.verify(httpClient).httpResponse(Mockito.any(),
			Mockito.eq(RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE));
    assertEquals(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR,
        exception.getWorkflowError());
  }

  @Test
  public void testDeleteProcessInstance204() {
    Mockito.when(workflowCoreConfig.getRestEndpointProcessInstance()).thenReturn("/process-instance");
    Mockito.when(camundaRestUtil.getCamundaBaseURL()).thenReturn("http://camunda-engine.intuit.com");

    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder().status(HttpStatus.NO_CONTENT).isSuccess2xx(true).build());
    camundaRunTimeServiceRest.deleteProcessInstance("test-process-id");
    Mockito.verify(httpClient).httpResponse(Mockito.any());
  }

	private CorrelateMessage prepareCorrelateMessage() {
		Map<String, Object> testVariableMap = new HashMap<String, Object>();
		testVariableMap.put("testVariable1", "testValue1");
		testVariableMap.put("testVariable2", "testValue2");

		CorrelateMessage testCorrelateMessage = CorrelateMessage.builder().messageName("testMessage")
				.processInstanceId("testProcessInstanceId").processVariables(testVariableMap).build();
		return testCorrelateMessage;
	}

  @Test
  public void correlateAllMessageAsyncWithAllData() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);

    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(new HashMap<>())
                .build());
    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void correlateAllMessageAsyncWithAllDataWithException() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .build());
    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage));
  }

  @Test
  public void correlateAllMessageAsyncWithNoActiveProcesses() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);

    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .error(
                    "Error in making rest call. Error={\"type\":\"BadUserRequestException\",\"message\":\"Process instance ids cannot be empty: process instance ids is empty\",\"code\":0}")
                .build());
    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage));
  }

  @Test
  public void correlateAllMessageAsyncWithAllDataOffline() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(true)
                .response(new HashMap<>())
                .build());

    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage));
  }

  @Test
  public void correlateAllMessageAsyncWithNoActiveProcessesOffline() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .error(
                    "Error in making rest call. Error={\"type\":\"BadUserRequestException\",\"message\":\"Process instance ids cannot be empty: process instance ids is empty\",\"code\":0}")
                .build());

    Assert.assertNotNull(camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void correlateAllMessageAsyncWithAllDataOfflineError() {
    List<CorrelateAllMessageAsync.ProcessVariable> variables =
        Collections.singletonList(
            CorrelateAllMessageAsync.ProcessVariable.builder()
                .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                .value("definitionKey1")
                .build());
    CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
        CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
            .businessKey("ownerId")
            .variables(variables)
            .build();
    CorrelateAllMessageAsync testCorrelateMessage =
        new CorrelateAllMessageAsync("testMessage", processInstanceQuery);
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("testPrefix");
    Mockito.when(workflowCoreConfig.getRestEndpointMessage()).thenReturn("/testMessage");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn(Authorization.AUTH_VERFIED);
    Mockito.when(httpClient.httpResponse(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .isSuccess2xx(false)
                .response(new HashMap<>())
                .build());

    camundaRunTimeServiceRest.correlateAllMessageAsync(testCorrelateMessage);
  }

  @Test
  public void logIfOwnerIdMatches() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Method method = CamundaRunTimeServiceRest.class.getDeclaredMethod("logIfOwnerIdMatches", Object.class, String.class);
    method.setAccessible(true);

    String message = "Test message";
    Object objectToLog = new JSONObject();
    Long ownerId = 123456789L;

    Mockito.when(camundaRequestResponseLoggerConfig.getOwnerIds()).thenReturn(Collections.singleton(ownerId));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(String.valueOf(ownerId));

    try (MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      method.invoke(camundaRunTimeServiceRest, objectToLog, message);
      mockedLogger.verify(() -> WorkflowLogger.info(ArgumentMatchers.any()));
    }
  }

  @Test
  public void logIfOwnerIdMatchesNoConfigPresent() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Method method = CamundaRunTimeServiceRest.class.getDeclaredMethod("logIfOwnerIdMatches", Object.class, String.class);
    method.setAccessible(true);

    String message = "Test message";
    Object objectToLog = new JSONObject();
    Long ownerId = 123456789L;

    Mockito.when(camundaRequestResponseLoggerConfig.getOwnerIds()).thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(String.valueOf(ownerId));

    try (MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      method.invoke(camundaRunTimeServiceRest, objectToLog, message);
      mockedLogger.verify(Mockito.times(0), () -> WorkflowLogger.info(ArgumentMatchers.any()));
    }
  }

  @Test
  public void testLogIfOwnerIdNotParsable() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    // Arrange
    Method method = CamundaRunTimeServiceRest.class.getDeclaredMethod("logIfOwnerIdMatches", Object.class, String.class);
    method.setAccessible(true);

    String message = "Test message";
    Object objectToLog = new JSONObject();

    String nonParsableOwnerId = "not-a-number";
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(nonParsableOwnerId);
    Mockito.when(camundaRequestResponseLoggerConfig.getOwnerIds()).thenReturn(Collections.singleton(123456789L));

    try (MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      method.invoke(camundaRunTimeServiceRest, objectToLog, message);
      mockedLogger.verify(Mockito.times(0), () -> WorkflowLogger.info(ArgumentMatchers.any()));
    }
  }

  @Test
  public void testLogIfOwnerIdIsNull() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    // Arrange
    Method method = CamundaRunTimeServiceRest.class.getDeclaredMethod("logIfOwnerIdMatches", Object.class, String.class);
    method.setAccessible(true);

    String message = "Test message";
    Object objectToLog = new JSONObject();

    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(camundaRequestResponseLoggerConfig.getOwnerIds()).thenReturn(Collections.singleton(123456789L));

    try (MockedStatic<WorkflowLogger> mockedLogger = Mockito.mockStatic(WorkflowLogger.class)) {
      method.invoke(camundaRunTimeServiceRest, objectToLog, message);
      mockedLogger.verify(Mockito.times(0), () -> WorkflowLogger.info(ArgumentMatchers.any()));
    }
  }

}
