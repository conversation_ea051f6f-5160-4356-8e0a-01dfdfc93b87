package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.StateTransitionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class CamundaServiceManagerTest {


  @Mock
  private BPMNEngineHistoryServiceRest camundaHistoryRest;

  @Mock private WorkflowTaskConfig workflowTaskConfig;

  @InjectMocks
  CamundaServiceManager camundaServiceManager;


  @Test
  public void test_getExternalTaskVariablesEmptyResponse(){
    WASHttpResponse<List<ExternalTaskLog>> wasResponse = WASHttpResponse
        .<List<ExternalTaskLog>>builder()
        .response(Collections.emptyList()).build();
    Mockito.when(camundaHistoryRest.getExternalTaskLogs(Mockito.any())).thenReturn(wasResponse);
    Assert.assertTrue(camundaServiceManager.getExternalTaskVariables("pid", "123").isEmpty());

    wasResponse = WASHttpResponse
        .<List<ExternalTaskLog>>builder()
        .response(null).build();
    Mockito.when(camundaHistoryRest.getExternalTaskLogs(Mockito.any())).thenReturn(wasResponse);
    Assert.assertTrue(camundaServiceManager.getExternalTaskVariables("pid", "123").isEmpty());
  }

  @Test
  public void test_getExternalTaskVariables(){
    ExternalTaskLog externalTaskLog = new ExternalTaskLog();
    externalTaskLog.setExecutionId("aa-bb");
    WASHttpResponse<List<ExternalTaskLog>> wasResponse = WASHttpResponse
        .<List<ExternalTaskLog>>builder()
        .response(Collections.singletonList(externalTaskLog)).build();
    Mockito.when(camundaHistoryRest.getExternalTaskLogs(Mockito.any())).thenReturn(wasResponse);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);


    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setName("key1");
    processVariableDetailsResponse1.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setName("key2");
    processVariableDetailsResponse2.setValue("newValue2");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    ProcessVariableDetailsResponse processVariableDetailsResponse3 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse3.setName("key3");
    processVariableDetailsResponse3.setValue("value3");
    processVariableDetailsResponse.add(processVariableDetailsResponse3);

    ProcessVariableDetailsResponse processVariableDetailsResponse4 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse4.setName("key3");
    processVariableDetailsResponse4.setValue("value3");
    processVariableDetailsResponse.add(processVariableDetailsResponse4);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    Map<String, Object> result = camundaServiceManager.getExternalTaskVariables("pid", "123");
    Assert.assertEquals(3, result.size());
    Assert.assertTrue(result.containsKey("key3"));
    Assert.assertTrue(result.containsKey("key2"));
  }
}
