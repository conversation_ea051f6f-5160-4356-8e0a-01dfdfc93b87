
package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema;

import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.CurrentStepDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import java.io.ByteArrayInputStream;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/** <AUTHOR> */
public class SchemaDecoderTest {

  private static final String parametersSchema =
          TestHelper.readResourceAsString("schema/testData/parameters.json");

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapproval.bpmn");

  private static final String BPMN_XML_TEMPLATE_WITH_WORKFLOW_NAME =
      TestHelper.readResourceAsString("bpmn/invoiceapprovalRec.bpmn");

  private static final String handlersSchema =
      TestHelper.readResourceAsString("schema/testData/handlers.json");

  private static final String stepDetailsSchema =
      TestHelper.readResourceAsString("schema/testData/stepDetails.json");

  private static final String recurrenceDetailsSchema =
      TestHelper.readResourceAsString("schema/testData/recurrenceDetails.json");

  private static final String taskDetailsSchema = "{\"required\": true}";

  private static final String currentStepDetailsSchema = "{\"required\": true}";

  private static final String INVOICE_APPROVAL_ONLY_BPMN =
          Resources.getResource("bpmn/InvoiceApproval_withHandlerDetails.bpmn").getFile();

  private static final String INVOICE_APPROVAL_DMN =
          Resources.getResource("dmn/invoiceSendDecisionExtra.dmn").getFile();

  private static final String INVOICE_APPROVAL_BPMN_NO_TEMPLATE_NAME =
          Resources.getResource("bpmn/invoiceApproval_noTemplateName.bpmn").getFile();

  private static final String INVOICE_APPROVAL_BPMN_NO_TEMPLATE_NAME_PRESENT =
          Resources.getResource("bpmn/invoiceApproval_noTemplateName_present.bpmn").getFile();

  private static final String INVOICE_APPROVAL_NO_PROCESS_BPMN=
          Resources.getResource("bpmn/inv_no_process.bpmn").getFile();

  private static final String INVOICE_APPROVAL_BPMN_TEMPLATE =
      Resources.getResource("bpmn/invoiceapproval.bpmn").getFile();

  private static final String BPMN_XML_TEMPLATE_WITH_SYSTEM_TAG =
          TestHelper.readResourceAsString("bpmn/customReminderWithSystemTag.bpmn");

  private static final String BPMN_XML_TEMPLATE_WITHOUT_SYSTEM_TAG =
          TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");

  private static final String BPMN_XML_TEMPLATE_WITH_INVALID_SYSTEM_TAG =
          TestHelper.readResourceAsString("bpmn/customReminderWithInvalidTag.bpmn");

  private static Map<String, String> schema = populateSchemaMap();

  @Test
  public void testGetParametersForUI_Success() {
    Map<String, ParameterDetails> parametersForUI = SchemaDecoder.getParametersForUI(schema).get();
    Assert.assertNotNull(parametersForUI);
    Assert.assertEquals(3, parametersForUI.size());
    Assert.assertTrue(parametersForUI.containsKey("To"));
    Assert.assertTrue(parametersForUI.containsKey("Subject"));
    Assert.assertTrue(parametersForUI.containsKey("Message"));
  }

  @Test
  public void testGetParametersForHandler_Success() {
    Map<String, ParameterDetails> parametersForHandler =
        SchemaDecoder.getParametersForHandler(schema).get();
    Assert.assertNotNull(parametersForHandler);
    Assert.assertEquals(3, parametersForHandler.size());
    Assert.assertFalse(parametersForHandler.containsKey("To"));
    Assert.assertTrue(parametersForHandler.containsKey("Subject"));
    Assert.assertTrue(parametersForHandler.containsKey("Message"));
  }

  @Test
  public void testGetHandlerDetails() {
    HandlerDetails handlerDetails = SchemaDecoder.getHandlerDetails(schema).get();
    Assert.assertNotNull(handlerDetails);
    Assert.assertEquals("1234", handlerDetails.getHandlerId());
    Assert.assertEquals("appconnect", handlerDetails.getTaskHandler());
    Assert.assertEquals("appconnectWorkflowTaskHandler", handlerDetails.getActionName());
    Assert.assertEquals(RecordType.INVOICE, handlerDetails.getRecordType());
  }

  @Test
  public void testGetTaskDetails() {
    TaskDetails taskDetails = SchemaDecoder.getTaskDetails(schema).get();
    Assert.assertNotNull(taskDetails);
    Assert.assertTrue(taskDetails.getRequired());
  }

  @Test
  public void testGetCurrentStepDetails() {
    CurrentStepDetails currentStepDetails = SchemaDecoder.getCurrentStepDetails(schema).get();
    Assert.assertNotNull(currentStepDetails);
    Assert.assertTrue(currentStepDetails.getRequired());
  }

  @Test
  public void testGetStepDetails() {
    Map<String, Set<String>> stepDetails = SchemaDecoder.getStepDetails(schema).get();
    Assert.assertNotNull(stepDetails);
    Assert.assertEquals(2, stepDetails.size());
    Assert.assertEquals(5, stepDetails.get("abc").size());
    Assert.assertEquals(6, stepDetails.get("def").size());
  }

  @Test
  public void testGetHandlerDetailsFromExtensionElementsSuccess() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    BpmnModelInstance modelInstance = Bpmn.readModelFromStream(fisBpmn);
    ExtensionElements extensionElements =
            modelInstance.getModelElementsByType(StartEvent.class).stream().findFirst().get().getExtensionElements();
    Optional<HandlerDetails> handlerDetailsOptional = SchemaDecoder.getHandlerDetailsFromExtensionElements(extensionElements);
    Assert.assertTrue(handlerDetailsOptional.isPresent());
    Assert.assertEquals("was", handlerDetailsOptional.get().getTaskHandler());
    Assert.assertEquals(RecordType.INVOICE, handlerDetailsOptional.get().getRecordType());
  }

  @Test
  public void testGetHandlerDetailsFromExtensionElementsForNoExtensionElement() {
    Optional<HandlerDetails> handlerDetailsOptional = SchemaDecoder.getHandlerDetailsFromExtensionElements(null);
    Assert.assertFalse(handlerDetailsOptional.isPresent());
  }

  @Test
  public void testGetTemplateNameForBPMN() {
    File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
    BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromFile(bpmnFile);
    String expectedTemplateName = SchemaDecoder.getTemplateName(bpmnModelInstance, ModelType.BPMN);
    Assert.assertEquals("InvoiceApproval", expectedTemplateName);
  }

  @Test
  public void testGetTemplateNameForDmn() {
    File dmnFile = new File(INVOICE_APPROVAL_DMN);
    DmnModelInstance dmnModelInstance = Dmn.readModelFromFile(dmnFile);
    String expectedTemplateName = SchemaDecoder.getTemplateName(dmnModelInstance, ModelType.DMN);
    Assert.assertEquals("invoiceSendDecisionTest", expectedTemplateName);
  }

  @Test
  public void testGetTemplateNameForNoTemplateName() {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN_NO_TEMPLATE_NAME);
    BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromFile(bpmnFile);
    String expectedTemplateName = SchemaDecoder.getTemplateName(bpmnModelInstance, ModelType.BPMN);
    Assert.assertNull(expectedTemplateName);
  }

  @Test(expected = UnsupportedOperationException.class)
  public void testGetTemplateNameIncorrectModelType() {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN_TEMPLATE);
    BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromFile(bpmnFile);
    SchemaDecoder.getTemplateName(bpmnModelInstance, ModelType.fromType("Fail"));
  }

  @Test
  public void testGetTemplateNameIncorrectModelTypeFail() {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN_TEMPLATE);
    BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromFile(bpmnFile);
    String expectedTemplateName = SchemaDecoder.getTemplateName(bpmnModelInstance, null);
    Assert.assertNull(expectedTemplateName);
  }

  @Test
  public void getDisplayNameFromTemplateTest() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN_TEMPLATE);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    BpmnModelInstance modelInstance = Bpmn.readModelFromStream(fisBpmn);
    String expectedTemplateName =
        SchemaDecoder.getTemplateDisplayName("invoiceapproval", modelInstance);
    Assert.assertNotNull(expectedTemplateName);
  }

  @Test
  public void getDisplayNameFromTemplateTestNull() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN_NO_TEMPLATE_NAME_PRESENT);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    BpmnModelInstance modelInstance = Bpmn.readModelFromStream(fisBpmn);
    String expectedTemplateName =
            SchemaDecoder.getTemplateDisplayName("invoiceapproval", modelInstance);
    Assert.assertNull(expectedTemplateName);
  }

  @Test
  public void getDisplayNameFromTemplateTestFail() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_NO_PROCESS_BPMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    BpmnModelInstance modelInstance = Bpmn.readModelFromStream(fisBpmn);
    String expectedTemplateName =
        SchemaDecoder.getTemplateDisplayName("invoiceapproval", modelInstance);
    Assert.assertNull(expectedTemplateName);
  }

  @Test
  public void testNoStepDetails() {
    Map<String, String> schema = populateSchemaMap();
    schema.remove(WorkFlowVariables.STEP_DETAILS_KEY.getName());
    Optional<Map<String, Set<String>>> stepDetailsOpt = SchemaDecoder.getStepDetails(schema);
    Assert.assertFalse(stepDetailsOpt.isPresent());
  }

  @Test
  public void testNoParametersForHandler() {
    Map<String, String> schema = populateSchemaMap();
    schema.remove(WorkFlowVariables.PARAMETERS_KEY.getName());
    Optional<Map<String, ParameterDetails>> parametersForHandlerOpt =
        SchemaDecoder.getParametersForHandler(schema);
    Assert.assertFalse(parametersForHandlerOpt.isPresent());
  }

  @Test
  public void testGetTaskDetailsNullTaskDetails() {
    Map<String, String> schema = populateSchemaMap();
    schema.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), null);
    Optional<TaskDetails> taskDetails = SchemaDecoder.getTaskDetails(schema);
    Assert.assertEquals(Optional.empty(),taskDetails);
  }

  @Test
  public void testGetRecurrenceDetails() {
    Map<String, String> recurrenceDetails = SchemaDecoder.getRecurrenceElementDetails(schema).get();
    Assert.assertNotNull(recurrenceDetails);
    Assert.assertEquals(2, recurrenceDetails.size());
    Assert.assertEquals("val1", recurrenceDetails.get("abc"));
    Assert.assertEquals("val2", recurrenceDetails.get("def"));
  }

  @Test
  public void testGetWorkflowName_isNull() {
    BpmnModelInstance modelInstance =
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes()));
    String workflowName = SchemaDecoder.getWorkflowNameFromBpmnXml(modelInstance);
    Assert.assertNull(workflowName);
  }

  @Test
  public void testGetWorkflowName() {
    BpmnModelInstance modelInstance =
        Bpmn.readModelFromStream(
            new ByteArrayInputStream(BPMN_XML_TEMPLATE_WITH_WORKFLOW_NAME.getBytes()));
    String workflowName = SchemaDecoder.getWorkflowNameFromBpmnXml(modelInstance);
    Assert.assertNotNull(workflowName);
    Assert.assertEquals("approval", workflowName);
  }

  @Test
  public void testGetAndValidateTemplateTagVersion(){
    BpmnModelInstance  modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML_TEMPLATE_WITH_SYSTEM_TAG.getBytes()));
    String version = SchemaDecoder.getAndValidateTemplateTagVersion(modelInstance);
    Assert.assertNotNull(version);
    Assert.assertEquals("1.2.3-zeta",version);

  }
  @Test
  public void testGetAndValidateTemplateWithoutTagVersion(){
    BpmnModelInstance  modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML_TEMPLATE_WITHOUT_SYSTEM_TAG.getBytes()));
    String version = SchemaDecoder.getAndValidateTemplateTagVersion(modelInstance);
    Assert.assertNotNull(version);
    Assert.assertEquals(StringUtils.EMPTY,version);

  }

  @Test
  public void testGetAndValidateTemplateWithInvalidTagVersion() {
    BpmnModelInstance modelInstance =
        Bpmn.readModelFromStream(
            new ByteArrayInputStream(BPMN_XML_TEMPLATE_WITH_INVALID_SYSTEM_TAG.getBytes()));
    String version = SchemaDecoder.getAndValidateTemplateTagVersion(modelInstance);
    Assert.assertNotNull(version);
    Assert.assertEquals(StringUtils.EMPTY, version);
  }

  private static Map<String, String> populateSchemaMap() {
    Map<String, String> schemaMap = new HashMap<>();
    schemaMap.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schemaMap.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), handlersSchema);
    schemaMap.put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), taskDetailsSchema);
    schemaMap.put(WorkFlowVariables.CURRENT_STEP_DETAILS.getName(), currentStepDetailsSchema);
    schemaMap.put(WorkFlowVariables.STEP_DETAILS_KEY.getName(), stepDetailsSchema);
    schemaMap.put(WorkFlowVariables.RECURRENCE_DETAILS_KEY.getName(), recurrenceDetailsSchema);
    return schemaMap;
  }
}
