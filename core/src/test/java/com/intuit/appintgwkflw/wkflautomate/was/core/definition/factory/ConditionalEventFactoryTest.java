package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.ExclusiveGatewayHandler;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.ExclusiveGateway;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ConditionalEventFactoryTest {
  @Mock private BusinessRuleTaskHandler businessRuleTaskHandler;

  @Mock private ExclusiveGatewayHandler exclusiveGatewayHandler;

  @InjectMocks private ConditionalElementFactory conditionalElementFactory;

  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance bpmnModelInstance1;

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String BYO_BPMN = "bpmn/customWorkflowDefinition.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";

  /**
   * @param fileName
   * @return : BPMN Model Instance
   */
  private static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        ConditionalEventFactoryTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    bpmnModelInstance1 = readBPMNFile(BYO_BPMN);
  }

  @Test
  public void testBusinessRuleTask() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class).stream().findFirst().get();
    Assert.assertEquals(businessRuleTaskHandler, conditionalElementFactory.getHandler(element));
  }

  @Test
  public void testExclusiveGateway() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(ExclusiveGateway.class).stream().findFirst().get();
    Assert.assertEquals(exclusiveGatewayHandler, conditionalElementFactory.getHandler(element));
  }

  @Test(expected = IllegalArgumentException.class)
  public void testExceptionScenario() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(SequenceFlow.class).stream().findFirst().get();
    Assert.assertNotEquals(exclusiveGatewayHandler, conditionalElementFactory.getHandler(element));
    Assert.assertNotEquals(businessRuleTaskHandler, conditionalElementFactory.getHandler(element));
  }
}
