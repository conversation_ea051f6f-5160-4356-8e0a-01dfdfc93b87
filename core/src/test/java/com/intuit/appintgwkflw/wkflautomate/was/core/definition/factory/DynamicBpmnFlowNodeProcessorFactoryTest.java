package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.BusinessRuleTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.CallActivityFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.EndEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.ServiceTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import java.util.ArrayList;
import java.util.List;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.ExclusiveGateway;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the DynamicBpmnFlowNodeProcessorFactory class.
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnFlowNodeProcessorFactoryTest {

  private DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  @Mock private BusinessRuleTaskFlowNodeProcessor businessRuleTaskFlowNodeProcessor;

  @Mock private CallActivityFlowNodeProcessor callActivityFlowNodeProcessor;

  @Mock private EndEventFlowNodeProcessor endEventFlowNodeProcessor;

  @Mock private ServiceTaskFlowNodeProcessor serviceTaskFlowNodeProcessor;

  @Mock private StartEventFlowNodeProcessor startEventFlowNodeProcessor;

  @Mock private DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private BpmnModelInstance bpmnModelInstance;

  private List<DynamicBpmnFlowNodeProcessor> processorList = new ArrayList<>();

  @Before
  public void setup() {
    bpmnModelInstance = Bpmn.createEmptyModel();
    businessRuleTaskFlowNodeProcessor = new BusinessRuleTaskFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
    callActivityFlowNodeProcessor = new CallActivityFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
    endEventFlowNodeProcessor = new EndEventFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
    serviceTaskFlowNodeProcessor = new ServiceTaskFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
    startEventFlowNodeProcessor = new StartEventFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
    processorList.add(businessRuleTaskFlowNodeProcessor);
    processorList.add(callActivityFlowNodeProcessor);
    processorList.add(endEventFlowNodeProcessor);
    processorList.add(serviceTaskFlowNodeProcessor);
    processorList.add(startEventFlowNodeProcessor);

    dynamicBpmnFlowNodeProcessorFactory = new DynamicBpmnFlowNodeProcessorFactory(processorList);
  }

  @Test
  public void testGetProcessorFromFlowNodeForBusinessRuleTask() {
    BusinessRuleTask businessRuleTask = bpmnModelInstance.newInstance(BusinessRuleTask.class);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(businessRuleTask);
    Assert.assertEquals(businessRuleTaskFlowNodeProcessor, dynamicBpmnFlowNodeProcessor);
  }

  @Test
  public void testGetProcessorFromFlowNodeForCallActivity() {
    CallActivity callActivity = bpmnModelInstance.newInstance(CallActivity.class);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(callActivity);
    Assert.assertEquals(callActivityFlowNodeProcessor, dynamicBpmnFlowNodeProcessor);
  }

  @Test
  public void testGetProcessorFromFlowNodeForEndEvent() {
    EndEvent endEvent = bpmnModelInstance.newInstance(EndEvent.class);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(endEvent);
    Assert.assertEquals(endEventFlowNodeProcessor, dynamicBpmnFlowNodeProcessor);
  }

  @Test
  public void testGetProcessorFromFlowNodeForServiceTask() {
    ServiceTask serviceTask = bpmnModelInstance.newInstance(ServiceTask.class);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(serviceTask);
    Assert.assertEquals(serviceTaskFlowNodeProcessor, dynamicBpmnFlowNodeProcessor);
  }

  @Test
  public void testGetProcessorFromFlowNodeForStartEvent() {
    StartEvent startEvent = bpmnModelInstance.newInstance(StartEvent.class);
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(startEvent);
    Assert.assertEquals(startEventFlowNodeProcessor, dynamicBpmnFlowNodeProcessor);
  }

  @Test
  public void testGetProcessorFromFlowNodeForUnknownFlowNode() {
    ExclusiveGateway exclusiveGateway = bpmnModelInstance.newInstance(ExclusiveGateway.class);
    assertThrows(
        WorkflowGeneralException.class,
        () -> dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(exclusiveGateway),
        WorkflowError.INVALID_BPMN_ELEMENT.getErrorMessage());
  }
}
