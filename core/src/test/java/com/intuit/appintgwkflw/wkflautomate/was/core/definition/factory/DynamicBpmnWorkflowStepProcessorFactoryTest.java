package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.BusinessRuleTaskWorkflowStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.CallActivityWorkflowStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.DynamicBpmnWorkflowStepProcessor;
import com.intuit.v4.workflows.StepTypeEnum;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the DynamicBpmnWorkflowStepProcessorFactor class.
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnWorkflowStepProcessorFactoryTest {

  private DynamicBpmnWorkflowStepProcessorFactory dynamicBpmnWorkflowStepProcessorFactory;

  @Mock private BusinessRuleTaskWorkflowStepProcessor businessRuleTaskWorkflowStepProcessor;

  @Mock private CallActivityWorkflowStepProcessor callActivityWorkflowStepProcessor;

  private List<DynamicBpmnWorkflowStepProcessor> processorList = new ArrayList<>();

  @Before
  public void setup() {
    businessRuleTaskWorkflowStepProcessor = new BusinessRuleTaskWorkflowStepProcessor();
    callActivityWorkflowStepProcessor = new CallActivityWorkflowStepProcessor();
    processorList.add(businessRuleTaskWorkflowStepProcessor);
    processorList.add(callActivityWorkflowStepProcessor);

    dynamicBpmnWorkflowStepProcessorFactory =
        new DynamicBpmnWorkflowStepProcessorFactory(processorList);
  }

  @Test
  public void testGetProcessorFromWorkflowStepForBusinessRuleTask() {
    DynamicBpmnWorkflowStepProcessor dynamicBpmnWorkflowStepProcessor =
        dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
            StepTypeEnum.CONDITION);
    Assert.assertEquals(businessRuleTaskWorkflowStepProcessor, dynamicBpmnWorkflowStepProcessor);
  }

  @Test
  public void testGetProcessorFromWorkflowStepForCallActivity() {
    DynamicBpmnWorkflowStepProcessor dynamicBpmnWorkflowStepProcessor =
        dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(StepTypeEnum.ACTION);
    Assert.assertEquals(callActivityWorkflowStepProcessor, dynamicBpmnWorkflowStepProcessor);
  }

  @Test
  public void testGetProcessorFromWorkflowStepForUnknownStepType() {
    assertThrows(
        WorkflowGeneralException.class,
        () ->
            dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
                StepTypeEnum.WORFKLOWSTEP),
        WorkflowError.INVALID_WORKFLOW_STEP.getErrorMessage());
  }
}
