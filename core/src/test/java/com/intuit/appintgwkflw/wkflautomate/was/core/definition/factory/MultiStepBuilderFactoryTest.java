package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.FlowNodeImpl;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collection;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepBuilderFactoryTest {
    private static final String MULTI_CONDITION_CUSTOM_APPROVAL_BPMN = "bpmn/customApproval_multiCondition.bpmn";
    private MultiStepBuilderFactory multiStepBuilderFactory;
    @Mock
    private MultiStepDefinitionActionBuilder multiStepDefinitionActionBuilder;
    @Mock
    private MultiStepDefinitionConditionBuilder multiStepDefinitionConditionBuilder;
    private BpmnModelInstance bpmnModelInstance;

    private static BpmnModelInstance readBPMNFile(String fileName) {
        return Bpmn.readModelFromStream(
                OutgoingActivityMapperFactoryTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    @Before
    public void setUp() {
        bpmnModelInstance = readBPMNFile(MULTI_CONDITION_CUSTOM_APPROVAL_BPMN);
        multiStepBuilderFactory = new MultiStepBuilderFactory(
                multiStepDefinitionActionBuilder,
                multiStepDefinitionConditionBuilder);
    }

    @Test
    public void testBpmnBusinessRuleTaskHandler() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        Assert.assertTrue(multiStepBuilderFactory.
                getHandler(outgoingSequenceFlows.stream().findFirst().get().getTarget()) instanceof MultiStepDefinitionConditionBuilder);
    }

    @Test
    public void testInvalidMatchingElementHandler() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        FlowNode businessRuleElement = outgoingSequenceFlows.stream().findFirst().get().getTarget();
        Collection<SequenceFlow> outgoingSequences = businessRuleElement.getOutgoing();
        Assert.assertNull(multiStepBuilderFactory.getHandler(outgoingSequences.stream().findFirst().get().getTarget()));
    }

    @Test
    public void testBpmnCallActivityElementHandler() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        FlowNode businessRuleElement = outgoingSequenceFlows.stream().findFirst().get().getTarget();
        Collection<SequenceFlow> outgoingSequences = businessRuleElement.getOutgoing();
        ArrayList<SequenceFlow> outgoingSequencesList = new ArrayList<>(outgoingSequences);
        Assert.assertTrue(multiStepBuilderFactory.getHandler(outgoingSequencesList.get(2).getTarget()) instanceof MultiStepDefinitionActionBuilder);
    }
}
