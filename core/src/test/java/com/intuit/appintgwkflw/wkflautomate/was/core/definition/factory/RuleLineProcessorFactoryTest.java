package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiSplitRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.RuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.EmptyConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RuleLineProcessorFactoryTest {

  @InjectMocks
  RuleLineProcessorFactory ruleLineProcessorFactory;

  @Mock
  MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;

  @Mock
  EmptyConditionRuleLineProcessor emptyConditionRuleLineProcessor;

  @Mock
  private MultiSplitRuleLineProcessor multiSplitRuleLineProcessor;

  @Test
  public void test_getHandlerMultiConditionProcessor() {
    List<WorkflowStep> workflowStepList = new ArrayList<>();
    workflowStepList.add(addWorkflowStep());
    RuleLineProcessor ruleLineProcessor = ruleLineProcessorFactory.getHandler(false, workflowStepList);
    Assert.assertTrue(ruleLineProcessor instanceof MultiConditionRuleLineProcessor);
  }

  @Test
  public void test_getHandler() {
    List<WorkflowStep> workflowStepList = new ArrayList<>();
    workflowStepList.add(addWorkflowStep());
    RuleLineProcessor ruleLineProcessor = ruleLineProcessorFactory.getHandler(true, workflowStepList);
    Assert.assertTrue(ruleLineProcessor instanceof EmptyConditionRuleLineProcessor);
  }

  @Test
  public void test_getHandlerNullStep() {
    RuleLineProcessor ruleLineProcessor = ruleLineProcessorFactory.getHandler(true, null);
    Assert.assertTrue(ruleLineProcessor instanceof EmptyConditionRuleLineProcessor);
  }

  @Test
  public void test_getHandlerMultiSplit() {
    List<WorkflowStep> workflowStepList = new ArrayList<>();
    workflowStepList.add(addWorkflowStep());
    workflowStepList.add(addWorkflowStep());
    RuleLineProcessor ruleLineProcessor = ruleLineProcessorFactory.getHandler(true, workflowStepList);
    Assert.assertTrue(ruleLineProcessor instanceof MultiSplitRuleLineProcessor);
  }

  private WorkflowStep addWorkflowStep() {
    WorkflowStep workflowStep = new WorkflowStep();
    workflowStep.setId(GlobalId.builder().setLocalId("step").setTypeId("step").setRealmId("11").build());
    return workflowStep;
  }


}
