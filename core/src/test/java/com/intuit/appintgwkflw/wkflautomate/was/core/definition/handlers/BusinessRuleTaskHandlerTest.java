package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.ListTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.v4.workflows.Definition;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Import({WorkflowGlobalConfiguration.class, CustomWorkflowConfig.class, CustomWorkflowConfigFactory.class , MigratedConfig.class, OldCustomWorkflowConfig.class,
    CustomConfigV2.class})
@RunWith(SpringRunner.class)
public class BusinessRuleTaskHandlerTest {
  private BusinessRuleTaskHandler businessRuleTaskHandler;
  @Autowired private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  @Autowired private CustomWorkflowConfig customWorkflowConfig;

  @MockBean
  private FeatureFlagManager featureFlagManager;

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapproval.bpmn");
  private static final String DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");
  private static final String DMN_XML_NOT_CONTAINS =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval_v1.dmn");
  private static final String DMN_CUSTOM_FIELD_XML =
      TestHelper.readResourceAsString("dmn/decision_invoiceReminderWithCustomFields.dmn");
  private static final String DMN_CUSTOM_FIELD_For_Or_XML =
          TestHelper.readResourceAsString("dmn/decision_invoiceReminderWithCustomFieldsForOr.dmn");
  private static final String BPMN_WITH_CUSTOM_FIELD =
      TestHelper.readResourceAsString("bpmn/invoiceReminderWithCustomField.bpmn");
  private static final String BPMN_WITH_CUSTOM_FIELD_NOT_CONTAINS =
      TestHelper.readResourceAsString("bpmn/invoiceReminderWithCustomField_NOT_CONTAINS.bpmn");
  private static final String DMN_WITH_CUSTOM_FIELD_NOT_CONTAINS =
      TestHelper.readResourceAsString("dmn/decision_invoiceReminderWithCF_NOT_Contains.dmn");
  private static final String BPMN_CUSTOM_WORKFLOW_BILL =
      TestHelper.readResourceAsString("bpmn/customapprovalbill.bpmn");
  private static final String DMN_CUSTOM_WORKFLOW_BILL =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");
  private BpmnModelInstance bpmnWithCustomField;
  private DmnModelInstance dmnWithCustomField;
  private DmnModelInstance dmnWithCustomFieldForOr;
  private Definition definitionWithCustomRules;
  private Definition definitionWithCustomRulesForOr;
  private BpmnModelInstance bpmnWithCustomFieldNotContains;
  private DmnModelInstance dmnWithCustomFieldNotContains;
  private Definition definitionWithCustomRulesNotContains;

  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    businessRuleTaskHandler =
        new BusinessRuleTaskHandler(
            workflowGlobalConfiguration, customWorkflowConfig, featureFlagManager);
    // MockitoAnnotations.initMocks(this);
    bpmnWithCustomField = Bpmn.readModelFromStream(IOUtils.toInputStream(BPMN_WITH_CUSTOM_FIELD));
    dmnWithCustomField = Dmn.readModelFromStream(IOUtils.toInputStream(DMN_CUSTOM_FIELD_XML));
    dmnWithCustomFieldForOr=Dmn.readModelFromStream(IOUtils.toInputStream(DMN_CUSTOM_FIELD_For_Or_XML));
    bpmnWithCustomFieldNotContains =
        Bpmn.readModelFromStream(IOUtils.toInputStream(BPMN_WITH_CUSTOM_FIELD_NOT_CONTAINS));
    dmnWithCustomFieldNotContains =
        Dmn.readModelFromStream(IOUtils.toInputStream(DMN_WITH_CUSTOM_FIELD_NOT_CONTAINS));
    definitionWithCustomRules = TestHelper.mockDefinitionEntityWithCustomFieldNew();
    definitionWithCustomRules.getTemplate().setCategory("CUSTOM");
    definitionWithCustomRulesForOr = TestHelper.mockDefinitionEntityWithCustomFieldNewForOr();
    definitionWithCustomRulesForOr.getTemplate().setCategory("CUSTOM");
    definitionWithCustomRulesNotContains =
        TestHelper.mockDefinitionEntityWithCustomFieldNotContains();
    definitionWithCustomRulesNotContains.getTemplate().setCategory("CUSTOM");
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST, new ListTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithContains_forJUEL() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount > 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")",
        inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithContains_forFEEL() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("> 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\",\"2\",\"3\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithContainsForOrforJuel() {
    Definition definition = TestHelper.mockDefinitionEntityNewForOr();
    DmnModelInstance dmnModelInstance =
            Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
            definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
            Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
            Collections.singletonList(dmnModelInstance),
            DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
            definition, false);
    List<Rule> rules =
            new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount > 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
            "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")",
            inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
    inputEntries=new ArrayList<>(rules.get(1).getInputEntries());
    Assert.assertEquals("Amount > 50", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
            "Customer.equals(\"2\") || Customer.equals(\"3\") || Customer.equals(\"4\")",
            inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"2\")", inputEntries.get(2).getTextContent());

  }
  @Test
  public void testBusinessRulesProcessing_RulesWithContainsForOrforFeel() {
    Definition definition = TestHelper.mockDefinitionEntityNewForOr();
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("> 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\",\"2\",\"3\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
    inputEntries=new ArrayList<>(rules.get(1).getInputEntries());
    Assert.assertEquals("> 50", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"2\",\"3\",\"4\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"2\"], Department)", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithContains_juel() {
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    businessRuleTaskHandler.process(
            definitionWithCustomRules.getWorkflowSteps().get(0).getWorkflowStepCondition(),
            bpmnWithCustomField,
            Collections.singletonList(dmnWithCustomField),
            DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
            definitionWithCustomRules, false);
    List<Rule> rules =
            new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomField).getRules());
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals(
            "CF36000000000001557156.equals(\"1\") || CF36000000000001557156.equals(\"2\") || CF36000000000001557156.equals(\"3\")",
            inputEntries.get(0).getTextContent());
    Assert.assertEquals("CF3600000000000155715 > 500", inputEntries.get(1).getTextContent());

  }
  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithContains_feel() {
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    businessRuleTaskHandler.process(
        definitionWithCustomRules.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        bpmnWithCustomField,
        Collections.singletonList(dmnWithCustomField),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definitionWithCustomRules, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomField).getRules());
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("\"1\",\"2\",\"3\"", inputEntries.get(0).getTextContent());
    Assert.assertEquals("> 500", inputEntries.get(1).getTextContent());

  }
  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithContainsForOrforjuel() {
    businessRuleTaskHandler.process(
            definitionWithCustomRulesForOr.getWorkflowSteps().get(0).getWorkflowStepCondition(),
            bpmnWithCustomField,
            Collections.singletonList(dmnWithCustomFieldForOr),
            DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
            definitionWithCustomRulesForOr, false);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    List<Rule> rules =
            new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomFieldForOr).getRules());
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals(
            "CF3600000000000155716.equals(\"1\") || CF3600000000000155716.equals(\"2\") || CF3600000000000155716.equals(\"3\")",
            inputEntries.get(0).getTextContent());
    Assert.assertEquals("CF3600000000000155715 > 500", inputEntries.get(1).getTextContent());
    inputEntries = new ArrayList<>(rules.get(1).getInputEntries());
    Assert.assertEquals(
            "CF3600000000000155718.equals(\"1\") || CF3600000000000155718.equals(\"2\")",
            inputEntries.get(3).getTextContent());
    Assert.assertEquals("CF3600000000000155717 > 50", inputEntries.get(2).getTextContent());

  }
  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithContainsForOrforfeel() {
    businessRuleTaskHandler.process(
        definitionWithCustomRulesForOr.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        bpmnWithCustomField,
        Collections.singletonList(dmnWithCustomFieldForOr),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definitionWithCustomRulesForOr, true);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomFieldForOr).getRules());
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("\"1\",\"2\",\"3\"", inputEntries.get(0).getTextContent());
    Assert.assertEquals("> 500", inputEntries.get(1).getTextContent());
    inputEntries = new ArrayList<>(rules.get(1).getInputEntries());
    Assert.assertEquals("\"1\",\"2\"", inputEntries.get(3).getTextContent());
    Assert.assertEquals("> 50", inputEntries.get(2).getTextContent());

  }

  @Test
  public void testDMNFriendlyExpressionWithSpaces() {
    String parameterName = "Customer";
    String type = "STRING";
    String expression = "CONTAINS a,b c, d";
    String result =
        businessRuleTaskHandler.getDMNFriendlyExpression(parameterName, expression, type, false);
    Assert.assertEquals(
        result, "Customer.equals(\"a\") || Customer.equals(\"b c\") || Customer.equals(\" d\")");
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithNotContainsforJuel() {
    Definition definition = TestHelper.mockDefinitionEntityNotContains();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST, new ListTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML_NOT_CONTAINS.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount > 500 && Amount < 1000", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "!Customer.equals(\"1\") && !Customer.equals(\"2\") && !Customer.equals(\"3\")",
        inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "!Department.equals(\"1\") || Department.equals(\"2\")",
        inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithNotContainsforfeel() {
    Definition definition = TestHelper.mockDefinitionEntityNotContains();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST, new ListTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML_NOT_CONTAINS.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("> 500 &&< 1000", inputEntries.get(0).getTextContent());
    Assert.assertEquals("not(\"1\",\"2\",\"3\")", inputEntries.get(1).getTextContent());
    Assert.assertEquals("\"2\"", inputEntries.get(2).getTextContent());
  }

  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithNotContains() {
    businessRuleTaskHandler.process(
            definitionWithCustomRulesNotContains.getWorkflowSteps().get(0).getWorkflowStepCondition(),
            bpmnWithCustomFieldNotContains,
            Collections.singletonList(dmnWithCustomFieldNotContains),
            DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
            definitionWithCustomRulesNotContains, false);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST, new ListTransformer());
    List<Rule> rules =
            new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomFieldNotContains).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("TxnAmount > 100", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
            "!CF36000000000001557156.equals(\"1\") && !CF36000000000001557156.equals(\"2\") && !CF36000000000001557156.equals(\"3\")",
            inputEntries.get(1).getTextContent());
  }
  @Test
  public void testBusinessRulesWithCustomFieldsProcessing_RulesWithNotContains2() {
    businessRuleTaskHandler.process(
        definitionWithCustomRulesNotContains.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        bpmnWithCustomFieldNotContains,
        Collections.singletonList(dmnWithCustomFieldNotContains),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definitionWithCustomRulesNotContains, true);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST, new ListTransformer());
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnWithCustomFieldNotContains).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("> 100", inputEntries.get(0).getTextContent());
    Assert.assertEquals("not(\"1\",\"2\",\"3\")", inputEntries.get(1).getTextContent());
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithContainsAlljuel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override customer rule to contain all
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(1)
        .setConditionalExpression("CONTAINS ALL_Customer");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount > 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals("", inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
    //Customer is not a string but a list
    //Assert.assertEquals("Customer:SELECT_ALL", rules.get(0).getDescription().getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithContainsAllfeel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override customer rule to contain all
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(1)
        .setConditionalExpression("CONTAINS ALL_Customer");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("> 500", inputEntries.get(0).getTextContent());
    Assert.assertEquals("", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
    //Customer is not a string but a list
    //Assert.assertEquals("Customer:SELECT_ALL", rules.get(0).getDescription().getTextContent());
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperatorJuel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(0)
        .setConditionalExpression("BTW 100,20000");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount >= 100 && Amount <= 20000", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")",
        inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperatorFeel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(0)
        .setConditionalExpression("BTW 100,20000");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("[100..20000]", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\",\"2\",\"3\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperator_RangeJuel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(0)
        .setConditionalExpression("BTW 20000,100");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount >= 100 && Amount <= 20000", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")",
        inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperator_RangeFeel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(0)
        .setConditionalExpression("BTW 20000,100");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("[100..20000]", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\",\"2\",\"3\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
  }

  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperator_ValueTypeDoubleJuel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getRuleLines()
            .get(0)
            .getRules()
            .get(0)
            .setConditionalExpression("BTW 100,2000.75");
    DmnModelInstance dmnModelInstance =
            Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
            definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
            Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
            Collections.singletonList(dmnModelInstance),
            DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
            definition, false);
    List<Rule> rules =
            new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("Amount >= 100 && Amount <= 2000.75", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
            "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")",
            inputEntries.get(1).getTextContent());
    Assert.assertEquals("Department.equals(\"1\")", inputEntries.get(2).getTextContent());
  }
  @Test
  public void testBusinessRulesProcessing_RulesWithBTWOperator_ValueTypeDoublefeel() {
    Definition definition = TestHelper.mockDefinitionEntityNew();
    // Override amount rule to BTW
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .getRuleLines()
        .get(0)
        .getRules()
        .get(0)
        .setConditionalExpression("BTW 100,2000.75");
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()));
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(dmnModelInstance),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
    List<Rule> rules =
        new ArrayList<>(businessRuleTaskHandler.getDecisionTable(dmnModelInstance).getRules());
    // Get the first rule's input entries
    List<InputEntry> inputEntries = new ArrayList<>(rules.get(0).getInputEntries());
    Assert.assertEquals("[100..2000.75]", inputEntries.get(0).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\",\"2\",\"3\"], Customer)", inputEntries.get(1).getTextContent());
    Assert.assertEquals(
        "containsAnyElement([\"1\"], Department)", inputEntries.get(2).getTextContent());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcess_FailureJuel() {
    Definition definition = TestHelper.mockDefinitionEntity();
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(
            Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()))),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, false);
  }
  @Test(expected = WorkflowGeneralException.class)
  public void testProcess_Failurefeel() {
    Definition definition = TestHelper.mockDefinitionEntity();
    businessRuleTaskHandler.process(
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition(),
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes())),
        Collections.singletonList(
            Dmn.readModelFromStream(new ByteArrayInputStream(DMN_XML.getBytes()))),
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build(),
        definition, true);
  }
}