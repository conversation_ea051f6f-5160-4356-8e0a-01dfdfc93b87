package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CommandUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;

public class CommandUtilTest {

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private DataStoreDeleteTaskService dataStoreDeleteTaskService;
  @InjectMocks private CommandUtil commandUtil;

  private Definition definition = TestHelper.mockDefinitionEntity();
  private static final String REALM_ID = "12345";
  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void prepareCamundaAndDatastoreTasks() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);

    List<Task> camundaDeleteDefinitionTaskList = new ArrayList<>();
    List<Task> dataStoreDeleteDefinitionAndProcessTaskList = new ArrayList<>();

    dataStoreDeleteDefinitionAndProcessTaskList.add(
        new DataStoreDeleteDefinitionAndProcessTask(
            definitionDetailsRepository,definitionDetail.getDefinitionId(),
            dataStoreDeleteTaskService));

    camundaDeleteDefinitionTaskList.add(
        new CamundaDeleteDefinitionTask(
            bpmnEngineDefinitionServiceRest, definitionDetail.getDefinitionId(), true, true));

    Pair<List<Task>, List<Task>> resultList =
        commandUtil.prepareCamundaAndDatastoreTasks(
            Collections.singletonList(definitionDetail), new State());
    Assert.assertNotNull(resultList);
    Assert.assertEquals(camundaDeleteDefinitionTaskList.size(), resultList.getLeft().size());
    Assert.assertEquals(dataStoreDeleteDefinitionAndProcessTaskList.size(), resultList.getRight().size());
    Assert.assertEquals(CamundaDeleteDefinitionTask.class, resultList.getLeft().get(0).getClass());
    Assert.assertEquals(DataStoreDeleteDefinitionAndProcessTask.class, resultList.getRight().get(0).getClass());
  }

  @Test
  public void testPrepareCamundaTasks() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);

    List<Task> camundaDeleteDefinitionTaskList = new ArrayList<>();

    camundaDeleteDefinitionTaskList.add(
        new CamundaDeleteDefinitionTask(
            bpmnEngineDefinitionServiceRest, definitionDetail.getDefinitionId(), true, true));

    List<Task> resultList =
        commandUtil.prepareCamundaTasks(
            Collections.singletonList(definitionDetail),true);
    Assert.assertNotNull(resultList);
  }

  @Test
  public void prepareCamundaAndDatastoreTasksEmptyDefinition() {

    Pair<List<Task>, List<Task>> resultList =
            commandUtil.prepareCamundaAndDatastoreTasks(
                    null, new State());
    Assert.assertNotNull(resultList);
    Assert.assertEquals(0, resultList.getRight().size());
    Assert.assertEquals(0, resultList.getLeft().size());
  }

  @Test
  public void testPrepareCamundaTasksEmptyDefinition() {

    List<Task> resultList =
            commandUtil.prepareCamundaTasks(
                    null,true);
    Assert.assertEquals(0, resultList.size());
  }
}
