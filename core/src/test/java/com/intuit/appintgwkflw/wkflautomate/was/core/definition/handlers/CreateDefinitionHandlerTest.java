package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.PlaceholderExtractorProvider;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.PrecannedDefinitionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ParameterDetailsConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Definition;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BoundaryEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@Import(CreateDefinitionHandler.class)
@RunWith(SpringRunner.class)
public class CreateDefinitionHandlerTest {
  private Definition definition;
  @Autowired private CreateDefinitionHandler createDefinitionHandler;
  @MockBean private ConditionalElementFactory conditionalElementFactory;
  @MockBean private PlaceholderExtractorProvider placeholderExtractorProvider;
  @MockBean private TranslationService translationService;
  @MockBean private WASContextHandler wasContextHandler;
  @MockBean private LookupKeysMapper lookupKeysMapper;
  @Mock private PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor;
  @Mock private ConditionalElementHandler conditionalElementHandler;
  @Captor private ArgumentCaptor<BpmnModelInstance> bpmnModelInstanceArgumentCaptor;
  @Captor private ArgumentCaptor<DefinitionId> definitionIdArgumentCaptor;
  @Captor private ArgumentCaptor<Definition> definitionArgumentCaptor;

  @MockBean private FeatureFlagManager featureFlagManager;

  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  @Rule public ExpectedException exceptionRule = ExpectedException.none();

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapprovalTest.bpmn");
  private static final String DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");
  private final static int FIRST_VALUE = 0;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    definition = TestHelper.mockDefinitionEntity();
    bpmnModelInstance = Bpmn.readModelFromStream(IOUtils.toInputStream(BPMN_XML));
    dmnModelInstance = Dmn.readModelFromStream(IOUtils.toInputStream(DMN_XML));
    Mockito.when(conditionalElementFactory.getHandler(any(BaseElement.class)))
        .thenReturn(conditionalElementHandler);
  }

  @Test
  public void testCreateDefinition_Success() {
    DefinitionInstance updatedDefinition = createDefinitionHandler.process(new DefinitionInstance(
                definition, bpmnModelInstance, Collections.singletonList(dmnModelInstance),
                TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build()),
            DefinitionTestConstants.REALM_ID);

    Assert.assertNotNull(updatedDefinition);
    Mockito.verify(conditionalElementHandler)
        .process(eq(definition.getWorkflowSteps().get(0).getWorkflowStepCondition()),
            bpmnModelInstanceArgumentCaptor.capture(), anyList(),
            definitionIdArgumentCaptor.capture(), definitionArgumentCaptor.capture(), eq(false));

    BpmnModelInstance bpmnModelInstance = bpmnModelInstanceArgumentCaptor.getValue();
    DefinitionId definitionId = definitionIdArgumentCaptor.getValue();
    assertTrigger(bpmnModelInstance, definitionId);
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_DMN));
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_XOR));

    Assert.assertNotNull(bpmnModelInstance.getModelElementById(
            MessageFormat.format("{0}_{1}_{2}", DefinitionTestConstants.CONDITION_ID_DMN,
                DefinitionTestConstants.REALM_ID, definitionId.getUniqueId())));

    Assert.assertNotNull(bpmnModelInstance.getModelElementById(
            MessageFormat.format("{0}_{1}_{2}", DefinitionTestConstants.CONDITION_ID_XOR,
                DefinitionTestConstants.REALM_ID, definitionId.getUniqueId())));
    assertAction(bpmnModelInstance, definitionId);
  }

  private void assertTrigger(BpmnModelInstance bpmnModelInstance, DefinitionId definitionId) {
    Assert.assertNull(bpmnModelInstance.getModelElementById(DefinitionTestConstants.TRIGGER_ID));

    BoundaryEvent boundaryEvent = bpmnModelInstance.getModelElementById(MessageFormat.format(
                "{0}_{1}_{2}", DefinitionTestConstants.TRIGGER_ID,
                DefinitionTestConstants.REALM_ID, definitionId.getUniqueId()));
    Assert.assertNotNull(boundaryEvent);
    // check for trigger if parameter is replaced
    Map<String, String> parameters =
        BpmnProcessorUtil.getMapOfCamundaProperties(boundaryEvent.getExtensionElements());
    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
        SchemaDecoder.getParametersForUI(parameters);
    Assert.assertEquals("10",
        parameterDetailsMap
            .get()
            .get(ParameterDetailsConstants.WAIT_TIME.getValue())
            .getFieldValue()
            .get(0));
  }

  private void assertAction(BpmnModelInstance bpmnModelInstance, DefinitionId definitionId) {
    Assert.assertNull(bpmnModelInstance.getModelElementById(DefinitionTestConstants.ACTION_ID));
    BaseElement actionElement = bpmnModelInstance.getModelElementById(MessageFormat.format(
                "{0}_{1}_{2}", DefinitionTestConstants.ACTION_ID,
                DefinitionTestConstants.REALM_ID, definitionId.getUniqueId()));
    Assert.assertNotNull(actionElement);
    // check for action if parameter is replaced
    Map<String, String> parameters =
        BpmnProcessorUtil.getMapOfInputOutputParameters(actionElement.getExtensionElements());
    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
        SchemaDecoder.getParametersForUI(parameters);
    Assert.assertEquals("ADMIN_TEST", parameterDetailsMap.get().get("To").getFieldValue().get(0));
  }

  @Test
  public void testProcessTriggerAndWorkflow() {
    Mockito.when(placeholderExtractorProvider.getPlaceholderExtractor(Mockito.any())).thenReturn(
             precannedDefinitionPlaceholderExtractor);
    Mockito.when(
            precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(
                Mockito.any()))
        .thenReturn(Collections.EMPTY_MAP);
    DefinitionInstance definitionInstance = new DefinitionInstance(
            definition, bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.getDefinition().getWorkflowSteps().get(FIRST_VALUE).setTrigger(null);
    definitionInstance.getDefinition().getWorkflowSteps().get(FIRST_VALUE).setWorkflowStepCondition(null);
    DefinitionInstance updatedDefinition = createDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);
    Assert.assertNotNull(updatedDefinition);
  }


  @Test
  public void removeParametersWithEmptyFieldValuesWithFFEnabled() {

    Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails processVariableParam = new HandlerDetails.ParameterDetails();
    processVariableParam.setValueType(ParameterDetailsValueType.PROCESS_VARIABLE);
    processVariableParam.setHandlerFieldName("processVariableParam");
    processVariableParam.setFieldType("string");
    parameterDetailsMap.put("processVariableParam", processVariableParam);
    Assert.assertEquals(1, createDefinitionHandler.removeParametersWithEmptyFieldValues(parameterDetailsMap).size());

    HandlerDetails.ParameterDetails emptyFieldValueParam = new HandlerDetails.ParameterDetails();
    emptyFieldValueParam.setFieldValue(List.of(""));
    emptyFieldValueParam.setFieldType("string");
    parameterDetailsMap.put("emptyVariableParam", emptyFieldValueParam);
    Assert.assertEquals(1, createDefinitionHandler.removeParametersWithEmptyFieldValues(parameterDetailsMap).size());

    HandlerDetails.ParameterDetails nullListFieldValueParam = new HandlerDetails.ParameterDetails();
    List<String> fieldValues = new ArrayList<>();
    fieldValues.add(null);
    nullListFieldValueParam.setFieldValue(fieldValues);
    nullListFieldValueParam.setFieldType("string");
    parameterDetailsMap.put("nullListFieldValueParam", nullListFieldValueParam);
    Assert.assertEquals(1, createDefinitionHandler.removeParametersWithEmptyFieldValues(parameterDetailsMap).size());

    HandlerDetails.ParameterDetails nullFieldValueParam = new HandlerDetails.ParameterDetails();
    nullFieldValueParam.setFieldValue(null);
    nullFieldValueParam.setFieldType("string");
    parameterDetailsMap.put("nullFieldValueParam", nullFieldValueParam);
    Assert.assertEquals(1, createDefinitionHandler.removeParametersWithEmptyFieldValues(parameterDetailsMap).size());

    HandlerDetails.ParameterDetails normalVariableParam = new HandlerDetails.ParameterDetails();
    normalVariableParam.setFieldValue(List.of("test"));
    normalVariableParam.setFieldType("string");
    parameterDetailsMap.put("normalVariableParam", normalVariableParam);
    Assert.assertEquals(2, createDefinitionHandler.removeParametersWithEmptyFieldValues(parameterDetailsMap).size());
  }

}
