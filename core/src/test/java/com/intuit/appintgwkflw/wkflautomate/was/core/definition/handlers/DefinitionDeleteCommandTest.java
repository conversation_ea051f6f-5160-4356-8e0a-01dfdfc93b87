package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionDeleteCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DefinitionDeleteCommandTest {

  @InjectMocks private DefinitionDeleteCommand command;

  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private TemplateDetails bpmnTemplateDetail = TestHelper.mockTemplateDetails("TEST");

  @Mock private TriggerDetailsRepository triggerDetailsRepository;

  @Mock private FeatureManager featureManager;

  @Captor private ArgumentCaptor<CorrelateAllMessage> argumentCaptor;

  @Captor private ArgumentCaptor<CorrelateAllMessageAsync> asyncArgumentCaptor;

  private Definition definition = TestHelper.mockDefinitionEntity();
  private static final String REALM_ID = "12345";
  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(Optional.empty());
    when(triggerDetailsRepository.findByTemplateDetails(bpmnTemplateDetail))
        .thenReturn(
            Optional.of(
                Collections.singletonList(
                    TriggerDetails.builder().triggerName("deleted_voided_disable").build())));
  }

  @Test
  public void testSingleDelete() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(argumentCaptor.capture());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys().get(CorrelationKeysEnum.DEFINITION_KEY.getName()));
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest)
          .correlateAllMessageAsync(asyncArgumentCaptor.capture());
      Assert.assertNotNull(asyncArgumentCaptor.getValue().getProcessInstanceQuery());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getName(),
          CorrelationKeysEnum.DEFINITION_KEY.getName());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getValue(),
          definitionDetail.getDefinitionKey());
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testSingleDeleteWithoutTriggerDetails() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(
            Optional.of(
                Collections.singletonList(
                    TriggerDetails.builder().triggerName("disable_event").build())));

    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testCorrelateFail() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);
    when(bpmnEngineRunTimeServiceRest.correlateAllMessage(any()))
        .thenThrow(new WorkflowGeneralException(new RuntimeException("Error")));
    try {
      command.execute(definitionInstance, REALM_ID);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Assert.assertEquals("Error", e.getCause().getMessage());
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(any()))
        .thenThrow(new WorkflowGeneralException(new RuntimeException("Error")));
    try {
      command.execute(definitionInstance, REALM_ID);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
      Assert.assertEquals("Error", e.getCause().getMessage());
    }
  }

  @Test
  public void testMultipleDelete() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetailsList(definitionDetailsList);
    definitionInstance.setDefinitionDetails(definitionDetail);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testCustomDelete() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(
            definition, TestHelper.mockTemplateDetails("CUSTOM"), authorization
        );
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(argumentCaptor.capture());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys().get(CorrelationKeysEnum.DEFINITION_KEY.getName()));
      Assert.assertEquals(argumentCaptor.getValue().getMessageName(), CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest)
          .correlateAllMessageAsync(asyncArgumentCaptor.capture());
      Assert.assertNotNull(asyncArgumentCaptor.getValue().getProcessInstanceQuery());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getName(),
          CorrelationKeysEnum.DEFINITION_KEY.getName());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getValue(),
          definitionDetail.getDefinitionKey());
      Assert.assertEquals(
          argumentCaptor.getValue().getMessageName(), CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testTriggerNotFoundForNonCustomDefinitionDelete() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    TemplateDetails sampleTemplateDetails = TestHelper.mockTemplateDetails("SAMPLE");
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, sampleTemplateDetails, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

}
