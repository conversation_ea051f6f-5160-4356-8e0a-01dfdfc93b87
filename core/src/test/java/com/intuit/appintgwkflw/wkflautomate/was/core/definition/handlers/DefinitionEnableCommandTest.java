package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.SchedulingSvcException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionEnableCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

/** <AUTHOR> */
public class DefinitionEnableCommandTest {

  @InjectMocks private DefinitionEnableCommand command;

  @Mock private AppConnectService appConnectService;

  @Mock private AuthDetailsService authDetailsService;

  @Mock private EventScheduleHelper eventScheduleHelper;

  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  @Mock private SchedulingService schedulingService;

  private Definition definition = TestHelper.mockDefinitionEntity();
  private static final String REALM_ID = "12345";
  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  @Mock private TemplateDetails bpmnTemplateDetail;

  private UpdateEventScheduleTask updateStatusEventSchedulerTask;
  private UpdateEventSchedulingTask updateEventSchedulingTask;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    AppConnectSaveWorkflowResponse workflowResponse = new AppConnectSaveWorkflowResponse();

    Mockito.when(
            appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenReturn(workflowResponse);

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID)))
        .thenReturn(authDetails);
     updateStatusEventSchedulerTask =
        Mockito.mock(UpdateEventScheduleTask.class);
    Mockito.when(updateStatusEventSchedulerTask.execute(Mockito.any()))
        .thenReturn(Mockito.mock(State.class));
    Mockito.when(eventScheduleHelper.prepareScheduleStatusUpdateTask(Mockito.any(), Mockito.any()))
        .thenReturn(null);
    Mockito.when(bpmnTemplateDetail.getTemplateName()).thenReturn("workflow");
    updateEventSchedulingTask = mock(UpdateEventSchedulingTask.class);
    when(updateEventSchedulingTask.execute(any())).thenReturn(mock(State.class));
    when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(false);
  }

  @Test
  public void testEnable() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testEnable_WithEmptyWorkflowId() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    definitionInstance.getDefinitionDetails().setWorkflowId(null);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testEnableWithExceptionAlreadyActive() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setDefinitionDetailsList(Collections.singletonList(definitionDetail));
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);

    Mockito.when(
        appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenThrow(new WorkflowGeneralException(
            WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, "dId",
            WorkflowConstants.WORKFLOW_ALREADY_ACTIVE
        ));

    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEnableWithRunTimeException() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, bpmnTemplateDetail);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);

    Mockito.when(
            appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenThrow(new NullPointerException("error"));

    command.execute(definitionInstance, REALM_ID);
  }

  @Test
  public void testUpdateEventScheduleTaskNotExecuted() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    command.execute(definitionInstance, REALM_ID);
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(0)).execute(Mockito.any());
  }

  @Test
  public void testUpdateEventScheduleTaskExecuted() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateStatusEventSchedulerTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    command.execute(definitionInstance, REALM_ID);
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(1)).execute(Mockito.any());
  }

  @Test
  public void testUpdateEventScheduleTaskExecutedWithSchedulingFlow() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateEventSchedulingTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    command.execute(definitionInstance, REALM_ID);
    Mockito.verify(updateEventSchedulingTask, Mockito.times(1)).execute(Mockito.any());
  }

  @Test
  public void testUpdateEventScheduleTaskExecutedWithMigrationFlow() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateEventSchedulingTask);
    updateEventScheduleTasks.add(updateStatusEventSchedulerTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    command.execute(definitionInstance, REALM_ID);
    Mockito.verify(updateEventSchedulingTask, Mockito.times(1)).execute(Mockito.any());
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(1)).execute(Mockito.any());
  }

  @Test
  public void testUpdateEventScheduleTaskExecutedWithSchedulingFlowException() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    when(schedulingService.isEnabled((DefinitionDetails) any(), any())).thenReturn(true);
    when(eventScheduleHelper.prepareSchedulingUpdateTask(any(State.class), any(SchedulingMetaData.class), any(boolean.class))).thenThrow(new SchedulingSvcException(WorkflowError.SCHEDULING_SERVICE_CALL_FAILURE, "code", "msg"));
    try{
      command.execute(definitionInstance, REALM_ID);
    }catch (SchedulingSvcException e){
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.SCHEDULING_SERVICE_CALL_FAILURE);
    }


  }
}
