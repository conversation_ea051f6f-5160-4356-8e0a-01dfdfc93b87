package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DeleteAllWorkflowsCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CommandUtil;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.async.execution.Task;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.tuple.Pair;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DeleteAllWorkflowsCommandTest {

  @InjectMocks private DeleteAllWorkflowsCommand command;

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @Mock private ProcessDetailsRepository processDetailsRepository;

  @Mock private AuthDetailsRepository authDetailsRepository;

  @Mock private AppConnectService appConnectService;

  @Mock private AuthDetailsService authDetailsService;

  @Mock private TemplateDetails bpmnTemplateDetail;

  @Mock private MetricLogger metricLogger;
  
  @Mock private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  @Mock private CommandUtil commandUtil;
  private Definition definition = TestHelper.mockDefinitionEntity();
  private static final String REALM_ID = "12345";
  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    definition.setId(TestHelper.getGlobalId(DEF_ID));
  }

  @Test
  public void testWithDefn() {
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance();
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);

    List<Task> camundaDeleteDefinitionTaskList = new ArrayList<>();
    List<Task> dataStoreDeleteDefinitionAndProcessTaskList = new ArrayList<>();

    Pair<List<Task>, List<Task>> taskListPair =
        Pair.of(camundaDeleteDefinitionTaskList, dataStoreDeleteDefinitionAndProcessTaskList);
    Mockito.when(commandUtil.prepareCamundaAndDatastoreTasks(Mockito.any(), Mockito.any()))
        .thenReturn(taskListPair);

    dataStoreDeleteDefinitionAndProcessTaskList.add(
        new DataStoreDeleteDefinitionAndProcessTask(
            definitionDetailsRepository,
            definitionDetail.getDefinitionId(),
            dataStoreDeleteTaskService));

    camundaDeleteDefinitionTaskList.add(
        new CamundaDeleteDefinitionTask(
            bpmnEngineDefinitionServiceRest, definitionDetail.getDefinitionId(), true, true));
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testNoDefnDetails() {
    DefinitionInstance definitionInstance = new DefinitionInstance();
    List<DefinitionDetails> list = new ArrayList<>();
    definitionInstance.setDefinitionDetailsList(list);

    List<Task> camundaDeleteDefinitionTaskList = new ArrayList<>();
    List<Task> dataStoreDeleteDefinitionAndProcessTaskList = new ArrayList<>();

    Pair<List<Task>, List<Task>> taskListPair =
        Pair.of(camundaDeleteDefinitionTaskList, dataStoreDeleteDefinitionAndProcessTaskList);
    Mockito.when(commandUtil.prepareCamundaAndDatastoreTasks(Mockito.any(), Mockito.any()))
        .thenReturn(taskListPair);

    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
