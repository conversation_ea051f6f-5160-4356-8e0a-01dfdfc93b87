package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionDeleteCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DeleteDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.*;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Author: Nitin Gupta Date: 28/01/20 Description: {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DeleteDefinitionHandler}
 */
@RunWith(MockitoJUnitRunner.class)
public class DeleteDefinitionHandlerTest {

  @InjectMocks private DeleteDefinitionHandler deleteDefinitionHandler;
  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @Mock private TemplateDetails bpmnTemplateDetail;
  private static final String REALM_ID = "12345";
  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  private final Definition definition = TestHelper.mockDefinitionEntity();

  @Mock private DefinitionDeleteCommand command;

  @Before
  public void setup() {
    MockitoAnnotations.openMocks(this);
    Mockito.when(command.getName()).thenReturn(CrudOperation.DELETE.name());
    DefinitionCommands.addCommand(command.getName(), command);
  }

  @Test
  public void definitionIdNotSet() {
    try {
      deleteDefinitionHandler.process(definition, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_DEFINITION_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void templateNotFound() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_DETAILS));
    deleteDefinitionHandler.process(definition, authorization.getRealm());
  }

  @Test
  public void fetchDefinitionInvoked() {

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    Mockito.when(definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetails);

    Definition def = deleteDefinitionHandler.process(definition, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(DEF_ID, def.getId().getLocalId());
    Mockito.verify(command).execute(any(), any());
  }

  @Test
  public void fetchCustomApprovalDefinitionInvoked() {

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);

    Mockito.when(definitionDetail.getTemplateDetails().getTemplateName()).thenReturn(
        CustomWorkflowType.APPROVAL.getTemplateName());

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    Mockito.when(definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetailsList);

    DefinitionDetails precannedDefinitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);

    String precannedDefId = "precanned-def-id";
    precannedDefinitionDetail.setDefinitionId(precannedDefId);

    Mockito.when(definitionServiceHelper.findStaleDefinitionForPrecannedTemplateAndRecordType(
                eq(REALM_ID), any(), any())
        ).thenReturn(List.of(precannedDefinitionDetail));

    Definition def = deleteDefinitionHandler.process(definition, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(DEF_ID, def.getId().getLocalId());

    Mockito.verify(command).execute(any(), any());
  }

  @Test
  public void testMultiConditionDefinitionDeletion() {

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    //as workflow id is null for multi condition definition
    definitionDetail.setWorkflowId(null);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);

    Mockito.when(definitionDetail.getTemplateDetails().getTemplateName()).thenReturn(
        CustomWorkflowType.APPROVAL.getTemplateName());

    Mockito.when(definitionServiceHelper.findByDefinitionId(
            TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    Mockito.when(definitionServiceHelper.getDefinitionListByDefinitionKeyAndOwnerId(
            definitionDetail.getDefinitionKey(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetailsList);

    Mockito.when(definitionServiceHelper.findStaleDefinitionForPrecannedTemplateAndRecordType(
        eq(REALM_ID), any(), any())
    ).thenReturn(Collections.emptyList());

    Definition def = deleteDefinitionHandler.process(definition, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(DEF_ID, def.getId().getLocalId());

    Mockito.verify(command).execute(any(), any());
  }

  @Test
  public void updateDefnSoftStatusFailed() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    Mockito.when(definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetails);

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.DELETE_DEFINITION_FAILED))
        .when(definitionServiceHelper)
        .updateInternalStatusAndPublishDomainEvent(definitionDetails, InternalStatus.MARKED_FOR_DELETE);

    try {
      deleteDefinitionHandler.process(definition, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.DELETE_DEFINITION_FAILED.getErrorMessage(), e.getMessage());
    }
  }
}
