package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import java.util.HashMap;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
public class MultiStepConditionBuilderTest {
    public static String APPROVAL_ACTION_KEY = "approval";
    public static String RECORD_TYPE_INVOICE = "invoice";
    @Mock
    private WASContextHandler wasContextHandler;
    @Mock
    private TemplateConditionBuilder conditionBuilder;
    @InjectMocks
    private MultiStepConditionBuilder multiStepConditionBuilder;
    private CustomWorkflowConfig customWorkflowConfig;

    @Before
    @SneakyThrows
    public void setUp() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        multiStepConditionBuilder = new MultiStepConditionBuilder(wasContextHandler, conditionBuilder);
    }

    @Test
    public void testProcessWorkflowStep() {
        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(StepTypeEnum.CONDITION.value());
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(NextTypeEnum.ACTION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
        Attribute txnAttr = new Attribute();
        txnAttr.setName("TxnAmount");
        txnAttr.setId("txnAmount");
        txnAttr.setType("DOUBLE");
        txnAttr.setDefaultValue("500");
        txnAttr.setDefaultOperator("GTE");
        configStep.setAttributes(Arrays.asList(txnAttr));
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        WorkflowStep result = multiStepConditionBuilder.processWorkflowStep(
                record,
                APPROVAL_ACTION_KEY,
                false,
                configStep,
                null,
            new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.CONDITION, result.getStepType());

        WorkflowStep.StepNext nextPath = new WorkflowStep.StepNext();
        nextPath.setWorkflowStepId(Integer.toString(2));
        result = multiStepConditionBuilder.processWorkflowStep(
                record,
                APPROVAL_ACTION_KEY,
                false,
                configStep,
                nextPath,
                new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.CONDITION, result.getStepType());
        Assert.assertNotNull(result.getId());
    }
}
