package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateTriggerBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@Import(WorkflowGlobalConfiguration.class)
public class ReadCustomDefinitionHandlerTest {
  private ReadCustomDefinitionHandler readCustomDefinitionHandler;

  @MockBean private WASContextHandler wasContextHandler;
  @MockBean private TemplateBuilder templateBuilder;
  @Autowired private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private BpmnProcessorImpl bpmnProcessor;
  private CustomWorkflowConfig customWorkflowConfig;
  @Spy private TranslationService translationService = TestHelper.initTranslationService();

  private static final String CUSTOM_REMINDER_BPMN = "bpmn/customWorkflowDefinition.bpmn";
  private static final String CUSTOM_REMINDER_DMN = "dmn/customWorkflowDefinition.dmn";
  private static final String CUSTOM_APPROVAL_BPMN = "bpmn/customapprovalbill.bpmn";
  private static final String CUSTOM_APPROVAL_DMN = "dmn/dmn_customapprovalbill.dmn";
  private static final String CUSTOM_APPROVAL_DMN_For_Or = "dmn/dmn_customapprovalbillforor.dmn";
  private static final String CUSTOM_SCHEDULE_ACTIONS_BPMN  = "bpmn/customScheduledActions.bpmn";
  private static final String CUSTOM_SCHEDULE_ACTIONS_DMN  = "bpmn/decision_customScheduledActions.dmn";

  private String REALM_ID = "realmId";
  private String LOCAL_ID = "localId";
  @Mock
  FeatureFlagManager featureFlagManager;


  private static BpmnModelInstance readBPMNFile() {
    return readBPMNFile(CUSTOM_REMINDER_BPMN);
  }


  private static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        ReadCustomDefinitionHandlerTest.class
            .getClassLoader()
            .getResourceAsStream(fileName));
  }

  private static DmnModelInstance readDMNFile() {
    return readDMNFile(CUSTOM_REMINDER_DMN);
  }

  private static DmnModelInstance readDMNFile(String fileName) {
    return Dmn.readModelFromStream(
        IOUtils.toInputStream(TestHelper.readResourceAsString(fileName), Charset.defaultCharset()));
  }

  private byte[] readBPMNAsBytes() throws IOException {
    try (InputStream fisBpmn =
        ReadCustomDefinitionHandlerTest.class
            .getClassLoader()
            .getResourceAsStream(CUSTOM_REMINDER_BPMN)) {
      return IOUtils.toByteArray(fisBpmn);
    }
  }

  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    TemplateActionBuilder templateActionBuilder = new TemplateActionBuilder(wasContextHandler,translationService);
    TemplateConditionBuilder templateConditionBuilder =
        new TemplateConditionBuilder(customWorkflowConfig, wasContextHandler,translationService, featureFlagManager);
    TemplateTriggerBuilder templateTriggerBuilder = new TemplateTriggerBuilder(wasContextHandler);
    readCustomDefinitionHandler =
        new ReadCustomDefinitionHandler(
            templateConditionBuilder, templateActionBuilder, templateTriggerBuilder, customWorkflowConfig);
    //Not the best way. We could use constructor injection instead of field injection
    bpmnProcessor = new BpmnProcessorImpl();
    ReflectionTestUtils
        .setField(bpmnProcessor, "readCustomDefinitionHandler", readCustomDefinitionHandler);
    ReflectionTestUtils
        .setField(bpmnProcessor, "workflowGlobalConfiguration", workflowGlobalConfiguration);
    ReflectionTestUtils.setField(bpmnProcessor, "translationService", translationService);
    ReflectionTestUtils.setField(bpmnProcessor, "wasContextHandler", wasContextHandler);
    ReflectionTestUtils.setField(bpmnProcessor, "templateBuilder", templateBuilder);
    Mockito.when(wasContextHandler.get(Mockito.any())).thenReturn("1234");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
  }

  private Template getTemplate(RecordType recordType) throws IOException {
    if (recordType == RecordType.BILL) {
      return getTemplate(recordType, "customApproval");
    }
    return getTemplate(recordType, "customReminder");
  }

  private Template getTemplate(RecordType recordType, String templateName) throws IOException {
    String OFFER_ID = "offerId";
    String OWNER_ID = "1234";
    String CREATED_BY = "1234";

    String DESCRIPTION = "description";

    TemplateDetails bpmnDetails =
        TemplateDetails.builder()
            .id(UUID.randomUUID().toString())
            .templateName(templateName)
                .recordType(RecordType.INVOICE)
            .modelType(ModelType.BPMN)
            .createdByUserId(Long.parseLong(CREATED_BY))
            .creatorType(CreatorType.SYSTEM)
            .offeringId(OFFER_ID)
            .description(DESCRIPTION)
            .status(Status.ENABLED)
            .recordType(recordType)
            .ownerId(Long.parseLong(OWNER_ID))
            .version(1)
            .parentId(null)
            .templateData(readBPMNAsBytes())
            .build();
    BpmnModelInstance bpmnModelInstance = null;
    DmnModelInstance dmnModelInstance = null;
    if (templateName.equalsIgnoreCase("customApproval")) {
      bpmnModelInstance = readBPMNFile(CUSTOM_APPROVAL_BPMN);
      dmnModelInstance = readDMNFile(CUSTOM_APPROVAL_DMN);
    } else if (templateName.equalsIgnoreCase("customScheduledActions")) {
      bpmnModelInstance = readBPMNFile(CUSTOM_SCHEDULE_ACTIONS_BPMN);
      dmnModelInstance = readDMNFile(CUSTOM_SCHEDULE_ACTIONS_DMN);
    } else if (templateName.equalsIgnoreCase("customApprovalForOr")) {
      bpmnModelInstance = readBPMNFile(CUSTOM_APPROVAL_BPMN);
      dmnModelInstance = readDMNFile(CUSTOM_APPROVAL_DMN_For_Or);
    } else {
      bpmnModelInstance = readBPMNFile();
      dmnModelInstance = readDMNFile();
    }
    List<DmnModelInstance> dmnModelInstances = Arrays.asList(dmnModelInstance);

    DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
            bpmnModelInstance,
            dmnModelInstances,
            bpmnDetails);
    Template template = new Template();
    template.setName(templateName);
    if(Objects.nonNull(recordType)) {
      template.setRecordType(recordType.getRecordType());
    } else {
      template.setRecordType(null);
    }
    Mockito.when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(template);
    Template t =
        (Template)
                bpmnProcessor.processBpmn(
                        definitionInstance,
                        GlobalId.create("REALM_ID", "LOCAL_ID"),
                        true);
    return t;
  }

  @Test
  public void testUpdateWorkflowStepsInReadCustomWorkflowDefinition() throws IOException {
    Template template = getTemplate(RecordType.INVOICE);
    Template approval_template= getTemplate(RecordType.BILL);
    List<Rule> rules =
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(0).getRules();
    // check the value of rule lines from DMN
    Assert.assertEquals(2, rules.size());
    Assert.assertEquals("Customer", rules.get(0).getParameterName());
    Assert.assertEquals("CONTAINS 2,4,1", rules.get(0).getConditionalExpression());
    Assert.assertEquals("TxnAmount", rules.get(1).getParameterName());
    Assert.assertEquals("BTW 100,200", rules.get(1).getConditionalExpression());

    List<ActionMapper> actions = template.getWorkflowSteps().get(0).getActions();
    Assert.assertEquals(7, actions.size());
    // assert the size of actions and parameters before it gets updated with config values
    Action action = getAction(actions, "createTask");
    Assert.assertNotNull(action);
    Assert.assertEquals(3, action.getParameters().size());
    Assert.assertTrue(action.isSelected());
    List<String> actualPrams =
        action.getParameters().stream()
            .map(InputParameter::getParameterName)
            .collect(Collectors.toList());
    List<String> expectedPrams = Arrays.asList("Assignee", "TaskName", "CloseTask");
    Assert.assertTrue(
        actualPrams.size() == expectedPrams.size() && actualPrams.containsAll(expectedPrams));
    InputParameter taskName =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("TaskName"))
            .findFirst()
            .get();
    Assert.assertEquals("Review Invoice [[Invoice Number]]", taskName.getFieldValues().get(0));

    InputParameter assignee =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("Assignee"))
            .findFirst()
            .get();
    Assert.assertEquals("9130350413749026", assignee.getFieldValues().get(0));

    action = getAction(actions, "sendExternalEmail");
    Assert.assertEquals(5, action.getParameters().size());
    Assert.assertEquals("Send a customer email", action.getName());
    Assert.assertTrue(action.isSelected());
    InputParameter subject =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("Subject"))
            .findFirst()
            .get();
    Assert.assertTrue(CollectionUtils.isEmpty(subject.getFieldValues()));

    action = getAction(actions, "sendCompanyEmail");
    Assert.assertEquals(5, action.getParameters().size());
    Assert.assertEquals("Send a company email", action.getName());
    Assert.assertFalse(action.isSelected());

    action = getAction(actions, "sendPushNotification");
    Assert.assertEquals(3, action.getParameters().size());
    Assert.assertEquals("Send a push notification", action.getName());
    Assert.assertFalse(action.isSelected());
    // Subject field value is not empty but h as help variables so return the fieldvalue after
    // replacing helpvariable
    subject =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("Subject"))
            .findFirst()
            .get();
    Assert.assertEquals(
        "A test invoice [[Invoice Number]] needs your attention", subject.getFieldValues().get(0));
    // SendTo field value is not empty so return it
    InputParameter sendTo =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("SendTo"))
            .findFirst()
            .get();
    Assert.assertEquals("[[CustomerEmail]]", sendTo.getFieldValues().get(0));
    // Message field value is empty so return default value  from config
    InputParameter message =
        action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("Message"))
            .findFirst()
            .get();
    Assert.assertEquals("Go to QuickBooks to view it.", message.getFieldValues().get(0));

    // check that conditional input parameters are added from config
    Assert.assertEquals(
        17,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
    actions= approval_template.getWorkflowSteps().get(0).getActions();
     action= getAction(actions,"sendCompanyEmail","approval");
     InputParameter cc= action.getParameters().stream()
            .filter(param -> param.getParameterName().equalsIgnoreCase("cc"))
             .findFirst().get();
     Assert.assertEquals(cc.getFieldValues().get(0),"my cc");

  }

  private Action getAction(List<ActionMapper> actions, String actionId) {
    return actions.stream()
        .filter(actionMapper -> actionMapper.getAction().getId().getLocalId().startsWith(actionId))
        .findFirst()
        .get()
        .getAction();
  }

  private Action getAction(List<ActionMapper> actions, String actionId, String actionKey) {
    return actions.stream()
        .filter(actionMapper -> actionMapper.getAction().getId().getLocalId().startsWith(actionId) &&
            actionMapper.getActionKey().equalsIgnoreCase(actionKey))
        .findFirst()
        .get()
        .getAction();
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUpdateWorkflowStepsException() throws IOException {
    // Give a record type which is not there in config
    Template t = getTemplate(RecordType.ENGAGEMENT);
    t.setRecordType(RecordType.ENGAGEMENT.getRecordType());
    readCustomDefinitionHandler.updateWorkflowStep(
        t.getName(), t.getRecordType(), t.getWorkflowSteps().get(0));
  }

  @Test
  public void testUpdateWorkflowStepsWithEmptyAction() throws IOException {
    Template t = getTemplate(RecordType.INVOICE);
    t.getWorkflowSteps().get(0).setActions(new ArrayList<>());
    WorkflowStep workflowStep =
        readCustomDefinitionHandler.updateWorkflowStep(
            t.getName(), t.getRecordType(), t.getWorkflowSteps().get(0));
    Assert.assertEquals(0,workflowStep.getActions().size());

  }

  @Test
  public void testUpdateWorkflowStepsForActionRequiredProperty() throws IOException {
    Template t = getTemplate(RecordType.BILL, "customApproval");
    // all actions required are overridden from config
    // create task is mandatory for approval, hence required is true
    Assert.assertEquals(true,getAction(t.getWorkflowSteps().get(0).getActions(), "createTask", "approval").isRequired());
    Assert.assertEquals(false,getAction(t.getWorkflowSteps().get(0).getActions(), "sendCompanyEmail", "approval").isRequired());
  }

  @Test
  public void testUpdateWorkflowStepsWithEmptyCondition() throws IOException {
    Template t = getTemplate(RecordType.INVOICE);
    t.getWorkflowSteps().get(0).setWorkflowStepCondition(null);
    WorkflowStep workflowStep =
        readCustomDefinitionHandler.updateWorkflowStep(
            t.getName(), t.getRecordType(), t.getWorkflowSteps().get(0));
    Assert.assertTrue(ObjectUtils.isEmpty(workflowStep.getWorkflowStepCondition()));
  }

  @Test
  public void testUpdateWithInvalidRecordType() throws IOException {
    try {
      Template template = getTemplate(null);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_RECORD_TYPE.getErrorMessage(),
          e.getMessage());
    }
  }

  @Test
  public void testUpdateWorkflowStepsConditionalExpressionTest(){
    String conditionalExpression = "GTE 100 && LTE 200";
    conditionalExpression = conditionalExpression.replace("&&", ",").replaceAll("[A-Z ]", "");
    conditionalExpression =
        WorkflowConstants.OPERATOR_BTW + WorkflowConstants.SPACE + conditionalExpression;
    Assert.assertEquals("BTW 100,200", conditionalExpression);
  }

  @Test
  public void testGetUpdatedCondition() throws IOException {
    Template template = getTemplate(RecordType.INVOICE);
    WorkflowStepCondition stepCondition =
        template.getWorkflowSteps().get(0).getWorkflowStepCondition();
    Rule useridRule = new Rule();
    useridRule.setParameterName(WorkflowConstants.INTUIT_USERID);
    useridRule.setConditionalExpression("CONTAINS 1234");
    stepCondition.getRuleLines(0).getRules().add(useridRule);
    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType());
    Assert.assertEquals("initial size of attribute list", 19, record.getAttributes().size());
    Assert.assertEquals(
        "size of attribute list post filtering of hidden attributes",
        17,
        stepCondition.getConditionalInputParameters().size());
    Assert.assertEquals(
        "initial size of rule list", 3, stepCondition.getRuleLines(0).getRules().size());
    WorkflowStepCondition updatedStepCondition =
        readCustomDefinitionHandler.getUpdatedCondition(template.getName(), stepCondition, record);
    Assert.assertEquals(
        "size of rule list post filtering of rules based on hidden attributes",
        2,
        stepCondition.getRuleLines(0).getRules().size());
  }

  @Test
  public void testGetUpdatedConditionCustomField() throws IOException {
    Template template = getTemplate(RecordType.INVOICE);
    template.setName("customApproval");
    WorkflowStepCondition stepCondition =
        template.getWorkflowSteps().get(0).getWorkflowStepCondition();
    Rule rulesWIthCustomField = new Rule();
    rulesWIthCustomField.setParameterName(
        WorkflowConstants.CUSTOM_FIELD_PREFIX + "3600000000000155715");
    rulesWIthCustomField.setParameterType(FieldTypeEnum.DOUBLE);
    rulesWIthCustomField.setConditionalExpression("GTE 1234");

    stepCondition.getRuleLines(0).getRules().add(rulesWIthCustomField);
    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType());
    Assert.assertEquals("initial size of attribute list", 19, record.getAttributes().size());
    Assert.assertEquals(
        "size of attribute list post filtering of hidden attributes",
        17,
        stepCondition.getConditionalInputParameters().size());
    Assert.assertEquals(
        "initial size of rule list", 3, stepCondition.getRuleLines(0).getRules().size());
    WorkflowStepCondition updatedStepCondition =
        readCustomDefinitionHandler.getUpdatedCondition(template.getName(), stepCondition, record);
    Assert.assertEquals(
        "size of rule list post filtering of rules based on hidden attributes",
        3,
        stepCondition.getRuleLines(0).getRules().size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetUpdatedConditionCustomFieldError() throws IOException {
    Template template = getTemplate(RecordType.INVOICE);
    WorkflowStepCondition stepCondition =
        template.getWorkflowSteps().get(0).getWorkflowStepCondition();
    Rule rulesWIthCustomField = new Rule();
    rulesWIthCustomField.setParameterName(
        WorkflowConstants.CUSTOM_FIELD_PREFIX + "3600000000000155715");
    rulesWIthCustomField.setConditionalExpression("GTE 1234");
    stepCondition.getRuleLines(0).getRules().add(rulesWIthCustomField);
    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType());
    WorkflowStepCondition updatedStepCondition =
        readCustomDefinitionHandler.getUpdatedCondition(template.getName(), stepCondition, record);
    Assert.assertEquals(
        "size of rule list post filtering of rules based on hidden attributes",
        3,
        stepCondition.getRuleLines(0).getRules().size());
  }

  @Test
  public void testReadOneDefinitionStatement() throws IOException {
    Template template = getTemplate(RecordType.STATEMENT, "customScheduledActions");

    List<Rule> rules =
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(0).getRules();
    // check the value of rule lines from DMN
    Assert.assertEquals(1, rules.size());
    Assert.assertEquals("Customer", rules.get(0).getParameterName());
    Assert.assertEquals("CONTAINS 1", rules.get(0).getConditionalExpression());

    List<ActionMapper> actions = template.getWorkflowSteps().get(0).getActions();
    Assert.assertEquals(1, actions.size());
    // assert the size of actions and parameters before it gets updated with config values
    Action action = getAction(actions, "scheduledAction");
    Assert.assertNotNull(action);

    // check that conditional input parameters are added from config
    Assert.assertEquals(
        2,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
  }
  @Test
  public void testReadOneDefinitionStatementForOr() throws IOException {
    Template template = getTemplate(RecordType.BILL_PAYMENT, "customApprovalForOr");
    List<Rule> rules =
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(0).getRules();
    List<Rule> rules1 =
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(1).getRules();
    List<Rule> rules2 =
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(2).getRules();
    List<Rule> rules3 =
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(3).getRules();
    List<Rule> rules4 =
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines(4).getRules();
    // check the value of rule lines from DMN
    Assert.assertEquals(2, rules.size());
    Assert.assertEquals(2, rules1.size());
    Assert.assertEquals(2, rules2.size());
    Assert.assertEquals(3, rules3.size());
    Assert.assertEquals(3, rules4.size());
    Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
    Assert.assertEquals("GTE 0", rules.get(0).getConditionalExpression());
    Assert.assertEquals("intuit_userid", rules.get(1).getParameterName());
    Assert.assertEquals("NOT_CONTAINS 9130358394535786", rules.get(1).getConditionalExpression());
    Assert.assertEquals("TxnAmount", rules1.get(0).getParameterName());
    Assert.assertEquals("GT 1", rules1.get(0).getConditionalExpression());
    Assert.assertEquals("CFTxnBalanceAmount", rules1.get(1).getParameterName());
    Assert.assertEquals("GT 17", rules1.get(1).getConditionalExpression());
    Assert.assertEquals("CFTxnBall", rules2.get(0).getParameterName());
    Assert.assertEquals("GT 0", rules2.get(0).getConditionalExpression());
    Assert.assertEquals("CFid", rules2.get(1).getParameterName());
    Assert.assertEquals("NOT_CONTAINS 123", rules2.get(1).getConditionalExpression());
    Assert.assertEquals("TxnAmount", rules3.get(0).getParameterName());
    Assert.assertEquals("GT 50", rules3.get(0).getConditionalExpression());
    Assert.assertEquals("intuit_userid", rules3.get(1).getParameterName());
    Assert.assertEquals("NOT_CONTAINS 9130358394535786", rules3.get(1).getConditionalExpression());
    Assert.assertEquals("CFTxnBalanceAmount", rules3.get(2).getParameterName());
    Assert.assertEquals("GT 0", rules3.get(2).getConditionalExpression());
    Assert.assertEquals("TxnAmount", rules4.get(0).getParameterName());
    Assert.assertEquals("GT 50", rules4.get(0).getConditionalExpression());
    Assert.assertEquals("CFid", rules4.get(2).getParameterName());
    Assert.assertEquals("NOT_CONTAINS 1234", rules4.get(2).getConditionalExpression());
    Assert.assertEquals("CFTxnBalanceAmount", rules4.get(1).getParameterName());
    Assert.assertEquals("GT 0", rules4.get(1).getConditionalExpression());


    Assert.assertEquals(
            5,
            template
                    .getWorkflowSteps()
                    .get(0)
                    .getWorkflowStepCondition()
                    .getConditionalInputParameters()
                    .size());
  }

  @Test
  public void testPopulateWorkflowStepWithActionGroup() {
    String templateName = "customApproval";
    String recordType = "invoice";
    com.intuit.v4.workflows.ActionGroup actionGroup = readCustomDefinitionHandler.populateWorkflowStepWithActionGroup(templateName, recordType);
    Assert.assertNotNull(actionGroup);
    Assert.assertNotNull(actionGroup.getAction());
    Assert.assertEquals("Send For Approval", actionGroup.getAction().getName());
    Assert.assertNotNull(actionGroup.getAction().getParameters());
    Assert.assertEquals(1, actionGroup.getAction().getParameters().size());
    Assert.assertNotNull(actionGroup.getAction().getSubActions());
    Assert.assertEquals(2, actionGroup.getAction().getSubActions().size());
    Assert.assertNotNull(actionGroup.getAction().getSubActions().get(0).getParameters());
    Assert.assertNotNull(actionGroup.getAction().getSubActions().get(1).getParameters());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testPopulateWorkflowStepWithActionGroupWithException() {
    String templateName = "customApproval";
    String recordType = "invalid";
    try {
      readCustomDefinitionHandler.populateWorkflowStepWithActionGroup(templateName, recordType);
      Assert.fail("Method should throw exception");
    } catch (WorkflowGeneralException error) {
      Assert.assertEquals(WorkflowError.INVALID_RECORD_TYPE, error.getWorkflowError());
      throw error;
    }
  }
}
