package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.LogicalOperatorsEnum;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepRuleLineBuilderTest {
    @Mock
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    @InjectMocks
    private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
    private static final String CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");
    private MultiStepRuleLineBuilder multiStepRuleLineBuilder;
    private DmnModelInstance dmnModelInstance;
    private CustomWorkflowConfig customWorkflowConfig;
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    @Mock
    private TemplateConditionBuilder templateConditionBuilder;
    @Mock
    private TemplateActionBuilder templateActionBuilder;
    @Mock
    private TemplateTriggerBuilder templateTriggerBuilder;
    private final String REALM_ID = "2376273672";
    private final String LOCAL_ID = "29187";
    @Mock
    private FeatureFlagManager featureFlagManager;

    @Before
    @SneakyThrows
    public void setup() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        dmnModelInstance = Dmn.readModelFromStream(
                IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
        readCustomDefinitionHandler = new ReadCustomDefinitionHandler(
            templateConditionBuilder,
            templateActionBuilder,
            templateTriggerBuilder,
            customWorkflowConfig);
        multiStepRuleLineBuilder = new MultiStepRuleLineBuilder(
            readCustomDefinitionHandler, featureFlagManager);
    }

    @Test
    public void testGenerateV4RuleLine() {
        DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));
        Record record = customWorkflowConfig.getRecordObjForType("invoice");
        Collection<DecisionTable> decisionTables = dmnModelInstance.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        LinkedList<Rule> decisionRules = new LinkedList<>(decisionTable.getRules());
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        List<RuleLine.Rule> rules = multiStepRuleLineBuilder.buildV4RuleLine(
            record,
            Arrays.asList(decisionRules.get(0)),
            GlobalId.create("2376273672", "29187"),
            dmnHeadersMap);
        Assert.assertNotNull(rules);
        Assert.assertEquals(FieldTypeEnum.DOUBLE, rules.get(0).getParameterType());
        Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
        Assert.assertEquals(">= 100", rules.get(0).getConditionalExpression());
        Assert.assertEquals(LogicalOperatorsEnum.AND, rules.get(0).getSelectedlogicalGroupingOperator());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testGenerateV4RuleLineWithException() {
        Record record = customWorkflowConfig.getRecordObjForType("invoice");
        Collection<DecisionTable> decisionTables = dmnModelInstance.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        List<Rule> decisionRules = new LinkedList<>(decisionTable.getRules());
        try {
            multiStepRuleLineBuilder.buildV4RuleLine(
                record,
                Arrays.asList(decisionRules.get(1)),
                GlobalId.create(REALM_ID, LOCAL_ID),
                null);
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException exception) {
            throw exception;
        }
    }

    @Test
    public void testGenerateV4RuleLineForNullRules() {
        Record record = customWorkflowConfig.getRecordObjForType("invoice");
        Collection<DecisionTable> decisionTables = dmnModelInstance.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        List<Rule> decisionRules = new LinkedList<>(decisionTable.getRules());
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        List<RuleLine.Rule> rules = multiStepRuleLineBuilder.buildV4RuleLine(
            record,
            Arrays.asList(decisionRules.get(1)),
            GlobalId.create(REALM_ID, LOCAL_ID),
            dmnHeadersMap);
        Assert.assertNotNull(rules);
        Assert.assertEquals(0, rules.size());
    }
}
