package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class TemplateActionBuilderTest {
  private TemplateActionBuilder actionMapper;
  private CustomWorkflowConfig customWorkflowConfig;

  @MockBean
  private WASContextHandler wasContextHandler;
  @Spy private TranslationService translationService = TestHelper.initTranslationService();

  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    actionMapper = new TemplateActionBuilder(wasContextHandler,translationService);
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    MockitoAnnotations.initMocks(this);

  }

  private Parameter getParameterFromConfig(String actionId, String parameterName) {
    Optional<Action> action =
        customWorkflowConfig.getOldConfig().getActions().stream()
            .filter(t -> t.getId().equalsIgnoreCase(actionId))
            .findFirst();
    if (action.isPresent()) {
      Optional<Parameter> parameter =
          action.get().getParameters().stream()
              .filter(t -> t.getName().equalsIgnoreCase(parameterName))
              .findFirst();
      if (parameter.isPresent()) {
        return parameter.get();
      }
    }
    return null;
  }

  private String getActionName(List<WorkflowStep.ActionMapper> actions, String actionId) {
    return actions.stream()
        .filter(t -> t.getAction().getId().getLocalId().equalsIgnoreCase(actionId))
        .findFirst()
        .get()
        .getAction()
        .getName();
  }

  private com.intuit.v4.workflows.Action getAction(List<WorkflowStep.ActionMapper> actions, String actionId, String actionKey) {
    return actions.stream()
        .filter(action -> action.getAction().getId().getLocalId().equalsIgnoreCase(actionId)
                  && actionKey.equalsIgnoreCase(action.getActionKey()))
        .findFirst()
        .get()
        .getAction();
  }

  private InputParameter getParameter(
      List<WorkflowStep.ActionMapper> actions, String actionId, String parameterName) {
    Optional<WorkflowStep.ActionMapper> action =
        actions.stream()
            .filter(t -> t.getAction().getId().getLocalId().equalsIgnoreCase(actionId))
            .findFirst();
    if (action.isPresent()) {
      Optional<InputParameter> subjectParameter =
          action.get().getAction().getParameters().stream()
              .filter(t -> t.getParameterName().equalsIgnoreCase(parameterName))
              .findFirst();
      if (subjectParameter.isPresent()) {
        return subjectParameter.get();
      }
    }
    return null;
  }

  @Test
  public void testIfActionsAreOverriddenForInvoiceParameterNull() {
    Record record =
            customWorkflowConfig.getRecordObjForType(
                    TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    // Set the parameter field type to null, testing else condition
    record.getActionGroups().stream().findFirst().get().
            getActions().stream().findFirst().get().
            getParameters().stream().findFirst().get().setFieldType(null);
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(7, actions.size());
    Assert.assertNull(actions.stream().findFirst().get().getAction().getParameters().stream().findFirst().get().getParameterType());
  }

  @Test
  public void testIfActionsAreOverriddenForInvoice() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(7, actions.size());

    // check if parameter is overridden with entity
    InputParameter inputParameter = getParameter(actions, "sendExternalEmail", "Subject");
    Assert.assertEquals(9, inputParameter.getHelpVariables().size());
    Assert.assertTrue(inputParameter.getParameterType().value().equalsIgnoreCase("string"));
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());
    Assert.assertTrue(inputParameter.isRequired());
    Assert.assertEquals("Send a customer email", getActionName(actions, "sendExternalEmail"));

    inputParameter = getParameter(actions, "createTask", "intuit_realmid");
    Assert.assertNull(inputParameter);

    inputParameter = getParameter(actions, "createTask", "TaskName");
    Assert.assertEquals(FieldTypeEnum.STRING, inputParameter.getParameterType());

    inputParameter = getParameter(actions, "sendExternalEmail", "Message");
    Assert.assertEquals(
        "Hi [[Customer Name]],\n"
            + "\n"
            + "Invoice [[Invoice Number]] needs your attention. Please take a look at the attached invoice and contact us if you have any questions.\n"
            + "\n"
            + "Thanks,\n"
            + "[[Company Name]]",
        inputParameter.getFieldValues().get(0));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "txn_sent", "close_manually");
    Assert.assertTrue(
        inputParameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    Assert.assertTrue(
        inputParameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    // check if original config is not changed
    Parameter parameter = getParameterFromConfig("sendExternalEmail", "subject");
    Assert.assertTrue(CollectionUtils.isEmpty(parameter.getPossibleFieldValues()));
    Assert.assertTrue(parameter.getFieldType().equalsIgnoreCase("string"));
    Assert.assertTrue(parameter.getConfigurable());
    Assert.assertNull(parameter.getMultiSelect());
    Assert.assertTrue(parameter.getRequiredByUI());
  }

  @Test
  public void testIfActionsAreOverriddenForBill() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(actions.size(), 5);

    // check if parameter is overridden with entity
    InputParameter parameter = getParameter(actions, "sendCompanyEmail", "subject");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    Assert.assertEquals("Review Bill [[Bill Number]]", parameter.getFieldValues().get(0));

    parameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(2, parameter.getPossibleFieldValues().size());

    com.intuit.v4.workflows.Action action = getAction(actions, "createTask", "approval");
    Assert.assertEquals(true, action.isRequired());
    action = getAction(actions, "createTask", "reminder");
    Assert.assertEquals(false, action.isRequired());

    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "close_manually");
    Assert.assertTrue(
        parameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));
  }

  @Test
  public void testActionsIfCustomIAOff() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    // Set the parameter field type to null, testing else condition
    record.getActionGroups().stream().findFirst().get().
        getActions().stream().findFirst().get().
        getParameters().stream().findFirst().get().setFieldType(null);
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(7, actions.size());
    Assert.assertNull(actions.stream().findFirst().get().getAction().getParameters().stream().findFirst().get().getParameterType());
  }

  @Test
  public void testIfActionsAreOverriddenForInvoiceForFrCaLocale() {
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(7, actions.size());

    // check if parameter is overridden with entity
    InputParameter inputParameter = getParameter(actions, "sendExternalEmail", "Subject");
    Assert.assertEquals(9, inputParameter.getHelpVariables().size());
    Assert.assertTrue(inputParameter.getParameterType().value().equalsIgnoreCase("string"));
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());
    Assert.assertTrue(inputParameter.isRequired());
    Assert.assertEquals("Send a customer email", getActionName(actions, "sendExternalEmail"));

    inputParameter = getParameter(actions, "createTask", "intuit_realmid");
    Assert.assertNull(inputParameter);

    inputParameter = getParameter(actions, "createTask", "TaskName");
    Assert.assertEquals(FieldTypeEnum.STRING, inputParameter.getParameterType());

    inputParameter = getParameter(actions, "sendExternalEmail", "Message");
    Assert.assertEquals(
        "Bonjour [[Customer Name]],\n"
            + "\n"
            + "Facture [[Invoice Number]] requiert votre attention. Veuillez jeter un coup d’œil à (au) facture ci-joint(e) et communiquez avec nous si vous avez des questions.",
        inputParameter.getFieldValues().get(0));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "txn_sent", "close_manually");
    Assert.assertTrue(
        inputParameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    Assert.assertTrue(
        inputParameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    // check if original config is not changed
    Parameter parameter = getParameterFromConfig("sendExternalEmail", "subject");
    Assert.assertTrue(CollectionUtils.isEmpty(parameter.getPossibleFieldValues()));
    Assert.assertTrue(parameter.getFieldType().equalsIgnoreCase("string"));
    Assert.assertTrue(parameter.getConfigurable());
    Assert.assertNull(parameter.getMultiSelect());
    Assert.assertTrue(parameter.getRequiredByUI());
  }

  @Test
  public void testIfActionsAreOverriddenForBillForFrCaLocale() {
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    Assert.assertEquals(actions.size(), 5);

    // check if parameter is overridden with entity
    InputParameter parameter = getParameter(actions, "sendCompanyEmail", "subject");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    Assert.assertEquals("Vérifier Facture à payer [[Bill Number]]", parameter.getFieldValues().get(0));

    parameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(2, parameter.getPossibleFieldValues().size());

    com.intuit.v4.workflows.Action action = getAction(actions, "createTask", "approval");
    Assert.assertEquals(true, action.isRequired());
    action = getAction(actions, "createTask", "reminder");
    Assert.assertEquals(false, action.isRequired());

    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "close_manually");
    Assert.assertTrue(
        parameter.getPossibleFieldValues().stream()
            .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));
  }

  @Test
  public void testBuildTemplateStepActionForInvoiceWithCallActivity() {
    Record record =
            customWorkflowConfig.getRecordObjForType(
                    TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    List<ActionGroup> approvalActionGroup = record.getActionGroups().stream().filter(actionGroup -> actionGroup.getId().equalsIgnoreCase("approval")).collect(Collectors.toList());
    com.intuit.v4.workflows.Action stepAction = actionMapper.buildTemplateStepAction(record, approvalActionGroup.get(0), approvalActionGroup.get(0).getActionIdMapper().getActionId());
    // since call activity action is defined, stepAction should contain parent action and subactions
    Assert.assertNotNull(stepAction);
    // subaction list should not be null
    Assert.assertNotNull(stepAction.getSubActions());
    Assert.assertEquals(2, stepAction.getSubActions().size());
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    // actions list should contain all record actions + all call activity actions (in parent action - subaction format)
    Assert.assertEquals(7, actions.size());

    // check if parameter is overridden with entity, should work as is, irrespective of call activities defined in config
    InputParameter inputParameter = getParameter(actions, "sendExternalEmail", "Subject");
    Assert.assertEquals(9, inputParameter.getHelpVariables().size());
    Assert.assertTrue(inputParameter.getParameterType().value().equalsIgnoreCase("string"));
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());
    Assert.assertTrue(inputParameter.isRequired());
    Assert.assertEquals("Send a customer email", getActionName(actions, "sendExternalEmail"));

    inputParameter = getParameter(actions, "createTask", "intuit_realmid");
    Assert.assertNull(inputParameter);

    inputParameter = getParameter(actions, "createTask", "TaskName");
    Assert.assertEquals(FieldTypeEnum.STRING, inputParameter.getParameterType());

    inputParameter = getParameter(actions, "sendExternalEmail", "Message");
    Assert.assertEquals(
            "Hi [[Customer Name]],\n"
                    + "\n"
                    + "Invoice [[Invoice Number]] needs your attention. Please take a look at the attached invoice and contact us if you have any questions.\n"
                    + "\n"
                    + "Thanks,\n"
                    + "[[Company Name]]",
            inputParameter.getFieldValues().get(0));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "txn_sent", "close_manually");
    Assert.assertTrue(
            inputParameter.getPossibleFieldValues().stream()
                    .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    inputParameter = getParameter(actions, "sendExternalEmail", "CC");
    Assert.assertTrue(inputParameter.isConfigurable());
    Assert.assertNull(inputParameter.isMultiSelect());

    inputParameter = getParameter(actions, "sendCompanyEmail", "SendTo");
    Assert.assertEquals("Company Email", inputParameter.getHelpVariables(0));

    inputParameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(3, inputParameter.getPossibleFieldValues().size());
    Assert.assertTrue(
            inputParameter.getPossibleFieldValues().stream()
                    .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));

    // check if original config is not changed
    Parameter parameter = getParameterFromConfig("sendExternalEmail", "subject");
    Assert.assertTrue(CollectionUtils.isEmpty(parameter.getPossibleFieldValues()));
    Assert.assertTrue(parameter.getFieldType().equalsIgnoreCase("string"));
    Assert.assertTrue(parameter.getConfigurable());
    Assert.assertNull(parameter.getMultiSelect());
    Assert.assertTrue(parameter.getRequiredByUI());
  }

  @Test
  public void testBuildTemplateStepActionForBillWithoutCallActivity() {
    Record record =
            customWorkflowConfig.getRecordObjForType(
                    TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    List<ActionGroup> approvalActionGroup = record.getActionGroups()
            .stream().filter(actionGroup -> actionGroup.getId()
                            .equalsIgnoreCase("approval")).collect(Collectors.toList());
    com.intuit.v4.workflows.Action stepAction =
            actionMapper.buildTemplateStepAction(record, approvalActionGroup.get(0), "sendForApproval");
    // no call activity action is defined so stepAction should be empty
    Assert.assertNull(stepAction);
    List<WorkflowStep.ActionMapper> actions = actionMapper.build(record);
    Assert.assertNotNull(actions);
    // Actions list will only comprise of record actions
    Assert.assertEquals(actions.size(), 5);
    // check if parameter is overridden with entity, should work as is, irrespective of call activities defined in config
    InputParameter parameter = getParameter(actions, "sendCompanyEmail", "subject");
    Assert.assertEquals(9, parameter.getHelpVariables().size());
    Assert.assertEquals("Review Bill [[Bill Number]]", parameter.getFieldValues().get(0));

    parameter = getParameter(actions, "createTask", "CloseTask");
    Assert.assertEquals(2, parameter.getPossibleFieldValues().size());

    com.intuit.v4.workflows.Action action = getAction(actions, "createTask", "approval");
    Assert.assertEquals(true, action.isRequired());
    action = getAction(actions, "createTask", "reminder");
    Assert.assertEquals(false, action.isRequired());

    List<String> closeTaskPossibleValues = Arrays.asList("txn_paid", "close_manually");
    Assert.assertTrue(
            parameter.getPossibleFieldValues().stream()
                    .allMatch(fieldValue -> closeTaskPossibleValues.contains(fieldValue)));
  }
}
