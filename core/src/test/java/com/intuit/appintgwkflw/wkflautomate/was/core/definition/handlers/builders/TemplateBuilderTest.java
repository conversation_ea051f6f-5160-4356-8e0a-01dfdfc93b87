package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.workflows.Template;

import java.util.List;
import java.util.Map;

import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class TemplateBuilderTest {
  private TemplateBuilder templateBuilder;
  private TemplateConditionBuilder templateConditionBuilder;
  private TemplateActionBuilder templateActionBuilder;

  private TemplateBuilder templateBuilderWithUnsupportedAttributes;
  private TemplateConditionBuilder templateConditionBuilderWithUnsupportedAttributes;

  public static String RECORD_TYPE_INVOICE = "invoice";
  public static String RECORD_TYPE_BILL = "bill";
  public static String INVALID_RECORD_TYPE = "invalid";
  public static String REMINDER_ACTION_KEY = "reminder";
  public static String APPROVAL_ACTION_KEY = "approval";
  public static String PAYMENT_DUE_REMINDER = "paymentDueReminder";
  public static String INVOICE_UNSENT_REMINDER = "invoiceUnsentReminder";
  public static String SEND_STATEMENTS = "sendStatements";
  public static String BILL_PAYMENT = "billpayment";

  private CustomWorkflowConfig customWorkflowConfig;
  private CustomWorkflowConfig customWorkflowConfigExcludedAttributes;

  @MockBean WASContextHandler wasContextHandler;
  @MockBean private FeatureFlagManager featureFlagManager;
  @MockBean private TemplateLabelsService templateLabelsService;
  @Spy private TranslationService translationService = TestHelper.initTranslationService();
  @Mock WASContextHandler wasContextHandlerTemp;
  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    customWorkflowConfigExcludedAttributes =
            TemplateBuilderTestHelper.getConfig("dictionaryExcludeAttributeTest.yaml");
    templateActionBuilder = new TemplateActionBuilder(wasContextHandler,translationService);
    templateConditionBuilder =
            new TemplateConditionBuilder(customWorkflowConfig, wasContextHandler,translationService, featureFlagManager);
    templateConditionBuilderWithUnsupportedAttributes =
            new TemplateConditionBuilder(customWorkflowConfigExcludedAttributes, wasContextHandler,translationService, featureFlagManager);
    templateBuilder =
            new TemplateBuilder(
                    templateConditionBuilder,
                    templateActionBuilder,
                    customWorkflowConfig,
                    wasContextHandler,
                    featureFlagManager,translationService, templateLabelsService);

    templateBuilderWithUnsupportedAttributes = new TemplateBuilder(
                    templateConditionBuilderWithUnsupportedAttributes,
                    templateActionBuilder,
                    customWorkflowConfigExcludedAttributes,
                    wasContextHandler,
                    featureFlagManager,translationService, templateLabelsService);

    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testTemplateBuild() {
    Template template = templateBuilder.build("QBO", RECORD_TYPE_INVOICE, REMINDER_ACTION_KEY, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_INVOICE);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(7, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());

    Assert.assertEquals(WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(17,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTemplateBuildInvalidRecordType() {
    try {
      templateBuilder.build("QBO", INVALID_RECORD_TYPE, REMINDER_ACTION_KEY, false);
      Assert.fail("Method should throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_INPUT, e.getWorkflowError());
      throw e;
    }
  }

  // Adding new Test cases here
  @Test
  public void testBuildPreCannedTemplates() {
    Mockito.when(wasContextHandlerTemp.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    List<Template> templates = templateBuilder.buildConfigTemplates();
    Assert.assertNotNull(templates);
    Assert.assertEquals(13, templates.size());
    for (Template template : templates) {
      // Verifying name is not null
      Assert.assertNotNull(template.getName());
      // Verifying Record Type is not null
      Assert.assertNotNull(template.getRecordType());
      // Verifying Display Name is Null as It should be provided by the user.
      Assert.assertNotNull(template.getDisplayName());
    }
  }

  @Test
  public void testBuildPreCannedTemplatesId_paymentDueReminder() {
    Template template = templateBuilder.getConfigTemplateById(PAYMENT_DUE_REMINDER);
    Assert.assertNotNull(template);
    Assert.assertEquals("paymentDueReminder", template.getName());
    Assert.assertEquals(RECORD_TYPE_INVOICE, template.getRecordType().toLowerCase());
    Assert.assertEquals(1, template.getWorkflowSteps().size());
  }

  @Test
  public void testBuildPreCannedTemplatesId_unsentInvoiceReminder() {
    Template template = templateBuilder.getConfigTemplateById(INVOICE_UNSENT_REMINDER);
    Assert.assertNotNull(template);
    Assert.assertEquals("invoiceUnsentReminder", template.getName());
    Assert.assertEquals(RECORD_TYPE_INVOICE, template.getRecordType().toLowerCase());
    Assert.assertEquals(1, template.getWorkflowSteps().size());
  }

  @Test
  public void testBuildPreCannedTemplatesId() {
    Template template = templateBuilder.getConfigTemplateById(PAYMENT_DUE_REMINDER);
    Assert.assertNotNull(template);
    Assert.assertEquals("paymentDueReminder", template.getName());
    Assert.assertEquals(RECORD_TYPE_INVOICE, template.getRecordType().toLowerCase());
    Assert.assertNull(template.getRecurrence());
  }

  @Test
  public void testBuildPreCannedTemplatesId_sendStatements() {
    Template template = templateBuilder.getConfigTemplateById(SEND_STATEMENTS);
    Assert.assertNotNull(template);
    Assert.assertEquals(SEND_STATEMENTS, template.getName());
    Assert.assertEquals(RecordType.STATEMENT.toString(), template.getRecordType().toLowerCase());
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertNotNull(template.getRecurrence());
    Assert.assertEquals(RecurTypeEnum.WEEKLY, template.getRecurrence().getRecurType());
    Assert.assertEquals(Integer.valueOf(1), template.getRecurrence().getInterval());
  }

  @Test
  public void testBuildPreCannedTemplatesIdInvalid() {
    Template template = templateBuilder.getConfigTemplateById("invalid");
    Assert.assertNull(template);
  }

  @Test
  public void testTemplateBuildInvoiceReminder() {
    Template template =
        templateBuilderWithUnsupportedAttributes.build(
            "QBO", RECORD_TYPE_INVOICE, REMINDER_ACTION_KEY, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_INVOICE);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(6, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(13,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
    //Asserting for empty trigger object when config has trigger handler id but no trigger parameters
    Assert.assertEquals(0,
        template
            .getWorkflowSteps()
            .get(0)
            .getTrigger()
            .getParameters()
            .size());
    Assert.assertNull(template
            .getWorkflowSteps()
            .get(0)
            .getTrigger()
            .getId());
    // templateData will only be returned if actionIdMapper is defined in the config
    Assert.assertNull(template.getTemplateData());
  }

  @Test
  public void testTemplateBuildInvoiceNullActionKey() {
    Template template =
        templateBuilderWithUnsupportedAttributes.build(
            "QBO", RECORD_TYPE_INVOICE, null, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_INVOICE);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(6, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(13,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
  }

  @Test
  public void testTemplateBuildBillReminder() {
    Template template = templateBuilderWithUnsupportedAttributes.build(
            "QBO", RECORD_TYPE_BILL, REMINDER_ACTION_KEY, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_BILL);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(4, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(
        9,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
    Assert.assertTrue(template.getWorkflowSteps().get(0).getTrigger().isEmpty());
    // templateData will only be returned if actionIdMapper is defined in the config
    Assert.assertNull(template.getTemplateData());
  }

  @Test
  public void testTemplateBuildBillPayment() {
    Template template = templateBuilder.build(
                    "QBO", BILL_PAYMENT, null, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), BILL_PAYMENT);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(
            WorkflowConstants.CUSTOM_START_EVENT,
            template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
            WorkflowConstants.CUSTOM_DECISION_ELEMENT,
            template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(
            2,
            template
                    .getWorkflowSteps()
                    .get(0)
                    .getWorkflowStepCondition()
                    .getConditionalInputParameters()
                    .size());
    Assert.assertEquals(1,
            template
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters()
                    .size());
    Assert.assertEquals(3,
            template
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters().get(0)
                    .getPossibleFieldValues()
                    .size());
    // templateData will only be returned if actionIdMapper is defined in the config
    Assert.assertNull(template.getTemplateData());
  }

  @Test
  public void testTemplateBuildBillNullActionKey() {
    Template template =
        templateBuilderWithUnsupportedAttributes.build(
            "QBO", RECORD_TYPE_BILL, REMINDER_ACTION_KEY, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_BILL);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(4, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(9,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
  }

  @Test
  public void testTemplateBuildBillApproval() {
    Template template =
        templateBuilderWithUnsupportedAttributes.build(
            "QBO", RECORD_TYPE_BILL, APPROVAL_ACTION_KEY, false);
    Assert.assertNotNull(template);
    Assert.assertEquals(template.getRecordType(), RECORD_TYPE_BILL);
    Assert.assertEquals(1, template.getWorkflowSteps().size());
    Assert.assertEquals(4, template.getWorkflowSteps().get(0).getActions().size());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_START_EVENT,
        template.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        WorkflowConstants.CUSTOM_DECISION_ELEMENT,
        template.getWorkflowSteps().get(0).getWorkflowStepCondition().getId().getLocalId());
    Assert.assertEquals(
        7,
        template
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .getConditionalInputParameters()
            .size());
    // templateData will only be returned if actionIdMapper is defined in the config
    Assert.assertNull(template.getTemplateData());
  }

  @Test
  public void testBuildLocalisedTemplateForEnUsLocale() {
    Mockito.when(wasContextHandlerTemp.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    List<Template> templates = templateBuilder.buildConfigTemplates();
    Assert.assertNotNull(templates);
    Assert.assertEquals(13, templates.size());
    Map<String, String[]> idLocalisedKeyMap =
        Map.ofEntries(
            Map.entry("invoiceUnsentReminder",
                new String[]{
                    "Get unsent invoice reminder",
                    "Get reminders to send unsent invoices to customers on time."
                }),
            Map.entry("billVendorReminder",
                new String[]{
                    "Pay vendor bill reminder", "Get notified to pay vendor bills on time."
                }),
            Map.entry("estimateUnsentReminder",
                new String[]{
                    "Send estimate reminder",
                    "Get reminders for sending estimates to customers on time."
                }),
            Map.entry("estimateFollowupReminder",
                new String[]{
                    "Send estimate follow up reminder",
                    "Send estimate follow-up reminders to customers on time."
                }),
            Map.entry("openEstimateReminder",
                new String[]{
                    "Send open estimate reminder",
                    "Get reminders for estimates that are yet to be invoiced."
                }),
            Map.entry("sendStatements",
                new String[]{
                    "Send recurring statement to customer",
                    "We will send statements to customers at a frequency you choose."
                }),
            Map.entry("paymentDueReminder",
                new String[]{
                    "Payment Due Reminder",
                    "Payment Due Reminder"
                }),
            Map.entry("openPurchaseorderReminder",
                new String[]{
                    "Get open purchase order reminder",
                    "Get reminders for purchase orders that are yet to be converted to bills."
                }),
            Map.entry("billApproval",
                new String[]{
                    "Set up bill approval",
                    "Send bills to the approvers you choose."
                }),
            Map.entry("invoiceapproval-multicondition",
                new String[]{
                    "Invoice approval",
                    "Invoice approval with multi-condition support"
                }),
            Map.entry("estimateUpdateNotification", new String[]{
                "Notify about estimate update",
                "Notify yourself / peers / managers about the estimates updated by you."}),
            Map.entry("billUpdateNotification", new String[]{
                "Notify about bill update",
                "Notify yourself / peers / managers about the bills updated by you."}),
            Map.entry("purchaseorderUpdateNotification", new String[]{
                "Notify about PO update",
                "Notify your peers / managers to review the PO you updated."})
        );
    for (Template template : templates) {
      // Verifying name exists
      Assert.assertNotNull(idLocalisedKeyMap.containsKey(template.getName()));
      // Verifying Display Name
      Assert.assertEquals(idLocalisedKeyMap.get(template.getName())[0], template.getDisplayName());
      // Verifying name description
      Assert.assertEquals(idLocalisedKeyMap.get(template.getName())[1], template.getDescription());
    }
  }

  @Test
  public void testBuildLocalisedTemplateForFrCaLocale() {
    Mockito.when(wasContextHandlerTemp.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    List<Template> templates = templateBuilder.buildConfigTemplates();
    Assert.assertNotNull(templates);
    Assert.assertEquals(13, templates.size());
    Map<String, String[]> idLocalisedKeyMap =
        Map.ofEntries(
            Map.entry("invoiceUnsentReminder",
                new String[]{
                    "Rappel de facture non envoyée",
                    "Recevez des rappels pour envoyer les factures à temps."
                }),
            Map.entry("billVendorReminder",
                new String[]{
                    "Rappel de paiement de fournisseur",
                    "Recevez des rappels pour payer les factures des fournisseurs à temps."
                }),
            Map.entry("estimateUnsentReminder",
                new String[]{
                    "Envoyer un rappel de devis",
                    "Recevez des rappels pour envoyer les devis à temps."
                }),
            Map.entry("estimateFollowupReminder",
                new String[]{
                    "Envoyer un rappel de suivi de devis",
                    "Envoyez aux clients des rappels de suivi de devis."
                }),
            Map.entry("openEstimateReminder",
                new String[]{
                    "Envoyer un rappel de devis en cours",
                    "Recevez des rappels pour les devis qui doivent être facturés."
                }),
            Map.entry("sendStatements",
                new String[]{
                    "Envoyer un relevé récurrent au client",
                    "Les relevés de compte seront envoyés aux clients à la fréquence de votre choix."
                }),
            Map.entry("paymentDueReminder",
                new String[]{
                    "Payment Due Reminder",
                    "Payment Due Reminder"
                }),
            Map.entry("openPurchaseorderReminder",
                new String[]{
                    "Rappels de bon de commande en cours.",
                    "Recevez des rappels pour les bons de commande qui doivent être convertis en factures."
                }),
            Map.entry("billApproval",
                new String[]{
                    "Configurer l’approbation des factures à payer",
                    "Envoyez les factures à payer aux approbateurs de votre choix."
                }),
            Map.entry("invoiceapproval-multicondition",
                new String[]{
                    "Invoice approval",
                    "Invoice approval with multi-condition support"
                }),
            Map.entry("estimateUpdateNotification",
                new String[]{"Notification de modification de devis",
                    "Envoyez des notifications à vous-même, à des collègues ou à des gestionnaires lorsque vous modifiez des devis."
                }),
            Map.entry("billUpdateNotification",
                new String[]{
                    "Notification de modification de facture à payer",
                    "Envoyez des notifications à vous-même, à des collègues ou à des gestionnaires lorsque vous modifiez des factures à payer."
                }),
            Map.entry("purchaseorderUpdateNotification",
                new String[]{
                    "Notification de modification de bon de commande",
                    "Envoyez des notifications à des collègues ou à des gestionnaires pour qu’ils vérifient le bon de commande que vous avez modifié."
                })
 );


    for (Template template : templates) {
      // Verifying name exists
      Assert.assertNotNull(idLocalisedKeyMap.containsKey(template.getName()));
      // Verifying Display Name
      Assert.assertEquals(idLocalisedKeyMap.get(template.getName())[0], template.getDisplayName());
      // Verifying name description
      Assert.assertEquals(idLocalisedKeyMap.get(template.getName())[1], template.getDescription());

    }
  }

  @Test
  public void testBuildLocalisedTemplateWithKeyIsNotPresent() {
    Mockito.when(wasContextHandlerTemp.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    List<ConfigTemplate> configTemplates = customWorkflowConfig.getConfigTemplates();
    Assert.assertEquals(13, configTemplates.size());

    // change description key with invalid key for two templates only
    for (int i = 0; i < 2; i++) {
      ConfigTemplate configTemplate = configTemplates.get(i);
      configTemplate.setDescription("~key.is.not.present");
    }
    List<Template> templates = templateBuilder.buildConfigTemplates();
    Assert.assertNotNull(templates);
    // Now two templates will not be shown
    Assert.assertEquals(11, templates.size());
  }

  @Test
  public void testBuildTemplateDetails() {
    GlobalId globalId = null;
    TemplateDetails templateDetails = TestHelper.mockTemplateDetailsObject();
    Template template = (Template) templateBuilder.buildTemplateDetails(globalId, templateDetails);
    Assert.assertNotNull(template);
    Assert.assertEquals("CUSTOM", template.getCategory());
    Assert.assertEquals("customApproval", template.getName());
    Assert.assertEquals(WorkflowStatusEnum.ENABLED, template.getStatus());
    Assert.assertEquals("1", template.getVersion());

    templateDetails.setStatus(null);
    template = (Template) templateBuilder.buildTemplateDetails(globalId, templateDetails);
    Assert.assertNotNull(template);
  }
}
