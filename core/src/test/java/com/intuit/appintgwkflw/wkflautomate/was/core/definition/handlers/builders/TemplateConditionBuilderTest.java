package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import  com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStepCondition;

import com.intuit.v4.workflows.definitions.ConditionalParameter;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import com.intuit.v4.workflows.definitions.Operator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

/** <AUTHOR> */
@RunWith(SpringRunner.class)
public class TemplateConditionBuilderTest {
  private TemplateConditionBuilder templateConditionBuilder;
  private CustomWorkflowConfig customWorkflowConfig;
  private CustomWorkflowConfig customWorkflowConfigExcludedAttributes;
  public static String REMINDER_ACTION_KEY = "reminder";
  public static String APPROVAL_ACTION_KEY = "approval";
  @MockBean WASContextHandler wasContextHandler;
  @Spy TranslationService translationService = TestHelper.initTranslationService();

  @Mock WASContextHandler wasContextHandlerTemp;

  @Mock
  FeatureFlagManager featureFlagManager;


  @Before
  @SneakyThrows
  public void setup() {
    MockitoAnnotations.initMocks(this);
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    customWorkflowConfigExcludedAttributes =
        TemplateBuilderTestHelper.getConfig("dictionaryExcludeAttributeTest.yaml");
    templateConditionBuilder =
        new TemplateConditionBuilder(customWorkflowConfig, wasContextHandler,translationService, featureFlagManager);
  }

  @Test
  public void testConditionsForInvoice() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    // 2 conditions are present in unsupportedAttributes and will be skipped.
    Assert.assertEquals(17, workflowStepCondition.getConditionalInputParameters().size());
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "TxnSendDays",
            "TxnApprovalStatus",
            "TxnPaymentStatus",
            "TxnUpdateStatus",
            "TxnBalanceAmount",
            "TxnDepositStatus",
            "TxnApprovalDays",
            "TxnEmailAvailabilityStatus",
            "Location",
            "Term",
            "TxnDays",
            "TxnCreateDays",
            "TxnSendStatus",
            "Customer",
            "StringCustomField",
            "DoubleCustomField",
            "ListCustomField");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testConditionsForInvoiceWithFF() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY, false, null);
    Assert.assertEquals(8, workflowStepCondition.getConditionalInputParameters().size());
    workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY, false, null);
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream().noneMatch(
            param -> param.getInputParameter().getParameterName().equalsIgnoreCase("class")));
    Assert.assertEquals(13, workflowStepCondition.getConditionalInputParameters().size());

    Mockito.when(featureFlagManager.getBoolean("qbo-adv-feel-expr-supported", false)).thenReturn(true);
    workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY, false, null);
    Assert.assertEquals(9, workflowStepCondition.getConditionalInputParameters().size());
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream().anyMatch(
            param -> param.getInputParameter().getParameterName().equalsIgnoreCase("class")));
    workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY, false, null);
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream().noneMatch(
            param -> param.getInputParameter().getParameterName().equalsIgnoreCase("class")));
    Assert.assertEquals(13, workflowStepCondition.getConditionalInputParameters().size());
  }

  @Test
  public void testConditionsForBill() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(
        "initial size of attribute list in record: Bill", 10, record.getAttributes().size());
    Assert.assertEquals(
        "size of attribute list in record: Bill post filtering hidden attributes + Excluding further",
        7,
        workflowStepCondition.getConditionalInputParameters().size());
    List<String> hiddenAttributeList = Arrays.asList("intuit_userid");
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    Assert.assertTrue(
        "hidden parameter userid not returned in list",
        workflowStepCondition.getConditionalInputParameters().stream()
            .noneMatch(
                conditionalParameter ->
                    hiddenAttributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testConditionsForInvoiceReminder() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(13, workflowStepCondition.getConditionalInputParameters().size());
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "TxnSendDays",
            "TxnApprovalStatus",
            "TxnPaymentStatus",
            "TxnUpdateStatus",
            "TxnBalanceAmount",
            "TxnDepositStatus",
            "TxnApprovalDays",
            "TxnEmailAvailabilityStatus",
            "Location",
            "Term",
            "TxnDays",
            "TxnCreateDays",
            "TxnSendStatus",
            "Customer");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testConditionsForBillReminder() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(
        "initial size of attribute list in record: Bill", 10, record.getAttributes().size());
    Assert.assertEquals(
        "size of attribute list in record: Bill post filtering hidden attributes",
        9,
        workflowStepCondition.getConditionalInputParameters().size());
    List<String> hiddenAttributeList = Arrays.asList("intuit_userid");
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    Assert.assertTrue(
        "hidden parameter userid not returned in list",
        workflowStepCondition.getConditionalInputParameters().stream()
            .noneMatch(
                conditionalParameter ->
                    hiddenAttributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("BF -1", rulesMap.get("TxnDueDays").getConditionalExpression());
  }

  @Test
  public void testConditionsForBillApproval() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(
        "initial size of attribute list in record: Bill", 10, record.getAttributes().size());
    Assert.assertEquals(
        "size of attribute list in record: Bill post filtering hidden attributes",
        7,
        workflowStepCondition.getConditionalInputParameters().size());
    List<String> hiddenAttributeList = Arrays.asList("intuit_userid");
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    Assert.assertTrue(
        "hidden parameter userid not returned in list",
        workflowStepCondition.getConditionalInputParameters().stream()
            .noneMatch(
                conditionalParameter ->
                    hiddenAttributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testConditionsForBillNewConfig() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(
        "initial size of attribute list in record: Bill", 10, record.getAttributes().size());
    // 2 conditions are present in excludedAttributes for approval. But the first actionGroup here
    // is reminder so all the conditions will appear and nothing will be excluded.
    Assert.assertEquals(
        "size of attribute list in record: Bill post filtering hidden attributes",
        9,
        workflowStepCondition.getConditionalInputParameters().size());
    List<String> hiddenAttributeList = Arrays.asList("intuit_userid");
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    Assert.assertTrue(
        "hidden parameter userid not returned in list",
        workflowStepCondition.getConditionalInputParameters().stream()
            .noneMatch(
                conditionalParameter ->
                    hiddenAttributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("BF -1", rulesMap.get("TxnDueDays").getConditionalExpression());
  }

  @Test
  public void testInvalidActionKey() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    try {
      WorkflowStepCondition workflowStepCondition =
          templateConditionBuilder.build(record, "INVALID",false, null);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_ACTION_KEY.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testDataTypeNotFoundException() {
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    List<Attribute> attributeList = new ArrayList<>();
    Attribute attribute = new Attribute();
    attribute.setId("1");
    attribute.setName("DataType");
    attribute.setType(null);
    attribute.setHidden(false);
    attributeList.add(attribute);
    record.setAttributes(attributeList);
    try {
      WorkflowStepCondition workflowStepCondition =
          templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,false, null);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.DATATYPE_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testingDefaultAttributeAtActionGroupLevelInvoice() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(13, workflowStepCondition.getConditionalInputParameters().size());
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "TxnSendDays",
            "TxnApprovalStatus",
            "TxnPaymentStatus",
            "TxnUpdateStatus",
            "TxnBalanceAmount",
            "TxnDepositStatus",
            "TxnApprovalDays",
            "TxnEmailAvailabilityStatus",
            "Location",
            "Term",
            "TxnDays",
            "TxnCreateDays",
            "TxnSendStatus",
            "Customer");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testingDefaultAttributeAtActionGroupLevelBillReminder() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, REMINDER_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(9, workflowStepCondition.getConditionalInputParameters().size());
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("BF -1", rulesMap.get("TxnDueDays").getConditionalExpression());
  }

  @Test
  public void testingDefaultAttributeAtActionGroupLevelBillApproval() {
    Record record =
        customWorkflowConfigExcludedAttributes.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_BILL.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(7, workflowStepCondition.getConditionalInputParameters().size());
    List<String> attributeList =
        Arrays.asList(
            "TxnDueDays",
            "TxnAmount",
            "Vendor",
            "Term",
            "Location",
            "TxnPaymentStatus",
            "Class",
            "TxnUpdateStatus",
            "TxnDays",
            "TxnCreateDays");
    Assert.assertTrue(
        workflowStepCondition.getConditionalInputParameters().stream()
            .allMatch(
                conditionalParameter ->
                    attributeList.contains(
                        conditionalParameter.getInputParameter().getParameterName())));
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
        rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 0", rulesMap.get("TxnAmount").getConditionalExpression());
  }

  @Test
  public void testConditionalOperationLocalisedDescrptionForEnUsLocale() {
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    Assert.assertNotNull(record);
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY, false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    List<ConditionalParameter> conditionalParameters =
        workflowStepCondition.getConditionalInputParameters();
    Assert.assertEquals(12, conditionalParameters.size());

    workflowStepCondition.getConditionalInputParameters().stream()
        .forEach(
            conditionalParameter ->
                Assert.assertNotNull(
                    conditionalParameter.getSupportedOperators().get(0).getDescription()));

    Map<String, String> symbolLocalisedDescriptionMap =
        Map.of(
                "GTE",
                "is greater than or equal to",
                "GT",
                "is greater than",
                "CONTAINS",
                "is",
                "NOT_CONTAINS",
                "is not",
                "LT",
                "is less than",
                "AF",
                "After",
                "BF",
                "Before",
                "LTE",
                "is less than or equal to",
                "EQ",
                "is equal to",
                "BTW",
                "is between");

    for (ConditionalParameter conditionalParameter : conditionalParameters) {
      Assert.assertNotNull(conditionalParameter.getSupportedOperators());
      for (Operator operators : conditionalParameter.getSupportedOperators()) {
        Assert.assertEquals(true, symbolLocalisedDescriptionMap.containsKey(operators.getSymbol()));
        Assert.assertEquals(
            symbolLocalisedDescriptionMap.get(operators.getSymbol()), operators.getDescription());
      }
    }
  }
  @Test
  public void testConditionalOperationLocalisedDescrptionForFrCaLocale() {
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    Record record =
        customWorkflowConfig.getRecordObjForType(
            TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    Assert.assertNotNull(record);
    WorkflowStepCondition workflowStepCondition =
        templateConditionBuilder.build(record, APPROVAL_ACTION_KEY, false, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    List<ConditionalParameter> conditionalParameters =
        workflowStepCondition.getConditionalInputParameters();
    Assert.assertEquals(12, conditionalParameters.size());

    workflowStepCondition.getConditionalInputParameters().stream()
        .forEach(
            conditionalParameter ->
                Assert.assertNotNull(
                    conditionalParameter.getSupportedOperators().get(0).getDescription()));

    Map<String, String> symbolLocalisedDescriptionMap =
        Map.of(
            "GTE",
            "est supérieur ou égal à",
            "GT",
            "est supérieur à",
            "CONTAINS",
            "est",
            "NOT_CONTAINS",
            "n’est pas",
            "LT",
            "est inférieur à",
            "AF",
            "After",
            "BF",
            "Before",
            "LTE",
            "est inférieur ou égal à",
            "EQ",
            "est égal à",
            "BTW",
            "est compris entre");

    for (ConditionalParameter conditionalParameter : conditionalParameters) {
      Assert.assertNotNull(conditionalParameter.getSupportedOperators());
      for (Operator operators : conditionalParameter.getSupportedOperators()) {
        Assert.assertEquals(true, symbolLocalisedDescriptionMap.containsKey(operators.getSymbol()));
        Assert.assertEquals(
            symbolLocalisedDescriptionMap.get(operators.getSymbol()), operators.getDescription());
      }
    }
  }

  @Test
  public void testConditionsForInvoiceApprovalWithConfigSteps() {
    Steps configStep = new Steps();
    configStep.setStepId(1);
    configStep.setStepType(StepTypeEnum.CONDITION.value());
    Next yesPath = new Next();
    Next noPath = new Next();
    yesPath.setType(NextTypeEnum.ACTION.value());
    yesPath.setStepId("2");
    yesPath.setLabel(NextLabelEnum.YES.value());
    noPath.setType(StepTypeEnum.CONDITION.value());
    noPath.setStepId("3");
    noPath.setLabel(NextLabelEnum.NO.value());
    List<Next> nexts = new ArrayList<>();
    nexts.add(yesPath);
    nexts.add(noPath);
    configStep.setNexts(nexts);

    Record record =
            customWorkflowConfig.getRecordObjForType(
                    TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
    WorkflowStepCondition workflowStepCondition =
            templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,true, configStep);

    Assert.assertNotNull(workflowStepCondition);

    Attribute txnAttr = new Attribute();
    txnAttr.setName("TxnAmount");
    txnAttr.setId("txnAmount");
    txnAttr.setType("DOUBLE");
    txnAttr.setDefaultValue("500");
    txnAttr.setDefaultOperator("GTE");
    configStep.setAttributes(Arrays.asList(txnAttr));

    workflowStepCondition =
            templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,true, configStep);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
    Assert.assertEquals(
            "initial size of attribute list in record: invoice", 19, record.getAttributes().size());
    Assert.assertEquals(
            "size of attribute list in record: invoice post filtering hidden attributes",
            12,
            workflowStepCondition.getConditionalInputParameters().size());
    List<Rule> rules = workflowStepCondition.getRuleLines().get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Map<String, Rule> rulesMap =
            rules.stream().collect(Collectors.toMap(Rule::getParameterName, Function.identity()));
    Assert.assertEquals("GTE 500", rulesMap.get("TxnAmount").getConditionalExpression());

    workflowStepCondition =
            templateConditionBuilder.build(record, APPROVAL_ACTION_KEY,true, null);
    Assert.assertNotNull(workflowStepCondition);
    Assert.assertNotNull(workflowStepCondition.getRuleLines());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters());
    Assert.assertNotNull(workflowStepCondition.getConditionalInputParameters().get(0));
  }
}
