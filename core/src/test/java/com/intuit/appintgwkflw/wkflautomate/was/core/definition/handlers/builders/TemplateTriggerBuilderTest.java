package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.Trigger;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class TemplateTriggerBuilderTest {

    private CustomWorkflowConfig customWorkflowConfig;
    private TemplateTriggerBuilder templateTriggerBuilder;

    @MockBean
    private WASContextHandler wasContextHandler;

    @Before
    @SneakyThrows
    public void setup() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        templateTriggerBuilder = new TemplateTriggerBuilder(wasContextHandler);
    }

    @Test
    public void test_trigger_billPayment() {
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
        Record record =
                customWorkflowConfig.getRecordObjForType(
                        TemplateBuilderTestHelper.ENTITY_BILL_PAYMENT.toLowerCase());
        Trigger trigger = templateTriggerBuilder.build(record,"notification");
        Assert.assertNotNull(trigger);
    }

    @Test
    public void test_trigger_InvalidActionKey() {
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
        Record record =
                customWorkflowConfig.getRecordObjForType(
                        TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
        Trigger trigger = null;
        try {
            trigger = templateTriggerBuilder.build(record, "statement");
        } catch (WorkflowGeneralException e) {
            Assert.assertEquals(WorkflowError.INVALID_ACTION_KEY.getErrorMessage(), e.getMessage());
        }
        Assert.assertNull(trigger);
    }

    @Test
    public void test_trigger_Invoice() {
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("OWNER_ID");
        Record record =
                customWorkflowConfig.getRecordObjForType(
                        TemplateBuilderTestHelper.ENTITY_INVOICE.toLowerCase());
        Trigger trigger = null;
        try {
            trigger = templateTriggerBuilder.build(record, "reminder");
        } catch (WorkflowGeneralException e) {
            Assert.assertEquals(WorkflowError.INVALID_ACTION_KEY.getErrorMessage(), e.getMessage());
        }
        Assert.assertTrue(trigger.getParameters().isEmpty());
    }
}
