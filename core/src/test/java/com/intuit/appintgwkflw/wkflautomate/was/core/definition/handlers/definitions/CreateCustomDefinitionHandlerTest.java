package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CREATE_TASK_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_CONDITION_ID_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_STEP_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_TRIGGER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEFINITION_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.MAPPED_ACTION_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.REALM_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.SEND_COMPANY_EMAIL_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.SEND_STATEMENT_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CREATE_TASK;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.ConditionalElementHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.DailyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.MonthlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.WeeklyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.YearlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProjectType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;

import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.mock.mockito.MockBean;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class CreateCustomDefinitionHandlerTest {

  private Definition definition;
  private Definition definitionStatement;

  // The class to be mocked and tested is CreateCustomDefinitionHandler
  // Since it has 2 dependencies, therefore its annotated with @InjectMocks and
  // placeholderExtractorProvider and ConditionalElementHandler with @Mock
  @InjectMocks private CreateCustomDefinitionHandler createCustomDefinitionHandler;
  @Mock private PlaceholderExtractorProvider placeholderExtractorProvider;
  @Mock private ConditionalElementHandler conditionalElementHandler;
  // Spy allows mocks to be initialised, @Mock will not initialise the object
  @Spy private CustomWorkflowConfig customWorkflowConfig = initCustomWorkflowConfig();

  @Mock private LookupKeysMapper lookupKeysMapper;

  @Mock private AuthHelper authHelper;

  @MockBean DefinitionServiceHelper definitionServiceHelper;

  @Spy private FilterParameterExtractorUtil filterParameterExtractorUtil = initFilterParameterExtractorUtil();
  @Mock private ConditionalElementFactory conditionalElementFactory;


  @Captor private ArgumentCaptor<BpmnModelInstance> bpmnModelInstanceArgumentCaptor;
  @Captor private ArgumentCaptor<DefinitionId> definitionIdArgumentCaptor;
  @Captor private ArgumentCaptor<Definition> definitionArgumentCaptor;

  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;

  @Mock MonthlyRecurrenceProcessor monthlyRecurrenceProcessor;

  @Mock DailyRecurrenceProcessor dailyRecurrenceProcessor;

  @Mock YearlyRecurrenceProcessor yearlyRecurrenceProcessor;

  @Mock WeeklyRecurrenceProcessor weeklyRecurrenceProcessor;

  @Mock private FeatureFlagManager featureFlagManager;

  @Rule public ExpectedException exceptionRule = ExpectedException.none();
  private static final String CUSTOM_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");

  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsTest3.bpmn");

  private static final String CUSTOM_WORKFLOW_APPROVAL_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApprovalTest.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  @Spy private TranslationService translationService = TestHelper.initTranslationService();

  @Mock private WASContextHandler wasContextHandler;




  private CustomWorkflowConfig initCustomWorkflowConfig() {
    CustomWorkflowConfig loadedCustomWorkflowConfig = null;
    try {
      loadedCustomWorkflowConfig = TestHelper.loadCustomConfigIncludingStatements();
    } catch (Exception e) {
      return null;
    }
    return loadedCustomWorkflowConfig;
  }

  private FilterParameterExtractorUtil initFilterParameterExtractorUtil() {
    return new FilterParameterExtractorUtil(customWorkflowConfig, definitionServiceHelper);
  }

  @Before
  public void mockInitialise() {
    RecurrenceHandler.addHandler(RecurTypeEnum.MONTHLY, monthlyRecurrenceProcessor);
    RecurrenceHandler.addHandler(RecurTypeEnum.WEEKLY, weeklyRecurrenceProcessor);
    RecurrenceHandler.addHandler(RecurTypeEnum.YEARLY, yearlyRecurrenceProcessor);
    RecurrenceHandler.addHandler(RecurTypeEnum.DAILY, dailyRecurrenceProcessor);
    PlaceholderExtractorHelper placeholderExtractorHelper =
        new PlaceholderExtractorHelper(authHelper);
    // Needs custom workflow config as dependency
    Mockito.when(authHelper.getOwnerId()).thenReturn(REALM_ID);
    CustomDefinitionPlaceholderExtractor customDefinitionPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    Mockito.when(conditionalElementFactory.getHandler(any(BaseElement.class)))
        .thenReturn(conditionalElementHandler);
    Mockito.when(
            placeholderExtractorProvider.getPlaceholderExtractor(any(DefinitionInstance.class)))
        .thenReturn(customDefinitionPlaceholderExtractor);
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");

  }

  public void setUp(String recordType) {
    definition = TestHelper.mockCustomWorkflowDefinition(recordType);
    definitionStatement =
        TestHelper.mockCustomWorkflowDefinition(
            RecordType.STATEMENT.getRecordType(),
            CustomWorkflowType.SCHEDULEDACTIONS.getActionKey());
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
  }

  @Test
  public void testCreateCustomWorkflowDefinition_Invoice_DefinitionCreated() {
    setUp(RecordType.INVOICE.getRecordType());
    definition.setDisplayName("Test definition ");
    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(0, parameterDetailsMap.size());
    HandlerDetails handlerDetails =
        getHandlerDetailsForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    HandlerDetails startEventHandlerDetails = getHandlerDetailsFromStartEvent(
        definition.getWorkflowSteps().get(0).getId().getLocalId());

    Trigger trigger = new Trigger();
    trigger.setId(definition.getWorkflowSteps().get(0).getId());
    InputParameter parameter = new InputParameter();
    parameter.setParameterName("entityOperation");
    parameter.setFieldValues(0, "create");
    parameter.setFieldValues(1, "update");
    trigger.add("parameters", List.of(parameter));
    definition.getWorkflowSteps().get(0).setTrigger(trigger);

    Assert.assertNull(startEventHandlerDetails.getHandlerId());

    definitionInstance.setUuid("UUID");
    definitionInstance =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);

    Assert.assertNotNull(definitionInstance);
    startEventHandlerDetails = getHandlerDetailsFromStartEvent(
        definition.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals("intuit-workflows/custom-reminder-start-process",
        startEventHandlerDetails.getHandlerId());

    String CUSTOM_PLACEHOLDER_VALUES_PATH = "placeholder/custom_placeholder_value_invoice.json";
    JSONObject actualPlaceholderValue = new JSONObject(definitionInstance.getPlaceholderValue());
    JSONObject expectedPlaceholderValue =
        new JSONObject(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH));
    Assert.assertEquals(expectedPlaceholderValue.toString(), actualPlaceholderValue.toString());
    // Check that all parameters are set properly for create task as it was selected
    parameterDetailsMap = getParametersForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(15, parameterDetailsMap.size());
    List<String> paramList =
        Arrays.asList(
            "Id",
            "intuit_realmid",
            "ProjectType",
            "TaskType",
            "TaskName",
            "CloseTask",
            "CompanyName",
            "CompanyEmail",
            "CustomerName",
            "CustomerEmail",
            "DocNumber",
            "TxnAmount",
            "TxnBalanceAmount",
            "TxnDueDate",
            "TxnDate");
    parameterDetailsMap.keySet().stream()
        .forEach(key -> Assert.assertTrue(key + " is not present", paramList.contains(key)));
    ParameterDetails parameterDetails = parameterDetailsMap.get("TaskName");
    Assert.assertTrue(parameterDetails.isRequiredByUI());
    Assert.assertTrue(parameterDetails.isRequiredByHandler());
    Assert.assertEquals("string", parameterDetails.getFieldType());
    // Check helpvariable is replace with common helpvariable
    Assert.assertEquals(
        "Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnDueDate]]",
        parameterDetails.getFieldValue().get(0));
    Assert.assertEquals(
        "QB_INVOICE_DUE_REMINDER", parameterDetailsMap.get("ProjectType").getFieldValue().get(0));
    Assert.assertEquals("QB_INVOICE", parameterDetailsMap.get("TaskType").getFieldValue().get(0));
    parameterDetails = parameterDetailsMap.get("TxnAmount");
    Assert.assertTrue(parameterDetails.isRequiredByHandler());
    Assert.assertEquals(
        ParameterDetailsValueType.PROCESS_VARIABLE, parameterDetails.getValueType());

    // Check that handler details are set properly
    HandlerDetails createTaskHandlerDetails =
        getHandlerDetailsForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(
        "intuit-workflows/taskmanager-create-task", createTaskHandlerDetails.getHandlerId());
    Assert.assertEquals("appconnect", createTaskHandlerDetails.getTaskHandler());
    Assert.assertEquals("executeWorkflowAction", createTaskHandlerDetails.getActionName());

    List<String> expectedResponseFields = Arrays.asList("projectId");
    expectedResponseFields.stream()
        .forEach(
            responseField ->
                Assert.assertTrue(
                    createTaskHandlerDetails.getResponseFields().contains(responseField)));

    // Check that all parameters are set properly for create task as it was selected
    parameterDetailsMap = getParametersForAction(this.definition, SEND_COMPANY_EMAIL_ACTION_ID);
    Assert.assertEquals(2, parameterDetailsMap.size());
    Assert.assertEquals("<EMAIL>", parameterDetailsMap.get("SendTo").getFieldValue().get(0));
    Assert.assertEquals(
        "[[DocNumber]] needs your attention",
        parameterDetailsMap.get("Message").getFieldValue().get(0));
    HandlerDetails sendEmailHandlerDetails =
        getHandlerDetailsForAction(this.definition, SEND_COMPANY_EMAIL_ACTION_ID);
    Assert.assertNull(sendEmailHandlerDetails);

    // check that process variables are set properly in start event element
    List<ProcessVariableDetail> processVariablesFromStartEvent =
        getProcessVariablesFromStartEvent(
            definition.getWorkflowSteps().get(0).getId().getLocalId());
    List<String> expectedProcessVariables =
        Arrays.asList(
            "entityChangeType",
            "Id",
            "intuit_userid",
            "intuit_realmid",
            "TxnAmount",
            "dueDate",
            "TxnDueDays",
            "CompanyName",
            "CompanyEmail",
            "CustomerName",
            "CustomerEmail",
            "DocNumber",
            "TxnBalanceAmount",
            "TxnDueDate",
            "TxnDate");
    List<String> actualProcessVariables =
        processVariablesFromStartEvent.stream()
            .map(ProcessVariableDetail::getVariableName)
            .collect(Collectors.toList());
    Assert.assertEquals(14, actualProcessVariables.size());
    actualProcessVariables.forEach(
        variable -> Assert.assertTrue(expectedProcessVariables.contains(variable)));

    // Check type alias
    ProcessVariableDetail txnDueDate =
        processVariablesFromStartEvent.stream()
            .filter(
                processVariableDetail ->
                    processVariableDetail.getVariableName().equalsIgnoreCase("TxnDueDays"))
            .findFirst()
            .get();
    Assert.assertEquals("integer", txnDueDate.getVariableType());

    ProcessVariableDetail amount =
        processVariablesFromStartEvent.stream()
            .filter(
                processVariableDetail ->
                    processVariableDetail.getVariableName().equalsIgnoreCase("TxnAmount"))
            .findFirst()
            .get();
    Assert.assertEquals("double", amount.getVariableType());

    String expectedFilterConditions =
        "{\"rules\":[{\"parameterName\":\"TxnAmount\",\"conditionalExpression\":\"GT 500\"},{\"parameterName\":\"TxnDueDays\",\"conditionalExpression\":\"AF 1\"}]}";

    // check that parameter details are set properly in start event element
    Map<String, ParameterDetails> parameterDetailMap =
        getParameterDetailsFromStartEvent(
            definition.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        expectedFilterConditions,
        parameterDetailMap.get(WorkflowConstants.FILTER_CONDITION).getFieldValue().get(0));
    Assert.assertEquals(
        "invoice",
        parameterDetailMap.get(WorkflowConstants.FILTER_RECORD_TYPE).getFieldValue().get(0));

    // check that parameter details are set properly in wait event element
    parameterDetailMap =
        getParameterDetailsFromWaitEvent(
            MessageFormat.format(
                "{0}_{1}_{2}",
                "customWorkflowWaitEvent",
                DefinitionTestConstants.REALM_ID,
                definitionInstance.getUuid()));
    Assert.assertEquals(
        expectedFilterConditions,
        parameterDetailMap.get(WorkflowConstants.FILTER_CONDITION).getFieldValue().get(0));
    Assert.assertEquals(
        "invoice",
        parameterDetailMap.get(WorkflowConstants.FILTER_RECORD_TYPE).getFieldValue().get(0));

    Mockito.verify(conditionalElementHandler)
        .process(
            eq(this.definition.getWorkflowSteps().get(0).getWorkflowStepCondition()),
            bpmnModelInstanceArgumentCaptor.capture(),
            anyList(),
            definitionIdArgumentCaptor.capture(),
            definitionArgumentCaptor.capture(), eq(false));
    BpmnModelInstance bpmnModelInstance = bpmnModelInstanceArgumentCaptor.getValue();
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_DMN));
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_XOR));

    // Check if actions flows are set properly
    SequenceFlow sequenceFlow =
        bpmnModelInstance.getModelElementById("SequenceFlow_createTask_realm-id_UUID");
    Assert.assertEquals("${true}", sequenceFlow.getConditionExpression().getRawTextContent());

    // This should not happen. We need to fix code
    sequenceFlow =
        bpmnModelInstance.getModelElementById("SequenceFlow_sendExternalEmail_realm-id_UUID");
    Assert.assertEquals(
        "${execution.hasVariable(\"sendExternalEmail\") == true}",
        sequenceFlow.getConditionExpression().getRawTextContent());

    sequenceFlow =
        bpmnModelInstance.getModelElementById("SequenceFlow_sendCompanyEmail_realm-id_UUID");
    Assert.assertEquals("${false}", sequenceFlow.getConditionExpression().getRawTextContent());

    sequenceFlow =
        bpmnModelInstance.getModelElementById("SequenceFlow_sendPushNotification_realm-id_UUID");
    Assert.assertEquals(
        "${execution.hasVariable(\"sendPushNotification\") == true}",
        sequenceFlow.getConditionExpression().getRawTextContent());
  }

  @Test
  public void testCreateCustomWorkflowDefinition_Bill_DefinitionCreated() {
    setUp(RecordType.BILL.getRecordType());
    this.definition.recordType(RecordType.BILL.getRecordType());
    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(0, parameterDetailsMap.size());
    HandlerDetails handlerDetails =
        getHandlerDetailsForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    definitionInstance =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);

    String CUSTOM_PLACEHOLDER_VALUES_PATH = "placeholder/custom_placeholder_value_bill.json";
    JSONObject actualPlaceholderValue = new JSONObject(definitionInstance.getPlaceholderValue());
    JSONObject expectedPlaceholderValue =
        new JSONObject(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH));
    Assert.assertEquals(expectedPlaceholderValue.toString(), actualPlaceholderValue.toString());

    Assert.assertNotNull(definitionInstance);

    // Check that all parameters are set properly
    parameterDetailsMap = getParametersForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(15, parameterDetailsMap.size());
    List<String> paramList =
        Arrays.asList(
            "Id",
            "intuit_realmid",
            "ProjectType",
            "TaskType",
            "TaskName",
            "CloseTask",
            "CompanyName",
            "CompanyEmail",
            "VendorName",
            "DocNumber",
            "TxnAmount",
            "TxnBalanceAmount",
            "TxnDueDate",
            "TxnDate",
            "TxnDueDays");
    Assert.assertTrue(
        parameterDetailsMap.keySet().stream().allMatch(key -> paramList.contains(key)));
    ParameterDetails parameterDetails = parameterDetailsMap.get("TaskName");
    Assert.assertTrue(parameterDetails.isRequiredByUI());
    Assert.assertEquals(
        "Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnDueDate]]",
        parameterDetails.getFieldValue().get(0));

    Assert.assertEquals(
        "QB_BILL_DUE_REMINDER", parameterDetailsMap.get("ProjectType").getFieldValue().get(0));
    Assert.assertEquals("QB_BILL", parameterDetailsMap.get("TaskType").getFieldValue().get(0));
    parameterDetails = parameterDetailsMap.get("DocNumber");
    Assert.assertTrue(parameterDetails.isRequiredByHandler());
    Assert.assertEquals(
        ParameterDetailsValueType.PROCESS_VARIABLE, parameterDetails.getValueType());

    // Check that handler details are set properly
    handlerDetails = getHandlerDetailsForAction(this.definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals("intuit-workflows/taskmanager-create-task", handlerDetails.getHandlerId());
    Assert.assertEquals("appconnect", handlerDetails.getTaskHandler());
    Assert.assertEquals("executeWorkflowAction", handlerDetails.getActionName());

    // check that process variables are set properly in start event element
    List<ProcessVariableDetail> processVariablesFromStartEvent =
        getProcessVariablesFromStartEvent(
            definition.getWorkflowSteps().get(0).getId().getLocalId());
    List<String> expectedProcessVariables =
        Arrays.asList(
            "entityChangeType",
            "Id",
            "intuit_userid",
            "intuit_realmid",
            "TxnAmount",
            "TxnDueDays",
            "CompanyName",
            "CompanyEmail",
            "VendorName",
            "DocNumber",
            "TxnBalanceAmount",
            "TxnDate",
            "TxnDueDate");
    List<String> actualProcessVariables =
        processVariablesFromStartEvent.stream()
            .map(ProcessVariableDetail::getVariableName)
            .collect(Collectors.toList());
    Assert.assertEquals(13, actualProcessVariables.size());
    expectedProcessVariables.forEach(
        variable -> Assert.assertTrue(actualProcessVariables.contains(variable)));

    String expectedFilterConditions =
        "{\"rules\":[{\"parameterName\":\"TxnAmount\",\"conditionalExpression\":\"GT 500\"},{\"parameterName\":\"TxnDueDays\",\"conditionalExpression\":\"AF 1\"}]}";

    // check that parameter details are set properly in start event element
    Map<String, ParameterDetails> parameterDetailMap =
        getParameterDetailsFromStartEvent(
            definition.getWorkflowSteps().get(0).getId().getLocalId());
    Assert.assertEquals(
        expectedFilterConditions,
        parameterDetailMap.get(WorkflowConstants.FILTER_CONDITION).getFieldValue().get(0));
    Assert.assertEquals(
        "bill",
        parameterDetailMap.get(WorkflowConstants.FILTER_RECORD_TYPE).getFieldValue().get(0));
    // check that parameter details are set properly in wait event element
    parameterDetailMap =
        getParameterDetailsFromWaitEvent(
            MessageFormat.format(
                "{0}_{1}_{2}",
                "customWorkflowWaitEvent",
                DefinitionTestConstants.REALM_ID,
                definitionInstance.getUuid()));
    Assert.assertEquals(
        expectedFilterConditions,
        parameterDetailMap.get(WorkflowConstants.FILTER_CONDITION).getFieldValue().get(0));
    Assert.assertEquals(
        "bill",
        parameterDetailMap.get(WorkflowConstants.FILTER_RECORD_TYPE).getFieldValue().get(0));
    Assert.assertEquals(
        "txn_paid",
        parameterDetailMap
            .get(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS)
            .getFieldValue()
            .get(0));

    Mockito.verify(conditionalElementHandler)
        .process(
            eq(this.definition.getWorkflowSteps().get(0).getWorkflowStepCondition()),
            bpmnModelInstanceArgumentCaptor.capture(),
            anyList(),
            definitionIdArgumentCaptor.capture(),
            definitionArgumentCaptor.capture(), eq(false));
    BpmnModelInstance bpmnModelInstance = bpmnModelInstanceArgumentCaptor.getValue();
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_DMN));
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_XOR));
  }

  private HandlerDetails getHandlerDetailsForAction(Definition definition, String actionId) {
    String actionLocalId = getAction(definition, actionId).getAction().getId().getLocalId();
    CamundaInputParameter handlerParameter =
        BpmnProcessorUtil.getCamundaInputParam(
                bpmnModelInstance.getModelElementById(actionLocalId),
                WorkFlowVariables.HANDLER_DETAILS_KEY.getName())
            .get();
    return ObjectConverter.fromJson(
        handlerParameter.getTextContent(), new TypeReference<HandlerDetails>() {});
  }

  private List<ProcessVariableDetail> getProcessVariablesFromStartEvent(String eventId) {
    CamundaProperty camundaProperty =
        BpmnProcessorUtil.getCamundaProperty(
                bpmnModelInstance.getModelElementById(eventId),
                WorkFlowVariables.PROCESS_VARIABLE_DETAILS_KEY.getName())
            .get();
    return ObjectConverter.fromJson(
        camundaProperty.getCamundaValue(), new TypeReference<List<ProcessVariableDetail>>() {});
  }

  private HandlerDetails getHandlerDetailsFromStartEvent(String eventId) {
    CamundaProperty camundaProperty =
        BpmnProcessorUtil.getCamundaProperty(
                bpmnModelInstance.getModelElementById(eventId),
                WorkFlowVariables.HANDLER_DETAILS_KEY.getName())
            .get();
    return ObjectConverter.fromJson(
        camundaProperty.getCamundaValue(), new TypeReference<HandlerDetails>() {});
  }

  private Map<String, ParameterDetails> getParameterDetailsFromStartEvent(String eventId) {
    CamundaProperty camundaProperty =
        BpmnProcessorUtil.getCamundaProperty(
                bpmnModelInstance.getModelElementById(eventId),
                WorkFlowVariables.PARAMETERS_KEY.getName())
            .get();
    return ObjectConverter.fromJson(
        camundaProperty.getCamundaValue(), new TypeReference<Map<String, ParameterDetails>>() {});
  }

  private Map<String, ParameterDetails> getParameterDetailsFromWaitEvent(String eventId) {
    CamundaInputParameter camundaInputParameter =
        BpmnProcessorUtil.getCamundaInputParam(
                bpmnModelInstance.getModelElementById(eventId),
                WorkFlowVariables.PARAMETERS_KEY.getName())
            .get();
    return ObjectConverter.fromJson(
        camundaInputParameter.getTextContent(),
        new TypeReference<Map<String, ParameterDetails>>() {});
  }

  private ActionMapper getAction(Definition definition, String actionId) {
    return definition.getWorkflowSteps().get(0).getActions().stream()
        .filter(mapper -> mapper.getAction().getId().getLocalId().startsWith(actionId))
        .findFirst()
        .orElse(null);
  }

  private Map<String, ParameterDetails> getParametersForAction(
      Definition definition, String actionId) {
    ActionMapper actionMapper = getAction(definition, actionId);
    Assert.assertNotNull(actionMapper);
    String actionLocalId = actionMapper.getAction().getId().getLocalId();

    CamundaInputParameter inputParameter =
        BpmnProcessorUtil.getCamundaInputParam(
                bpmnModelInstance.getModelElementById(actionLocalId),
                WorkFlowVariables.PARAMETERS_KEY.getName())
            .get();
    Map<String, ParameterDetails> parameterDetailsMap =
        ObjectConverter.fromJson(
            inputParameter.getTextContent(), new TypeReference<Map<String, ParameterDetails>>() {});
    return parameterDetailsMap;
  }

  private com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
      getCreateTaskAction(String recordType, String actionKey) {
    return customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
        .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
        .map(ActionGroup::getActions)
        .flatMap(Collection::stream)
        .filter(action -> action.getId().equals(CREATE_TASK))
        .findFirst()
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_ACTION_KEY));
  }

  @Test
  public void testGetProjectForAction() {
    ActionMapper actionMapper = getActionMapper("CloseTask", "Txn_Paid");
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
        invoiceRecordAction = getCreateTaskAction("Invoice", "reminder");
    ProjectType projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "invoice", invoiceRecordAction);
    Assert.assertEquals(ProjectType.QB_INVOICE_DUE_REMINDER, projectType);

    actionMapper = getActionMapper("CloseTask", "Txn_Sent");
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "invoice", invoiceRecordAction);
    Assert.assertEquals(ProjectType.QB_INVOICE_UNSENT_REMINDER, projectType);

    actionMapper = getActionMapper("CloseTask", "close_manually");
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "invoice", invoiceRecordAction);
    Assert.assertEquals(ProjectType.QB_INVOICE_REMINDER, projectType);

    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
        billReminderRecordAction = getCreateTaskAction("bill", "reminder");
    actionMapper = getActionMapper("CloseTask", "close_manually");
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "bill", billReminderRecordAction);
    Assert.assertEquals(ProjectType.QB_BILL_REMINDER, projectType);

    actionMapper = getActionMapper("CloseTask-2", "close_manually");

    projectType = CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "bill", billReminderRecordAction);
    Assert.assertNull(projectType);

    actionMapper = getActionMapper("CloseTask", "close_manually");
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "sales", invoiceRecordAction);
    Assert.assertNull(projectType);

    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
        billApprovalRecordAction = getCreateTaskAction("bill", "approval");
    actionMapper = getActionMapper("CloseTask", "txn_approval_changed", "approval");
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "bill", billApprovalRecordAction);

    Assert.assertEquals(ProjectType.QB_BILL_APPROVAL, projectType);

    // for bill approval, config close task has default value, hence it will return project
    actionMapper =
        new ActionMapper()
            .actionKey("approval")
            .action(
                new Action()
                    .parameter(new InputParameter())
                    .id(getGlobalId(CREATE_TASK_ACTION_ID)));
    projectType =
        CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "bill", billApprovalRecordAction);
    Assert.assertEquals(ProjectType.QB_BILL_APPROVAL, projectType);

    // for bill reminder, config close task does not have default value, so  we will get an
    // exception
    try {
      CustomWorkflowUtil.getProjectTypeForAction(actionMapper, "bill", billReminderRecordAction);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.CLOSE_TASK_ACTION_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  private ActionMapper getActionMapper(String parameterName, String closeTaskValue) {
    return getActionMapper(parameterName, closeTaskValue, "reminder");
  }

  private ActionMapper getActionMapper(
      String parameterName, String closeTaskValue, String actionKey) {
    return new ActionMapper()
        .actionKey(actionKey)
        .action(
            new Action()
                .parameter(
                    new InputParameter().parameterName(parameterName).fieldValue(closeTaskValue))
                .id(getGlobalId(CREATE_TASK_ACTION_ID)));
  }

  @Test
  public void testBillApprovalCloseTask() {

    Definition definitionCloseTask = getDefinitionClosedTaskDetails("bill", "approval", true);

    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_APPROVAL_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    Mockito.when(conditionalElementFactory.getHandler(any(BaseElement.class)))
        .thenReturn(conditionalElementHandler);
    definitionCloseTask.setDisplayName("Test definition ");
    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(definitionCloseTask, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(0, parameterDetailsMap.size());
    HandlerDetails handlerDetails =
        getHandlerDetailsForAction(definitionCloseTask, CREATE_TASK_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definitionCloseTask,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    definitionInstance =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);
    // simple assert to check that even without close task details in definition, we are able to
    // create bill approval definition
    Assert.assertNotNull(definitionInstance);

    // Check that all parameters are set properly
    parameterDetailsMap = getParametersForAction(definitionCloseTask, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(14, parameterDetailsMap.size());
    // close task is not present in params
    List<String> paramList =
        Arrays.asList(
            "Id",
            "intuit_realmid",
            "ProjectType",
            "TaskType",
            "TaskName",
            "CompanyName",
            "CompanyEmail",
            "VendorName",
            "DocNumber",
            "TxnAmount",
            "TxnBalanceAmount",
            "TxnDueDate",
            "TxnDate",
            "TxnDueDays");
    Assert.assertTrue(
        parameterDetailsMap.keySet().stream().allMatch(key -> paramList.contains(key)));
    // project type is set as bill approval
    Assert.assertEquals(
        "QB_BILL_APPROVAL", parameterDetailsMap.get("ProjectType").getFieldValue().get(0));
    Assert.assertEquals("QB_BILL", parameterDetailsMap.get("TaskType").getFieldValue().get(0));
  }

  @Test
  public void testCloseTaskNotFoundException() {
    Definition definitionCloseTask = getDefinitionClosedTaskDetails();

    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    definitionCloseTask.setDisplayName("Test definition ");
    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(definitionCloseTask, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(0, parameterDetailsMap.size());
    HandlerDetails handlerDetails =
        getHandlerDetailsForAction(definitionCloseTask, CREATE_TASK_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definitionCloseTask,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    try {
      createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.CLOSE_TASK_ACTION_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testCreateDefWithCreateTaskNotSelected() {
    // getting create task action with no close task param and selected as false
    Definition definitionCloseTask = getDefinitionClosedTaskDetails("bill", "approval", false);

    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    Mockito.when(conditionalElementFactory.getHandler(any(BaseElement.class)))
        .thenReturn(conditionalElementHandler);
    definitionCloseTask.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definitionCloseTask,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    DefinitionInstance definitionInstanceResult =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);
    Assert.assertEquals(DEFINITION_NAME, definitionInstanceResult.getDefinition().getName());
  }

  private GlobalId getGlobalId(String localId) {
    return GlobalId.builder().setRealmId(REALM_ID).setLocalId(localId).build();
  }

  private Definition getDefinitionClosedTaskDetails() {
    return getDefinitionClosedTaskDetails(RecordType.INVOICE.getRecordType(), "reminder", true);
  }

  private Definition getDefinitionClosedTaskDetails(
      String recordType, String actionKey, boolean isSelected) {

    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    InputParameter taskNameParameter =
        new InputParameter()
            .parameterName("TaskName")
            .fieldValue(
                String.format(
                    "Review [[%s Number]] for Amount [[Total Amount]] and Balance [[Due Date]]",
                    StringUtils.capitalize(RecordType.INVOICE.getRecordType())))
            .required(false) // This should not have impact
            .parameterType(FieldTypeEnum.DOUBLE); // This should not have impact

    Action createTaskAction =
        new Action()
            .id(getGlobalId(CREATE_TASK_ACTION_ID))
            .parameter(taskNameParameter)
            .selected(isSelected);
    workflowStep =
        workflowStep.action(new ActionMapper().action(createTaskAction).actionKey(actionKey));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
            .rule(new RuleLine.Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);

    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }

  @Test
  public void testCreateCustomWorkflowDefinition_Statement_DefinitionCreated() {
    setUp(RecordType.STATEMENT.getRecordType());
    this.definitionStatement =
        TestHelper.mockStatementWorkflowDefinition(
            RecordType.STATEMENT.getRecordType(), "scheduledActions");
    this.definitionStatement.recordType(RecordType.STATEMENT.getRecordType());
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRule.setRecurType(RecurTypeEnum.MONTHLY);
    recurrenceRule.setActive(true);
    recurrenceRule.setStartDate(new DateTime());
    definitionStatement.setRecurrence(recurrenceRule);
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(this.definitionStatement, SEND_STATEMENT_ACTION_ID);

    Assert.assertNull(parameterDetailsMap);
    HandlerDetails handlerDetails =
        getHandlerDetailsForAction(this.definitionStatement, SEND_STATEMENT_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definitionStatement,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    definitionInstance =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);

    Assert.assertNotNull(definitionInstance);

    // check that process variables are set properly in start event element
    List<ProcessVariableDetail> processVariablesFromStartEvent =
        getProcessVariablesFromStartEvent(
            definitionStatement.getWorkflowSteps().get(0).getId().getLocalId());

    Assert.assertNotNull(processVariablesFromStartEvent);

    Mockito.verify(conditionalElementHandler)
        .process(
            eq(this.definitionStatement.getWorkflowSteps().get(0).getWorkflowStepCondition()),
            bpmnModelInstanceArgumentCaptor.capture(),
            anyList(),
            definitionIdArgumentCaptor.capture(),
            definitionArgumentCaptor.capture(), eq(false));
    BpmnModelInstance bpmnModelInstance = bpmnModelInstanceArgumentCaptor.getValue();

    // Asserting for parameterDetails (Filter Conditions)
    BaseElement baseElement =
        bpmnModelInstance.getModelElementById("scheduledAction_realm-id_UUID");
    ExtensionElements elements = baseElement.getExtensionElements();
    Optional<CamundaInputParameter> inputParameterOptional =
        elements.getElementsQuery().filterByType(CamundaInputOutput.class).list().stream()
            .map(CamundaInputOutput::getCamundaInputParameters)
            .flatMap(Collection::stream)
            .filter(
                parameter ->
                    WorkFlowVariables.PARAMETERS_KEY.getName().equals(parameter.getCamundaName()))
            .findFirst();
    Map<String, ParameterDetails> parameterDetailMap = new HashMap<>();
    inputParameterOptional.ifPresent(
        inputParameter -> {
          parameterDetailMap.putAll(
              ObjectConverter.fromJson(
                  inputParameter.getTextContent(),
                  new TypeReference<Map<String, ParameterDetails>>() {}));
          String jsonString = ObjectConverter.toJson(parameterDetailMap);
          inputParameter.setTextContent(jsonString);
        });
    Assert.assertNotNull(parameterDetailMap);
    Assert.assertNotNull(parameterDetailMap.get("FilterCondition"));
    RuleLine rules =
        ObjectConverter.fromJson(
            parameterDetailMap.get("FilterCondition").getFieldValue().get(0),
            new TypeReference<RuleLine>() {});
    Assert.assertNotNull(rules);
    Assert.assertEquals(1, rules.getRules().size());
    Assert.assertNotNull(parameterDetailMap.get("FilterRecordType"));

    ParameterDetails filterRecordType = parameterDetailMap.get("FilterRecordType");
    Assert.assertTrue(filterRecordType.isRequiredByHandler());

    ParameterDetails filterCondition = parameterDetailMap.get("FilterCondition");
    Assert.assertTrue(filterCondition.isRequiredByHandler());

    Assert.assertEquals(
        "statement", parameterDetailMap.get("FilterRecordType").getFieldValue().get(0));

    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_DMN));
    Assert.assertNull(
        bpmnModelInstance.getModelElementById(DefinitionTestConstants.CONDITION_ID_XOR));
  }

  @Test
  public void testBillApprovalWithLocalisedNonUserConfiguredBlocksForEnLocale() {

    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en");
    processDefinitionInstanceForBillApproval();
    List<String> paramList =
        Arrays.asList(
            "intuit_userid",
            "Subject",
            "Message",
            "Id",
            "intuit_realmid",
            "Send email",
            "Send notification",
            "Workflow Type",
            "Mobile Notification Title",
            "Mobile Notification Action",
            "Mobile Notification Subject");

    // sendRejectNotification_txnApproval
    Map<String, String> fieldValueKeyMap =
        Map.of(
            "Subject",
            "Bill [[DocNumber]] is denied approval",
            "Message",
            "Hi,\n"
                + "\n"
                + "Bill [[DocNumber]] was denied approval by [[ApproverName]].[[Comment]]\n"
                + "\n"
                + "Warm regards,\n"
                + "[[CompanyName]]");
    testBillApprovalNonUserConfiguredBlock(
        "sendRejectNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);
    // sendAutoRejectNotification_txnApproval
    fieldValueKeyMap =
        Map.of(
            "Subject",
            "Bill [[DocNumber]] is denied approval",
            "Message",
            "Hi,\n"
                + "\n"
                + "Bill [[DocNumber]] couldn’t be approved. The designated approver didn’t take action within 30 days of it being sent for approval. You need to resend the bill for approval.\n"
                + "\n"
                + "Warm regards,\n"
                + "[[CompanyName]]");
    testBillApprovalNonUserConfiguredBlock(
        "sendAutoRejectNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);

    // sendApproveNotification_txnApproval
    paramList =
        Arrays.asList(
            "intuit_userid",
            "Subject",
            "Message",
            "Id",
            "intuit_realmid",
            "IsEmail",
            "IsMobile",
            "Workflow Type",
            "NotificationAction");
    fieldValueKeyMap =
        Map.of(
            "Subject",
            "Bill [[DocNumber]] is approved",
            "Message",
            "Hi,\n"
                + "\n"
                + "Bill [[DocNumber]] was approved by [[ApproverName]].\n"
                + "\n"
                + "Warm regards,\n"
                + "[[CompanyName]]");

    testBillApprovalNonUserConfiguredBlock(
        "sendApproveNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);
  }

  @Test
  public void testBillApprovalWithLocalisedNonUserConfiguredBlocksForFrCALocale() {

    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
    processDefinitionInstanceForBillApproval();
    List<String> paramList =
        Arrays.asList(
            "intuit_userid",
            "Subject",
            "Message",
            "Id",
            "intuit_realmid",
            "Send email",
            "Send notification",
            "Workflow Type",
            "Mobile Notification Title",
            "Mobile Notification Action",
            "Mobile Notification Subject");

    // sendRejectNotification_txnApproval
    Map<String, String> fieldValueKeyMap =
        Map.of(
            "Subject",
            "L’approbation de Facture à payer [[DocNumber]] a été refusée",
            "Message",
            "Bonjour,\n"
                + "\n"
                + "Facture à payer [[DocNumber]] s’est vu refuser l’approbation par [[ApproverName]].[[Comment]]\n"
                + "\n"
                + "Cordialement,\n"
                + "[[CompanyName]]");
    testBillApprovalNonUserConfiguredBlock(
        "sendRejectNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);
    // sendAutoRejectNotification_txnApproval
    fieldValueKeyMap =
        Map.of(
            "Subject",
            "L’approbation de Facture à payer [[DocNumber]] a été refusée",
            "Message",
            "Bonjour,\n"
                + "\n"
                + "Facture à payer [[DocNumber]]n’a pas été approuvé(e) L’approbateur désigné n’a pas agi dans les 30 jours suivant sa réception. Vous devez renvoyer le (la) facture à payer pour approbation.\n"
                + "\n"
                + " Cordialement,\n"
                + "[[CompanyName]]");
    testBillApprovalNonUserConfiguredBlock(
        "sendAutoRejectNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);

    // sendApproveNotification_txnApproval
    paramList =
        Arrays.asList(
            "intuit_userid",
            "Subject",
            "Message",
            "Id",
            "intuit_realmid",
            "IsEmail",
            "IsMobile",
            "Workflow Type",
            "NotificationAction");
    fieldValueKeyMap =
        Map.of(
            "Subject",
            "Facture à payer [[DocNumber]] est approuvé(e)",
            "Message",
            "Bonjour,\n"
                + "\n"
                + "Facture à payer [[DocNumber]] a été approuvé(e) par [[ApproverName]].\n"
                + "\n"
                + " Cordialement,\n"
                + "[[CompanyName]]");

    testBillApprovalNonUserConfiguredBlock(
        "sendApproveNotification_txnApproval_realm-id_UUID", fieldValueKeyMap, paramList);
  }

  private void testBillApprovalNonUserConfiguredBlock(
      String actionId, Map<String, String> fieldValueKeyMap, List<String> paramList) {
    Map<String, ParameterDetails> parameterDetailsMap =
        getParameterDetailsMapForNonUserConfiguredId(actionId);
    Assert.assertEquals(paramList.size(), parameterDetailsMap.size());
    Assert.assertTrue(
        parameterDetailsMap.keySet().stream().allMatch(key -> paramList.contains(key)));
    for (String fieldValue : fieldValueKeyMap.keySet()) {
      Assert.assertEquals(
          fieldValueKeyMap.get(fieldValue),
          parameterDetailsMap.get(fieldValue).getFieldValue().get(0));
    }
  }

  private void processDefinitionInstanceForBillApproval() {
    definition = getDefinitionClosedTaskDetails("bill", "approval", false);
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_APPROVAL_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    Mockito.when(conditionalElementFactory.getHandler(any(BaseElement.class)))
        .thenReturn(conditionalElementHandler);
    definition.setDisplayName("Test definition");
    Map<String, ParameterDetails> parameterDetailsMap =
        getParametersForAction(definition, CREATE_TASK_ACTION_ID);
    Assert.assertEquals(0, parameterDetailsMap.size());
    HandlerDetails handlerDetails = getHandlerDetailsForAction(definition, CREATE_TASK_ACTION_ID);
    Assert.assertNull(handlerDetails.getActionName());
    Assert.assertNull(handlerDetails.getTaskHandler());
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    definitionInstance.setUuid("UUID");
    definitionInstance =
        createCustomDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);

    // create bill approval definition
    Assert.assertNotNull(definitionInstance);
  }

  private Map<String, ParameterDetails> getParameterDetailsMapForNonUserConfiguredId(String id) {
    CamundaInputParameter inputParameter =
        BpmnProcessorUtil.getCamundaInputParam(
                bpmnModelInstance.getModelElementById(id),
                WorkFlowVariables.PARAMETERS_KEY.getName())
            .get();
    Map<String, ParameterDetails> parameterDetailsMap =
        ObjectConverter.fromJson(
            inputParameter.getTextContent(), new TypeReference<Map<String, ParameterDetails>>() {});
    return parameterDetailsMap;
  }
}
