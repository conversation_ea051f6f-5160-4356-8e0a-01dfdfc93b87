package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECURRENCE_RECUR_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RULE_LINE_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.MonthlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.intuit.v4.workflows.RuleLine;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CustomDefinitionPlaceholderExtractorTest {

  private CustomWorkflowConfig customWorkflowConfig;
  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private Definition definition;
  @Mock private WASContextHandler wasContextHandler;
  @Mock
  private PlaceholderExtractorHelper placeholderExtractorHelper;

  @Mock
  MonthlyRecurrenceProcessor mockMonthlyRecurrenceProcessor;

  private static final String SINGLE_CUSTOM_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/singleCustomReminderTest.bpmn");
  private static final String SINGLE_CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/singleCustomReminder.dmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingle.bpmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_customScheduledActions2.dmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingleTest.bpmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST_2 =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingleTest2.bpmn");

  public void setUp(String recordType) throws Exception {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    MockitoAnnotations.initMocks(this);
    definition = TestHelper.mockProcessedCustomWorkflowDefinition(recordType);
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(SINGLE_CUSTOM_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(SINGLE_CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    RecurrenceHandler.addHandler(RecurTypeEnum.MONTHLY, mockMonthlyRecurrenceProcessor);
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesUserVariables()
      throws Exception {
    setUp(RecordType.INVOICE.getRecordType());
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
            new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("reminder:createTask");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("CloseTask");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("txn_paid"), attributeFieldValue);
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionDmnPlaceholderValues() throws Exception {
    setUp(RecordType.INVOICE.getRecordType());
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> dmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(DMN_PLACEHOLDER_VALUES);
    List<RuleLine.Rule> ruleLinePlaceholderValue = (List<RuleLine.Rule>) dmnPlaceholderValue.get(RULE_LINE_VARIABLES);
    ruleLinePlaceholderValue.forEach(rule -> {
      Assert.assertTrue(rule.containsKey("parameterName") && rule.getParameterName() != null);
      Assert.assertTrue(rule.get("parameterName").equals("TxnAmount") || rule.getParameterName().equals("TxnDueDays"));
      Assert.assertTrue(rule.containsKey("conditionalExpression") && rule.getConditionalExpression() != null);
      Assert.assertTrue(rule.get("conditionalExpression").equals("GT 500") || rule.get("conditionalExpression").equals("AF 1"));
    });
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesProcessVariables()
      throws Exception {
    setUp(RecordType.INVOICE.getRecordType());
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
            new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData actionProcessVariable =
        (ProcessVariableData) userPlaceholderValues.get("createTask");
    Assert.assertEquals("String", actionProcessVariable.getType());
    Assert.assertEquals("true", actionProcessVariable.getValue());
  }

  @Test
  public void testCreateCustomDefinitionwithCreateTaskActionSelectedFalse() throws Exception {
    setUp(RecordType.INVOICE.getRecordType());
    definition = TestHelper.mockProcessedCustomWorkflowDefinitionCreateTaskActionSelectedFalse("invoice");
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper,wasContextHandler);
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData actionProcessVariable =
        (ProcessVariableData) userPlaceholderValues.get("createTask");
    Assert.assertEquals("String", actionProcessVariable.getType());
    Assert.assertEquals("false", actionProcessVariable.getValue());
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesUserVariablesRecurrence() throws Exception{
    setUp(RecordType.INVOICE.getRecordType());
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_DMN_XML, Charset.defaultCharset()));
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> processVariables =
        (Map<String, Object>) bpmnPlaceholderValue.get("process_variables");

    when(mockMonthlyRecurrenceProcessor.getRecurrence(definition.getRecurrence())).thenReturn("0 */2 * * * ? *");

    Assert.assertTrue(userPlaceholderValues.containsKey(WorkFlowVariables.RECURRENCE_RULE_KEY.getName()));
    String recurrenceRuleJson = (String) userPlaceholderValues.get(WorkFlowVariables.RECURRENCE_RULE_KEY.getName());
    JSONObject recurrenceRule = ObjectConverter.convertObject(recurrenceRuleJson, JSONObject.class);
    Assert.assertEquals(recurrenceRule.get(RECURRENCE_RECUR_TYPE), RecurTypeEnum.MONTHLY.toString());
    Assert.assertTrue(processVariables.containsKey("startDate"));
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesUserVariablesRecurrenceFail() throws Exception{
    setUp(RecordType.INVOICE.getRecordType());
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_DMN_XML, Charset.defaultCharset()));
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> processVariables =
        (Map<String, Object>) bpmnPlaceholderValue.get("process_variables");

    Assert.assertFalse(processVariables.containsKey("startDate"));
    Assert.assertFalse(processVariables.containsKey("cronExpression"));
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesUserVariablesRecurrenceFail2() throws Exception{
    setUp(RecordType.INVOICE.getRecordType());
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST_2, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_DMN_XML, Charset.defaultCharset()));
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    CustomDefinitionPlaceholderExtractor customPlaceholderExtractor =
        new CustomDefinitionPlaceholderExtractor(customWorkflowConfig, placeholderExtractorHelper, wasContextHandler);
    when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn("createTask");
    Map<String, Object> testPlaceholderValue =
        customPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> processVariables =
        (Map<String, Object>) bpmnPlaceholderValue.get("process_variables");

    Assert.assertFalse(processVariables.containsKey("startDate"));
    Assert.assertFalse(processVariables.containsKey("cronExpression"));
  }
}
