package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

public class DeleteDefinitionAsyncTaskTest {

    @Mock
    private DefinitionServiceHelper definitionServiceHelper;

    @Mock
    private DataStoreDeleteTaskService dataStoreDeleteTaskService;

    @Mock
    private MetricLogger metricLogger;

    private DeleteDefinitionAsyncTask deleteDefinitionTask;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        deleteDefinitionTask = new DeleteDefinitionAsyncTask(definitionServiceHelper, dataStoreDeleteTaskService, metricLogger);
    }

    @Test
    public void testExecute_Success_WithValidState() {
        // Arrange
        State state = new State();
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "test123");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("test123");
        state.addValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST, Arrays.asList(definitionDetails));
        List<DefinitionDetails> definitionDetailsList = state.getValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST);
        Mockito.doNothing().when(dataStoreDeleteTaskService).deleteDefinitions(definitionDetailsList);
        Mockito.doNothing().when(definitionServiceHelper).deleteAllByParentIdIn(Mockito.anyList());

        // Act
        State resultState = deleteDefinitionTask.execute(state);

        // Assert
        assertEquals(state, resultState);
        Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
        Mockito.verify(definitionServiceHelper, Mockito.times(1)).deleteAllByParentIdIn(Mockito.anyList());
    }

    @Test
    public void testExecute_Success_WithEmptyDefinitionsList() {
        // Arrange
        State state = new State();
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "test123");
        state.addValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST, Arrays.asList());
        List<DefinitionDetails> definitionDetailsList = state.getValue(AsyncTaskConstants.DEFINITION_DETAILS_LIST);
        Mockito.doNothing().when(dataStoreDeleteTaskService).deleteDefinitions(definitionDetailsList);
        Mockito.doNothing().when(definitionServiceHelper).deleteAllByParentIdIn(Mockito.anyList());

        // Act
        State resultState = deleteDefinitionTask.execute(state);

        // Assert
        assertEquals(state, resultState);
        Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
        Mockito.verify(definitionServiceHelper, Mockito.times(1)).deleteAllByParentIdIn(Mockito.anyList());
    }

}