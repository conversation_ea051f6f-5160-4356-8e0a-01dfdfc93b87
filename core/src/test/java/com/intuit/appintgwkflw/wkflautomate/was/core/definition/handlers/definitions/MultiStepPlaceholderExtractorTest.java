package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CONFIG_PARAMETER_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.MonthlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.PlaceholderParameterAttribute;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MultiStepPlaceholderExtractorTest {

  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private MonthlyRecurrenceProcessor mockMonthlyRecurrenceProcessor;
  @Mock
  private WASContextHandler wasContextHandler;

  private Definition multiConditionDefinition;

  private BpmnModelInstance bpmnModelInstance;

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  @Before
  public void setUp() throws Exception {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    RecurrenceHandler.addHandler(RecurTypeEnum.MONTHLY, mockMonthlyRecurrenceProcessor);
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
  }

  @Test
  public void extractPlaceholders() {
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            multiConditionDefinition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    Map<String, Object> testPlaceholderValue =
        multiStepPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);

    Map<String, PlaceholderParameterAttribute> configParam = (Map<String, PlaceholderParameterAttribute>) testPlaceholderValue.get(CONFIG_PARAMETER_VARIABLES);
    Assert.assertNotNull(testPlaceholderValue);
    Assert.assertEquals(testPlaceholderValue.size(), 4);
    Assert.assertEquals(configParam.size(), 3);
    Assert.assertNotNull(testPlaceholderValue.get(USER_META_DATA));
    Assert.assertNotNull(testPlaceholderValue.get(PROCESS_VARIABLES));
  }

  @Test(expected = Exception.class)
  public void extractPlaceholdersWithNullActionKey() {
    Definition definition = new Definition();
    WorkflowStep workflowStep = new WorkflowStep();

    ActionGroup actionGroup = new ActionGroup();
    workflowStep.setActionGroup(actionGroup);
    definition.setWorkflowSteps(Collections.singletonList(workflowStep));
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    Map<String, Object> testPlaceholderValue =
        multiStepPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance);

    Map<String, PlaceholderParameterAttribute> configParam = (Map<String, PlaceholderParameterAttribute>) testPlaceholderValue.get(CONFIG_PARAMETER_VARIABLES);
    Assert.assertNotNull(testPlaceholderValue);
    Assert.assertEquals(testPlaceholderValue.size(), 4);
    Assert.assertEquals(configParam.size(), 3);
    Assert.assertNotNull(testPlaceholderValue.get(USER_META_DATA));
    Assert.assertNotNull(testPlaceholderValue.get(PROCESS_VARIABLES));
  }

}
