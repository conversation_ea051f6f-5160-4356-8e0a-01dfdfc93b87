package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PlaceholderExtractorHelperTest {

  @Mock
  private AuthHelper authHelper;
  @InjectMocks
  private PlaceholderExtractorHelper placeholderExtractorHelper;
  private Action action;
  private GlobalId globalId = GlobalId.builder().setLocalId("sendForApproval_txnApproval_abcd")
      .setRealmId("abcd").build();

  @Before
  public void setup() {
    action = new com.intuit.v4.workflows.Action();
    action.setId(globalId);
    action.setSelected(true);
    action.setParameters(fetchParameter("actionName", "111111"));
  }

  @Test
  public void testActionIdFromAction() {
    Mockito.when(authHelper.getOwnerId()).thenReturn("abcd");
    Assert.assertEquals(placeholderExtractorHelper.getOriginalActionId(action),
        "sendForApproval_txnApproval");
  }

  @Test
  public void testActionIdFromTrigger() {
    Trigger trigger = new Trigger();
    trigger.setId(globalId);
    Mockito.when(authHelper.getOwnerId()).thenReturn("abcd");
    Assert.assertEquals(placeholderExtractorHelper.getOriginalActionIdFromTrigger(trigger),
        "sendForApproval_txnApproval");
  }

  @Test
  public void testActionIdFromLocalId() {
    Mockito.when(authHelper.getOwnerId()).thenReturn("abcd");
    Assert.assertEquals(
        placeholderExtractorHelper.getActionIdFromLocalId("sendForApproval_txnApproval_abcd"),
        "sendForApproval_txnApproval");
  }

  private List<InputParameter> fetchParameter(String actionName, String values) {
    List<InputParameter> parameterList = new ArrayList<>();
    InputParameter inputParameter = new InputParameter();
    inputParameter.setConfigurable(true);
    inputParameter.setParameterName(actionName);
    inputParameter.setParameterType(FieldTypeEnum.STRING);
    List<String> fieldValues = new ArrayList<>();
    fieldValues.add(values);
    inputParameter.setFieldValues(fieldValues);
    parameterList.add(inputParameter);
    return parameterList;
  }
}
