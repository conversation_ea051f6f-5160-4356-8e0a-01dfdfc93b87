package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Collections;
import junit.framework.Assert;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class PlaceholderExtractorProviderTest {

  // The class to be mocked and tested is PlaceholderExtractorProvider
  @InjectMocks private PlaceholderExtractorProvider placeholderExtractorProvider;
  @Mock private FeatureFlagManager featureFlagManager;
  @Mock private CustomDefinitionPlaceholderExtractor customDefinitionPlaceholderExtractor;
  @Mock private PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor;

  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private Definition definition;

  private DefinitionInstance setup_CustomDefinition() {

    final String CUSTOM_WORKFLOW_BPMN_XML =
        TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");

    final String CUSTOM_WORKFLOW_DMN_XML =
        TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

    bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_BPMN_XML, Charset.defaultCharset()));

    dmnModelInstance = Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition, bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).templateName("customReminder").build());
    return definitionInstance;
  }

  private  DefinitionInstance setup_PreCannedDefinition() {
    final String BPMN_XML = TestHelper.readResourceAsString("bpmn/bankDepositReminder.bpmn");
    final String DMN_XML = TestHelper.readResourceAsString("dmn/decision_bankDepositReminder.dmn");
    bpmnModelInstance = Bpmn.readModelFromStream(IOUtils.toInputStream(BPMN_XML));
    dmnModelInstance = Dmn.readModelFromStream(IOUtils.toInputStream(DMN_XML));

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition, bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateName("depositbankreminder").build());
    return definitionInstance;
  }

  @Test
  public void testGetPlaceholderExtractorCustomDefinitionFlagOn() {
    DefinitionInstance definitionInstance = setup_CustomDefinition();
    PlaceholderExtractor placeholderExtractorInstance =
        placeholderExtractorProvider.getPlaceholderExtractor(definitionInstance);
    Assert.assertTrue(placeholderExtractorInstance instanceof CustomDefinitionPlaceholderExtractor);
  }

  @Test
  public void testGetPlaceholderExtractorPrecannedDefinitionFlagOn() {
    DefinitionInstance definitionInstance = setup_PreCannedDefinition();
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(true);
    PlaceholderExtractor placeholderExtractorInstance =
        placeholderExtractorProvider.getPlaceholderExtractor(definitionInstance);
    Assert.assertTrue(
        placeholderExtractorInstance instanceof PrecannedDefinitionPlaceholderExtractor);
  }

  @Test
  public void testGetPlaceholderExtractorPrecannedDefinitionFlagOff() {
    DefinitionInstance definitionInstance = setup_PreCannedDefinition();
    Mockito.when(featureFlagManager.getBoolean(any(String.class), any(Boolean.class), any(String.class)))
        .thenReturn(false);
    PlaceholderExtractor placeholderExtractorInstance =
        placeholderExtractorProvider.getPlaceholderExtractor(definitionInstance);
    Assert.assertNull(placeholderExtractorInstance);
  }
}
