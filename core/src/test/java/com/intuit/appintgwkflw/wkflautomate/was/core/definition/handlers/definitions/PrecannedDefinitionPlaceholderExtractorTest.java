package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RULE_LINE_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WORKFLOW_STEP_CONDITION_TYPE;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PrecannedDefinitionPlaceholderExtractorTest {

  private static final String SINGLE_BANK_DEPOSIT_REMINDER_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/bankDepositReminder.bpmn");
  private static final String SINGLE_BANK_DEPOSIT_REMINDER_DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_bankDepositReminder.dmn");
  private static final String SINGLE_QBDT_BILL_APPROVAL_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/billapproval_QBDT.bpmn");
  private static final String SINGLE_QBDT_BILL_APPROVAL_DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_billapproval_QBDT.dmn");
  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private BpmnModelInstance qbdtBillApprovalBpmnModelInstance;
  private DmnModelInstance qbdtBillApprovalDmnModelInstance;
  private Definition definition;
  @Mock
  private PlaceholderExtractorHelper placeholderExtractorHelper;
  @Mock
  private WASContextHandler wasContextHandler;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    definition = TestHelper.mockDefinitionBankDepositReminder();
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(SINGLE_BANK_DEPOSIT_REMINDER_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(SINGLE_BANK_DEPOSIT_REMINDER_DMN_XML, Charset.defaultCharset()));
    qbdtBillApprovalBpmnModelInstance = Bpmn.readModelFromStream(
        IOUtils.toInputStream(SINGLE_QBDT_BILL_APPROVAL_BPMN_XML, Charset.defaultCharset()));

    qbdtBillApprovalDmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(SINGLE_QBDT_BILL_APPROVAL_DMN_XML, Charset.defaultCharset()));

    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionBpmnPlaceholderValuesUserVariables()
      throws Exception {
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendNotification_bankdepositreminder");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get(
            "sendNotification_bankdepositreminder");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("Approver #1");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("****************"), attributeFieldValue);
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApproval() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithNullTrigger() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    definition.getWorkflowSteps().get(0).setTrigger(null);
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertNull(processVariableData);
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithNullAction() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    definition.getWorkflowSteps().get(0).getActions().get(0).setAction(null);
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Assert.assertNull(actionPlaceholderValues);

  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithNullParametersForTrigger() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    definition.getWorkflowSteps().get(0).getTrigger().setParameters(null);
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertNull(processVariableData);
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithOtherTriggerParams() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    definition.getWorkflowSteps().get(0).getTrigger()
        .setParameters(fetchParameter("actionName", "sendForApproval"));
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "actionName");
    Assert.assertEquals(processVariableData.getValue(), "sendForApproval");
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionDmnPlaceholderValues() throws Exception {
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn(
        "sendNotification_bankdepositreminder");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> dmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(DMN_PLACEHOLDER_VALUES);
    List<RuleLine.Rule> ruleLinePlaceholderValue = (List<RuleLine.Rule>) dmnPlaceholderValue.get(
        RULE_LINE_VARIABLES);
    ruleLinePlaceholderValue.forEach(rule -> {
      Assert.assertTrue(rule.containsKey("parameterName") && rule.getParameterName() != null);
      Assert.assertTrue(rule.get("parameterName").equals("Undeposited Funds"));
      Assert.assertTrue(
          rule.containsKey("conditionalExpression") && rule.getConditionalExpression() != null);
      Assert.assertTrue(rule.get("conditionalExpression").equals("GT 500"));
    });
  }

  @Test
  public void testCreateSingleCustomWorkflowDefinitionBpmnPlaceholderValuesProcessVariables()
      throws Exception {
    definition.setDisplayName("Test definition ");
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any())).thenReturn(
        "sendNotification_bankdepositreminder");
    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData actionProcessVariable =
        (ProcessVariableData) userPlaceholderValues.get("UndepositedFunds");
    Assert.assertEquals("Double", actionProcessVariable.getType());
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithGWStep() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    WorkflowStepCondition workflowStepCondition = fetchGatewayStepCondition();
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(workflowStepCondition);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(0).getAction()))
        .thenReturn("sendForApproval_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(1).getAction()))
        .thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(2).getAction()))
        .thenReturn("autoUpdateAsApproved_txnApproval");

    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("sendReminderEmail_txnApproval")).
        thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("autoUpdateAsApproved_txnApproval")).
        thenReturn("autoUpdateAsApproved_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("sendApprovalReminder");
    Assert.assertEquals(processVariableData.getValue(), "true");

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("autoUpdate");
    Assert.assertEquals(processVariableData.getValue(), "false");

    actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendReminderEmail_txnApproval");
    Assert.assertFalse((Boolean) actionPlaceholderValues.get("selected"));
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalNullCondition() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(null);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    Assert.assertNull(processPlaceholderValue.get("sendApprovalReminder"));
    Assert.assertNull(processPlaceholderValue.get("autoUpdate"));
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalOtherConditionExceptGW() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    WorkflowStepCondition workflowStepCondition = fetchGatewayStepCondition();
    workflowStepCondition.remove(WORKFLOW_STEP_CONDITION_TYPE);
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(workflowStepCondition);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    Assert.assertNull(processPlaceholderValue.get("sendApprovalReminder"));
    Assert.assertNull(processPlaceholderValue.get("autoUpdate"));
  }


  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithGWStepWithNullAction() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    WorkflowStepCondition workflowStepCondition = fetchGatewayStepCondition();
    workflowStepCondition.getRuleLines().get(0).setMappedActionKeys(null);
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(workflowStepCondition);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(0).getAction()))
        .thenReturn("sendForApproval_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(1).getAction()))
        .thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(2).getAction()))
        .thenReturn("autoUpdateAsApproved_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");

    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("sendReminderEmail_txnApproval")).
        thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("autoUpdateAsApproved_txnApproval")).
        thenReturn("autoUpdateAsApproved_txnApproval");

    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("sendApprovalReminder");
    Assert.assertNull(processVariableData);
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithGWStepWithNullRule() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    WorkflowStepCondition workflowStepCondition = fetchGatewayStepCondition();
    workflowStepCondition.getRuleLines().get(0).setRules(null);
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(workflowStepCondition);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(0).getAction()))
        .thenReturn("sendForApproval_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(1).getAction()))
        .thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(2).getAction()))
        .thenReturn("autoUpdateAsApproved_txnApproval");

    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("sendReminderEmail_txnApproval")).
        thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("autoUpdateAsApproved_txnApproval")).
        thenReturn("autoUpdateAsApproved_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("sendApprovalReminder");
    Assert.assertNull(processVariableData);

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("autoUpdate");
    Assert.assertEquals(processVariableData.getValue(), "false");
  }

  @Test
  public void testCreateSinglePrecannedWorkflowDefinitionForBillApprovalWithGWStepWithNullConditionExpression() {
    definition.setDisplayName("Test definition ");
    setWorkflowSteps(definition);
    WorkflowStepCondition workflowStepCondition = fetchGatewayStepCondition();
    workflowStepCondition.getRuleLines().get(0).getRules().get(0).setConditionalExpression(null);
    definition.getWorkflowSteps().get(0).setWorkflowStepCondition(workflowStepCondition);

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            this.definition,
            qbdtBillApprovalBpmnModelInstance,
            Collections.singletonList(qbdtBillApprovalDmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.HUB.name()).build());
    definitionInstance.setUuid("Test_UUID");
    PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor =
        new PrecannedDefinitionPlaceholderExtractor(placeholderExtractorHelper, wasContextHandler);
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(Mockito.any()))
        .thenReturn("sendForApproval_txnApproval");

    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(0).getAction()))
        .thenReturn("sendForApproval_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(1).getAction()))
        .thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getOriginalActionId(
            definition.getWorkflowSteps().get(0).getActions().get(2).getAction()))
        .thenReturn("autoUpdateAsApproved_txnApproval");

    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("sendReminderEmail_txnApproval")).
        thenReturn("sendReminderEmail_txnApproval");
    Mockito.when(placeholderExtractorHelper.getActionIdFromLocalId("autoUpdateAsApproved_txnApproval")).
        thenReturn("autoUpdateAsApproved_txnApproval");

    Map<String, Object> testPlaceholderValue =
        precannedDefinitionPlaceholderExtractor.extractPlaceholderValue(definitionInstance);
    Map<String, Object> bpmnPlaceholderValue =
        (Map<String, Object>) testPlaceholderValue.get(BPMN_PLACEHOLDER_VALUES);
    Map<String, Object> userMetaData =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_META_DATA);
    Assert.assertEquals(userMetaData.get(WASContextEnums.INTUIT_WAS_LOCALE.getValue()), "en_US");
    Map<String, Object> userPlaceholderValues =
        (Map<String, Object>) bpmnPlaceholderValue.get(USER_VARIABLES);
    Map<String, Object> actionPlaceholderValues =
        (Map<String, Object>) userPlaceholderValues.get("sendForApproval_txnApproval");
    Map<String, Object> actionPlaceholderParameters =
        (Map<String, Object>) actionPlaceholderValues.get(ACTION_PARAMETERS);
    Map<String, ArrayList<String>> attributePlaceholderParameters =
        (Map<String, ArrayList<String>>) actionPlaceholderParameters.get("actionName");
    ArrayList<String> attributeFieldValue = attributePlaceholderParameters.get(FIELD_VALUE);
    Assert.assertEquals(Arrays.asList("111111"), attributeFieldValue);

    Map<String, Object> processPlaceholderValue =
        (Map<String, Object>) bpmnPlaceholderValue.get(PROCESS_VARIABLES);
    ProcessVariableData processVariableData = (ProcessVariableData) processPlaceholderValue.get(
        "waitTime");
    Assert.assertEquals(processVariableData.getValue(), "P7D");

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("sendReminderEmail_txnApproval");
    Assert.assertNull(processVariableData);

    processVariableData = (ProcessVariableData) processPlaceholderValue.get("autoUpdate");
    Assert.assertEquals(processVariableData.getValue(), "false");
  }

  private void setWorkflowSteps(Definition definition) {
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    WorkflowStep workflowStep = new WorkflowStep();
    workflowStep.setWorkflowStepCondition(fetchDMNConditions());
    workflowStep.setActions(fetchActions());
    workflowStep.setTrigger(fetchTrigger());
    workflowSteps.add(workflowStep);
    definition.setWorkflowSteps(workflowSteps);
  }

  private Trigger fetchTrigger() {
    GlobalId globalId = GlobalId.builder().setLocalId("waitForTimerToElapse1_txnApproval")
        .setRealmId("abcd").build();
    Trigger trigger = new Trigger();
    trigger.setId(globalId);
    trigger.setRequired(true);
    trigger.setParameters(fetchParameter("waitTime", "7"));
    return trigger;

  }

  private List<ActionMapper> fetchActions() {
    GlobalId globalId = GlobalId.builder().setLocalId("sendForApproval_txnApproval")
        .setRealmId("abcd").build();
    List<ActionMapper> actionMappers = new ArrayList<>();

    ActionMapper actionMapper1 = new ActionMapper();
    actionMapper1.setActionKey("approvalRequired");
    Action action = new Action();
    action.setId(globalId);
    action.setSelected(true);
    action.setParameters(fetchParameter("actionName", "111111"));
    actionMapper1.setAction(action);

    ActionMapper actionMapper2 = new ActionMapper();
    globalId = GlobalId.builder().setLocalId("sendReminderEmail_txnApproval").setRealmId("abcd")
        .build();
    actionMapper2.setActionKey("sendApprovalReminder");
    action = new Action();
    action.setId(globalId);
    action.setSelected(false);
    action.setParameters(fetchParameter("actionName", "111111"));
    actionMapper2.setAction(action);

    ActionMapper actionMapper3 = new ActionMapper();
    globalId = GlobalId.builder().setLocalId("autoUpdateAsApproved_txnApproval").setRealmId("abcd")
        .build();
    actionMapper3.setActionKey("autoUpdate");
    action = new Action();
    action.setId(globalId);
    action.setSelected(false);
    action.setParameters(fetchParameter("actionName", "111111"));
    actionMapper3.setAction(action);

    actionMappers.add(actionMapper1);
    actionMappers.add(actionMapper2);
    actionMappers.add(actionMapper3);
    return actionMappers;
  }

  private List<InputParameter> fetchParameter(String actionName, String values) {
    List<InputParameter> parameterList = new ArrayList<>();
    InputParameter inputParameter = new InputParameter();
    inputParameter.setConfigurable(true);
    inputParameter.setParameterName(actionName);
    inputParameter.setParameterType(FieldTypeEnum.STRING);
    List<String> fieldValues = new ArrayList<>();
    fieldValues.add(values);
    inputParameter.setFieldValues(fieldValues);
    parameterList.add(inputParameter);
    return parameterList;
  }

  private WorkflowStepCondition fetchDMNConditions() {
    GlobalId globalId = GlobalId.builder().setLocalId("decision_billapproval_QBDT")
        .setRealmId("abcd").build();
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    workflowStepCondition.setId(globalId);
    return workflowStepCondition;
  }

  private WorkflowStepCondition fetchGatewayStepCondition() {
    GlobalId globalId = GlobalId.builder().setLocalId("evaluateUserDefinedAction_txnApproval")
        .setRealmId("abcd").build();
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    workflowStepCondition.setId(globalId);
    workflowStepCondition.set(WorkflowConstants.WORKFLOW_STEP_CONDITION_TYPE,
        BpmnComponentType.EXCLUSIVE_GATEWAY.getName());
    List<RuleLine> ruleLineList = new ArrayList<>();
    RuleLine ruleLine1 = new RuleLine();
    ruleLine1.setMappedActionKeys(Arrays.asList("sendReminderEmail_txnApproval"));
    Rule rule = new Rule();
    rule.setConditionalExpression("${true}");
    ruleLine1.setRules(Arrays.asList(rule));

    RuleLine ruleLine2 = new RuleLine();
    rule = new Rule();
    ruleLine2.setMappedActionKeys(Arrays.asList("autoUpdateAsApproved_txnApproval"));
    rule.setConditionalExpression("${false}");
    ruleLine2.setRules(Arrays.asList(rule));

    ruleLineList.add(ruleLine1);
    ruleLineList.add(ruleLine2);
    workflowStepCondition.setRuleLines(ruleLineList);
    return workflowStepCondition;
  }

}
