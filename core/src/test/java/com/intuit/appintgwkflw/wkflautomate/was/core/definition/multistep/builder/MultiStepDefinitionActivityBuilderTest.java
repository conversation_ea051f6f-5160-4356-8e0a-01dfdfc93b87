package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builder;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepDefinitionActivityBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepDefinitionActivityBuilderTest {
    DmnResponse dmnResponse = new DmnResponse("decisionElement",
            "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<definitions xmlns=\"https://www.omg.org/spec/DMN/20191111/MODEL/\" xmlns:dc=\"http://www.omg.org/spec/DMN/20180521/DC/\" xmlns:dmndi=\"https://www.omg.org/spec/DMN/20191111/DMNDI/\" xmlns:ns0=\"http://camunda.org/schema/1.0/dmn\" id=\"Definitions_086839o\" name=\"DRD\" namespace=\"http://camunda.org/schema/1.0/dmn\" expressionLanguage=\"https://www.omg.org/spec/DMN/20191111/FEEL/\" typeLanguage=\"https://www.omg.org/spec/DMN/20191111/FEEL/\" exporter=\"Camunda Modeler\" exporterVersion=\"5.1.0\">\n" +
                    "  <decision id=\"decisionElement\" name=\"Multi Call Condition Def\">\n" +
                    "    <decisionTable id=\"decisionTable_1\" hitPolicy=\"FIRST\" preferredOrientation=\"Rule-as-Row\">\n" +
                    "      <input id=\"input0\" label=\"Index\" ns0:inputVariable=\"Index\">\n" +
                    "        <inputExpression id=\"inputExpression_0\" typeRef=\"integer\">\n" +
                    "          <text>${Index}</text>\n" +
                    "        </inputExpression>\n" +
                    "      </input>\n" +
                    "      <input id=\"input1\" label=\"TxnAmount\" ns0:inputVariable=\"TxnAmount\">\n" +
                    "        <inputExpression id=\"inputExpression_1\" typeRef=\"double\">\n" +
                    "          <text>${TxnAmount}</text>\n" +
                    "        </inputExpression>\n" +
                    "      </input>\n" +
                    "      <input id=\"input2\" label=\"Location\" ns0:inputVariable=\"Location\">\n" +
                    "        <inputExpression id=\"inputExpression_2\" typeRef=\"string\">\n" +
                    "          <text>${Location}</text>\n" +
                    "        </inputExpression>\n" +
                    "      </input>\n" +
                    "      <input id=\"input3\" label=\"TxnBalanceAmount\" ns0:inputVariable=\"TxnBalanceAmount\">\n" +
                    "        <inputExpression id=\"inputExpression_3\" typeRef=\"double\">\n" +
                    "          <text>${TxnBalanceAmount}</text>\n" +
                    "        </inputExpression>\n" +
                    "      </input>\n" +
                    "      <output id=\"output_1\" label=\"decisionResult\" name=\"decisionResult\" typeRef=\"string\" />\n" +
                    "      <rule id=\"rule_4886d8a3-a6ca-4df4-b2da-582c816b470b\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_701c4502-ab07-4c50-8b79-d622316fd996\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 0</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_dca731c6-769d-4a14-aed4-2b8cb475bd98\" expressionLanguage=\"juel\">\n" +
                    "          <text>TxnAmount &gt;= 100</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_dc0931df-8d19-47f0-9f9e-80351bf11a6f\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_d2c90fac-d256-47ba-98c0-57fb3848cb63\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_717652e0-9baf-4d57-8567-50c9703c6924\">\n" +
                    "          <text>1</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "      <rule id=\"rule_6c4ec18e-eec8-4a1f-a86d-444016959e5d\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_12be7916-9e6f-4afe-8d77-638256651201\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 0</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_58e0a58c-8500-4762-9565-597c8c489b0e\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_2e761db8-a4b5-496e-8d17-e0f7df2f3a3a\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_1772f73b-00de-4fd4-8a11-44fc3ec31330\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_b6d6a0da-309a-411e-a346-896ce41d0af2\">\n" +
                    "          <text>'action-2'</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "      <rule id=\"rule_0a017912-7945-4244-9a8a-7e1ff846ad9c\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_d70b49a1-bc54-45b9-96ac-ee51a9860e6b\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 1</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_6e97900a-9220-4566-8bd2-61a8d9a8a3b4\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_5ceb2b20-be21-4899-8976-cd0f3221a54c\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_be37357d-47ce-4d19-be63-62db81440608\" expressionLanguage=\"juel\">\n" +
                    "          <text>TxnBalanceAmount &gt;= 1000</text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_e4b89b03-9b36-4086-918b-0a01e549f028\">\n" +
                    "          <text>'action-3'</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "      <rule id=\"rule_381e44e9-ab9d-47d1-9166-74a9f903af2e\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_52eff526-87c3-41fe-97a4-0cc6da4336f7\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 1</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_c01af142-6f91-4e7f-a36e-7cb00f646b9d\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_8563d395-5d2e-4881-a3db-cb0d9884c190\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_1afa79b1-ae2c-4958-8128-37a9671cf32a\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_741e5b45-2ce3-4b74-915c-bb5f3eb43f0b\">\n" +
                    "          <text>4</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "      <rule id=\"rule_0177004f-a7f6-4345-8a2c-c5aa0aa71d0c\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_e5845909-c399-4c54-8ba0-78a2d2f4de86\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 4</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_85adb7ee-87fb-4b2b-be91-0c1f47127b72\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_594eeaa5-e9ab-4155-87a2-b7b65dbfba14\" expressionLanguage=\"juel\">\n" +
                    "          <text>Location.equals(\"1\")</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_0059c65b-938a-488b-ba70-2a7250612409\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_b0813e35-ec57-4172-b558-31a7cf2fe7ea\">\n" +
                    "          <text>'action-5'</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "      <rule id=\"rule_3c59a38a-c634-404f-ae3d-f80854b6d3ac\">\n" +
                    "        <description></description>\n" +
                    "        <inputEntry id=\"inputEntry_2807bfc1-3df7-4934-bc54-2d9615abac35\" expressionLanguage=\"juel\">\n" +
                    "          <text>Index == 4</text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_5bba2ce1-4b17-42b1-9f21-8185f2557180\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_77132486-cbae-421d-b5b9-b65b628ad898\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <inputEntry id=\"inputEntry_1dcd1fa9-70a3-4cf1-b756-0a478733310e\" expressionLanguage=\"juel\">\n" +
                    "          <text></text>\n" +
                    "        </inputEntry>\n" +
                    "        <outputEntry id=\"outputEntry_144855e9-3e64-4d77-9744-3ef3b6791960\">\n" +
                    "          <text>'action-4'</text>\n" +
                    "        </outputEntry>\n" +
                    "      </rule>\n" +
                    "    </decisionTable>\n" +
                    "  </decision>\n" +
                    "  <dmndi:DMNDI>\n" +
                    "    <dmndi:DMNDiagram id=\"DMNDiagram_0wz3ah0\">\n" +
                    "      <dmndi:DMNShape id=\"DMNShape_0h7yp5z\" dmnElementRef=\"decisionElement\" isCollapsed=\"false\">\n" +
                    "        <dc:Bounds height=\"80\" width=\"180\" x=\"150\" y=\"150\" />\n" +
                    "      </dmndi:DMNShape>\n" +
                    "    </dmndi:DMNDiagram>\n" +
                    "  </dmndi:DMNDI>\n" +
                    "</definitions>\n");
    @Mock
    private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
    @InjectMocks
    private MultiStepDefinitionActivityBuilder multiStepDefinitionActivityBuilder;

    private List<DefinitionActivityDetail> generateListOfActivityDetails() {
        List<DefinitionActivityDetail> definitionActivityDetails = new ArrayList<>();
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("08f411b4-0bd1-4b50-af7b-4cab928abf4e");
        DefinitionActivityDetail definitionActivityDetail1 = DefinitionActivityDetail.builder()
                .id("3")
                .definitionDetails(definitionDetails)
                .activityId("action-4")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273349816\"]}}}}")
                .build();
        definitionActivityDetails.add(definitionActivityDetail1);
        DefinitionActivityDetail definitionActivityDetail2 = DefinitionActivityDetail.builder()
                .id("4")
                .definitionDetails(definitionDetails)
                .activityId("action-3")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273350346\"]}}}}")
                .build();
        definitionActivityDetails.add(definitionActivityDetail2);
        DefinitionActivityDetail definitionActivityDetail3 = DefinitionActivityDetail.builder()
                .id("5")
                .definitionDetails(definitionDetails)
                .activityId("action-2")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273350856\"]}}}}")
                .build();
        definitionActivityDetails.add(definitionActivityDetail3);
        DefinitionActivityDetail definitionActivityDetail4 = DefinitionActivityDetail.builder()
                .id("6")
                .definitionDetails(definitionDetails)
                .activityId("action-5")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273344206\"]}}}}")
                .build();
        definitionActivityDetails.add(definitionActivityDetail4);
        DefinitionActivityDetail definitionActivityDetail5 = DefinitionActivityDetail.builder()
                .id("7")
                .definitionDetails(definitionDetails)
                .activityId("createTask")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273349816\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"close_manually\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}}")
                .build();
        List<DefinitionActivityDetail> childDefs1 = new ArrayList<>();
        childDefs1.add(definitionActivityDetail5);
        DefinitionActivityDetail definitionActivityDetail6 = DefinitionActivityDetail.builder()
                .id("8")
                .definitionDetails(definitionDetails)
                .activityId("sendCompanyEmail")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}}")
                .build();
        childDefs1.add(definitionActivityDetail6);
        DefinitionActivityDetail definitionActivityDetail7 = DefinitionActivityDetail.builder()
                .id("9")
                .definitionDetails(definitionDetails)
                .activityId("sendPushNotification")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"[[Company Email]]\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}")
                .build();
        childDefs1.add(definitionActivityDetail7);
        definitionActivityDetail1.setChildActivityDetails(childDefs1);

        DefinitionActivityDetail definitionActivityDetail8 = DefinitionActivityDetail.builder()
                .id("10")
                .definitionDetails(definitionDetails)
                .activityId("createTask")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273350346\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"close_manually\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}}")
                .build();
        List<DefinitionActivityDetail> childDefs2 = new ArrayList<>();
        childDefs2.add(definitionActivityDetail8);
        DefinitionActivityDetail definitionActivityDetail9 = DefinitionActivityDetail.builder()
                .id("11")
                .definitionDetails(definitionDetails)
                .activityId("sendCompanyEmail")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}}")
                .build();
        childDefs2.add(definitionActivityDetail9);
        DefinitionActivityDetail definitionActivityDetail10 = DefinitionActivityDetail.builder()
                .id("12")
                .definitionDetails(definitionDetails)
                .activityId("sendPushNotification")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"[[Company Email]]\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}")
                .build();
        childDefs2.add(definitionActivityDetail10);
        definitionActivityDetail2.setChildActivityDetails(childDefs2);

        DefinitionActivityDetail definitionActivityDetail11 = DefinitionActivityDetail.builder()
                .id("13")
                .definitionDetails(definitionDetails)
                .activityId("createTask")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273350856\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"txn_approval_changed\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}}")
                .build();
        List<DefinitionActivityDetail> childDefs3 = new ArrayList<>();
        childDefs3.add(definitionActivityDetail11);
        DefinitionActivityDetail definitionActivityDetail12 = DefinitionActivityDetail.builder()
                .id("14")
                .definitionDetails(definitionDetails)
                .activityId("sendCompanyEmail")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}}")
                .build();
        childDefs3.add(definitionActivityDetail12);
        DefinitionActivityDetail definitionActivityDetail13 = DefinitionActivityDetail.builder()
                .id("15")
                .definitionDetails(definitionDetails)
                .activityId("sendPushNotification")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"[[Company Email]]\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}")
                .build();
        childDefs3.add(definitionActivityDetail13);
        definitionActivityDetail3.setChildActivityDetails(childDefs3);

        DefinitionActivityDetail definitionActivityDetail14 = DefinitionActivityDetail.builder()
                .id("16")
                .definitionDetails(definitionDetails)
                .activityId("createTask")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273344206\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"close_manually\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}}")
                .build();
        List<DefinitionActivityDetail> childDefs4 = new ArrayList<>();
        childDefs4.add(definitionActivityDetail14);
        DefinitionActivityDetail definitionActivityDetail15 = DefinitionActivityDetail.builder()
                .id("17")
                .definitionDetails(definitionDetails)
                .activityId("sendCompanyEmail")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}}")
                .build();
        childDefs4.add(definitionActivityDetail15);
        DefinitionActivityDetail definitionActivityDetail16 = DefinitionActivityDetail.builder()
                .id("18")
                .definitionDetails(definitionDetails)
                .activityId("sendPushNotification")
                .userAttributes("{\"userAttributes\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"[[Company Email]]\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}")
                .build();
        childDefs4.add(definitionActivityDetail16);
        definitionActivityDetail4.setChildActivityDetails(childDefs4);
        return definitionActivityDetails;
    }

    @Test
    public void testGenerateActivityInstanceMap() {
        Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.anyString()))
                .thenReturn(Optional.of(generateListOfActivityDetails()));
        String definitionId = "08f411b4-0bd1-4b50-af7b-4cab928abf4e";
        Map<String, ActivityInstance> activityInstanceMap = multiStepDefinitionActivityBuilder.generateActivityInstanceMap(definitionId, Arrays.asList(dmnResponse));
        Assert.assertNotNull(activityInstanceMap);
        Assert.assertEquals(5, activityInstanceMap.size());
        Assert.assertNotNull(activityInstanceMap.get("action-2"));
        Assert.assertNull(activityInstanceMap.get("action-2").getDmnModelInstance());
        Assert.assertEquals(3, activityInstanceMap.get("action-2").getChildActivityInstances().size());
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("createTask"));
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("createTask").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("createTask").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendCompanyEmail"));
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendCompanyEmail").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendCompanyEmail").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendPushNotification"));
        Assert.assertNotNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendPushNotification").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-2").getChildActivityInstances().get("sendPushNotification").getDmnModelInstance());

        Assert.assertNotNull(activityInstanceMap.get("action-3"));
        Assert.assertNull(activityInstanceMap.get("action-3").getDmnModelInstance());
        Assert.assertEquals(3, activityInstanceMap.get("action-3").getChildActivityInstances().size());
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("createTask"));
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("createTask").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("createTask").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendCompanyEmail"));
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendCompanyEmail").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendCompanyEmail").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendPushNotification"));
        Assert.assertNotNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendPushNotification").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-3").getChildActivityInstances().get("sendPushNotification").getDmnModelInstance());

        Assert.assertNotNull(activityInstanceMap.get("action-4"));
        Assert.assertNull(activityInstanceMap.get("action-4").getDmnModelInstance());
        Assert.assertEquals(3, activityInstanceMap.get("action-4").getChildActivityInstances().size());
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("createTask"));
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("createTask").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("createTask").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendCompanyEmail"));
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendCompanyEmail").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendCompanyEmail").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendPushNotification"));
        Assert.assertNotNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendPushNotification").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-4").getChildActivityInstances().get("sendPushNotification").getDmnModelInstance());

        Assert.assertNotNull(activityInstanceMap.get("action-5"));
        Assert.assertNull(activityInstanceMap.get("action-5").getDmnModelInstance());
        Assert.assertEquals(3, activityInstanceMap.get("action-5").getChildActivityInstances().size());
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("createTask"));
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("createTask").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("createTask").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendCompanyEmail"));
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendCompanyEmail").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendCompanyEmail").getDmnModelInstance());
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendPushNotification"));
        Assert.assertNotNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendPushNotification").getUserAttributes());
        Assert.assertNull(activityInstanceMap.get("action-5").getChildActivityInstances().get("sendPushNotification").getDmnModelInstance());

        Assert.assertNotNull(activityInstanceMap.get("decisionElement"));
        Assert.assertNotNull(activityInstanceMap.get("decisionElement").getDmnModelInstance());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testGenerateActivityInstanceMapForNoActivityDetails() {
        Optional<List<DefinitionActivityDetail>> definitionActivityDetails = Optional.empty();
        Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.anyString()))
                .thenReturn(definitionActivityDetails);
        String definitionId = "08f411b4-0bd1-4b50-af7b-4cab928abf4e";
        try {
            Map<String, ActivityInstance> activityInstanceMap = multiStepDefinitionActivityBuilder.generateActivityInstanceMap(definitionId, Arrays.asList(dmnResponse));
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException error) {
            Assert.assertEquals(WorkflowError.DEFINITION_ACTIVITY_DETAILS_NOT_FOUND, error.getWorkflowError());
            throw error;
        }
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testGenerateActivityInstanceMapForNoDmn() {
        String definitionId = "08f411b4-0bd1-4b50-af7b-4cab928abf4e";
        try {
            multiStepDefinitionActivityBuilder.generateActivityInstanceMap(definitionId, new ArrayList<>());
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException error) {
            Assert.assertEquals(WorkflowError.DMN_NOT_FOUND, error.getWorkflowError());
            throw error;
        }
    }
}
