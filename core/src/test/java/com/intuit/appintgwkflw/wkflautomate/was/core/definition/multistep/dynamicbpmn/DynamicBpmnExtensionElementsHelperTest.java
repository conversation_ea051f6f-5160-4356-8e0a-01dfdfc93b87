package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 *
 * Test class for DynamicBpmnExtensionElementsHelper class.
 */
@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnExtensionElementsHelperTest {

  @InjectMocks private DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private BpmnModelInstance baseTemplateBpmnModelInstance;

  @Before
  public void setup() {
    baseTemplateBpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");
  }

  @Test
  public void test_addAllValidExtensionElements_forCallActivity() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .callActivity("sendForApproval")
            .endEvent("endEvent")
            .done();

    CallActivity callActivity = bpmnModelInstance.getModelElementById("sendForApproval");

    CallActivity baseTemplateCallActivity =
        baseTemplateBpmnModelInstance.getModelElementById("sendForApproval");

    dynamicBpmnExtensionElementsHelper.addAllValidExtensionElements(
        callActivity, baseTemplateCallActivity, bpmnModelInstance);

    ExtensionElements resultExtensionElements = callActivity.getExtensionElements();
    Assert.assertEquals(
        1, BpmnProcessorUtil.getMapOfCamundaProperties(resultExtensionElements).size());
    Assert.assertEquals(
        2, BpmnProcessorUtil.getMapOfInputOutputParameters(resultExtensionElements).size());
    Assert.assertEquals(
        2, BpmnProcessorUtil.getListOfCamundaInParameters(resultExtensionElements).size());
    Assert.assertEquals(
        1, BpmnProcessorUtil.getListOfCamundaOutParameters(resultExtensionElements).size());
  }
}
