package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnImplicitElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnSubProcessEventHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.CallActivityFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.TemplateServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mock;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 *
 * This class includes test cases for DynamicBpmnDefinitionProcessor class.
 */
@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnDefinitionProcessorTest {

  private DynamicBpmnDefinitionProcessor dynamicBpmnDefinitionProcessor;

  @Mock private DynamicBpmnProcessorHelper dynamicBpmnProcessorHelper;

  @Mock private StartEventFlowNodeProcessor startEventFlowNodeProcessor;

  @Mock private DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  @Mock private DynamicBpmnImplicitElementsHelper dynamicBpmnImplicitElementsHelper;

  @Mock private DynamicBpmnSubProcessEventHelper dynamicBpmnSubProcessEventHelper;

  @Mock private CallActivityFlowNodeProcessor callActivityFlowNodeProcessor;

  @Mock private TemplateServiceImpl templateServiceImpl;

  @Mock private WASContextHandler contextHandler;

  private Definition definition;
  private Definition multiDmnDefinition;
  private Definition singleStepReminderWithCompositeStepDefinition;
  private Definition multiStepReminderWithCompositeStepDefinition;

  private BpmnModelInstance bpmnModelInstance;
  private TemplateDetails templateDetails = new TemplateDetails();

  @Before
  public void setup() {
    definition = TestHelper.mockMultiConditionDefinitionEntityWithMoreThan6Approvers();
    multiDmnDefinition = TestHelper.mockMultiConditionDefinitionEntityWithMultipleDmn();
    singleStepReminderWithCompositeStepDefinition = TestHelper.mockSingleStepRecurringReminderDefinitionEntity();
    multiStepReminderWithCompositeStepDefinition = TestHelper.mockMultiStepRecurringReminderDefinitionEntity();

    when(dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(any()))
        .thenReturn(callActivityFlowNodeProcessor);

    templateDetails.setTemplateName("dummyName");

    when(templateServiceImpl.getOrSaveTemplateDetailsByHashValue(any(),any(),any())).thenReturn(templateDetails);

    this.dynamicBpmnDefinitionProcessor =
        spy(
            new DynamicBpmnDefinitionProcessor(
                dynamicBpmnProcessorHelper,
                startEventFlowNodeProcessor,
                dynamicBpmnFlowNodeProcessorFactory,
                dynamicBpmnImplicitElementsHelper,
                dynamicBpmnSubProcessEventHelper,
                templateServiceImpl,
                contextHandler));
  }

  @Test
  public void test_buildDefinitionInstanceDynamically_happyCase() {

    DefinitionInstance definitionInstance =
        dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(definition);

    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertEquals(0, definitionInstance.getDmnModelInstanceList().size());
  }

  @Test
  public void test_buildDefinitionInstanceDynamically_withSingleDmn() {
    bpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile(
            "baseTemplates/bpmn/customApprovalMultiConditionSingleDmn.bpmn");

    doReturn(bpmnModelInstance)
        .when(dynamicBpmnDefinitionProcessor)
        .createBpmnModelInstanceFromPayload(any(), any(), any(), any());

    DefinitionInstance definitionInstance =
        dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(definition);

    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertEquals(1, definitionInstance.getDmnModelInstanceList().size());
  }

  @Test
  public void test_buildDefinitionInstanceDynamically_forMultipleDMNs() {

    bpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalWithMultipleDmn.bpmn");

    doReturn(bpmnModelInstance)
        .when(dynamicBpmnDefinitionProcessor)
        .createBpmnModelInstanceFromPayload(any(), any(), any(), any());

    DefinitionInstance definitionInstance =
        dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(multiDmnDefinition);

    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertEquals(2, definitionInstance.getDmnModelInstanceList().size());
  }

  @Test
  public void test_buildDefinitionInstanceDynamically_withCompositeWorkflowSteps(){

    DefinitionInstance definitionInstance =
        dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(singleStepReminderWithCompositeStepDefinition);

    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertEquals(0, definitionInstance.getDmnModelInstanceList().size());
    Assert.assertEquals(3, definitionInstance.getDefinition().getWorkflowSteps().size());
    Assert.assertEquals(3,
        definitionInstance.getDefinition().getWorkflowSteps().stream().map(
            workflowStep -> workflowStep.getId().getLocalId()).distinct().count());

  }

  @Test
  public void test_buildDefinitionInstanceDynamically_withCompositeWorkflowSteps_MultiCondition(){

    DefinitionInstance definitionInstance =
        dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(multiStepReminderWithCompositeStepDefinition);

    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertEquals(0, definitionInstance.getDmnModelInstanceList().size());
    Assert.assertEquals(5, definitionInstance.getDefinition().getWorkflowSteps().size());
    Assert.assertEquals(5,
        definitionInstance.getDefinition().getWorkflowSteps().stream().map(
            workflowStep -> workflowStep.getId().getLocalId()).distinct().count());
  }
}
