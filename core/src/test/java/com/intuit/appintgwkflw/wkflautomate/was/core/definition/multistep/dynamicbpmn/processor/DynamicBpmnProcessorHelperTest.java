package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnWorkflowStepProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.BusinessRuleTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.CallActivityFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.EndEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.ServiceTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.BusinessRuleTaskWorkflowStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps.CallActivityWorkflowStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * <p>
 * This class includes test cases for DynamicBpmnProcessorHelper.
 */
@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnProcessorHelperTest {

    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
    DynamicBpmnProcessorHelper dynamicBpmnProcessorHelper;
    @Mock
    private DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;
    @Mock
    private BusinessRuleTaskWorkflowStepProcessor businessRuleTaskWorkflowStepProcessor;
    @Mock
    private StartEventFlowNodeProcessor startEventFlowNodeProcessor;
    @Mock
    private BusinessRuleTaskFlowNodeProcessor businessRuleTaskFlowNodeProcessor;
    @Mock
    private CallActivityFlowNodeProcessor callActivityFlowNodeProcessor;
    @Mock
    private ServiceTaskFlowNodeProcessor serviceTaskFlowNodeProcessor;
    @Mock
    private EndEventFlowNodeProcessor endEventFlowNodeProcessor;
    @Mock
    private CallActivityWorkflowStepProcessor callActivityWorkflowStepProcessor;
    @Mock
    private DynamicBpmnWorkflowStepProcessorFactory dynamicBpmnWorkflowStepProcessorFactory;
    private Definition definition;
    private BpmnModelInstance baseTemplateBpmnModelInstance;
    private Map<String, WorkflowStep> workflowStepMap;
    private Set<String> visitedNodeSet;
    private Map<String, String> effectiveParentIdMap;
    private Map<String, String> dynamicActivityIdMap = new HashMap<>();
    private BpmnModelInstance multiConditionBpmnModelInstance;
    private DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;
    private List<DynamicBpmnFlowNodeProcessor> processorList = new ArrayList<>();

    @Before
    public void setup() {
        definition = TestHelper.mockMultiConditionDefinitionEntityWithMoreThan6Approvers();
        baseTemplateBpmnModelInstance =
                BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");
        workflowStepMap = new HashMap<>();
        visitedNodeSet = new HashSet<>();
        effectiveParentIdMap = new HashMap<>();
        multiConditionBpmnModelInstance =
                Bpmn.readModelFromStream(
                        IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));

        businessRuleTaskFlowNodeProcessor = new BusinessRuleTaskFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
        callActivityFlowNodeProcessor = new CallActivityFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
        endEventFlowNodeProcessor = new EndEventFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
        serviceTaskFlowNodeProcessor = new ServiceTaskFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
        startEventFlowNodeProcessor = new StartEventFlowNodeProcessor(dynamicBpmnExtensionElementsHelper);
        processorList.add(businessRuleTaskFlowNodeProcessor);
        processorList.add(callActivityFlowNodeProcessor);
        processorList.add(endEventFlowNodeProcessor);
        processorList.add(serviceTaskFlowNodeProcessor);
        processorList.add(startEventFlowNodeProcessor);

        dynamicBpmnFlowNodeProcessorFactory = new DynamicBpmnFlowNodeProcessorFactory(processorList);

        dynamicBpmnProcessorHelper = new DynamicBpmnProcessorHelper(dynamicBpmnFlowNodeProcessorFactory,
                dynamicBpmnWorkflowStepProcessorFactory,
                endEventFlowNodeProcessor);
    }

    @Test
    public void testGetHashValueForTemplateAdjacencyList() {
        String actionKey = "approval";
        String hashValueForBaseTemplate =
                dynamicBpmnProcessorHelper.getHashValueForTemplateAdjacencyList(baseTemplateBpmnModelInstance, actionKey);

        Assert.assertNotNull(hashValueForBaseTemplate);

        String firstHashValueForMultiConditionTemplate =
                dynamicBpmnProcessorHelper.getHashValueForTemplateAdjacencyList(multiConditionBpmnModelInstance, actionKey);

        Assert.assertNotNull(firstHashValueForMultiConditionTemplate);
        Assert.assertNotEquals(hashValueForBaseTemplate, firstHashValueForMultiConditionTemplate);

        String secondHashValueForMultiConditionTemplate =
                dynamicBpmnProcessorHelper.getHashValueForTemplateAdjacencyList(multiConditionBpmnModelInstance, actionKey);

        Assert.assertNotNull(secondHashValueForMultiConditionTemplate);
        Assert.assertEquals(secondHashValueForMultiConditionTemplate, firstHashValueForMultiConditionTemplate);
    }

    @Test
    public void test_processWorkflowStep_happyCase() {

        WorkflowStep currentWorkflowStep = definition.getWorkflowSteps().stream().findFirst().get();

        for (WorkflowStep workflowStep : definition.getWorkflowSteps()) {
            workflowStepMap.put(workflowStep.getId().toString(), workflowStep);
        }

        AbstractFlowNodeBuilder flowNodeBuilder =
                Bpmn.createExecutableProcess()
                        .startEvent(currentWorkflowStep.getId().toString())
                        .name(currentWorkflowStep.getName());

        DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData =
                DynamicBpmnWorkflowStepProcessorMetaData.builder()
                        .flowNodeBuilder(flowNodeBuilder)
                        .workflowStep(currentWorkflowStep)
                        .parentWorkflowStep(null)
                        .workflowStepMap(workflowStepMap)
                        .effectiveParentIdMap(effectiveParentIdMap)
                        .baseTemplateBpmnModelInstance(baseTemplateBpmnModelInstance)
                        .dynamicActivityIdMap(dynamicActivityIdMap)
                        .build();

        when(dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
                StepTypeEnum.CONDITION))
                .thenReturn(businessRuleTaskWorkflowStepProcessor);
        when(dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(StepTypeEnum.ACTION))
                .thenReturn(callActivityWorkflowStepProcessor);

        dynamicBpmnProcessorHelper.processWorkflowStep(
                dynamicBpmnWorkflowStepProcessorMetaData, visitedNodeSet);

        Mockito.verify(businessRuleTaskWorkflowStepProcessor, Mockito.times(7))
                .processDynamicBpmnStep(any());
        Mockito.verify(callActivityWorkflowStepProcessor, Mockito.times(7))
                .processDynamicBpmnStep(any());
    }

    @Test
    public void test_processWorkflowStep_throwsErrorForInvalidWorkflowStep() {

        WorkflowStep currentWorkflowStep = definition.getWorkflowSteps().stream().findFirst().get();

        AbstractFlowNodeBuilder flowNodeBuilder =
                Bpmn.createExecutableProcess()
                        .startEvent("startEvent");

        DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData =
                DynamicBpmnWorkflowStepProcessorMetaData.builder()
                        .flowNodeBuilder(flowNodeBuilder)
                        .workflowStep(currentWorkflowStep)
                        .parentWorkflowStep(null)
                        .workflowStepMap(workflowStepMap)
                        .effectiveParentIdMap(effectiveParentIdMap)
                        .baseTemplateBpmnModelInstance(baseTemplateBpmnModelInstance)
                        .dynamicActivityIdMap(dynamicActivityIdMap)
                        .build();

        when(dynamicBpmnWorkflowStepProcessorFactory.getProcessorFromWorkflowStep(
                StepTypeEnum.CONDITION))
                .thenReturn(businessRuleTaskWorkflowStepProcessor);

        assertThrows(
                WorkflowGeneralException.class,
                () ->
                        dynamicBpmnProcessorHelper.processWorkflowStep(
                                dynamicBpmnWorkflowStepProcessorMetaData, visitedNodeSet));

        Mockito.verify(businessRuleTaskWorkflowStepProcessor, Mockito.times(1))
                .processDynamicBpmnStep(any());
        Mockito.verify(callActivityWorkflowStepProcessor, Mockito.never())
                .processDynamicBpmnStep(any());
    }

    @Test
    public void test_addEndEventToBpmnModelInstance() {

        this.dynamicBpmnProcessorHelper = spy(
                new DynamicBpmnProcessorHelper(
                        dynamicBpmnFlowNodeProcessorFactory,
                        dynamicBpmnWorkflowStepProcessorFactory,
                        endEventFlowNodeProcessor));

        AbstractFlowNodeBuilder flowNodeBuilder =
                Bpmn.createExecutableProcess()
                        .startEvent("startEvent")
                        .businessRuleTask("decisionElement")
                        .callActivity("actionStep-1");

        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-2");
        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-3");
        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-4");
        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-5");
        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-6");
        flowNodeBuilder.moveToNode("decisionElement").callActivity("actionStep-7");

        dynamicActivityIdMap.put("actionStep-1", "actionStep-1");
        dynamicActivityIdMap.put("actionStep-2", "actionStep-2");
        dynamicActivityIdMap.put("actionStep-3", "actionStep-3");
        dynamicActivityIdMap.put("actionStep-4", "actionStep-4");
        dynamicActivityIdMap.put("actionStep-5", "actionStep-5");
        dynamicActivityIdMap.put("actionStep-6", "actionStep-6");
        dynamicActivityIdMap.put("actionStep-7", "actionStep-7");

        dynamicBpmnProcessorHelper.addEndEventToBpmnModelInstance(
                definition.getWorkflowSteps(), flowNodeBuilder, baseTemplateBpmnModelInstance, dynamicActivityIdMap);

        BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

        Assert.assertEquals(1, bpmnModelInstance.getModelElementsByType(EndEvent.class).size());

        EndEvent endEvent = bpmnModelInstance.getModelElementById("endEvent");

        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-1"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-2"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-3"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-4"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-5"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-6"), endEvent));
        Assert.assertTrue(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("actionStep-7"), endEvent));
        Assert.assertFalse(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("decisionElement"), endEvent));
        Assert.assertFalse(
                DynamicBpmnUtil.isSourceAndTargetNodesConnected(
                        bpmnModelInstance.getModelElementById("startEvent"), endEvent));
    }
}
