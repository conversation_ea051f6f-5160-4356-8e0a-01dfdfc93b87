package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.BusinessRuleTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the BusinessRuleTaskFlowNodeProcessor class.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class BusinessRuleTaskFlowNodeProcessorTest {
  @InjectMocks
  BusinessRuleTaskFlowNodeProcessor businessRuleTaskFlowNodeProcessor;

  @Mock DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private FlowNode flowNode;
  private FlowNode baseTemplateFlowNode;
  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance baseTemplateBpmnModelInstance;

  @Before
  public void setup() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().businessRuleTask("dmnFlowNode").done();
    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().businessRuleTask("dmnFlowNode").done();
  }

  @Test
  public void testGetType() {
    Assert.assertEquals(
        BpmnComponentType.BUSINESS_RULE_TASK, businessRuleTaskFlowNodeProcessor.getType());
  }

  @Test
  public void testAddExtensionElementsWithAllParametersAsNull() {
    businessRuleTaskFlowNodeProcessor.addExtensionElements(null, null, null, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.never())
        .addAllValidExtensionElements(
            any(FlowNode.class), any(FlowNode.class), any(BpmnModelInstance.class));
  }

  @Test
  public void testAddExtensionElementsWithNullBaseTemplateFlowNodeAndNullBpmnModelInstance() {
    flowNode = bpmnModelInstance.getModelElementById("dmnFlowNode");
    businessRuleTaskFlowNodeProcessor.addExtensionElements(
        flowNode, null, null, baseTemplateBpmnModelInstance);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddExtensionElementsWithNullFlowNodeAndNullBaseTemplateBpmnModelInstance() {
    baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById("dmnFlowNode");
    businessRuleTaskFlowNodeProcessor.addExtensionElements(
        null, baseTemplateFlowNode, bpmnModelInstance, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }
}
