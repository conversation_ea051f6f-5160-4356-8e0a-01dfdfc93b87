package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Collection;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the CallActivityFlowNodeProcessor class.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CallActivityFlowNodeProcessorTest {

  @InjectMocks
  CallActivityFlowNodeProcessor callActivityFlowNodeProcessor;

  @Mock DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private FlowNode flowNode;
  private FlowNode baseTemplateFlowNode;
  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private SubProcess subProcess;
  private SubProcess baseTemplateSubprocess;

  @Before
  public void setup() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().callActivity("callActivityFlowNode").done();
    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().callActivity("callActivityFlowNode").done();
  }

  @Test
  public void testGetType() {
    Assert.assertEquals(BpmnComponentType.CALL_ACTIVITY, callActivityFlowNodeProcessor.getType());
  }

  @Test
  public void testAddExtensionElementsWithAllParametersAsNull() {
    callActivityFlowNodeProcessor.addExtensionElements(null, null, null, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.never())
        .addAllValidExtensionElements(
            any(FlowNode.class), any(FlowNode.class), any(BpmnModelInstance.class));
  }

  @Test
  public void testAddExtensionElementsWithNullBaseTemplateFlowNodeAndNullBpmnModelInstance() {
    flowNode = bpmnModelInstance.getModelElementById("callActivityFlowNode");
    ((CallActivity) flowNode).setCalledElement("callActivityFlowNode");
    callActivityFlowNodeProcessor.addExtensionElements(
        flowNode, null, null, baseTemplateBpmnModelInstance);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddExtensionElementsWithNullFlowNodeAndNullBaseTemplateBpmnModelInstance() {
    baseTemplateFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("callActivityFlowNode");
    callActivityFlowNodeProcessor.addExtensionElements(
        null, baseTemplateFlowNode, bpmnModelInstance, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddEventToSubProcess() {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> processOptional = processes.stream().findFirst();
    processOptional.ifPresent(
        process -> process.builder().eventSubProcess().startEvent("subProcessStartNode").done());
    subProcess =
        bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst().get();

    Collection<Process> baseTemplateProcesses =
        baseTemplateBpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> baseTemplateProcessOptional = baseTemplateProcesses.stream().findFirst();
    baseTemplateProcessOptional.ifPresent(
        process ->
            process
                .builder()
                .eventSubProcess()
                .startEvent("subProcessStartNode")
                .callActivity("baseTemplateSubProcessCallActivity")
                .name("dummyName")
                .calledElement("dummyCalledElement")
                .done());
    baseTemplateSubprocess =
        baseTemplateBpmnModelInstance.getModelElementsByType(SubProcess.class).stream()
            .findFirst()
            .get();

    StartEvent subProcessSourceNode =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst().get();

    callActivityFlowNodeProcessor.addEventToSubProcess(
        subProcessSourceNode, subProcess, baseTemplateSubprocess, null);

    Optional<StartEvent> subprocessStartEvent =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessStartEvent.isPresent());

    Optional<CallActivity> subprocessCallActivity =
        subProcess.getChildElementsByType(CallActivity.class).stream().findFirst();
    Assert.assertTrue(subprocessCallActivity.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            subprocessStartEvent.get(), subprocessCallActivity.get()));
  }
}
