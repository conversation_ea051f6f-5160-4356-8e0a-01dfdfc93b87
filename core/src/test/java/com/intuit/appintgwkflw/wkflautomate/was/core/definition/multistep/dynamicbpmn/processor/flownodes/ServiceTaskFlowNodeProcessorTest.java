package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.ServiceTaskFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Collection;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the ServiceTaskFlowNodeProcessor class.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ServiceTaskFlowNodeProcessorTest {

  @InjectMocks
  ServiceTaskFlowNodeProcessor serviceTaskFlowNodeProcessor;

  @Mock DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private FlowNode flowNode;
  private FlowNode baseTemplateFlowNode;
  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private SubProcess subProcess;
  private SubProcess baseTemplateSubprocess;

  @Before
  public void setup() {
    bpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").serviceTask("serviceTask").done();
  }

  @Test
  public void testGetType() {
    Assert.assertEquals(BpmnComponentType.SERVICE_TASK, serviceTaskFlowNodeProcessor.getType());
  }

  @Test
  public void testAddExtensionElementsWithAllParametersAsNull() {
    serviceTaskFlowNodeProcessor.addExtensionElements(null, null, null, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.never())
        .addAllValidExtensionElements(
            any(FlowNode.class), any(FlowNode.class), any(BpmnModelInstance.class));
  }

  @Test
  public void testAddExtensionElementsWithNullBaseTemplateFlowNodeAndNullBpmnModelInstance() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").serviceTask("serviceTask").done();
    flowNode = bpmnModelInstance.getModelElementById("serviceTask");
    serviceTaskFlowNodeProcessor.addExtensionElements(
        flowNode, null, null, baseTemplateBpmnModelInstance);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddExtensionElementsWithNullFlowNodeAndNullBaseTemplateBpmnModelInstance() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").serviceTask("serviceTask").done();
    baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById("serviceTask");
    serviceTaskFlowNodeProcessor.addExtensionElements(
        null, baseTemplateFlowNode, bpmnModelInstance, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddEventToSubProcess() {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> processOptional = processes.stream().findFirst();
    processOptional.ifPresent(
        process -> process.builder().eventSubProcess().startEvent("subProcessStartNode").done());
    subProcess =
        bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst().get();

    Collection<Process> baseTemplateProcesses =
        baseTemplateBpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> baseTemplateProcessOptional = baseTemplateProcesses.stream().findFirst();
    baseTemplateProcessOptional.ifPresent(
        process ->
            process
                .builder()
                .eventSubProcess()
                .startEvent("subProcessStartNode")
                .serviceTask("baseTemplateSubProcessServiceTask")
                .name("dummyName")
                .camundaType("dummyType")
                .camundaTopic("dummyTopic")
                .done());
    baseTemplateSubprocess =
        baseTemplateBpmnModelInstance.getModelElementsByType(SubProcess.class).stream()
            .findFirst()
            .get();

    StartEvent subProcessSourceNode =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst().get();

    serviceTaskFlowNodeProcessor.addEventToSubProcess(
        subProcessSourceNode, subProcess, baseTemplateSubprocess, null);

    Optional<StartEvent> subprocessStartEvent =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessStartEvent.isPresent());

    Optional<ServiceTask> subprocessEndEvent =
        subProcess.getChildElementsByType(ServiceTask.class).stream().findFirst();
    Assert.assertTrue(subprocessEndEvent.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            subprocessStartEvent.get(), subprocessEndEvent.get()));
  }

  @Test
  public void testAddImplicitEvent() {

    StartEvent sourceFlowNode = bpmnModelInstance.getModelElementById("startEvent");
    StartEvent baseTemplateSourceFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    ServiceTask baseTemplateTargetFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("serviceTask");

    SequenceFlow baseTemplateSequenceFlow =
        baseTemplateSourceFlowNode.getOutgoing().stream().findFirst().get();
    baseTemplateSequenceFlow.setName("dummyName");
    baseTemplateSequenceFlow.setTextContent("dummyTextContent");

    serviceTaskFlowNodeProcessor.addImplicitEvents(
        sourceFlowNode,
        baseTemplateTargetFlowNode,
        baseTemplateSequenceFlow,
        bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    Optional<ServiceTask> optionalTargetFlowNode =
        bpmnModelInstance.getModelElementsByType(ServiceTask.class).stream().findFirst();
    Assert.assertTrue(optionalTargetFlowNode.isPresent());
    Assert.assertEquals(baseTemplateTargetFlowNode.getId(), optionalTargetFlowNode.get().getId());

    Optional<StartEvent> optionalSourceFlowNode =
        bpmnModelInstance.getModelElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(optionalSourceFlowNode.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            optionalSourceFlowNode.get(), optionalTargetFlowNode.get()));

    SequenceFlow expectedSequenceFlow =
        optionalSourceFlowNode.get().getOutgoing().stream().findFirst().get();
    Assert.assertEquals(baseTemplateSequenceFlow.getName(), expectedSequenceFlow.getName());
    Assert.assertEquals(
        baseTemplateSequenceFlow.getTextContent(), expectedSequenceFlow.getTextContent());
  }
}
