package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.MultiStepProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.OutgoingActivityMapperFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.PlaceholderExtractorProvider;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.CreateMultiStepDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CreateMultiStepDefinitionHandlerTest {

    private Definition multiConditionDefinition;

    @InjectMocks
    private CreateMultiStepDefinitionHandler createMultiStepDefinitionHandler;

    @Mock
    private OutgoingActivityMapperFactory outgoingActivityMapperFactory;

    @Mock
    private MultiStepProcessorFactory multiStepProcessorFactory;

    @Mock
    private ConditionalElementFactory conditionalElementFactory;

    @Mock
    private PlaceholderExtractorProvider placeholderExtractorProvider;

    @Mock
    private TranslationService translationService;

    @Mock
    private FeatureFlagManager featureFlagManager;

    @Mock
    private WASContextHandler wasContextHandler;
    @Mock
    private MultiStepPlaceholderExtractor multiStepPlaceholderExtractor;

    private BpmnModelInstance bpmnModelInstance;
    private DmnModelInstance dmnModelInstance;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();
    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

    private static final String CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

    @Before
    public void setUp() {

        createMultiStepDefinitionHandler = new CreateMultiStepDefinitionHandler(conditionalElementFactory,
                placeholderExtractorProvider, translationService, wasContextHandler,
                featureFlagManager, outgoingActivityMapperFactory, multiStepProcessorFactory,
            multiStepPlaceholderExtractor);

        multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
        bpmnModelInstance =
                Bpmn.readModelFromStream(
                        IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
        dmnModelInstance =
                Dmn.readModelFromStream(
                        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    }

    @Test
    public void testCreateCustomWorkflowDefinition_Invoice_DefinitionCreated() {
        multiConditionDefinition.setDisplayName("Test definition ");

        DefinitionInstance definitionInstance =
            new DefinitionInstance(
                this.multiConditionDefinition,
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

        definitionInstance.setUuid("UUID");

        List<Pair<String, String>> activityIdToCalledElementsForDmn = new ArrayList<>();
        String firstCallActivityId = "activity-1";
        String firstCalledElementId = "sendForApproval";
        String secondCallActivityId = "activity-2";
        String secondCalledElementId = "sendForApproval";
        activityIdToCalledElementsForDmn.add(new Pair<>(firstCallActivityId, firstCalledElementId));
        activityIdToCalledElementsForDmn.add(new Pair<>(secondCallActivityId, secondCalledElementId));

        Mockito.when(outgoingActivityMapperFactory.fetchOutgoingActivityIds(anyString(), any()))
                .thenReturn(activityIdToCalledElementsForDmn);

        Map<String, String> stepIdToActivityIdMap = new HashMap<>();
        String firstActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-1";
        String secondActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-2";
        stepIdToActivityIdMap.put(firstActionStepId, firstCallActivityId);
        stepIdToActivityIdMap.put(secondActionStepId, secondCallActivityId);

        Mockito.when(multiStepProcessorFactory
                        .processWorkflowStep(any(), any(), any(), any(), anySet()))
                .thenReturn(stepIdToActivityIdMap)
                .thenReturn(new HashMap<>());

        definitionInstance =
                createMultiStepDefinitionHandler.process(definitionInstance, DefinitionTestConstants.REALM_ID);

        Assert.assertNotNull(definitionInstance);

    }


}
