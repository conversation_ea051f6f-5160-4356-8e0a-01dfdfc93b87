package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.BusinessRuleTaskOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Collections;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

@RunWith(MockitoJUnitRunner.class)
public class BusinessRuleTaskOutgoingActivityMapperTest {

  @InjectMocks
  private BusinessRuleTaskOutgoingActivityMapper businessRuleTaskOutgoingActivityMapper;

  private BpmnModelInstance multiConditionBpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private Definition multiConditionDefinition;

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  @Before
  public void setUp() {
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
  }

  @Test
  public void test_fetchOutgoingActivityIds() {
    DefinitionInstance definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiConditionBpmnModelInstance, Collections.singletonList(dmnModelInstance),
        new TemplateDetails());
    String businessRuleTaskId = ((StartEvent) CustomWorkflowUtil.findStartEventElement(
        multiConditionBpmnModelInstance))
        .getOutgoing().stream().findFirst().get().getTarget().getId();
    Assert.notEmpty(
        businessRuleTaskOutgoingActivityMapper.fetchOutgoingActivityIds(businessRuleTaskId,
            definitionInstance));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_fetchOutgoingActivityIds_throwsException() {
    DefinitionInstance definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiConditionBpmnModelInstance, Collections.singletonList(dmnModelInstance),
        new TemplateDetails());
    businessRuleTaskOutgoingActivityMapper.fetchOutgoingActivityIds(null, definitionInstance);
  }
}
