package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for DynamicBpmnWasCamundaMapper
 *
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnWasCamundaTransformerTest {

    private TemplateMetadata templateMetadata;

    private TemplateDetails templateDetails;

    private BpmnModelInstance bpmnModelInstance;

    private DmnModelInstance dmnModelInstance;

    @Before
    public void setUp() {
        templateMetadata = new TemplateMetadata();
        templateDetails = new TemplateDetails();
        bpmnModelInstance = Bpmn.createProcess().name("demoProcessName").done();
    }

    @Test
    public void testUpdateTemplateDetailsValuesForDynamicBpmnForNonDynamicBpmn() {
        templateMetadata.setTemplateCreatedDynamically(false);
        templateDetails.setVersion(0);

        DynamicBpmnWasCamundaTransformer.transformTemplateDetailsValuesForBpmn(templateMetadata, bpmnModelInstance, templateDetails);

        Assert.assertEquals(0, templateDetails.getVersion());
    }

    @Test
    public void testUpdateTemplateDetailsValuesForDynamicBpmnForBpmnModelType() {
        templateMetadata.setTemplateCreatedDynamically(true);
        templateMetadata.setTemplateAdjacencyValuesMd5Sum("md5Sum");

        templateDetails.setVersion(7);
        templateDetails.setTemplateName("demoTemplateName_hashValue");
        templateDetails.setDisplayName("demoDisplayName");
        templateDetails.setTemplateAdjacencyValuesMd5Sum(null);
        templateDetails.setTemplateData(null);

        DynamicBpmnWasCamundaTransformer.transformTemplateDetailsValuesForBpmn(templateMetadata, bpmnModelInstance, templateDetails);

        Assert.assertEquals(-1, templateDetails.getVersion());
        Assert.assertEquals("demoTemplateName", templateDetails.getTemplateName());
        Assert.assertEquals("demoProcessName", templateDetails.getDisplayName());
        Assert.assertEquals("md5Sum", templateDetails.getTemplateAdjacencyValuesMd5Sum());
        Assert.assertNotNull(templateDetails.getTemplateData());
    }

    @Test
    public void testUpdateTemplateDetailsValuesForDynamicBpmnForDmnModelType() {

        dmnModelInstance = BpmnProcessorUtil.readDMNFile("baseTemplates/dmn/customApprovalBaseTemplate.dmn");

        templateMetadata.setTemplateCreatedDynamically(true);
        templateMetadata.setTemplateAdjacencyValuesMd5Sum("md5Sum");

        templateDetails.setVersion(7);
        templateDetails.setTemplateName("demoTemplateName_hash_Value");

        DynamicBpmnWasCamundaTransformer.transformTemplateDetailsValuesForDmn(templateMetadata, dmnModelInstance, templateDetails);

        Assert.assertEquals(-1, templateDetails.getVersion());
        Assert.assertEquals("demoTemplateName", templateDetails.getTemplateName());
        Assert.assertNull(templateDetails.getDisplayName());
        Assert.assertNull(templateDetails.getTemplateAdjacencyValuesMd5Sum());
        Assert.assertNotNull(templateDetails.getTemplateData());
    }

    @Test
    public void testGetTemplateNameAsPerCamundaForDynamicBpmn() {
        TemplateDetails wasDmnTemplateDetails = new TemplateDetails();
        TemplateDetails wasBpmnTemplateDetails = new TemplateDetails();

        wasDmnTemplateDetails.setTemplateName("wasTemplateName");
        wasBpmnTemplateDetails.setTemplateAdjacencyValuesMd5Sum("camundaHashValue");

        Assert.assertEquals("wasTemplateName_camundaHashValue", DynamicBpmnWasCamundaTransformer.getTemplateNameCamundaDeployment(wasDmnTemplateDetails, wasBpmnTemplateDetails));
    }

    @Test
    public void testGetTemplateNameAsPerCamundaForDynamicBpmnForNonDynamicDefinitions() {
        TemplateDetails wasDmnTemplateDetails = new TemplateDetails();
        TemplateDetails wasBpmnTemplateDetails = new TemplateDetails();

        wasDmnTemplateDetails.setTemplateName("wasTemplateName");
        wasBpmnTemplateDetails.setTemplateAdjacencyValuesMd5Sum(null);

        Assert.assertEquals("wasTemplateName", DynamicBpmnWasCamundaTransformer.getTemplateNameCamundaDeployment(wasDmnTemplateDetails, wasBpmnTemplateDetails));
    }
}
