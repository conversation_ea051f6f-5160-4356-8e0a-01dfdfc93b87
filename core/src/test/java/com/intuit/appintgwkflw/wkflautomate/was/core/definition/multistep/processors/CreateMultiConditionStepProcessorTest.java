package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiConditionBusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.CreateMultiConditionStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Set;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

/**
 * author btolani
 */
@RunWith(MockitoJUnitRunner.class)
public class CreateMultiConditionStepProcessorTest {

  @InjectMocks
  public CreateMultiConditionStepProcessor createMultiConditionStepProcessor;

  @Mock
  private MultiConditionBusinessRuleTaskHandler multiConditionBusinessRuleTaskHandler;

  DefinitionInstance definitionInstance;
  Definition multiConditionDefinition;
  DmnModelInstance dmnModelInstance;
  DmnModelInstance invalidDmnModelInstance;
  BpmnModelInstance multiConditionBpmnModelInstance;

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  private static final String CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow_withNoDecisionTable.dmn");

  @Before
  public void init() {
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    invalidDmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML,
                Charset.defaultCharset()));

    definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());
  }

  @Test
  public void testProcessWorkflowStep_Success() {
    String dmnElementId = "decisionElement";
    DmnModelInstance dmnModelInstance = CustomWorkflowUtil.getDmnModelInstance(definitionInstance,
        dmnElementId);
    WorkflowStep firstWorkflowStep = multiConditionDefinition.getWorkflowSteps().stream()
        .findFirst().get();
    Mockito.when(multiConditionBusinessRuleTaskHandler.workflowDecisionHandler(dmnModelInstance,
            firstWorkflowStep, definitionInstance, new LinkedList<>()))
        .thenReturn(new HashMap<>());
    Assert.notNull(createMultiConditionStepProcessor.processWorkflowStep(dmnElementId,
        firstWorkflowStep,
        definitionInstance,
        new LinkedList<>(),
        new HashSet<>()));
  }

  @Test
  public void testProcessWorkflowStep_Success_withNonEmptyDmnRootWorkflowStepIdsHashSet() {
    String dmnElementId = "decisionElement";
    WorkflowStep firstWorkflowStep = multiConditionDefinition.getWorkflowSteps().stream()
        .findFirst().get();
    Set<String> visitedDmnRootWorkflowStepIds = new HashSet<>();
    visitedDmnRootWorkflowStepIds.add(firstWorkflowStep.getId().toString());

    Assert.isNull(createMultiConditionStepProcessor.processWorkflowStep(dmnElementId,
        firstWorkflowStep,
        definitionInstance,
        new LinkedList<>(),
        visitedDmnRootWorkflowStepIds));
  }


}
