package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiConditionRuleLineParserTest {
    private static final String CUSTOM_WORKFLOW_DMN_XML_WITH_OR =
            TestHelper.readResourceAsString("dmn/multiConditionDmnWithOrSupport.dmn");
    private static final String CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");

    private static final String OLD_CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/customWorkflowDefinition.dmn");

    private static final String CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT =
            TestHelper.readResourceAsString("dmn/customApprovalWorkflowWithMultiSplit.dmn");

    private static final String CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT_AND_OR =
        TestHelper.readResourceAsString("dmn/customApprovalWorkflowWithMultiSplitAndOR.dmn");

    private DmnModelInstance dmnModelInstanceWithOR;
    private DmnModelInstance dmnModelInstance;
    private DmnModelInstance oldDmnModelInstance;
    private DmnModelInstance dmnModelInstanceWithMultiSplit;
    private DmnModelInstance dmnModelInstanceWithMultiSplitAndOR;
    @InjectMocks
    private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
    @InjectMocks
    private MultiConditionRuleLineParser multiConditionProcessor;

    @Before
    public void init() {
        dmnModelInstanceWithOR =
                Dmn.readModelFromStream(
                        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML_WITH_OR, Charset.defaultCharset()));
        dmnModelInstance = Dmn.readModelFromStream(
                IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
        oldDmnModelInstance = Dmn.readModelFromStream(
                IOUtils.toInputStream(OLD_CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
        dmnModelInstanceWithMultiSplit = Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT, Charset.defaultCharset()));
        dmnModelInstanceWithMultiSplitAndOR = Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT_AND_OR, Charset.defaultCharset()));
    }

    @Test
    public void testParseDecisionTable() {
        Collection<DecisionTable> decisionTables = dmnModelInstance.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionProcessor.parseDecisionTable(decisionTable, dmnHeadersMap);
        Assert.assertEquals(3, indexToRulesMap.size());
        Assert.assertEquals(2, indexToRulesMap.get("0").size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("1").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("1").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("4").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("4").get(WorkflowConstants.NO_RULE).get(0).size());
    }

    @Test
    public void testParseDecisionTableForORDmn() {
        Collection<DecisionTable> decisionTables = dmnModelInstanceWithOR.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionProcessor.parseDecisionTable(decisionTable, dmnHeadersMap);
        Assert.assertEquals(3, indexToRulesMap.size());
        Assert.assertEquals(2, indexToRulesMap.get("0").size());
        Assert.assertEquals(2, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("1").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("1").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("4").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("4").get(WorkflowConstants.NO_RULE).get(0).size());
    }

    @Test
    public void testParseDecisionTableForOldDmns() {
        Collection<DecisionTable> decisionTables = oldDmnModelInstance.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionProcessor.parseDecisionTable(decisionTable, dmnHeadersMap);
        Assert.assertEquals(1, indexToRulesMap.size());
        Assert.assertEquals(2, indexToRulesMap.get("0").size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).get(0).size());
    }

    @Test
    public void testParseDecisionTableForMultiSplit() {
        Collection<DecisionTable> decisionTables = dmnModelInstanceWithMultiSplit.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionProcessor.parseDecisionTable(decisionTable, dmnHeadersMap);
        Assert.assertEquals(1, indexToRulesMap.size());
        Assert.assertEquals(2, indexToRulesMap.get("0").size());
        Assert.assertEquals(3, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(1).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(2).size());
    }

    @Test
    public void testParseDecisionTableForMultiSplitAndOrCombined() {
        Collection<DecisionTable> decisionTables = dmnModelInstanceWithMultiSplitAndOR.getModelElementsByType(DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);
        Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionProcessor.parseDecisionTable(decisionTable, dmnHeadersMap);
        Assert.assertEquals(1, indexToRulesMap.size());
        Assert.assertEquals(2, indexToRulesMap.get("0").size());
        Assert.assertEquals(3, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.NO_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(0).size());
        Assert.assertEquals(1, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(1).size());
        Assert.assertEquals(2, indexToRulesMap.get("0").get(WorkflowConstants.YES_RULE).get(2).size());

    }
}
