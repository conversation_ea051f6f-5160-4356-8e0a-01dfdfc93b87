package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiConditionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.Import;

/**
 * author btolani
 */
@Import({WorkflowGlobalConfiguration.class, CustomWorkflowConfig.class})
@RunWith(MockitoJUnitRunner.class)
public class MultiConditionRuleLineProcessorTest {

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");
  private static final String CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow_withNoDecisionTable.dmn");
  private static final String CUSTOM_WORKFLOW_WITH_CUSTOM_FIELDS_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflowWithCustomFields.dmn");
  private DefinitionInstance definitionInstance;
  private DefinitionInstance definitionInstanceWithCustomField;
  private Definition multiConditionDefinition;
  private Definition multiConditionDefinitionWithCustomField;
  private DmnModelInstance dmnModelInstance;
  private DmnModelInstance invalidDmnModelInstance;
  private DmnModelInstance customFieldsDmnModelInstance;
  private BpmnModelInstance multiConditionBpmnModelInstance;

  @InjectMocks
  private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
  @InjectMocks
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  @Mock
  private BusinessRuleTaskHandler businessRuleTaskHandler;
  @Mock
  private MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor;
  @Mock
  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private FeatureFlagManager featureFlagManager;
  @Before
  @SneakyThrows
  public void init() {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(customWorkflowConfig, featureFlagManager);
    multiConditionRuleLineProcessor = new MultiConditionRuleLineProcessor(customWorkflowConfig,
        businessRuleTaskHandler, customWorkflowDecisionHandler, multiConditionPlaceholderExtractor,
        featureFlagManager);
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML,
                Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML,
                Charset.defaultCharset()));

    definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());

    buildWorkflowStepMap(definitionInstance);

    invalidDmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML,
                Charset.defaultCharset()));

    customFieldsDmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_WITH_CUSTOM_FIELDS_DMN_XML,
                Charset.defaultCharset()));

    multiConditionDefinitionWithCustomField = TestHelper.mockMultiConditionDefinitionEntityWithCustomFields();

    definitionInstanceWithCustomField = new DefinitionInstance(multiConditionDefinitionWithCustomField,
        multiConditionBpmnModelInstance,
        Collections.singletonList(customFieldsDmnModelInstance), new TemplateDetails());

  }

  private void buildWorkflowStepMap(DefinitionInstance definitionInstance) {
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();

    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    definitionInstance.setWorkflowStepMap(workflowStepMap);
  }

  @Test
  public void test_createDmnHeaders_Success() {
    Map<String, String> expectedInputColumns = new HashMap<>();
    expectedInputColumns.put("Index", "integer");
    expectedInputColumns.put("TxnAmount", "double");
    expectedInputColumns.put("Customer", "list");
    Map<String, String> expectedOutputColumns = new HashMap<>();
    expectedOutputColumns.put("decisionResult", "String");

    DecisionTable decisionTable = multiConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        multiConditionDefinition.getWorkflowSteps().stream().findFirst().get(), definitionInstance);
    Map<String, String> actualInputColumns = decisionTable.getInputs().stream().collect(
        Collectors.toMap(input -> input.getLabel(),
            input -> input.getInputExpression().getTypeRef()));
    Map<String, String> actualOutputColumns = decisionTable.getOutputs().stream()
        .collect(Collectors.toMap(output -> output.getLabel(), output -> output.getTypeRef()));

    Assert.assertEquals(expectedInputColumns, actualInputColumns);
    Assert.assertEquals(expectedOutputColumns, actualOutputColumns);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_createDmnHeaders_decisionTableMissing_Exception() {
    multiConditionRuleLineProcessor.createDmnHeaders(invalidDmnModelInstance,
        multiConditionDefinition.getWorkflowSteps().stream().findFirst().get(), definitionInstance);
  }

  @Test
  public void testBuildDmn() {
    DecisionTable decisionTable = multiConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        multiConditionDefinition.getWorkflowSteps().stream().findFirst().get(), definitionInstance);

    Collection<Rule> rules = decisionTable.getRules();

    Map<String, Map<String, DmnHeader>> attributeToHeaderMap =
        multiConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable);

    List<String> activityIds = new LinkedList<>();
    String firstCallActivityId = "activity-1";
    String secondCallActivityId = "activity-2";
    activityIds.add(firstCallActivityId);
    activityIds.add(secondCallActivityId);
    Map<String, String> stepIdToActivityId = new HashMap<>();

    String workflowStepId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();

    Mockito.when(businessRuleTaskHandler.getDMNFriendlyExpression("TxnAmount",
            "> 500", "double", false))
        .thenReturn("TxnAmount GT 500");
    Mockito.when(businessRuleTaskHandler.getDMNFriendlyExpression("TxnAmount",
            "BTW 100,1000", "double", false))
        .thenReturn("TxnAmount GT 100 && TxnAmount LT 1000");
    Mockito.when(businessRuleTaskHandler.getDMNFriendlyExpression("Customer",
            "CONTAINS ALL_Customer", "list", false))
        .thenReturn("Customer:SELECT_ALL");

    RuleLineInstance ruleLineInstance = RuleLineInstance.builder().currentIndexColumnValue(0)
        .workflowStepId(workflowStepId).rules(rules).build();

    multiConditionRuleLineProcessor.buildDmn(definitionInstance, ruleLineInstance, activityIds,
        attributeToHeaderMap, stepIdToActivityId, dmnModelInstance);

    Map<String, String> expectedActivityIdStepIdMap = new HashMap<>();
    String firstActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-1";
    String secondActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-2";
    expectedActivityIdStepIdMap.put(firstActionStepId, firstCallActivityId);
    expectedActivityIdStepIdMap.put(secondActionStepId, secondCallActivityId);

    Assert.assertEquals(expectedActivityIdStepIdMap, stepIdToActivityId);
  }

  @Test
  public void testBuildDmnHeadersMap() {
    DecisionTable decisionTable = multiConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        multiConditionDefinition.getWorkflowSteps().stream().findFirst().get(), definitionInstance);

    Map<String, String> expectedInputMap = new HashMap<>();
    expectedInputMap.put("TxnAmount", "double");
    expectedInputMap.put("Index", "integer");
    expectedInputMap.put("Customer", "list");
    Map<String, String> expectedOutputMap = new HashMap<>();
    expectedOutputMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT,
        WorkflowConstants.STRING_TYPE_CAMUNDA);

    Map<String, Map<String, DmnHeader>> actualOutput = multiConditionRuleLineProcessor.buildDmnHeadersMap(
        decisionTable);

    Assert.assertEquals(expectedInputMap,
        actualOutput.get(WorkflowConstants.INPUT).entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDataType())));

    Assert.assertEquals(expectedOutputMap,
        actualOutput.get(WorkflowConstants.OUTPUT).entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDataType())));
  }

  @Test
  public void testBuildDmnHeadersMapWithCustomFields() {
    buildWorkflowStepMap(definitionInstanceWithCustomField);
    DecisionTable decisionTable = multiConditionRuleLineProcessor.createDmnHeaders(customFieldsDmnModelInstance,
        multiConditionDefinitionWithCustomField.getWorkflowSteps().stream().findFirst().get(), definitionInstanceWithCustomField);

    Map<String, String> expectedInputMap = new HashMap<>();
    expectedInputMap.put("TxnAmount", "double");
    expectedInputMap.put("Index", "integer");
    expectedInputMap.put("CF302300000000000329561", "list");
    expectedInputMap.put("CF302300000000000282303", "string");

    Map<String, String> expectedOutputMap = new HashMap<>();
    expectedOutputMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT,
        WorkflowConstants.STRING_TYPE_CAMUNDA);

    Map<String, Map<String, DmnHeader>> actualOutput = multiConditionRuleLineProcessor.buildDmnHeadersMap(
        decisionTable);

    Assert.assertEquals(expectedInputMap,
        actualOutput.get(WorkflowConstants.INPUT).entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDataType())));

    Assert.assertEquals(expectedOutputMap,
        actualOutput.get(WorkflowConstants.OUTPUT).entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDataType())));
  }

}
