package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiConditionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.Import;

@Import({WorkflowGlobalConfiguration.class, CustomWorkflowConfig.class})
@RunWith(MockitoJUnitRunner.class)
public class MultiSplitRuleLineProcessorTest {
  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");
  private static final String CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow_withNoDecisionTable.dmn");
  private DefinitionInstance definitionInstance;
  private Definition multiSplitDefinition;
  private DmnModelInstance dmnModelInstance;
  private BpmnModelInstance multiConditionBpmnModelInstance;
  @InjectMocks
  private MultiSplitRuleLineProcessor multiSplitRuleLineProcessor;
  @InjectMocks
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  @Mock
  private BusinessRuleTaskHandler businessRuleTaskHandler;
  @Mock
  private MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor;
  @Mock
  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private FeatureFlagManager featureFlagManager;

  @Before
  @SneakyThrows
  public void init() {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(customWorkflowConfig, featureFlagManager);
    multiSplitRuleLineProcessor = new MultiSplitRuleLineProcessor(customWorkflowConfig,
        businessRuleTaskHandler, customWorkflowDecisionHandler, multiConditionPlaceholderExtractor,featureFlagManager);
    multiSplitDefinition = TestHelper.mockMultiSplitDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML,
                Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML,
                Charset.defaultCharset()));

    DefinitionId.DefinitionIdBuilder definitionIdBuilder =
        DefinitionId.builder().realmId("123L").uniqueId("uniqueId");

    definitionInstance = new DefinitionInstance(multiSplitDefinition,
        multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());

    buildWorkflowStepMap(definitionInstance);

  }

  private void buildWorkflowStepMap(DefinitionInstance definitionInstance) {
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();

    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    definitionInstance.setWorkflowStepMap(workflowStepMap);
  }

  @Test
  public void testBuildDmn() {
    DecisionTable decisionTable = multiSplitRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        multiSplitDefinition.getWorkflowSteps().stream().findFirst().get(), definitionInstance);

    Collection<Rule> rules = decisionTable.getRules();

    Map<String, Map<String, DmnHeader>> attributeToHeaderMap =
        multiSplitRuleLineProcessor.buildDmnHeadersMap(decisionTable);

    List<String> activityIds = new LinkedList<>();
    String firstCallActivityId = "activity-1";
    String secondCallActivityId = "activity-2";
    String thirdCallActivityId = "activity-3";
    String fourthCallActivityId = "activity-4";
    activityIds.add(firstCallActivityId);
    activityIds.add(secondCallActivityId);
    activityIds.add(thirdCallActivityId);
    activityIds.add(fourthCallActivityId);
    Map<String, String> stepIdToActivityId = new HashMap<>();

    String workflowStepId = multiSplitDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();

    List<WorkflowStep> siblings = new ArrayList<>();
    siblings.add(definitionInstance.getDefinition().getWorkflowSteps(0));
    siblings.add(definitionInstance.getDefinition().getWorkflowSteps(1));
    RuleLineInstance ruleLineInstance = RuleLineInstance.builder().currentIndexColumnValue(0)
        .workflowStepId(workflowStepId).rules(rules)
        .siblingSteps(siblings)
            .build();

    multiSplitRuleLineProcessor.buildDmn(definitionInstance, ruleLineInstance, activityIds,
        attributeToHeaderMap, stepIdToActivityId, dmnModelInstance);

    Map<String, String> expectedActivityIdStepIdMap = new HashMap<>();
    String firstActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-1";
    String secondActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-2";
    String thirdActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-3";
    String fourthActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-4";
    expectedActivityIdStepIdMap.put(firstActionStepId, firstCallActivityId);
    expectedActivityIdStepIdMap.put(secondActionStepId, secondCallActivityId);
    expectedActivityIdStepIdMap.put(thirdActionStepId, fourthCallActivityId);
    expectedActivityIdStepIdMap.put(fourthActionStepId, thirdCallActivityId);
    Assert.assertEquals(expectedActivityIdStepIdMap, stepIdToActivityId);
  }
}
