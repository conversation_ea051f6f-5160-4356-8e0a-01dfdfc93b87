package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiStepPlaceholderSubstitutor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.WorkflowStep;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class MultiStepPlaceholderSubstitutorTest {
    public static final String APPROVAL_ACTION_KEY = "approval";
    public static final String INVOICE_RECORD_TYPE = "invoice";
    @Mock
    WASContextHandler wasContextHandler;
    private CustomWorkflowConfig customWorkflowConfig;
    private TemplateActionBuilder templateActionBuilder;
    private MultiStepPlaceholderSubstitutor multiStepPlaceholderSubstitutor;
    @Spy
    private TranslationService translationService = TestHelper.initTranslationService();

    @Before
    @SneakyThrows
    public void setup() throws Exception {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        templateActionBuilder = new TemplateActionBuilder(wasContextHandler, translationService);
        multiStepPlaceholderSubstitutor = new MultiStepPlaceholderSubstitutor(customWorkflowConfig);
        MockitoAnnotations.initMocks(this);
    }

    private WorkflowStep getPopulatedWorkflowStep() {
        WorkflowStep workflowStep = new WorkflowStep();
        com.intuit.v4.workflows.ActionGroup stepActionGroup = new com.intuit.v4.workflows.ActionGroup();
        Record record = customWorkflowConfig.getRecordObjForType(INVOICE_RECORD_TYPE);
        ActionGroup recordActionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, APPROVAL_ACTION_KEY);
        com.intuit.v4.workflows.Action action = templateActionBuilder.buildTemplateStepAction(
                record,
                recordActionGroup,
                recordActionGroup.getActionIdMapper().getActionId());
        stepActionGroup.setAction(action);
        stepActionGroup.setActionKey(APPROVAL_ACTION_KEY);
        workflowStep.setActionGroup(stepActionGroup);
        return workflowStep;
    }

    @Test
    public void testSubstitutePlaceholderValues() {
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        WorkflowStep workflowStep = getPopulatedWorkflowStep();
        multiStepPlaceholderSubstitutor.substitutePlaceholderValues(activityInstance, INVOICE_RECORD_TYPE, workflowStep);
        Assert.assertNotNull(workflowStep.getActionGroup().getAction().getName());
        // assert that field values have been replaced for parent action
        Assert.assertEquals(1, workflowStep.getActionGroup().getAction().getParameters().size());
        Assert.assertEquals("Assignee", workflowStep.getActionGroup().getAction().getParameters().get(0).getParameterName());
        Assert.assertEquals("9130359273350856", workflowStep.getActionGroup().getAction().getParameters().get(0).getFieldValues().get(0));

        Assert.assertEquals(2, workflowStep.getActionGroup().getAction().getSubActions().size());
        // assert that field values have replaced for createTask sub-action
        Assert.assertEquals("Create a task", workflowStep.getActionGroup().getAction().getSubActions().get(0).getName());
        Assert.assertEquals(2, workflowStep.getActionGroup().getAction().getSubActions().get(0).getParameters().size());
        Assert.assertEquals("Assignee", workflowStep.getActionGroup().getAction().getSubActions().get(0).getParameters().get(0).getParameterName());
        Assert.assertEquals("9130359273349816", workflowStep.getActionGroup().getAction().getSubActions().get(0).getParameters().get(0).getFieldValues().get(0));
        Assert.assertEquals("TaskName", workflowStep.getActionGroup().getAction().getSubActions().get(0).getParameters().get(1).getParameterName());
        Assert.assertEquals("Approval due for Invoice [[Invoice Number]]", workflowStep.getActionGroup().getAction().getSubActions().get(0).getParameters().get(1).getFieldValues().get(0));
        // assert that field values have replaced for sendCompanyEmail sub-action
        Assert.assertEquals("Send a company email", workflowStep.getActionGroup().getAction().getSubActions().get(1).getName());
        Assert.assertEquals(5, workflowStep.getActionGroup().getAction().getSubActions().get(1).getParameters().size());
        Assert.assertEquals("SendTo", workflowStep.getActionGroup().getAction().getSubActions().get(1).getParameters().get(3).getParameterName());
        Assert.assertEquals("[[Company Email]]", workflowStep.getActionGroup().getAction().getSubActions().get(1).getParameters().get(3).getFieldValues().get(0));
        Assert.assertEquals("Subject", workflowStep.getActionGroup().getAction().getSubActions().get(1).getParameters().get(4).getParameterName());
        Assert.assertEquals("Review Invoice [[Invoice Number]]", workflowStep.getActionGroup().getAction().getSubActions().get(1).getParameters().get(4).getFieldValues().get(0));
    }
}
