package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.WeekIndexType;
import java.util.Arrays;
import java.util.List;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class MonthlyRecurrenceProcessorTest {
  @InjectMocks private MonthlyRecurrenceProcessor recurrenceProcessor;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRecurrenceMonthly_EveryMonth() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 * * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_EveryMonth31st() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(31), null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 L * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_EveryMonth30th31st() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(30, 31), null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 30,31 * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every3Months31st() {
    DateTime startDate = new DateTime().withMonthOfYear(9);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.MONTHLY, Arrays.asList(31), null, null, null, startDate);

    Assert.assertEquals("0 0 0 L 3,6,9,12 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every3Months30th31st() {
    DateTime startDate = new DateTime().withMonthOfYear(8);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.MONTHLY, Arrays.asList(30, 31), null, null, null, startDate);

    Assert.assertEquals(
        "0 0 0 30,31 2,5,8,11 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_EveryMonth2ndAnd25th() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(2, 25), null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 2,25 * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every4Months2ndAnd25th() {
    DateTime startDate = new DateTime().withMonthOfYear(5);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(2, 25), null, null, null, startDate);

    Assert.assertEquals("0 0 0 2,25 1,5,9 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_EveryMonth1stFriday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.FRIDAY),
            null,
            WeekOfMonthEnum.FIRST,
            new DateTime());

    Assert.assertEquals("0 0 0 ? * 6#1 *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every4Months3rdWednesday() {
    DateTime startDate = new DateTime().withMonthOfYear(7);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.WEDNESDAY),
            null,
            WeekOfMonthEnum.THIRD,
            startDate);

    Assert.assertEquals("0 0 0 ? 3,7,11 4#3 *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every4Months3rdWednesdayAndAllFridaysSaturdays() {
    DateTime startDate = new DateTime().withMonthOfYear(7);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.WEDNESDAY, DayOfWeekEnum.FRIDAY, DayOfWeekEnum.SATURDAY),
            null,
            WeekOfMonthEnum.THIRD,
            startDate);

    Assert.assertEquals(
        "0 0 0 ? 3,7,11 4#3,6,7 *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every3Months() {
    DateTime startDate = new DateTime().withMonthOfYear(10);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.MONTHLY, null, null, null, null, startDate);

    Assert.assertEquals("0 0 0 * 1,4,7,10 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceMonthly_Every5Months() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            5, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 * */5 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRecurrenceMonthly_13Months() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            13,
            RecurTypeEnum.MONTHLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            WeekOfMonthEnum.FIRST,
            new DateTime());
    recurrenceProcessor.getRecurrence(recurrenceRule);
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryMonth() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of());

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryMonth31st() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(31), null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(31));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryMonth30th31st() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(30, 31), null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(30, 31));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every3Months31st() {
    DateTime startDate = new DateTime().withMonthOfYear(9);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.MONTHLY, Arrays.asList(31), null, null, null, startDate);

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(3)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(31));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every3Months30th31st() {
    DateTime startDate = new DateTime().withMonthOfYear(8);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.MONTHLY, Arrays.asList(30, 31), null, null, null, startDate);

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(3)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(30, 31));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryMonth2ndAnd25th() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.MONTHLY, Arrays.asList(2, 25), null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(2, 25));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every4Months2ndAnd25th() {
    DateTime startDate = new DateTime().withMonthOfYear(5);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(2, 25), null, null, null, startDate);

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(4)
            .type(RecurrencePatternType.ABSOLUTEMONTHLY)
            .daysOfMonth(List.of(2, 25));
    ;

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryMonth1stFriday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.FRIDAY),
            null,
            WeekOfMonthEnum.FIRST,
            new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.RELATIVEMONTHLY)
            .weekIndex(WeekIndexType.FIRST)
            .daysOfWeek(List.of(DayOfWeekType.FRI));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every4Months3rdWednesday() {
    DateTime startDate = new DateTime().withMonthOfYear(7);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.WEDNESDAY),
            null,
            WeekOfMonthEnum.THIRD,
            startDate);

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(4)
            .type(RecurrencePatternType.RELATIVEMONTHLY)
            .weekIndex(WeekIndexType.THIRD)
            .daysOfWeek(List.of(DayOfWeekType.WED));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every4Months3rdWednesdayAndAllFridaysSaturdays() {
    DateTime startDate = new DateTime().withMonthOfYear(7);
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            Arrays.asList(2, 25),
            Arrays.asList(DayOfWeekEnum.WEDNESDAY, DayOfWeekEnum.FRIDAY, DayOfWeekEnum.SATURDAY),
            null,
            WeekOfMonthEnum.THIRD,
            startDate);
    List<Integer> daysOfMonth = null;
    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(4)
            .type(RecurrencePatternType.RELATIVEMONTHLY)
            .weekIndex(WeekIndexType.THIRD)
            .daysOfWeek(List.of(DayOfWeekType.WED, DayOfWeekType.FRI, DayOfWeekType.SAT));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }
}
