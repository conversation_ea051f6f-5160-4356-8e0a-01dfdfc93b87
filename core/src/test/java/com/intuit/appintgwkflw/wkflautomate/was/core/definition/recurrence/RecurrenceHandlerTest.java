package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.v4.common.RecurTypeEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class RecurrenceHandlerTest {
  @Mock private WeeklyRecurrenceProcessor recurrenceProcessor;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(recurrenceProcessor.getName()).thenReturn(RecurTypeEnum.WEEKLY);
    RecurrenceHandler.addHandler(recurrenceProcessor.getName(), recurrenceProcessor);
  }

  @Test
  public void getTestNUll() {
    RecurrenceProcessor scheduler = RecurrenceHandler.getHandler(null);
    Assert.assertNull(scheduler);
  }

  @Test
  public void getTestRecurrenceHandler() {
    RecurrenceProcessor scheduler = RecurrenceHandler.getHandler(RecurTypeEnum.WEEKLY);
    Assert.assertNotNull(scheduler);
    Assert.assertTrue(scheduler instanceof WeeklyRecurrenceProcessor);
  }

  @Test
  public void containsFalse() {
    Assert.assertFalse(RecurrenceHandler.contains(null));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(RecurrenceHandler.contains(RecurTypeEnum.WEEKLY));
  }
}
