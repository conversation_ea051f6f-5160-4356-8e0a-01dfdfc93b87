package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.cronutils.model.field.expression.Always;
import com.cronutils.model.field.expression.And;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.On;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.WeekIndexType;
import java.util.Arrays;
import java.util.List;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class RecurrenceProcessorTest {

  @InjectMocks private DailyRecurrenceProcessor recurrenceProcessor;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testPopulateWeeksOfMonthParameter_Every3rdWednesday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            null,
            Arrays.asList(DayOfWeekEnum.WEDNESDAY),
            null,
            WeekOfMonthEnum.THIRD,
            new DateTime());

    FieldExpression on = recurrenceProcessor.populateWeeksOfMonthParameter(recurrenceRule);
    Assert.assertEquals("4#3", on.asString());
  }

  @Test
  public void testPopulateWeeksOfMonthParameter_EveryLastWednesday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            null,
            Arrays.asList(DayOfWeekEnum.WEDNESDAY),
            null,
            WeekOfMonthEnum.LAST,
            new DateTime());

    FieldExpression on = recurrenceProcessor.populateWeeksOfMonthParameter(recurrenceRule);
    Assert.assertEquals("4L", on.asString());
  }

  @Test
  public void testPopulateWeeksOfMonthParameter_EveryLastMondayWednesday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            null,
            Arrays.asList(DayOfWeekEnum.MONDAY, DayOfWeekEnum.WEDNESDAY),
            null,
            WeekOfMonthEnum.LAST,
            new DateTime());

    FieldExpression on = recurrenceProcessor.populateWeeksOfMonthParameter(recurrenceRule);
    Assert.assertEquals("2L,4", on.asString());
  }

  @Test
  public void testPopulateWeeksOfMonthParameter_EmptyDaysOfWeek() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, null, null, null, WeekOfMonthEnum.LAST, new DateTime());

    FieldExpression on = recurrenceProcessor.populateWeeksOfMonthParameter(recurrenceRule);
    Assert.assertEquals("1L", on.asString());
  }

  @Test
  public void testPopulateMonthsOfYearParameter_EmptyMonthsList() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateMonthsOfYearParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof Always);
  }

  @Test
  public void testPopulateMonthsOfYearParameter() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            null,
            null,
            Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.DECEMBER),
            null,
            new DateTime());

    FieldExpression exp = recurrenceProcessor.populateMonthsOfYearParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof And);
    Assert.assertEquals("1,12", exp.asString());
  }

  @Test
  public void testPopulateDaysOfMonthParameter_EmptyList() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfMonthParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof Always);
  }

  @Test
  public void testPopulateDaysOfMonthParameter() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(1, 3, 5, 15), null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfMonthParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof And);
    Assert.assertEquals("1,3,5,15", exp.asString());
  }

  @Test
  public void testPopulateDaysOfMonthParameter_LastDay() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(31), null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfMonthParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof On);
    Assert.assertEquals("L", exp.asString());
  }

  @Test
  public void testPopulateDaysOfMonthParameter_30th() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(30), null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfMonthParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof And);
    Assert.assertEquals("30", exp.asString());
  }

  @Test
  public void testPopulateDaysOfMonthParameter_30thAnd31st() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, Arrays.asList(30, 31), null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfMonthParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof And);
    Assert.assertEquals("30,31", exp.asString());
  }

  @Test
  public void testPopulateDaysOfWeekParameter_EmptyList() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.MONTHLY, null, null, null, null, new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfWeekParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof Always);
  }

  @Test
  public void testPopulateDaysOfWeekParameter() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4,
            RecurTypeEnum.MONTHLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY, DayOfWeekEnum.THURSDAY),
            null,
            null,
            new DateTime());

    FieldExpression exp = recurrenceProcessor.populateDaysOfWeekParameter(recurrenceRule);
    Assert.assertTrue(exp instanceof And);
    Assert.assertEquals("6,2,5", exp.asString());
  }

  @Test
  public void testMapDaysOfWeekTypeForESSSchedule_EmptyRecurrenceDaysOfWeek() {
    Assert.assertNull(recurrenceProcessor.mapDaysOfWeekTypeForESSSchedule(List.of()));
  }

  @Test
  public void testMapDaysOfWeekTypeForESSSchedule() {
    List<DayOfWeekType> dayOfWeekType = List.of(DayOfWeekType.MON, DayOfWeekType.TUE);
    Assert.assertEquals(
        dayOfWeekType,
        recurrenceProcessor.mapDaysOfWeekTypeForESSSchedule(
            List.of(DayOfWeekEnum.MONDAY, DayOfWeekEnum.TUESDAY)));
  }

  @Test
  public void testMapMonthIndexForESSSchedule_EmptyRecurrenceMonthsOfYear() {
    Assert.assertNull(recurrenceProcessor.mapMonthIndexForESSSchedule(List.of()));
  }

  @Test
  public void testMapMonthIndexForESSSchedule() {
    List<Integer> monthIndex = List.of(1, 3);
    Assert.assertEquals(
        monthIndex,
        recurrenceProcessor.mapMonthIndexForESSSchedule(
            List.of(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.MARCH)));
  }

  @Test
  public void testMapRelativeAndAbsoluteParametersForESSSchedule_RelativeMonthly() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    4, RecurTypeEnum.MONTHLY, null, List.of(DayOfWeekEnum.FRIDAY), null, WeekOfMonthEnum.LAST, new DateTime());

    RecurrencePattern pattern = new RecurrencePattern().type(RecurrencePatternType.RELATIVEMONTHLY);
    recurrenceProcessor.mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, pattern);
    Assert.assertEquals(List.of(DayOfWeekType.FRI), pattern.getDaysOfWeek());
    Assert.assertEquals(WeekIndexType.LAST, pattern.getWeekIndex());
    Assert.assertEquals(0, pattern.getDaysOfMonth().size());
  }

  @Test
  public void testMapRelativeAndAbsoluteParametersForESSSchedule_AbsoluteMonthly() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    4, RecurTypeEnum.MONTHLY, List.of(25, 26), null, null, null, new DateTime());

    RecurrencePattern pattern = new RecurrencePattern().type(RecurrencePatternType.ABSOLUTEMONTHLY);
    recurrenceProcessor.mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, pattern);
    Assert.assertEquals(0, pattern.getDaysOfWeek().size());
    Assert.assertNull(pattern.getWeekIndex());
    Assert.assertEquals(List.of(25, 26), pattern.getDaysOfMonth());
  }

  @Test
  public void testMapRelativeAndAbsoluteParametersForESSSchedule_RelativeYearly() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    1, RecurTypeEnum.YEARLY, null, List.of(DayOfWeekEnum.FRIDAY), List.of(MonthsOfYearEnum.DECEMBER), WeekOfMonthEnum.LAST, new DateTime());

    RecurrencePattern pattern = new RecurrencePattern().type(RecurrencePatternType.RELATIVEYEARLY);
    recurrenceProcessor.mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, pattern);
    Assert.assertEquals(List.of(DayOfWeekType.FRI), pattern.getDaysOfWeek());
    Assert.assertEquals(WeekIndexType.LAST, pattern.getWeekIndex());
    Assert.assertEquals(0, pattern.getDaysOfMonth().size());
  }

  @Test
  public void testMapRelativeAndAbsoluteParametersForESSSchedule_RelativeYearly_EmptyWeeksOfMonth() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    1, RecurTypeEnum.YEARLY, null, List.of(DayOfWeekEnum.FRIDAY), List.of(MonthsOfYearEnum.DECEMBER), null, new DateTime());

    RecurrencePattern pattern = new RecurrencePattern().type(RecurrencePatternType.RELATIVEYEARLY);
    recurrenceProcessor.mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, pattern);
    Assert.assertEquals(List.of(DayOfWeekType.FRI), pattern.getDaysOfWeek());
    Assert.assertNull(pattern.getWeekIndex());
    Assert.assertEquals(0, pattern.getDaysOfMonth().size());
  }

  @Test
  public void testMapRelativeAndAbsoluteParametersForESSSchedule_AbsoluteYearly() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    4, RecurTypeEnum.YEARLY, List.of(25, 26), null, List.of(MonthsOfYearEnum.DECEMBER), null, new DateTime());

    RecurrencePattern pattern = new RecurrencePattern().type(RecurrencePatternType.ABSOLUTEYEARLY);
    recurrenceProcessor.mapRelativeAndAbsoluteParametersForESSSchedule(recurrenceRule, pattern);
    Assert.assertEquals(0, pattern.getDaysOfWeek().size());
    Assert.assertNull(pattern.getWeekIndex());
    Assert.assertEquals(List.of(25, 26), pattern.getDaysOfMonth());
  }
}
