package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import java.util.Arrays;
import java.util.List;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class WeeklyRecurrenceProcessorTest {

  @InjectMocks private WeeklyRecurrenceProcessor recurrenceProcessor;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRecurrenceWeekly_1Week() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            WeekOfMonthEnum.FIRST,
            new DateTime());

    Assert.assertEquals("0 0 0 ? * 6,2 *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceWeekly_3Weeks() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            WeekOfMonthEnum.FIRST,
            new DateTime());

    Assert.assertEquals("R/P3W", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceWeekly_5Weeks() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            5,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            null,
            new DateTime());
    Assert.assertEquals("R/P5W", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_1Week() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            WeekOfMonthEnum.FIRST,
            new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.WEEKLY)
            .daysOfWeek(List.of(DayOfWeekType.FRI));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_3Weeks() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            Arrays.asList(MonthsOfYearEnum.JANUARY),
            WeekOfMonthEnum.FIRST,
            new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(3)
            .type(RecurrencePatternType.WEEKLY)
            .daysOfWeek(List.of(DayOfWeekType.FRI, DayOfWeekType.MON));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_5Weeks() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            5,
            RecurTypeEnum.WEEKLY,
            null,
            Arrays.asList(DayOfWeekEnum.FRIDAY, DayOfWeekEnum.MONDAY),
            null,
            null,
            new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(5)
            .type(RecurrencePatternType.WEEKLY)
            .daysOfWeek(List.of(DayOfWeekType.FRI, DayOfWeekType.MON));

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }
}
