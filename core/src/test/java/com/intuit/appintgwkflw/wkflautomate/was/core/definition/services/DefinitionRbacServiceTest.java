package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DefinitionRbacServiceTest {

    @Mock
    private DefinitionServiceHelper definitionServiceHelper;

    @Mock
    private AccessVerifier accessVerifier;

    @Mock
    private DefinitionRbacConfig definitionRbacConfig;

    @InjectMocks
    private DefinitionRbacService definitionRbacService;

    private static final String DEFINITION_ID = "test-definition-id";
    private static final String REALM_ID = "12345";
    private static final String WORKFLOW_TYPE = "approval";
    private static final String TEMPLATE_NAME = "Invoice-Approval";

    @BeforeEach
    void setUp() {
        // Default setup - RBAC enabled and access granted
        when(definitionRbacConfig.isEnabledForOperation(anyString())).thenReturn(true);
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, REALM_ID)).thenReturn(WORKFLOW_TYPE);
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "READ")).thenReturn(true);
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "CREATE")).thenReturn(true);
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "UPDATE")).thenReturn(true);
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "DELETE")).thenReturn(true);
    }

    @Test
    void testVerifyReadAccess_WhenRbacDisabled_ShouldNotCheckAccess() {
        // Given
        when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(false);

        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyReadAccess(DEFINITION_ID, REALM_ID));
        verify(definitionServiceHelper, never()).getWorkflowType(anyString(), anyString());
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }

    @Test
    void testVerifyReadAccess_WhenRbacEnabledAndAccessGranted_ShouldPass() {
        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyReadAccess(DEFINITION_ID, REALM_ID));
        verify(definitionServiceHelper).getWorkflowType(DEFINITION_ID, REALM_ID);
        verify(accessVerifier).verifyUserAccess(WORKFLOW_TYPE, "READ");
    }

    @Test
    void testVerifyReadAccess_WhenRbacEnabledAndAccessDenied_ShouldThrowException() {
        // Given
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "READ")).thenReturn(false);

        // When & Then
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class,
            () -> definitionRbacService.verifyReadAccess(DEFINITION_ID, REALM_ID));
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getError());
    }

    @Test
    void testVerifyReadAccess_WhenWorkflowTypeIsNull_ShouldNotCheckAccess() {
        // Given
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, REALM_ID)).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyReadAccess(DEFINITION_ID, REALM_ID));
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }

    @Test
    void testVerifyCreateAccess_WhenRbacEnabledAndAccessGranted_ShouldPass() {
        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyCreateAccess(WORKFLOW_TYPE));
        verify(accessVerifier).verifyUserAccess(WORKFLOW_TYPE, "CREATE");
    }

    @Test
    void testVerifyCreateAccess_WhenRbacEnabledAndAccessDenied_ShouldThrowException() {
        // Given
        when(accessVerifier.verifyUserAccess(WORKFLOW_TYPE, "CREATE")).thenReturn(false);

        // When & Then
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class,
            () -> definitionRbacService.verifyCreateAccess(WORKFLOW_TYPE));
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getError());
    }

    @Test
    void testVerifyUpdateAccess_WhenRbacEnabledAndAccessGranted_ShouldPass() {
        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyUpdateAccess(DEFINITION_ID, REALM_ID));
        verify(definitionServiceHelper).getWorkflowType(DEFINITION_ID, REALM_ID);
        verify(accessVerifier).verifyUserAccess(WORKFLOW_TYPE, "UPDATE");
    }

    @Test
    void testVerifyDeleteAccess_WhenRbacEnabledAndAccessGranted_ShouldPass() {
        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyDeleteAccess(DEFINITION_ID, REALM_ID));
        verify(definitionServiceHelper).getWorkflowType(DEFINITION_ID, REALM_ID);
        verify(accessVerifier).verifyUserAccess(WORKFLOW_TYPE, "DELETE");
    }

    @Test
    void testGetWorkflowTypeFromTemplate_WithValidTemplateName_ShouldReturnWorkflowType() {
        // When
        String result = definitionRbacService.getWorkflowTypeFromTemplate(TEMPLATE_NAME);

        // Then
        assertEquals("approval", result);
    }

    @Test
    void testGetWorkflowTypeFromTemplate_WithNullTemplateName_ShouldReturnNull() {
        // When
        String result = definitionRbacService.getWorkflowTypeFromTemplate(null);

        // Then
        assertEquals(null, result);
    }

    @Test
    void testVerifyReadAccess_WhenExceptionInGetWorkflowType_ShouldNotCheckAccess() {
        // Given
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, REALM_ID))
            .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> definitionRbacService.verifyReadAccess(DEFINITION_ID, REALM_ID));
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
}
