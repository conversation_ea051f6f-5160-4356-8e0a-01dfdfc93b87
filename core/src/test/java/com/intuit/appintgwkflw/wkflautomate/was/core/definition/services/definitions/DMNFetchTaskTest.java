package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.async.execution.request.State;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DMNFetchTaskTest {

  private String requestKey = "requestKey";
  private String responseKey = "responseKey";

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @Before
  public void setup() {
    bpmnEngineDefinitionServiceRest =
            Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testDmnFetchTask() {
    State inputRequest = new State();
    DmnFetchTask dmnFetchTask =
        new DmnFetchTask(requestKey, responseKey, bpmnEngineDefinitionServiceRest);
    dmnFetchTask.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
        .getDMNXMLDefinition(inputRequest.getValue(requestKey));
  }

  @Test
  public void testDmnFetchTaskWithNullRequestKey() {
    State inputRequest = new State();
    DmnFetchTask dmnFetchTask =
        new DmnFetchTask(null, responseKey, bpmnEngineDefinitionServiceRest);
    dmnFetchTask.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
        .getDMNXMLDefinition(inputRequest.getValue(requestKey));
  }
}
