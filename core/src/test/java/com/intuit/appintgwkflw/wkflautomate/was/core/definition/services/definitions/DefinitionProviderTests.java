package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.ObjectUtils;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionProviderTests {

  @Mock private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  @MockBean private ReadCustomDefinitionHandler readCustomDefinitionHandler;
  @Mock private TemplateLabelsService templateLabelsService;
  @InjectMocks private DefinitionServiceHelper definitionServiceHelper;
  @InjectMocks private BpmnProcessorImpl bpmnProcessor;
  @Mock private TemplateBuilder templateBuilder;

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private TranslationService translationService;
  @Mock private WASContextHandler wasContextHandler;
  private static final String REALM_ID = "12345";
  private static final String LOCAL_ID = "localId";
  private static final String LOCAL_ID_BPMN = "etet2-2434j2-3232fl-33ff";
  private static final String LOCAL_ID_DMN = "h342j3-n13m30-i12kjf-3k0p";
  private static final String bpmnPath = "schema/testData/xmlResponseBPMN";
  private static final String dmnPath = "schema/testData/xmlResponseDMN";
  private static final String PATH_INVOICE_APPROVAL_BPMN =
      "src/test/resources/bpmn/invoiceapproval.bpmn";
  private static final String NAME = "customApproval";
  private static final String DESCRIPTION = "description";
  private static final String OFFER_ID = "offerId";
  private static final String OWNER_ID = "1234";
  private static final String CREATED_BY = "1234";

  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  private DefinitionDetails definitionDetailsBpmn =
      TestHelper.mockDefinitionDetailsWithId(
    		  TemplateDetails.builder().templateName("name").build(), 
    		  authorization, LOCAL_ID_BPMN);
  private List<DefinitionDetails> definitionDetailsDmn =
      Arrays.asList(
          TestHelper.mockDefinitionDetailsWithParentId(
              bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));
  private BpmnResponse bpmnResponse = TestHelper.mockBpmnResponse(definitionDetailsBpmn, bpmnPath);
  private List<DmnResponse> dmnResponseList =
      Arrays.asList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));
  private Template template = TestHelper.mockTemplateEntity();
  public Map<String, String> operatorToSymbolMap = TestHelper.mockCongigurationMap();

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
  }

  @Test
  public void testSetup() {
    Assert.assertEquals("demo", "demo");
  }

  /*@Test(expected = WorkflowGeneralException.class)
  public void testFindByDefinitionFailure() {
      String defId = "INVALID_ID";
    Mockito.when(
            definitionDetailsRepository.findByDefinitionId(defId))
        .thenThrow(WorkflowGeneralException.class);
    definitionServiceHelper.findByDefinitionId(definitionDetails.getDefinitionId());
  }*/

  @Test
  public void testFindByDefinitionSuccess() {
	  
    Mockito.when(
            definitionDetailsRepository.findByDefinitionId(definitionDetailsBpmn.getDefinitionId()))
        .thenReturn(Optional.ofNullable(definitionDetailsBpmn));
    
    Mockito.doNothing().when(wasContextHandler)
    	.addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    
    DefinitionDetails definitionDetails = 
    		definitionServiceHelper.findByDefinitionId(definitionDetailsBpmn.getDefinitionId());
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void ReadOneTemplate() throws IOException {
    File bpmnFile = new File(PATH_INVOICE_APPROVAL_BPMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    TemplateDetails bpmnDetails =
        TemplateDetails.builder()
            .id(UUID.randomUUID().toString())
            .templateName(NAME)
            .modelType(ModelType.BPMN)
            .createdByUserId(Long.parseLong(CREATED_BY))
            .creatorType(CreatorType.SYSTEM)
            .offeringId(OFFER_ID)
            .description(DESCRIPTION)
            .status(Status.ENABLED)
            .recordType(RecordType.INVOICE)
            .ownerId(Long.parseLong(OWNER_ID))
            .version(1)
            .parentId(null)
            .templateData(IOUtils.toByteArray(fisBpmn))
            .build();
    Mockito.when(
            definitionDetailsRepository.findByDefinitionId(definitionDetailsBpmn.getDefinitionId()))
        .thenReturn(Optional.ofNullable(definitionDetailsBpmn));
    Mockito.doNothing().when(wasContextHandler)
		.addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    DefinitionDetails defDetails =
        definitionServiceHelper.findByDefinitionId(definitionDetailsBpmn.getDefinitionId());
    Assert.assertEquals(defDetails, definitionDetailsBpmn);

    Mockito.when(definitionServiceHelper.findByParentId(definitionDetailsBpmn.getDefinitionId()))
        .thenReturn(Optional.ofNullable(definitionDetailsDmn));
    Optional<List<DefinitionDetails>> optionalDmnDefinitionDetails =
        definitionServiceHelper.findByParentId(definitionDetailsBpmn.getDefinitionId());
    Assert.assertEquals(
        optionalDmnDefinitionDetails,
        Optional.ofNullable(Arrays.asList(definitionDetailsDmn.get(0))));

    WASHttpResponse<BpmnResponse> response =
        WASHttpResponse.<BpmnResponse>builder().response(bpmnResponse).isSuccess2xx(true).build();

    Mockito.when(
            bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(
                definitionDetailsBpmn.getDefinitionId()))
        .thenReturn(response);
    BpmnResponse bpmnResponse = definitionServiceHelper.getBPMNXMLDefinition(definitionDetailsBpmn);
    Assert.assertEquals(this.bpmnResponse, bpmnResponse);

    Definition definition = new Definition();
    definition.setName(definitionDetailsBpmn.getDefinitionName());
    definition.setRecordType(definitionDetailsBpmn.getRecordType().getRecordType());

    /**
     * DMN Processing is required only when it has a DMN Reference, which may or may not be the case
     * always.
     */
    List<String> dmnXmlStrings = null;
    if (!ObjectUtils.isEmpty(dmnResponseList)) {
      dmnXmlStrings = dmnResponseList.stream().map(t -> t.getDmnXml()).collect(Collectors.toList());
    }

    /*Mockito.when(
            bpmnProcessor.processBpmn(
                BpmnProcessorUtil.getBpmnModelInstanceFromXml(bpmnResponse.getBpmn20Xml()),
                BpmnProcessorUtil.getDmnModelInstanceListFromXml(dmnXmlStrings),
                TestHelper.getGlobalId(LOCAL_ID_BPMN),
                bpmnDetails,
                true))
        .thenReturn(template);*/
    DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
            BpmnProcessorUtil.getBpmnModelInstanceFromXml(bpmnResponse.getBpmn20Xml()),
            BpmnProcessorUtil.getDmnModelInstanceListFromXml(dmnXmlStrings),
            bpmnDetails);
    bpmnProcessor.processBpmn(
            definitionInstance,
            GlobalId.create(REALM_ID, LOCAL_ID),
            false);
    Assert.assertNotNull(definition);
  }
}
