package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DefinitionServiceTests {

  private static final String REALM_ID = "12345";
  private static final String LOCAL_ID_BPMN = "etet2-2434j2-3232fl-33ff";
  private static final String LOCAL_ID_DMN = "h342j3-n13m30-i12kjf-3k0p";
  private static final String bpmnPath = "schema/testData/xmlResponseBPMN";
  private static final String dmnPath = "schema/testData/xmlResponseDMN";
  private final Definition definition = TestHelper.mockDefinitionEntity();
  @Rule public ExpectedException exceptionRule = ExpectedException.none();
  Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  @Mock private CreateDefinitionHandler createDefinitionHandler;
  @Mock private TemplateDetails bpmnTemplateDetail;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void mockBuildDefinition() {
    DefinitionDetails definitionDetailsBpmn =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, "template-id");
    BpmnResponse bpmnResponse = TestHelper.mockBpmnResponse(definitionDetailsBpmn, bpmnPath);
    bpmnTemplateDetail.setTemplateData(bpmnResponse.getBpmn20Xml().getBytes());

    List<DefinitionDetails> definitionDetailsDmn =
        Arrays.asList(
            TestHelper.mockDefinitionDetailsWithParentId(
                bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));
    List<DmnResponse> dmnResponseList =
        Arrays.asList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));

    DefinitionServiceImpl definitionServiceImpl = mock(DefinitionServiceImpl.class);
    List<String> dmnLists =
        dmnResponseList.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    List<DmnModelInstance> dmnModelInstances =
        BpmnProcessorUtil.getDmnModelInstanceListFromXml(dmnLists);

    try {
      createDefinitionHandler.process(
          new DefinitionInstance(definition, null, null, null), REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
