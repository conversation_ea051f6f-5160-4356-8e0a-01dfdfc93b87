package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import java.util.Collections;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionUnWrapIdsTest {

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";

  private BpmnModelInstance bpmnModelInstance;

  private DmnModelInstance dmnModelInstance;

  private Definition definition;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    definition = TestHelper.mockDefinitionEntity();
    bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
  }

  private static final String REALM_ID = "realmId";
  private final static int FIRST_VALUE = 0;

  /**
   * @param fileName
   * @return : BPMN Model Instance
   */
  private static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        DefinitionUnWrapIdsTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  /**
   * @param fileName
   * @return : DMN Model Instance
   */
  private static DmnModelInstance readDMNFile(String fileName) {
    return Dmn.readModelFromStream(
        DefinitionUnWrapIdsTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  @Rule public ExpectedException exceptionRule = ExpectedException.none();

  @Test
  public void testupdateProcessId() {
    Assert.assertNotNull(bpmnModelInstance);

    String previousName =
        bpmnModelInstance.getModelElementsByType(Process.class).stream()
            .findFirst()
            .get()
            .getName();
    DefinitionUnWrapIds.updateProcessId(bpmnModelInstance, REALM_ID);
    String newName =
        bpmnModelInstance.getModelElementsByType(Process.class).stream()
            .findFirst()
            .get()
            .getName();
    Assert.assertNotNull(bpmnModelInstance);
    Assert.assertEquals(previousName, newName);
  }

  @Test
  public void updateWorkflowStepId() {
    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            templateDetails);
    DefinitionUnWrapIds.updateWorkflowStepId(definitionInstance, REALM_ID);
    Assert.assertNotNull(definitionInstance);
  }

  @Test
  public void testDefinitionUnWrapNull() {
    try {
      DefinitionUnWrapIds.updateTriggerId(null, REALM_ID);
      DefinitionUnWrapIds.updateActionIds(null, REALM_ID);
      DefinitionUnWrapIds.updateWorkflowConditionId(null, REALM_ID);
      DefinitionUnWrapIds.updateActionId(null, REALM_ID);
      DefinitionUnWrapIds.updateRulesMappedActionKeys(null, REALM_ID);
    } catch (Exception e) {
      Assert.fail(
          "methods updateTriggerId,updateActionIds,updateWorkflowConditionId,updateActionId,updateRulesMappedActionKeys should not throw exception");
    }
  }

  @Test
  public void testUpdateWorkflowStep() {
    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionInstance definitionInstance =
            new DefinitionInstance(
                    definition,
                    bpmnModelInstance,
                    Collections.singletonList(dmnModelInstance),
                    templateDetails);
    definitionInstance.getDefinition().getWorkflowSteps().get(FIRST_VALUE).getWorkflowStepCondition().getRuleLines().get(FIRST_VALUE).setMappedActionKeys(null);
    DefinitionUnWrapIds.updateWorkflowStepId(definitionInstance, REALM_ID);
    Assert.assertNotNull(definitionInstance);
  }
}
