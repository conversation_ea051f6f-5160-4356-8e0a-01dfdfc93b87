package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ContextConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MigrationServiceHelperTest {

  @InjectMocks
  private MigrationServiceHelper migrationServiceHelper;
  @Mock
  private DefinitionServiceImpl definitionService;
  @Mock
  TemplateDetailsRepository templateDetailsRepository;
  String templateName="customReminder";
  Template template = new Template();

  @Mock
  private MultiStepConfig multiStepConfig;

  @Mock
  private MultiStepDefinitionTransformer multiStepDefinitionTransformer;

  @Test
  public void testMigrationValidPath() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);
    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenReturn(updatedDefinition);

    Definition resultDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
        authorization, templateId);
    Assert.assertEquals(resultDefinition.getName(), updatedDefinition.getName());

    template.setId(GlobalId.builder().setRealmId("1111").setLocalId(templateId).build());
    template.setName("customTestReminder");
  }

  @Test
  public void testMigrationValidPathPreCanned() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);
    definitionDetails.getTemplateDetails().setTemplateCategory(TemplateCategory.SYSTEM.name());

    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey(null);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenReturn(updatedDefinition);

    Definition resultDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
        authorization, templateId);
    Assert.assertEquals(resultDefinition.getName(), updatedDefinition.getName());
  }

  @Test
  public void testMigrationWithEmptyTrigger() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);

    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());
    updatedDefinition.getWorkflowSteps().get(0).setTrigger(new Trigger());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenReturn(updatedDefinition);

    Definition resultDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
        authorization, templateId);
    Assert.assertEquals(resultDefinition.getName(), updatedDefinition.getName());
    Assert.assertNull(resultDefinition.getWorkflowSteps().get(0).getTrigger());
  }

  @Test
  public void testMigrationWithNullTrigger() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);

    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());
    updatedDefinition.getWorkflowSteps().get(0).setTrigger(null);

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenReturn(updatedDefinition);

    Definition resultDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
        authorization, templateId);
    Assert.assertEquals(resultDefinition.getName(), updatedDefinition.getName());
    Assert.assertNull(resultDefinition.getWorkflowSteps().get(0).getTrigger());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMigrationValidPathUpdateDefThrowError() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);
    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenThrow(new WorkflowGeneralException("Invalid WorkflowId"));

    migrationServiceHelper.migrateDefinition(definitionDetails, authorization, templateId);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMigrationValidPathReadDefThrowError() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);
    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenThrow(new WorkflowGeneralException("Invalid WorkflowId"));

    migrationServiceHelper.migrateDefinition(definitionDetails, authorization, templateId);
  }

  private Authorization getMockAuth() {
    Authorization authorization = new Authorization();
    authorization.putAuthId("A111");
    authorization.putRealm("R111");
    authorization.put(ContextConstants.TOKEN_KEY, "IAM-V1-XXX");
    return authorization;
  }

  private DefinitionDetails prepareTestDefinitionDetails(String templateId) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionKey("test-def-key-1");
    definitionDetails.setDefinitionId("test-def-id-1");
    definitionDetails.setVersion(1);
    definitionDetails.setDefinitionName("mock-unit-test");
    definitionDetails.setOwnerId(111l);
    definitionDetails.setModifiedByUserId(22222l);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customReminder");
    templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.name());
    templateDetails.setId(templateId);
    definitionDetails.setTemplateDetails(templateDetails);

    return definitionDetails;
  }

  private Definition prepareTestDefinition(String templateId) {
    Definition definition = new Definition();
    definition.setId(GlobalId.builder().setRealmId("111").setLocalId("test-1").build());
    definition.setName("test-def-2");
    definition.setConnectorWorkflowId("1111");
    definition.setRecordType(RecordType.INVOICE.getRecordType());
    definition.setTemplate(template);

    setActionInDefinition(definition, "notification");
    setActionInDefinition(definition, "reminder");
    return definition;
  }

  private void setActionInDefinition(Definition definition, String actionKey) {
    WorkflowStep step = new WorkflowStep();
    ActionMapper action = new ActionMapper();
    WorkflowStepCondition condition = new WorkflowStepCondition();
    RuleLine ruleLine = new RuleLine();
    RuleLine.Rule rule = new RuleLine.Rule();
    rule.setConditionalExpression("ON 0");
    rule.setParameterName("txnCreateDays");
    List<RuleLine.Rule> ruleList = new ArrayList<>();
    ruleList.add(rule);
    ruleLine.setRules(ruleList);
    List<RuleLine> ruleLines = new ArrayList<>();
    ruleLines.add(ruleLine);
    condition.setRuleLines(ruleLines);
    action.setActionKey(actionKey);
    step.getActions().add(action);
    step.setWorkflowStepCondition(condition);
    definition.setWorkflowSteps(0, step);
    definition.setStatus(WorkflowStatusEnum.ENABLED);
  }

  @Test
  public void getSingleTemplateId_success(){

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName(templateName);
    templateDetails.setDefinitionType(DefinitionType.SINGLE);
    templateDetails.setVersion(3);
    templateDetails.setId("11");
    TemplateDetails templateDetails1 = new TemplateDetails();
    templateDetails1.setTemplateName(templateName);
    templateDetails1.setDefinitionType(DefinitionType.USER);
    templateDetails1.setVersion(2);
    templateDetails1.setId("12");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails1);
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateName)).thenReturn(Optional.of(templateDetails));
    String templateId = MigrationServiceHelper.getSingleTemplateId(definitionDetails,templateDetailsRepository, templateName);
    Assert.assertEquals("11", templateId);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void getSingleTemplateId_failure(){

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName(templateName);
    templateDetails.setDefinitionType(DefinitionType.SINGLE);
    templateDetails.setVersion(2);
    templateDetails.setId("11");
    TemplateDetails templateDetails1 = new TemplateDetails();
    templateDetails1.setTemplateName(templateName);
    templateDetails1.setDefinitionType(DefinitionType.USER);
    templateDetails1.setVersion(2);
    templateDetails1.setId("12");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails1);
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateName)).thenReturn(Optional.of(templateDetails));
    MigrationServiceHelper.getSingleTemplateId(definitionDetails,templateDetailsRepository, templateName);
  }

  @Test
  public void testMultiConditionMigration() {
    String templateId = UUID.randomUUID().toString();
    DefinitionDetails definitionDetails = prepareTestDefinitionDetails(templateId);
    definitionDetails.getTemplateDetails().setTemplateCategory(TemplateCategory.CUSTOM.name());
    definitionDetails.getTemplateDetails().setTemplateName("customApproval");

    Authorization authorization = getMockAuth();
    Definition definition = prepareTestDefinition(templateId);
    definition.getTemplate().setName("customApproval");
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey(null);
    Definition updatedDefinition = new Definition(definition);
    updatedDefinition.setId(GlobalId.builder().setRealmId("1111").setLocalId("111").build());

    Mockito.when(definitionService.getDefinitionReadOne(any(), any(), Mockito.anyBoolean())).
        thenReturn(updatedDefinition);

    Map<String, WorkflowTemplate> workflowTemplateMap = new HashMap<>();
    WorkflowTemplate workflowTemplate = new WorkflowTemplate();
    workflowTemplate.setSingleStepVersion(1);
    workflowTemplateMap.put("customApproval", workflowTemplate);

    Mockito.when(multiStepConfig.getWorkflowTemplates()).thenReturn(workflowTemplateMap);

    Mockito.when(definitionService.updateDefinition(any(), any())).
        thenReturn(updatedDefinition);

    Definition resultDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
        authorization, templateId);
    Assert.assertEquals(resultDefinition.getName(), updatedDefinition.getName());
  }



}
