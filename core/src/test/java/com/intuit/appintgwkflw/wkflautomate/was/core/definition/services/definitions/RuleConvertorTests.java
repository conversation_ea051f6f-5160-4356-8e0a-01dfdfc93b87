package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.StringUtils;

@RunWith(MockitoJUnitRunner.class)
public class RuleConvertorTests {

  @Rule
  public ExpectedException exceptionRule = ExpectedException.none();

  public static Map<String, String> operatorMap;

  static {
    operatorMap = new HashMap<>();
    operatorMap.put("GT", ">");
    operatorMap.put("GTE", ">=");
    operatorMap.put("LT", "<");
    operatorMap.put("LTE", "<=");
    operatorMap.put("EQ", "==");
    operatorMap.put("CONTAINS", "contains");
    operatorMap.put("NOT_CONTAINS", "!contains");
  }

  private static String prepareExpression(String expression, String parameterName, String type) {

    // For Default Row
    if (StringUtils.isEmpty(expression)) return null;

    String dmnFriendlyExpression = null;

    // Checks for Select All use case
    String checkForSelectAll =
            MessageFormat.format("{0}_{1}", WorkflowConstants.KEYWORD_ALL, parameterName);
    boolean isSelectAll = checkForSelectAll.equalsIgnoreCase(expression);
    /**
     * Assumption : UI should not allow user to enter rule, if none is entered, empty input should
     * come. 1. GT 500 2. GT 500 && LT 1000 3. GT 500 && LT 1000 && EQ 2000
     *
     * <p>For String (Possible Cases) ex, a)CONTAINS 1 b)NOT_CONTAINS 3 c)CONTAINS 1,2,4 d)CONTAINS
     * 1,2,43 && NOT_CONTAINS 3 e)CONTAINS 1,2,43 && NOT_CONTAINS 3,4 f)CONTAINS 1 && NOT_CONTAINS 3
     * g)CONTAINS 1 && NOT_CONTAINS 3,4
     */
    if (!WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(type)) {
      // Tokenizing the expression
      String[] stringToken = expression.split(WorkflowConstants.SPACE);
      dmnFriendlyExpression = MessageFormat.format("{0} ", parameterName);
      dmnFriendlyExpression =
          prepareNonStringExpression(stringToken, parameterName, type, dmnFriendlyExpression);
    } else {
      // in case of string
      if (isSelectAll) {
        // Case for Select All
        return expression;
      }
      if (!expression.contains(WorkflowConstants.AND_MODIFIER)) {

        String[] stringToken = expression.split(WorkflowConstants.SPACE);
        WorkflowVerfiy.verify(
            stringToken.length < 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
        List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
        dmnFriendlyExpression =
            prepareStringExpression(values, parameterName, stringToken[0], dmnFriendlyExpression);
      } else {
        String[] stringToken = expression.split(WorkflowConstants.AND_MODIFIER);
        for (int i = 0; i < stringToken.length; i++) {
          String tempExpression = stringToken[i].trim();
          String[] stringTokens = tempExpression.split(WorkflowConstants.SPACE);
          WorkflowVerfiy.verify(
              stringTokens.length < 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
          List<String> values = Arrays.asList(stringTokens[1].split(WorkflowConstants.COMMA));
          if (WorkflowConstants.CONTAINS_OPERATOR.equalsIgnoreCase(stringTokens[0])) {
            dmnFriendlyExpression =
                prepareStringExpression(
                    values, parameterName, stringTokens[0], dmnFriendlyExpression);
          } else if (WorkflowConstants.NOT_CONTAINS.equalsIgnoreCase(stringTokens[0])) {
            dmnFriendlyExpression =
                prepareStringExpression(
                    values, parameterName, stringTokens[0], dmnFriendlyExpression);
          }
        }
      }
    }

    return dmnFriendlyExpression;
  }

  private static String prepareNonStringExpression(
      String[] stringToken, String parameterName, String type, String dmnFriendlyExpression) {
    Map<String, String> supportedOperator =
        BpmnProcessorUtil.getSupportedOperator(type.toUpperCase());
    for (String token : stringToken) {
      // this check tells if the scanned token is a operator
      if (null != supportedOperator.get(token)) {
        String operatorEquivalent = operatorMap.get(token);
        dmnFriendlyExpression =
            MessageFormat.format("{0}{1} ", dmnFriendlyExpression, operatorEquivalent);
      } else if (WorkflowConstants.AND_MODIFIER.equalsIgnoreCase(token)) {
        dmnFriendlyExpression =
            MessageFormat.format("{0}{1} {2} ", dmnFriendlyExpression, token, parameterName);
      } else {
        // the numeric value
        dmnFriendlyExpression = MessageFormat.format("{0}{1} ", dmnFriendlyExpression, token);
      }
    }
    return dmnFriendlyExpression.trim();
  }

  /**
   * @param values : Token Values
   * @param parameterName : Name of the DMN Column
   * @param containsKeyword : IF it is of type contains and not contains
   * @param dmnFriendlyExpression : Returned Expression
   * @return
   */
  private static String prepareStringExpression(
      List<String> values,
      String parameterName,
      String containsKeyword,
      String dmnFriendlyExpression) {
    for (String token : values) {
      if (WorkflowConstants.CONTAINS_OPERATOR.equalsIgnoreCase(containsKeyword)) {
        /**
         * If True Ex : Parameter name is Customer and value to be contained is 123. So, rule will
         * be framed like this : {0} = Customer, {1} = . ,{2} = equals, {3} = 123 Sample
         * Expression = Customer.equals("123")
         */

        /**
         * If false Ex : Parameter name is Customer and value to be contained is 123. So, rule will
         * be framed like this : {0} = Customer.equals("123")[Existing expression] , {1} = ||, {2}
         * = Customer, {3} = . ,{4} = equals, {5} = 1234 Sample Expression =
         * Customer.equals("123") || Customer.equals("1234")
         */
        dmnFriendlyExpression =
            StringUtils.isEmpty(dmnFriendlyExpression)
                ? MessageFormat.format(
                    "{0}{1}{2}(\"{3}\")",
                    parameterName,
                    WorkflowConstants.DOT_OPERATOR,
                    WorkflowConstants.EQUALS_OPERATOR.toLowerCase(),
                    token)
                : MessageFormat.format(
                    "{0} {1} {2}{3}{4}(\"{5}\")",
                    dmnFriendlyExpression,
                    WorkflowConstants.OR_MODIFIER,
                    parameterName,
                    WorkflowConstants.DOT_OPERATOR,
                    WorkflowConstants.EQUALS_OPERATOR.toLowerCase(),
                    token);

      } else if (WorkflowConstants.NOT_CONTAINS.equalsIgnoreCase(containsKeyword)) {
        /**
         * If True Ex : Parameter name is Customer and value to be contained is 123. So, rule will
         * be framed like this : {0} = !, {1} = Customer, {2} = . ,{3} = contains, {4} = 1234 Sample
         * Expression = !Customer.contains("123")
         */

        /**
         * If False Ex : Parameter name is Customer and value to be contained is 123. So, rule will
         * be framed like this : {0} = !Customer.contains("123")[Existing expression] , {1} = ||
         * ,{2} = !, {3} = Customer, {4} = . ,{5} = contains, {6} = 123 Sample Expression =
         * !Customer.contains("123") || !Customer.contains("1234")
         */
        dmnFriendlyExpression =
            StringUtils.isEmpty(dmnFriendlyExpression)
                ? MessageFormat.format(
                    "{0}{1}{2}{3}(\"{4}\")",
                    WorkflowConstants.NOT_OPERATOR,
                    parameterName,
                    WorkflowConstants.DOT_OPERATOR,
                    WorkflowConstants.EQUALS_OPERATOR.toLowerCase(),
                    token)
                : MessageFormat.format(
                    "{0} {1} {2}{3}{4}{5}(\"{6}\")",
                    dmnFriendlyExpression,
                    WorkflowConstants.AND_MODIFIER,
                    WorkflowConstants.NOT_OPERATOR,
                    parameterName,
                    WorkflowConstants.DOT_OPERATOR,
                    WorkflowConstants.EQUALS_OPERATOR.toLowerCase(),
                    token);
      }
      // Currently It Supports 2 operations, if in future we are supporting any other operation then
      // add it here.
    }
    return dmnFriendlyExpression.trim();
  }

  @Test
  public void checkForAmountSingleRule() {
    String expression = "GT 300";
    String parameterName = "Amount";
    String type = "Double";
    String expectedOutput = "Amount > 300";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test
  public void checkForAmountMultipleRule() {
    String expression = "GT 30 && LTE 1000";
    String parameterName = "Amount";
    String type = "Double";
    String expectedOutput = "Amount > 30 && Amount <= 1000";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test
  public void checkForMultipleCustomerContains() {
    String expression = "CONTAINS 1,2,3";
    String parameterName = "Customer";
    String type = "String";
    String expectedOutput =
        "Customer.equals(\"1\") || Customer.equals(\"2\") || Customer.equals(\"3\")";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test
  public void checkForSingleCustomerContains() {
    String expression = "CONTAINS 1";
    String parameterName = "Customer";
    String type = "String";
    String expectedOutput =
            "Customer.equals(\"1\")";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test
  public void checkForMultipleCustomerNotContains() {
    String expression = "NOT_CONTAINS 1,2,3";
    String parameterName = "Customer";
    String type = "String";
    String expectedOutput =
            "!Customer.equals(\"1\") && !Customer.equals(\"2\") && !Customer.equals(\"3\")";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test
  public void checkForSingleCustomerNotContains() {
    String expression = "NOT_CONTAINS 1";
    String parameterName = "Customer";
    String type = "String";
    String expectedOutput =
            "!Customer.equals(\"1\")";
    String output = prepareExpression(expression, parameterName, type);
    Assert.assertEquals(expectedOutput, output);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void badCustomerStringExceptionCase() {
    String expression = "NOT_CONTAINS";
    String parameterName = "Customer";
    String type = "String";
    prepareExpression(expression, parameterName, type);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_INPUT_FOR_CREATING_RULES.getErrorMessage());
  }

}