package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;
import static org.springframework.util.ObjectUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.WorkflowDefinitionComparator;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.definitions.InputParameter;
import org.joda.time.DateTime;
import org.json.JSONException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SingleDefinitionReadTest {

  private static final String LOCAL_ID_BPMN = "etet2-2434j2-3232fl-33ff";
  private static final String LOCAL_ID_DMN = "h342j3-n13m30-i12kjf-3k0p";
  @InjectMocks
  private SingleDefinitionRead singleDefinitionRead;
  @Mock
  private TemplateDetails bpmnTemplateDetail;
  @Mock
  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private Record record;
  @Mock
  private TemplateDetails templateDetails;
  @Mock
  private Template template;
  @Mock
  private BpmnProcessorImpl bpmnProcessorImpl;

  @Mock
  private MetricLogger metricLogger;

  @Mock
  private WorkflowDefinitionComparator workflowDefinitionComparator;
  private Definition definition;
  private Authorization authorization;
  private DefinitionDetails definitionDetails;

  public static String PLACEHOLDER_VALUES_PATH = "placeholder/placeholder_value_without_trigger.json";

  @Before
  public void setUp() {
    definition = TestHelper.mockDefinitionEntity();
    authorization = new Authorization();
    authorization.putRealm("12345");
    definition.setId(TestHelper.getGlobalId(DefinitionTestConstants.DEF_ID));
    definitionDetails = TestHelper.mockDefinitionDetails(definition, templateDetails, authorization);
    record = TestHelper.mockRecord();
  }

  @Test
  public void testGetBPMNXMLDefinition() {
    DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
    TemplateDetails templateDetails = Mockito.mock(TemplateDetails.class);
    String testData = "Olá, são dados de teste.";
    when(templateDetails.getTemplateData()).thenReturn(testData.getBytes(StandardCharsets.UTF_8));
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    BpmnResponse bpmnResponse = singleDefinitionRead.getBPMNXMLDefinition(definitionDetails);
    Assert.assertNotNull(bpmnResponse);
    Assert.assertNotNull(bpmnResponse.getBpmn20Xml());
    Assert.assertEquals(testData, bpmnResponse.getBpmn20Xml());
  }

  @Test
  public void testSubstitutePlaceHolder() {
    Mockito.when(customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType())).
        thenReturn(record);
    definition.setTemplate(null);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRule.setRecurType(RecurTypeEnum.MONTHLY);
    recurrenceRule.setActive(true);
    recurrenceRule.setStartDate(new DateTime());
    definition.setRecurrence(recurrenceRule);
    Trigger trigger = new Trigger();
    InputParameter parameter = new InputParameter();
    parameter.setParameterName("entityOperation");
    parameter.setFieldValues(0, "create");
    parameter.setFieldValues(1, "update");
    trigger.add("parameters", List.of(parameter));
    trigger.setId(definition.getWorkflowSteps().get(0).getId().setLocalId("waitForTimerToElapse1_invoiceApproval_companyId_uuid"));
    definition.getWorkflowSteps().get(0).setTrigger(trigger);
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    Assert.assertEquals(
        "[[Company Email]]",
        definition
            .getWorkflowSteps()
            .get(0)
            .getActions()
            .get(0)
            .getAction()
            .getParameters(0)
            .getFieldValues(0));
    Assert.assertEquals(
            2,
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().size());
    Assert.assertTrue(
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().contains("create"));
    Assert.assertTrue(
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().contains("update"));
  }


  @Test
  public void testSubstitutePlaceHolderWithoutTriggerParams() {
    Mockito.when(customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType())).
            thenReturn(record);
    definition.setTemplate(null);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRule.setRecurType(RecurTypeEnum.MONTHLY);
    recurrenceRule.setActive(true);
    recurrenceRule.setStartDate(new DateTime());
    definition.setRecurrence(recurrenceRule);
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    Assert.assertEquals(
            "[[Company Email]]",
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getActions()
                    .get(0)
                    .getAction()
                    .getParameters(0)
                    .getFieldValues(0));
    Assert.assertEquals(
            1,
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().size());
    Assert.assertFalse(
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().contains("create"));
    Assert.assertFalse(
            definition
                    .getWorkflowSteps()
                    .get(0)
                    .getTrigger()
                    .getParameters(0)
                    .getFieldValues().contains("update"));
  }

  @Test
  public void testSubstituteRecurrence() {
    definition.setTemplate(null);
    definitionDetails.setPlaceholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH));
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRule.setRecurType(RecurTypeEnum.MONTHLY);
    recurrenceRule.setActive(true);
    recurrenceRule.setStartDate(new DateTime());
    definition.setRecurrence(recurrenceRule);
    singleDefinitionRead.substituteRecurrence(definition, definitionDetails);
    Assert.assertNotNull(definition.getRecurrence());
  }

  @Test
  public void testSubstituteRecurrenceWithNullPlaceholderValues() {
    definition.setTemplate(null);
    definitionDetails.setPlaceholderValue(null);
    singleDefinitionRead.substituteRecurrence(definition, definitionDetails);
    Assert.assertNull(definition.getRecurrence());
  }

  @Test
  public void testSubstitutePlaceHolderWithNoParams() {
    definition.setTemplate(null);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    String mangledParams = definitionDetails.getPlaceholderValue().replace(
        "parameters", "mangledParamters");
    definitionDetails.setPlaceholderValue(mangledParams);
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    System.out.println(definitionDetails.getPlaceholderValue().contains("parameters"));
    Assert.assertEquals(
        "ADMIN_TEST",
        definition
            .getWorkflowSteps()
            .get(0)
            .getActions()
            .get(0)
            .getAction()
            .getParameters(0)
            .getFieldValues(0));
  }

  @Test
  public void testSubstitutePlaceHolderWithMangledActionKey() {
    definition.setTemplate(null);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("mangledActionKey");
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    Assert.assertEquals(
        "ADMIN_TEST",
        definition
            .getWorkflowSteps()
            .get(0)
            .getActions()
            .get(0)
            .getAction()
            .getParameters(0)
            .getFieldValues(0));
  }

  @Test
  public void testSubstitutePlaceHolderWithNonCustomWorkflow() {
    Mockito.when(customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType())).
        thenReturn(record);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    Assert.assertEquals(
        "[[CompanyEmail]]",
        definition
            .getWorkflowSteps()
            .get(0)
            .getActions()
            .get(0)
            .getAction()
            .getParameters(0)
            .getFieldValues(0));
  }

  @Test
  public void testSubstitutePlaceHolderWithActionKeyNotInPlaceHolder() {
    definition.setTemplate(null);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("dummyActionKey");
    singleDefinitionRead.substitutePlaceHolder(definition, definitionDetails);
    Assert.assertEquals(
        "ADMIN_TEST",
        definition
            .getWorkflowSteps()
            .get(0)
            .getActions()
            .get(0)
            .getAction()
            .getParameters(0)
            .getFieldValues(0));
  }

  @Test
  public void testVerifyPlaceholderDefinition() throws IOException {
    String xml = TestHelper.readResourceAsString("schema/testData/xmlResponseBPMN");
    definitionDetails.setDefinitionData(xml.getBytes());
    List<DefinitionDetails> definitionDetailsDmn =
            Collections.singletonList(TestHelper.mockDefinitionDetailsWithParentId(
                            bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));

    String dmnPath = "schema/testData/xmlResponseDMN";
    List<DmnResponse> dmnResponses =
            Collections.singletonList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));
    List<String> dmnXmlStrings = new ArrayList<>();
    if (!isEmpty(dmnResponses)) {
      dmnXmlStrings =
          dmnResponses.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    }

    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    when(workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(Mockito.any(),
        Mockito.any())).thenReturn(true);

    String expectedName = definition.getTemplate().getName();
    singleDefinitionRead.verifyPlaceholderDefinition(definition, definitionDetails, dmnXmlStrings,
        "realm");

    //assert that the object is not modified after the execution of this method
    Assert.assertEquals(expectedName, definition.getTemplate().getName());
  }

  @Test
  public void testVerifyPlaceholderDefinitionWithoutDefinitionData() throws IOException {
    List<String> dmnXmlStrings = new ArrayList<>();
    List<DefinitionDetails> definitionDetailsDmn =
            Collections.singletonList(
                    TestHelper.mockDefinitionDetailsWithParentId(
                            bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));

    String dmnPath = "schema/testData/xmlResponseDMN";
    List<DmnResponse> dmnResponses =
            Collections.singletonList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));
    if (!isEmpty(dmnResponses)) {
      dmnXmlStrings =
          dmnResponses.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    }
    String expectedName = definition.getTemplate().getName();
    singleDefinitionRead.verifyPlaceholderDefinition(definition, definitionDetails, dmnXmlStrings,
        "realm");
    //assert that the object is not modified after the execution of this method
    Assert.assertEquals(expectedName, definition.getTemplate().getName());
  }

  @Test
  public void testVerifyPlaceholderDefinitionTemplateError() throws IOException {

    String xml = TestHelper.readResourceAsString("schema/testData/xmlResponseBPMN");
    definitionDetails.setDefinitionData(xml.getBytes());
    List<DefinitionDetails> definitionDetailsDmn =
            Collections.singletonList(
                    TestHelper.mockDefinitionDetailsWithParentId(
                            bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));

    String dmnPath = "schema/testData/xmlResponseDMN";
    List<DmnResponse> dmnResponses =
            Collections.singletonList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));
    List<String> dmnXmlStrings = new ArrayList<>();
    if (!isEmpty(dmnResponses)) {
      dmnXmlStrings =
          dmnResponses.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    }
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(new IOException());
    String expectedName = definition.getTemplate().getName();
    singleDefinitionRead.verifyPlaceholderDefinition(definition, definitionDetails, dmnXmlStrings,
        "realm");
    //assert that the object is not modified after the execution of this method
    Assert.assertEquals(expectedName, definition.getTemplate().getName());
  }

  @Test
  public void testVerifyPlaceholderDefinitionFailure() throws IOException {
    String xml = TestHelper.readResourceAsString("schema/testData/xmlResponseBPMN");
    definitionDetails.setDefinitionData(xml.getBytes());
    List<DefinitionDetails> definitionDetailsDmn =
            Collections.singletonList(
                    TestHelper.mockDefinitionDetailsWithParentId(
                            bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));

    String dmnPath = "schema/testData/xmlResponseDMN";
    List<DmnResponse> dmnResponses =
            Collections.singletonList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));
    List<String> dmnXmlStrings = new ArrayList<>();
    if (!isEmpty(dmnResponses)) {
      dmnXmlStrings =
          dmnResponses.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    }
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    when(workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(Mockito.any(),
        Mockito.any())).thenThrow(JSONException.class);
    String expectedName = definition.getTemplate().getName();
    singleDefinitionRead.verifyPlaceholderDefinition(definition, definitionDetails, dmnXmlStrings,
        "realm");
    //assert that the object is not modified after the execution of this method
    Assert.assertEquals(expectedName, definition.getTemplate().getName());
  }
}