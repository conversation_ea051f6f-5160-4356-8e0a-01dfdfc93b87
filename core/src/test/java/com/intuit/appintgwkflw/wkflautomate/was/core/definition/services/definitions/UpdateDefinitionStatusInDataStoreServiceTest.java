package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.DefinitionDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;

/** Author: Nitin Gupta Date: 21/01/20 Description: */
@RunWith(MockitoJUnitRunner.class)
public class UpdateDefinitionStatusInDataStoreServiceTest {

  @InjectMocks private UpdateDefinitionStatusInDataStoreService updateStatusDefinitionService;

  @Mock private TemplateDetails bpmnTemplateDetail;

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock private DefinitionDomainEventHandler definitionDomainEventHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Mock private EnabledDefinitionCacheService enabledDefinitionCacheService;

  private static final String REALM_ID = "12345";

  private Definition definition = TestHelper.mockDefinitionEntity();

  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testHappyCaseStatusEnable() {
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetails(definitionDetail.stream().findFirst().get());
    Mockito.when(definitionDetailsRepository.updateInternalStatusAndStatus(Status.ENABLED, null, DEF_ID))
        .thenReturn(2);

    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(DEF_ID, DEF_ID))
            .thenReturn(Optional.of(definitionDetail));
    try {
      updateStatusDefinitionService.updateStatusForEnabled(def);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExceptionStatusEnable() {
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetails(definitionDetail.stream().findFirst().get());
    Mockito.when(definitionDetailsRepository.updateInternalStatusAndStatus(Status.ENABLED, null, DEF_ID))
        .thenReturn(0);

    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(DEF_ID, DEF_ID))
            .thenReturn(Optional.of(definitionDetail));
    try {
      updateStatusDefinitionService.updateStatusForEnabled(def);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertTrue(e.getMessage()
              .contains(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage()));
    }
  }

  @Test
  public void testHappyCaseStatusDisableNoActiveProcess() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetails(definitionDetail.stream().findFirst().get());
    Mockito.when(
            definitionDetailsRepository.updateInternalStatusAndStatus(
                Status.DISABLED, InternalStatus.MARKED_FOR_DISABLE, DEF_ID))
        .thenReturn(1);

    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(DEF_ID, DEF_ID))
            .thenReturn(Optional.of(definitionDetail));

    try {
      updateStatusDefinitionService.updateStatusForDisabled(def);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExceptionStatusDisableNoActiveProcess() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetails(definitionDetail.stream().findFirst().get());
    Mockito.when(
            definitionDetailsRepository.updateInternalStatusAndStatus(
                Status.DISABLED, InternalStatus.MARKED_FOR_DISABLE, DEF_ID))
        .thenReturn(0);

    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(DEF_ID, DEF_ID))
            .thenReturn(Optional.of(definitionDetail));
    try {
      updateStatusDefinitionService.updateStatusForDisabled(def);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertTrue(
          e.getMessage()
              .contains(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage()));
    }
  }

  @Test
  public void testHappyCaseStatusDisableWithActiveProcess() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    ProcessDetails processDetail = new ProcessDetails();
    processDetail.setProcessId("test123");

    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    List<ProcessDetails> processDetails = Collections.singletonList(processDetail);

    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetails(definitionDetail.stream().findFirst().get());
    def.setProcessDetails(processDetails);
    Mockito.when(
            definitionDetailsRepository.updateInternalStatusAndStatus(
                Status.DISABLED, InternalStatus.MARKED_FOR_DISABLE, DEF_ID))
        .thenReturn(1);

    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(DEF_ID, DEF_ID))
            .thenReturn(Optional.of(definitionDetail));

    try {
      updateStatusDefinitionService.updateStatusForDisabled(def);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testUpdateInternalStatusForDowngradeEmptyDefnDetails() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);

    try {
      updateStatusDefinitionService.updateInternalStatusForDowngrade(def, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void updateInternalStatusForDowngrade() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetailsList(definitionDetail);
    Mockito.when(definitionDetailsRepository.updateInternalStatus(Mockito.any(), Mockito.any()))
        .thenReturn(1);

    try {
      updateStatusDefinitionService.updateInternalStatusForDowngrade(def, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void updateInternalStatusForDowngradeFailed() {
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.DISABLED);

    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    def.setDefinitionDetailsList(definitionDetail);
    Mockito.when(definitionDetailsRepository.updateInternalStatus(Mockito.any(), Mockito.any()))
        .thenReturn(0);

    try {
      updateStatusDefinitionService.updateInternalStatusForDowngrade(def, REALM_ID);
      Assert.fail();
    } catch (Exception e) {
    }
  }
}
