package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class UserDefinitionReadTest {

    @InjectMocks
    private UserDefinitionRead userDefinitionRead;

    @Mock
    private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetBPMNXMLDefinition_fromDB() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        when(definitionDetails.getDefinitionData()).thenReturn(new byte[1]);
        BpmnResponse bpmnResponse = userDefinitionRead.getBPMNXMLDefinition(definitionDetails);
        Assert.assertNotNull(bpmnResponse);
        Assert.assertNotNull(bpmnResponse.getBpmn20Xml());
    }

    @Test
    public void testGetBPMNXMLDefinition_fromCamunda() {
        when(bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(any())).thenReturn(
                WASHttpResponse.<BpmnResponse>builder()
                        .response(BpmnResponse.builder().bpmn20Xml("1234").id("id").build()).isSuccess2xx(true)
                        .build());
        BpmnResponse bpmnResponse = userDefinitionRead.getBPMNXMLDefinition( Mockito.mock(DefinitionDetails.class));
        Assert.assertNotNull(bpmnResponse);
        Assert.assertEquals("1234", bpmnResponse.getBpmn20Xml());
        Assert.assertEquals("id", bpmnResponse.getId());
    }

}