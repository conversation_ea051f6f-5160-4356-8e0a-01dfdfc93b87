package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.MonthlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.common.NameValue;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.Definition;

import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.Optional;

import com.intuit.v4.workflows.Template;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.TimerEventDefinitionImpl;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RecurrenceUtilTest {

  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingle.bpmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingleTest.bpmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST_2 =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsSingleTest2.bpmn");
  private static final String CUSTOM_WORKFLOW_STATEMENTS_DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_customScheduledActions2.dmn");

  private BpmnModelInstance bpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private Definition definition;
  private DefinitionInstance definitionInstance;
  private Template template;

  @Mock
  MonthlyRecurrenceProcessor mockMonthlyRecurrenceProcessor;

  @Mock
  FeatureFlagManager featureFlagManager;

  @Before
  public void setup(){
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_DMN_XML, Charset.defaultCharset()));

    MockitoAnnotations.initMocks(this);
    definition = TestHelper.mockProcessedCustomWorkflowDefinition(RecordType.INVOICE.getRecordType());

    definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

    RecurrenceHandler.addHandler(RecurTypeEnum.MONTHLY, mockMonthlyRecurrenceProcessor);
    when(mockMonthlyRecurrenceProcessor.getRecurrence(definition.getRecurrence())).thenReturn("test-cron-exp");
    template = mock(Template.class);
  }

  @Test
  public void testGetRecurrenceProcessVariables(){
    String result = RecurrenceUtil.getRecurrenceProcessVariables("recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertEquals(result, "cronExpression");
    String result1 = RecurrenceUtil.getRecurrenceProcessVariables("recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertEquals(result1, "startDate");
    String result2 = RecurrenceUtil.getRecurrenceProcessVariables("", definitionInstance);
    Assert.assertNull(result2);

  }

  @Test
  public void testGetRecurrenceProcessVariablesFail(){
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST, Charset.defaultCharset()));
    definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    String result = RecurrenceUtil.getRecurrenceProcessVariables("recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertNull(result);
    String result1 = RecurrenceUtil.getRecurrenceProcessVariables("recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertNull(result1);
  }

  @Test
  public void testGetRecurrenceProcessVariablesFailEmptyEvent(){
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML_TEST_2, Charset.defaultCharset()));
    definitionInstance =
        new DefinitionInstance(
            this.definition,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());
    String result1 = RecurrenceUtil.getRecurrenceProcessVariables("recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertNull(result1);

  }

  @Test
  public void testGetFormattedRecurrenceStartDate(){
    String result = RecurrenceUtil.getFormattedRecurrenceStartDate(definition.getRecurrence());
    Assert.assertNotNull(result);
  }

  @Test
  public void testGetCronExpression(){
    String result = RecurrenceUtil.getCronExpression(definition.getRecurrence());
    Assert.assertEquals(result, "test-cron-exp");
  }

  @Test
  public void testGetTimerEvent(){
    Optional<TimerEventDefinitionImpl> timerEventOptional = RecurrenceUtil.getTimerEvent("recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21", definitionInstance);
    Assert.assertNotNull(timerEventOptional.get());
    Assert.assertNotNull(timerEventOptional.get().getTimeCycle());
  }

  @Test
  public void testIsValidRecurrence_NullRecurrence() {
    RecurrenceRule recurrenceRule = null;
    Assert.assertFalse(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }

  @Test
  public void testIsValidRecurrence_EmptyRecurrence() {
    RecurrenceRule recurrenceRule = new RecurrenceRule();
    Assert.assertFalse(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }

  @Test
  public void testIsValidRecurrence_InvalidRecurrence_NullRecurType() {
    RecurrenceRule recurrenceRule = new RecurrenceRule().interval(1);
    Assert.assertFalse(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }

  @Test
  public void testIsValidRecurrence_InvalidRecurrence_NullInterval() {
    RecurrenceRule recurrenceRule = new RecurrenceRule().recurType(RecurTypeEnum.MONTHLY);
    Assert.assertFalse(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }

  @Test
  public void testIsValidRecurrence_InvalidRecurrence_NullStartDate() {
    RecurrenceRule recurrenceRule =
        new RecurrenceRule().recurType(RecurTypeEnum.MONTHLY).interval(1);
    Assert.assertFalse(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }

  @Test
  public void testIsValidRecurrence_ValidRecurrence() {
    RecurrenceRule recurrenceRule =
        new RecurrenceRule().recurType(RecurTypeEnum.MONTHLY).interval(1).startDate(new DateTime());
    Assert.assertTrue(RecurrenceUtil.isValidRecurrence(recurrenceRule));
  }
  
  @Test
  public void testIsInValidRecurrence_endDate() {
    RecurrenceRule recurrenceRule =
        new RecurrenceRule().recurType(RecurTypeEnum.MONTHLY).interval(1).startDate(new DateTime());
    Assert.assertFalse(RecurrenceUtil.isValidRecurrenceWithEndDate(recurrenceRule));
  }
  
  @Test
  public void testIsValidRecurrence_endDate() {
    RecurrenceRule recurrenceRule =
        new RecurrenceRule().recurType(RecurTypeEnum.MONTHLY).interval(1).startDate(new DateTime()).endDate(new DateTime());
    Assert.assertTrue(RecurrenceUtil.isValidRecurrenceWithEndDate(recurrenceRule));
  }

  @Test
  public void testPrepareRecurrence_withValidRecurrence() {
    String data = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\"\n" +
            "             xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
            "             xsi:schemaLocation=\"http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd\"\n" +
            "             targetNamespace=\"http://www.example.org/bpmn\">\n" +
            "  <process id=\"simpleProcess\" name=\"Simple Process\" isExecutable=\"true\">\n" +
            "    <startEvent id=\"startEvent\" name=\"Start\"/>\n" +
            "    <userTask id=\"userTask\" name=\"User Task\"/>\n" +
            "    <endEvent id=\"endEvent\" name=\"End\"/>\n" +
            "    <sequenceFlow id=\"flow1\" sourceRef=\"startEvent\" targetRef=\"userTask\"/>\n" +
            "    <sequenceFlow id=\"flow2\" sourceRef=\"userTask\" targetRef=\"endEvent\"/>\n" +
            "  </process>\n" +
            "</definitions>";
    byte[] definitionData = data.getBytes();
    try (MockedStatic<BpmnProcessorUtil> mockedBpmnProcessorUtil = Mockito.mockStatic(BpmnProcessorUtil.class)) {
      BpmnModelInstance modelInstance = mock(BpmnModelInstance.class);
      FlowElement startEventElement = mock(FlowElement.class);
      CamundaProperty camundaProperty = mock(CamundaProperty.class);
      mockedBpmnProcessorUtil.when(() -> BpmnProcessorUtil.findStartEventElement(modelInstance))
              .thenReturn(startEventElement);
      mockedBpmnProcessorUtil.when(() -> BpmnProcessorUtil.getCamundaProperty(startEventElement, "recurrenceRuleKey"))
              .thenReturn(Optional.of(camundaProperty));
      RecurrenceRule recurrenceRule = RecurrenceUtil.prepareRecurrence(definitionData);
      Assert.assertNotNull(recurrenceRule);
    }
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_EmptyRealmId() {
    when(template.getRecurrence()).thenReturn(null);

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, null);
    verify(featureFlagManager, times(0)).getBoolean(Mockito.anyString(), Mockito.anyString());
    assertNull(result);
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_FeatureFlagDisabled() {
    when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyString())).thenReturn(false);
    when(template.getRecurrence()).thenReturn(null);

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, "1");
    assertNull(result);
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_LabelsNull() {
    when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    when(template.getLabels()).thenReturn(null);
    when(template.getRecurrence()).thenReturn(null);

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, "1");
    assertNull(result);
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_LabelsEmpty() {
    when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    when(template.getLabels()).thenReturn(Collections.emptyList());
    when(template.getRecurrence()).thenReturn(null);

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, "1");
    assertNull(result);
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_LabelsContainReminder() {
    when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    NameValue namevalue = new NameValue();
    namevalue.setName("Reminder");
    namevalue.setValue("Reminder");
    when(template.getLabels()).thenReturn(Collections.singletonList(namevalue));

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, "1");

    assertEquals(RecurTypeEnum.DAILY, result.getRecurType());
    assertEquals(1, result.getInterval());
    assertEquals(EventScheduleConstants.TIMEZONE, result.getTimeZone());

    TimeDuration recurrenceTime = result.getRecurrenceTime();
    assertEquals(8, recurrenceTime.getHours());
    assertEquals(15, recurrenceTime.getMinutes());
    assertEquals(DateTime.now().plusDays(1).toDate().toString(), result.getStartDate().toDate().toString());
  }

  @Test
  public void testGetRecurrenceForPrecannedTemplates_LabelsDoNotContainReminder() {
    when(featureFlagManager.getBoolean(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
    NameValue namevalue = new NameValue();
    namevalue.setName("Approval");
    namevalue.setValue("Approval");
    when(template.getLabels()).thenReturn(Collections.singletonList(namevalue));
    when(template.getRecurrence()).thenReturn(null);

    RecurrenceRule result = RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, "1");
    assertNull(result);
  }
}
