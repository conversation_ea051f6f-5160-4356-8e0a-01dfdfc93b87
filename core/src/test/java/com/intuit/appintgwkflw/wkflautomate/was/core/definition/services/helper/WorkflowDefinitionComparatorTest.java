package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
public class WorkflowDefinitionComparatorTest {

  @InjectMocks
  WorkflowDefinitionComparator workflowDefinitionComparator = new WorkflowDefinitionComparator();

  @Test
  public void testhasDifferenceInWorkflowDefinitionsSuccess(){
    String expectedJson = "{\"test\":\"test\"}";
    String actualJson = "{\"test\":\"test\"}";
    Assert.assertEquals(false, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureField(){
    String expectedJson = "{\"key1\":\"value1\", \"key2\":\"value2SomeSuffix\"}";
    String actualJson = "{\"key1\":\"value2\", \"key2\":\"value2\"}";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureMissingField(){
    String expectedJson = "{\"key1\":\"null\"}";
    String actualJson = "{\"key1\":\"value1\"}";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureMissingNullValue(){
    String expectedJson = "{\"key\":\"null\"}";
    String actualJson = "{\"key\":\"value1\"}";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureNullFieldValue(){
    String expectedJson = "{\"key1\":\"value122\", \"key2\":\"value2SomeSuffix\"}";
    String actualJson = "{\"key\":\"value1\"}";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureFieldActionKey(){
    String expectedJson = "{\"actionKey\":\"null\"}";
    String actualJson = "{\"actionKey\":\"\"}";
    Assert.assertEquals(false, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }

  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureUnExpectedField(){
    String expectedJson = "{\"key1\":\"value1\"}";
    String actualJson = "{\"key1\":\"value1\", \"key2\":\"value2\"}";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }



  @Test
  public void testhasDifferenceInWorkflowDefinitionsFailureException(){
    String expectedJson = "";
    String actualJson = "";
    Assert.assertEquals(true, workflowDefinitionComparator.hasDifferenceInWorkflowDefinitions(expectedJson, actualJson));
  }





}
