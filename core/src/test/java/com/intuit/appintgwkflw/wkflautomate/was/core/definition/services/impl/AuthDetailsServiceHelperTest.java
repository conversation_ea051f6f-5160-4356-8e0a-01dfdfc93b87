package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import com.intuit.v4.Authorization;

import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


public class AuthDetailsServiceHelperTest {

  @InjectMocks private AuthDetailsServiceHelper authDetailsServiceHelper;

  @Mock private AppConnectService appConnectService;

  @Mock private AuthDetailsRepository authDetailsRepository;

  @Mock private AuthHelper authHelper;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void populateAuthDetailsSyncWithFlag() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("ID");
    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
        .thenReturn(getSubscriptionResponse.getId());
    try {
      authDetailsServiceHelper.populateAuthDetailsSync(authorization, Boolean.FALSE);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void populateAuthDetailsSync() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("ID");
    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
        .thenReturn(getSubscriptionResponse.getId());
    try {
      authDetailsServiceHelper.populateAuthDetailsSync(authorization);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void populateAuthDetailsSyncException() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    try {
      authDetailsServiceHelper.populateAuthDetailsSync(authorization, true);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(
          "Input App-Connect Subscription id cannot be null or empty.", e.getMessage());
    }
  }

  @Test
  public void populateAuthDetails() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
            .thenReturn(Optional.ofNullable(null));
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("ID");
    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
            .thenReturn(getSubscriptionResponse.getId());
    try {
      authDetailsServiceHelper.populateAuthDetails(authorization);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void populateAuthDetailsException() {
    Authorization authorization = null;
    try {
      authDetailsServiceHelper.populateAuthDetails(authorization);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(null, e.getMessage());
    }
  }

}
