package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.v4.Authorization;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class AuthDetailsServiceImplTest {

  @InjectMocks
  private AuthDetailsServiceImpl authDetailsServiceImpl;

  @Mock
  private OfflineTicketClient offlineTicketClient;

  @Mock
  private AuthDetailsRepository authDetailsRepository;

  @Mock
  private AuthHelper authHelper;


  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRenewOfflineTicketAndUpdate() {

    final AuthDetails authDetails = new AuthDetails();
    doReturn(Optional.of(authDetails)).when(authDetailsRepository)
        .findAuthByProcessId(anyString());
    final String header = "Intuit_IAM_Authentication intuit_token=345";
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(anyString()))
        .thenReturn(header);
    Assert.assertEquals(header, authDetailsServiceImpl.renewOfflineTicketAndUpdateDB("123"));
  }

  @Test
  public void testAuthDetails() {

    final AuthDetails authDetails = new AuthDetails();
    List<AuthDetails> authDetailsList = Arrays.asList(authDetails);
    final String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    final Authorization authorization = new Authorization(header);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(authDetailsList));
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));

    Assert.assertNotNull(
        authDetailsServiceImpl.getAuthDetailsFromRealmIdSafe(authorization.getRealm()));
  }

  @Test
  public void testAuthDetailsNoFound() {

    final String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    final Authorization authorization = new Authorization(header);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    Assert.assertNull(
        authDetailsServiceImpl.getAuthDetailsFromRealmIdSafe(authorization.getRealm()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetAuthDetailsFromRealmIdError() {

    authDetailsServiceImpl.getAuthDetailsFromRealmId(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetAuthDetailsFromRealmIdDetailsNotFound() {

    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(null));

    authDetailsServiceImpl.getAuthDetailsFromRealmId("1234");
  }

  @Test
  public void testGetAuthDetailsFromRealmId() {

    final AuthDetails authDetails = new AuthDetails();
    List<AuthDetails> authDetailsList = Arrays.asList(authDetails);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.ofNullable(authDetailsList));
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));

    final AuthDetails result = authDetailsServiceImpl.getAuthDetailsFromRealmId("1234");
    Assert.assertNotNull(result);
  }

  @Test
  public void testRenewOfflineTicketAndUpdateWithAuthDetailsNonNull() {

    final AuthDetails authDetails = new AuthDetails();
    doReturn(Optional.of(authDetails)).when(authDetailsRepository)
            .findAuthByDefinitionId(anyString());
    final String header = "Intuit_IAM_Authentication intuit_token=345";
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(anyString()))
            .thenReturn(header);
    Assert.assertEquals(header, authDetailsServiceImpl.renewOfflineTicketAndUpdateDB(authDetails));
  }

  @Test
  public void testRenewOfflineTicketAndUpdateWithAuthDetailsNull() {

    final AuthDetails authDetails = null;
    final String header = "Intuit_IAM_Authentication intuit_token=345";
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(anyString()))
            .thenReturn(header);
    try {
      authDetailsServiceImpl.renewOfflineTicketAndUpdateDB(authDetails);
      Assert.fail();
    } catch(Exception e) {
    }
  }

  @Test
  public void testAuthDetailsNotFoundWithDefinitionId() {
    String definitionId = "";
    final String header = "Intuit_IAM_Authentication intuit_token=345";
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(anyString()))
        .thenReturn(header);
    try {
      authDetailsServiceImpl.renewOfflineTicketAndUpdateDB(definitionId);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.AUTH_DETAILS_NOT_FOUND.getErrorMessage(),e.getMessage());
    }
  }

  @Test
  public void AuthDetailsNotFoundTest() {
    try {
      authDetailsServiceImpl.getAuthDetailsFromRealmId("0");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.AUTH_DETAILS_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void InvalidRealmIdTest() {
    try {
      authDetailsServiceImpl.getAuthDetailsFromRealmId(null);
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.INVALID_REALM_ID.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testRenewTicketUsingOwner(){
    final AuthDetails authDetails = new AuthDetails();
    List<AuthDetails> authDetailsList = Arrays.asList(authDetails);
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.of(authDetailsList));
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));

    final String header = "Intuit_IAM_Authentication intuit_token=1234";
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any()))
        .thenReturn(header);
    Assert.assertEquals(header,
        authDetailsServiceImpl.renewOfflineTicketAndUpdateDB(12345L));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRenewTicketUsingOwnerDetailsNotFound(){
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.any()))
        .thenReturn(Optional.empty());
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.empty());

    authDetailsServiceImpl.renewOfflineTicketAndUpdateDB(12345L);
  }

}