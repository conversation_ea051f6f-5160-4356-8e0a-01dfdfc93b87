package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration.SingleDefInputMigration;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BulkDefinitionMigrationServiceImplTest {

  private final String authHeaderWithAuthId = "Intuit_IAM_Authentication intuit_appid=xyz,intuit_app_secret=ppd,intuit_token=V1-xx,intuit_userid=9130360743195416,intuit_token_type=IAM-Ticket,intuit_realmid=9130360743195436";

  @InjectMocks
  private BulkDefinitionMigrationServiceImpl bulkDefinitionMigrationService;
  @Mock
  private DefinitionServiceHelper definitionServiceHelper;
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private MigrationServiceHelper migrationServiceHelper;

  @Mock
  private OfflineTicketClient offlineTicketClient;
  private SingleDefInputMigration singleDefInputMigration;

  @Before
  public void init() {
    List<String> bpmnDefnIds = new ArrayList<>();
    bpmnDefnIds.add("test-def-id-1");
    singleDefInputMigration = new SingleDefInputMigration();
    singleDefInputMigration.setBpmnDefinitionIds(bpmnDefnIds);
    singleDefInputMigration.setUpdatedTemplateId("test-template-id-1");
    singleDefInputMigration.setOfflineTicketRefresh(false);
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any()))
            .thenReturn(authHeaderWithAuthId);
  }

  @Test
  public void testMigrationValidPath() {
    DefinitionDetails definitionDetails = prepareTestDefinitionData(
        singleDefInputMigration.getBpmnDefinitionIds().get(0));
    Definition definition = prepareTestDefinition();

    Mockito.when(definitionServiceHelper.findByDefinitionId(
        singleDefInputMigration.getBpmnDefinitionIds().get(0))).thenReturn(definitionDetails);
    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.anyString()))
        .thenReturn(definition);

    bulkDefinitionMigrationService.migrateDefinition(singleDefInputMigration);
    Assert.assertTrue(WASContext.isMigrationContext());
  }


  @Test
  public void testMigrationWithRefresh() {
    singleDefInputMigration.setOfflineTicketRefresh(true);
    DefinitionDetails definitionDetails = prepareTestDefinitionData(
        singleDefInputMigration.getBpmnDefinitionIds().get(0));
    Definition definition = prepareTestDefinition();

    Mockito.when(definitionServiceHelper.findByDefinitionId(
        singleDefInputMigration.getBpmnDefinitionIds().get(0))).thenReturn(definitionDetails);
    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.anyString()))
        .thenReturn(definition);

    bulkDefinitionMigrationService.migrateDefinition(singleDefInputMigration);
  }

  @Test
  public void testInvalidDefinitionId() {
    Mockito.when(definitionServiceHelper.findByDefinitionId(
            singleDefInputMigration.getBpmnDefinitionIds().get(0)))
        .thenThrow(new WorkflowGeneralException("Invalid Definition in DB"));
    bulkDefinitionMigrationService.migrateDefinition(singleDefInputMigration);
    Mockito.verify(migrationServiceHelper, Mockito.times(0))
        .migrateDefinition(Mockito.any(), Mockito.any(), Mockito.anyString());
  }


  private Definition prepareTestDefinition() {
    Definition definition = new Definition();
    definition.setId(GlobalId.builder().setRealmId("111").setLocalId("test-1").build());
    definition.setName("test-def-2");
    definition.setConnectorWorkflowId("1111");
    definition.setRecordType(RecordType.INVOICE.getRecordType());
    Template template = new Template();
    template.setId(GlobalId.builder().setRealmId("222").setLocalId("test-2").build());
    template.setName("customTestReminder");
    definition.setTemplate(template);
    return definition;
  }

  private DefinitionDetails prepareTestDefinitionData(String defId) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionKey(defId);
    definitionDetails.setDefinitionId("test-def-id-1");
    definitionDetails.setVersion(1);
    definitionDetails.setDefinitionName("mock-unit-test");
    definitionDetails.setOwnerId(111l);
    definitionDetails.setModifiedByUserId(22222l);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customReminder");
    templateDetails.setId(UUID.randomUUID().toString());
    definitionDetails.setTemplateDetails(templateDetails);

    return definitionDetails;
  }

}
