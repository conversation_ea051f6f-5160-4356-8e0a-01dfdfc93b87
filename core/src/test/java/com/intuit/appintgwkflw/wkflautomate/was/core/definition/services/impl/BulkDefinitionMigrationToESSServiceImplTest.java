package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration.SingleDefInputMigration;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BulkDefinitionMigrationToESSServiceImplTest {


  @InjectMocks
  private BulkDefinitionMigrationToESSServiceImpl bulkDefinitionMigrationToESSService;
  @Mock
  private EventScheduleHelper eventScheduleHelper;

  @Mock
  private DefinitionServiceHelper definitionServiceHelper;

  private final String DEF_ID = "def123";

  @Test
  public void test_migrateDefinition() {
    Mockito.doNothing().when(eventScheduleHelper)
        .migrateWorkflowScheduleActionsToESS(Mockito.any());
    Mockito.when(definitionServiceHelper.findByDefinitionId(DEF_ID)).thenReturn(
        DefinitionDetails.builder().definitionId(DEF_ID).build());
    SingleDefInputMigration singleDefInputMigration = new SingleDefInputMigration();
    singleDefInputMigration.setBpmnDefinitionIds(List.of(DEF_ID));
    bulkDefinitionMigrationToESSService.migrateDefinition(singleDefInputMigration);
    Mockito.verify(definitionServiceHelper, Mockito.times(1))
        .findByDefinitionId(
            DEF_ID);
    Mockito.verify(eventScheduleHelper, Mockito.times(1))
        .migrateWorkflowScheduleActionsToESS(
            Mockito.any());
  }

  @Test
  public void test_migrateDefinition_failed() {
    Mockito.doThrow(new RuntimeException("Test")).when(eventScheduleHelper)
        .migrateWorkflowScheduleActionsToESS(Mockito.any());
    Mockito.when(definitionServiceHelper.findByDefinitionId(DEF_ID)).thenReturn(
        DefinitionDetails.builder().definitionId(DEF_ID).build());
    SingleDefInputMigration singleDefInputMigration = new SingleDefInputMigration();
    singleDefInputMigration.setBpmnDefinitionIds(List.of(DEF_ID));
    try {
      bulkDefinitionMigrationToESSService.migrateDefinition(singleDefInputMigration);
      Assert.fail("Exception should be thrown");
    } catch (Exception e) {
      Mockito.verify(definitionServiceHelper, Mockito.times(1))
          .findByDefinitionId(
              DEF_ID);
      Mockito.verify(eventScheduleHelper, Mockito.times(1))
          .migrateWorkflowScheduleActionsToESS(
              Mockito.any());
    }


  }

}
