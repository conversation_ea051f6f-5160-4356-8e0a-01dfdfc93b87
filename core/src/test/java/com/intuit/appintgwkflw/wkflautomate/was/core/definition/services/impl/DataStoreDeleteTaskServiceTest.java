package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TransactionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.Task;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class DataStoreDeleteTaskServiceTest {


  @InjectMocks
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock
  private ActivityProgressDetailsRepository activityProgressDetailsRepository;
  @Mock
  private TransactionDetailsRepository txnDetailsRepository;
  @Mock
  private SchedulerDetailsRepository schedulerDetailsRepository;

  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  private Definition definition = TestHelper.mockDefinitionEntity();
  @Mock
  private TemplateDetails bpmnTemplateDetail;
  private Authorization authorization = TestHelper.mockAuthorization("123");
  private static Map<String, String> schema = new HashMap<>();
  private static final String parametersSchema =
      TestHelper.readResourceAsString("schema/testData/parameters.json");

  static {
    schema.put(WorkFlowVariables.PARAMETERS_KEY.getName(), parametersSchema);
    schema.put(WorkflowConstants.INTUIT_REALMID, "123");
  }

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testSuccessWithDefinitions() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);
    Mockito.doReturn(definitionDetailsList)
        .when(definitionDetailsRepository)
        .findByOwnerId(workerActionRequest.getOwnerId());

    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsIn(Mockito.anyList()))
        .thenReturn(null);

    List<Task> dataStoreDeleteDefinitionAndProcessTaskList = new ArrayList<>();

    dataStoreDeleteDefinitionAndProcessTaskList.add(
        new DataStoreDeleteDefinitionAndProcessTask(
            definitionDetailsRepository,
            definitionDetail.getDefinitionId(),
            dataStoreDeleteTaskService));
    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);
    Assert.assertTrue(
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithoutDefinitions() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);
    Assert.assertTrue(
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithoutProcess() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    List<DefinitionDetails> definitions = Arrays.asList(DefinitionDetails.builder().build());
    Mockito.when(
        processDetailsRepository.findByOwnerIdAndDefinitionDetailsIn(
            Long.valueOf("123"), definitions))
        .thenReturn(null);
    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);
    Assert.assertTrue(
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithProcesses() {
    List<ProcessDetails> processDetailList =
        Arrays.asList(ProcessDetails.builder().ownerId(123L).build());
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    List<DefinitionDetails> definitions = Arrays.asList(DefinitionDetails.builder().build());
    Mockito.when(
        processDetailsRepository.findByOwnerIdAndDefinitionDetailsIn(
            Long.valueOf("123"), definitions))
        .thenReturn(processDetailList);
    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);
    Assert.assertTrue(
        resp.get(
            workerActionRequest.getActivityId()
                + WorkflowConstants.UNDERSCORE
                + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithActivityDeletion() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);

    Mockito.doReturn(definitionDetailsList)
        .when(definitionDetailsRepository)
        .findByOwnerId(workerActionRequest.getOwnerId());

    ProcessDetails processDetails = ProcessDetails.builder().processId("wkid").build();
    List<String> processDetailsList = Arrays.asList(new String[]{"wkid"});

    Mockito.when(processDetailsRepository.findByDefinitionDetailsIn(Mockito.anyList()))
        .thenReturn(processDetailsList);

    List<ActivityProgressDetails> activityDetailsList = new ArrayList<>();

    ActivityProgressDetails activityDetails1 = ActivityProgressDetails.builder()
        .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").build())
        .processDetails(processDetails)
        .id("act1").build();
    activityDetailsList.add(activityDetails1);

    ActivityProgressDetails activityDetails2 = ActivityProgressDetails.builder()
        .txnDetails(null)
        .processDetails(processDetails)
        .id("act2").build();
    activityDetailsList.add(activityDetails2);

    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsIn(Mockito.anyList()))
        .thenReturn(activityDetailsList);

    Mockito.when(activityProgressDetailsRepository.deleteByIdIn(Mockito.anySet()))
        .thenReturn(1);

    Mockito.when(txnDetailsRepository.deleteByIdIn(Mockito.anySet()))
        .thenReturn(1);

    List<Task> dataStoreDeleteDefinitionAndProcessTaskList = new ArrayList<>();

    dataStoreDeleteDefinitionAndProcessTaskList.add(
        new DataStoreDeleteDefinitionAndProcessTask(
            definitionDetailsRepository,
            definitionDetail.getDefinitionId(),
            dataStoreDeleteTaskService));
    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);

    Mockito.verify(activityProgressDetailsRepository, Mockito.times(1))
        .findByProcessDetailsIn(Mockito.anyList());

    Mockito.verify(activityProgressDetailsRepository, Mockito.times(1))
        .deleteByIdIn(Mockito.anySet());

    Mockito.verify(txnDetailsRepository, Mockito.times(1))
        .deleteByIdIn(Mockito.anySet());

    Assert.assertTrue(
        resp.get(workerActionRequest.getActivityId()
            + WorkflowConstants.UNDERSCORE
            + WorkFlowVariables.RESPONSE.getName())
            .equals("true"));
  }

  @Test
  public void testSuccessWithDefinitionActivityDetailsDeletion() {
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .ownerId(123L)
            .processDefinitionId("dId")
            .processInstanceId("iId")
            .inputVariables(schema)
            .handlerId("hId")
            .build();

    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);

    DefinitionDetails definitionDetailNew =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetail);
    definitionDetailsList.add(definitionDetailNew);

    Mockito.doReturn(definitionDetailsList)
        .when(definitionDetailsRepository)
        .findByOwnerId(workerActionRequest.getOwnerId());

    List<String> processDetailsList =
        new ArrayList<>(Collections.singletonList("parentId"));

    Mockito.when(processDetailsRepository.findByDefinitionDetailsIn(Mockito.anyList()))
        .thenReturn(processDetailsList);

    List<ProcessDetails> childProcessDetailsList =
        Collections.singletonList(
            ProcessDetails.builder().processId("childProcessId").build()
        );

    Mockito.when(processDetailsRepository.findByParentIdIn(Mockito.anyList()))
        .thenReturn(Optional.of(childProcessDetailsList));

    ArgumentCaptor<List<String>> processIdArgumentCaptor = ArgumentCaptor.forClass(List.class);
    Mockito.when(
        processDetailsRepository.deleteByProcessIdIn(processIdArgumentCaptor.capture())
    ).thenReturn(2L);

    Map<String, Object> resp = dataStoreDeleteTaskService.execute(workerActionRequest);

    Assert.assertEquals(
        2L,
        processIdArgumentCaptor.getValue().size()
    );

    Mockito.verify(definitionActivityDetailsRepository, Mockito.times(1))
        .deleteByDefinitionDetailsIn(Mockito.anyList());

    Assert.assertEquals("true", resp.get(workerActionRequest.getActivityId()
        + WorkflowConstants.UNDERSCORE
        + WorkFlowVariables.RESPONSE.getName()));
  }


}
