package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation.DELETE_ALL;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DeleteAllWorkflowsCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.v4.Authorization;
import com.intuit.v4.WorkflowsBulkAction.WorkflowBulkActionDetails;
import com.intuit.v4.qbshared.bulk.definitions.BulkActionEnum;
import com.intuit.v4.qbshared.bulk.definitions.BulkActionModeEnum;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class WorkflowBulkActionServiceImplTest {

  @InjectMocks private WorkflowBulkActionServiceImpl workflowBulkActionServiceImpl;

  @Mock private WASContextHandler contextHandler;

  @Mock
  private WorkflowBulkActionDetails workflowBulkActionDetails;
  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private DeleteAllWorkflowsCommand command;
  @Mock private RunTimeService runTimeService;
  @Mock private AuthDetailsServiceHelper authDetailsServiceHelper;
  @Mock private EventScheduleHelper eventScheduleHelper;
  @Mock private UserContributionService userContributionService;
  private static final String HEADER = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testDowngradeNoDefnNoAuth() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(
            definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm())))
        .thenReturn(null);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm()))
        .thenReturn(null);
    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    deletionDetails.setName(DELETE_ALL.name());
    try {
      Assert.assertNotNull(workflowBulkActionServiceImpl.deleteAll(deletionDetails));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDowngradeWithDefn() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    DefinitionDetails def = new DefinitionDetails();
    def.setRecordType(RecordType.SUBSCRIPTION);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(def);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(
            definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm())))
        .thenReturn(list);

    AuthDetails authDetails = AuthDetails.builder().subscriptionId("subId").build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm()))
        .thenReturn(authDetails);
    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    deletionDetails.setName(DELETE_ALL.name());
    try {
      Assert.assertNotNull(workflowBulkActionServiceImpl.deleteAll(deletionDetails));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testSuccess() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    DefinitionDetails def = new DefinitionDetails();
    def.setRecordType(RecordType.SUBSCRIPTION);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(def);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(
            definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm())))
        .thenReturn(list);

    AuthDetails authDetails = AuthDetails.builder().subscriptionId("subId").build();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm()))
        .thenReturn(authDetails);
    Mockito.when(
            runTimeService.processTriggerMessageV2(
                TriggerUtil.prepareTriggerPayloadForDowngrade("123",workflowBulkActionDetails)))
        .thenReturn(WorkflowGenericResponse.builder().build());

    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    deletionDetails.setName(DELETE_ALL.name());

    Assert.assertNotNull(workflowBulkActionServiceImpl.deleteAll(deletionDetails));
  }

  @Test
  public void testFailureAuthDetailsCase() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286";
    Authorization authorization = new Authorization(header);
    DefinitionDetails def = new DefinitionDetails();
    def.setRecordType(RecordType.SUBSCRIPTION);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(def);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(
            definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm())))
        .thenReturn(list);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm()))
        .thenReturn(null);

    Mockito.doThrow(RuntimeException.class)
        .when(authDetailsServiceHelper)
        .populateAuthDetailsSync(authorization);

    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    deletionDetails.setName(DELETE_ALL.name());

    Assert.assertNotNull(workflowBulkActionServiceImpl.deleteAll(deletionDetails));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDowngradeWithoutWkflwBulkActionDetails() {
    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    workflowBulkActionServiceImpl.deleteAll(deletionDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDowngradeWithoutWkflwBulkActionDetailsNoMode() {
    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setName(DELETE_ALL.name());
    workflowBulkActionServiceImpl.deleteAll(deletionDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDowngradeWithoutWkflwBulkActionDetailsNoName() {
    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    workflowBulkActionServiceImpl.deleteAll(deletionDetails);
  }

  @Test
  public void testDeleteAllException() {
    Authorization authorization = new Authorization(HEADER);
    DefinitionDetails def = new DefinitionDetails();
    def.setRecordType(RecordType.SUBSCRIPTION);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(def);
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(HEADER);
    Mockito.when(
            definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm())))
            .thenReturn(list);

    Mockito.when(authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm()))
            .thenReturn(null);

    Mockito.doThrow(WorkflowGeneralException.class)
            .when(authDetailsServiceHelper)
            .populateAuthDetailsSync(Mockito.any());

    WorkflowBulkActionDetails deletionDetails = new WorkflowBulkActionDetails();
    deletionDetails.setAction(BulkActionEnum.DELETE);
    deletionDetails.setMode(BulkActionModeEnum.ASYNC);
    deletionDetails.setName(DELETE_ALL.name());

    Assert.assertNotNull(workflowBulkActionServiceImpl.deleteAll(deletionDetails));
  }
}
