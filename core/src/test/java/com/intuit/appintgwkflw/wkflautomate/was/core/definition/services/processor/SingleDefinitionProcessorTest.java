package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelperTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DefinitionDeploymentUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse.DeployedDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.async.execution.request.State;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Import(SingleDefinitionProcessor.class)
public class SingleDefinitionProcessorTest {

  @Autowired
  SingleDefinitionProcessor singleDefinitionProcessor;

  @MockBean
  private TemplateDetailsRepository templateDetailsRepository;

  @MockBean
  private TriggerDetailsRepository triggerDetailsRepository;

  @MockBean
  private ActivityDetailsRepository activityDetailsRepository;

  @MockBean
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

  @MockBean
  private WorkflowTaskConfig workflowParserConfig;

  @MockBean
  private TemplateDomainEventHandler templateDomainEventHandler;

  @MockBean
  private DefinitionServiceHelper definitionServiceHelper;

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";

  private TemplateDetails bpmnTemplateDetail = new TemplateDetails();

  private List<TemplateDetails> bpmnTemplateDetails = new ArrayList<>();

  private TemplateDetails dmnTemplateDetail = new TemplateDetails();

  private List<TemplateDetails> dmnTemplateDetails = new ArrayList<>();
  @Before
  public void setUp() {
    bpmnTemplateDetail.setModelType(ModelType.BPMN);
    bpmnTemplateDetails.add(bpmnTemplateDetail);
    dmnTemplateDetail.setModelType(ModelType.DMN);
    dmnTemplateDetails.add(dmnTemplateDetail);
  }

  @SuppressWarnings("serial")
  @Test
  public void testExecute() {
    State inputRequest = new State();
    BpmnModelInstance bpmnModelInstance =
        DefinitionServiceHelperTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
    bpmnTemplateDetail.setTemplateName(WorkflowConstants.CUSTOM_APPROVAL_TEMPLATE);
    DmnModelInstance dmnModelInstance =
        DefinitionServiceHelperTest.readDMNFile(INVOICE_APPROVAL_DMN);

    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    // Mock camunda response
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    DeployedDefinition deployedDefinition = new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("id");
    deployDefinitionResponse.setId("id");

    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", deployedDefinition);
          }
        });

    WASHttpResponse<Object> resp = WASHttpResponse.<Object>builder().status(HttpStatus.OK)
        .response(deployDefinitionResponse).isSuccess2xx(true).build();
    Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(Mockito.any())).thenReturn(resp);

    inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetail);
    inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetails);
    inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, bpmnTemplateDetails);

    Mockito.when(workflowParserConfig.isEnable()).thenReturn(true);

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);

    inputRequest.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, CamundaServiceHelper
	        .createDeployDefinitionRequest(model.getBpmnModelInstance(),
	            model.getDmnModelInstanceList(),
	            DefinitionDeploymentUtil.getDeploymentName(bpmnTemplateDetail)));

    inputRequest =
        singleDefinitionProcessor.executeAction(inputRequest, model);

    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY));
    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY));
  }

  @Test
  public void testNoRollback() {
    State inputRequest = new State();
    // No exception expected as no failure in state
    singleDefinitionProcessor.checkAndRollBack(inputRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRollback() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE, true);
    inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE,
        WorkflowError.TEMPLATE_SAVE_EXCEPTION);
    inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION, new Exception());

    singleDefinitionProcessor.checkAndRollBack(inputRequest);
  }

  @SuppressWarnings("serial")
  @Test
  public void testExecuteMethodInInterface() {
    State inputRequest = new State();
    BpmnModelInstance bpmnModelInstance =
        DefinitionServiceHelperTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance =
        DefinitionServiceHelperTest.readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);
    // Mock camunda response
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    DeployedDefinition deployedDefinition = new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("id");
    deployDefinitionResponse.setId("id");

    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", deployedDefinition);
          }
        });
    inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetail);
    inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetails);
    inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, bpmnTemplateDetails);
    WASHttpResponse<Object> resp = WASHttpResponse.<Object>builder().status(HttpStatus.OK)
        .response(deployDefinitionResponse).isSuccess2xx(true).build();
    Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(Mockito.any())).thenReturn(resp);
    Mockito.when(workflowParserConfig.isEnable()).thenReturn(true);
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    singleDefinitionProcessor.execute(inputRequest, model);

    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY));
    Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRollback_saveTaskFail() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE, true);
    inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION, new Exception());
    inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE,
        WorkflowError.TASK_DETAIL_SAVE_EXCEPTION);
    singleDefinitionProcessor.checkAndRollBack(inputRequest);
  }


  @SuppressWarnings("serial")
  @Test(expected = RuntimeException.class)
  public void testExecute_saveTaskFail() {
    State inputRequest = new State();
    BpmnModelInstance bpmnModelInstance =
        DefinitionServiceHelperTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance =
        DefinitionServiceHelperTest.readDMNFile(INVOICE_APPROVAL_DMN);

    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    // Mock camunda response
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    DeployedDefinition deployedDefinition = new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("id");
    deployDefinitionResponse.setId("id");

    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<String, DeployDefinitionResponse.DeployedDefinition>() {
          {
            put("123", deployedDefinition);
          }
        });

    WASHttpResponse<Object> resp = WASHttpResponse.<Object>builder().status(HttpStatus.OK)
        .response(deployDefinitionResponse).isSuccess2xx(true).build();
    Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(Mockito.any())).thenReturn(resp);

    Map<TaskType, List<Task>> tasksMap = new HashMap<>();
    tasksMap.put(TaskType.HUMAN_TASK, new ArrayList<>());
    tasksMap.get(TaskType.HUMAN_TASK).add(new Task());

    inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetail);
    inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnModelInstanceList);
    inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, bpmnTemplateDetails);
    inputRequest.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, tasksMap);

    Mockito.when(workflowParserConfig.isEnable()).thenReturn(true);
    Mockito.doThrow(new RuntimeException("test")).when(activityDetailsRepository)
        .saveAll(Mockito.anyList());

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);

    singleDefinitionProcessor.executeAction(inputRequest, model);
  }

}
