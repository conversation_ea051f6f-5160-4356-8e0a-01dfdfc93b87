package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelperTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Import(UserDefinitionProcessor.class)
public class UserDefinitionProcessorTest {

    @Autowired
    UserDefinitionProcessor userDefinitionProcessor;

    @MockBean
    private TemplateDetailsRepository templateDetailsRepository;

    @MockBean
    private TriggerDetailsRepository triggerDetailsRepository;

    @MockBean
    private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

    @MockBean
    private TemplateDomainEventHandler templateDomainEventHandler;

    @MockBean
    private ActivityDetailsRepository activityDetailsRepository;

    @MockBean
    private WorkflowTaskConfig workflowTaskConfig;

    private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";
    private TemplateDetails bpmnTemplateDetail = new TemplateDetails();

    private List<TemplateDetails> bpmnTemplateDetails = new ArrayList<>();

    private TemplateDetails dmnTemplateDetail = new TemplateDetails();

    private List<TemplateDetails> dmnTemplateDetails = new ArrayList<>();
    @Before
    public void setUp() {
        bpmnTemplateDetail.setModelType(ModelType.BPMN);
        bpmnTemplateDetails.add(bpmnTemplateDetail);
        dmnTemplateDetail.setModelType(ModelType.DMN);
        dmnTemplateDetails.add(dmnTemplateDetail);
    }


    @SuppressWarnings("serial")
	@Test
    public void testExecute() {
        State inputRequest = new State();
        BpmnModelInstance bpmnModelInstance =
                DefinitionServiceHelperTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        DmnModelInstance dmnModelInstance =
                DefinitionServiceHelperTest.readDMNFile(INVOICE_APPROVAL_DMN);

        List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
        dmnModelInstanceList.add(dmnModelInstance);

        // Mock camunda response
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        DeployDefinitionResponse.DeployedDefinition deployedDefinition = new DeployDefinitionResponse.DeployedDefinition();
        deployedDefinition.setId("id");
        deployDefinitionResponse.setId("id");

        deployDefinitionResponse.setDeployedProcessDefinitions(
                new HashMap<>() {
                    {
                        put("123", deployedDefinition);
                    }
                });

        WASHttpResponse<Object> resp = WASHttpResponse.<Object>builder().status(HttpStatus.OK)
                .response(deployDefinitionResponse).isSuccess2xx(true).build();
        Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(Mockito.any())).thenReturn(resp);

        inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetail);
        inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetails);
        inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, bpmnTemplateDetails);

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

        TemplateModelInstance model =
                new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
        inputRequest =
                userDefinitionProcessor.executeAction(inputRequest, model);

        Assert.assertNotNull(inputRequest.getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY));

    }

    @SuppressWarnings("serial")
    @Test(expected = RuntimeException.class)
    public void testExecute_SaveTaskFail() {
        State inputRequest = new State();
        BpmnModelInstance bpmnModelInstance =
            DefinitionServiceHelperTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        DmnModelInstance dmnModelInstance =
            DefinitionServiceHelperTest.readDMNFile(INVOICE_APPROVAL_DMN);

        List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
        dmnModelInstanceList.add(dmnModelInstance);

        // Mock camunda response
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        DeployDefinitionResponse.DeployedDefinition deployedDefinition = new DeployDefinitionResponse.DeployedDefinition();
        deployedDefinition.setId("id");
        deployDefinitionResponse.setId("id");

        deployDefinitionResponse.setDeployedProcessDefinitions(
            new HashMap<String, DeployDefinitionResponse.DeployedDefinition>() {
                {
                    put("123", deployedDefinition);
                }
            });

        WASHttpResponse<Object> resp = WASHttpResponse.<Object>builder().status(HttpStatus.OK)
            .response(deployDefinitionResponse).isSuccess2xx(true).build();
        Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(Mockito.any()))
            .thenReturn(resp);

        Map<TaskType, List<Task>> tasksMap = new HashMap<>();
        tasksMap.put(TaskType.HUMAN_TASK, new ArrayList<>());
        tasksMap.get(TaskType.HUMAN_TASK).add(new Task());

        inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetail);
        inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetails);
        inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, bpmnTemplateDetails);
        inputRequest.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, tasksMap);

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);
        Mockito.doThrow(new RuntimeException("test")).when(activityDetailsRepository)
            .saveAll(Mockito.anyList());

        TemplateModelInstance model =
            new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);

        userDefinitionProcessor.executeAction(inputRequest, model);
    }


    @Test
    public void testNoRollback() {
        State inputRequest = new State();
        // No exception expected as no failure in state
        userDefinitionProcessor.checkAndRollBack(inputRequest);
    }

    @Test
    public void testGetType() {
        // No exception expected as no failure in state
        DefinitionType deftype = userDefinitionProcessor.getType();
        Assert.assertEquals(DefinitionType.USER, deftype);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testRollbackTemplate() {
        State inputRequest = new State();
        inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE, true);
        inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE,
            WorkflowError.TEMPLATE_SAVE_EXCEPTION);
        inputRequest.addValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION, new Exception());

        userDefinitionProcessor.checkAndRollBack(inputRequest);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testRollbackTaskDetails() {
        State inputRequest = new State();
        inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE, true);
        inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION, new Exception());
        inputRequest.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE,
            WorkflowError.TASK_DETAIL_SAVE_EXCEPTION);

        userDefinitionProcessor.checkAndRollBack(inputRequest);
    }

}

