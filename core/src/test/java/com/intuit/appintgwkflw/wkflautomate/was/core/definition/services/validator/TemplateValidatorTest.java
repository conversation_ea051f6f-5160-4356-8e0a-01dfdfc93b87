package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;

import static com.google.common.collect.MoreCollectors.onlyElement;

@Import(TemplateValidator.class)
@RunWith(SpringRunner.class)
public class TemplateValidatorTest {

  private TemplateValidator templateValidator;

  @MockBean private WorkflowGlobalConfiguration workflowGlobalConfiguration;

  @MockBean private ReadCustomDefinitionHandler readCustomDefinitionHandler;

  @MockBean private HistoryConfig historyConfig;

  private static final String INVOICE_APPROVAL_BPMN =
      "src/test/resources/bpmn/invoiceapproval.bpmn";
  private static final String TEST_CONSUMER_BPMN_FAIL =
      "src/test/resources/bpmn/testConsumer2.bpmn";
  private static final String TEST_CONSUMER_BPMN =
      "src/test/resources/bpmn/testConsumer.bpmn";
  private static final String CONTENT_TYPE = "application/octet-stream";


  @Before
  public void setUp() {
    historyConfig = new HistoryConfig();
    historyConfig.setTtl(30);
    templateValidator = new TemplateValidator(historyConfig);
  }


  @Test
  public void testValidateHistoryTTLSuccess() throws Exception {
    try {
      File bpmnFile = new File(TEST_CONSUMER_BPMN);
      FileInputStream fisBpmn = new FileInputStream(bpmnFile);
      TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
      mockTemplateMetadata.setValidateHistoryTTL(true);
      MultipartFile[] templates = {
          new MockMultipartFile(
              bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
      };

      final MultipartFile bpmnfile =
          Arrays.stream(templates)
              .filter(
                  template -> template.getOriginalFilename().endsWith(WorkflowConstants.BPMN_TYPE))
              .collect(onlyElement());

      final BpmnModelInstance bpmnModelInstance =
          Bpmn.readModelFromStream(bpmnfile.getInputStream());

      templateValidator.validateTTL(bpmnModelInstance, mockTemplateMetadata);
    }catch(Exception e){
      Assert.fail("Error in skipping Validation");
    }

  }


  //testing a bpmn with no ttl value
  @Test(expected = WorkflowGeneralException.class)
  public void testValidateHistoryTTLFailNoTTLFound() throws Exception {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
    mockTemplateMetadata.setValidateHistoryTTL(true);
    MultipartFile[] templates = {
        new MockMultipartFile(
            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
    };

    final MultipartFile bpmnfile =
        Arrays.stream(templates)
            .filter(
                template -> template.getOriginalFilename().endsWith(WorkflowConstants.BPMN_TYPE))
            .collect(onlyElement());
    final BpmnModelInstance bpmnModelInstance =
        Bpmn.readModelFromStream(bpmnfile.getInputStream());

    templateValidator.validateTTL(bpmnModelInstance, mockTemplateMetadata);
  }


  //testing a bpmn with ttl value greater than threshold
  @Test(expected = WorkflowGeneralException.class)
  public void testValidateHistoryTTLFailTTLGreaterThanThreshold() throws Exception {
    File bpmnFile = new File(TEST_CONSUMER_BPMN_FAIL);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
    mockTemplateMetadata.setValidateHistoryTTL(true);
    MultipartFile[] templates = {
        new MockMultipartFile(
            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
    };

    final MultipartFile bpmnfile =
        Arrays.stream(templates)
            .filter(
                template -> template.getOriginalFilename().endsWith(WorkflowConstants.BPMN_TYPE))
            .collect(onlyElement());
    final BpmnModelInstance bpmnModelInstance =
        Bpmn.readModelFromStream(bpmnfile.getInputStream());

    templateValidator.validateTTL(bpmnModelInstance, mockTemplateMetadata);
  }

  @Test
  public void testValidateHistoryTTLWithoutMetaData(){
    try {
      File bpmnFile = new File(TEST_CONSUMER_BPMN);
      FileInputStream fisBpmn = new FileInputStream(bpmnFile);
      TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
      mockTemplateMetadata.setValidateHistoryTTL(false);
      MultipartFile[] templates = {
          new MockMultipartFile(
              bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
      };

      final MultipartFile bpmnfile =
          Arrays.stream(templates)
              .filter(
                  template -> template.getOriginalFilename().endsWith(WorkflowConstants.BPMN_TYPE))
              .collect(onlyElement());

      final BpmnModelInstance bpmnModelInstance =
          Bpmn.readModelFromStream(bpmnfile.getInputStream());

      templateValidator.validate(bpmnModelInstance, mockTemplateMetadata);
    }catch(Exception e){
      Assert.fail("Error in skipping Validation");
    }



  }


}
