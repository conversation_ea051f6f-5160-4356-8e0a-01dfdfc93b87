package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.Definition;
import com.intuit.system.interfaces.BaseEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RunWith(MockitoJUnitRunner.class)
public class DefinitionDomainEventHandlerTest {
    @InjectMocks
    private DefinitionDomainEventHandler definitionDomainEventHandler;
    @Mock
    private DomainEventConfig domainEventTopiConfig;
    @Mock
    private WASContextHandler contextHandler;
    @Mock
    private DomainEventRepository domainEventRepository;
    private Map<String, String> topic = new HashMap<>();
    private TopicDetails topicDetails = new TopicDetails();
    private Map<DomainEventName, TopicDetails> map = new HashMap<>();

    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(definitionDomainEventHandler, "contextHandler", contextHandler);
        ReflectionTestUtils.setField(
                definitionDomainEventHandler, "domainEventTopiConfig", domainEventTopiConfig);
        ReflectionTestUtils.setField(
                definitionDomainEventHandler, "domainEventRepository", domainEventRepository);

        topic.put("definition", "qal.foundation.workflow.workflowautomation.definition.v1");
        topicDetails.setTopic("qal.foundation.workflow.workflowautomation.definition.v1");
        topicDetails.setEnabled(true);
        map.put(DomainEventName.DEFINITION, topicDetails);
    }

    @Test
    public void testGetDomainEventName() {
        Assert.assertEquals(DomainEventName.DEFINITION, definitionDomainEventHandler.getName());
    }

    @Test
    public void shouldTransformDefinitionDomainEvent() {
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response = definitionDomainEventHandler
                .transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNotNull(response);
        boolean isTypeCorrect = response.getPayload() instanceof String;
        Definition definition = ObjectConverter.fromJson(response.getPayload(),Definition.class);
        Assert.assertTrue(isTypeCorrect);
        Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", definition.getId());
        Assert.assertEquals("1234", definition.getOwnerId());
    }

    @Test
    public void shouldTransformDefinitionDomainEventWithAdditionalFieldsInCamelCase() {
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response = definitionDomainEventHandler
                .transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNotNull(response);
        boolean isTypeCorrect = response.getPayload() instanceof String;
        Definition definition = ObjectConverter.fromJson(response.getPayload(),Definition.class);
        Assert.assertTrue(isTypeCorrect);
        Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", definition.getId());
        Assert.assertEquals("1234", definition.getOwnerId());
        Assert.assertEquals(response.getHeaders().getEntitychangeaction(), response.getHeaders().getEntityChangeAction());
        Assert.assertEquals(response.getHeaders().getEntityversion(), response.getHeaders().getEntityVersion());
        Assert.assertEquals(0, response.getHeaders().getEntityversion().intValue());
        Assert.assertEquals(EntityChangeAction.CREATE, response.getHeaders().getEntityChangeAction());
        Assert.assertEquals(Definition.URN, response.getHeaders().getIntuitEntityType());
        Assert.assertEquals(Definition.SCHEMA_VERSION, response.getHeaders().getSchemaVersion());

    }

    @Test
    public void shouldTransformDefinitionDomainEventWithOutAdditionalFieldsInCamelCase() {
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response = definitionDomainEventHandler
                .transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNotNull(response);
        boolean isTypeCorrect = response.getPayload() instanceof String;
        Definition definition = ObjectConverter.fromJson(response.getPayload(),Definition.class);
        Assert.assertTrue(isTypeCorrect);
        Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", definition.getId());
        Assert.assertEquals("1234", definition.getOwnerId());
        Assert.assertNotNull(response.getHeaders().getEntityChangeAction());
        Assert.assertNotNull(response.getHeaders().getEntityVersion());
        Assert.assertEquals(0, response.getHeaders().getEntityversion().intValue());
        Assert.assertEquals(EntityChangeAction.CREATE, response.getHeaders().getEntitychangeaction());
        Assert.assertNotNull(response.getHeaders().getIntuitEntityType());
        Assert.assertNotNull(response.getHeaders().getSchemaVersion());
    }

  @Test
  public void testPublish() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Definition definition = prepareDefinitionPayload(buildDefinitionDetails());

    String obj = ObjectConverter.toJson(definition);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);

    DomainEvent<BaseEntity> response =
        definitionDomainEventHandler.publish(buildDomainEntityRequest(EntityChangeAction.CREATE));

    Assert.assertNotNull(response);
  }

    @Test
    public void testPublishAll() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        List<DomainEntityRequest<DefinitionDetails>> domainEvents = new ArrayList<>();
        domainEvents.add(buildDomainEntityRequest(EntityChangeAction.DELETE));

        List<DomainEvent> response = definitionDomainEventHandler.publishAll(domainEvents);

        Assert.assertNotNull(response);
    }

  @Test
  public void testPublishAllWithTidIsNull() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn(null);

    List<DomainEntityRequest<DefinitionDetails>> domainEvents = new ArrayList<>();
    domainEvents.add(buildDomainEntityRequest(EntityChangeAction.DELETE));

    List<DomainEvent> response = definitionDomainEventHandler.publishAll(domainEvents);

    Assert.assertNotNull(response);
  }

    @Test
    public void testNotPublishSuccess() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);

        DomainEvent<BaseEntity> response = definitionDomainEventHandler
                .publish(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNull(response);
    }

    @Test
    public void testNotPublishAllSuccess() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);

        List<DomainEntityRequest<DefinitionDetails>> domainEvents = new ArrayList<>();
        domainEvents.add(buildDomainEntityRequest(EntityChangeAction.DELETE));

        List<DomainEvent> response = definitionDomainEventHandler.publishAll(domainEvents);

        Assert.assertNull(response);
    }

    @Test
    public void testNotPublishSuccessForUpdate() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);

        DomainEvent<BaseEntity> response = definitionDomainEventHandler
                .publish(buildDomainEntityRequest((EntityChangeAction.UPDATE)));

        Assert.assertNull(response);
    }

    @Test
    public void testNotPublishSuccessEndProcessWithEntityHeadersNull() {

        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");

        DomainEvent<BaseEntity> response = definitionDomainEventHandler.publish(buildDomainEntityRequest(EntityChangeAction.CREATE));
        Assert.assertNull(response);
    }

    private DomainEntityRequest buildDomainEntityRequest(EntityChangeAction entityChangeAction) {

        return DomainEntityRequest.builder()
                .eventHeaderEntity(null)
                .entityChangeAction(entityChangeAction)
                .request(buildDefinitionDetails())
                .build();
    }

    private Definition prepareDefinitionPayload(DefinitionDetails definitionDetails) {
        Definition definition = new Definition();
        definition.setOwnerId(String.valueOf(definitionDetails.getOwnerId()));
        definition.setOfferingId(definitionDetails.getOfferingId().toString());
        definition.setId(definitionDetails.getDefinitionId());
        definition.setInternalStatus(null);
        return definition;
    }

    private DefinitionDetails buildDefinitionDetails() {
        return DefinitionDetails.builder()
                .definitionId("353b0efa-cd76-4fbf-9b5b-dd68813b08da")
                .definitionName("dName")
                .ownerId(1234L)
                .offeringId(12L)
                .status(Status.ENABLED)
                .recordType(RecordType.INVOICE)
                .modelType(ModelType.BPMN)
                .lookupKeys("{\"envelopeId\":\"1234\", \"customId\":\"1234\"}")
                .internalStatus(InternalStatus.MARKED_FOR_DISABLE)
                .templateDetails(TemplateDetails.builder().offeringId("Intuit.appintgwkflw.wkflautomate.qbowasapiclient").build())
                .build();
    }

}