package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEventErrorDetails;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.foundation.workflow.workflowautomation.types.ErrorDetail;
import com.intuit.system.interfaces.BaseEntity;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ProcessDomainEventHandlerTest {

  @InjectMocks ProcessDomainEventHandler processDomainEventHandler;
  @Mock DomainEventConfig domainEventTopiConfig;
  @Mock WASContextHandler contextHandler;
  @Mock DomainEventRepository domainEventRepository;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    ReflectionTestUtils.setField(processDomainEventHandler, "contextHandler", contextHandler);
    ReflectionTestUtils.setField(
        processDomainEventHandler, "domainEventTopiConfig", domainEventTopiConfig);
    ReflectionTestUtils.setField(
        processDomainEventHandler, "domainEventRepository", domainEventRepository);
  }

  @Test
  public void testGetDomainEventName() {
    Assert.assertEquals(DomainEventName.PROCESS, processDomainEventHandler.getName());
  }

  @Test
  public void testTransform() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123L)
            .entityVersion(0)
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(processDetails)
            .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Process process = ObjectConverter.fromJson(response.getPayload(),Process.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("dId", process.getDefinitionDetail().getDefinitionId());
    Assert.assertEquals("dName", process.getDefinitionDetail().getDefinitionName());
    Assert.assertEquals(ProcessStatus.ACTIVE.getProcessStatus(), process.getStatus());
    Assert.assertNull(process.getInternalStatus());
    Assert.assertEquals("123", process.getOwnerId());
    Assert.assertEquals(
        RecordType.INVOICE.getRecordType(), process.getTriggerEntity().getEntityType());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());
  }

  @Test
  public void testTransformWithAdditionalFieldsInCamelCase() {

    ProcessDetails processDetails =
            ProcessDetails.builder()
                    .processId("pId")
                    .recordId("recordId")
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(
                            DefinitionDetails.builder()
                                    .definitionId("dId")
                                    .definitionName("dName")
                                    .recordType(RecordType.INVOICE)
                                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                                    .build())
                    .ownerId(123L)
                    .entityVersion(0)
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build();
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
            DomainEntityRequest.builder()
                    .eventHeaderEntity(EventHeaderEntity.builder().build())
                    .entityChangeAction(EntityChangeAction.CREATE)
                    .request(processDetails)
                    .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Process process = ObjectConverter.fromJson(response.getPayload(),Process.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("dId", process.getDefinitionDetail().getDefinitionId());
    Assert.assertEquals("dName", process.getDefinitionDetail().getDefinitionName());
    Assert.assertEquals(ProcessStatus.ACTIVE.getProcessStatus(), process.getStatus());
    Assert.assertNull(process.getInternalStatus());
    Assert.assertEquals("123", process.getOwnerId());
    Assert.assertEquals(
            RecordType.INVOICE.getRecordType(), process.getTriggerEntity().getEntityType());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());

    // Retrieve the headers from the response
    DomainEventHeaders headers = response.getHeaders();

    // Assert each field in the DomainEventHeaders
    Assert.assertEquals("123", headers.getAccountId());
    Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntityChangeAction());
    Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntitychangeaction());
    Assert.assertEquals("pId", headers.getEntityId());
    Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.Process", headers.getEntityType());
    Assert.assertEquals(0, headers.getEntityVersion().intValue());
    Assert.assertEquals(0, headers.getEntityversion().intValue());
    Assert.assertEquals("tid", headers.getIntuitTid());
    Assert.assertNull(headers.getTrace());
    Assert.assertEquals("oId", headers.getOfferingId());
    Assert.assertEquals(Process.SCHEMA_VERSION, headers.getSchemaVersion());
    Assert.assertEquals(Process.URN, headers.getIntuitEntityType());

  }

  @Test
  public void testTransformWithouthAdditionalFieldsInCamelCase() {

    ProcessDetails processDetails =
            ProcessDetails.builder()
                    .processId("pId")
                    .recordId("recordId")
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(
                            DefinitionDetails.builder()
                                    .definitionId("dId")
                                    .definitionName("dName")
                                    .recordType(RecordType.INVOICE)
                                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                                    .build())
                    .ownerId(123L)
                    .entityVersion(0)
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build();
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
            DomainEntityRequest.builder()
                    .eventHeaderEntity(EventHeaderEntity.builder().build())
                    .entityChangeAction(EntityChangeAction.CREATE)
                    .request(processDetails)
                    .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Process process = ObjectConverter.fromJson(response.getPayload(),Process.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("dId", process.getDefinitionDetail().getDefinitionId());
    Assert.assertEquals("dName", process.getDefinitionDetail().getDefinitionName());
    Assert.assertEquals(ProcessStatus.ACTIVE.getProcessStatus(), process.getStatus());
    Assert.assertNull(process.getInternalStatus());
    Assert.assertEquals("123", process.getOwnerId());
    Assert.assertEquals(
            RecordType.INVOICE.getRecordType(), process.getTriggerEntity().getEntityType());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());

    // Retrieve the headers from the response
    DomainEventHeaders headers = response.getHeaders();

    // Assert each field in the DomainEventHeaders
    Assert.assertEquals("123", headers.getAccountId());
    Assert.assertNotNull(headers.getEntityChangeAction());
    Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntitychangeaction());
    Assert.assertEquals("pId", headers.getEntityId());
    Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.Process", headers.getEntityType());
    Assert.assertNotNull(headers.getEntityVersion());
    Assert.assertEquals(0, headers.getEntityversion().intValue());
    Assert.assertEquals("tid", headers.getIntuitTid());
    Assert.assertNull(headers.getTrace());
    Assert.assertEquals("oId", headers.getOfferingId());
    Assert.assertNotNull(headers.getSchemaVersion());
    Assert.assertNotNull(headers.getIntuitEntityType());
  }

  @Test
  public void testPublish() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .entityVersion(0)
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Process process = prepareProcessPayload(processDetails);
    String obj = ObjectConverter.toJson(process);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(processDetails)
            .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNotNull(response);
  }

  @Test
  public void testNotPublishSuccess() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build())
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);

    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(processDetails)
            .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNull(response);
  }

  @Test
  public void testNotPublishSuccessForUpdate() {
    ProcessDetails processDetails =
            ProcessDetails.builder()
                    .processId("pId")
                    .recordId("recordId")
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(
                            DefinitionDetails.builder()
                                    .definitionId("dId")
                                    .definitionName("dName")
                                    .recordType(RecordType.INVOICE)
                                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                                    .createdDate(Timestamp.from(Instant.now()))
                                    .modifiedDate(Timestamp.from(Instant.now()))
                                    .build())
                    .ownerId(123L)
                    .entityVersion(0)
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);

    DomainEntityRequest domainEntityRequest =
            DomainEntityRequest.builder()
                    .eventHeaderEntity(EventHeaderEntity.builder().build())
                    .entityChangeAction(EntityChangeAction.UPDATE)
                    .request(processDetails)
                    .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNull(response);
  }

  @Test
  public void testNotPublishSuccessEndProcessWithEntityHeadersNull() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build())
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .entityVersion(0)
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(null)
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(processDetails)
            .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNull(response);
  }

  @Test
  public void testNotPublishSuccessEndProcessWithOwnerIdIsNull() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .entityVersion(0)
                    .build())
            .ownerId(123L)
            .entityVersion(0)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(null)
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(processDetails)
            .build();
    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNull(response);
  }

  @Test
  public void testPublishWithErrorDetail() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .entityVersion(0)
                    .build())
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .entityVersion(0)
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Process process = prepareProcessPayload(processDetails);
    ErrorDetail erroDetail = new ErrorDetail();
    erroDetail.setErrorMessage("Error Message");
    erroDetail.setActivityId("aId");
    process.setErrorDetail(erroDetail);
    String obj = ObjectConverter.toJson(process);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);

    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(processDetails)
            .domainEventErrorDetails(
                DomainEventErrorDetails.builder()
                    .errorMessage("Error Message")
                    .activityId("aId")
                    .build())
            .build();

    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);

    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Process processResponse = ObjectConverter.fromJson(response.getPayload(),Process.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertNotNull(processResponse.getErrorDetail());
    Assert.assertEquals("aId", erroDetail.getActivityId());
    Assert.assertEquals("Error Message", erroDetail.getErrorMessage());
  }

  @Test
  public void testPublishWithErrorDetailTidIsNull() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .createdDate(Timestamp.from(Instant.now()))
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .entityVersion(0)
                    .build())
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .entityVersion(0)
            .build();

    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.process.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn(null);
    Process process = prepareProcessPayload(processDetails);
    ErrorDetail erroDetail = new ErrorDetail();
    erroDetail.setErrorMessage("Error Message");
    erroDetail.setActivityId("aId");
    process.setErrorDetail(erroDetail);
    String obj = ObjectConverter.toJson(process);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);

    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(processDetails)
            .domainEventErrorDetails(
                DomainEventErrorDetails.builder()
                    .errorMessage("Error Message")
                    .activityId("aId")
                    .build())
            .build();

    DomainEvent<BaseEntity> response = processDomainEventHandler.publish(domainEntityRequest);

    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Process processResponse = ObjectConverter.fromJson(response.getPayload(), Process.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertNotNull(processResponse.getErrorDetail());
    Assert.assertEquals("aId", erroDetail.getActivityId());
    Assert.assertEquals("Error Message", erroDetail.getErrorMessage());
  }

  private Process prepareProcessPayload(ProcessDetails processDetails) {
    Process process = new Process();
    process.setOwnerId(String.valueOf(processDetails.getOwnerId()));
    process.setId(processDetails.getProcessId());
    process.setInternalStatus(null);
    process.setStatus(processDetails.getProcessStatus().getProcessStatus());
    process.setErrorDetail(null);
    return process;
  }
}
