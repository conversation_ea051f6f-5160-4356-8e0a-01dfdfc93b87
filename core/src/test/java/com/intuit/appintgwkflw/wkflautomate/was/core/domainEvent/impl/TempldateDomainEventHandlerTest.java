package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.*;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.Template;
import com.intuit.system.interfaces.BaseEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RunWith(MockitoJUnitRunner.class)
public class TempldateDomainEventHandlerTest {
    @InjectMocks
    private TemplateDomainEventHandler templateDomainEventHandler;
    @Mock
    private DomainEventConfig domainEventTopiConfig;
    @Mock
    private WASContextHandler contextHandler;
    @Mock
    private DomainEventRepository domainEventRepository;
    private Map<String, String> topic = new HashMap<>();
    private TopicDetails topicDetails = new TopicDetails();
    private Map<DomainEventName, TopicDetails> map = new HashMap<>();

    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(templateDomainEventHandler, "contextHandler", contextHandler);
        ReflectionTestUtils.setField(
                templateDomainEventHandler, "domainEventTopiConfig", domainEventTopiConfig);
        ReflectionTestUtils.setField(
                templateDomainEventHandler, "domainEventRepository", domainEventRepository);

        topic.put("template", "qal.foundation.workflow.workflowautomation.template.v1");
        topicDetails.setTopic("qal.foundation.workflow.workflowautomation.template.v1");
        topicDetails.setEnabled(true);
        map.put(DomainEventName.TEMPLATE, topicDetails);
    }

    @Test
    public void testGetDomainEventName() {
        Assert.assertEquals(DomainEventName.TEMPLATE, templateDomainEventHandler.getName());
    }

  @Test
  public void shouldTransformTemplateDomainEvent() {
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    DomainEvent<BaseEntity> response =
        templateDomainEventHandler.transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    Template template = ObjectConverter.fromJson(response.getPayload(), Template.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("id", template.getId());
    Assert.assertEquals("1234", template.getOwnerId());
  }

    @Test
    public void shouldTransformTemplateDomainEventWithCamelCase() {

        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response =
                templateDomainEventHandler.transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNotNull(response);
        boolean isTypeCorrect = response.getPayload() instanceof String;
        Template template = ObjectConverter.fromJson(response.getPayload(), Template.class);
        Assert.assertTrue(isTypeCorrect);
        Assert.assertEquals("id", template.getId());
        Assert.assertEquals("1234", template.getOwnerId());

        // Retrieve the headers from the response
        DomainEventHeaders headers = response.getHeaders();

        // Assert each field in the DomainEventHeaders
        Assert.assertEquals("1234", headers.getAccountId());
        Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntityChangeAction());
        Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntitychangeaction());
        Assert.assertEquals("id", headers.getEntityId());
        Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.Template", headers.getEntityType());
        Assert.assertEquals(0, headers.getEntityVersion().intValue());
        Assert.assertEquals(0, headers.getEntityversion().intValue());
        Assert.assertEquals("tid", headers.getIntuitTid());
        Assert.assertNull(headers.getTrace());
        Assert.assertEquals("ttlive", headers.getOfferingId());
        Assert.assertEquals(Template.SCHEMA_VERSION, headers.getSchemaVersion());
        Assert.assertEquals(Template.URN, headers.getIntuitEntityType());

    }

    @Test
    public void shouldTransformTemplateDomainEventWithoutCamelCase() {
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response =
                templateDomainEventHandler.transform(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNotNull(response);
        boolean isTypeCorrect = response.getPayload() instanceof String;
        Template template = ObjectConverter.fromJson(response.getPayload(), Template.class);
        Assert.assertTrue(isTypeCorrect);
        Assert.assertEquals("id", template.getId());
        Assert.assertEquals("1234", template.getOwnerId());

        // Retrieve the headers from the response
        DomainEventHeaders headers = response.getHeaders();

        // Assert each field in the DomainEventHeaders
        Assert.assertEquals("1234", headers.getAccountId());
        Assert.assertNotNull(headers.getEntityChangeAction());
        Assert.assertEquals(EntityChangeAction.CREATE, headers.getEntitychangeaction());
        Assert.assertEquals("id", headers.getEntityId());
        Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.Template", headers.getEntityType());
        Assert.assertNotNull(headers.getEntityVersion());
        Assert.assertEquals(0, headers.getEntityversion().intValue());
        Assert.assertEquals("tid", headers.getIntuitTid());
        Assert.assertNull(headers.getTrace());
        Assert.assertEquals("ttlive", headers.getOfferingId());
        Assert.assertNotNull(headers.getSchemaVersion());
        Assert.assertNotNull(headers.getIntuitEntityType());
    }

  @Test
  public void testPublish() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Template template = prepareTemplatePayload(buildTemplateDetails());
    String obj = ObjectConverter.toJson(template);

    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);

    DomainEvent<BaseEntity> response =
        templateDomainEventHandler.publish(buildDomainEntityRequest(EntityChangeAction.CREATE));

    Assert.assertNotNull(response);
  }

    @Test
    public void testPublishAll() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        List<DomainEntityRequest<TemplateDetails>> domainEvents = new ArrayList<>();
        domainEvents.add(buildDomainEntityRequest(EntityChangeAction.UPDATE));

        List<DomainEvent> response = templateDomainEventHandler.publishAll(domainEvents);

        Assert.assertNotNull(response);
    }

    @Test
    public void testNotPublishSuccess() {
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);

        DomainEvent<BaseEntity> response = templateDomainEventHandler
                .publish(buildDomainEntityRequest(EntityChangeAction.CREATE));

        Assert.assertNull(response);
    }

    @Test
    public void testNotPublishSuccessEndProcessWithEntityHeadersNull() {

        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
        Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
        Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        DomainEvent<BaseEntity> response = templateDomainEventHandler.publish(buildDomainEntityRequest(EntityChangeAction.CREATE));
        Assert.assertNull(response);
    }

    private DomainEntityRequest buildDomainEntityRequest(EntityChangeAction entityChangeAction) {

        return DomainEntityRequest.builder()
                .eventHeaderEntity(null)
                .entityChangeAction(entityChangeAction)
                .request(buildTemplateDetails())
                .build();
    }

    private Template prepareTemplatePayload(TemplateDetails templateDetails) {
        Template template = new Template();
        template.setOwnerId(String.valueOf(templateDetails.getOwnerId()));
        template.setOfferingId(templateDetails.getOfferingId().toString());
        return template;
    }

    private TemplateDetails buildTemplateDetails() {
        return TemplateDetails.builder()
                .id("id")
                .offeringId("ttlive")
                .status(Status.ENABLED)
                .modelType(ModelType.BPMN)
                .creatorType(CreatorType.SYSTEM)
                .recordType(RecordType.INVOICE)
                .ownerId(1234L)
                .build();
    }
}