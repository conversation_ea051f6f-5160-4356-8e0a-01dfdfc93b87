package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType.INVOICE;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.DefaultPlaceholderUserVariableExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.PlaceholderUserVariableExtractors;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.AdditionalDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEventErrorDetails;
import com.intuit.foundation.workflow.workflowautomation.ActivityRuntime;
import com.intuit.foundation.workflow.workflowautomation.Definition;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.foundation.workflow.workflowautomation.Template;
import com.intuit.foundation.workflow.workflowautomation.types.Attribute;
import com.intuit.foundation.workflow.workflowautomation.types.LookUpKey;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * <AUTHOR>
 *     <p>Process Instance Mapper Tests
 */
public class DomainEventMapperTest {

  @MockBean WASContextHandler wasContextHandler;

  @Mock
  private DefaultPlaceholderUserVariableExtractor defaultPlaceholderUserVariableExtractor;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    Mockito.when(defaultPlaceholderUserVariableExtractor.getName()).thenReturn(PlaceholderUserVariableExtractorType.DEFAULT);
    PlaceholderUserVariableExtractors.addHandler(defaultPlaceholderUserVariableExtractor.getName(), defaultPlaceholderUserVariableExtractor);
  }

  private DomainEntityRequest getDomainEntityRequest(ProcessDetails processDetails){
    return DomainEntityRequest.<ProcessDetails>builder()
            .request(processDetails)
            .entityChangeAction(EntityChangeAction.CREATE)
            .eventHeaderEntity(null)
            .build();
  }

  @Test
  public void testProcess() {

    Process process = DomainEventMapper.mapEntityToProcessDomainEvent(getDomainEntityRequest(buildProcessDetails()));
    Assert.assertNotNull(process);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());
    Assert.assertEquals("123", process.getOwnerId());
  }

  @Test
  public void testDomainEventWithParentProcessDetails() {
    ProcessDetails processDetails = buildProcessDetails();
    processDetails.setParentId("11111");
    DomainEntityRequest domainEntityRequest = getDomainEntityRequest(processDetails);
    Map<String, Object> variables = new HashMap<>();
    variables.put("DocNumber", "1001");
    domainEntityRequest.setAdditionalDetails(AdditionalDetails.builder().variables(variables).build());
    Process process = DomainEventMapper.mapEntityToProcessDomainEvent(domainEntityRequest);
    Assert.assertNotNull(process);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());
    Assert.assertEquals("123", process.getOwnerId());
    Assert.assertEquals(process.getCorrelationId(), "11111");
  }

  @Test
  public void testProcess_WithEmptyRecordType() {
    ProcessDetails processDetails = buildProcessDetails();
    processDetails.getDefinitionDetails().setRecordType(null);
    Process process = DomainEventMapper.mapEntityToProcessDomainEvent(getDomainEntityRequest(processDetails));
    Assert.assertNotNull(process);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());
    Assert.assertEquals("123", process.getOwnerId());
  }

  @Test
  public void testProcess_ODA() {
    ProcessDetails processDetails = buildProcessDetails();
    processDetails.getDefinitionDetails().setRecordType(null);
    DomainEntityRequest domainEntityRequest = getDomainEntityRequest(processDetails);
    domainEntityRequest.setAdditionalDetails(AdditionalDetails.builder().variables(Map.of(ENTITY_TYPE,INVOICE.getRecordType())).build());
    Process process = DomainEventMapper.mapEntityToProcessDomainEvent(domainEntityRequest);
    Assert.assertNotNull(process);
    Assert.assertEquals(INVOICE.getRecordType(),process.getTriggerEntity().getEntityType());

  }

  @Test
  public void shouldMapEntityToDefinitionDomainEvent() {
    Definition definition =
        DomainEventMapper.mapEntityToDefinitionDomainEvent(buildDefinitionDetails());
    Assert.assertNotNull(definition);
    Assert.assertEquals("dId", definition.getId());
    Assert.assertEquals("1234", definition.getOwnerId());
  }

  @Test
  public void shouldMapEntityToDefinitionDomainEvent_WithEmptyRecordType() {
    DefinitionDetails definitionDetails = buildDefinitionDetails();
    definitionDetails.setRecordType(null);
    Definition definition = DomainEventMapper.mapEntityToDefinitionDomainEvent(definitionDetails);
    Assert.assertNotNull(definition);
    Assert.assertEquals("dId", definition.getId());
    Assert.assertEquals("1234", definition.getOwnerId());
  }

  @Test
  public void shouldMapEntityToTemplateDomainEvent() {
    Template template = DomainEventMapper.mapEntityToTemplateDomainEvent(buildTemplateDetails());
    Assert.assertNotNull(template);
    Assert.assertEquals("id", template.getId());
    Assert.assertEquals("1234", template.getOwnerId());
  }

  @Test
  public void testProcessWithErrorDetails() {
    DomainEventErrorDetails domainEventErrorDetails =
        DomainEventErrorDetails.builder().errorMessage("Error Message").activityId("aId").build();
    DomainEntityRequest domainEntityRequest = getDomainEntityRequest(buildProcessDetails());
    domainEntityRequest.setDomainEventErrorDetails(domainEventErrorDetails);
    Process process =
        DomainEventMapper.mapEntityToProcessDomainEvent(domainEntityRequest);
    Assert.assertNotNull(process);
    Assert.assertEquals("pId", process.getId());
    Assert.assertEquals("recordId", process.getTriggerEntity().getEntityId());
    Assert.assertEquals("123", process.getOwnerId());
    Assert.assertNotNull(process.getErrorDetail());
    Assert.assertEquals("Error Message", process.getErrorDetail().getErrorMessage());
    Assert.assertEquals("aId", process.getErrorDetail().getActivityId());
  }

  @Test
  public void testActivityRuntime() {
    ActivityRuntime activityRuntime =
        DomainEventMapper.mapEntityToActivityRuntimeDomainEvent(
            buildActivityProgressDetails(), null);
    Assert.assertNotNull(activityRuntime);
    Assert.assertEquals("aId", activityRuntime.getId());
    Assert.assertNotNull(activityRuntime.getAttributes());
    Assert.assertEquals(activityRuntime.getCorrelationId(), "pId");
  }

  @Test
  public void testActivityRuntimeForParentProcess() {
    ActivityProgressDetails activityProgressDetails = buildActivityProgressDetails();
    ActivityProgressDetails parentProgressDetails = buildActivityProgressDetails();
    parentProgressDetails.getProcessDetails().setProcessId("P1");
    activityProgressDetails.getProcessDetails().setParentProcessDetails(parentProgressDetails.getProcessDetails());
    ActivityRuntime activityRuntime =
        DomainEventMapper.mapEntityToActivityRuntimeDomainEvent(
            activityProgressDetails, null);
    Assert.assertNotNull(activityRuntime);
    Assert.assertEquals("aId", activityRuntime.getId());
    Assert.assertNotNull(activityRuntime.getAttributes());
    Assert.assertEquals(activityRuntime.getCorrelationId(), "P1");
  }

  @Test
  public void testActivityRuntimeWithPlaceholders() {
    ActivityProgressDetails activityProgressDetails = buildActivityProgressDetails();
    activityProgressDetails.getProcessDetails().setDefinitionDetails(buildDefinitionDetailsWithPlaceholders());
    activityProgressDetails.setId("createTask:b2754689-d383-11ed-9878-f6372692eefb");
    activityProgressDetails.setAttributes("{\"runtimeAttributes\":{\"parameterDetails\":\"{}\",\"handlerDetails\":\"{}\",\"entityChangeType\":\"created\",\"entityType\":\"Invoice\",\"TxnDate\":\"2020-01-31\",\"CompanyEmail\":\"\",\"TxnAmount\":7898.0,\"intuit_userid\":\"-9130357096595396\",\"sendCompanyEmail\":\"true\",\"TxnBalanceAmount\":null,\"CompanyName\":\"\",\"TxnDueDate\":\"\",\"templateName\":\"invoiceapproval\",\"CustomerEmail\":\"\",\"DocNumber\":\"1013\",\"sendPushNotification\":\"true\",\"definitionKey\":\"customApproval_9130357096596496_4e0d5de3-484c-4365-88e1-0162e7c7b82e\",\"Id\":\"22734\",\"projectServiceMap\":{\"2cf83ea0-9e5b-42b1-8616-d7fbd8d191f6\":\"djQuMTo5MTMwMzYwNTYyNjMzMDg2OjY4ZDAxMTQ3ZGQ:42099034\", \"2cf83ea0-9e5b-42b1-8616-d7fbd8d191f6\":\"djQuMTo5MTMwMzYwNTYyNjMzMDg2OjY4ZDAxMTQ3ZGQ:42099034\"},\"createTask\":\"true\",\"projectTaskDetailIds\":[\"2cf83ea0-9e5b-42b1-8616-d7fbd8d191f6\",\"2cf83ea0-9e5b-42b1-8616-d7fbd8d191f6\"],\"taskDetails\":\"{ \\\"required\\\": true}\",\"TxnSendStatus\":\"\",\"intuit_realmid\":\"9130357096596496\",\"decisionResult\":true},\"modelAttributes\":{\"events\":\"[\\\"start\\\"]\",\"auditMessage\":\"Task assigned to {}\"}}");
    Map<String, String> resMap = new HashMap<>();
    resMap.put("Assignee", "7890072772");
    resMap.put("TaskName", "Dummy task name [[DocNumber]]");
    Mockito.when(defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(Mockito.any(), Mockito.any())).thenReturn(resMap);
    ActivityRuntime activityRuntime =
            DomainEventMapper.mapEntityToActivityRuntimeDomainEvent(
                   activityProgressDetails , null);
    Assert.assertNotNull(activityRuntime);
    Assert.assertEquals("createTask:b2754689-d383-11ed-9878-f6372692eefb", activityRuntime.getId());
    Assert.assertNotNull(activityRuntime.getAttributes());
    List<Attribute> attributes = activityRuntime.getAttributes();
    Attribute runtimeAttributes = attributes.get(0);
    Assert.assertEquals(true, runtimeAttributes.getValues().contains("Dummy task name 1013"));
  }

  @Test
  public void getListOfLookupKeysTest() {
    Map<String, String> lookupKeys = new HashMap<>();
    lookupKeys.put("envelopeId", "1234");
    String lookupKeysString = ObjectConverter.toJson(lookupKeys);
    List<LookUpKey> lookupKeyList =
        DomainEventMapper.getListOfLookupKeysFromString(lookupKeysString).get();
    Assert.assertEquals(lookupKeyList.size(), 1);
    Assert.assertEquals(lookupKeyList.get(0).getKey(), "envelopeId");
    Assert.assertEquals(lookupKeyList.get(0).getValue(), "1234");
  }

  @Test
  public void getListOfLookupKeysJsonTest() {
    Map<String, Object> lookupKeys = new HashMap<>();
    SystemTags systemTag = new SystemTags();
    systemTag.addSystemTag("system_tag", "value");

    lookupKeys.put("envelopeId", systemTag);
    String lookupKeysString = ObjectConverter.toJson(lookupKeys);
    List<LookUpKey> lookupKeyList =
        DomainEventMapper.getListOfLookupKeysFromString(lookupKeysString).get();
    
    Assert.assertEquals(lookupKeyList.size(), 1);
    Assert.assertEquals(lookupKeyList.get(0).getKey(), "envelopeId");
    Assert.assertTrue(lookupKeyList.get(0).getValue().contains("version=value"));
  }

  @Test
  public void getListOfLookupKeysNullTest() {
    Optional<List<LookUpKey>> lookupKeyList = DomainEventMapper.getListOfLookupKeysFromString(null);
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void getListOfLookupKeysEmptyTest() {
    Optional<List<LookUpKey>> lookupKeyList = DomainEventMapper.getListOfLookupKeysFromString("");
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void getListOfLookupKeysNullMapTest() {
    String lookupKeysString = "invalid string";
    Optional<List<LookUpKey>> lookupKeyList =
        DomainEventMapper.getListOfLookupKeysFromString(lookupKeysString);
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void testDefinitionDomainEventWithNullCreatedDate() {
    Definition definition =
        DomainEventMapper.mapEntityToDefinitionDomainEvent(buildDefinitionDetailsWithNullDates());
    Assert.assertNotNull(definition);
    Assert.assertEquals("dId", definition.getId());
    Assert.assertNotNull(definition.getAudit());
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getMeta().getCreated());
    Assert.assertNotNull(definition.getLookUpKeys());
  }

  @Test
  public void testTemplateDomainEventWithNullCreatedDate() {
    Template template =
        DomainEventMapper.mapEntityToTemplateDomainEvent(buildTemplateDetailsWithNullDates());
    Assert.assertNotNull(template);
    Assert.assertEquals("id", template.getId());
    Assert.assertEquals("1234", template.getOwnerId());
    Assert.assertNotNull(template.getAudit());
    Assert.assertNotNull(template.getMeta());
    Assert.assertNotNull(template.getMeta().getCreated());
  }

  private ActivityProgressDetails buildActivityProgressDetails() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    return ActivityProgressDetails.builder()
        .id("aId")
        .startTime(timestamp)
        .updatedTime(timestamp)
        .attributes(ObjectConverter.toJson(runtimeDefAttributes))
        .activityDefinitionDetail(
            ActivityDetail.builder()
                .activityId("u-activityId")
                .activityType("aType")
                .attributes(ObjectConverter.toJson(modelDefAttributes))
                .build())
        .processDetails(buildProcessDetails())
        .build();
  }

  private ProcessDetails buildProcessDetails() {

    return ProcessDetails.builder()
        .processId("pId")
        .recordId("recordId")
        .processStatus(ProcessStatus.ACTIVE)
        .definitionDetails(buildDefinitionDetails())
        .ownerId(123L)
        .createdDate(Timestamp.from(Instant.now()))
        .modifiedDate(Timestamp.from(Instant.now()))
        .parentId(null)
        .build();
  }

  private DefinitionDetails buildDefinitionDetails() {
    return DefinitionDetails.builder()
        .definitionId("dId")
        .definitionName("dName")
        .ownerId(1234L)
        .offeringId(123L)
        .status(Status.ENABLED)
        .internalStatus(InternalStatus.MARKED_FOR_DISABLE)
        .recordType(INVOICE)
        .lookupKeys("{\"envelopeId\":\"1234\", \"customId\":\"1234\"}")
        .modelType(ModelType.BPMN)
        .createdDate(Timestamp.from(Instant.now()))
        .modifiedDate(Timestamp.from(Instant.now()))
        .templateDetails(TemplateDetails.builder().offeringId("oId").build())
        .build();
  }

  private DefinitionDetails buildDefinitionDetailsWithPlaceholders() {
    return DefinitionDetails.builder()
            .definitionId("dId")
            .definitionName("dName")
            .ownerId(1234L)
            .offeringId(123L)
            .status(Status.ENABLED)
            .internalStatus(InternalStatus.MARKED_FOR_DISABLE)
            .recordType(INVOICE)
            .lookupKeys("{\"envelopeId\":\"1234\", \"customId\":\"1234\"}")
            .modelType(ModelType.BPMN)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .placeholderValue("{\"user_meta_data\": {\"intuit_was_locale\": \"en_us\"}, \"user_variables\": {\"approval:createTask\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130358018375316\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"txn_approval_changed\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}, \"approval:sendCompanyEmail\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\\n\\nNote that invoices that are not approved for more than 30 days will be auto rejected.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}, \"approval:sendPushNotification\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"9130358018375286\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}, \"process_variables\": {\"Id\": {\"type\": \"String\"}, \"TxnDate\": {\"type\": \"string\"}, \"DocNumber\": {\"type\": \"string\"}, \"TxnAmount\": {\"type\": \"double\"}, \"TxnDueDate\": {\"type\": \"string\"}, \"createTask\": {\"type\": \"String\", \"value\": \"true\"}, \"entityType\": {\"type\": \"String\"}, \"CompanyName\": {\"type\": \"string\"}, \"CompanyEmail\": {\"type\": \"string\"}, \"CustomerName\": {\"type\": \"string\"}, \"CustomerEmail\": {\"type\": \"string\"}, \"TxnSendStatus\": {\"type\": \"string\"}, \"intuit_userid\": {\"type\": \"string\"}, \"intuit_realmid\": {\"type\": \"String\"}, \"TxnBalanceAmount\": {\"type\": \"double\"}, \"entityChangeType\": {\"type\": \"String\"}, \"sendCompanyEmail\": {\"type\": \"String\", \"value\": \"true\"}, \"sendPushNotification\": {\"type\": \"String\", \"value\": \"true\"}}}")
            .templateDetails(TemplateDetails.builder().offeringId("oId").build())
            .build();
  }

  private DefinitionDetails buildDefinitionDetailsWithNullDates() {
    return DefinitionDetails.builder()
        .definitionId("dId")
        .definitionName("dName")
        .offeringId(123L)
        .status(Status.ENABLED)
        .internalStatus(null)
        .lookupKeys("{\"envelopeId\":\"1234\", \"customId\":\"1234\"}")
        .modelType(ModelType.BPMN)
        .templateDetails(buildTemplateDetailsWithNullDates())
        .build();
  }

  private TemplateDetails buildTemplateDetails() {
    return TemplateDetails.builder()
        .id("id")
        .status(Status.ENABLED)
        .creatorType(CreatorType.SYSTEM)
        .createdDate(Timestamp.from(Instant.now()))
        .modelType(ModelType.BPMN)
        .modifiedDate(Timestamp.from(Instant.now()))
        .recordType(INVOICE)
        .offeringId("oid")
        .ownerId(1234L)
        .build();
  }

  private TemplateDetails buildTemplateDetailsWithNullDates() {
    return TemplateDetails.builder()
        .id("id")
        .status(Status.ENABLED)
        .creatorType(CreatorType.SYSTEM)
        .modelType(ModelType.BPMN)
        .modifiedDate(Timestamp.from(Instant.now()))
        .recordType(INVOICE)
        .ownerId(1234L)
        .build();
  }
}
