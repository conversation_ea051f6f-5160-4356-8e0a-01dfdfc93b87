package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 * Test class for MultiStepPlaceholderUserVariableExtractor
 */
public class MultiStepPlaceholderUserVariableExtractorTest {

  @InjectMocks
  private MultiStepPlaceholderUserVariableExtractor multiStepPlaceholderUserVariableExtractor;

  @Mock
  private ProcessDetailsRepoService processDetailsRepoService;
  @Mock
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  private DefinitionDetails definitionDetails;
  private ProcessDetails processDetails;
  private ActivityProgressDetails activityProgressDetails;
  private DefinitionActivityDetail definitionActivityDetail;

  private final Map<String, String> runtimeAttributes = new HashMap<>();


  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    definitionDetails = DefinitionDetails.builder()
        .definitionId("test-def-id")
        .definitionKey("customApproval_9130357096596496_4e0d5de3-484c-4365-88e1-0162e7c7b82e")
        .placeholderValue(
            "{\"user_meta_data\": {\"intuit_was_locale\": null}, \"user_variables\": {}, \"process_variables\": {\"Id\": {\"type\": \"String\"}, \"TxnDate\": {\"type\": \"string\"}, \"Location\": {\"type\": \"string\"}, \"DocNumber\": {\"type\": \"string\"}, \"TxnAmount\": {\"type\": \"double\"}, \"TxnDueDate\": {\"type\": \"string\"}, \"entityType\": {\"type\": \"String\"}, \"CompanyName\": {\"type\": \"string\"}, \"CompanyEmail\": {\"type\": \"string\"}, \"CustomerName\": {\"type\": \"string\"}, \"CustomerEmail\": {\"type\": \"string\"}, \"TxnSendStatus\": {\"type\": \"string\"}, \"intuit_userid\": {\"type\": \"String\"}, \"intuit_realmid\": {\"type\": \"String\"}, \"TxnBalanceAmount\": {\"type\": \"double\"}, \"entityChangeType\": {\"type\": \"String\"}}}")
        .build();
    processDetails = ProcessDetails.builder()
        .processId("test-id")
        .definitionDetails(definitionDetails)
        .ownerId(1234556L)
        .recordId("22734")
        .processStatus(ProcessStatus.ACTIVE)
        .build();
    activityProgressDetails = ActivityProgressDetails.builder()
        .id("createTask:b2754689-d383-11ed-9878-f6372692eefb")
        .name("Create project service task")
        .attributes(
            "{\"runtimeAttributes\":{\"parameterDetails\":\"{}\",\"handlerDetails\":\"{}\",\"entityChangeType\":\"created\",\"entityType\":\"Invoice\",\"TxnDate\":\"2020-01-31\",\"CompanyEmail\":\"\",\"TxnAmount\":7898.0,\"intuit_userid\":\"-9130357096595396\",\"sendCompanyEmail\":\"true\",\"TxnBalanceAmount\":null,\"CompanyName\":\"\",\"TxnDueDate\":\"\",\"templateName\":\"invoiceapproval\",\"CustomerEmail\":\"\",\"DocNumber\":\"1013\",\"sendPushNotification\":\"true\",\"definitionKey\":\"customApproval_9130357096596496_4e0d5de3-484c-4365-88e1-0162e7c7b82e\",\"Id\":\"22734\",\"CustomerName\":\"\",\"createTask\":\"true\",\"taskDetails\":\"{ \\\"required\\\": true}\",\"TxnSendStatus\":\"\",\"intuit_realmid\":\"9130357096596496\",\"decisionResult\":true},\"modelAttributes\":{\"events\":\"[\\\"start\\\"]\",\"auditMessage\":\"Task assigned to {}\"}}")
        .processDetails(processDetails)
        .build();

    definitionActivityDetail = DefinitionActivityDetail.builder()
        .id("222")
        .userAttributes(
            "{\"selected\": false, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"22222222\"]}, \"approvalType\": {\"fieldValue\": [\"PARALLEL\"]}}}")
        .definitionDetails(definitionDetails)
        .build();

    runtimeAttributes.put(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "P11");
    runtimeAttributes.put(WorkflowConstants.ACTIVITY_ID, "A11");
  }

  @Test
  public void testGetUserVariablesForActivity() {

    Mockito.when(processDetailsRepoService.findByProcessId(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));
    Mockito.when(definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
            Mockito.anyString(),
            Mockito.anyString(), Mockito.anyString()))
        .thenReturn(Optional.of(definitionActivityDetail));

    Map<String, String> userPlaceholderAttributes = multiStepPlaceholderUserVariableExtractor.getUserVariablesForActivity(
        activityProgressDetails, runtimeAttributes);

    Assert.assertNotNull(userPlaceholderAttributes);

    Assert.assertNotNull(userPlaceholderAttributes.get("Assignee"));
    Assert.assertNotNull(userPlaceholderAttributes.get("approvalType"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetUserVariablesForActivityWhenParentProcessIdDoesnotExist() {
    Mockito.when(processDetailsRepoService.findByProcessId(Mockito.anyString()))
        .thenReturn(Optional.empty());
    multiStepPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails,
        runtimeAttributes);
  }

  @Test
  public void testGetUserVariablesForActivityWhenDefinitionDetailsDoesnotExist() {

    Mockito.when(processDetailsRepoService.findByProcessId(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));
    Mockito.when(definitionActivityDetailsRepository.findActivityDetailsByDefinitionIdAndParentId(
        Mockito.anyString(),
        Mockito.anyString(), Mockito.anyString())).thenReturn(Optional.empty());

    Map<String, String> userPlaceholderAttributes = multiStepPlaceholderUserVariableExtractor.getUserVariablesForActivity(
        activityProgressDetails, runtimeAttributes);
    Assert.assertEquals(userPlaceholderAttributes.size(), 0);
  }

}
