package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PlaceholderUserVariableExtractorUtilTest {

    @Mock
    private DefaultPlaceholderUserVariableExtractor defaultPlaceholderUserVariableExtractor;
    private Map<String, String> runtimeAttributes = new HashMap<>();


    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(defaultPlaceholderUserVariableExtractor.getName()).thenReturn(PlaceholderUserVariableExtractorType.DEFAULT);
        PlaceholderUserVariableExtractors.addHandler(defaultPlaceholderUserVariableExtractor.getName(), defaultPlaceholderUserVariableExtractor);
        runtimeAttributes.put("xuz", "aaa");
    }

    @Test
    public void testGetPlaceholderExtractorForMultiCondition(){
        runtimeAttributes.put(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, "111");
        PlaceholderUserVariableExtractorType placeholderUserVariableExtractor = PlaceholderUserVariableExtractorUtil.getPlaceholderExtractor(runtimeAttributes);
        Assert.assertNotNull(placeholderUserVariableExtractor);
        Assert.assertEquals(placeholderUserVariableExtractor.name(), PlaceholderUserVariableExtractorType.MULTI_STEP.name());
    }

    @Test
    public void testGetPlaceholderExtractorWithNullVariables(){
        PlaceholderUserVariableExtractorType placeholderUserVariableExtractor = PlaceholderUserVariableExtractorUtil.getPlaceholderExtractor(null);
        Assert.assertNotNull(placeholderUserVariableExtractor);
        Assert.assertEquals(placeholderUserVariableExtractor.name(), PlaceholderUserVariableExtractorType.DEFAULT.name());
    }

    @Test
    public void testGetPlaceholderExtractorForDefaultValues(){
        PlaceholderUserVariableExtractorType placeholderUserVariableExtractor = PlaceholderUserVariableExtractorUtil.getPlaceholderExtractor(null);
        Assert.assertNotNull(placeholderUserVariableExtractor);
        Assert.assertEquals(placeholderUserVariableExtractor.name(), PlaceholderUserVariableExtractorType.DEFAULT.name());
    }

}
