package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PlaceholderUserVariableExtractorsTest {

    @Mock
    private DefaultPlaceholderUserVariableExtractor defaultPlaceholderUserVariableExtractor;


    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(defaultPlaceholderUserVariableExtractor.getName()).thenReturn(PlaceholderUserVariableExtractorType.DEFAULT);
        PlaceholderUserVariableExtractors.addHandler(defaultPlaceholderUserVariableExtractor.getName(), defaultPlaceholderUserVariableExtractor);
    }


    @Test
    public void getTestNUll() {
        PlaceholderUserVariableExtractor handler = PlaceholderUserVariableExtractors.getHandler(null);
        Assert.assertNull(handler);
    }

    @Test
    public void getTestAction() {
        PlaceholderUserVariableExtractor handler = PlaceholderUserVariableExtractors.getHandler(PlaceholderUserVariableExtractorType.DEFAULT);
        Assert.assertNotNull(handler);
    }
    @Test
    public void containsFalse() {
        Assert.assertFalse(PlaceholderUserVariableExtractors.contains(null));
    }
    @Test
    public void containsTrue() {
        Assert.assertTrue(PlaceholderUserVariableExtractors.contains(PlaceholderUserVariableExtractorType.DEFAULT));
    }

}
