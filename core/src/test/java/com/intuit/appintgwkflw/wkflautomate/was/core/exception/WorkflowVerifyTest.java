package com.intuit.appintgwkflw.wkflautomate.was.core.exception;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;

import org.junit.Assert;
import org.junit.Test;

/** <AUTHOR> */
public class WorkflowVerifyTest {

  @Test(expected = WorkflowGeneralException.class)
  public void verifyTrue() {
    String x = null;
    WorkflowVerfiy.verify(x == null, WorkflowError.INTERNAL_EXCEPTION);
  }

  @Test
  public void verifyFalse() {
    String x = "acb";
    WorkflowVerfiy.verify(x == null, WorkflowError.INTERNAL_EXCEPTION);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void verifyTrueWithoutError() {
    String x = null;
    WorkflowVerfiy.verify(x == null, null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void verifyFalseWithoutError() {
    String x = null;
    WorkflowVerfiy.verify(x == null, null);
  }

  @Test
  public void verifyFormatMsg() {
    String x = null;
    try {
      WorkflowVerfiy.verify(x == null, WorkflowError.OTHER_TEST, "errorArg1");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains("errorArg1"));
    }
  }
  
  @Test(expected = WorkflowGeneralException.class)
  public void verifyFormatMsg1() {
    String x = null;
    WorkflowVerfiy.verify(x == null, WorkflowError.OTHER_TEST, null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void verifyNullTrue() {
    String x = null;
    WorkflowVerfiy.verifyNull(x, WorkflowError.INTERNAL_EXCEPTION);
  }

  @Test
  public void verifyNullFalse() {
    String x = "abc";
    WorkflowVerfiy.verifyNull(x, WorkflowError.INTERNAL_EXCEPTION);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void verifyNullWithoutError() {
    String x = null;
    WorkflowVerfiy.verifyNull(x, null);
  }

  @Test
  public void verifyStringNull() {
    String x = null;
    try {
      WorkflowVerfiy.verify(x, WorkflowError.OTHER_TEST, "errorArg1");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains("errorArg1"));
    }
  }

  @Test
  public void verifyStringEmpty() {
    String x = "";
    try {
      WorkflowVerfiy.verify(x, WorkflowError.OTHER_TEST, "errorArg1");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains("errorArg1"));
    }
  }

  @Test(expected = WorkflowEventException.class)
  public void verifyNullAndThrowTest() {
    WorkflowVerfiy.verifyNullAndThrow(null, () -> {
      throw new WorkflowEventException(new WorkflowGeneralException(WorkflowError.OTHER_TEST));
    });
  }

  @Test
  public void verifyNullAndThrowEmptyExceptionTest() {
    try{
      WorkflowVerfiy.verifyNullAndThrow(null, null);
    } catch (WorkflowGeneralException e){
      Assert.assertEquals(
          WorkflowError.INVALID_WORKFLOW_ERROR_INPUT.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void verifyNullAndThrowFalseTest() {
    String x = "xyz";
    WorkflowVerfiy.verifyNullAndThrow(x, () -> {
      throw new WorkflowEventException(new WorkflowGeneralException(WorkflowError.OTHER_TEST));
    });
  }
}
