package com.intuit.appintgwkflw.wkflautomate.was.core.feelprovider;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class CustomListFunctionProviderTest {
  @InjectMocks private CustomListFunctionProvider customListFunctionProvider;

  @Test
  public void test_CustomFunction() {
    Collection<String> strings = customListFunctionProvider.getFunctionNames();
    Assert.assertNotNull(strings);
    Assert.assertEquals(1, strings.size());
    Assert.assertTrue(strings.contains("containsAnyElement"));
  }

  @Test
  public void test_CustomFunction_args_isNull() {
    Assert.assertFalse(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(null));
  }

  @Test
  public void test_CustomFunction_argsSize_lessThanTwo() {
    Assert.assertFalse(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(List.of("INPUT")));
  }

  @Test
  public void test_CustomFunction_conditionElemList_isNull() {
    List<Object> stringList = new ArrayList<>();
    stringList.add(null);
    stringList.add(List.of("1", "2"));
    Assert.assertFalse(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(stringList));
  }

  @Test
  public void test_CustomFunction_input_isNull() {
    List<Object> stringList = new ArrayList<>();
    stringList.add(List.of("1", "2"));
    stringList.add(null);
    Assert.assertFalse(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(stringList));
  }

  @Test
  public void test_CustomFunction_input_isString_true() {
    List<Object> stringList = new ArrayList<>();
    stringList.add(List.of("1", "2"));
    stringList.add("1,2");
    Assert.assertTrue(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(stringList));
  }

  @Test
  public void test_CustomFunction_input_isString_false() {
    List<Object> stringList = new ArrayList<>();
    stringList.add(List.of("1", "2"));
    stringList.add("3,4");
    Assert.assertFalse(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(stringList));
  }

  @Test
  public void test_CustomFunction_input_isList_true() {
    List<Object> stringList = new ArrayList<>();
    stringList.add(List.of("1", "2"));
    stringList.add(List.of("1", "2", "3"));
    Assert.assertTrue(
        (Boolean)
            customListFunctionProvider
                .resolveFunction("containsAnyElement")
                .get()
                .getFunction()
                .apply(stringList));
  }
}
