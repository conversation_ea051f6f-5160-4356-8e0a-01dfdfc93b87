package com.intuit.appintgwkflw.wkflautomate.was.core.filters.template;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_TYPE_NAME;
import static org.junit.Assert.assertEquals;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MockDataUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.query.FilterExpression;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TemplateNameFilterTest {

  @InjectMocks
  private TemplateNameFilter templateNameFilter;

  @Test
  public void whenFilter_andPredicateMatches_thenSuccess() {

    final List<TemplateDetails> templateDetails = MockDataUtil.buildTemplateDetails();

    final FilterExpression filterExpression = new FilterExpression();
    filterExpression.setProperty(FILTER_TYPE_NAME);
    filterExpression.addArgs("invoice_approval.bpmn");

    final List<TemplateDetails> result = templateNameFilter
        .filter(templateDetails, filterExpression);

    assertEquals(1, result.size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void whenFilter_andPredicateDoesNotMatch_thenSuccess() {

    final List<TemplateDetails> templateDetails = MockDataUtil.buildTemplateDetails();

    final FilterExpression filterExpression = new FilterExpression();
    filterExpression.setProperty(FILTER_TYPE_NAME);
    filterExpression.addArgs("default");

    templateNameFilter.filter(templateDetails, filterExpression);
  }
}