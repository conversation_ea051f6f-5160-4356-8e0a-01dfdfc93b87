package com.intuit.appintgwkflw.wkflautomate.was.core.helper;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DefinitionTestConstants {
  public static final String REALM_ID = "realm-id";
  public static final String TEMPLATE_ID = "template-id";
  public static final String WORKFLOW_STEP_ID = "step-id";
  public static final String TRIGGER_ID = "waitForTimerToElapse1_invoiceApproval_companyId_uuid";
  public static final String ACTION_ID = "sendApprovalEmail_invoiceApproval_companyId_uuid";
  public static final String ACTION_ID_BANK_DEPOSIT =
      "sendNotification_bankdepositreminder_companyId_uuid";
  public static final String CONDITION_ID_XOR =
      "evaluateUserDefinedAction_invoiceApproval_companyId_uuid";
  public static final String CONDITION_ID_DMN =
      "invoiceApprovalDecision_invoiceApproval_companyId_uuid";
  public static final String MAPPED_KEY_DMN = "approvalRequired";
  public static final String MAPPED_KEY_XOR = "sendReminderEmail_invoiceApproval_companyId_uuid";
  public static final String DEFINITION_NAME = "defintion-1";
  public static final String TEMPLATE_NAME = "template-1";
  public static final String DEF_ID = "353b0efa-cd76-4fbf-9b5b-dd68813b08da";

  public static final String CUSTOM_WORKFLOW_CONDITION_ID_DMN = "decisionElement";
  public static final String CUSTOM_WORKFLOW_REALM_ID = "****************";
  public static final String PRECANNED_WORKFLOW_REALM_ID = "companyId";
  public static final String CUSTOM_WORKFLOW_GUID = "be434694-c53b-4772-99c2-cddc2dcb9c3d";
  public static final String CUSTOM_WORKFLOW_STEP_ID = "customStartEvent";
  public static final String CREATE_TASK_ACTION_ID = "createTask";
  public static final String SEND_COMPANY_EMAIL_ACTION_ID = "sendCompanyEmail";
  public static final String CUSTOM_WORKFLOW_TRIGGER_ID = "customStartEvent";
  public static final String MAPPED_ACTION_DMN = "sendReminder";
  public static final String SEND_STATEMENT_ACTION_ID = "scheduledAction";
  public static final String CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION ="decisionResult";
  public static final String ACTION_STEP_ID = "action-id";
  public static final String CONDITION_STEP_ID = "condition-id";
  public static final String YES_PATH_STEP_ID = "yes-id";
  public static final String SEND_FOR_APPROVAL_ACTION_ID = "sendForApproval";
}
