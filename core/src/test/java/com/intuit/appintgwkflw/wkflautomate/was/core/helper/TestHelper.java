package com.intuit.appintgwkflw.wkflautomate.was.core.helper;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.ACTION_ID_BANK_DEPOSIT;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CONDITION_ID_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CREATE_TASK_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_CONDITION_ID_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_GUID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_REALM_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_STEP_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_TRIGGER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEFINITION_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.MAPPED_ACTION_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.MAPPED_KEY_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.REALM_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.SEND_COMPANY_EMAIL_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.SEND_STATEMENT_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.TEMPLATE_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.TEMPLATE_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.TRIGGER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.WORKFLOW_STEP_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_TEMP_FILE_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RULE_LINE_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.OnDemandConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ParameterDetailsConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.ExternalServiceMapping;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.LocalisationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.HelpVariableRecordTypeTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.RecordTypeTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.UrlTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.AppLocalisationResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.LocalizedResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStep.StepNext;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.feel.syntaxtree.In;
import org.joda.time.DateTime;
import org.json.JSONObject;

public class TestHelper {
  public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
  public static String ON_DEMAND_PATH = "schema/testData/ondemand.yaml";
  public static String DICTIONARY_STATEMENT_PATH = "schema/testData/dictionary_statement.yaml";
  public static String YAML_KEY = "templateConfig";
  public static String PLACEHOLDER_VALUES_PATH = "placeholder/placeholder_value.json";
  public static final String ACTIVITY_ATTRIBUTES = "{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"startEvent\\\",\\\"Account-Manager\\\"] }\", \"taskDetails\": \"{ \\\"required\\\": true }\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"taskHandler\\\":\\\"was\\\",\\\"recordType\\\":\\\"test\\\"}\", \"startableEvents\": \"[\\\"created\\\", \\\"updated\\\"]\", \"currentStepDetails\": \"{ \\\"required\\\": true }\", \"processVariablesDetails\": \"[{\\\"variableName\\\":\\\"userId\\\",\\\"variableType\\\":\\\"String\\\"},{\\\"variableName\\\":\\\"assigneeId\\\",\\\"variableType\\\":\\\"String\\\"}]\", \"ignoreProcessVariablesDetails\": \"true\"}}";

  public static String readResourceAsString(String path) {
    try (InputStream stream = Resources.getResource(path).openStream()) {
      return IOUtils.toString(stream, Charsets.UTF_8);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static DefinitionInstance mockDefinitionInstance(
      Definition definition,
      TemplateDetails bpmnTemplateDetail,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstanceList) {
    return new DefinitionInstance(
        definition, bpmnModelInstance, dmnModelInstanceList, bpmnTemplateDetail);
  }

  public static DefinitionInstance mockDefinitionInstanceWithPlaceholder(
      Definition definition,
      TemplateDetails bpmnTemplateDetail,
      BpmnModelInstance bpmnModelInstance,
      List<DmnModelInstance> dmnModelInstanceList) {
    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definition, bpmnModelInstance, dmnModelInstanceList, bpmnTemplateDetail);
    definitionInstance.setPlaceholderValue(getMockedPlaceholderValue());
    return definitionInstance;
  }

  public static Authorization mockAuthorization(String realmId) {
    Authorization authorization = new Authorization();
    authorization.putRealm(realmId);
    authorization.putAuthId("453");
    return authorization;
  }

  public static GlobalId getGlobalId(String localId) {
    return GlobalId.builder().setRealmId(REALM_ID).setLocalId(localId).build();
  }

  public static GlobalId getActionGlobalId(String localId) {
    return GlobalId.builder().setRealmId(REALM_ID).setLocalId(getLocalId(localId)).build();
  }

  public static DefinitionDetails mockDefinitionDetails(
      Definition definition, TemplateDetails bpmnTemplateDetail, Authorization authorization) {
    return DefinitionDetails.builder()
        .definitionId(definition.getId().getLocalId())
        .templateDetails(bpmnTemplateDetail)
        .ownerId(Long.valueOf(authorization.getRealm()))
        .createdDate(new Timestamp(System.currentTimeMillis()))
        .createdByUserId(12345L)
        .modifiedByUserId(54321L)
        .workflowId("wkid")
        .placeholderValue(TestHelper.readResourceAsString(PLACEHOLDER_VALUES_PATH))
        .version(0)
        .recordType(RecordType.INVOICE)
        .modelType(ModelType.BPMN)
        .definitionKey("def_12345_uuid")
        .build();
  }

  public static TemplateDetails mockTemplateDetails(
      String templateCategory) {
    return TemplateDetails.builder()
        .templateName("mockTemplate")
        .templateCategory(templateCategory)
        .version(0)
        .build();
  }

  public static DefinitionDetails mockDefinitionDetailsWithNonZeroVersion(
      Definition definition, TemplateDetails bpmnTemplateDetail, Authorization authorization) {
    return DefinitionDetails.builder()
        .definitionId(definition.getId().getLocalId())
        .templateDetails(bpmnTemplateDetail)
        .ownerId(Long.valueOf(authorization.getRealm()))
        .workflowId("wkid")
        .version(1)
        .build();
  }

  public static DefinitionDetails mockDefinitionDetailsWithId(
      TemplateDetails bpmnTemplateDetail, Authorization authorization, String localId) {
    return DefinitionDetails.builder()
        .definitionId(localId)
        .recordType(RecordType.INVOICE)
        .templateDetails(bpmnTemplateDetail)
        .workflowId("wkid")
        .ownerId(Long.valueOf(authorization.getRealm()))
        .build();
  }

  public static BpmnResponse mockBpmnResponse(DefinitionDetails definitionDetails, String path) {
    String xml = TestHelper.readResourceAsString(path);
    return BpmnResponse.builder().bpmn20Xml(xml).id(definitionDetails.getDefinitionId()).build();
  }

  public static DmnResponse mockDmnResponse(DefinitionDetails definitionDetails, String path) {
    String xml = TestHelper.readResourceAsString(path);
    return DmnResponse.builder().dmnXml(xml).id(definitionDetails.getDefinitionId()).build();
  }

  public static DefinitionDetails mockDefinitionDetailsWithParentId(
      TemplateDetails bpmnTemplateDetail, Authorization authorization,
      String localId, String parentId) {
    return DefinitionDetails.builder()
        .definitionId(localId)
        .parentId(parentId)
        .recordType(RecordType.INVOICE)
        .templateDetails(bpmnTemplateDetail)
        .ownerId(Long.valueOf(authorization.getRealm()))
        .build();
  }

  private static WorkflowStep getMockedWorkflowStep() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
        new Trigger()
            .id(getGlobalId(TRIGGER_ID))
            .parameter(
                new InputParameter()
                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
        new Action()
            .id(getGlobalId(ACTION_ID))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"))
            .parameter(new InputParameter().parameterName("requireByUI").fieldValue("true"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression("GT  500")
                    .parameterName("amountEvaluation").parameterType(FieldTypeEnum.STRING));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
            .description("descriotion");
    workflowStep.workflowStepCondition(conditionDmn);

    return workflowStep;
  }

  private static WorkflowStep getMockedWorkflowStepWithActionGroup() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);

    Action action =
            new Action()
                    .id(getGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"))
                    .parameter(new InputParameter().parameterName("requireByUI").fieldValue("true"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().actionKey("approval").action(action));
    RuleLine ruleLineDmn =
            new RuleLine()
                    .mappedActionKey(MAPPED_KEY_DMN)
                    .rule(
                            new RuleLine.Rule()
                                    .conditionalExpression("GT  500")
                                    .parameterName("amountEvaluation").parameterType(FieldTypeEnum.STRING));
    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
                    .description("descriotion");
    workflowStep.workflowStepCondition(conditionDmn);
    ActionGroup actionGroup = new ActionGroup();
    actionGroup.setActionKey("approval");
    actionGroup.setAction(action);
    workflowStep.setActionGroup(actionGroup);
    return workflowStep;
  }

  private static WorkflowStep getMockedWorkflowStepWithoutCondition() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"))
                    .parameter(new InputParameter().parameterName("requireByUI").fieldValue("true"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    RuleLine ruleLineDmn =
            new RuleLine()
                    .mappedActionKey(MAPPED_KEY_DMN)
                    .rule(
                            new RuleLine.Rule()
                                    .conditionalExpression("GT  500")
                                    .parameterName("amountEvaluation"));
    return workflowStep;
  }

  private static WorkflowStep getMockedWorkflowStepBankDepositReminder() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
        new Trigger()
            .id(getGlobalId(TRIGGER_ID))
            .parameter(
                new InputParameter()
                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
        new Action()
            .id(getGlobalId(ACTION_ID_BANK_DEPOSIT))
            .parameter(
                new InputParameter()
                    .parameterName("Approver #1")
                    .fieldValue("****************")
                    .configurable(Boolean.TRUE));
    workflowStep =
        workflowStep.action(
            new WorkflowStep.ActionMapper().actionKey("sendReminder").action(action));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression("GT 500")
                    .parameterName("Undeposited Funds"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn)
            .description("description");
    workflowStep.workflowStepCondition(conditionDmn);

    return workflowStep;
  }

  public static Template mockTemplateEntity() {
    return new Template()
        .name(TEMPLATE_NAME)
        .id(GlobalId.builder().setLocalId("1111").build())
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(getMockedWorkflowStep());
  }

  public static Definition mockDefinitionEntity() {
    return new Definition()
        .name(DEFINITION_NAME)
        .template(new Template().id(getGlobalId(TEMPLATE_ID)))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(getMockedWorkflowStep())
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockDefinitionEntityWithActionGroup() {
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(getMockedWorkflowStepWithActionGroup())
            .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockDefinitionEntityWithNoCondition() {
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(getMockedWorkflowStepWithoutCondition())
            .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockDefinitionBankDepositReminder() {
    return new Definition()
        .name(DEFINITION_NAME)
        .template(new Template().id(getGlobalId(TEMPLATE_ID)))
        .recordType(RecordType.BILL.getRecordType())
        .workflowStep(getMockedWorkflowStepBankDepositReminder())
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Record mockRecord() {
    List<String> helpVariables = new ArrayList<>();
    helpVariables.addAll(Arrays.asList("Company Email:CompanyEmail:string"));
    Record record = new Record();
    record.setHelpVariables(helpVariables);
    return record;
  }

  public static Map<String, String> mockCongigurationMap() {
    HashMap<String, String> operatorToSymbolMap = new HashMap<>();
    operatorToSymbolMap.put(">", "GT");
    operatorToSymbolMap.put(">=", "GTE");
    operatorToSymbolMap.put("<", "LT");
    operatorToSymbolMap.put("<=", "LTE");
    operatorToSymbolMap.put("==", "EQ");
    return operatorToSymbolMap;
  }

  public static DefinitionDetails mockDefinitionDetailsWithInternalStatus(
      TemplateDetails bpmnTemplateDetail,
      Authorization authorization,
      InternalStatus internalStatus) {
    String definitionId = "definition-id";
    if (Objects.isNull(internalStatus)) {
      return DefinitionDetails.builder()
          .definitionId(definitionId)
          .recordType(RecordType.INVOICE)
          .templateDetails(bpmnTemplateDetail)
          .ownerId(Long.valueOf(authorization.getRealm()))
              .createdDate(new Timestamp(new Date().getTime()))
              .status(Status.ENABLED)
          .internalStatus(null)
          .build();
    }
    switch (internalStatus) {
      case MARKED_FOR_DELETE:
      case STALE_DEFINITION:
      case MARKED_FOR_DISABLE:
        return DefinitionDetails.builder()
            .definitionId(definitionId)
            .recordType(RecordType.INVOICE)
            .templateDetails(bpmnTemplateDetail)
            .ownerId(Long.valueOf(authorization.getRealm()))
            .status(Status.DISABLED)
                .createdDate(new Timestamp(new Date().getTime()))
                .internalStatus(internalStatus)
            .build();
      default: // in case of enabled definition
        return DefinitionDetails.builder()
            .definitionId(definitionId)
            .recordType(RecordType.INVOICE)
            .templateDetails(bpmnTemplateDetail)
            .ownerId(Long.valueOf(authorization.getRealm()))
            .status(Status.ENABLED)
            .internalStatus(null)
            .build();
    }
  }

  public static DeployDefinition createDeployDefinitionRequest(
      BpmnModelInstance bpmnModelInstance, List<DmnModelInstance> dmnModelInstanceList) {
    /* Creates the bpmn temp file object */
    File bpmnFile;
    try {
      bpmnFile =
          File.createTempFile(
              MessageFormat.format(
                  "{0}{1}", WorkflowConstants.BPMN_TEMP_FILE_KEY, System.currentTimeMillis()),
              BPMN_TYPE);
      bpmnFile.deleteOnExit();
      Bpmn.writeModelToFile(bpmnFile, bpmnModelInstance);
    } catch (IOException e) {
      throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, e);
    }
    /* iterate through all the dmns and create the file for each */
    List<File> dmnFileList =
        dmnModelInstanceList.stream()
            .map(
                dmnModelInstance -> {
                  try {
                    File dmnFile =
                        File.createTempFile(
                            MessageFormat.format(
                                "{0}{1}", DMN_TEMP_FILE_KEY, System.currentTimeMillis()),
                            DMN_TYPE);
                    dmnFile.deleteOnExit();
                    Dmn.writeModelToFile(dmnFile, dmnModelInstance);
                    return dmnFile;
                  } catch (IOException e) {
                    throw new WorkflowGeneralException(WorkflowError.WRITE_FAILED, e);
                  }
                })
            .collect(Collectors.toList());
    // TODO headers
    return new DeployDefinition(dmnFileList, bpmnFile, null, DeployDefinitionResponse.class,
    		"tId_ownerId_1");
  }

  public static Definition mockDefinitionEntityNew() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
        new Trigger()
            .id(getGlobalId(TRIGGER_ID))
            .parameter(
                new InputParameter()
                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
        new Action()
            .id(getActionGlobalId(ACTION_ID))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("Amount"));
    rules.add(
        new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("Customer"));
    rules.add(new RuleLine.Rule().conditionalExpression("CONTAINS 1").parameterName("Department"));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(MAPPED_KEY_DMN).rules(rules);

    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId("decision_invoiceapproval"))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(new Template().id(getGlobalId(TEMPLATE_ID)))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(workflowStep);
  }
  public static Definition mockDefinitionEntityNewForOr() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getActionGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("Amount"));
    rules.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("Customer"));
    rules.add(new RuleLine.Rule().conditionalExpression("CONTAINS 1").parameterName("Department"));
    List<RuleLine.Rule> rules1 = new ArrayList<>();
    rules1.add(new RuleLine.Rule().conditionalExpression("GT 50").parameterName("Amount"));
    rules1.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 2,3,4").parameterName("Customer"));
    rules1.add(new RuleLine.Rule().conditionalExpression("CONTAINS 2").parameterName("Department"));

    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(MAPPED_KEY_DMN).rules(rules);
    RuleLine ruleLineDmn1 = new RuleLine().mappedActionKey(MAPPED_KEY_DMN).rules(rules1);


    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId("decision_invoiceapproval"))
                    .ruleLine(ruleLineDmn).ruleLine(ruleLineDmn1);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(workflowStep);
  }
  public static Definition mockDefinitionEntityWithCustomFieldNew() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getActionGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("3600000000000155715").parameterType(FieldTypeEnum.DOUBLE));
    rules.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("36000000000001557156").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules);

    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId("decision_customField_invoiceapproval"))
                    .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(workflowStep);
  }
  public static Definition mockDefinitionEntityWithCustomFieldNewForOr() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getActionGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("3600000000000155716").parameterType(FieldTypeEnum.STRING));
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("3600000000000155715").parameterType(FieldTypeEnum.DOUBLE));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules);
    List<RuleLine.Rule> rules1 = new ArrayList<>();
    rules1.add(new RuleLine.Rule().conditionalExpression("GT 50").parameterName("3600000000000155717").parameterType(FieldTypeEnum.DOUBLE));
    rules1.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2").parameterName("3600000000000155718").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn1 = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules1);

    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId("decision_customField_invoiceapproval"))
                    .ruleLine(ruleLineDmn).ruleLine(ruleLineDmn1);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(workflowStep);
  }


  public static Definition mockDefinitionEntityWithCustomFieldBillForOr() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getActionGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("3600000000000155715").parameterType(FieldTypeEnum.DOUBLE));
    rules.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("36000000000001557156").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules);
    List<RuleLine.Rule> rules1 = new ArrayList<>();
    rules1.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("36000000000001").parameterType(FieldTypeEnum.DOUBLE));
    rules1.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,3").parameterName("3600000001557156").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn1 = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules1);
    List<RuleLine.Rule> rules2 = new ArrayList<>();
    rules2.add(new RuleLine.Rule().conditionalExpression("GT 600").parameterName("3000000000155715").parameterType(FieldTypeEnum.DOUBLE));
    rules2.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3,5").parameterName("360000000000056").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn2 = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules2);


    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId("decision_customField_invoiceapproval"))
                    .ruleLine(ruleLineDmn).ruleLine(ruleLineDmn1).ruleLine(ruleLineDmn2);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.BILL.getRecordType())
            .workflowStep(workflowStep);
  }

  public static Definition mockDefinitionEntityWithCustomFieldBill() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
            new Trigger()
                    .id(getGlobalId(TRIGGER_ID))
                    .parameter(
                            new InputParameter()
                                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
            new Action()
                    .id(getActionGlobalId(ACTION_ID))
                    .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("3600000000000155715").parameterType(FieldTypeEnum.DOUBLE));
    rules.add(
            new RuleLine.Rule().conditionalExpression("CONTAINS 1,2,3").parameterName("36000000000001557156").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules);

    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId("decision_customField_invoiceapproval"))
                    .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.BILL.getRecordType())
            .workflowStep(workflowStep);
  }

  public static Definition mockDefinitionEntityNotContains() {
    Definition definition = mockDefinitionEntityNew();

    // Update workflow condition
    WorkflowStep workflowStep = definition.getWorkflowSteps(0);
    // Add rules
    workflowStep.getWorkflowStepCondition().getRuleLines().clear();
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new Rule().conditionalExpression("GT 500 && LT 1000").parameterName("Amount"));
    rules.add(new Rule().conditionalExpression("NOT_CONTAINS 1,2,3").parameterName("Customer"));
    rules.add(
        new Rule()
            .conditionalExpression("NOT_CONTAINS 1 && CONTAINS 2")
            .parameterName("Department"));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(MAPPED_KEY_DMN).rules(rules);
    workflowStep.getWorkflowStepCondition().ruleLine(ruleLineDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(new Template().id(getGlobalId(TEMPLATE_ID)))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(workflowStep);
  }
  public static Definition mockDefinitionEntityWithCustomFieldNotContains() {
    Definition definition = mockDefinitionEntityWithCustomFieldNew();

    // Update workflow condition
    WorkflowStep workflowStep = definition.getWorkflowSteps(0);
    // Add rules
    workflowStep.getWorkflowStepCondition().getRuleLines().clear();
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(new Rule().conditionalExpression("GT 100").parameterName("TxnAmount").parameterType(FieldTypeEnum.DOUBLE));
    rules.add(new Rule().conditionalExpression("NOT_CONTAINS 1,2,3").parameterName("36000000000001557156").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn = new RuleLine().mappedActionKey(DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION).rules(rules);
    workflowStep.getWorkflowStepCondition().ruleLine(ruleLineDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(workflowStep);
  }

  public static Optional<List<TemplateDetails>> createTemplateDetails() {
    Optional<List<TemplateDetails>> templateDetails = Optional.of(new ArrayList<TemplateDetails>());
    TemplateDetails templateDetail = new TemplateDetails();
    templateDetail.setOwnerId(123L);
    templateDetail.setRecordType(RecordType.STAGE_CONNECTION);
    templateDetail.setStatus(Status.ENABLED);
    templateDetail.setTemplateName("stageconnectionsalesforcedisconnectionDowngrade");
    templateDetails.get().add(templateDetail);
    return templateDetails;
  }

  public static Map<String, Object> getTriggerPayloadForDisconnection() {
    Map<String, Object> map = new HashMap<>();
    EventHeaders eventHeaders = new EventHeaders();
    eventHeaders.setEntityChangeType("rejected");
    eventHeaders.setEntityType(RecordType.STAGE_CONNECTION);
    eventHeaders.setEntityId("1223");
    eventHeaders.setWorkflow("downgrade");
    map.put(WorkflowConstants.EVENT_HEADERS, eventHeaders);

    Map<String, Object> entityTypeMap = new HashMap<>();
    Map<String, Object> entityObject = new HashMap<>();
    entityObject.put(WorkflowConstants.ID, "1123");
    entityTypeMap.put(RecordType.STAGE_CONNECTION.getDisplayValue(), entityObject);
    map.put(WorkflowConstants.ENTITY, entityTypeMap);
    return map;
  }

  public static Template mockDbTemplateEntity(String id, TemplateCategory templateCategory) {
    Template template = new Template();
    template.setDisplayName("Template DisplayName");
    template.setName(id);
    template.setId(GlobalId.create(REALM_ID, template.getTypeId(), id));
    template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
    return template;
  }

  public static Template mockConfigTemplateEntity(String id, TemplateCategory templateCategory) {
    Template template = new Template();
    template.setDisplayName("Template DisplayName");
    template.setName(id);
    template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
    template.setId(GlobalId.create(REALM_ID, template.getTypeId(), id));
    return template;
  }

  public static String getLocalId(String localId) {
    return localId + "_" + CUSTOM_WORKFLOW_REALM_ID + "_" + CUSTOM_WORKFLOW_GUID;
  }

  public static Definition mockCustomWorkflowDefinitionWithParameterType(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    InputParameter taskNameParameter =
        new InputParameter()
            .parameterName("TaskName")
            .fieldValue(
                String.format(
                    "Review [[%s Number]] for Amount [[Total Amount]] and Balance [[Due Date]]",
                    StringUtils.capitalize(recordType)))
            .required(false) // This should not have impact
            .parameterType(FieldTypeEnum.DOUBLE); // This should not have impact

    InputParameter closeTaskParameter =
        new InputParameter()
            .parameterName("CloseTask")
            .fieldValue("txn_paid")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);

    Action createTaskAction =
        new Action()
            .id(getGlobalId(CREATE_TASK_ACTION_ID))
            .parameter(taskNameParameter)
            .parameter(closeTaskParameter)
            .selected(true);

    InputParameter sendToParameter =
        new InputParameter()
            .parameterName("SendTo")
            .fieldValue("<EMAIL>")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
        new InputParameter()
            .parameterName("Message")
            .fieldValue("[[Invoice Number]] needs your attention")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
        new Action()
            .id(getGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
            .parameter(sendToParameter)
            .parameter(messageParameter)
            .selected(false);
    workflowStep =
        workflowStep
            .action(new ActionMapper().action(createTaskAction).actionKey("reminder"))
            .action(new ActionMapper().action(sendCompanyEmailAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new Rule().conditionalExpression("GT 500").parameterType(FieldTypeEnum.DOUBLE).parameterName("TxnAmount"))
            .rule(new Rule().conditionalExpression("BF 2").parameterType(FieldTypeEnum.DAYS).parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }
  public static Definition mockCustomWorkflowDefinition(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    InputParameter taskNameParameter =
        new InputParameter()
            .parameterName("TaskName")
            .fieldValue(
                String.format(
                    "Review [[%s Number]] for Amount [[Total Amount]] and Balance [[Due Date]]",
                    StringUtils.capitalize(recordType)))
            .required(false) // This should not have impact
            .parameterType(FieldTypeEnum.DOUBLE); // This should not have impact

    InputParameter closeTaskParameter =
        new InputParameter()
            .parameterName("CloseTask")
            .fieldValue("txn_paid")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);

    InputParameter projectTypeParameter =
        new InputParameter()
            .parameterName("ProjectType")
            .fieldValue("QB_INVOICE_REMINDER")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    Action createTaskAction =
        new Action()
            .id(getGlobalId(CREATE_TASK_ACTION_ID))
            .parameter(taskNameParameter)
            .parameter(closeTaskParameter)
            .parameter(projectTypeParameter)
            .selected(true);

    InputParameter sendToParameter =
        new InputParameter()
            .parameterName("SendTo")
            .fieldValue("<EMAIL>")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
        new InputParameter()
            .parameterName("Message")
            .fieldValue("[[Invoice Number]] needs your attention")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
        new Action()
            .id(getGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
            .parameter(sendToParameter)
            .parameter(messageParameter)
            .selected(false);
    workflowStep =
        workflowStep
            .action(new ActionMapper().action(createTaskAction).actionKey("reminder"))
            .action(new ActionMapper().action(sendCompanyEmailAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
            .rule(new Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockCustomWorkflowDefinitionWithoutCloseTaskFieldValue(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    InputParameter taskNameParameter =
            new InputParameter()
                    .parameterName("TaskName")
                    .fieldValue(
                            String.format(
                                    "Review [[%s Number]] for Amount [[Total Amount]] and Balance [[Due Date]]",
                                    StringUtils.capitalize(recordType)))
                    .required(false) // This should not have impact
                    .parameterType(FieldTypeEnum.DOUBLE); // This should not have impact

    InputParameter closeTaskParameter =
            new InputParameter()
                    .parameterName("CloseTask")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);

    InputParameter projectTypeParameter =
            new InputParameter()
                    .parameterName("ProjectType")
                    .fieldValue("QB_INVOICE_REMINDER")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    Action createTaskAction =
            new Action()
                    .id(getGlobalId(CREATE_TASK_ACTION_ID))
                    .parameter(taskNameParameter)
                    .parameter(closeTaskParameter)
                    .parameter(projectTypeParameter)
                    .selected(true);

    InputParameter sendToParameter =
            new InputParameter()
                    .parameterName("SendTo")
                    .fieldValue("<EMAIL>")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
            new InputParameter()
                    .parameterName("Message")
                    .fieldValue("[[Invoice Number]] needs your attention")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
            new Action()
                    .id(getGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
                    .parameter(sendToParameter)
                    .parameter(messageParameter)
                    .selected(false);
    workflowStep =
            workflowStep
                    .action(new ActionMapper().action(createTaskAction).actionKey("reminder"))
                    .action(new ActionMapper().action(sendCompanyEmailAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
            new RuleLine()
                    .mappedActionKey(MAPPED_ACTION_DMN)
                    .rule(new Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
                    .rule(new Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
                    .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(null)
            .recordType(recordType)
            .workflowStep(workflowStep)
            .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockProcessedCustomWorkflowDefinition(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    InputParameter taskNameParameter =
            new InputParameter()
                    .parameterName("TaskName")
                    .fieldValue(
                            String.format(
                                    "Review [[%s Number]] for Amount [[Total Amount]] and Balance [[Due Date]]",
                                    StringUtils.capitalize(recordType)))
                    .required(false) // This should not have impact
                    .parameterType(FieldTypeEnum.DOUBLE); // This should not have impact

    InputParameter closeTaskParameter =
            new InputParameter()
                    .parameterName("CloseTask")
                    .fieldValue("txn_paid")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);

    InputParameter projectTypeParameter =
            new InputParameter()
                    .parameterName("ProjectType")
                    .fieldValue("QB_INVOICE_REMINDER")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    Action createTaskAction =
            new Action()
                    .id(getActionGlobalId(CREATE_TASK_ACTION_ID))
                    .parameter(taskNameParameter)
                    .parameter(closeTaskParameter)
                    .parameter(projectTypeParameter)
                    .selected(true);

    InputParameter sendToParameter =
            new InputParameter()
                    .parameterName("SendTo")
                    .fieldValue("<EMAIL>")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
            new InputParameter()
                    .parameterName("Message")
                    .fieldValue("[[Invoice Number]] needs your attention")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
            new Action()
                    .id(getActionGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
                    .parameter(sendToParameter)
                    .parameter(messageParameter)
                    .selected(false);
    workflowStep =
            workflowStep
                    .action(new ActionMapper().action(createTaskAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
            new RuleLine()
                    .mappedActionKey(MAPPED_ACTION_DMN)
                    .rule(new Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
                    .rule(new Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
                    .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);

    RecurrenceRule recurrenceRule = new RecurrenceRule();
    recurrenceRule.setRecurType(RecurTypeEnum.MONTHLY);
    recurrenceRule.setActive(true);
    recurrenceRule.setStartDate(new DateTime());

    return new Definition()
            .name(DEFINITION_NAME)
            .template(null)
            .recordType(recordType)
            .workflowStep(workflowStep)
            .status(WorkflowStatusEnum.ENABLED)
            .recurrence(recurrenceRule);
  }

  public static Definition mockCustomWorkflowDefinition(String recordType, String actionKey) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));

    InputParameter sendToParameter =
        new InputParameter()
            .parameterName("SendTo")
            .fieldValue("<EMAIL>")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
        new InputParameter()
            .parameterName("Message")
            .fieldValue("[[Invoice Number]] needs your attention")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);

    Action sendStatementAction =
        new Action()
            .id(getGlobalId(SEND_STATEMENT_ACTION_ID))
            .parameter(sendToParameter)
            .parameter(messageParameter)
            .selected(true);
    workflowStep =
        workflowStep.action(new ActionMapper().action(sendStatementAction).actionKey(actionKey));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new Rule().conditionalExpression("CONTAINS 1").parameterName("Customer"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockCustomWorkflowDefinitionCreateTaskActionSelectedFalse(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    Action createTaskAction = new Action().id(getGlobalId(CREATE_TASK_ACTION_ID)).selected(false);

    InputParameter sendToParameter =
        new InputParameter()
            .parameterName("SendTo")
            .fieldValue("<EMAIL>")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
        new InputParameter()
            .parameterName("Message")
            .fieldValue("[[Invoice Number]] needs your attention")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
        new Action()
            .id(getActionGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
            .parameter(sendToParameter)
            .parameter(messageParameter)
            .selected(true);
    workflowStep
        .action(new ActionMapper().action(createTaskAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
            .rule(new Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockProcessedCustomWorkflowDefinitionCreateTaskActionSelectedFalse(String recordType) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));
    Action createTaskAction = new Action().id(getActionGlobalId(CREATE_TASK_ACTION_ID)).selected(false);

    InputParameter sendToParameter =
            new InputParameter()
                    .parameterName("SendTo")
                    .fieldValue("<EMAIL>")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
            new InputParameter()
                    .parameterName("Message")
                    .fieldValue("[[Invoice Number]] needs your attention")
                    .required(true) // This should not have impact
                    .parameterType(FieldTypeEnum.STRING);
    Action sendCompanyEmailAction =
            new Action()
                    .id(getActionGlobalId(SEND_COMPANY_EMAIL_ACTION_ID))
                    .parameter(sendToParameter)
                    .parameter(messageParameter)
                    .selected(true);
    workflowStep
            .action(new ActionMapper().action(createTaskAction).actionKey("reminder"));
    RuleLine ruleLineDmn =
            new RuleLine()
                    .mappedActionKey(MAPPED_ACTION_DMN)
                    .rule(new Rule().conditionalExpression("GT 500").parameterName("TxnAmount"))
                    .rule(new Rule().conditionalExpression("AF 1").parameterName("TxnDueDays"));
    WorkflowStepCondition conditionDmn =
            new WorkflowStepCondition()
                    .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
                    .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
            .name(DEFINITION_NAME)
            .template(null)
            .recordType(recordType)
            .workflowStep(workflowStep)
            .status(WorkflowStatusEnum.ENABLED);
  }

  public static CustomWorkflowConfig loadCustomConfig() throws Exception {

    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory (
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    return customWorkflowConfig;
  }

  public static OnDemandConfig loadOnDemandConfig() throws Exception {

    OnDemandConfig onDemandConfig =
            new ObjectMapper(new YAMLFactory())
                    .readValue(
                            readResourceAsString(ON_DEMAND_PATH),
                            new TypeReference<Map<String, OnDemandConfig>>() {})
                    .get("on-demand");
    return onDemandConfig;
  }

  public static CustomWorkflowConfig loadCustomConfigIncludingStatements() throws Exception {

    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                readResourceAsString(DICTIONARY_STATEMENT_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory (
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    return customWorkflowConfig;
  }

  public static Map<String,Object> getMockedPlaceholderValue() {
    Map<String,Object> mockedPlaceholderValue = new HashMap<>();
    Map<String,Object> mockedBpmnPlaceholderValue = new HashMap<>();
    Map<String,Object> mockedDmnPlaceholderValue = new HashMap<>();
    Map<String,Object> mockedProcessVariables = new HashMap<>();
    Map<String,Object> mockedUserVariables =  new HashMap<>();
    Map<String,Object> mockedUserVariablesParameter = new HashMap<>();
    Map<String,Object> mockedConsolidatedUserVariablesParameters = new HashMap<>();
    Map<String,Object> mockedRuleline = new HashMap<>();

    ProcessVariableData mockedProcessVariableData = ProcessVariableData.builder().value("true").type("String").build();
    mockedProcessVariables.put("createTask",mockedProcessVariableData);

    ParameterDetails mockedUserVariableParameterDetail = new ParameterDetails();
    mockedUserVariableParameterDetail.setFieldValue(Arrays.asList("txn_paid"));
    mockedUserVariablesParameter.put("CloseTask", mockedUserVariableParameterDetail);
    mockedConsolidatedUserVariablesParameters.put(PARAMETERS, mockedUserVariablesParameter);
    mockedConsolidatedUserVariablesParameters.put(SELECTED, true);
    mockedUserVariables.put("createTask", mockedConsolidatedUserVariablesParameters);

    mockedBpmnPlaceholderValue.put(PROCESS_VARIABLES,mockedProcessVariables);
    mockedBpmnPlaceholderValue.put(USER_VARIABLES, mockedUserVariables);

    mockedRuleline.put(RULE_LINE_VARIABLES, Collections.singletonMap("TxnAmount", "GT 500"));
    mockedDmnPlaceholderValue.put(DMN_PLACEHOLDER_VALUES, mockedRuleline);

    mockedPlaceholderValue.put(BPMN_PLACEHOLDER_VALUES, mockedBpmnPlaceholderValue);
    mockedPlaceholderValue.put(DMN_PLACEHOLDER_VALUES, mockedDmnPlaceholderValue);
    return  mockedPlaceholderValue;
  }

  public static Definition mockStatementWorkflowDefinition(String recordType, String actionKey) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(CUSTOM_WORKFLOW_STEP_ID));
    workflowStep.setTrigger(new Trigger().id(getGlobalId(CUSTOM_WORKFLOW_TRIGGER_ID)));

    InputParameter sendToParameter =
        new InputParameter()
            .parameterName("SendTo")
            .fieldValue("<EMAIL>")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);
    InputParameter messageParameter =
        new InputParameter()
            .parameterName("Message")
            .fieldValue("[[Invoice Number]] needs your attention")
            .required(true) // This should not have impact
            .parameterType(FieldTypeEnum.STRING);

    Action sendStatementAction =
        new Action()
            .id(getGlobalId(SEND_STATEMENT_ACTION_ID))
            .parameter(sendToParameter)
            .parameter(messageParameter)
            .selected(true);
    workflowStep =
        workflowStep.action(new ActionMapper().action(sendStatementAction).actionKey(actionKey));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_ACTION_DMN)
            .rule(new Rule().conditionalExpression("CONTAINS 1").parameterName("Customer"));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition()
            .id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn);
    workflowStep = workflowStep.workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DEFINITION_NAME)
        .template(null)
        .recordType(recordType)
        .workflowStep(workflowStep)
        .status(WorkflowStatusEnum.ENABLED);
  }

  public static Definition mockDefinitionEntityDisabled() {
    return new Definition()
            .name(DEFINITION_NAME)
            .template(new Template().id(getGlobalId(TEMPLATE_ID)))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowStep(getMockedWorkflowStep())
            .status(WorkflowStatusEnum.DISABLED);
  }

  public static DefinitionActivityDetail mockDefinitionActivityDetails(
      String activityId,
      JSONObject userAttributes) {
    return DefinitionActivityDetail.builder()
        .id(UUID.randomUUID().toString())
        .activityId(activityId)
        .userAttributes(Objects.nonNull(userAttributes) ? String.valueOf(userAttributes) : null)
        .parentActivityDetail(
            DefinitionActivityDetail.builder()
                .id(UUID.randomUUID().toString())
                .build()
        )
        .childActivityDetails(new ArrayList<>())
        .build();
  }

  public static JSONObject getMockUserAttributes(boolean selected) {
    JSONObject userAttributes = new JSONObject();

    JSONObject assignee = new JSONObject();
    assignee.put(WorkflowConstants.FIELD_VALUE, "33333");

    userAttributes.put(WorkflowConstants.ASSIGNEE, assignee);
    userAttributes.put(SELECTED, selected ? "true" : "false");

    return userAttributes;
  }

  public static JSONObject getMockUserAttributesForRecurringReminder(boolean isRecurring,
      Integer recurFrequency, Integer maxScheduleCount) {
    JSONObject userAttributes = new JSONObject();

    JSONObject parameters = new JSONObject();

    JSONObject isRecurringObject = new JSONObject();
    isRecurringObject.put(WorkflowConstants.FIELD_VALUE, Collections.singletonList(isRecurring ? "true" : "false"));

    JSONObject recurFrequencyObject = new JSONObject();
    if (Objects.nonNull(recurFrequency)) {
      recurFrequencyObject.put(WorkflowConstants.FIELD_VALUE, Collections.singletonList(recurFrequency));
    }

    JSONObject maxScheduleCountObject = new JSONObject();
    if (Objects.nonNull(maxScheduleCount)) {
      maxScheduleCountObject.put(WorkflowConstants.FIELD_VALUE, Collections.singletonList(maxScheduleCount));
    }

    parameters.put(WorkflowConstants.IS_RECURRING_ENABLED, isRecurringObject.toString());
    parameters.put(WorkflowConstants.RECUR_FREQUENCY, recurFrequencyObject.toString());
    parameters.put(WorkflowConstants.MAX_SCHEDULE_COUNT, maxScheduleCountObject.toString());

    userAttributes.put("parameters", parameters);
    return userAttributes;
  }

  public static TranslationService initTranslationService() {
    LocalisationConfig localisationConfig = new LocalisationConfig();
    localisationConfig.setBasePath("templates.sla.");
    localisationConfig.setFolderName("CustomWorkflowTemplate");
    localisationConfig.setFileName("workflowContent");
    ClassloaderResourceBundleLoader classloaderResourceBundleLoader =
        new ClassloaderResourceBundleLoader();
    ResourceBundleLoaderFactory.addLoader(
        classloaderResourceBundleLoader.getType(), classloaderResourceBundleLoader);
    AppLocalisationResourceBundle appLocalisationResourceBundle =
        new AppLocalisationResourceBundle(new LocalizedResourceBundle(), localisationConfig);
    ExternalServiceMapping externalServiceMapping = new ExternalServiceMapping();
    externalServiceMapping.setUrls(
        Map.of(
            "taskManagerUrl",
            " https://app.qal.qbo.intuit.com/app/taskmanager",
            "bankDepositReminder",
            "https://silver-develop.qbo.intuit.com/login?pagereq=https://silver-develop.qbo.intuit.com/app/deposit"));
    TranslationService translationService =
        new TranslationService(
                appLocalisationResourceBundle,
            List.of(
                new HelpVariableRecordTypeTokenHandler(),
                new RecordTypeTokenHandler(appLocalisationResourceBundle),
                new UrlTokenHandler(externalServiceMapping)));
    translationService.setTokenHandlerMap();
    return translationService;
  }

  public static Definition mockSingleStepMultiConditionDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");
    workflowSteps.add(firstActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");

    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");

    WorkflowStep fourthConditionStep = createMultiConditionStep("condition-4",
        secondActionStep.getId().toString(), firstActionStep.getId().toString(),
        "> 500","TxnAmount" );

    WorkflowStep thirdConditionStep = createMultiConditionStep("condition-3",
        fourthConditionStep.getId().toString(), null, "CONTAINS ALL_Customer",
        "Customer");

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        null, thirdConditionStep.getId().toString(), "BTW 100,1000",
        "TxnAmount");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(), firstActionStep.getId().toString(), "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(fourthConditionStep);

    return new Definition()
            .name(DefinitionTestConstants.DEFINITION_NAME)
            .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
                    .name(CustomWorkflowType.APPROVAL.getTemplateName()))
            .recordType(RecordType.INVOICE.getRecordType())
            .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionDefinitionEntityWithCustomFields() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");

    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");

    WorkflowStep fourthConditionStep = createMultiConditionStep("condition-4",
        secondActionStep.getId().toString(), firstActionStep.getId().toString(),
        "CONTAINS yash","302300000000000282303" );
    fourthConditionStep.getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0).setParameterType(FieldTypeEnum.STRING);

    WorkflowStep thirdConditionStep = createMultiConditionStep("condition-3",
        fourthConditionStep.getId().toString(), null, "CONTAINS 1,2",
        "302300000000000329561");
    thirdConditionStep.getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0).setParameterType(FieldTypeEnum.LIST);

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        null, thirdConditionStep.getId().toString(), "BTW 100,1000",
        "TxnAmount");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(), firstActionStep.getId().toString(), "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(fourthConditionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiSplitDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");

    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");
    WorkflowStep thirdActionStep = createMultiActionStep("actionStep-3");
    WorkflowStep fourthActionStep = createMultiActionStep("actionStep-4");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        firstActionStep.getId().toString(), null,
        "> 500","TxnAmount" );

    StepNext stepNext = new StepNext();
    stepNext.setWorkflowStepId(secondActionStep.getId().toString());
    stepNext.setLabel(NextLabelEnum.YES);
    firstConditionStep.getNext().add(stepNext);

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        thirdActionStep.getId().toString(), null, "> 1000",
        "TxnAmount");

    stepNext = new StepNext();
    stepNext.setWorkflowStepId(fourthActionStep.getId().toString());
    stepNext.setLabel(NextLabelEnum.YES);
    firstConditionStep.getNext().add(stepNext);

    secondConditionStep.getNext().add(stepNext);

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(thirdActionStep);
    workflowSteps.add(fourthActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiSplitNoPathDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");

    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");
    WorkflowStep thirdActionStep = createMultiActionStep("actionStep-3");
    WorkflowStep fourthActionStep = createMultiActionStep("actionStep-4");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        null, firstActionStep.getId().toString(),
        "> 500","TxnAmount" );

    StepNext stepNext = new StepNext();
    stepNext.setWorkflowStepId(secondActionStep.getId().toString());
    stepNext.setLabel(NextLabelEnum.YES);
    firstConditionStep.getNext().add(stepNext);

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
       null , thirdActionStep.getId().toString(), "> 1000",
        "TxnAmount");

    stepNext = new StepNext();
    stepNext.setWorkflowStepId(fourthActionStep.getId().toString());
    stepNext.setLabel(NextLabelEnum.YES);
    firstConditionStep.getNext().add(stepNext);

    secondConditionStep.getNext().add(stepNext);

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(thirdActionStep);
    workflowSteps.add(fourthActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static ActionGroup createActionGroup(String actionId, String actionKey) {
    Action action =
        new Action()
            .id(getGlobalId(actionId))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));

    Action createTaskSubAction =
        new Action()
            .id(getGlobalId("createTask"))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));

    List<Action> subActions = new ArrayList<>();
    subActions.add(createTaskSubAction);
    action.setSubActions(subActions);

    return new ActionGroup().actionKey(actionKey).action(action);
  }

  public static WorkflowStep createMultiActionStep(String stepId) {

    ActionGroup actionGroup = createActionGroup("sendForApproval", "approval");

    return new WorkflowStep().id(getGlobalId(stepId))
            .actionGroup(actionGroup)
            .stepType(StepTypeEnum.ACTION);
  }

  public static WorkflowStep createMultiConditionStep(String stepId, String yesPathId,
      String noPathId, String conditionalExpression, String parameterName) {
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression(conditionalExpression)
                    .parameterName(parameterName));

    WorkflowStepCondition workflowStepCondition =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn);

    List<WorkflowStep.StepNext> stepNexts = getWorkflowStepNextListFromChildPaths(yesPathId, noPathId);

    WorkflowStep conditionStep =
        new WorkflowStep().id(getGlobalId(stepId))
            .workflowStepCondition(workflowStepCondition)
            .stepType(StepTypeEnum.CONDITION)
            .next(stepNexts);

    return conditionStep;
  }

  public static WorkflowStep createMultiWorkflowStep(String stepId, String conditionalExpression, String parameterName) {

    WorkflowStep compositeWorkflowStep = createMultiConditionStep(stepId, null, null, conditionalExpression, parameterName);
    compositeWorkflowStep.setStepType(StepTypeEnum.WORFKLOWSTEP);
    compositeWorkflowStep.setActionGroup(createActionGroup("sendForReminder", "reminder"));

    return compositeWorkflowStep;
  }

  public static List<WorkflowStep.StepNext> getWorkflowStepNextListFromChildPaths(String yesPathId, String noPathId){
    List<WorkflowStep.StepNext> stepNextList = new ArrayList<>();

    if(ObjectUtils.isNotEmpty(yesPathId)) {

      WorkflowStep.StepNext yesPath = new WorkflowStep.StepNext()
          .workflowStepId(yesPathId)
          .label(NextLabelEnum.YES);

      stepNextList.add(yesPath);
    }

    if(ObjectUtils.isNotEmpty(noPathId)) {

      WorkflowStep.StepNext noPath = new WorkflowStep.StepNext()
          .workflowStepId(noPathId)
          .label(NextLabelEnum.NO);

      stepNextList.add(noPath);
    }

    return stepNextList;
  }

  public static TemplateDetails mockTemplateDetailsObject() {
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customApproval");
    templateDetails.setId("a947e806-b32f-48b9-8333-70d26fa57351");
    templateDetails.setRecordType(RecordType.INVOICE);
    templateDetails.setTemplateCategory("CUSTOM");
    templateDetails.setVersion(1);
    templateDetails.setOfferingId("Intuit.appintgwkflw.wkflautomate.workflowappconnect");
    templateDetails.setCreatorType(CreatorType.SYSTEM);
    templateDetails.setStatus(Status.ENABLED);
    templateDetails.setDeployedDefinitionId("customApproval:10:e4eacf71-b1c9-11ed-8946-f6ebc0857423");
    templateDetails.setDefinitionType(DefinitionType.SINGLE);
    templateDetails.setOwnerId(9130358689435546L);
    templateDetails.setModelType(ModelType.BPMN);
    templateDetails.setCreatedByUserId(9130358689435516L);
    templateDetails.setModifiedByUserId(9130358689435516L);
    templateDetails.setDisplayName("Multi Condition Approval");
    templateDetails.setAllowMultipleDefinitions(false);
    return templateDetails;
  }

  public static ActivityInstance mockActivityInstance() {
    String parentActivityAttrs = "{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273350856\"]}}}}";
    JSONObject placeholderValue = new JSONObject(parentActivityAttrs);
    Map<String, Object> placeholderMap = placeholderValue.getJSONObject(WorkflowConstants.USER_ATTRIBUTES).toMap();
    ActivityInstance activityInstance = ActivityInstance.builder()
            .dmnModelInstance(null)
            .userAttributes(placeholderMap)
            .build();

    Map<String, ActivityInstance> childActivityInstances = new HashMap<>();

    String childActivityAttrs1 = "{\"userAttributes\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130359273349816\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"close_manually\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}}";
    JSONObject childPlaceholderValue1 = new JSONObject(childActivityAttrs1);
    Map<String, Object> childPlaceholderMap1 = childPlaceholderValue1.getJSONObject(WorkflowConstants.USER_ATTRIBUTES).toMap();
    ActivityInstance childActivityInstance1 = ActivityInstance.builder()
            .dmnModelInstance(null)
            .userAttributes(childPlaceholderMap1)
            .build();
    childActivityInstances.put("createTask", childActivityInstance1);

    String childActivityAttrs2 = "{\"userAttributes\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}}";
    JSONObject childPlaceholderValue2 = new JSONObject(childActivityAttrs2);
    Map<String, Object> childPlaceholderMap2 = childPlaceholderValue2.getJSONObject(WorkflowConstants.USER_ATTRIBUTES).toMap();
    ActivityInstance childActivityInstance2 = ActivityInstance.builder()
            .dmnModelInstance(null)
            .userAttributes(childPlaceholderMap2)
            .build();
    childActivityInstances.put("sendCompanyEmail", childActivityInstance2);

    String childActivityAttrs3 = "{\"userAttributes\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"[[Company Email]]\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}";
    JSONObject childPlaceholderValue3 = new JSONObject(childActivityAttrs3);
    Map<String, Object> childPlaceholderMap3 = childPlaceholderValue3.getJSONObject(WorkflowConstants.USER_ATTRIBUTES).toMap();
    ActivityInstance childActivityInstance3 = ActivityInstance.builder()
            .dmnModelInstance(null)
            .userAttributes(childPlaceholderMap3)
            .build();
    childActivityInstances.put("sendPushNotification", childActivityInstance3);
    activityInstance.setChildActivityInstances(childActivityInstances);
    return activityInstance;
  }


  public static Definition mockMultiConditionDefinitionEntityWithMoreThan6Approvers() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");
    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");
    WorkflowStep thirdActionStep = createMultiActionStep("actionStep-3");
    WorkflowStep fourthActionStep = createMultiActionStep("actionStep-4");
    WorkflowStep fifthActionStep = createMultiActionStep("actionStep-5");
    WorkflowStep sixthActionStep = createMultiActionStep("actionStep-6");
    WorkflowStep seventhActionStep = createMultiActionStep("actionStep-7");

    WorkflowStep seventhConditionStep = createMultiConditionStep("condition-7",
        seventhActionStep.getId().toString(), null,
        "> 8000","TxnAmount" );

    WorkflowStep sixthConditionStep = createMultiConditionStep("condition-6",
        seventhConditionStep.getId().toString(), sixthActionStep.getId().toString(),
        "> 7000","TxnAmount" );

    WorkflowStep fifthConditionStep = createMultiConditionStep("condition-5",
        sixthConditionStep.getId().toString(), fifthActionStep.getId().toString(),
        "> 6000","TxnAmount" );

    WorkflowStep fourthConditionStep = createMultiConditionStep("condition-4",
        fifthConditionStep.getId().toString(), fourthActionStep.getId().toString(),
        "> 5000","TxnAmount" );

    WorkflowStep thirdConditionStep = createMultiConditionStep("condition-3",
        fourthConditionStep.getId().toString(), thirdActionStep.getId().toString(), "CONTAINS ALL_Customer",
        "Customer");

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        thirdConditionStep.getId().toString(),secondActionStep.getId().toString(), "BTW 1000,2000",
        "TxnAmount");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(), firstActionStep.getId().toString(), "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdActionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(fourthActionStep);
    workflowSteps.add(fourthConditionStep);
    workflowSteps.add(fifthActionStep);
    workflowSteps.add(fifthConditionStep);
    workflowSteps.add(sixthActionStep);
    workflowSteps.add(sixthConditionStep);
    workflowSteps.add(seventhActionStep);
    workflowSteps.add(seventhConditionStep);


    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionDefinitionEntityWithMultipleDmn() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");
    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");
    WorkflowStep thirdActionStep = createMultiActionStep("actionStep-3");
    WorkflowStep fourthActionStep = createMultiActionStep("actionStep-4");
    WorkflowStep fifthActionStep = createMultiActionStep("actionStep-5");
    WorkflowStep sixthActionStep = createMultiActionStep("actionStep-6");
    WorkflowStep seventhActionStep = createMultiActionStep("actionStep-7");
    WorkflowStep eighthActionStep = createMultiActionStep("actionStep-8");
    WorkflowStep ninthActionStep = createMultiActionStep("actionStep-9");

    WorkflowStep seventhConditionStep = createMultiConditionStep("condition-7",
        eighthActionStep.getId().toString(), ninthActionStep.getId().toString(),
        "< 250","TxnAmount" );

    firstActionStep.setNext(getWorkflowStepNextListFromChildPaths(seventhConditionStep.getId().toString(), null));

    WorkflowStep sixthConditionStep = createMultiConditionStep("condition-6",
        seventhActionStep.getId().toString(), sixthActionStep.getId().toString(),
        "> 7000","TxnAmount" );

    WorkflowStep fifthConditionStep = createMultiConditionStep("condition-5",
        sixthConditionStep.getId().toString(), fifthActionStep.getId().toString(),
        "> 6000","TxnAmount" );

    WorkflowStep fourthConditionStep = createMultiConditionStep("condition-4",
        fifthConditionStep.getId().toString(), fourthActionStep.getId().toString(),
        "> 5000","TxnAmount" );

    WorkflowStep thirdConditionStep = createMultiConditionStep("condition-3",
        fourthConditionStep.getId().toString(), thirdActionStep.getId().toString(), "CONTAINS ALL_Customer",
        "Customer");

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        thirdConditionStep.getId().toString(),secondActionStep.getId().toString(), "BTW 1000,2000",
        "TxnAmount");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(), firstActionStep.getId().toString(), "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdActionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(fourthActionStep);
    workflowSteps.add(fourthConditionStep);
    workflowSteps.add(fifthActionStep);
    workflowSteps.add(fifthConditionStep);
    workflowSteps.add(sixthActionStep);
    workflowSteps.add(sixthConditionStep);
    workflowSteps.add(seventhActionStep);
    workflowSteps.add(seventhConditionStep);
    workflowSteps.add(eighthActionStep);
    workflowSteps.add(ninthActionStep);


    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockSingleStepRecurringReminderDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstCompositeWorkflowStep = createMultiWorkflowStep("compositeStep-1", "BF 2", "TxnDueDays");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        firstCompositeWorkflowStep.getId().toString(),null, "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstCompositeWorkflowStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.REMINDER.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiStepRecurringReminderDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstCompositeWorkflowStep =
        createMultiWorkflowStep("compositeStep-1", "BF 2", "TxnDueDays");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        firstCompositeWorkflowStep.getId().toString(),null, "> 500",
        "TxnAmount");

    WorkflowStep secondCompositeWorkflowStep =
        createMultiWorkflowStep("compositeStep-2", "BF 4", "TxnDueDays");

    firstCompositeWorkflowStep.setNext(getWorkflowStepNextListFromChildPaths(
        secondCompositeWorkflowStep.getId().toString(), null));

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstCompositeWorkflowStep);
    workflowSteps.add(secondCompositeWorkflowStep);


    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.REMINDER.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionRecurringReminder() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep =
        createMultiActionStep("actionStep-1");
    WorkflowStep secondActionStep =
        createMultiActionStep("actionStep-2");

    WorkflowStep secondConditionStep = createMultiConditionStep("conditionStep-2",firstActionStep.getId().toString(), null,"BF 2", "TxnDueDays");
    WorkflowStep thirdConditionStep = createMultiConditionStep("conditionStep-3",secondActionStep.getId().toString(), null,"BF 4", "TxnDueDays");


    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(),thirdConditionStep.getId().toString(), "GT 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.REMINDER.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionRecurringReminderWithMultipleconditionsInSameNode() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep =
        createMultiActionStep("actionStep-1");
    WorkflowStep secondActionStep =
        createMultiActionStep("actionStep-2");

    WorkflowStep secondConditionStep = createMultiConditionStep("conditionStep-2",firstActionStep.getId().toString(), null,"BF 2", "TxnDueDays");
    WorkflowStep thirdConditionStep = createMultiConditionStep("conditionStep-3",secondActionStep.getId().toString(), null,"BF 4", "TxnDueDays");


    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(),thirdConditionStep.getId().toString(), "GT 500",
        "TxnAmount");
    firstConditionStep.getWorkflowStepCondition().getRuleLines(0).getRules().add(new Rule().conditionalExpression("CONTAINS 123")
        .parameterName("Project"));

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.REMINDER.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static ActivityDetail getTestStartEventActivityDetail() {
    ActivityDetail activityDetail = new ActivityDetail();
    activityDetail.setId(1L);
    activityDetail.setActivityId("startEvent");
    activityDetail.setActivityType(BpmnComponentType.START_EVENT.getName());
    activityDetail.setAttributes(ACTIVITY_ATTRIBUTES);
    return activityDetail;
  }

}