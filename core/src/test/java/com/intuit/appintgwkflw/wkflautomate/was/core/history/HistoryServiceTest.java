package com.intuit.appintgwkflw.wkflautomate.was.core.history;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_ERROR;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import java.util.UUID;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HistoryServiceTest {

  private static final String PROCESS_INSTANCE_ID = UUID.randomUUID().toString();

  @Mock
  private BPMNEngineHistoryServiceRest bpmnEngineHistoryServiceRest;

  @InjectMocks
  private HistoryServiceImpl historyService;

  @Test(expected = WorkflowGeneralException.class)
  public void whenGetProcessDetails_andBpmnEngineException_thenThrowException() {

    doThrow(new WorkflowGeneralException(PROCESS_DETAILS_ERROR)).when(bpmnEngineHistoryServiceRest)
        .getProcessDetails(any());

    historyService.getProcessDetails(PROCESS_INSTANCE_ID);
  }

  @Test
  public void whenGetProcessDetails_andSuccess_thenReturnSuccessResponse() {

    doReturn(WASHttpResponse.builder().build()).when(bpmnEngineHistoryServiceRest)
        .getProcessDetails(any());

    final WorkflowGenericResponse response = historyService.getProcessDetails(PROCESS_INSTANCE_ID);
    assertNotNull(response);
    assertEquals(ResponseStatus.SUCCESS, response.getStatus());
  }
  
	@Test(expected = WorkflowGeneralException.class)
	public void testGetProcessVariableDetails_exception() {

		doThrow(new WorkflowGeneralException(WorkflowError.PROCESS_VARIABLE_DETAILS_ERROR))
				.when(bpmnEngineHistoryServiceRest).getProcessVariableDetails(any());

		historyService.getProcessVariableDetails(new ProcessVariableDetailsRequest());
	}

	@Test
	public void testGetProcessVariableDetails_success() {

		doReturn(WASHttpResponse.builder().build()).when(bpmnEngineHistoryServiceRest).getProcessVariableDetails(any());

		final WorkflowGenericResponse response = historyService
				.getProcessVariableDetails(new ProcessVariableDetailsRequest());
		assertNotNull(response);
		assertEquals(ResponseStatus.SUCCESS, response.getStatus());
	}

}