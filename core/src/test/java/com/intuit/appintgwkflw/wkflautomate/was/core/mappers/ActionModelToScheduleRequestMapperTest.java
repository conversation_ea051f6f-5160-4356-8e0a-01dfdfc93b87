package com.intuit.appintgwkflw.wkflautomate.was.core.mappers;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mapstruct.factory.Mappers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.EndType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.RecurrenceType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.WeekOfMonth;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;

/** <AUTHOR> */
public class ActionModelToScheduleRequestMapperTest {
    private ActionModelToScheduleRequestMapper mapper;

    @Before
    public void setUp() {
        mapper = Mappers.getMapper(ActionModelToScheduleRequestMapper.class);
    }

    @Test
    public void test_convertToScheduleRequest() {
        EventScheduleWorkflowActionModel model =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = mapper.convertToScheduleRequest(model, Status.ACTIVE);

        assertNotNull(request);
        assertNotNull(request.getScheduleInfo());
    }

    @Test
    public void test_convertToScheduleInfo() {
        RecurrenceRule rule = new RecurrenceRule();
        rule.setRecurType(RecurTypeEnum.BIMONTHLY);
        rule.setDaysOfWeek(Collections.singletonList(DayOfWeekEnum.MONDAY));
        rule.setMonthOfYear(MonthsOfYearEnum.JANUARY);
        rule.setTimeZone("UTC");

        ScheduleInfo info = mapper.convertToScheduleInfo(rule);

        assertNotNull(info);
        assertEquals(RecurrenceType.MONTHLY, info.getRecurrenceType());
        assertEquals(DayOfWeekEnum.MONDAY, info.getDaysOfWeek().get(0));
        assertEquals(Integer.valueOf(1), info.getMonthOfYear());
        assertEquals(ZoneId.of("UTC"), info.getZoneId());
    }

    @Test
    public void test_mapRecurTypeEnumToRecurrenceType() {
        assertEquals(RecurrenceType.MONTHLY, mapper.mapRecurTypeEnumToRecurrenceType(RecurTypeEnum.BIMONTHLY));
        assertEquals(RecurrenceType.DAILY, mapper.mapRecurTypeEnumToRecurrenceType(RecurTypeEnum.ANY_ACTIVITY));
    }

    @Test
    public void test_stringToEnum() {
        List<DayOfWeekEnum> daysOfWeek = Collections.singletonList(DayOfWeekEnum.MONDAY);
        assertEquals(DayOfWeek.MONDAY, ActionModelToScheduleRequestMapper.stringToEnum(daysOfWeek));
    }

    @Test
    public void test_monthOfYearEnumToInteger() {
        assertEquals(Integer.valueOf(1), ActionModelToScheduleRequestMapper.monthOfYearEnumToInteger(MonthsOfYearEnum.JANUARY));
    }

    @Test
    public void test_mapWeekOfMonthEnumToWeekOfMonth() {
        assertEquals(WeekOfMonth.FIRST, mapper.mapWeekOfMonthEnumToWeekOfMonth(WeekOfMonthEnum.DAY));
    }

    @Test
    public void test_stringToZoneId() {
        assertEquals(ZoneId.of("UTC"), ActionModelToScheduleRequestMapper.stringToZoneId("UTC"));
    }

    @Test
    public void test_convertToScheduleRequest_withNullModel() {
        SchedulingSvcRequest request = mapper.convertToScheduleRequest(null, Status.ACTIVE);
        assertNotNull(request);
        assertNull(request.getScheduleInfo());
    }

    @Test
    public void test_convertToScheduleRequest_withNullStatus() {
        EventScheduleWorkflowActionModel model =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = mapper.convertToScheduleRequest(model, null);
        assertNotNull(request);
        assertNotNull(request.getScheduleInfo());
    }

    @Test
    public void test_convertToScheduleInfo_withNullRecurrenceRule() {
        ScheduleInfo info = mapper.convertToScheduleInfo(null);
        assertNull(info);
    }

    @Test
    public void test_mapRecurTypeEnumToRecurrenceType_withNull() {
        RecurrenceType recurrenceType = mapper.mapRecurTypeEnumToRecurrenceType(null);
        assertNull(recurrenceType);
    }

    @Test
    public void test_stringToEnum_withNull() {
        DayOfWeek dayOfWeek = ActionModelToScheduleRequestMapper.stringToEnum(null);
        assertNull(dayOfWeek);
    }

    @Test
    public void test_stringToEnum_withEmptyList() {
        DayOfWeek dayOfWeek = ActionModelToScheduleRequestMapper.stringToEnum(Collections.emptyList());
        assertNull(dayOfWeek);
    }

    @Test
    public void test_monthsOfYearEnumToInteger_withNull() {
        Integer month = ActionModelToScheduleRequestMapper.monthOfYearEnumToInteger(null);
        assertNull(month);
    }

    @Test
    public void test_mapWeekOfMonthEnumToWeekOfMonth_withNull() {
        WeekOfMonth weekOfMonth = mapper.mapWeekOfMonthEnumToWeekOfMonth(null);
        assertNull(weekOfMonth);
    }

    @Test
    public void test_stringToZoneId_withNull() {
        ZoneId zoneId = ActionModelToScheduleRequestMapper.stringToZoneId(null);
        assertEquals(zoneId,null);
    }
    
    @Test
    public void test_mapEndType_withEndDate() {
        EndType endType = ActionModelToScheduleRequestMapper.mapEndType(DateTime.now(),null);
        assertEquals(EndType.END_BY_DATE,endType);
    }
    
    @Test
    public void test_mapEndType_withOccurences() {
        EndType endType = ActionModelToScheduleRequestMapper.mapEndType(null,5);
        assertEquals(EndType.END_AFTER_OCCURRENCES,endType);
    }
    
    @Test
    public void test_mapEndType_withDateTimeAndoccurences() {
        EndType endType = ActionModelToScheduleRequestMapper.mapEndType(DateTime.now(),5);
        assertEquals(EndType.END_BY_DATE,endType);
    }
    
    @Test
    public void test_mapEndType_withNUll() {
        EndType endType = ActionModelToScheduleRequestMapper.mapEndType(null,null);
        assertEquals(EndType.NO_END_DATE,endType);
    }
    
    @Test
    public void test_buildDateTime_startDateNull() {
        DateTime dateTime = ActionModelToScheduleRequestMapper.buildDateTime(null, null);
        assertNull(dateTime);
    }
    
    @Test
    public void test_buildDateTime_TimeDurationNull() {
        DateTime dateTime = ActionModelToScheduleRequestMapper.buildDateTime(DateTime.now(), null);
        assertNotNull(dateTime);
    }
    
	@Test
	public void test_buildDateTime_TimeDurationAndStartDate() {
		DateTime inputDateTime = DateTime.now();
		DateTime dateTime = ActionModelToScheduleRequestMapper.buildDateTime(inputDateTime,
				new TimeDuration().hours(9).minutes(30));
		assertNotNull(dateTime);
		int expectedHrs = (30 + inputDateTime.getMinuteOfHour())/60;
		assertEquals((30 + inputDateTime.getMinuteOfHour())%60, dateTime.getMinuteOfHour());
		assertEquals(expectedHrs + (9 + inputDateTime.getHourOfDay())%24, dateTime.getHourOfDay());
	}
	
	@Test
	public void test_buildDateTime_TimeDurationAndStartDate_0() {
		DateTime inputDateTime = DateTime.now();
		DateTime dateTime = ActionModelToScheduleRequestMapper.buildDateTime(DateTime.now(),
				new TimeDuration());
		assertNotNull(dateTime);
		assertEquals(inputDateTime.getMinuteOfHour(), dateTime.getMinuteOfHour());
		assertEquals(inputDateTime.getHourOfDay(), dateTime.getHourOfDay());
	}

    @Test
    public void test_monthsOfYearEnumToInteger_withValidValues() {
        List<MonthsOfYearEnum> input = Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.FEBRUARY, MonthsOfYearEnum.MARCH);
        List<Integer> expected = Arrays.asList(1, 2, 3);
        List<Integer> result = ActionModelToScheduleRequestMapper.monthsOfYearEnumToInteger(input);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void test_monthsOfYearEnumToInteger_withEmptyList() {
        List<MonthsOfYearEnum> input = Collections.emptyList();
        List<Integer> result = ActionModelToScheduleRequestMapper.monthsOfYearEnumToInteger(input);
        Assertions.assertEquals(null, result);
    }

    @Test
    public void test_daysOfWeekMapping_withNonEmptyList() {
        List<DayOfWeekEnum> input = Collections.singletonList(DayOfWeekEnum.MONDAY);
        List<DayOfWeekEnum> result = ActionModelToScheduleRequestMapper.daysOfWeekMapping(input);
        Assertions.assertEquals(input, result);
    }

    @Test
    public void test_daysOfWeekMapping_withEmptyList() {
        List<DayOfWeekEnum> result = ActionModelToScheduleRequestMapper.daysOfWeekMapping(Collections.emptyList());
        Assertions.assertNull(result);
    }

    @Test
    public void test_daysOfWeekMapping_withNull() {
        List<DayOfWeekEnum> result = ActionModelToScheduleRequestMapper.daysOfWeekMapping(null);
        Assertions.assertNull(result);
    }

    @Test
    public void test_daysOfMonthMapping_withNonEmptyList() {
        List<Integer> input = Collections.singletonList(1);
        List<Integer> result = ActionModelToScheduleRequestMapper.daysOfMonthMapping(input);
        Assertions.assertEquals(input, result);
    }

    @Test
    public void test_daysOfMonthMapping_withEmptyList() {
        List<Integer> result = ActionModelToScheduleRequestMapper.daysOfMonthMapping(Collections.emptyList());
        Assertions.assertNull(result);
    }

    @Test
    public void test_daysOfMonthMapping_withNull() {
        List<Integer> result = ActionModelToScheduleRequestMapper.daysOfMonthMapping(null);
        Assertions.assertNull(result);
    }

    @Test
    public void test_weeksOfMonthMapping_withNonEmptyList() {
        List<WeekOfMonthEnum> input = Collections.singletonList(WeekOfMonthEnum.DAY);
        List<WeekOfMonthEnum> result = ActionModelToScheduleRequestMapper.weeksOfMonthMapping(input);
        Assertions.assertEquals(input, result);
    }

    @Test
    public void test_weeksOfMonthMapping_withEmptyList() {
        List<WeekOfMonthEnum> result = ActionModelToScheduleRequestMapper.weeksOfMonthMapping(Collections.emptyList());
        Assertions.assertNull(result);
    }

    @Test
    public void test_weeksOfMonthMapping_withNull() {
        List<WeekOfMonthEnum> result = ActionModelToScheduleRequestMapper.weeksOfMonthMapping(null);
        Assertions.assertNull(result);
    }

    @Test
    public void test_buildEndDateTime_withValidDate() {
        DateTime endDate = new DateTime(2023, 10, 25, 0, 0);
        LocalDateTime expected = LocalDateTime.of(2023, 10, 25, 23, 59, 59);
        LocalDateTime result = ActionModelToScheduleRequestMapper.buildEndDateTime(endDate);
        assertEquals(expected, result);
    }

    @Test
    public void test_buildEndDateTime_withNullDate() {
        LocalDateTime result = ActionModelToScheduleRequestMapper.buildEndDateTime(null);
        assertNull(result);
    }

    @Test
    public void test_dayOfMonthMapping_withValidValues() {
        List<Integer> input = Arrays.asList(1, 2, 3, 30, 31);
        List<Integer> expected = Arrays.asList(1, 2, 3, 30, -1);
        List<Integer> result = ActionModelToScheduleRequestMapper.daysOfMonthMapping(input);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testExtractLocalTime_withValidTimeDuration() {
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(10);
        timeDuration.setMinutes(30);

        LocalTime result = ActionModelToScheduleRequestMapper.extractLocalTime(timeDuration);
        Assertions.assertEquals(LocalTime.of(10, 30), result);
    }

    @Test
    public void testExtractLocalTime_withNullTimeDuration() {
        TimeDuration timeDuration = null;

        LocalTime result = ActionModelToScheduleRequestMapper.extractLocalTime(timeDuration);
        Assertions.assertNull(result);
    }

    @Test
    public void testExtractLocalTime_withEmptyTimeDuration() {
        TimeDuration timeDuration = new TimeDuration();

        LocalTime result = ActionModelToScheduleRequestMapper.extractLocalTime(timeDuration);
        Assertions.assertNull(result);
    }

    @Test
    public void testExtractLocalTime_withNullHours() {
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setMinutes(30);

        LocalTime result = ActionModelToScheduleRequestMapper.extractLocalTime(timeDuration);
        Assertions.assertNull(result);
    }

    @Test
    public void testExtractLocalTime_withNullMinutes() {
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(10);

        LocalTime result = ActionModelToScheduleRequestMapper.extractLocalTime(timeDuration);
        Assertions.assertNull(result);
    }

    @Test
    public void test_addStartDateFromActionModel() {
        ScheduleInfo scheduleInfo = new ScheduleInfo();
        LocalDate startDate = new LocalDate(2023, 10, 25);
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel = new EventScheduleWorkflowActionModel("test", startDate, null);

        java.time.LocalDate localDate = java.time.LocalDate.of(2023, 10, 25);

        mapper.addStartDateFromActionModel(scheduleInfo, eventScheduleWorkflowActionModel);

        assertEquals(localDate, scheduleInfo.getStartDate());
        assertNull(scheduleInfo.getDayOfWeek());
        assertNull(scheduleInfo.getDayOfMonth());
        assertNull(scheduleInfo.getMonthOfYear());
    }
}
