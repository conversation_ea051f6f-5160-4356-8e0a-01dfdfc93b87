package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.aspects;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTIVITY_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CALLED_PROCESS_CREATION_RESULT;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.aop.annotations.CreateCalledProcess;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections.map.HashedMap;
import org.aspectj.lang.ProceedingJoinPoint;
import org.hibernate.exception.GenericJDBCException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.postgresql.util.PSQLException;
import org.postgresql.util.PSQLState;
import org.springframework.dao.DataIntegrityViolationException;

@RunWith(MockitoJUnitRunner.class)
public class CreateCalledProcessAspectTest {

  @Mock
  private ProcessDetailsRepository processDetailsRepository;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private V3RunTimeHelper runTimeHelper;

  @Mock
  private DomainEventService domainEventService;

  @Mock
  private MetricLogger metricLogger;

  @Mock
  private ProceedingJoinPoint joinPoint;

  @Mock
  private CreateCalledProcess createCalledProcess;

  @InjectMocks
  CreateCalledProcessAspect createCalledProcessAspect;

  @Before
  public void init() throws Throwable {
    createCalledProcessAspect = null;
    MockitoAnnotations.initMocks(this);
    Mockito.when(joinPoint.proceed()).thenReturn(
        new HashedMap(
            Map.of("activity", "true")
        )
    );
  }

  @Test
  public void testPersistenceForNonChildProcess() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(new HashedMap())
    );

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Mockito.verify(runTimeHelper, times(0))
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Assert.assertFalse(result.containsKey(getCalledProcessCreatedResultVariableName()));
  }

  @Test
  public void testChildProcessAlreadyPersistedByProcessVariableFlag() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(
                ROOT_PROCESS_INSTANCE_ID, "parentProcessId",
                getCalledProcessCreatedResultVariableName(), "true",
                ACTIVITY_ID, getCalledProcessId(),
                    WorkflowConstants.ON_DEMAND_APPROVAL, "true"
            )
        )
    );

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Mockito.verify(runTimeHelper, times(0))
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Assert.assertFalse(result.containsKey(getCalledProcessCreatedResultVariableName()));
  }

  @Test
  public void testChildProcessAlreadyPersistedByDatabaseCall() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(ROOT_PROCESS_INSTANCE_ID, "parentProcessId")
        )
    );

    Mockito.when(
        processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.of(getMockChildProcessDetails()));

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Mockito.verify(runTimeHelper, times(0))
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Assert.assertFalse(result.containsKey(getCalledProcessCreatedResultVariableName()));
  }


  @Test
  public void testParentProcessNotStoredForParentProcess() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(ROOT_PROCESS_INSTANCE_ID, "parentProcessId")
        )
    );

    Mockito.when(
        processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.empty());

    Mockito.when(
        processDetailsRepository.findByProcessId("parentProcessId")
    ).thenReturn(Optional.empty());

    try {
      createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.PROCESS_NOT_FOUND);
    }
  }

  @Test
  public void testChildProcessPersistence() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(
                ROOT_PROCESS_INSTANCE_ID, "parentProcessId",
                ACTIVITY_ID, getCalledProcessId(),
                    WorkflowConstants.ON_DEMAND_APPROVAL, "true"
            )
        )
    );

    Mockito.when(
        processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.empty());

    Mockito.when(
        processDetailsRepository.findByProcessId("parentProcessId")
    ).thenReturn(Optional.of(getMockParentProcessDetails()));

    Mockito.when(
        definitionDetailsRepository.findByDefinitionId("childDefinitionId")
    ).thenReturn(Optional.of(getMockChildDefinitionDetails()));

    Mockito
        .doReturn(getMockChildProcessDetails())
        .when(runTimeHelper)
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Mockito.verify(runTimeHelper, times(1))
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );
    Assert.assertTrue((boolean) result.get(getCalledProcessCreatedResultVariableName()));

  }

  @Test
  public void testChildProcessAlreadyPersisted() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(
                ROOT_PROCESS_INSTANCE_ID, "parentProcessId",
                ACTIVITY_ID, getCalledProcessId()
            )
        )
    );

    Mockito.when(
        processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.empty());

    Mockito.when(
        processDetailsRepository.findByProcessId("parentProcessId")
    ).thenReturn(Optional.of(getMockParentProcessDetails()));

    Mockito.when(
        definitionDetailsRepository.findByDefinitionId("childDefinitionId")
    ).thenReturn(Optional.of(getMockChildDefinitionDetails()));

    Mockito
        .doThrow(new DataIntegrityViolationException("processExists"))
        .when(runTimeHelper)
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Assert.assertNull(result.get(getCalledProcessCreatedResultVariableName()));
  }

  @Test
  public void testChildProcessSiblingsAlreadyPersisted() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
        joinPointArgs(
            Map.of(
                ROOT_PROCESS_INSTANCE_ID, "parentProcessId",
                ACTIVITY_ID, getCalledProcessId()
            )
        )
    );

    Mockito.when(
        processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.empty());

    Mockito.when(
        processDetailsRepository.findByProcessId("parentProcessId")
    ).thenReturn(Optional.of(getMockParentProcessDetails()));

    Mockito.when(
        definitionDetailsRepository.findByDefinitionId("childDefinitionId")
    ).thenReturn(Optional.of(getMockChildDefinitionDetails()));

    Mockito.when(
        processDetailsRepository.findByProcessStatusAndParentIdAndProcessIdIsNot(any(), any(), any())
    ).thenReturn(Optional.of(List.of(getMockChildProcessDetails())));

    Mockito.when(
        domainEventService.updateStatus(
            any(),
            any(),
            any(),
            any(),
            any()
        )
    ).thenReturn(1);

    Mockito
        .doReturn(getMockChildProcessDetails())
        .when(runTimeHelper)
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Map<String, Object> result =
        (Map<String, Object>) createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);


    Mockito.verify(runTimeHelper, times(1))
        .saveProcessDetailsInstance(
            anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap()
        );

    Mockito.verify(domainEventService, times(1))
        .updateStatus(
            any(),
            any(),
            any(),
            any(),
            any()
        );

    Assert.assertTrue((boolean) result.get(getCalledProcessCreatedResultVariableName()));
  }

  @Test
  public void testChildProcessWithGenericJDBCException() throws Throwable {

    Mockito.when(joinPoint.getArgs()).thenReturn(
            joinPointArgs(
                    Map.of(
                            ROOT_PROCESS_INSTANCE_ID, "parentProcessId",
                            ACTIVITY_ID, getCalledProcessId()
                    )
            )
    );

    Mockito.when(
            processDetailsRepository.findByProcessId("childProcessId")
    ).thenReturn(Optional.empty());

    Mockito.when(
            processDetailsRepository.findByProcessId("parentProcessId")
    ).thenReturn(Optional.of(getMockParentProcessDetails()));

    Mockito.when(
            definitionDetailsRepository.findByDefinitionId("childDefinitionId")
    ).thenReturn(Optional.of(getMockChildDefinitionDetails()));

    Mockito.when(
            processDetailsRepository.findByProcessStatusAndParentIdAndProcessIdIsNot(any(), any(), any())
    ).thenReturn(Optional.of(List.of(getMockChildProcessDetails())));

    Mockito.when(
            domainEventService.updateStatus(
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
            )
    ).thenReturn(1);

    GenericJDBCException genericJDBCException = new GenericJDBCException("could not execute statement",
            new PSQLException("ERROR: current transaction is aborted, commands ignored until end of transaction block", PSQLState.IN_FAILED_SQL_TRANSACTION));

    Mockito.when(runTimeHelper.saveProcessDetailsInstance(anyString(),
            anyLong(),
            anyString(),
            any(),
            any(),
            anyString(),
            anyMap())).thenThrow(genericJDBCException);


    createCalledProcessAspect.saveCalledProcessDetailsAndAddConfirmationForCalledProcessCreation(joinPoint, createCalledProcess);

    Mockito.verify(runTimeHelper, times(1))
            .saveProcessDetailsInstance(
                    anyString(),
                    anyLong(),
                    anyString(),
                    any(),
                    any(),
                    anyString(),
                    anyMap()
            );

    Mockito.verify(metricLogger, times(1)).logErrorMetric(any(), any(), any());
    Mockito.verify(domainEventService, times(1))
            .updateStatus(
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
            );

  }

  private Object[] joinPointArgs(Map<String, String> inputVariables) {
    Object[] args = new Object[1];
    args[0] = getWorkerActionRequestObject(inputVariables);
    return args;
  }

  private WorkerActionRequest getWorkerActionRequestObject(Map<String, String> inputVariables) {
    return WorkerActionRequest.builder()
            .activityId("childActivityId")
            .processDefinitionId("childDefinitionId")
            .processInstanceId("childProcessId")
            .inputVariables(inputVariables)
            .handlerId("sampleHandler")
            .taskId("externalTaskId")
            .ownerId(1234L)
            .build();
  }

  private ProcessDetails getMockParentProcessDetails() {
    return ProcessDetails.builder()
        .processId("parentProcessId")
        .definitionDetails(
            getMockParentDefinitionDetails()
        )
        .ownerId(1234L)
        .recordId("1")
        .parentId(null)
        .build();
  }

  private DefinitionDetails getMockParentDefinitionDetails() {
    return DefinitionDetails.builder().build();
  }

  private ProcessDetails getMockChildProcessDetails() {
    return ProcessDetails.builder()
        .processId("childProcessId")
        .definitionDetails(
            getMockChildDefinitionDetails()
        )
        .ownerId(1234L)
        .recordId("1")
        .parentId("parentProcessId")
        .build();
  }

  private DefinitionDetails getMockChildDefinitionDetails() {
    return DefinitionDetails.builder().build();
  }

  private String getCalledProcessCreatedResultVariableName() {
    return getCalledProcessId() + UNDERSCORE + CALLED_PROCESS_CREATION_RESULT;
  }

  private String getCalledProcessId() {
    return "childProcessId";
  }

}
