package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class ActivateDeActivateAppConnectWorkflowTaskTest {

  private AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);

  private AuthDetailsService authDetailsService = Mockito.mock(AuthDetailsServiceImpl.class);

  private ActivateDeActivateAppConnectWorkflowTask activateDeActivateAppConnectWorkflowTask;

  private State input;

  private AppConnectSaveWorkflowResponse workflowResponse;

  @Before
  public void setUp() {
    activateDeActivateAppConnectWorkflowTask =
        new ActivateDeActivateAppConnectWorkflowTask(authDetailsService, appConnectService);
    input = new State();
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "123");
    input.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, "wkid");
    workflowResponse = new AppConnectSaveWorkflowResponse();

    Mockito.when(
            appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenReturn(workflowResponse);
  }

  @Test
  public void testExecute_SubscriptionProvided() {
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subid");

    Assert.assertEquals(
        workflowResponse,
        activateDeActivateAppConnectWorkflowTask
            .execute(input)
            .getValue(AsyncTaskConstants.ACTIVATE_RESPONSE_KEY));
  }

  @Test
  public void testExecute_SubscriptionNotProvided() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq("realmId")))
        .thenReturn(authDetails);
    Assert.assertEquals(
        workflowResponse,
        activateDeActivateAppConnectWorkflowTask
            .execute(input)
            .getValue(AsyncTaskConstants.ACTIVATE_RESPONSE_KEY));
  }

  @Test
  public void testExecute_SubscriptionNotProvidedAndALreadyActive() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq("realmId")))
        .thenReturn(authDetails);

    Mockito.when(
        appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenThrow(new WorkflowGeneralException(
            WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, "dId",
            WorkflowConstants.WORKFLOW_ALREADY_INACTIVE
        ));

    try {
      activateDeActivateAppConnectWorkflowTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
    }
  }
  @Test
  public void testExecute_SubscriptionNotProvidedAndAlreadyDeactive() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq("realmId")))
        .thenReturn(authDetails);

    Mockito.when(
        appConnectService.activateDeactivateActionWorkflow(eq("wkid"), eq("subid"), eq(true)))
        .thenThrow(new WorkflowGeneralException(
            WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, "dId",
            WorkflowConstants.WORKFLOW_ALREADY_ACTIVE
        ));

    try {
      activateDeActivateAppConnectWorkflowTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
