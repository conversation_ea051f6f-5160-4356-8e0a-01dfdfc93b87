package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import static org.mockito.ArgumentMatchers.any;


/**
 * Author: Nitin Gupta Date: 30/01/20 Description: {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.AppConnectDeleteWorkflowTask}
 */
@RunWith(MockitoJUnitRunner.class)
public class AppConnectDeleteWorkflowTaskTest {

  @InjectMocks private AppConnectDeleteWorkflowTask task;

  @Mock private AppConnectService appConnectService;

  @Mock private AuthDetailsService authDetailsService;

  String realmId = "realmId";
  String subscriptionId = "subscriptionId";
  String workflowId = "workflowId";

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRealmIdNotPresent() {
    State input = new State();
    task.execute(input);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSubScriptingIdNotPresent() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(realmId))
        .thenReturn(new AuthDetails());
    task.execute(input);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testWorkFlowIdNotPresent() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    task.execute(input);
  }

  @Test
  public void happyCase() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    input.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);

    State response = task.execute(input);

    Mockito.verify(appConnectService)
            .deleteWorkflow(Mockito.eq(workflowId), Mockito.eq((subscriptionId)));
    Assert.assertNotNull(response);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteWorkFlowGeneralException() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DELETE_WORKFLOW_FAIL)).
            when(appConnectService).deleteWorkflow(any(), any());

    task.execute(inputRequest);
  }

  @Test
  public void testWorkflowResourceNotFoundException() {

    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DELETE_WORKFLOW_FAIL,
                    workflowId, "{\"errors\":[{\"details\":\" RESOURCE_NOT_FOUND\"}]}")).
            when(appConnectService).deleteWorkflow(any(), any());

    State response = task.execute(inputRequest);
    Mockito.verify(appConnectService)
            .deleteWorkflow(Mockito.eq(workflowId), Mockito.eq((subscriptionId)));
    Assert.assertNotNull(response);
  }
}
