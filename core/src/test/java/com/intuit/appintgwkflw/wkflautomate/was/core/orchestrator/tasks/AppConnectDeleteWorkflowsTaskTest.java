package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.Mockito;
import static org.mockito.ArgumentMatchers.any;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;

/** <AUTHOR> AppConnectDeleteWorkflowsTask} */
@RunWith(MockitoJUnitRunner.class)
public class AppConnectDeleteWorkflowsTaskTest {

  final String realmId = "realmId";
  final String subscriptionId = "subscriptionId";
  String workflowId = "workflowId";
  @InjectMocks private AppConnectDeleteWorkflowsTask appConnectDeleteWorkflowsTask;
  @Mock private AppConnectService appConnectService;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void happyCase() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    AuthDetails authDetails = new AuthDetails();
    State response = appConnectDeleteWorkflowsTask.execute(inputRequest);
    Mockito.verify(appConnectService)
            .deleteWorkflow(Mockito.any(), Mockito.any(),Mockito.any());
    Assert.assertEquals(inputRequest,response);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteWorkFlowGeneralException() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DELETE_WORKFLOW_FAIL)).
            when(appConnectService).deleteWorkflow(any(), any(), any());

    appConnectDeleteWorkflowsTask.execute(inputRequest);
  }

  @Test
  public void testWorkflowResourceNotFoundException() {

    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, subscriptionId);
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DELETE_WORKFLOW_FAIL,
                    workflowId, "{\"errors\":[{\"details\":\" RESOURCE_NOT_FOUND\"}]}")).
            when(appConnectService).deleteWorkflow(any(), any(), any());

    State response = appConnectDeleteWorkflowsTask.execute(inputRequest);
    Mockito.verify(appConnectService)
            .deleteWorkflow(Mockito.any(), Mockito.any(),Mockito.any());
    Assert.assertEquals(inputRequest,response);
  }
}
