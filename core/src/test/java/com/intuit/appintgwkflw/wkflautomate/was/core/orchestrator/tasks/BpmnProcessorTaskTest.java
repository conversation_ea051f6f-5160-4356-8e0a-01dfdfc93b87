package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessorTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.Template;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class BpmnProcessorTaskTest {

  private BpmnProcessorImpl bpmnProcessor = Mockito.mock(BpmnProcessorImpl.class);
  @MockBean private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  @MockBean private ReadCustomDefinitionHandler readCustomDefinitionHandler;

  private static final String INVOICE_APPROVAL_BPMN =
      "src/test/resources/bpmn/invoiceapproval.bpmn";
  private static final String CUSTOM_WORKFLOW_DMN = "src/test/resources/dmn/dmn_customapprovalbill.dmn";
  private static final String OFFER_ID = "offerId";
  private static final String OWNER_ID = "1234";
  private static final String CREATED_BY = "1234";

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = NullPointerException.class)
  public void templateIdNotPresent() {
    State inputRequest = new State();
    BpmnProcessorTask task = new BpmnProcessorTask("temp", null, (BpmnProcessorImpl) bpmnProcessor);
    task.execute(inputRequest);
  }

  @Test
  public void happyCase() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
    File dmnFile = new File(CUSTOM_WORKFLOW_DMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    FileInputStream fisDmn = new FileInputStream(dmnFile);
    TemplateDetails bpmnDetails = prepareBpmnDetails(fisBpmn, Status.ENABLED, bpmnFile.getName());
    TemplateDetails dmnDetails =
        prepareDmnDetails(fisDmn, bpmnDetails.getId(), Status.ENABLED, dmnFile.getName());
    Map<String, Pair<TemplateDetails, List<TemplateDetails>>> requestMap = new HashMap<>();
    requestMap.put(bpmnFile.getName(), Pair.of(bpmnDetails, Collections.singletonList(dmnDetails)));
    State inputRequest = new State();
    inputRequest.addValue(WorkflowConstants.TEMPLATE_REQ, requestMap);
    BpmnProcessorTask bpmnProcessorTask =
        new BpmnProcessorTask(
            bpmnFile.getName(),
            MessageFormat.format(
                "{0}_{1}", WorkflowConstants.RESPONSE_IDENTIFIER, bpmnDetails.getId()),bpmnProcessor);

    State response = bpmnProcessorTask.execute(inputRequest);
    Assert.assertNotNull(response);
  }

  @Test
  public void invalidTemplateCase() throws IOException {
    File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
    FileInputStream fisBpmn = new FileInputStream(bpmnFile);
    TemplateDetails bpmnDetails = prepareBpmnDetails(fisBpmn, Status.ENABLED, bpmnFile.getName());
    Map<String, Pair<TemplateDetails, List<TemplateDetails>>> requestMap = new HashMap<>();
    requestMap.put(bpmnFile.getName(), Pair.of(bpmnDetails, new ArrayList<>()));
    State inputRequest = new State();
    inputRequest.addValue(WorkflowConstants.TEMPLATE_REQ, requestMap);
    BpmnProcessorTask bpmnProcessorTask =
        new BpmnProcessorTask(
            bpmnFile.getName(),
            MessageFormat.format(
                "{0}_{1}", WorkflowConstants.RESPONSE_IDENTIFIER, bpmnDetails.getId()),bpmnProcessor);

    bpmnProcessorTask.execute(inputRequest);

    Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
    map.put(
        bpmnDetails,
        Collections.singletonList(
            prepareDmnDetails(fisBpmn, bpmnDetails.getId(), Status.ENABLED, bpmnFile.getName())));
    List<Template> response = bpmnProcessor.processBpmn(map);
    Assert.assertNotNull(response);
    Assert.assertEquals(0,response.size());
  }

  private TemplateDetails prepareDmnDetails(
      FileInputStream fisBpmn, String parentId, Status status, String name) throws IOException {
    return TemplateDetails.builder()
        .id(UUID.randomUUID().toString())
        .templateName(name)
        .modelType(ModelType.DMN)
        .createdByUserId(Long.parseLong(CREATED_BY))
        .creatorType(CreatorType.SYSTEM)
        .offeringId(OFFER_ID)
        .status(status)
        .ownerId(Long.parseLong(OWNER_ID))
        .version(1)
        .parentId(parentId)
        .templateData(IOUtils.toByteArray(fisBpmn))
        .build();
  }

  private TemplateDetails prepareBpmnDetails(FileInputStream fisBpmn, Status status, String name)
      throws IOException {
    return TemplateDetails.builder()
        .id(UUID.randomUUID().toString())
        .templateName(name)
        .modelType(ModelType.BPMN)
        .createdByUserId(Long.parseLong(CREATED_BY))
        .creatorType(CreatorType.SYSTEM)
        .offeringId(OFFER_ID)
        .status(status)
        .ownerId(Long.parseLong(OWNER_ID))
        .version(1)
        .parentId(null)
        .templateData(IOUtils.toByteArray(fisBpmn))
        .build();
  }
}
