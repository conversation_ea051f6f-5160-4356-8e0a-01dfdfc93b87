package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;

import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;

/**
 * Author: Nitin Gupta Date: 30/01/20 Description: {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteDefinitionTask}
 */
@RunWith(MockitoJUnitRunner.class)
public class CamundaDeleteDefinitionTaskTest {

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest =
      Mockito.mock(BPMNEngineDefinitionServiceRest.class);

  private static final String DEFINITION_ID = "definitionId";

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void definitionIdNotPresent() {
    State inputRequest = new State();
    CamundaDeleteDefinitionTask task =
        new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, null, true, true);
    task.execute(inputRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void cascadeNotPresent() {
    State inputRequest = new State();
    CamundaDeleteDefinitionTask task =
        new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, null, true);
    task.execute(inputRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void skipListenerNotPresent() {
    State inputRequest = new State();
    CamundaDeleteDefinitionTask task =
        new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, true, null);
    task.execute(inputRequest);
  }

  @Test
  public void happyCase() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEFINITION_ID);
    CamundaDeleteDefinitionTask task =
        new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, true, true);
    task.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
        .deleteDefinition(any(DeleteDeploymentRequest.class));
  }

  @Test
  public void testDeleteDefnNoProcessFound() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEFINITION_ID);
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    CamundaDeleteDefinitionTask task =
        new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, true, true);

    try {
      task.execute(inputRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteDefinitionException() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEFINITION_ID);
    CamundaDeleteDefinitionTask task =
            new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, true, true);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DEFINITION_NOT_FOUND)).
            when(bpmnEngineDefinitionServiceRest).deleteDefinition(
            new DeleteDeploymentRequest(DEFINITION_ID, true, true));

    task.execute(inputRequest);
  }

  @Test
  public void testDefinitionNotFoundException() {

    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEFINITION_ID);
    CamundaDeleteDefinitionTask task =
            new CamundaDeleteDefinitionTask(bpmnEngineDefinitionServiceRest, DEFINITION_ID, true, true);
    Mockito.doThrow(
            new WorkflowGeneralException(
                    WorkflowError.DELETE_DEFINITION_FAILED, "{\"errors\":[{\"details\":\" No process definition found\"}]}")).
            when(bpmnEngineDefinitionServiceRest).deleteDefinition(
            new DeleteDeploymentRequest(DEFINITION_ID, true, true));

    State response = task.execute(inputRequest);
    Mockito.verify(bpmnEngineDefinitionServiceRest)
            .deleteDefinition(any(DeleteDeploymentRequest.class));
    Assert.assertEquals(inputRequest,response);
  }
}
