package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

/** Author: Jatin Mahajan Date: 19/09/20 Description: {@link CorrelateAllMessageTaskTest} */
@RunWith(MockitoJUnitRunner.class)
public class CorrelateAllMessageTaskTest {

  private static final String realmId = "123";
  private static final String messageName = "test_message";
  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  @InjectMocks private CorrelateAllMessageTask correlateAllMessageTask;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void happyCase() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    CorrelateAllMessage correlateAllMessage = new CorrelateAllMessage(messageName, realmId, null);
    bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage);
    Assert.assertNotNull(correlateAllMessageTask.execute(input));
  }

  @Test
  public void failCase() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    CorrelateAllMessage correlateAllMessage = new CorrelateAllMessage(messageName, realmId, null);
    bpmnEngineRunTimeServiceRest.correlateAllMessage(correlateAllMessage);
    try {
      correlateAllMessageTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExceptionScenario() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Boolean isFailed = bpmnEngineRunTimeServiceRest.correlateAllMessage(null);
    Assert.assertFalse(isFailed);
    try {
      correlateAllMessageTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void asyncSuccess() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.IS_CORRELATE_ASYNC, true);
    CorrelateAllMessageAsync correlateAllMessage = new CorrelateAllMessageAsync(messageName, null);
    bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(correlateAllMessage);
    Assert.assertNotNull(correlateAllMessageTask.execute(input));
  }

  @Test
  public void asyncFailure() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.IS_CORRELATE_ASYNC, true);
    CorrelateAllMessageAsync correlateAllMessage = new CorrelateAllMessageAsync(messageName, null);
    bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(correlateAllMessage);
    try {
      correlateAllMessageTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
      Assert.assertNotNull(AsyncTaskConstants.CORRELATE_ALL_EXCEPTION);
    }
  }

  @Test
  public void asyncException() {
    State input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    input.addValue(AsyncTaskConstants.IS_CORRELATE_ASYNC, true);
    Boolean isFailed = bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(null);
    Assert.assertFalse(isFailed);
    try {
      correlateAllMessageTask.execute(input);
    } catch (Exception e) {
      Assert.fail();
      Assert.assertNotNull(AsyncTaskConstants.CORRELATE_ALL_EXCEPTION);
    }
  }
}
