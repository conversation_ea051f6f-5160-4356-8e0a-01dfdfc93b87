package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.async.execution.request.State;

/**
 * Author: Ni<PERSON> Gupta Date: 30/01/20 Description: {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask}
 */
@RunWith(MockitoJUnitRunner.class)
public class DataStoreDeleteDefinitionAndProcessTaskTest {

  private DefinitionDetailsRepository definitionDetailsRepository =
      Mockito.mock(DefinitionDetailsRepository.class);

  private ProcessDetailsRepository processDetailsRepository =
      Mockito.mock(ProcessDetailsRepository.class);

  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository =
        Mockito.mock(DefinitionActivityDetailsRepository.class);
  
  @Mock
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  String definitionId = "definitinId";

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void definitionIdNotPresent() {
    State inputRequest = new State();
    DataStoreDeleteDefinitionAndProcessTask task =
        new DataStoreDeleteDefinitionAndProcessTask(
            definitionDetailsRepository, null, dataStoreDeleteTaskService);
    task.execute(inputRequest);
  }

  @Test
  public void happyCase() {
    State inputRequest = new State();
    
    Mockito.doNothing().when(dataStoreDeleteTaskService)
    	.deleteDefinitions(Mockito.anyList());
    
    Mockito.when(definitionDetailsRepository
    	.deleteByDefinitionIdOrParentId(Mockito.eq(definitionId), Mockito.eq(definitionId)))
    .thenReturn(1l);
    
    DataStoreDeleteDefinitionAndProcessTask task =
        new DataStoreDeleteDefinitionAndProcessTask(
                definitionDetailsRepository, definitionId, dataStoreDeleteTaskService);
    task.execute(inputRequest);

    Mockito.verify(dataStoreDeleteTaskService)
        .deleteDefinitions(Mockito.anyList());

    Mockito.verify(definitionDetailsRepository)
        .deleteByDefinitionIdOrParentId(Mockito.eq(definitionId), Mockito.eq(definitionId));
  }
}