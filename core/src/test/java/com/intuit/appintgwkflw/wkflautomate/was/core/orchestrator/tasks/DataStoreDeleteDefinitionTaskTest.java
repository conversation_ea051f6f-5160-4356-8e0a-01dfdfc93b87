package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.async.execution.request.State;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Optional;

import static org.mockito.Mockito.*;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public  class DataStoreDeleteDefinitionTaskTest {

    @Mock
    private DefinitionDetailsRepository definitionDetailsRepository;

    @Mock
    private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

    @Mock
    private EnabledDefinitionCacheService cacheService;

    @InjectMocks
    private DataStoreDeleteDefinitionTask dataStoreDeleteDefinitionTask;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testExecute_withValidDefinitionId() {
        State inputRequest = new State();
        inputRequest.addValue("definitionIdKey", "testDefinitionId");
        DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
        inputRequest.addValue("definitionInstance", definitionInstance);


        when(definitionDetailsRepository.findByParentId("testDefinitionId"))
                .thenReturn(Optional.of(Collections.emptyList()));
        when(definitionDetailsRepository.deleteByDefinitionIdOrParentId("testDefinitionId", "testDefinitionId"))
                .thenReturn(1L);

        State result = dataStoreDeleteDefinitionTask.execute(inputRequest);

        verify(definitionActivityDetailsRepository, times(1))
                .deleteByDefinitionDetailsIn(anyList());
        verify(definitionDetailsRepository, times(1))
                .deleteByDefinitionIdOrParentId("testDefinitionId", "testDefinitionId");
        verify(cacheService, times(1))
                .updateCacheWithDefinitionDetails(any());

        // Add assertions as needed
    }

    @org.junit.Test(expected = WorkflowGeneralException.class)
    public void definitionIdNotPresent() {
        State inputRequest = new State();
        DataStoreDeleteDefinitionTask task =
                new DataStoreDeleteDefinitionTask(definitionDetailsRepository, definitionActivityDetailsRepository, cacheService);
        task.execute(inputRequest);
    }

    @Test
    void testExecute_withChildDefinitions() {
        State inputRequest = new State();
        inputRequest.addValue("definitionIdKey", "testDefinitionId");
        DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
        inputRequest.addValue("definitionInstance", definitionInstance);

        DefinitionDetails childDefinition = new DefinitionDetails();
        childDefinition.setDefinitionId("childDefinitionId");

        when(definitionDetailsRepository.findByParentId("testDefinitionId"))
                .thenReturn(Optional.of(Collections.singletonList(childDefinition)));
        when(definitionDetailsRepository.deleteByDefinitionIdOrParentId("testDefinitionId", "testDefinitionId"))
                .thenReturn(2L);

        State result = dataStoreDeleteDefinitionTask.execute(inputRequest);

        verify(definitionActivityDetailsRepository, times(1))
                .deleteByDefinitionDetailsIn(anyList());
        verify(definitionDetailsRepository, times(1))
                .deleteByDefinitionIdOrParentId("testDefinitionId", "testDefinitionId");
        verify(cacheService, times(1))
                .updateCacheWithDefinitionDetails(any());

        // Add assertions as needed
    }
}