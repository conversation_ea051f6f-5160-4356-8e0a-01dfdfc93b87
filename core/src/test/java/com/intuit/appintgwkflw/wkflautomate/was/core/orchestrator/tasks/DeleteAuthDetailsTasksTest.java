package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;

import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/** <AUTHOR> */
public class DeleteAuthDetailsTasksTest {

  private final static long OWNER_COUNT_ZERO = 0l;
  private final static long OWNER_COUNT_ONE = 1l;

  private AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
  private DefinitionDetailsRepository definitionDetailsRepository =
      Mockito.mock(DefinitionDetailsRepository.class);
  private ProcessDetailsRepository processDetailsRepository =
      Mockito.mock(ProcessDetailsRepository.class);
  private DeleteAuthDetailsTasks task =
      new DeleteAuthDetailsTasks(
          authDetailsRepository, definitionDetailsRepository, processDetailsRepository);

  private String REALM_ID = "1234";

  @Test(expected = WorkflowGeneralException.class)
  public void test() {
    task.execute(new State());
  }

  @Test
  public void testDeleteAuth() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);

    Mockito.when(definitionDetailsRepository.countByOwnerId(Long.parseLong(REALM_ID)))
        .thenReturn(0l);
    Mockito.when(authDetailsRepository.deleteByOwnerId(Long.parseLong(REALM_ID))).thenReturn(0l);
    try {
      task.execute(inputRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteAuthOwnerCount() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);

    Mockito.when(definitionDetailsRepository.countByOwnerId(Long.parseLong(REALM_ID)))
            .thenReturn(OWNER_COUNT_ZERO);
    Mockito.when(authDetailsRepository.deleteByOwnerId(Long.parseLong(REALM_ID))).thenReturn(OWNER_COUNT_ONE);
    try {
      task.execute(inputRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteAuthFail() {
    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);

    Mockito.when(definitionDetailsRepository.countByOwnerId(Long.parseLong(REALM_ID)))
            .thenReturn(1l);
    try {
      task.execute(inputRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
