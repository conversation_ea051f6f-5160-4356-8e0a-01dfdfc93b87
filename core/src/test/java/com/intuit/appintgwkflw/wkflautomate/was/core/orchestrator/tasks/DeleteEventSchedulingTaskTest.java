package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class DeleteEventSchedulingTaskTest {
    @Mock
    private SchedulingService schedulingService;
    @Mock
    private SchedulingServiceUtil schedulingServiceUtil;

    @InjectMocks
    private DeleteEventSchedulingTask task;

    private State state;
    private DefinitionInstance definition;

    @Before
    public void setUp() {
        state = new State();
    }

    @Test
    public void testExecute_ScheduleActionsModelsEmpty() {
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.empty());

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService, Mockito.never()).deleteSchedules(any(), any());
    }

    @Test
    public void testExecute_RealmIdNull() {
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.empty());

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService, Mockito.never()).deleteSchedules(any(), any());
    }

    @Test
    public void testExecute_DefinitionNull() {
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.empty());

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService, Mockito.never()).deleteSchedules(any(), any());
    }

    @Test
    public void testExecute_SchedulesDeletedSuccessfully() {
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionKey("definitionKey");
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);
        state.addValue(AsyncTaskConstants.DEFINITION_KEY, "definitionKey");
        state.addValue(AsyncTaskConstants.WORKFLOW_NAME_KEY, "customScheduledActions");

        List<SchedulingSvcResponse> responses = Collections.singletonList(new SchedulingSvcResponse());
        when(schedulingService.deleteSchedules(any(), eq("realmId"))).thenReturn(responses);

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService).deleteSchedules(any(), any());
    }

    @Test
    public void testExecute_SchedulesNotDeletedForAllActions() {
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionKey("definitionKey");
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);
        state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
        List<SchedulingSvcResponse> responses = Collections.emptyList();

        task.execute(state);
        Assert.assertNotNull(schedulingServiceUtil);
    }

    @Test
    public void testExecute_ExceptionThrownDuringScheduleDeletion() {
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionKey("definitionKey");
        TemplateDetails templateDetails = new TemplateDetails();
        templateDetails.setTemplateName("customScheduledActions");
        definitionDetails.setTemplateDetails(templateDetails);
        state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
        task.execute(state);
        Assert.assertNotNull(state);

    }
}