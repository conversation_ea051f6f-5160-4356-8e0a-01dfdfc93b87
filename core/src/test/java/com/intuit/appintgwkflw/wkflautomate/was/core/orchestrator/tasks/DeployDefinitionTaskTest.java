package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY;
import static org.mockito.ArgumentMatchers.any;

public class DeployDefinitionTaskTest {

  @Test
  public void testExecute() {
    BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest = Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    DeployDefinitionTask deployDefinitionTask = new DeployDefinitionTask(bpmnEngineDefinitionServiceRest);
    State state = new State();
    state.addValue(BPMN_ENGINE_DEPLOY_REQUEST_KEY, Mockito.mock(DeployDefinition.class));
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedProcess = new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("123");
    deployedProcess.put("123", deployedDefinition);
    deployDefinitionResponse.setDeployedProcessDefinitions(deployedProcess);
    deployDefinitionResponse.setId("456");
    WASHttpResponse<Object> responseEntity =
        WASHttpResponse.builder()
            .response(deployDefinitionResponse)
            .build();
    Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(any(DeployDefinition.class)))
        .thenReturn(responseEntity);
    Assert.assertEquals(
        "123", deployDefinitionTask.execute(state).getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
  }
}
