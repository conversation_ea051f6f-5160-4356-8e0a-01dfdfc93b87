package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.async.execution.request.State;
import java.util.Collections;

import com.intuit.v4.common.RecurrenceRule;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class DisableDeleteWorkflowTaskTest {

  @Mock
  private AppConnectService appConnectService;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock
  private AuthDetailsService authDetailsService;

  @Mock
  private EventScheduleHelper eventScheduleHelper;

  @Mock
  private SchedulingService schedulingService;

  @InjectMocks
  private DisableDeleteWorkflowTask disableDeleteWorkflowTask;

  private State state;

  private static final String REALM_ID = "124";

  private static final String WORKFLOW_ID = "345";

  private DefinitionDetails definitionDetails;

  private UpdateEventScheduleTask updateStatusEventSchedulerTask;

  private UpdateEventSchedulingTask updateEventSchedulingTask;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    definitionDetails =
        DefinitionDetails.builder()
            .internalStatus(InternalStatus.MARKED_FOR_DISABLE)
            .definitionId("def-id")
            .workflowId(WORKFLOW_ID)
            .ownerId(Long.parseLong(REALM_ID))
            .templateDetails(TemplateDetails.builder().templateName("workflow").build())
            .build();
    state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, Collections.singletonList(definitionDetails));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("sub-id");
    when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID))).thenReturn(authDetails);
    updateStatusEventSchedulerTask = Mockito.mock(UpdateEventScheduleTask.class);
    ReflectionTestUtils.setField(
        disableDeleteWorkflowTask, "eventScheduleHelper", eventScheduleHelper);
    Mockito.when(updateStatusEventSchedulerTask.execute(Mockito.any()))
        .thenReturn(Mockito.mock(State.class));
    updateEventSchedulingTask = Mockito.mock(UpdateEventSchedulingTask.class);
    Mockito.when(updateEventSchedulingTask.execute(Mockito.any()))
        .thenReturn(Mockito.mock(State.class));
    when(schedulingService.isEnabled((DefinitionDetails) any(), any())).thenReturn(false);
  }

  @Test
  public void testExecute_MarkedForDisable() {

    when(appConnectService.activateDeactivateActionWorkflow(
        eq(WORKFLOW_ID), any(), eq(false)))
        .thenReturn(new AppConnectSaveWorkflowResponse());
    disableDeleteWorkflowTask.execute(state);
    verify(appConnectService)
        .activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
    verify(definitionDetailsRepository)
        .updateInternalStatus(eq(null), eq(Collections.singletonList("def-id")));
  }

  @Test
  public void testExecute_MarkedForDelete() {

    state.addValue(AsyncTaskConstants.DEFINITION_DETAILS, Collections.singletonList(
        definitionDetails
            .toBuilder()
            .internalStatus(InternalStatus.MARKED_FOR_DELETE)
            .build()));
    disableDeleteWorkflowTask.execute(state);
    verify(appConnectService).deleteWorkflow(eq(WORKFLOW_ID), any());
    verify(definitionDetailsRepository)
        .updateInternalStatus(eq(InternalStatus.DELETED), eq(Collections.singletonList("def-id")));
  }

  @Test
  public void testExecute_AppConnectError() {
    doThrow(new RuntimeException())
        .when(appConnectService)
        .activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
    try {
      disableDeleteWorkflowTask.execute(state);
      Assert.fail();
    } catch (Exception e) {
      verify(appConnectService)
          .activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
      verify(definitionDetailsRepository, never())
          .updateInternalStatus(
              eq(InternalStatus.DELETED), eq(Collections.singletonList("def-id")));
      Assert.assertEquals(WorkflowGeneralException.class, e.getClass());
    }
  }

  @Test
  public void testExecute_UpdateScheduleStatusNotEnabled() {
    when(appConnectService.activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false)))
        .thenReturn(new AppConnectSaveWorkflowResponse());
    disableDeleteWorkflowTask.execute(state);
    verify(appConnectService).activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
    verify(definitionDetailsRepository)
        .updateInternalStatus(eq(null), eq(Collections.singletonList("def-id")));
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(0)).execute(Mockito.any());
  }

  @Test
  public void testExecute_UpdateScheduleStatusEnabled() {
    when(appConnectService.activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false)))
        .thenReturn(new AppConnectSaveWorkflowResponse());
    Mockito.when(
        eventScheduleHelper.prepareScheduleStatusUpdateTask(
            Mockito.any(), Mockito.any()))
        .thenReturn(updateStatusEventSchedulerTask);
    disableDeleteWorkflowTask.execute(state);
    verify(appConnectService).activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
    verify(definitionDetailsRepository)
        .updateInternalStatus(eq(null), eq(Collections.singletonList("def-id")));
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(1)).execute(Mockito.any());
  }

  @Test
  public void testExecute_UpdateScheduleStatusEnabledSchedulingFlow() {
    when(schedulingService.isEnabled((DefinitionDetails) any(), any())).thenReturn(true);
    when(appConnectService.activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false)))
            .thenReturn(new AppConnectSaveWorkflowResponse());
    Mockito.when(
                    eventScheduleHelper.prepareSchedulingUpdateTask(any(), any(), any(boolean.class))).thenReturn(updateEventSchedulingTask);
    disableDeleteWorkflowTask.execute(state);
    verify(appConnectService).activateDeactivateActionWorkflow(eq(WORKFLOW_ID), any(), eq(false));
    verify(definitionDetailsRepository)
            .updateInternalStatus(eq(null), eq(Collections.singletonList("def-id")));
    Mockito.verify(updateEventSchedulingTask, Mockito.times(1)).execute(Mockito.any());
  }
}
