package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;

import java.util.Arrays;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import static org.mockito.ArgumentMatchers.any;

public class GetAuthDetailsTaskTest {

  private GetAuthDetailsTask task;
  private AuthHelper authHelper = Mockito.mock(AuthHelper.class);

  @Test
  public void testExecute() {
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    task =
        new GetAuthDetailsTask(
            authDetailsRepository,
            new Authorization("Intuit_IAM_Authentication intuit_realmid=123"), authHelper);

    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(new AuthDetails()));
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenReturn(Optional.of(Arrays.asList(new AuthDetails())));

    State state = task.execute(new State());
    Assert.assertEquals("123", state.getValue(AsyncTaskConstants.REALM_ID_KEY));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteMissingAuthDetails() {
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);

    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(null));
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenReturn(Optional.ofNullable(null));

    task =
        new GetAuthDetailsTask(
            authDetailsRepository, new Authorization("Intuit_IAM_Authentication "), authHelper);

    task.execute(new State());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteMissingRealm() {
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    task =
        new GetAuthDetailsTask(
            authDetailsRepository, new Authorization("Intuit_IAM_Authentication "), authHelper);

    task.execute(new State());
  }
}