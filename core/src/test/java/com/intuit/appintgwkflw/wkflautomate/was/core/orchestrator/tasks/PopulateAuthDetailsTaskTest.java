package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class PopulateAuthDetailsTaskTest {

  @Mock private AuthDetailsService authDetailsService;
  @Mock private AuthDetailsServiceHelper authDetailsServiceHelper;
  @Mock private WASContextHandler contextHandler;
  private Authorization authorization;
  private PopulateAuthDetailsTask populateAuthDetailsTask;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=1234";
    authorization = new Authorization(header);
    this.populateAuthDetailsTask = new PopulateAuthDetailsTask(contextHandler, authDetailsServiceHelper);
  }

  @Test
  public void testAuthDetailsSync() {
    State inputRequest = new State();
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(null))
        .thenThrow(WorkflowGeneralException.class);
    State result = populateAuthDetailsTask.execute(inputRequest);
    Assert.assertNotNull(result);
    Assert.assertEquals(inputRequest.getAll(), result.getAll());
  }
}
