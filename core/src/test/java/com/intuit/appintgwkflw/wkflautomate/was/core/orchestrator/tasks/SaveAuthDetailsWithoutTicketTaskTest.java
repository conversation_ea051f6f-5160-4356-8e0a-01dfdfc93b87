package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.dao.DataIntegrityViolationException;

import java.sql.Timestamp;
import java.time.Instant;

import static org.mockito.ArgumentMatchers.any;

public class SaveAuthDetailsWithoutTicketTaskTest {

  private SaveAuthDetailsWithoutTicketTask saveAuthDetailsTask;
  private final static Long OWNER_ID = 1L;

  @Test
  public void testExecute() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "123");
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    AuthDetails authDetails = new AuthDetails();
    Mockito.when(authDetailsRepository.save(any(AuthDetails.class))).thenReturn(authDetails);
    saveAuthDetailsTask.execute(state);
    Mockito.verify(authDetailsRepository).save(any(AuthDetails.class));
  }

  @Test
  public void testExecute_Update() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    AuthDetails authDetails = new AuthDetails();
    authDetails.setOwnerId(1L);
    authDetails.setSubscriptionId("12345");
    state.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetails);
    Mockito.when(authDetailsRepository.save(any(AuthDetails.class))).thenReturn(authDetails);
    saveAuthDetailsTask.execute(state);
    Mockito.verify(authDetailsRepository).save(any(AuthDetails.class));
  }

  @Test
  public void testExecute_UpdateException() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    AuthDetails authDetails = new AuthDetails();
    authDetails.setOwnerId(1L);
    state.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetails);
    Mockito.when(authDetailsRepository.save(any(AuthDetails.class)))
        .thenThrow(new RuntimeException());
    saveAuthDetailsTask.execute(state);
    Assert.assertNull(state.getValue(AsyncTaskConstants.SAVE_AUTH_DETAILS_RESPONSE_KEY));
  }


  @Test
  public void testExecute_RealmId_Exception() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "123");
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "");
    state.addValue(AsyncTaskConstants.OFFLINE_TICKET_KEY, "123");
    state.addValue(AsyncTaskConstants.EXPIRY_DATE_KEY, Timestamp.from(Instant.now()));
    Mockito.when(authDetailsRepository.save(any(AuthDetails.class)))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_REALM_ID));
    try{
      saveAuthDetailsTask.execute(state);
      Assert.fail();
    }
    catch (WorkflowGeneralException e){
      Assert.assertEquals(WorkflowError.INVALID_REALM_ID.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testExecute_SubscriptionId_Exception() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "123");
    try{
      saveAuthDetailsTask.execute(state);
      Assert.fail();
    }
    catch (WorkflowGeneralException e){
      Assert.assertEquals(WorkflowError.INVALID_APPCONNECT_SUBSCRIPTION_ID.getErrorMessage(), e.getMessage());
    }

  }

  @Test
  public void testExecute_UpdateDataIntegrityException() {

    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    saveAuthDetailsTask = new SaveAuthDetailsWithoutTicketTask(authDetailsRepository);
    State state = new State();
    AuthDetails authDetails = new AuthDetails();
    authDetails.setOwnerId(OWNER_ID);
    state.addValue(AsyncTaskConstants.AUTH_DETAILS_KEY, authDetails);
    Mockito.when(authDetailsRepository.save(any(AuthDetails.class)))
            .thenThrow(new DataIntegrityViolationException(""));
    saveAuthDetailsTask.execute(state);
    Assert.assertNull(state.getValue(AsyncTaskConstants.SAVE_AUTH_DETAILS_RESPONSE_KEY));
  }


}
