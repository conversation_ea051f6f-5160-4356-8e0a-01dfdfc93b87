package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SaveEventScheduleWorkflowTaskTest {

  private SaveEventScheduleWorkflowTask saveEventScheduleWorkflowTask;
  private State input;
  private EventScheduleService eventScheduleService;
  private EventScheduleConfig eventScheduleConfig;

  @Before
  public void setup() {
    input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234");
    eventScheduleService = Mockito.mock(EventScheduleServiceImpl.class);
    eventScheduleConfig = Mockito.mock(EventScheduleConfig.class);
    saveEventScheduleWorkflowTask = new SaveEventScheduleWorkflowTask(eventScheduleService, eventScheduleConfig);
  }

  @Test
  public void testExecute() {
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
        new EventScheduleWorkflowActionModel("customStart", new LocalDate(), new RecurrenceRule());
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData scheduleData = new EventScheduleData();
    scheduleData.setId("customStart-11111");
    eventScheduleData.add(scheduleData);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(List.of(eventScheduleWorkflowActionModel)));
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE));
    Map<String, String> data = input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE);
    Assert.assertNotNull(data.get("customStart"));
    Assert.assertEquals("customStart-11111", data.get("customStart"));
  }

  @Test
  public void testExecute_eventScheduleReqNull() {
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE));
  }

  @Test
  public void testExecute_createScheduleThrowInternalServerError() {
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
        new EventScheduleWorkflowActionModel("customStart", new LocalDate(), new RecurrenceRule());
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData scheduleData = new EventScheduleData();
    scheduleData.setId("customStart-11111");
    eventScheduleData.add(scheduleData);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(List.of(eventScheduleWorkflowActionModel)));
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenThrow(
            new WorkflowGeneralException(
                WorkflowError.EVENT_SCHEDULE_CALL_FAILURE, "Internal Server error"));
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    Assert.assertEquals(true, input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
  }

  @Test
  public void testExecute_createScheduleReturnErrors() {
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
        new EventScheduleWorkflowActionModel("customStart", new LocalDate(), new RecurrenceRule());
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleError> eventScheduleErrors = new ArrayList<>();
    EventScheduleError eventScheduleError = new EventScheduleError();
    eventScheduleError.setCode("PT-404");
    eventScheduleResponse.setErrors(eventScheduleErrors);
    eventScheduleResponses.add(eventScheduleResponse);
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(List.of(eventScheduleWorkflowActionModel)));
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    Assert.assertEquals(true, input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    WorkflowGeneralException workflowGeneralException =
        input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION);
    Assert.assertEquals(
        workflowGeneralException.getWorkflowError(), WorkflowError.EVENT_SCHEDULE_CALL_FAILURE);
  }

  @Test
  public void testExecute_createScheduleReturnUnequalData() {
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
        new EventScheduleWorkflowActionModel("customStart", new LocalDate(), new RecurrenceRule());
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData scheduleData = new EventScheduleData();
    scheduleData.setId("customStart-11111");
    eventScheduleData.add(scheduleData);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(List.of(eventScheduleWorkflowActionModel, eventScheduleWorkflowActionModel)));
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    Assert.assertEquals(true, input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    WorkflowGeneralException workflowGeneralException =
        input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION);
    Assert.assertEquals(
        workflowGeneralException.getWorkflowError(), WorkflowError.EVENT_SCHEDULE_CALL_FAILURE);
    Assert.assertTrue(
        workflowGeneralException.getMessage().contains("Schedules Not created for all actions"));
  }

  @Test
  public void testExecute_createScheduleReturnNoData() {
    EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
        new EventScheduleWorkflowActionModel("customStart", new LocalDate(), new RecurrenceRule());
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(List.of(eventScheduleWorkflowActionModel, eventScheduleWorkflowActionModel)));
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    saveEventScheduleWorkflowTask.execute(input);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    Assert.assertEquals(true, input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    WorkflowGeneralException workflowGeneralException =
        input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION);
    Assert.assertEquals(
        workflowGeneralException.getWorkflowError(), WorkflowError.EVENT_SCHEDULE_CALL_FAILURE);
    Assert.assertTrue(workflowGeneralException.getMessage().contains("No schedules created"));
  }
}
