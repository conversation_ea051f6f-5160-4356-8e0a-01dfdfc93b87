package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class SaveEventSchedulingTaskTest {
    @Mock
    private SchedulingService schedulingService;

    @Mock
    private ActionModelToScheduleRequestMapper actionModelToCreateScheduleRequestMapper;

    @InjectMocks
    private SaveEventSchedulingTask task;

    @Test
    public void testExecute_ScheduleActionsModelsEmpty() {
        State state = new State();
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.empty());

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService, Mockito.never()).createSchedules(any(), any());
    }

    @Test
    public void testExecute_SchedulesCreatedSuccessfully() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);

        List<SchedulingSvcResponse> responses = Collections.singletonList(new SchedulingSvcResponse());
        Mockito.when(schedulingService.createSchedules(any(), eq("realmId"))).thenReturn(responses);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService).createSchedules(any(), eq("realmId"));
    }

    @Test
    public void testExecute_SchedulesNotCreatedForAllActions() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        List<SchedulingSvcResponse> responses = Collections.emptyList();
        Mockito.when(schedulingService.createSchedules(any(), eq("realmId"))).thenReturn(responses);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());

        State result = task.execute(state);

        assertTrue(result.getValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE));
        assertNotNull(result.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION));
        assertEquals(WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, result.getValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE));
    }

    @Test
    public void testExecute_ExceptionThrownDuringScheduleCreation() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);

        Mockito.when(schedulingService.createSchedules(any(), eq("realmId"))).thenThrow(new WorkflowGeneralException(WorkflowError.EVENT_SCHEDULE_CALL_FAILURE, "Error"));
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());

        State result = task.execute(state);

        assertTrue(result.getValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE));
        assertNotNull(result.getValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION));
        assertEquals(WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, result.getValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE));
    }

    @Test
    public void testOnError(){
        State state = task.onError(new State());
        Assert.assertNotNull(state);
        Assert.assertTrue(state.getValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE));
    }

}
