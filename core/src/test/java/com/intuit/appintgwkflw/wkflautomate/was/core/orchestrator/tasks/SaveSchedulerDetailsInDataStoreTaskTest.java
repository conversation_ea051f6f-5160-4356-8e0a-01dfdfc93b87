package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.async.execution.request.State;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SaveSchedulerDetailsInDataStoreTaskTest {
  private SaveScheduleDetailsInDataStoreTask saveSchedulerDetailsInDataStoreTask;
  private State input;
  private SchedulerDetailsRepository schedulerDetailsRepository;

  @Before
  public void setup() {
    input = new State();
    schedulerDetailsRepository = Mockito.mock(SchedulerDetailsRepository.class);
    saveSchedulerDetailsInDataStoreTask =
        new SaveScheduleDetailsInDataStoreTask(schedulerDetailsRepository);
  }

  @Test
  public void testExecute() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "Def-1234");
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE,
        Map.of(
            SchedulerAction.CUSTOM_REMINDER_CUSTOM_START.getAction(),
            "sch123",
            SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT.getAction(),
            "sch1234"));
    saveSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).saveAll(any());
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS));
    List<String> schedulesIds = input.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);
    Assert.assertEquals(schedulesIds.size(), 2);
    Assert.assertTrue(schedulesIds.contains("sch123"));
    Assert.assertTrue(schedulesIds.contains("sch1234"));
  }

  @Test
  public void testExecute_noRealmId() {
    State state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "");
    state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
  }

  @Test
  public void testExecute_noDefinitionId() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    State state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "");
    state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
  }

  @Test
  public void testExecute_NoActionIdKey() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "Def-1234");
    saveSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).saveAll(any());
    Assert.assertEquals(input.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE));
  }

  @Test
  public void testExecute_emptyActionIdKey() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "Def-1234");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE, Map.of());
    saveSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0)).saveAll(any());
    Assert.assertEquals(input.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    Assert.assertNotNull(input.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE));
  }

  @Test
  public void testExecute_wrongKey() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "Def-1234");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE, Map.of("test-123", "sch123"));
    State state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE));
  }

  @Test
  public void testExecute_ExceptionSavingDataToDB() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "12345");
    input.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "Def-1234");
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE,
        Map.of(
            SchedulerAction.CUSTOM_REMINDER_CUSTOM_START.getAction(),
            "sch123",
            SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT.getAction(),
            "sch1234"));
    Mockito.when(schedulerDetailsRepository.saveAll(Mockito.any()))
        .thenThrow(new RuntimeException("DB connection failed"));
    State state = saveSchedulerDetailsInDataStoreTask.execute(input);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1)).saveAll(any());
    Assert.assertEquals(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE), true);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_IN_DATASTORE_EXCEPTION));
  }
}
