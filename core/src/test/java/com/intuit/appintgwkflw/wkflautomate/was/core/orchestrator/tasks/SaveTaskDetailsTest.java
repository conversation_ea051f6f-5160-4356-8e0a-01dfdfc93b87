package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class SaveTaskDetailsTest {

  private ActivityDetailsRepository activityDetailsRepository = Mockito
      .mock(ActivityDetailsRepository.class);

  private SaveTaskDetails saveTaskDetails;

  @Before
  public void setUp() {

    saveTaskDetails = new SaveTaskDetails(activityDetailsRepository, true);
  }

  /**
   * SaveTaskDetails called will save 2 tasks.
   */
  @Test
  public void testExecute_ForHumanAndSystemTask() {
    State state = new State();
    TemplateDetails templateDetails = TemplateDetails.builder().id("id1").displayName("sample")
        .description("desc")
        .deployedDefinitionId("deployiD").definitionType(DefinitionType.SYSTEM)
        .creatorType(CreatorType.SYSTEM)
        .build();
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);

    List<ActivityDetail> activityDetails = new ArrayList<>();
    
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    .activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);

    saveTaskDetails.execute(state);
    Mockito.verify(activityDetailsRepository).saveAll(anyList());
  }

  /**
   * Verifies no save call invoke when templateDetails is not available.
   */
  @Test
  public void testExecute_NoSave() {
    State state = new State();
    
    List<ActivityDetail> activityDetails = new ArrayList<>();
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    		.activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
    
    saveTaskDetails.execute(state);
    Mockito.verifyNoInteractions(activityDetailsRepository);
  }

  @Test
  public void testExecute_Exception() {
    State state = new State();
    boolean exceptionFlag = false;
    try {
      TemplateDetails templateDetails = TemplateDetails.builder().id("id1").displayName("sample")
          .description("desc").deployedDefinitionId("deployiD")
          .definitionType(DefinitionType.SYSTEM)
          .creatorType(CreatorType.SYSTEM).build();
      state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);

      List<ActivityDetail> activityDetails = new ArrayList<>();
      ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
      		.activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
      		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
              })).build())).build();
      activityDetails.add(humantask);
      
      ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
      		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
      		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
      		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
              })).build())).build();
      activityDetails.add(systemTask);
      
      state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
      Mockito.doThrow(new RuntimeException()).when(activityDetailsRepository).saveAll(any());
      state = saveTaskDetails.execute(state);
    } catch (Exception ex) {
      exceptionFlag = true;
    }
    Assert.assertTrue(exceptionFlag);
    Assert.assertNotNull(state);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE));
    Mockito.verify(activityDetailsRepository).saveAll(anyList());

  }

  @Test
  public void testExecute_NoOp() {
    State state = new State();
    SaveTaskDetails saveTaskDetailsWriteFalse = new SaveTaskDetails(activityDetailsRepository,
        false);
    TemplateDetails templateDetails = TemplateDetails.builder().id("id1").displayName("sample")
        .description("desc")
        .deployedDefinitionId("deployiD").definitionType(DefinitionType.SYSTEM)
        .creatorType(CreatorType.SYSTEM)
        .build();
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, templateDetails);

    List<ActivityDetail> activityDetails = new ArrayList<>();
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    		.activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
    saveTaskDetailsWriteFalse.execute(state);
    Mockito.verifyNoInteractions(activityDetailsRepository);
  }

}
