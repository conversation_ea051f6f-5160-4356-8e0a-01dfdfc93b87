package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_START_EVENTS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3SignalProcess;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.request.State;

import java.util.Map;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/** <AUTHOR> */
public class SignalUserDefinitionTaskTest {

  private ProcessDetailsRepository processDetailsRepository =
      Mockito.mock(ProcessDetailsRepository.class);
  private V3SignalProcess v3SignalProcess = Mockito.mock(V3SignalProcess.class);
  private V3RunTimeHelper runtimeHelper = Mockito.mock(V3RunTimeHelper.class);
  private WASContextHandler contextHandler = Mockito.mock(WASContextHandler.class);

  public SignalUserDefinitionTask getTask(ProcessDetails proccessDetails) {
    Optional<DefinitionDetails> defDetailsOptional = Optional.empty();

    return new SignalUserDefinitionTask(
        defDetailsOptional, proccessDetails, processDetailsRepository, v3SignalProcess,runtimeHelper);
  }

  @Test
  public void testErrorSignalling() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("test").getTriggerMessage(),
            contextHandler);
    ;
    transactionEntity.getEventHeaders().setBlockProcessOnSignalFailure(true);
    @SuppressWarnings("unchecked")
    Map<String, Object> startEvents = Mockito.mock(Map.class);
    State inputRequest = new State();
    inputRequest.addValue(BPMN_START_EVENTS, startEvents);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR));

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    SignalUserDefinitionTask signalUserDefinitionTask = getTask(proccessDetails);
    State response = signalUserDefinitionTask.execute(inputRequest);
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("pId");
    Assert.assertNotNull(resp);
    Mockito.verify(runtimeHelper, Mockito.times(1)).markProcessError(Mockito.any(), Mockito.any());
    Assert.assertEquals(TriggerStatus.ERROR_SIGNALING_PROCESS, resp.getStatus());
  }

  @Test
  public void testErrorSignallingProcessNotMarkedInErrorStateTest() {
    TransactionEntity transactionEntity =
		TransactionEntityFactory.getInstanceOf(
				TriggerHandlerTestData.prepareV3TriggerMessage("test").getTriggerMessage(),
	        contextHandler);
    transactionEntity.getEventHeaders().setBlockProcessOnSignalFailure(false);

    Map<String, Object> startEvents = Mockito.mock(Map.class);
    State inputRequest = new State();
    inputRequest.addValue(BPMN_START_EVENTS, startEvents);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR));

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    SignalUserDefinitionTask signalUserDefinitionTask = getTask(proccessDetails);
    State response = signalUserDefinitionTask.execute(inputRequest);
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("pId");
    Assert.assertNotNull(resp);
    Mockito.verify(processDetailsRepository, Mockito.times(0))
        .updateProcessStatus(Mockito.any(), Mockito.any());
    Assert.assertEquals(TriggerStatus.ERROR_SIGNALING_PROCESS, resp.getStatus());
  }

  @Test
  public void testNoAction() {
    TransactionEntity transactionEntity = Mockito.mock(TransactionEntity.class);
    @SuppressWarnings("unchecked")
    Map<String, Object> startEvents = Mockito.mock(Map.class);
    State inputRequest = new State();
    inputRequest.addValue(BPMN_START_EVENTS, startEvents);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(false);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    SignalUserDefinitionTask signalUserDefinitionTask = getTask(proccessDetails);
    State response = signalUserDefinitionTask.execute(inputRequest);
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("pId");
    Assert.assertNotNull(resp);
    Assert.assertEquals(TriggerStatus.NO_ACTION, resp.getStatus());
  }

  @Test
  public void testSuccessSignal() {
    TransactionEntity transactionEntity = Mockito.mock(TransactionEntity.class);
    @SuppressWarnings("unchecked")
    Map<String, Object> startEvents = Mockito.mock(Map.class);
    State inputRequest = new State();
    inputRequest.addValue(BPMN_START_EVENTS, startEvents);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(true);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    SignalUserDefinitionTask signalUserDefinitionTask = getTask(proccessDetails);
    State response = signalUserDefinitionTask.execute(inputRequest);
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("pId");
    Assert.assertNotNull(resp);
    Assert.assertEquals(TriggerStatus.PROCESS_SIGNALLED, resp.getStatus());
  }
}
