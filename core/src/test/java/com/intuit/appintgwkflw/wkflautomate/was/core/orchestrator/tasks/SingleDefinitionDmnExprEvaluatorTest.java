package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.DMNEngineConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationTaskTest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.commons.utils.IoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.config.client.ConfigServicePropertySourceLocator;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@Import(DMNEngineConfig.class)
@TestPropertySource(properties = {"camunda.bpm.dmnFeelEnableLegacyBehavior=true", "camunda.bpm.dmnEnableCustomTransformer=true"})
public class SingleDefinitionDmnExprEvaluatorTest {

  @MockBean
  ConfigServicePropertySourceLocator configServicePropertySourceLocator;

  @Autowired
  @Qualifier("juelDmnEngine")
  DmnEngine legacyDmnEngine;

  @Autowired
  @Qualifier("feelDmnEngine")
  DmnEngine feelDmnEngine;

  private static final Map<String, Object> schema = new HashMap<>();
  private final byte[] dmnDataWithJuelExpr =
      IoUtil.inputStreamAsByteArray(
          RuleEvaluationTaskTest.class
              .getClassLoader()
              .getResourceAsStream("dmn/decision_invoiceapproval_listRules_Juel.dmn"));

  private final byte[] dmnDataWithJuelExprLegacy =
      IoUtil.inputStreamAsByteArray(
          RuleEvaluationTaskTest.class
              .getClassLoader()
              .getResourceAsStream("dmn/decision_invoiceapproval_listRules_Juel_legacy.dmn"));

  private final byte[] dmnDataWithFeelExpr =
      IoUtil.inputStreamAsByteArray(
          RuleEvaluationTaskTest.class
              .getClassLoader()
              .getResourceAsStream("dmn/decision_invoiceapproval_listRules_Feel.dmn"));

  private final byte[] dmnMCDataWithFeelExpr =
      IoUtil.inputStreamAsByteArray(
          RuleEvaluationTaskTest.class
              .getClassLoader()
              .getResourceAsStream("dmn/decision_invoiceapproval_MultiCondition_Feel.dmn"));

  @Before
  public void setup() {
    schema.put(INTUIT_REALMID, "1234");
  }

  @Test
  public void testListContains_InputList_ContainsElement() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", "500")
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", "-2")
        .putValue("Customer", List.of("12", "32"))
        .putValue("SalesRep", "abcd")
        .putValue("DocNum", "inv003");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_ElementPresent_WithJuelEquals() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", "500")
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("Customer", "12");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithJuelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("Customer", null);
    res = evaluateDMNTask.evaluateDMN(dmnDataWithJuelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("Customer", List.of());
    res = evaluateDMNTask.evaluateDMN(dmnDataWithJuelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_ElementNotPresent_WithJuelEquals() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("Customer", "20");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithJuelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_ElementNotPresent_WithJuelEqualsLegacy() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("Customer", "20");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithJuelExprLegacy,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputList_DoesNotContainElement() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", -1)
        .putValue("Customer", List.of("30", "32"))
        .putValue("SalesRep", "abcd")
        .putValue("DocNum", "inv001");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputString_ContainsElement() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", -2)
        .putValue("Customer", List.of("12", "32"))
        .putValue("SalesRep", "abcd")
        .putValue("DocNum", "inv003");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.putValue("TxnDueDays", "-2");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.putValue("Customer", "12, 32");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.putValue("TxnDueDays", "");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    //Invalid case
    variables.putValue("TxnDueDays", List.of());
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputString_ContainsElementWithSingleElement()
      throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", -2)
        .putValue("Customer", List.of("12"))
        .putValue("SalesRep", "abcd")
        .putValue("DocNum", "inv003");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputString_DoesNotContainElement() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", -1)
        .putValue("Customer", List.of("30", "32"))
        .putValue("SalesRep", "abcd")
        .putValue("DocNum", "inv001");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputString_ContainsElement_OROp() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", "-2")
        .putValue("Customer", List.of("1", "32"))
        .putValue("SalesRep", "pqr")
        .putValue("DocNum", "inv003");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("SalesRep", "xyz");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("SalesRep", "abc");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("Customer", "");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("Customer", 1);
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testListContains_InputString_EqualsElement_OROp() throws JsonProcessingException {
    VariableMap variables = Variables.createVariables()
        .putValue("TxnAmount", 500)
        .putValue("TxnPaymentStatus", "UNPAID")
        .putValue("TxnDueDays", -2)
        .putValue("Customer", List.of("1", "32"))
        .putValue("SalesRep", "pqr")
        .putValue("DocNum", "inv003");

    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("DocNum", "inv001");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertFalse(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));

    variables.put("DocNum", "inv003");
    res = evaluateDMNTask.evaluateDMN(dmnDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertTrue(BooleanUtils.toBoolean(res.get(0).get("approvalRequired").toString()));
  }

  @Test
  public void testMulticondition() {
    VariableMap variables = Variables.createVariables()
        .putValue("Index", 0)
        .putValue("Class", "204100000000009617079")
        .putValue("TxnAmount", 500);
    SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(legacyDmnEngine,
        feelDmnEngine);
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnMCDataWithFeelExpr,
        new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertEquals("sendForApproval-3", res.get(0).get("decisionResult").toString());
  }

}