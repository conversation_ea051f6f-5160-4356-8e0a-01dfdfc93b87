package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/** <AUTHOR> */
public class UnsubscribeAppConnectTaskTest {

  private final AppConnectService appConnectService = Mockito.mock(AppConnectService.class);

  private final AuthDetailsService authDetailsService = Mockito.mock(AuthDetailsService.class);

  private final String REALM_ID_STRING = "1234";

  private final UnsubscribeAppConnectTask task =
      new UnsubscribeAppConnectTask(appConnectService, authDetailsService);

  @Test(expected = WorkflowGeneralException.class)
  public void testEmptyRealm() {

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(null))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_INPUT));
    task.execute(new State());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUnsubscribeException() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID_STRING)))
        .thenReturn(authDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID_STRING);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(REALM_ID_STRING))
        .thenThrow(new WorkflowGeneralException(WorkflowError.AUTH_DETAILS_NOT_FOUND));
    task.execute(state);
  }

  @Test
  public void test() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");

    AppConnectUnsubscribeResponse appConnectUnsubscribeResponse =
        new AppConnectUnsubscribeResponse();

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID_STRING)))
        .thenReturn(authDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID_STRING);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, false);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(REALM_ID_STRING))
        .thenReturn(authDetails);
    Mockito.when(appConnectService.unsubscribe(authDetails, true))
        .thenReturn(appConnectUnsubscribeResponse);
    appConnectService.unsubscribe(authDetails, false);
    Assert.assertEquals(
        appConnectUnsubscribeResponse, appConnectService.unsubscribe(authDetails, true));
    try {
      task.execute(state);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testOfflineTicketFlow() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");

    AppConnectUnsubscribeResponse appConnectUnsubscribeResponse =
        new AppConnectUnsubscribeResponse();

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID_STRING)))
        .thenReturn(authDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID_STRING);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(REALM_ID_STRING))
        .thenReturn(authDetails);
    Mockito.when(appConnectService.unsubscribe(authDetails, true))
        .thenReturn(appConnectUnsubscribeResponse);
    appConnectService.unsubscribe(authDetails, true);
    Assert.assertEquals(
        appConnectUnsubscribeResponse, appConnectService.unsubscribe(authDetails, true));
    try {
      task.execute(state);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testAllreadyUsubscribed() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID_STRING)))
        .thenReturn(authDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID_STRING);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, false);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(REALM_ID_STRING))
        .thenReturn(authDetails);

    Mockito.when(appConnectService.unsubscribe(authDetails, true))
        .thenThrow(
            new WorkflowGeneralException(
                WorkflowError.UNSUBSCRIBE_WORKFLOW_FAIL,
                "{\"errors\":[{\"details\":\" RESOURCE_NOT_FOUND\"}]}"));
    try {
      task.execute(state);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testAllreadyUsubscribedUsingOfflineTicket() {
    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subid");

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(eq(REALM_ID_STRING)))
        .thenReturn(authDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID_STRING);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(REALM_ID_STRING))
        .thenReturn(authDetails);

    Mockito.when(appConnectService.unsubscribe(authDetails, true))
        .thenThrow(
            new WorkflowGeneralException(
                WorkflowError.UNSUBSCRIBE_WORKFLOW_FAIL,
                "{\"errors\":[{\"details\":\" RESOURCE_NOT_FOUND\"}]}"));
    try {
      task.execute(state);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
