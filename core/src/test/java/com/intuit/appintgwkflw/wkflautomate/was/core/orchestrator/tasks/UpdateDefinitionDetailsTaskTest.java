package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class UpdateDefinitionDetailsTaskTest {

  private UpdateDefinitionDetailsTask updateDefinitionDetailsTask;

  @Test
  public void testExecute() {
    DefinitionDetailsRepository definitionDetailsRepository =
        Mockito.mock(DefinitionDetailsRepository.class);
    updateDefinitionDetailsTask = new UpdateDefinitionDetailsTask(definitionDetailsRepository);
    State state = new State();
    String defId = "def-id";
    String workflowId = "workflow";
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, defId);
    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);

    try {
      state = updateDefinitionDetailsTask.execute(state);
      Assert.assertEquals("def-id", state.getAll().get("definitionIdKey"));
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
