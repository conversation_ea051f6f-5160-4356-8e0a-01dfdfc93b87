package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

/** <AUTHOR> */
public class UpdateDefinitionForSchedulingMigrationTaskTest {

    private DefinitionDetails definitionDetails;
    private EventScheduleHelper eventScheduleHelper;
    private UpdateDefinitionForSchedulingMigrationTask task;

    @Before
    public void setUp() {
        eventScheduleHelper = Mockito.mock(EventScheduleHelper.class);
        definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("def123");
        definitionDetails.setDefinitionData(new byte[]{});
        definitionDetails.setPlaceholderValue("placeholder");
        task = new UpdateDefinitionForSchedulingMigrationTask(definitionDetails, eventScheduleHelper);
    }

    @Test
    public void testExecute_Success() {
        State state = new State();
        MockedStatic<SchedulingServiceUtil> schedulingServiceUtilMockedStatic =
                mockStatic(SchedulingServiceUtil.class);
        schedulingServiceUtilMockedStatic
                .when(() -> SchedulingServiceUtil.getUpdateDefinitionDataWithTime(any(), any()))
                .thenReturn(new byte[0]);
        schedulingServiceUtilMockedStatic
                .when(() -> SchedulingServiceUtil.getUpdatedPlaceholderValuesWithTime(any(), any()))
                .thenReturn("placeholder");
//        Mockito.doNothing().when(eventScheduleHelper).updateDefinitionForSchedulingMigration(any(), any(), any(), any());

        try{
            State result = task.execute(state);

            Mockito.verify(eventScheduleHelper, Mockito.times(1)).updateDefinitionForSchedulingMigration(any(), any(), any(), any());
            assertNull(result.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
        } finally {
            schedulingServiceUtilMockedStatic.close();
        }
    }

    @Test
    public void testExecute_withNullDefinitionData() {
        State state = new State();
        MockedStatic<SchedulingServiceUtil> schedulingServiceUtilMockedStatic =
                mockStatic(SchedulingServiceUtil.class);
        schedulingServiceUtilMockedStatic
                .when(() -> SchedulingServiceUtil.getUpdateDefinitionDataWithTime(any(), any()))
                .thenReturn(new byte[0]);
        schedulingServiceUtilMockedStatic
                .when(() -> SchedulingServiceUtil.getUpdatedPlaceholderValuesWithTime(any(), any()))
                .thenReturn("placeholder");
        definitionDetails.setDefinitionData(null);

        try{
            State result = task.execute(state);
            Mockito.verify(eventScheduleHelper, Mockito.times(1)).updateDefinitionForSchedulingMigration(any(), any(), any(), any());
            assertNull(result.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
        } finally {
            schedulingServiceUtilMockedStatic.close();
        }
    }

    @Test
    public void testExecute_DefinitionDetailsNull() {
        task = new UpdateDefinitionForSchedulingMigrationTask(null, eventScheduleHelper);
        State state = new State();

        try {
            task.execute(state);
            fail("Expected WorkflowGeneralException");
        } catch (WorkflowGeneralException e) {
            assertEquals(WorkflowError.INVALID_INPUT, e.getWorkflowError());
        }
    }

    @Test
    public void testExecute_ExceptionThrown() {
        State state = new State();
        Mockito.doThrow(new WorkflowGeneralException(WorkflowError.DEFINITION_UPDATE_ERROR)).when(eventScheduleHelper).updateDefinitionForSchedulingMigration(any(), any(), any(), any());
        try{
            State result = task.execute(state);
            Assert.fail();
        }catch (Exception e){
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void testOnError(){
        State state = task.onError(new State());
        Assert.assertNotNull(state);
        Assert.assertTrue(state.getValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE));
    }
}