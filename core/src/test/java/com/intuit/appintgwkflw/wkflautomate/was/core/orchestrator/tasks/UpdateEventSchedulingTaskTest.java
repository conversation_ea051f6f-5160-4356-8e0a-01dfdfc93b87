package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class UpdateEventSchedulingTaskTest {
    @Mock
    private SchedulingService schedulingService;

    @Mock
    private ActionModelToScheduleRequestMapper actionModelToCreateScheduleRequestMapper;

    @InjectMocks
    private UpdateEventSchedulingTask task;

    @Test
    public void testExecute_InvalidRealmIdOrScheduleStatus() {
        State state = new State();
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "");
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);

        try {
            task.execute(state);
            fail("Expected WorkflowGeneralException");
        } catch (WorkflowGeneralException e) {
            assertEquals(WorkflowError.INPUT_INVALID, e.getWorkflowError());
        }
    }

    @Test
    public void testExecute_ScheduleIdsEmpty() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = new ArrayList<>();
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService, Mockito.never()).updateSchedules(any(), any());
    }

    @Test
    public void testExecute_ActionModelsNull() {
        State state = new State();
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        try{
            task.execute(state);
            Assert.fail("Should have thrown exception");
        }catch (WorkflowGeneralException e){
            assertEquals(WorkflowError.INPUT_INVALID, e.getWorkflowError());
        }
    }

    @Test
    public void testExecute_SchedulesUpdatedSuccessfully() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
        List<SchedulingSvcResponse> responses = Collections.singletonList(new SchedulingSvcResponse());
        Mockito.when(schedulingService.updateSchedules(any(), any())).thenReturn(responses);

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService).updateSchedules(any(), eq("realmId"));
    }

    @Test
    public void testExecute_SchedulesUpdatedSuccessfullyWithUpdateComplete() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
        List<SchedulingSvcResponse> responses = Collections.singletonList(new SchedulingSvcResponse());
        Mockito.when(schedulingService.updateSchedules(any(), any())).thenReturn(responses);

        State result = task.execute(state);

        assertEquals(state, result);
        Mockito.verify(schedulingService).updateSchedules(any(), eq("realmId"));
    }


    @Test
    public void testExecute_SchedulesNotUpdatedForAllActions() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
        List<SchedulingSvcResponse> responses = new ArrayList<>();
        Mockito.when(schedulingService.updateSchedules(any(), any())).thenReturn(responses);
        try{
            task.execute(state);
            Assert.fail("Should have thrown exception");
        }catch (WorkflowGeneralException e){
            assertEquals(WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, e.getWorkflowError());
        }
    }

    @Test
    public void testExecute_ExceptionThrownDuringScheduleUpdate() {
        State state = new State();
        List<EventScheduleWorkflowActionModel> models = Collections.singletonList(new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule()));
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(models));
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realmId");
        SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
        state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
        Mockito.when(actionModelToCreateScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
        Mockito.when(schedulingService.updateSchedules(any(), eq("realmId"))).thenThrow(new WorkflowGeneralException(WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, "Error"));
        try{
            task.execute(state);
            Assert.fail("Should have thrown exception");
        }catch (WorkflowGeneralException e){
            assertEquals(WorkflowError.EVENT_SCHEDULING_CALL_FAILURE, e.getWorkflowError());
        }
    }


}
