package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;

public class DeployDefinitionRollBackTaskTest {

  private DeployDefinitionRollBackTask task;

  @Test
  public void testExecute() {
    
    BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest =  Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    
    task = new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest);
    State state = new State();
    String defId = "defId";
    state.addValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY, defId);
    
    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testExecute_Error() {

    BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest =  Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    doThrow(new RuntimeException()).when(bpmnEngineDefinitionServiceRest).deleteDeployment(any(DeleteDeploymentRequest.class)
    );
    task = new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest);
    State state = new State();
    String defId = "defId";
    state.addValue(AsyncTaskConstants.DEPLOYMENT_ID_KEY, defId);

    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testExecute_NoDefinitionId() {

    BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest =  Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    doThrow(new RuntimeException()).when(bpmnEngineDefinitionServiceRest).deleteDeployment(any(DeleteDeploymentRequest.class));
    task = new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest);
    State state = new State();
    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testFatal() {

    BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest =  Mockito.mock(BPMNEngineDefinitionServiceRest.class);
    task = new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest);
    Assert.assertFalse(task.isFatal());
  }
}
