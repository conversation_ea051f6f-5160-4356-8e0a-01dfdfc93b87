package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;

public class SaveAndActivateAppConnectRollBackTaskTest {

  private SaveAndActivateAppConnectRollBackTask task;

  @Test
  public void testExecute() {
    
    AppConnectService appConnectService = Mockito.mock(AppConnectService.class);
    AuthDetailsService authDetailsService =  Mockito.mock(AuthDetailsService.class);
    
    task = new SaveAndActivateAppConnectRollBackTask(appConnectService,authDetailsService);
    State state = new State();
    String defId = "defId";
    String workflowId = "workflow";
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, defId);
    state.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, workflowId);
    
    state = task.execute(state);
    Assert.assertNotNull(state);
  }
}
