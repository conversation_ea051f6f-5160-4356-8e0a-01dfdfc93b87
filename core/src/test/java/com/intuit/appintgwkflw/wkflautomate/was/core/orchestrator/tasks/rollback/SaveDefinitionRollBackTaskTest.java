package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import static org.mockito.ArgumentMatchers.anyString;

public class SaveDefinitionRollBackTaskTest {

  private SaveDefinitionRollBackTask task;

  @Test
  public void testExecute() {

    DefinitionDetailsRepository definitionDetailsRepository =
        Mockito.mock(DefinitionDetailsRepository.class);
    DefinitionActivityDetailsRepository definitionActivityDetailsRepository =
        Mockito.mock(DefinitionActivityDetailsRepository.class);
    EnabledDefinitionCacheService enabledDefinitionCacheService =
            Mockito.mock(EnabledDefinitionCacheService.class);

    task = new SaveDefinitionRollBackTask(definitionDetailsRepository, definitionActivityDetailsRepository, enabledDefinitionCacheService);
    State state = new State();
    String defId = "defId";
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, defId);

    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testExecute_Error() {
    DefinitionDetailsRepository definitionDetailsRepository =
        Mockito.mock(DefinitionDetailsRepository.class);
    DefinitionActivityDetailsRepository definitionActivityDetailsRepository =
        Mockito.mock(DefinitionActivityDetailsRepository.class);
    EnabledDefinitionCacheService enabledDefinitionCacheService =
            Mockito.mock(EnabledDefinitionCacheService.class);
    Mockito.when(
        definitionDetailsRepository.deleteByDefinitionIdOrParentId(anyString(), anyString()))
        .thenThrow(new RuntimeException());
    task = new SaveDefinitionRollBackTask(definitionDetailsRepository, definitionActivityDetailsRepository, enabledDefinitionCacheService);
    State state = new State();
    String defId = "defId";
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, defId);

    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testExecute_EmptyDefinitionId() {
    DefinitionDetailsRepository definitionDetailsRepository =
        Mockito.mock(DefinitionDetailsRepository.class);
    DefinitionActivityDetailsRepository definitionActivityDetailsRepository =
        Mockito.mock(DefinitionActivityDetailsRepository.class);
    EnabledDefinitionCacheService enabledDefinitionCacheService =
            Mockito.mock(EnabledDefinitionCacheService.class);
    task = new SaveDefinitionRollBackTask(definitionDetailsRepository, definitionActivityDetailsRepository, enabledDefinitionCacheService);
    State state =  task.execute(new State());
    Assert.assertNotNull(state);
  }
}
