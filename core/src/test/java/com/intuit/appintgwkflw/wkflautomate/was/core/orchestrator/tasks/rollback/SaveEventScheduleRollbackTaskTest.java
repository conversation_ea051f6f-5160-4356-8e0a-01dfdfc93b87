package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import java.util.List;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class SaveEventScheduleRollbackTaskTest {

  @InjectMocks
  private SaveEventScheduleRollbackTask saveEventScheduleRollbackTask;

  @Mock
  private EventScheduleHelper eventScheduleHelper;
  private final String REALM_ID = "reamlId";
  private final String DEF_ID = "defId";
  private final List<String> SCH_IDS = List.of("sch123");
  private MockedStatic<WorkflowLogger> mocked;


  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    mocked = Mockito.mockStatic(WorkflowLogger.class);
    ReflectionTestUtils.setField(saveEventScheduleRollbackTask, "eventScheduleHelper", eventScheduleHelper);
  }

  @After
  public void deregister() {
    mocked.reset();
    mocked.close();
  }

  @Test
  public void testExecuteRollbackFromESS() {
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEF_ID);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, SCH_IDS);
    state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE, true);
    saveEventScheduleRollbackTask.execute(state);
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).deleteSchedulesFromESS(SCH_IDS, REALM_ID);
    Mockito.verify(eventScheduleHelper, Mockito.times(0))
        .deleteAllSchedules(Mockito.any(), Mockito.any());
  }


  @Test
  public void testExecuteWithoutRealmId() {
    State state = new State();
    saveEventScheduleRollbackTask.execute(state);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Mockito.verify(eventScheduleHelper, Mockito.times(0))
        .deleteSchedulesFromESS(Mockito.any(), Mockito.any());
    Mockito.verify(eventScheduleHelper, Mockito.times(0))
        .deleteAllSchedules(Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteWithoutDefId() {
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);
    saveEventScheduleRollbackTask.execute(state);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Mockito.verify(eventScheduleHelper, Mockito.times(0))
        .deleteSchedulesFromESS(Mockito.any(), Mockito.any());
    Mockito.verify(eventScheduleHelper, Mockito.times(0))
        .deleteAllSchedules(Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteDeleteAllSchedules() {
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEF_ID);
    saveEventScheduleRollbackTask.execute(state);
    Mockito.verify(eventScheduleHelper, Mockito.times(0)).deleteSchedulesFromESS(Mockito.any(), Mockito.any());
    Mockito.verify(eventScheduleHelper, Mockito.times(1))
        .deleteAllSchedules(Mockito.any(), Mockito.any());
  }

  @Test
  public void testExecuteDeleteAllSchedulesThrowException() {
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, REALM_ID);
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, DEF_ID);
    Mockito.doThrow(new RuntimeException("Test")).when(eventScheduleHelper).deleteAllSchedules(Mockito.any(), Mockito.any());
    saveEventScheduleRollbackTask.execute(state);
    Mockito.verify(eventScheduleHelper, Mockito.times(0)).deleteSchedulesFromESS(Mockito.any(), Mockito.any());
    Mockito.verify(eventScheduleHelper, Mockito.times(1))
        .deleteAllSchedules(Mockito.any(), Mockito.any());
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString(), Mockito.any()));
  }
}
