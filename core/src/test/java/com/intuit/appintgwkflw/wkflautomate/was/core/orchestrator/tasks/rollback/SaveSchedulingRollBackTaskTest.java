package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.workflows.Definition;
import org.joda.time.LocalDate;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verifyNoMoreInteractions;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class SaveSchedulingRollBackTaskTest {
    @Mock
    private SchedulingService schedulingService;

    @Mock
    private State state;
    @InjectMocks
    private SaveSchedulingRollBackTask saveSchedulingRollBackTask;

    @Test
    public void testExecute_realmIdNotSet() {
        when(state.getValue(AsyncTaskConstants.REALM_ID_KEY)).thenReturn(null);

        saveSchedulingRollBackTask.execute(state);

        verify(state, times(1)).getValue(AsyncTaskConstants.REALM_ID_KEY);
        verifyNoMoreInteractions(state, schedulingService);
    }

    @Test
    public void testExecute_SchedulingMetaDataNotSet() {
        when(state.getValue(AsyncTaskConstants.REALM_ID_KEY)).thenReturn("realmId");
        when(state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA)).thenReturn(null);

        saveSchedulingRollBackTask.execute(state);

        verify(state, times(1)).getValue(AsyncTaskConstants.REALM_ID_KEY);
    }

    @Test
    public void testExecute_successfulDeletion() {
        when(state.getValue(AsyncTaskConstants.REALM_ID_KEY)).thenReturn("realmId");
        Definition definition = mock(Definition.class);
        when(state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA)).thenReturn(SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .definitionId("definitionId")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build());
        List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels = new ArrayList<>();
        eventScheduleWorkflowActionModels.add(new EventScheduleWorkflowActionModel("test", LocalDate.now(), new RecurrenceRule()));
        when(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST)).thenReturn(Optional.of(eventScheduleWorkflowActionModels));

        saveSchedulingRollBackTask.execute(state);

        verify(state, times(1)).getValue(AsyncTaskConstants.REALM_ID_KEY);
        verify(state, times(1)).getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
        verify(schedulingService, times(1)).deleteSchedules(anyList(), eq("realmId"));
    }

    @Test
    public void testExecute_exceptionDuringDeletion() {
        when(state.getValue(AsyncTaskConstants.REALM_ID_KEY)).thenReturn("realmId");
        Definition definition = mock(Definition.class);
        when(state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA)).thenReturn(SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .definitionId("definitionId")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build());
        List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels = new ArrayList<>();
        eventScheduleWorkflowActionModels.add(new EventScheduleWorkflowActionModel("test", LocalDate.now(), new RecurrenceRule()));
        when(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST)).thenReturn(Optional.of(eventScheduleWorkflowActionModels));
        doThrow(new RuntimeException("Exception during deletion")).when(schedulingService).deleteSchedules(anyList(), eq("realmId"));

        saveSchedulingRollBackTask.execute(state);

        verify(state, times(1)).getValue(AsyncTaskConstants.REALM_ID_KEY);
        verify(state, times(1)).getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
        verify(schedulingService, times(1)).deleteSchedules(anyList(), eq("realmId"));
    }

    @Test
    public void testExecute_exceptionDuringDeletionEmptyActionModel() {
        when(state.getValue(AsyncTaskConstants.REALM_ID_KEY)).thenReturn("realmId");
        Definition definition = mock(Definition.class);
        when(state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA)).thenReturn(SchedulingMetaData.builder()
                .status(Status.ACTIVE)
                .definitionKey("key")
                .definitionId("definitionId")
                .recurrenceRule(new RecurrenceRule())
                .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build());
        List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels = new ArrayList<>();
        when(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST)).thenReturn(Optional.of(eventScheduleWorkflowActionModels));

        saveSchedulingRollBackTask.execute(state);

        verify(state, times(1)).getValue(AsyncTaskConstants.REALM_ID_KEY);
        verify(state, times(1)).getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
        verify(schedulingService, times(0)).deleteSchedules(anyList(), eq("realmId"));
    }
}