package com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessingHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DaysTransformer;

import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Description;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BpmnProcessingHelperTest {

  private static final String CUSTOM_WORKFLOW_DMN = TestHelper.readResourceAsString(
      "dmn/multiConditionDMN.dmn");
  @Mock
  private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private Map<String, String> operatorMap;
  private Map<String, String> operatorToSymbolMap;
  private DmnModelInstance multiConditionDmnInstance;
  @Mock
  private FeatureFlagManager featureFlagManager;

  @Before
  @SneakyThrows
  public void setup() {
    operatorMap = new HashMap<>();
    operatorToSymbolMap = new HashMap<>();
    operatorMap.put("GT", ">");
    operatorMap.put("GTE", ">=");
    operatorMap.put("LT", "<");
    operatorMap.put("LTE", "<=");
    operatorMap.put("EQ", "==");
    operatorMap.put("CONTAINS", "contains");
    operatorMap.put("NOT_CONTAINS", "!contains");
    operatorToSymbolMap.put(">", "GT");
    operatorToSymbolMap.put(">=", "GTE");
    operatorToSymbolMap.put("<", "LT");
    operatorToSymbolMap.put("<=", "LTE");
    operatorToSymbolMap.put("==", "EQ");
    multiConditionDmnInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN, Charset.defaultCharset()));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DAYS, new DaysTransformer());
  }

  @Test
  public void testGetRuleExpressionForString() {
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    String ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Customer",
            "string",
            Collections.emptySet(),
            "Customer.equals(\"1\")");
    Assert.assertEquals("CONTAINS 1", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Customer",
            "string",
            Collections.emptySet(),
            "Customer.equals(\"1\") || Customer.equals(\"2\") ");
    Assert.assertEquals("CONTAINS 1,2", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Customer",
            "string",
            Collections.emptySet(),
            "Customer.contains(\"1\") || Customer.contains(\"2\") ");
    Assert.assertEquals("ANY_MATCH 1,2", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Customer",
            "string",
            Collections.emptySet(),
            "!Customer.contains(\"1\") && !Customer.contains(\"2\") ");
    Assert.assertEquals("NO_MATCH 1,2", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Customer",
            "string",
            Collections.emptySet(),
            "!Customer.equals(\"1\") && !Customer.equals(\"2\") ");
    Assert.assertEquals("NOT_CONTAINS 1,2", ruleExpression);
  }

  @Test
  public void testGetRuleExpressionForSelectAll() {
    Set<String> containsAllSet = new HashSet<>();
    Collections.addAll(containsAllSet, "Customer", "Term");
    String ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression("Customer", "string", containsAllSet, "");
    Assert.assertEquals("CONTAINS ALL_CUSTOMER", ruleExpression);
    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Term", "string", Collections.emptySet(), "Term.equals(\"1\")");
    Assert.assertEquals("CONTAINS 1", ruleExpression);
  }

  @Test
  public void testGetRuleExpressionForDays() {
    String ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression("DueDate", "days",
            Collections.emptySet(), "DueDate == -3");
    Assert.assertEquals("BF 3", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression("DueDate", "days",
            Collections.emptySet(), "DueDate == 2");
    Assert.assertEquals("AF 2", ruleExpression);
  }

  @Test
  public void testGetRuleExpressionForNonString() {
    Mockito.when(workflowGlobalConfiguration.getOperatorToSymbolMap())
        .thenReturn(operatorToSymbolMap);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    String ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Amount",
            "double",
            Collections.emptySet(),
            "Amount > 100");
    Assert.assertEquals("GT 100", ruleExpression);

    ruleExpression =
        BpmnProcessingHelper.getUIFriendlyRuleExpression(
            "Amount", "double", Collections.emptySet(),
            "Amount > 100 && Amount <= 200");
    Assert.assertEquals("GT 100 && LTE 200", ruleExpression);
  }

  @Test
  public void testGetSelectAllParametersForRule() {
    Collection<DecisionTable> decisionTables = multiConditionDmnInstance.getModelElementsByType(
        DecisionTable.class);
    DecisionTable decisionTable = decisionTables.stream().findFirst().get();
    Rule rule = decisionTable.getRules().stream().findFirst().get();
    Assert.assertNotNull(BpmnProcessingHelper.getSelectAllParametersForRule(rule));
  }

  @Test
  public void testGetSelectAllParametersForRuleWithDescription() {
    Collection<DecisionTable> decisionTables = multiConditionDmnInstance.getModelElementsByType(
        DecisionTable.class);
    DecisionTable decisionTable = decisionTables.stream().findFirst().get();
    Rule rule = decisionTable.getRules().stream().findFirst().get();
    Assert.assertNotNull(BpmnProcessingHelper.getSelectAllParametersForRule(rule));
    Description description = multiConditionDmnInstance.newInstance(Description.class);
    description.setTextContent("hello:HELLO, bye");
    rule.setDescription(description);
    Assert.assertNotNull(BpmnProcessingHelper.getSelectAllParametersForRule(rule));
  }
}
