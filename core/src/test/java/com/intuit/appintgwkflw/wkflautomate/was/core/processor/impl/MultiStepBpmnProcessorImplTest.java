package com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl;

import static com.intuit.v4.GlobalId.create;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.MultiStepBuilderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ProcessWorkflowStepFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepDefinitionConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepTemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiStepPlaceholderSubstitutor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DaysTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepBpmnProcessorImplTest {
    private static final String REALM_ID = "12345";
    private static final String LOCAL_ID_BPMN = "etet2-2434j2-3232fl-33ff";
    private static final String CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");
    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
    Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
    DmnModelInstance dmnModelInstance;
    BpmnModelInstance multiConditionBpmnModelInstance;
    @Mock
    private TemplateDetails bpmnTemplateDetail;
    private Map<String, DmnModelInstance> dmnModelInstanceMap = new HashMap<>();
    private DefinitionDetails definitionDetailsBpmn =
            TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, LOCAL_ID_BPMN);
    private MultiStepBpmnProcessorImpl multiStepBpmnProcessorImpl;
    private MultiStepTemplateBuilder multiStepTemplateBuilder;
    private MultiStepPlaceholderSubstitutor multiStepPlaceholderSubstitutor;
    @InjectMocks
    private CustomWorkflowConfig customWorkflowConfig;
    @Mock
    private BpmnProcessorImpl bpmnProcessor;
    private TemplateBuilder templateBuilder;
    @Mock
    private WASContextHandler wasContextHandler;
    @Mock
    private TemplateConditionBuilder conditionBuilder;
    @Mock
    private TemplateActionBuilder actionBuilder;
    @Mock
    private FeatureFlagManager featureFlagManager;
    @Mock
    private TranslationService translationService;
    @Mock
    private TemplateLabelsService templateLabelsService;
    @Mock
    private MultiStepBuilderFactory multiStepBuilderFactory;
    @Mock
    private ProcessWorkflowStepFactory processWorkflowStepFactory;
    @Mock
    private MultiStepDefinitionActionBuilder multiStepDefinitionActionBuilder;
    @Mock
    private MultiStepDefinitionConditionBuilder multiStepDefinitionConditionBuilder;
    @Before
    public void setup() throws Exception {
        dmnModelInstance = Dmn.readModelFromStream(
                IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
        multiConditionBpmnModelInstance =
                Bpmn.readModelFromStream(
                        IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
        DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DAYS, new DaysTransformer());
        ReflectionTestUtils.setField(bpmnProcessor, "translationService", TestHelper.initTranslationService());
        ReflectionTestUtils.setField(bpmnProcessor, "wasContextHandler", wasContextHandler);
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        multiStepPlaceholderSubstitutor = new MultiStepPlaceholderSubstitutor(customWorkflowConfig);
        multiStepBuilderFactory = new MultiStepBuilderFactory(
                multiStepDefinitionActionBuilder,
                multiStepDefinitionConditionBuilder);
        templateBuilder = new TemplateBuilder(
                conditionBuilder,
                actionBuilder,
                customWorkflowConfig,
                wasContextHandler,
                featureFlagManager,
                translationService,
                templateLabelsService);
        multiStepTemplateBuilder = new MultiStepTemplateBuilder(
                conditionBuilder,
                actionBuilder,
                customWorkflowConfig,
                wasContextHandler,
                featureFlagManager,
                translationService,
                templateLabelsService,
                processWorkflowStepFactory);
        multiStepBpmnProcessorImpl = new MultiStepBpmnProcessorImpl<>(
                templateBuilder,
                multiStepBuilderFactory,
                multiStepTemplateBuilder,
                customWorkflowConfig);
    }

    @Test
    public void testProcessBpmn() {
        TemplateDetails templateDetails = TestHelper.mockTemplateDetailsObject();
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        Map<String, ActivityInstance> activityInstanceMap = new HashMap<>();
        activityInstanceMap.put("action-2", activityInstance);
        activityInstanceMap.put("action-3", activityInstance);
        activityInstanceMap.put("action-4", activityInstance);
        activityInstanceMap.put("action-5", activityInstance);
        ActivityInstance dmnActivityInstance = ActivityInstance.builder()
                .dmnModelInstance(dmnModelInstance)
                .childActivityInstances(new HashMap<>())
                .build();
        activityInstanceMap.put("decisionElement", dmnActivityInstance);
        final Definition definition = new Definition();
        definition.setId(create(
                REALM_ID,
                WorkflowConstants.DEFINITION_TYPE_ID,
                definitionDetailsBpmn.getDefinitionId()));
        definition.setRecordType(definitionDetailsBpmn.getRecordType().getRecordType());
        definition.setStatus(WorkflowStatusEnum.ENABLED);
        DefinitionInstance definitionInstance = new DefinitionInstance(
                definition,
                multiConditionBpmnModelInstance,
                Arrays.asList(dmnModelInstance),
                templateDetails);
        definitionInstance.setActivityInstanceMap(activityInstanceMap);
        Template template = (Template) multiStepBpmnProcessorImpl.processBpmn(
                definitionInstance,
                create(
                        REALM_ID,
                        WorkflowConstants.TEMPLATE_TYPE_ID,
                        templateDetails.getId()),
                true);
        Assert.assertNotNull(template);
        Assert.assertEquals("customApproval", template.getName());
        Assert.assertEquals(WorkflowStatusEnum.ENABLED, template.getStatus());
        Assert.assertEquals("CUSTOM", template.getCategory());
        Assert.assertNotNull(template.getWorkflowSteps());
        Assert.assertNotNull(template.getTemplateData());
        Assert.assertNotNull(template.getTemplateData().getActionGroup());
        Assert.assertEquals(1, template.getTemplateData().getActionGroup().size());
        Assert.assertEquals("approval", template.getTemplateData().getActionGroup().get(0).getActionKey());
    }

    @Test
    public void testProcessBpmnWithMap() throws IOException {
        Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
        Assert.assertNull(multiStepBpmnProcessorImpl.processBpmn(map));
    }
}