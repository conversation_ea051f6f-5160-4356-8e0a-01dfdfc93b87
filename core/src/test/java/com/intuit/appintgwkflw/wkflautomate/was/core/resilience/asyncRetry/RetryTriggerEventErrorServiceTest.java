package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.asyncRetry;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.EntityRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.RetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RetryTriggerEventErrorServiceTest {

  @InjectMocks
  RetryTriggerEventErrorService retryTriggerEventErrorService;

  @Mock
  WASContextHandler wasContextHandler;

  @Mock
  TriggerEventPublisher triggerEventPublisher;

  @Mock
  RetryConfig retryConfig;

  private Map<String, Object> triggerMessage;
  private Map<String, EntityRetryConfig> asyncRetryProcessingConfig;
  private Map<String, String> eventHeaders;

  private Map<String, Object> entity;
  private EntityRetryConfig entityRetryConfig;

  @Before
  public void setup() {
    triggerMessage = new HashMap<>();

    entity = new HashMap<>();

    eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityType", "Invoice");
    eventHeaders.put("entityId", "400");

    asyncRetryProcessingConfig = new HashMap<>();
    entityRetryConfig = new EntityRetryConfig();
  }

  @Test
  public void test_publishTriggerEventsToKafka_success() {
    Map<String, String> invoice = new HashMap<>();
    invoice.put("TxnId", "400");
    entity.put("invoice", invoice);
    triggerMessage.put("entity", entity);

    triggerMessage.put("eventHeaders", eventHeaders);

    entityRetryConfig.setEnabled(true);
    entityRetryConfig.setWorkflows(List.of("approval"));
    asyncRetryProcessingConfig.put("trigger", entityRetryConfig);

    when(retryConfig.getAsyncRetryProcessing()).thenReturn(asyncRetryProcessingConfig);

    retryTriggerEventErrorService.processFailedTriggerEvent(triggerMessage,
        TriggerTargetAPI.TRIGGER_V1);

    verify(triggerEventPublisher, times(1)).publishTriggerEvent(any());
  }

  @Test
  public void test_publishTriggerEventsToKafka_forAsyncEvents() {
    triggerMessage.put(WorkflowConstants.SOURCE, WorkflowConstants.EVENT);
    retryTriggerEventErrorService.processFailedTriggerEvent(triggerMessage,
        TriggerTargetAPI.TRIGGER_V2);

    verify(triggerEventPublisher, never()).publishTriggerEvent(any());
  }

  @Test
  public void test_publishTriggerEventToKafka_withEmptyConfig() {

    Map<String, String> invoice = new HashMap<>();
    invoice.put("TxnId", "400");
    entity.put("invoice", invoice);
    triggerMessage.put("entity", entity);

    triggerMessage.put("eventHeaders", eventHeaders);

    entityRetryConfig.setEnabled(false);
    entityRetryConfig.setWorkflows(List.of("approval"));
    asyncRetryProcessingConfig.put("trigger", entityRetryConfig);
    when(retryConfig.getAsyncRetryProcessing()).thenReturn(asyncRetryProcessingConfig);

    retryTriggerEventErrorService.processFailedTriggerEvent(triggerMessage,
        TriggerTargetAPI.TRIGGER_V1);

    verify(triggerEventPublisher, never()).publishTriggerEvent(any());
  }

}
