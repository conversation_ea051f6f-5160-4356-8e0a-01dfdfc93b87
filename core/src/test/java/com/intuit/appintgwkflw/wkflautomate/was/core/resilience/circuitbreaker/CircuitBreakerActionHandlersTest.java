package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.ExternalTaskCompletionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class CircuitBreakerActionHandlersTest {

    @Mock
    CircuitBreakerActionHandler circuitBreakerActionHandler;

    @Test
    public void testGetWithAndWithoutHandlers() {
        Assert.assertEquals(Optional.empty(),
                CircuitBreakerActionHandlers.getHandler(null));

        CircuitBreakerActionHandlers.addHandler(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, circuitBreakerActionHandler);
        Assert.assertNotEquals(Optional.empty(),
                CircuitBreakerActionHandlers.getHandler(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT));
    }

}
