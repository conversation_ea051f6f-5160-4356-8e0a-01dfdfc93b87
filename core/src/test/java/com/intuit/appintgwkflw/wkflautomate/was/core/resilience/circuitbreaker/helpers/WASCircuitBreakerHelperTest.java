package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerRegistryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.Mockito.spy;

@RunWith(MockitoJUnitRunner.class)
public class WASCircuitBreakerHelperTest {

    @Mock
    private CircuitBreakerActionHandler circuitBreakerActionHandler;

    private final MeterRegistry meterRegistry = spy(SimpleMeterRegistry.class);

    @InjectMocks
    private WASCircuitBreakerHelper wasCircuitBreakerHelper;

    @Test
    public void testCreateCircuitBreaker() {
        WASCircuitBreakerRegistryConfig wasCircuitBreakerRegistryConfig = new WASCircuitBreakerRegistryConfig();
        wasCircuitBreakerRegistryConfig.setSlidingWindowSize(23);
        wasCircuitBreakerRegistryConfig.setFailureRateThreshold(50);
        wasCircuitBreakerRegistryConfig.setMinimumNumberOfCalls(4);
        wasCircuitBreakerRegistryConfig.setPermittedNumberOfCallsInHalfOpen(4);
        wasCircuitBreakerRegistryConfig.setWaitDurationInOpen(400);
        wasCircuitBreakerRegistryConfig.setRecordExceptions(Collections.emptyList());
        CircuitBreaker cb = wasCircuitBreakerHelper.createCircuitBreaker("test", wasCircuitBreakerRegistryConfig);
        Assert.assertEquals("test", cb.getName());
        Assert.assertEquals(CircuitBreaker.State.CLOSED, cb.getState());
        Assert.assertEquals(23, cb.getCircuitBreakerConfig().getSlidingWindowSize());
        Assert.assertEquals(4, cb.getCircuitBreakerConfig().getMinimumNumberOfCalls());
        Assert.assertEquals(400, cb.getCircuitBreakerConfig().getWaitDurationInOpenState().toMillis());
    }

    @Test
    public void testAddEventHandler() {
        CircuitBreaker cb = CircuitBreaker.ofDefaults("test");
        wasCircuitBreakerHelper.addCircuitBreakerEventHandler(cb, circuitBreakerActionHandler);
        cb.transitionToOpenState();
        Mockito.verify(circuitBreakerActionHandler, Mockito.times(1)).handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.CLOSED_TO_OPEN);
    }

    @Test
    public void testAddEventHandlerForSameStateTransition() {
        CircuitBreaker cb = CircuitBreaker.ofDefaults("test");
        wasCircuitBreakerHelper.addCircuitBreakerEventHandler(cb, circuitBreakerActionHandler);
        cb.transitionToClosedState();
        cb.reset();
        Mockito.verify(circuitBreakerActionHandler, Mockito.times(0)).handleCircuitBreakerStateTransitionEvent(Mockito.any());
    }

    @Test
    public void closeEmptySet() {
        wasCircuitBreakerHelper.closeCircuitBreakers(new HashSet<>());
    }

    @Test
    public void testCloseAndDisableCircuitBreakers(){
        CircuitBreaker cb1 = CircuitBreaker.ofDefaults("test1");
        CircuitBreaker cb2 = CircuitBreaker.ofDefaults("test2");
        Set<CircuitBreaker> cbSet = new HashSet<>();
        cbSet.add(cb1); cbSet.add(cb2);
        cb1.transitionToOpenState();
        cb2.transitionToDisabledState();
        wasCircuitBreakerHelper.closeCircuitBreakers(cbSet);
        Assert.assertTrue(wasCircuitBreakerHelper.areAllCircuitBreakersClosed(cbSet));
        wasCircuitBreakerHelper.disableCircuitBreakers(cbSet);
        Assert.assertTrue(cbSet.stream().allMatch(cb ->
                CircuitBreaker.State.DISABLED.equals(cb.getState())));
    }

    @Test
    public void testAllCBsClosed() {
        CircuitBreaker cb1 = CircuitBreaker.ofDefaults("test1");
        CircuitBreaker cb2 = CircuitBreaker.ofDefaults("test2");
        Set<CircuitBreaker> cbSet = new HashSet<>();
        cbSet.add(cb1); cbSet.add(cb2);
        cb1.transitionToOpenState();
        Assert.assertFalse(wasCircuitBreakerHelper.areAllCircuitBreakersClosed(cbSet));
        cb1.transitionToClosedState();
        Assert.assertTrue(wasCircuitBreakerHelper.areAllCircuitBreakersClosed(cbSet));
    }

    @Test
    public void disableEmptySet() {
        wasCircuitBreakerHelper.disableCircuitBreakers(new HashSet<>());
    }
}
