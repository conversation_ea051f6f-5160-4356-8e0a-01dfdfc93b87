package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerRegistryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.helpers.WASCircuitBreakerHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.DownStreamConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class WASCircuitBreakerServiceTest {
    @Mock
    private WASCircuitBreakerConfiguration wasCircuitBreakerConfiguration;
    @Mock
    private WASCircuitBreakerHelper wasCircuitBreakerHelper;
    @Mock
    private OfferingConfig offeringConfig;

    @InjectMocks
    private WASCircuitBreakerService wasCircuitBreakerService;

    @Captor
    private ArgumentCaptor<Set<CircuitBreaker>> argumentCaptor;

    @Captor
    private ArgumentCaptor<String> stringArgumentCaptor;

    @Test
    public void testCreateCircuitBreakersWhenNoInstances() {
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(null);
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(0)).createCircuitBreaker(Mockito.any(), Mockito.any());
    }

    @Test
    public void testCreateDisabledCircuitBreaker() {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setEnabled(false);
        config.setOfferingSpecific(false);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(0)).createCircuitBreaker(Mockito.any(), Mockito.any());
    }

    @Test
    public void testCreateOfferingSpecificCircuitBreakers() {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setOfferingSpecific(true);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getDownstreamServices());
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(2)).createCircuitBreaker(stringArgumentCaptor.capture(), Mockito.any());
        List<String> expectedCBNames = new ArrayList<>();
        expectedCBNames.add("firstTestOffering".concat(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
        expectedCBNames.add("secondTestOffering".concat(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
        Assert.assertEquals(expectedCBNames, stringArgumentCaptor.getAllValues());
    }

    @Test
    public void testCreateOfferingSpecificCircuitBreakersWithNoDownstreamServices() {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setOfferingSpecific(true);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(new ArrayList<>());
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(0)).createCircuitBreaker(Mockito.any(), Mockito.any());
    }

    @Test
    public void testCreateNonOfferingSpecificCircuitBreakers() {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setOfferingSpecific(false);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(1)).createCircuitBreaker(stringArgumentCaptor.capture(), Mockito.any());
        Assert.assertEquals(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString(), stringArgumentCaptor.getValue());

    }

    @Test
    public void testCreateWithoutActionHandler() {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setOfferingSpecific(false);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        wasCircuitBreakerService.createCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(0)).addCircuitBreakerEventHandler(Mockito.any(), Mockito.any());
    }

    @Test
    public void testCreateWithActionHandler() {
        CircuitBreakerActionHandlers.addHandler(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, new CircuitBreakerActionHandler() {
            @Override
            public CircuitBreakerActionType getName() {
                return null;
            }

            @Override
            public void handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition stateTransitionEvent) {

            }
        });
        createCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString());
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(1)).addCircuitBreakerEventHandler(Mockito.any(), Mockito.any());
        // Clear CircuitBreakerActionHandlers map so that it is not used for other tests
        CircuitBreakerActionHandlers.addHandler(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, null);
    }

    @Test
    public void testEnableWhenCircuitBreakersAlreadyCreated() {
        // Test should only enable circuit breakers, not create new ones
        createCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString());
        wasCircuitBreakerService.enableAllCircuitBreakers();
        // Should be called once only when creating circuit breakers
        Mockito.verify(wasCircuitBreakerConfiguration, Mockito.times(1)).getInstances();
        Mockito.verify(wasCircuitBreakerHelper).closeCircuitBreakers(argumentCaptor.capture());
        Assert.assertTrue(argumentCaptor.getValue().size() == 1);
    }

    @Test
    public void testEnableWhenCircuitBreakersNotYetCreated() {
        // Test should both create and enable circuit breakers when enabling for first time
        wasCircuitBreakerService.enableAllCircuitBreakers();
        // Must be called only once during the createCircuitBreaker() call
        Mockito.verify(wasCircuitBreakerConfiguration, Mockito.times(1)).getInstances();
        Mockito.verify(wasCircuitBreakerHelper).closeCircuitBreakers(argumentCaptor.capture());
        // No circuit breakers created since no instances provided
        Assert.assertTrue(argumentCaptor.getValue().size() == 0);
    }

    @Test
    public void testDisableEmtpySet() {
        wasCircuitBreakerService.disableAllCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper).disableCircuitBreakers(argumentCaptor.capture());
        Assert.assertTrue(argumentCaptor.getValue().size() == 0);
    }

    @Test
    public void testDisableAll() {
        createCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString());
        wasCircuitBreakerService.disableAllCircuitBreakers();
        Mockito.verify(wasCircuitBreakerHelper).disableCircuitBreakers(argumentCaptor.capture());
        Assert.assertTrue(argumentCaptor.getValue().size() == 1);
    }

    @Test
    public void testAreAllCircuitBreakersOfActionTypeClosed() {
        createCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString());
        wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT);
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(1)).areAllCircuitBreakersClosed(argumentCaptor.capture());
        Assert.assertTrue(argumentCaptor.getValue().size() == 1);
    }

    @Test
    public void testCloseAllCircuitBreakersOfActionType() {
        createCircuitBreaker("testOffering1".concat(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
        createCircuitBreaker("testOffering2".concat(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
        wasCircuitBreakerService.closeAllCircuitBreakersOfActionType(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT);
        Mockito.verify(wasCircuitBreakerHelper, Mockito.times(1)).closeCircuitBreakers(argumentCaptor.capture());
        Assert.assertTrue(argumentCaptor.getValue().size() == 2);
    }

    @Test
    public void testGetCircuitBreaker() {
        createCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString());
        Assert.assertEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreaker(null));
        Assert.assertEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreaker("fail"));
        Assert.assertNotEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreaker(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
    }

    @Test
    public void testGetCircuitBreakerForOffering() {
        createCircuitBreaker("testOffering1".concat(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT.toString()));
        Assert.assertEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreakerForOffering(null));
        WASContext.setOfferingId("testOffering1");
        Assert.assertNotEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreakerForOffering(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT));
        WASContext.setOfferingId("testOffering3");
        Assert.assertEquals(Optional.empty(), wasCircuitBreakerService.getCircuitBreakerForOffering(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT));
    }

    private List<DownStreamConfig> getDownstreamServices() {
        List<DownStreamConfig> downStreamConfigs = new ArrayList<>();
        DownStreamConfig downStreamConfig = new DownStreamConfig();
        downStreamConfig.setOfferingId("firstTestOffering");
        downStreamConfigs.add(downStreamConfig);
        DownStreamConfig downStreamConfig2 = new DownStreamConfig();
        downStreamConfig2.setOfferingId("secondTestOffering");
        downStreamConfigs.add(downStreamConfig2);
        return downStreamConfigs;
    }

    private void createCircuitBreaker(String nameForCB) {
        WASCircuitBreakerRegistryConfig config = new WASCircuitBreakerRegistryConfig();
        config.setOfferingSpecific(false);
        Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> mapp = new HashMap<>();
        mapp.put(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, config);
        Mockito.when(wasCircuitBreakerConfiguration.getInstances()).thenReturn(mapp);
        Mockito.when(wasCircuitBreakerHelper.createCircuitBreaker(Mockito.any(), Mockito.any())).thenReturn(CircuitBreaker.ofDefaults(nameForCB));
        wasCircuitBreakerService.createCircuitBreakers();
    }
}