package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.drresiliency;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.config.AsyncProcessingConfig;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.List;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ApiToEventConvertorTest {
  @Mock
  private WASContextHandler wasContextHandler;
  @Mock
  private TriggerEventPublisher triggerEventPublisher;
  @Mock
  private AsyncProcessingConfig asyncProcessingConfig;

  @Mock
  private HttpServletRequest request;
  @Mock
  private HttpServletResponse response;

  String requestBody = "{ \n"
      + "   \"eventHeaders\":{ \n"
      + "   \t  \"workflow\":\"%s\",\n"
      + "   \t  \"entityType\":\"engagement\",\n"
      + "      \"entityChangeType\":\"created\"\n"
      + "   },\n"
      + "   \"entity\":{ \n"
      + "       \"Engagement\": {\n"
      + "         \"Id\": \"6\",\n"
      + "         \"engagementId\": \"6\",\n"
      + "         \"milestone\": \"test\",\n"
      + "         \"lastKnownAssignments\": \"true\"\n"
      + "      }\n"
      + "   }\n"
      + "}";

  @InjectMocks
  private ApiToEventConvertor apiToEventConvertor;

  @Test
  public void processTriggerEvent_asyncProcessingDisabled(){
    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      apiToEventConvertor.processTriggerEvent(request, response);
    });

    String expectedMessage = WorkflowError.REGION_INACTIVE_ERROR.getErrorMessage();
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  public void processTriggerEvent_nonTriggerAPI(){
    Mockito.when(request.getRequestURI()).thenReturn("/v1/template/save");
    Mockito.when(asyncProcessingConfig.isEnabled()).thenReturn(true);

    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      apiToEventConvertor.processTriggerEvent(request, response);
    });

    String expectedMessage = WorkflowError.REGION_INACTIVE_ERROR.getErrorMessage();
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  public void test_workflowNotSupportAsync() throws IOException {
    Mockito.when(request.getRequestURI()).thenReturn("/v1/trigger");
    Mockito.when(asyncProcessingConfig.isEnabled()).thenReturn(true);
    Mockito.when(asyncProcessingConfig.getEndpoints()).thenReturn(List.of("/v1/trigger", "/v2/trigger"));
    Mockito.when(asyncProcessingConfig.getWorkflows()).thenReturn(List.of("engagementTTLiveFullServiceTestConsumer_test"));

    ServletInputStream servletInputStream = getInputStream("TTLiveFullServiceTestConsumer_PS");
    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(servletInputStream));

    Mockito.when(request.getReader()).thenReturn(bufferedReader);

    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      apiToEventConvertor.processTriggerEvent(request, response);
    });

    String expectedMessage = WorkflowError.REGION_INACTIVE_ERROR.getErrorMessage();
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  public void test_success() throws IOException {
    Mockito.when(request.getRequestURI()).thenReturn("/v1/trigger");
    Mockito.when(asyncProcessingConfig.isEnabled()).thenReturn(true);
    Mockito.when(asyncProcessingConfig.getEndpoints()).thenReturn(List.of("/v1/trigger", "/v2/trigger"));
    Mockito.when(asyncProcessingConfig.getWorkflows()).thenReturn(List.of("engagementTTLiveFullServiceTestConsumer_PS"));

    ServletInputStream servletInputStream = getInputStream("TTLiveFullServiceTestConsumer_PS");
    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(servletInputStream));

    Mockito.when(request.getReader()).thenReturn(bufferedReader);

    PrintWriter printWriter = Mockito.mock(PrintWriter.class);
    Mockito.doNothing().when(printWriter).println(Mockito.anyString());
    Mockito.when(response.getWriter()).thenReturn(printWriter);

    apiToEventConvertor.processTriggerEvent(request, response);
    Mockito.verify(response).setStatus(202);
  }

  private ServletInputStream getInputStream(String val){
    String reqBody = String.format(requestBody, val);
    ByteArrayInputStream inputStream = new ByteArrayInputStream(reqBody.getBytes());
    return new ServletInputStream() {
      @Override
      public boolean isFinished() {
        return false;
      }

      @Override
      public boolean isReady() {
        return false;
      }

      @Override
      public void setReadListener(ReadListener readListener) {

      }

      @Override
      public int read() {
        return inputStream.read();
      }
    };
  }
}