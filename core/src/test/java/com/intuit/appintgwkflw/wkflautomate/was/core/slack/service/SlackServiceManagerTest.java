package com.intuit.appintgwkflw.wkflautomate.was.core.slack.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.JiraConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.slack.model.SlackRequestModel;
import com.slack.api.Slack;
import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.SlackApiException;
import com.slack.api.methods.request.chat.ChatGetPermalinkRequest;
import com.slack.api.methods.request.conversations.ConversationsHistoryRequest;
import com.slack.api.methods.response.chat.ChatGetPermalinkResponse;
import com.slack.api.methods.response.conversations.ConversationsHistoryResponse;
import com.slack.api.model.ResponseMetadata;
import java.io.IOException;
import java.util.ArrayList;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SlackServiceManagerTest {

  @InjectMocks
  private SlackServiceManager slackServiceManager;
  @Mock
  private JiraConfig vocJiraConfig;


  @Test
  public void readAllMessages() throws SlackApiException, IOException {
    MockedStatic<Slack> slackMockedStatic;
    slackMockedStatic = Mockito.mockStatic(Slack.class);
    Slack slack = Mockito.mock(Slack.class);
    MethodsClient methodsClient = Mockito.mock(MethodsClient.class);
    slackMockedStatic.when(
            () -> Slack.getInstance())
        .thenReturn(slack);
    Mockito.when(slack.methods(Mockito.any())).thenReturn(methodsClient);
    ConversationsHistoryResponse conversationsHistoryResponse = Mockito.mock(
        ConversationsHistoryResponse.class);
    Mockito.when(conversationsHistoryResponse.isOk()).thenReturn(true);
    Mockito.when(conversationsHistoryResponse.getMessages()).thenReturn(new ArrayList<>());
    try {
      Mockito.when(methodsClient.conversationsHistory((ConversationsHistoryRequest) Mockito.any()))
          .thenReturn(conversationsHistoryResponse);
    } catch (IOException e) {
      throw new RuntimeException(e);
    } catch (SlackApiException e) {
      throw new RuntimeException(e);
    }
    slackServiceManager.readAllMessages(SlackRequestModel.builder().limit(100).build());
    Mockito.verify(conversationsHistoryResponse, Mockito.times(2)).isHasMore();
    Mockito.verify(methodsClient, Mockito.times(1))
        .conversationsHistory((ConversationsHistoryRequest) Mockito.any());
    slackMockedStatic.close();
  }

  @Test
  public void readAllMessagesIoException() {
    MockedStatic<Slack> slackMockedStatic;
    slackMockedStatic = Mockito.mockStatic(Slack.class);
    Slack slack = Mockito.mock(Slack.class);
    MethodsClient methodsClient = Mockito.mock(MethodsClient.class);
    slackMockedStatic.when(
            () -> Slack.getInstance())
        .thenReturn(slack);
    Mockito.when(slack.methods(Mockito.any())).thenReturn(methodsClient);
    ConversationsHistoryResponse conversationsHistoryResponse = Mockito.mock(
        ConversationsHistoryResponse.class);
    try {
      Mockito.when(methodsClient.conversationsHistory((ConversationsHistoryRequest) Mockito.any()))
          .thenThrow(new IOException());
    } catch (IOException e) {
    } catch (SlackApiException e) {
    }
    try {
      slackServiceManager.readAllMessages(SlackRequestModel.builder().limit(100).build());
      Assert.fail();
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertTrue(
          workflowGeneralException.getWorkflowError() == WorkflowError.SLACK_API_CALL_FAILURE);
    }

    Mockito.verify(conversationsHistoryResponse, Mockito.times(0)).isHasMore();
    slackMockedStatic.close();
  }

  @Test
  public void readAllMessagesWithHasMoreTrue() throws SlackApiException, IOException {
    MockedStatic<Slack> slackMockedStatic;
    slackMockedStatic = Mockito.mockStatic(Slack.class);
    Slack slack = Mockito.mock(Slack.class);
    MethodsClient methodsClient = Mockito.mock(MethodsClient.class);
    slackMockedStatic.when(
            () -> Slack.getInstance())
        .thenReturn(slack);
    Mockito.when(slack.methods(Mockito.any())).thenReturn(methodsClient);
    ConversationsHistoryResponse conversationsHistoryResponse = Mockito.mock(
        ConversationsHistoryResponse.class);
    Mockito.when(conversationsHistoryResponse.isOk()).thenReturn(true);
    Mockito.when(conversationsHistoryResponse.isHasMore()).thenReturn(true).thenReturn(false);
    Mockito.when(conversationsHistoryResponse.getMessages()).thenReturn(new ArrayList<>());
    ResponseMetadata responseMetadata = Mockito.mock(ResponseMetadata.class);
    Mockito.when(responseMetadata.getNextCursor()).thenReturn("tets");
    Mockito.when(conversationsHistoryResponse.getResponseMetadata()).thenReturn(responseMetadata);
    try {
      Mockito.when(methodsClient.conversationsHistory((ConversationsHistoryRequest) Mockito.any()))
          .thenReturn(conversationsHistoryResponse);
    } catch (IOException e) {
      throw new RuntimeException(e);
    } catch (SlackApiException e) {
      throw new RuntimeException(e);
    }
    slackServiceManager.readAllMessages(SlackRequestModel.builder().limit(100).build());
    Mockito.verify(conversationsHistoryResponse, Mockito.times(2)).isHasMore();
    Mockito.verify(methodsClient, Mockito.times(1))
        .conversationsHistory((ConversationsHistoryRequest) Mockito.any());
    slackMockedStatic.close();
  }

  @Test
  public void getChatLink() throws SlackApiException, IOException {
    MockedStatic<Slack> slackMockedStatic;
    slackMockedStatic = Mockito.mockStatic(Slack.class);
    Slack slack = Mockito.mock(Slack.class);
    MethodsClient methodsClient = Mockito.mock(MethodsClient.class);
    slackMockedStatic.when(
            () -> Slack.getInstance())
        .thenReturn(slack);
    Mockito.when(slack.methods(Mockito.any())).thenReturn(methodsClient);
    ChatGetPermalinkResponse chatGetPermalinkResponse = Mockito.mock(
        ChatGetPermalinkResponse.class);
    Mockito.when(chatGetPermalinkResponse.getPermalink()).thenReturn("Test.com");
    try {
      Mockito.when(methodsClient.chatGetPermalink((ChatGetPermalinkRequest) Mockito.any()))
          .thenReturn(chatGetPermalinkResponse);
    } catch (IOException e) {
      throw new RuntimeException(e);
    } catch (SlackApiException e) {
      throw new RuntimeException(e);
    }
    String result = slackServiceManager.getMessageChatLink("test", "test");
    Assert.assertEquals("Test.com", result);
    Mockito.verify(chatGetPermalinkResponse, Mockito.times(1)).getPermalink();
    Mockito.verify(methodsClient, Mockito.times(1))
        .chatGetPermalink((ChatGetPermalinkRequest) Mockito.any());
    slackMockedStatic.close();
  }

  @Test
  public void getChatLinkThrowsException() throws SlackApiException, IOException {
    MockedStatic<Slack> slackMockedStatic;
    slackMockedStatic = Mockito.mockStatic(Slack.class);
    Slack slack = Mockito.mock(Slack.class);
    MethodsClient methodsClient = Mockito.mock(MethodsClient.class);
    slackMockedStatic.when(
            () -> Slack.getInstance())
        .thenReturn(slack);
    Mockito.when(slack.methods(Mockito.any())).thenReturn(methodsClient);
    ChatGetPermalinkResponse chatGetPermalinkResponse = Mockito.mock(
        ChatGetPermalinkResponse.class);
    try {
      Mockito.when(methodsClient.chatGetPermalink((ChatGetPermalinkRequest) Mockito.any()))
          .thenThrow(new IOException());
    } catch (IOException e) {
      throw new RuntimeException(e);
    } catch (SlackApiException e) {
      throw new RuntimeException(e);
    }
    String result = slackServiceManager.getMessageChatLink("test", "test");
    Assert.assertNull(result);
    Mockito.verify(chatGetPermalinkResponse, Mockito.times(0)).getPermalink();
    Mockito.verify(methodsClient, Mockito.times(1))
        .chatGetPermalink((ChatGetPermalinkRequest) Mockito.any());
    slackMockedStatic.close();
  }
}
