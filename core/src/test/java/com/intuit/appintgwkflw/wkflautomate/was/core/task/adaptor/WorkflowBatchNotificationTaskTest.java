package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.OINPAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowBatchNotificationTaskTest {

  @InjectMocks
  private WorkflowBatchNotificationTask workflowNotificationTask;

  @Mock
  private OINPAdaptor oinpAdaptor;

  @Mock
  private WorkflowTaskConfig workflowTaskConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(workflowTaskConfig.getTaskConfig())
        .thenReturn(Collections
            .singletonMap(TaskType.BATCH_NOTIFICATION_TASK, new WorkflowTaskConfigDetails()));
  }

  @Test
  public void typeTest() {
    Assert.assertEquals(TaskType.BATCH_NOTIFICATION_TASK, workflowNotificationTask.type());
  }

  @Test
  public void typeReferenceTest() {
    Assert.assertEquals(BatchNotificationTask.class,
        workflowNotificationTask.typeReference().getType());
  }

  @Test
  public void testCreate() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    notificationTask.setId("abc");
    WorkflowTaskResponse response = workflowNotificationTask.create(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertEquals("abc", response.getTxnId());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCreateFailed() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    Mockito.doThrow(new WorkflowGeneralException("failed")).when(oinpAdaptor)
        .sendBatchEvent(Mockito.any(), Mockito.any());
    workflowNotificationTask.create(notificationTask);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUpdate() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    workflowNotificationTask.update(notificationTask);
  }

  @Test
  public void testComplete() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.complete(notificationTask);
    Assert.assertEquals(ActivityConstants.NO_ACTION, response.getStatus());
  }

  @Test
  public void testFailed() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.failed(notificationTask);
    Assert.assertEquals(ActivityConstants.NO_ACTION, response.getStatus());
  }

  @Test
  public void testGet() {
    BatchNotificationTask notificationTask = new BatchNotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.get(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, response.getStatus());
  }

  @Test
  public void testStringCasting(){
    String s = "[{\"notificationName\":\"customWorkflow\",\"serviceName\":\"Workflow\",\"notificationDataType\":\"customWorkflow\",\"idempotencyKey\":\"abcdef\",\"notificationMetaData\":{\"authId\":\"-1\"},\"notificationData\":{\"To\":\"<EMAIL>\",\"Message\":\"Test mail 2\",\"Subject\":\"Review Invoice 001\",\"FromName\":\"Test Mail\",\"ReplyToEmail\":\"<EMAIL>\"}},{\"notificationName\":\"customWorkflow\",\"serviceName\":\"Workflow\",\"notificationDataType\":\"customWorkflow\",\"idempotencyKey\":\"abcdef\",\"notificationMetaData\":{\"authId\":\"-1\"},\"notificationData\":{\"To\":\"<EMAIL>\",\"Message\":\"Test mail 2\",\"Subject\":\"Review Invoice 001\",\"FromName\":\"Test Mail\",\"ReplyToEmail\":\"<EMAIL>\"}}]";

    Map<String, String> map = new HashMap<>();
    map.put("notificationTaskList", s);
    BatchNotificationTask batchNotificationTask = ObjectConverter.convertObject(map, BatchNotificationTask.class);

    Assert.assertNotNull(batchNotificationTask);
    Assert.assertNotNull(batchNotificationTask.getNotificationTaskList());
    Assert.assertEquals(2, batchNotificationTask.getNotificationTaskList().size());

  }
}
