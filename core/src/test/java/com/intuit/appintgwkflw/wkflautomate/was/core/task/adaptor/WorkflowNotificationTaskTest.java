package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.OINPAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.Collections;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowNotificationTaskTest {

  @InjectMocks
  private WorkflowNotificationTask workflowNotificationTask;

  @Mock
  private OINPAdaptor oinpAdaptor;

  @Mock
  private WorkflowTaskConfig workflowTaskConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(workflowTaskConfig.getTaskConfig())
        .thenReturn(
            Collections.singletonMap(TaskType.NOTIFICATION_TASK, new WorkflowTaskConfigDetails()));
  }

  @Test
  public void typeTest() {
    Assert.assertEquals(TaskType.NOTIFICATION_TASK, workflowNotificationTask.type());
  }

  @Test
  public void typeReferenceTest() {
    Assert.assertEquals(NotificationTask.class, workflowNotificationTask.typeReference().getType());
  }

  @Test
  public void testCreate() {
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setIdempotencyKey("abc");
    notificationTask.setId("xyz");
    WorkflowTaskResponse response = workflowNotificationTask.create(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertEquals("abc", response.getTxnId());

  }

  @Test
  public void testCreate_idempotentKeyCheck() {
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setId("xyz");
    WorkflowTaskResponse response = workflowNotificationTask.create(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertEquals("xyz", response.getTxnId());

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testCreateFailed() {
    NotificationTask notificationTask = new NotificationTask();
    Mockito.doThrow(new WorkflowGeneralException("failed")).when(oinpAdaptor)
        .sendEvent(Mockito.any(), Mockito.any());
    workflowNotificationTask.create(notificationTask);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUpdate() {
    NotificationTask notificationTask = new NotificationTask();
    workflowNotificationTask.update(notificationTask);
  }

  @Test
  public void testComplete() {
    NotificationTask notificationTask = new NotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.complete(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_COMPLETE, response.getStatus());
  }

  @Test
  public void testFailed() {
    NotificationTask notificationTask = new NotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.failed(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, response.getStatus());
  }

  @Test
  public void testGet() {
    NotificationTask notificationTask = new NotificationTask();
    WorkflowTaskResponse response = workflowNotificationTask.get(notificationTask);
    Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, response.getStatus());
  }
}
