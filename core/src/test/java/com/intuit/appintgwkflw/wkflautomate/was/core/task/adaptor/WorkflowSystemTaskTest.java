package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.SystemTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowSystemTaskTest {

  @InjectMocks private WorkflowSystemTask workflowSystemTask;

  @Mock private EventPublisherUtil eventPublisherUtil;
  @Mock private WASContextHandler wasContextHandler;
  @Mock private EventPublisherCapability eventPublisherCapability;
  @Mock private ProcessDetailsRepository processDetailsRepository;

  private SystemTask systemTask;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);

    Mockito.when(eventPublisherUtil.getOfferingId()).thenReturn("ttlive");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid-123");
    Optional<ProcessDetails> processDetails;
    TemplateDetails templateDetails = TemplateDetails.builder()
        .templateName("templateName")
        .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .build();

    processDetails = Optional.of(
        ProcessDetails.builder()
            .definitionDetails(definitionDetails)
            .processId("pid")
            .ownerId(123L)
            .recordId("rId")
            .build());

    systemTask = new SystemTask();
    systemTask.setId("id");
    systemTask.setWorkerId("workerId");
    systemTask.setTaskAttributes(TaskAttributes.builder().modelAttributes(new HashMap<>())
    		.runtimeAttributes(new HashMap<>()).build());
    systemTask.setProcessInstanceId("pid");
    systemTask.setTaskAttributes(TaskAttributes.builder().modelAttributes(new HashMap<>(
        Map.of(WorkflowConstants.HANDLER_DETAILS, "{       \"taskHandler\": \"was\",       \"handlerScope\": \"publishEvent\"  , \"handlerId\": \"intuit/test1\"    }")))
        .variables(new HashMap<>()).build());

    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(systemTask.getProcessInstanceId())).thenReturn(processDetails);


  }

  @Test
  public void testTaskType(){
    Assert.assertEquals(TaskType.SYSTEM_TASK, workflowSystemTask.type());
  }

  @Test
  public void testTypeReference(){
    Assert.assertEquals(SystemTask.class, workflowSystemTask.typeReference().getType());
  }

  @Test
  public void testUpdateCommand(){
	SystemTask systemTask = SystemTask.builder().status("Blocked").build();
    Assert.assertEquals("Blocked",
    		workflowSystemTask.update(systemTask).getStatus());
  }

  @Test
  public void testFailedCommand(){
    Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, 
    		workflowSystemTask.failed(new SystemTask()).getStatus());
  }

  @Test
  public void testCompleteCommand(){
    Assert.assertEquals(ActivityConstants.TASK_STATUS_COMPLETE, 
    		workflowSystemTask.complete(new SystemTask()).getStatus());
  }

  @Test
  public void testGetCommand(){
    Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, 
    		workflowSystemTask.get(new SystemTask()).getStatus());
  }

  @Test
  public void testCreateCommand(){
    WorkflowTaskResponse workflowTaskResponse = workflowSystemTask.create(systemTask);
    Assert.assertEquals(systemTask.getId(), workflowTaskResponse.getTxnId());
  }

  @Test(expected = WorkflowEventException.class)
  public void testCreateCommand_eventPublishFailed(){
    Mockito.when(eventPublisherCapability.publish(Mockito.any(), Mockito.any()))
        .thenThrow(WorkflowEventException.class);
    workflowSystemTask.create(systemTask);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testCreateCommand_ProcessNotFound(){
    Mockito.when(processDetailsRepository.findByIdWithoutDefinitionData(Mockito.any())).thenReturn(Optional.empty());
    workflowSystemTask.create(systemTask);
  }


  @Test(expected = WorkflowEventException.class)
  public void testCreateCommand_HandlerIdNotfound(){
    systemTask.setTaskAttributes(TaskAttributes.builder().modelAttributes(new HashMap<>(
        Map.of(WorkflowConstants.HANDLER_DETAILS, "{       \"taskHandler\": \"was\",       \"handlerScope\": \"publishEvent\"    }")))
        .variables(new HashMap<>()).build());
    systemTask.setTaskAttributes(TaskAttributes.builder().modelAttributes(new HashMap<>()).variables(new HashMap<>()).build());
    workflowSystemTask.create(systemTask);
  }
}
