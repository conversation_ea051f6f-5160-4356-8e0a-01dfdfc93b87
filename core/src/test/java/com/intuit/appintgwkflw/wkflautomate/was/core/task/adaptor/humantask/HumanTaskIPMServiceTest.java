package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.ProjectTaskAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.v4.GlobalId;
import com.intuit.v4.work.Project;
import com.intuit.v4.work.definitions.ExternalReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class HumanTaskIPMServiceTest {

    @Mock
    private ProjectTaskAdaptor projectTaskAdaptor;

    @Mock
    private WASContextHandler contextHandler;

    @Mock
    private WorkflowTaskConfig workflowTaskConfig;

    @InjectMocks
    private HumanTaskIPMService humanTaskIPMService;


    @Before
    public void setUp() {
        ReflectionTestUtils.setField(humanTaskIPMService, "workflowTaskConfig", workflowTaskConfig);
        ReflectionTestUtils.setField(humanTaskIPMService, "contextHandler", contextHandler);
    }

    @Test
    public void typeTest() {
        Assert.assertEquals(HumanTaskServiceType.PROJECT_SERVICE, humanTaskIPMService.getServiceType());
    }

    @Test
    public void createTest() {
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        HumanTask request = getHumanTaskMock();

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        Mockito.when(projectTaskAdaptor.executeProject(any(), any(), any(), any(), any())).thenReturn(project);

        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.create(request);
        Assert.assertNotNull(respone);
        Assert.assertNotNull(respone.getResponseMap());
        Assert.assertNotNull(respone.getResponseMap().get("txnId"));
        Assert.assertNotNull(respone.getTxnId());
    }

    @Test
    public void createTest_txnVariable() {
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        HumanTask request = getHumanTaskMock();
        request.getTaskAttributes().getModelAttributes().put(
                ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_VARIABLE, "trnsc1"
        );
        request.setTxnVariable("trnsc1");

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        Mockito.when(projectTaskAdaptor.executeProject(any(), any(), any(), any(), any())).thenReturn(project);

        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.create(request);
        Assert.assertNotNull(respone);
        Assert.assertNotNull(respone.getResponseMap());
        Assert.assertNotNull(respone.getResponseMap().get("trnsc1"));
        Assert.assertNotNull(respone.getTxnId());
    }

    @Test
    public void updateTest() {
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        HumanTask request = getHumanTaskMock();
        request.setTxnId("1");
        request.getTaskAttributes().setVariables(null);

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        Mockito.when(projectTaskAdaptor.executeProject(any(), any(), any(), any(), any())).thenReturn(project);

        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.update(request);
        Assert.assertNotNull(respone);
        Assert.assertNotNull(respone.getTxnId());
    }

    @Test
    public void completeTest() {
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        HumanTask request = getHumanTaskMock();
        request.setTxnId("1");
        request.getTaskAttributes().setVariables(null);

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        Mockito.when(projectTaskAdaptor.executeProject(any(), any(), any(), any(), any())).thenReturn(project);

        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.complete(request);
        Assert.assertNotNull(respone);
        Assert.assertNotNull(respone.getTxnId());
    }

    @Test
    public void failed() {
        Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        HumanTask request = getHumanTaskMock();
        request.setTxnId("1");
        request.getTaskAttributes().setVariables(null);

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        Mockito.when(projectTaskAdaptor.executeProject(any(), any(), any(), any(), any())).thenReturn(project);

        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.failed(request);
        Assert.assertNotNull(respone);
        Assert.assertNotNull(respone.getTxnId());
    }

    @Test
    public void get() {
        HumanTask request = getHumanTaskMock();
        mockAndGetTaskConfigForHumanProjectTask();

        WorkflowTaskResponse respone = humanTaskIPMService.get(request);
        Assert.assertNotNull(respone);
        Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, respone.getStatus());
    }

    private HumanTask getHumanTaskMock() {
        return HumanTask.builder()
                .id(UUID.randomUUID().toString())
                .type(TaskType.HUMAN_TASK)
                .processInstanceId("pId")
                .activityId("actId")
                .activityName("actName")
                .customerId("cId")
                .taskName("TestTask-01")
                .status("created")
                .dueDate("2021-12-31")
                .taskType("SAMPLE_TASK_TYPE")
                .assigneeId("assignId")
                .recordId("1")
                .project(false)
                .estimate(1)
                .priority(1)
                .taskAttributes(taskAttributes())
                .build();
    }


    private TaskAttributes taskAttributes() {
        Map<String, Object> runtimeDefAttributes = new HashMap<>();
        runtimeDefAttributes.put("assigneeId", "${taskAssignee}");
        runtimeDefAttributes.put("taskName", "Task - ${docNumber}");

        Map<String, String> modelDefAttributes = new HashMap<>();
        modelDefAttributes.put("estimate", "3");
        modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
        modelDefAttributes.put("visibility", "true");
        modelDefAttributes.put("entityType", "invoice");

        Map<String, Object> variables = new HashMap<>();
        variables.put("taskAssignee", "assignId");
        variables.put("docNumber", "1010");

        return TaskAttributes.builder().modelAttributes(modelDefAttributes)
                .runtimeAttributes(runtimeDefAttributes).variables(variables).build();
    }

    private void mockAndGetTaskConfigForHumanProjectTask() {
        Map<TaskType, WorkflowTaskConfigDetails> taskConfig = new HashMap<>();

        WorkflowTaskConfigDetails workflowTaskConfigDetails = new WorkflowTaskConfigDetails();
        workflowTaskConfigDetails.setEndpoint("url");

        Set<String> nonRetryErrorCodes = new HashSet<>();
        nonRetryErrorCodes.add("W111");
        workflowTaskConfigDetails.setNonRetryErrorCodes(nonRetryErrorCodes);

        Map<String, WorkflowTaskConfigDetails> downstreamServices = new HashMap<>();
        WorkflowTaskConfigDetails taskServiceWorkflowTaskConfigDetails = new WorkflowTaskConfigDetails();
        taskServiceWorkflowTaskConfigDetails.setEndpoint("url");
        taskServiceWorkflowTaskConfigDetails.setNonRetryErrorCodes(nonRetryErrorCodes);
        downstreamServices.put(HumanTaskServiceType.PROJECT_SERVICE.toString(), taskServiceWorkflowTaskConfigDetails);
        workflowTaskConfigDetails.setDownstreamServices(downstreamServices);

        taskConfig.put(TaskType.HUMAN_TASK, workflowTaskConfigDetails);

        Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(taskConfig);
    }

    @Test
    public void test_additionalTaskAttribute_runtimeAttributes() {

        Map<TaskType, WorkflowTaskConfigDetails> taskConfig = new HashMap<>();
        WorkflowTaskConfigDetails workflowTaskConfigDetails = new WorkflowTaskConfigDetails();
        workflowTaskConfigDetails.setEndpoint("url");
        Set<String> nonRetryErrorCodes = new HashSet<>();
        nonRetryErrorCodes.add("W111");
        workflowTaskConfigDetails.setNonRetryErrorCodes(nonRetryErrorCodes);

        taskConfig.put(TaskType.HUMAN_TASK, workflowTaskConfigDetails);
        Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(taskConfig);

        Map<String, Object> runtimeAttributes = new HashMap<>();
        runtimeAttributes.put("policyNo", "P1");
        runtimeAttributes.put("journalNo", "J1");

        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .taskAttributes(taskAttributes()).build();
        request.getTaskAttributes().setVariables(runtimeAttributes);

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        project.setStatus(ActivityConstants.TASK_STATUS_CREATED);

        List<ExternalReference> externalReferences = new ArrayList<>();
        ExternalReference wasReference = new ExternalReference();
        wasReference.externalKey("wasReference");
        wasReference.externalBlob(
                "{\"processInstanceId\":\"Pid\", \"externalTaskId\":\"extId1\", \"recordId\":\"record1\"}");
        wasReference.externalReferenceId("121");
        externalReferences.add(wasReference);

        ExternalReference additionalTaskAttributeReference = new ExternalReference();
        additionalTaskAttributeReference.externalKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES);
        additionalTaskAttributeReference.externalBlob("{\"isVisibility\":true}");
        additionalTaskAttributeReference.externalReferenceId("122");
        externalReferences.add(additionalTaskAttributeReference);

        project.setExternalReferences(externalReferences);

        Mockito.when(projectTaskAdaptor.readProjectByIdempotencyId(any(), any(), any(), any())).thenReturn(project);

        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

        Map<String, ExternalReference> respone = humanTaskIPMService
                .getAdditionalTaskReference(request, Operation.UPDATE);

        Assert.assertTrue(respone.containsKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES));
        Assert.assertEquals(additionalTaskAttributeReference,
                respone.get(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES));
    }


    @Test
    public void test_additionalTaskAttribute_createCommand() {
        Map<String, Object> runtimeAttributes = new HashMap<>();
        runtimeAttributes.put("policyNo", "P1");
        runtimeAttributes.put("journalNo", "J1");

        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .taskAttributes(taskAttributes()).build();
        request.getTaskAttributes().setVariables(runtimeAttributes);

        Map<String, ExternalReference> respone = humanTaskIPMService
                .getAdditionalTaskReference(request, Operation.CREATE);
        Assert.assertEquals(0, respone.size());
        Mockito.verify(projectTaskAdaptor, Mockito.never()).readProjectById(Mockito.anyString(),
                any(WorkflowTaskConfigDetails.class), Mockito.anyString());
    }


    @Test
    public void test_prepareCustomHeader() {
        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .app("TTLIVE").domain("engagement-svc").usecase("usecase1").build();

        Map<String, String> customHeader = humanTaskIPMService.prepareCustomHeader(request);
        Assert.assertEquals("TTLIVE", customHeader.get("target_app"));
        Assert.assertEquals("engagement-svc", customHeader.get("target_domain"));
        Assert.assertEquals("usecase1", customHeader.get("target_usecase"));
    }

    @Test
    public void test_prepareCustomHeader_noValue() {
        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .build();

        Map<String, String> customHeader = humanTaskIPMService.prepareCustomHeader(request);
        Assert.assertFalse(customHeader.containsKey("target_app"));
        Assert.assertFalse(customHeader.containsKey("target_domain"));
        Assert.assertFalse(customHeader.containsKey("target_usecase"));
    }

    @Test
    public void test_getOwnerId() {
        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .ownerId("13563577446165049")
                .build();
        Mockito.when(contextHandler.get(any(WASContextEnums.class))).thenReturn("111");

        String taskOwnerRealm = humanTaskIPMService.getOwnerId(request);
        Assert.assertEquals(taskOwnerRealm, "13563577446165049");
    }

    @Test
    public void test_getOwnerId_noTaskCreator() {
        HumanTask request = HumanTask.builder().id("id").txnId("tId")
                .processInstanceId("pId").type(TaskType.HUMAN_TASK)
                .build();
        Mockito.when(contextHandler.get(any(WASContextEnums.class))).thenReturn("111");

        String taskOwnerRealm = humanTaskIPMService.getOwnerId(request);
        Assert.assertEquals(taskOwnerRealm, "111");
    }

    @Test
    public void get_withIdempotenceId() {
        HumanTask humanTask = new HumanTask();
        humanTask.setId("id1");

        Map<TaskType, WorkflowTaskConfigDetails> taskConfig = new HashMap<>();
        WorkflowTaskConfigDetails workflowTaskConfigDetails = new WorkflowTaskConfigDetails();
        workflowTaskConfigDetails.setEndpoint("url");
        Set<String> nonRetryErrorCodes = new HashSet<>();
        nonRetryErrorCodes.add("W111");
        workflowTaskConfigDetails.setNonRetryErrorCodes(nonRetryErrorCodes);

        taskConfig.put(TaskType.HUMAN_TASK, workflowTaskConfigDetails);
        Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(taskConfig);

        Project project = new Project();
        project.setId(GlobalId.create("targetRealmId1", "id"));
        project.setStatus(ActivityConstants.TASK_STATUS_CREATED);

        Mockito.when(projectTaskAdaptor.readProjectByIdempotencyId(any(), any(), any(), any())).thenReturn(project);
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");
        WorkflowTaskResponse respone = humanTaskIPMService.get(humanTask);
        Assert.assertNotNull(respone);
        Mockito.verify(projectTaskAdaptor, Mockito.times(1)).readProjectByIdempotencyId(any(), any(), any(), any());
        Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, respone.getStatus());
        Assert.assertNotNull(respone.getResponse());

    }

    @Test
    public void get_withIdempotenceId_noProject() {
        HumanTask humanTask = new HumanTask();
        humanTask.setId("id1");

        Map<TaskType, WorkflowTaskConfigDetails> taskConfig = new HashMap<>();
        WorkflowTaskConfigDetails workflowTaskConfigDetails = new WorkflowTaskConfigDetails();
        workflowTaskConfigDetails.setEndpoint("url");
        Set<String> nonRetryErrorCodes = new HashSet<>();
        nonRetryErrorCodes.add("W111");
        workflowTaskConfigDetails.setNonRetryErrorCodes(nonRetryErrorCodes);
        taskConfig.put(TaskType.HUMAN_TASK, workflowTaskConfigDetails);
        Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(taskConfig);

        Mockito.when(projectTaskAdaptor.readProjectByIdempotencyId(any(), any(), any(), any())).thenReturn(null);
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");
        WorkflowTaskResponse respone = humanTaskIPMService.get(humanTask);
        Assert.assertNotNull(respone);
        Mockito.verify(projectTaskAdaptor, Mockito.times(1))
                .readProjectByIdempotencyId(any(), any(), any(), any());
        Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, respone.getStatus());
        Assert.assertNull(respone.getResponse());

    }

}
