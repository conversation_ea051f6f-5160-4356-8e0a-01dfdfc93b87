package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.ORIGINATING_ASSET_ALIAS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_COMPLETE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_IN_PROGRESS;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.ITMTaskAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.Silent.class)
public class HumanTaskITMServiceTest {

  @Mock private ITMTaskAdaptor itmTaskAdaptor;

  @Mock private WASContextHandler contextHandler;

  @InjectMocks private HumanTaskITMService humanTaskITMService;

  @Mock private WorkflowTaskConfig workflowTaskConfig;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(
            humanTaskITMService, "workflowTaskConfig", workflowTaskConfig);
    ReflectionTestUtils.setField(humanTaskITMService, "contextHandler", contextHandler);
  }

  @Test
  public void typeTest() {
    Assert.assertEquals(
        HumanTaskServiceType.TASK_SERVICE,
        humanTaskITMService.getServiceType());
  }

  @Test
  public void createITMTaskTest() {
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

    HumanTask request = getHumanTaskMock();

    TaskManagementCreateTaskMutation.Task createdITMTask =
        new TaskManagementCreateTaskMutation.Task(
            StringUtils.EMPTY,
            UUID.randomUUID().toString(),
            request.getTaskName(),
            request.getDescription(),
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            request.getTaskType(),
            request.getAssigneeId(),
            new ArrayList<>());

    Mockito.when(itmTaskAdaptor.createTask(any(), any(), any())).thenReturn(createdITMTask);

    WorkflowTaskResponse respone = humanTaskITMService.create(request);
    Assert.assertNotNull(respone);
    Assert.assertNotNull(respone.getResponseMap());
    Assert.assertNotNull(respone.getResponseMap().get("txnId"));
    Assert.assertEquals(respone.getResponseMap().get("txnId"), createdITMTask.id().toString());
    Assert.assertNotNull(respone.getTxnId());
    Assert.assertEquals(respone.getTxnId(), createdITMTask.id().toString());
    Assert.assertEquals(respone.getStatus(), ITM_STATUS_IN_PROGRESS);
  }

  @Test
  public void updateITMTaskTest() {
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

    HumanTask request = getHumanTaskMock();
    request.setTxnId(UUID.randomUUID().toString());

    TaskManagementUpdateTaskMutation.Task updatedITMTask =
        new TaskManagementUpdateTaskMutation.Task(
            StringUtils.EMPTY,
            request.getTxnId(),
            request.getTaskName(),
            request.getDescription(),
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            request.getAssigneeId(),
            new ArrayList<>());

    Mockito.when(itmTaskAdaptor.updateTask(any(), any(), any())).thenReturn(updatedITMTask);

    WorkflowTaskResponse respone = humanTaskITMService.update(request);
    Assert.assertNotNull(respone);
    Assert.assertNotNull(respone.getTxnId());
    Assert.assertEquals(respone.getTxnId(), updatedITMTask.id().toString());
    Assert.assertEquals(respone.getStatus(), ITM_STATUS_IN_PROGRESS);
  }

  @Test
  public void completeITMTaskTest() {
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

    HumanTask request = getHumanTaskMock();
    request.setTxnId(UUID.randomUUID().toString());

    TaskManagementUpdateTaskMutation.Task completedITMTask =
            new TaskManagementUpdateTaskMutation.Task(
                    StringUtils.EMPTY,
                    request.getTxnId(),
                    request.getTaskName(),
                    request.getDescription(),
                    ITM_STATUS_COMPLETE,
                    new DateTime(),
                    1,
                    request.getAssigneeId(),
                    new ArrayList<>());

    Mockito.when(itmTaskAdaptor.updateTask(any(), any(), any())).thenReturn(completedITMTask);

    WorkflowTaskResponse respone = humanTaskITMService.complete(request);
    Assert.assertNotNull(respone);
    Assert.assertNotNull(respone.getTxnId());
    Assert.assertEquals(respone.getTxnId(), completedITMTask.id().toString());
    Assert.assertEquals(respone.getStatus(), ITM_STATUS_COMPLETE);
  }

  @Test
  public void getITMTaskTest() {
    Mockito.when(contextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("offering1");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("owner1");

    HumanTask request = getHumanTaskMock();
    request.setTxnId(UUID.randomUUID().toString());

    TaskManagementTaskQuery.TaskManagementTask fetchedITMTask =
        new TaskManagementTaskQuery.TaskManagementTask(
            StringUtils.EMPTY,
            request.getTxnId(),
            request.getTaskName(),
            request.getDescription(),
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            request.getTaskType(),
            request.getAssigneeId(),
            new ArrayList<>());

    Mockito.when(itmTaskAdaptor.getTask(any(), any(), any())).thenReturn(fetchedITMTask);

    WorkflowTaskResponse respone = humanTaskITMService.get(request);
    Assert.assertNotNull(respone);
    Assert.assertNotNull(respone.getTxnId());
    Assert.assertEquals(respone.getTxnId(), fetchedITMTask.id().toString());
    Assert.assertEquals(respone.getStatus(), ITM_STATUS_IN_PROGRESS);
  }

  private HumanTask getHumanTaskMock() {
    return HumanTask.builder()
        .id(UUID.randomUUID().toString())
        .type(TaskType.HUMAN_TASK)
        .processInstanceId("pId")
        .activityId("actId")
        .activityName("actName")
        .customerId("cId")
        .taskName("TestTask-01")
        .status("created")
        .dueDate("2021-12-31")
        .taskType("SAMPLE_TASK_TYPE")
        .assigneeId("assignId")
        .domain("QBO")
        .usecase("TASK_MANAGER")
        .recordId("1")
        .project(false)
        .estimate(1)
        .priority(1)
        .taskAttributes(taskAttributes())
        .build();
  }

  private TaskAttributes taskAttributes() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("assigneeId", "${taskAssignee}");
    runtimeDefAttributes.put("taskName", "Task - ${docNumber}");
    runtimeDefAttributes.put("entityType", "invoice");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("entityType", "invoice");
    modelDefAttributes.put(ORIGINATING_ASSET_ALIAS, "ORIGINATING_ASSET_ALIAS");

    Map<String, Object> variables = new HashMap<>();
    variables.put("taskAssignee", "assignId");
    variables.put("docNumber", "1010");

    return TaskAttributes.builder()
        .modelAttributes(modelDefAttributes)
        .runtimeAttributes(runtimeDefAttributes)
        .variables(variables)
        .build();
  }
}
