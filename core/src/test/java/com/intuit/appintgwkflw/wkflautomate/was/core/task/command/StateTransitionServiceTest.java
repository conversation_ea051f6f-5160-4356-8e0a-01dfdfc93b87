package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.StateTransitionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class StateTransitionServiceTest {

  @InjectMocks
  private StateTransitionService stateTransitionService;

  @Mock
  private CamundaRunTimeServiceRest camundaRest;

  @Mock
  private CamundaHistoryServiceRest camundaHistoryRest;

  @Mock
  private WorkflowTaskConfig workflowTaskConfig;

  @Mock
  private EventPublisherCapability eventPublishCapability;

  @Mock
  private WASContextHandler wasContextHandler;

  @Captor
  private ArgumentCaptor<WorkflowStateTransitionEvents> transitionEvent;

  @SuppressWarnings({"rawtypes", "unchecked"})
  @Test
  public void getExternalTaskVariables() {

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName("key1");
    processVariableDetailsResponse1.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key2");
    processVariableDetailsResponse2.setValue("newValue2");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    ProcessVariableDetailsResponse processVariableDetailsResponse3 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse3.setExecutionId("process1");
    processVariableDetailsResponse3.setName("key2");
    processVariableDetailsResponse3.setValue("value2");
    processVariableDetailsResponse.add(processVariableDetailsResponse3);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Map<String, Object> taskVariables = stateTransitionService
        .getExternalTaskVariable("execution1", "process1");

    Assert.assertNotNull(taskVariables);
    Assert.assertEquals(2, taskVariables.size());
    Assert.assertTrue(taskVariables.containsKey("key1"));
    Assert.assertTrue(taskVariables.containsKey("key2"));
    Assert.assertEquals("newValue2", taskVariables.get("key2"));
    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));
  }

  @SuppressWarnings({"rawtypes", "unchecked"})
  @Test
  public void getExternalTaskVariables_noExecutionVariable() {

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();

    ProcessVariableDetailsResponse processVariableDetailsResponse3 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse3.setExecutionId("process1");
    processVariableDetailsResponse3.setName("key2");
    processVariableDetailsResponse3.setValue("value2");
    processVariableDetailsResponse.add(processVariableDetailsResponse3);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Map<String, Object> taskVariables = stateTransitionService
        .getExternalTaskVariable("execution1", "process1");

    Assert.assertNotNull(taskVariables);
    Assert.assertEquals(1, taskVariables.size());
    Assert.assertTrue(taskVariables.containsKey("key2"));
    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));
  }

  @SuppressWarnings({"rawtypes", "unchecked"})
  @Test
  public void getExternalTaskVariables_noProcessVariable() {

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName("key1");
    processVariableDetailsResponse1.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key2");
    processVariableDetailsResponse2.setValue("newValue2");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Map<String, Object> taskVariables = stateTransitionService
        .getExternalTaskVariable("execution1", "process1");

    Assert.assertNotNull(taskVariables);
    Assert.assertEquals(2, taskVariables.size());
    Assert.assertTrue(taskVariables.containsKey("key2"));
    Assert.assertTrue(taskVariables.containsKey("key1"));
    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));
  }


  @SuppressWarnings({"rawtypes", "unchecked"})
  @Test
  public void getExternalTaskVariables_withExternalTaskId_success() {

    ExternalTaskDetail extTaskDetail = new ExternalTaskDetail();
    extTaskDetail.setExecutionId("execution1");
    extTaskDetail.setProcessInstanceId("process1");

    Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
        .thenReturn(extTaskDetail);

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName("key1");
    processVariableDetailsResponse1.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key2");
    processVariableDetailsResponse2.setValue("newValue2");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    ProcessVariableDetailsResponse processVariableDetailsResponse3 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse3.setExecutionId("process1");
    processVariableDetailsResponse3.setName("key2");
    processVariableDetailsResponse3.setValue("value2");
    processVariableDetailsResponse.add(processVariableDetailsResponse3);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Map<String, Object> taskVariables = stateTransitionService
        .getExternalTaskVariable("ext1");

    Assert.assertNotNull(taskVariables);
    Assert.assertEquals(2, taskVariables.size());
    Assert.assertTrue(taskVariables.containsKey("key1"));
    Assert.assertTrue(taskVariables.containsKey("key2"));
    Assert.assertEquals("newValue2", taskVariables.get("key2"));
    Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));
  }


  @SuppressWarnings({"unchecked", "rawtypes"})
  @Test
  public void test_publishStateTransitionUpdateEvent_success() {
    String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
    ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
        ExternalTaskDetail.class);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE);
    processVariableDetailsResponse1.setValue(ObjectConverter.toJson(statusVariableMap));
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key1");
    processVariableDetailsResponse2.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName")
                .recordType(RecordType.ENGAGEMENT)
                .templateDetails(TemplateDetails.builder().templateName("defName").build())
                .version(1).build())
            .build())
        .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID,
        "37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
    headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

    Mockito.when(eventPublishCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(WorkflowStateTransitionEvents.class)))
        .thenReturn(null);

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);
    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    stateTransitionService.publishStateTransitionEvent(headers,
        externalTaskDtl, activityProgressDetails, ActivityConstants.TASK_EVENT_TYPE_UPDATE, Instant.now().toEpochMilli());

    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));

    Mockito.verify(eventPublishCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(WorkflowStateTransitionEvents.class));
  }


  @SuppressWarnings({"rawtypes", "unchecked"})
  @Test
  public void test_publishUpdateEvent() {

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID,
        "37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
    headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName")
                .recordType(RecordType.ENGAGEMENT)
                .templateDetails(TemplateDetails.builder().templateName("defName").build())
                .version(1).build())
            .build())
        .build();

    ExternalTaskDetail extTaskDetail = new ExternalTaskDetail();
    extTaskDetail.setExecutionId("execution1");
    extTaskDetail.setProcessInstanceId("process1");
    extTaskDetail.setId("extId");

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE);
    processVariableDetailsResponse1.setValue(ObjectConverter.toJson(statusVariableMap));
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key1");
    processVariableDetailsResponse2.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    WASHttpResponse response =
        WASHttpResponse.builder().response(processVariableDetailsResponse).build();

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);

    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    Mockito.when(eventPublishCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(WorkflowStateTransitionEvents.class)))
        .thenReturn(null);

    stateTransitionService.publishUpdateEvent(headers,
        extTaskDetail, activityProgressDetails, Instant.now().toEpochMilli());

    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));

    Mockito.verify(eventPublishCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(WorkflowStateTransitionEvents.class));

  }

  @Test
  public void test_publishCompleteEvent() {

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID,
        "37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
    headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("completed")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName").recordType(RecordType.ENGAGEMENT)
                .templateDetails(TemplateDetails.builder().templateName("defName").build())
                .version(1).build())
            .build())
        .build();

    List<ExternalTaskLog> externalTaskLogs = new ArrayList<>();
    ExternalTaskLog externalTaskLog = new ExternalTaskLog();
    externalTaskLog.setExecutionId("execution1");
    externalTaskLog.setExternalTaskId("ext1");
    externalTaskLog.setActivityId("actId1");
    externalTaskLog.setProcessDefinitionId("processDef1");
    externalTaskLog.setProcessInstanceId("processInstance1");
    externalTaskLogs.add(externalTaskLog);

    WASHttpResponse<List<ExternalTaskLog>> wasResponse = WASHttpResponse
        .<List<ExternalTaskLog>>builder()
        .response(externalTaskLogs).build();

    Mockito.when(camundaHistoryRest.getExternalTaskLogs(Mockito.any())).thenReturn(wasResponse);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    List<ProcessVariableDetailsResponse> processVariableDetailsResponse = new ArrayList<>();
    ProcessVariableDetailsResponse processVariableDetailsResponse1 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse1.setExecutionId("execution1");
    processVariableDetailsResponse1.setName(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE);
    processVariableDetailsResponse1.setValue(ObjectConverter.toJson(statusVariableMap));
    processVariableDetailsResponse.add(processVariableDetailsResponse1);

    ProcessVariableDetailsResponse processVariableDetailsResponse2 = new ProcessVariableDetailsResponse();
    processVariableDetailsResponse2.setExecutionId("execution1");
    processVariableDetailsResponse2.setName("key1");
    processVariableDetailsResponse2.setValue("value1");
    processVariableDetailsResponse.add(processVariableDetailsResponse2);

    WASHttpResponse<List<ProcessVariableDetailsResponse>> response =
        WASHttpResponse.<List<ProcessVariableDetailsResponse>>builder()
            .response(processVariableDetailsResponse).build();

    StateTransitionConfig stateTransitionConfig = new StateTransitionConfig();
    stateTransitionConfig.setMaxResult(100);

    Mockito.when(workflowTaskConfig.getStateTransitionConfig()).thenReturn(stateTransitionConfig);

    Mockito.when(camundaHistoryRest
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class)))
        .thenReturn(response);

    stateTransitionService.publishCompleteEvent(activityProgressDetails, headers,
        "ext1", Instant.now().toEpochMilli());

    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getExternalTaskLogs(Mockito.any());

    Mockito.verify(camundaHistoryRest, Mockito.times(1))
        .getProcessVariableDetails(Mockito.any(ProcessVariableDetailsRequest.class));

    Mockito.verify(eventPublishCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(WorkflowStateTransitionEvents.class));
  }


  @Test
  public void test_publishEvent_success() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");

    Mockito.doNothing().when(wasContextHandler)
        .addKey(Mockito.eq(WASContextEnums.EVENT_TYPE), Mockito.anyString());

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .id("ext1")
        .taskType(TaskType.HUMAN_TASK).status("created")
        .taskAttributes(
            TaskAttributes.builder().modelAttributes(activityAttributes.getModelAttributes())
                .runtimeAttributes(activityAttributes.getRuntimeAttributes())
                .variables(runtimeAttributes)
                .build())
        .publishWorkflowStateTransitionEvent(true)
        .command(TaskCommand.CREATE).build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();
    Mockito.when(eventPublishCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(WorkflowStateTransitionEvents.class))).thenReturn(null);

    stateTransitionService
        .publishEvent(taskRequest, processDetails, ActivityConstants.TASK_STATUS_CREATED);

    Mockito.verify(wasContextHandler, Mockito.times(1))
        .addKey(Mockito.eq(WASContextEnums.EVENT_TYPE), Mockito.anyString());

    Mockito.verify(eventPublishCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(WorkflowStateTransitionEvents.class));

  }
  
  @Test
  public void test_isStateTransitionEnabled_true() {
	  
	  Map<String, String> modelAttributes = new HashMap<>();
	    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
	        "{\"actionName\":\"wasCustomTaskHandler\"}");
	    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
	        "[\"completed\"]");

	    ActivityDetail activityDetail = ActivityDetail.builder()
	        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
	        .activityName("actName")
	        .attributes(ObjectConverter
	            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
	        .build();

	    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
	        .id("ext1")
	        .activityDefinitionDetail(activityDetail)
	        .status("completed")
	        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
	        .processDetails(ProcessDetails.builder()
	            .ownerId(1l).recordId("rec1")
	            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
	                .definitionName("defName").recordType(RecordType.ENGAGEMENT)
	                .version(1).build())
	            .build())
	        .build();

	  
	 Assert.assertFalse(stateTransitionService
      .isStateTransitionEnabled(activityProgressDetails, ActivityConstants.TASK_EVENT_TYPE_UPDATE));
	  
	 Assert.assertTrue(stateTransitionService
      .isStateTransitionEnabled(activityProgressDetails, ActivityConstants.TASK_STATUS_COMPLETE));

  }

}