package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.SystemTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskCommandTest {

  @InjectMocks
  @Spy
  private WorkflowTaskCreateCommand command;
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private EventPublisherCapability eventPublisherCapability;
  @Mock
  private PublishEventHandler publishEventHandler;
  @Mock
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;

  @Before
  public void init() {
    ReflectionTestUtils.setField(command, "contextHandler", contextHandler);
    ReflectionTestUtils.setField(command, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(command, "publishEventHandler", publishEventHandler);

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, new WorkflowHumanTask(null));
    WorkflowTasks.addWorkflowTask(TaskType.SYSTEM_TASK, new WorkflowTask<SystemTask>() {

      @Override
      public TypeReference<SystemTask> typeReference() {
        return new TypeReference<SystemTask>() {
        };
      }

      @Override
      public TaskType type() {
        return TaskType.SYSTEM_TASK;
      }

      @Override
      public WorkflowTaskResponse create(SystemTask request) {
        return null;
      }

      @Override
      public WorkflowTaskResponse update(SystemTask request) {
        return null;
      }

      @Override
      public WorkflowTaskResponse complete(SystemTask request) {
        return null;
      }

      @Override
      public WorkflowTaskResponse failed(SystemTask request) {
        return null;
      }

      @Override
      public WorkflowTaskResponse get(SystemTask request) {
        return null;
      }

    });
  }

  @Test
  public void test_prepareHumanTask_createFlow() {

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put(WorkflowConstants.INTUIT_REALMID, "realmId");
    runtimeAttributes.put(WorkflowConstants.INTUIT_USERID, "userId");
    runtimeAttributes.put(WorkflowConstants.HANDLER_DETAILS, "handlerDetails");
    runtimeAttributes.put(WorkflowConstants.HANDLER_SCOPE, null);
    runtimeAttributes.put("journalNo", "J#051");
    runtimeAttributes.put("assigneeId", "expert1");
    runtimeAttributes.put("customerId", "user1");

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");
    modelDefAttributes.put("usecase", "test_use_case");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .recordId("rec1")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(runtimeAttributes).build())
        .build();

    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertNotNull(task.getTaskAttributes());
    Assert.assertNotNull(task.getTaskAttributes().getVariables().get("journalNo"));
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
    Assert.assertEquals("rec1", ((HumanTask) task).getRecordId());
    Assert.assertEquals("QB_LIVE", ((HumanTask) task).getDomain());
    Assert.assertEquals("test_use_case", ((HumanTask) task).getUsecase());
    Assert.assertTrue(task instanceof HumanTask);
  }


  @Test
  public void test_prepareHumanTask_createFlow_noModelAttribute() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put(WorkflowConstants.INTUIT_REALMID, "realmId");
    runtimeAttributes.put(WorkflowConstants.INTUIT_USERID, "userId");
    runtimeAttributes.put(WorkflowConstants.HANDLER_DETAILS, "handlerDetails");
    runtimeAttributes.put(WorkflowConstants.HANDLER_SCOPE, null);
    runtimeAttributes.put("journalNo", "J#051");
    runtimeAttributes.put("assigneeId", "expert1");
    runtimeAttributes.put("customerId", "user1");
    runtimeAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    runtimeAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    runtimeAttributes.put("visibility", "true");
    runtimeAttributes.put("estimate", "3");

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");
    runtimeDefAttributes.put("visibility", "true");
    runtimeDefAttributes.put("estimate", "3");
    runtimeDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .recordId("rec1")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(runtimeAttributes).build())
        .build();

    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertNotNull(task.getTaskAttributes().getVariables());
    Assert.assertNotNull(task.getTaskAttributes().getVariables().get("journalNo"));
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
    Assert.assertEquals("rec1", task.getRecordId());
    Assert.assertTrue(task instanceof HumanTask);
  }

  @Test
  public void test_prepareHumanTask_sync_updateOrCompleteFlow() {

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("subStatus", "Document approved");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("domain", "QB_LIVE");
    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(attributes).build())
        .build();

    Mockito.when(command.command()).thenReturn(TaskCommand.UPDATE);
    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertTrue(task instanceof HumanTask);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertNotNull(task.getTaskAttributes().getVariables());
    Assert.assertEquals(task.getTaskAttributes().getVariables().get("subStatus"), attributes.get("subStatus"));
    //Check existing runtimeAttribute doesn't get override to null.
    Assert.assertFalse(task.getTaskAttributes().getVariables().containsKey("journalNo"));
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
  }
  
  
  @Test
  public void test_approval_createNotification() {

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("subStatus", "Document approved");
    attributes.put("estimate", 5);
    attributes.put("activityId", "activity1");

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("domain", "QB_LIVE");
    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(attributes).build())
        .build();

    Mockito.when(command.command()).thenReturn(TaskCommand.UPDATE);
    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertTrue(task instanceof HumanTask);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertNotNull(task.getTaskAttributes().getVariables());
    Assert.assertEquals(task.getTaskAttributes().getVariables().get("subStatus"), attributes.get("subStatus"));
    //Check existing runtimeAttribute doesn't get override to null.
    Assert.assertFalse(task.getTaskAttributes().getVariables().containsKey("journalNo"));
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
    Assert.assertEquals("actName", task.getActivityName());
  }


  @Test
  public void test_prepareHumanTask_sync_updateOrCompleteFlow_noRuntimeAttributes() {

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("subStatus", "Document approved");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(new HashMap<>()).variables(attributes).build())
        .build();

    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertTrue(task instanceof HumanTask);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertNotNull(task.getTaskAttributes().getVariables());
    Assert.assertEquals(task.getTaskAttributes().getVariables().get("subStatus"), attributes.get("subStatus"));
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
  }


  @Test
  public void test_prepareHumanTask_async_completeFlow() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put("txnMode", "ASYNC");
    modelAttributes.put("domain", "QB_LIVE");

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put(WorkflowConstants.INTUIT_REALMID, "realmId");
    runtimeAttributes.put(WorkflowConstants.INTUIT_USERID, "userId");
    runtimeAttributes.put(WorkflowConstants.HANDLER_DETAILS, "handlerDetails");
    runtimeAttributes.put(WorkflowConstants.HANDLER_SCOPE, null);

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .recordId("rec1")
        .build();

    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
    Assert.assertEquals("rec1",  task.getRecordId());
    Assert.assertTrue(task instanceof HumanTask);
  }


  @Test
  public void test_prepareSystemTask() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .taskType(TaskType.SYSTEM_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .workerId("test").build();

    Task task = command.prepareTaskRequest(taskRequest);
    Assert.assertNotNull(task);
    Assert.assertNotNull(task.getId());
    Assert.assertNotNull(task.getStatus());
    Assert.assertEquals("actName", task.getTaskName());
    Assert.assertEquals("proc1", task.getProcessInstanceId());
    Assert.assertEquals("actId", task.getActivityId());
    Assert.assertEquals("actName", task.getActivityName());
    Assert.assertTrue(task instanceof SystemTask);
  }


  @Test
  public void test_publish_ExternalTask() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .id("ext1").taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .workerId("test").build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    Mockito.when(publishEventHandler.buildEventPayload(Mockito.any(WorkerActionRequest.class),
        Mockito.any(ProcessDetails.class)))
        .thenReturn(ExternalTaskAssigned.builder().build());
    Mockito.when(contextHandler.get(Mockito.any(WASContextEnums.class))).thenReturn("abc");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(ExternalTaskAssigned.class))).thenReturn(null);
    taskRequest.setTxnId("txn1");
    command.publish(taskRequest, processDetails, PublishEventType.EXTERNAL_TASK);
    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(ExternalTaskAssigned.class));
  }


  @Test
  public void test_publish_WorkflowTaskTransition_createCommand() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .id("ext1").taskType(TaskType.HUMAN_TASK)
        .status("create")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .command(TaskCommand.CREATE).build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    Mockito.when(contextHandler.get(Mockito.any(WASContextEnums.class))).thenReturn("abc");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(WorkflowStateTransitionEvents.class))).thenReturn(null);
    taskRequest.setTxnId("txn1");
    command
        .publish(taskRequest, processDetails, PublishEventType.WORKFLOW_TRANSITION_EVENTS);
    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(WorkflowStateTransitionEvents.class));
  }

  @Test
  public void test_publish_WorkflowTaskTransition_completeCommand() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowActivityAttributes activityAttributes =
        WorkflowActivityAttributes.builder()
            .modelAttributes(modelAttributes)
            .runtimeAttributes(runtimeAttributes)
            .build();

    WorkflowTaskRequest taskRequest =
        WorkflowTaskRequest.builder()
            .processInstanceId("pid1")
            .id("ext1")
            .taskType(TaskType.HUMAN_TASK)
            .status("complete")
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(activityAttributes.getModelAttributes())
                    .runtimeAttributes(activityAttributes.getRuntimeAttributes())
                    .variables(runtimeAttributes)
                    .build())
            .command(TaskCommand.COMPLETE)
            .build();

    TemplateDetails templateDtls =
        TemplateDetails.builder().offeringId("offer1").templateName("template1").build();
    DefinitionDetails definitionDtls =
        DefinitionDetails.builder()
            .templateDetails(templateDtls)
            .definitionId("def1")
            .version(1)
            .recordType(RecordType.INVOICE)
            .build();
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .ownerId(1l)
            .definitionDetails(definitionDtls)
            .build();

    Mockito.when(contextHandler.get(Mockito.any(WASContextEnums.class))).thenReturn("abc");
    Mockito.when(
            eventPublisherCapability.publish(
                Mockito.any(EventHeaderEntity.class),
                Mockito.any(WorkflowStateTransitionEvents.class)))
        .thenReturn(null);

    WorkflowTaskCommand completeCommand = new WorkflowTaskCompleteCommand(null, null);
    ReflectionTestUtils.setField(completeCommand, "contextHandler", contextHandler);
    ReflectionTestUtils.setField(
        completeCommand, "eventPublisherCapability", eventPublisherCapability);
    taskRequest.setTxnId("txn1");
    completeCommand.publish(
        taskRequest, processDetails, PublishEventType.WORKFLOW_TRANSITION_EVENTS);
    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(
            Mockito.any(EventHeaderEntity.class), Mockito.any(WorkflowStateTransitionEvents.class));
  }

  @SuppressWarnings("unchecked")
  @Test
  public void test_prepareTaskRequestMap_failedCommand() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK).activityType("serviceTask")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .build();

    Mockito.when(command.command()).thenReturn(TaskCommand.FAILED);
    Map<String, Object> taskRequestMap = (Map<String, Object>) ReflectionTestUtils
        .invokeMethod(command, "prepareTaskRequestMap", taskRequest);
    Assert
        .assertTrue(taskRequestMap.containsKey(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE));
  }


  @Test
  public void test_publish_ExternalTaskTest() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .id("ext1").taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .workerId("test").build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    Mockito.when(publishEventHandler.buildEventPayload(Mockito.any(WorkerActionRequest.class),
        Mockito.any(ProcessDetails.class)))
        .thenReturn(ExternalTaskAssigned.builder().build());
    Mockito.when(contextHandler.get(Mockito.any(WASContextEnums.class))).thenReturn("abc");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(EventHeaderEntity.class),
        Mockito.any(ExternalTaskAssigned.class))).thenReturn(null);
    taskRequest.setTxnId("txn1");
    command.publish(taskRequest, processDetails, PublishEventType.EXTERNAL_TASK_TEST);
    Mockito.verify(eventPublisherCapability, Mockito.times(1))
        .publish(Mockito.any(EventHeaderEntity.class),
            Mockito.any(ExternalTaskAssigned.class));
  }


  @Test(expected = WorkflowGeneralException.class)
  public void test_publish_SERVICE_TASK_TEST() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .id("ext1").taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .workerId("test").build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    Mockito.when(contextHandler.get(Mockito.any(WASContextEnums.class))).thenReturn("abc");
    taskRequest.setTxnId("txn1");
    command.publish(taskRequest, processDetails, PublishEventType.SERVICE_TASK_TEST);
  }
  
  @Test(expected=WorkflowGeneralException.class)
  public void test_prepareHumanTask_ValidationFailure() {
    Map<String, Object> attributes = new HashMap<>();
    attributes.put("subStatus", "Document approved");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("priority", "1");
    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(attributes).build())
        .build();

    Mockito.when(command.command()).thenReturn(TaskCommand.UPDATE);
    command.prepareTaskRequest(taskRequest);
  }
  
  @Test(expected=WorkflowGeneralException.class)
  public void test_prepareHumanTask_ValidationFailure2() {
    Map<String, Object> attributes = new HashMap<>();
    attributes.put("subStatus", "Document approved");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = Collections.emptyMap();
    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityName("actName").activityId("actId")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes).variables(attributes).build())
        .build();

    Mockito.when(command.command()).thenReturn(TaskCommand.UPDATE);
    command.prepareTaskRequest(taskRequest);
  }

}
