package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;

/**
 * 
 * <AUTHOR>
 *
 */
public class WorkflowTaskCommandsTest {

	@Mock
	private WorkflowTaskCreateCommand workflowTaskCreateCommand;

	@Before
	public void init() {
		MockitoAnnotations.initMocks(this);
		Mockito.when(workflowTaskCreateCommand.command()).thenReturn(TaskCommand.CREATE);
		WorkflowTaskCommands.addCommand(TaskCommand.CREATE, workflowTaskCreateCommand);
	}

	@Test
	public void getTestNUll() {
		WorkflowTaskCommand command = WorkflowTaskCommands.getCommand(null);
		Assert.assertNull(command);
	}

	@Test
	public void getTestAction() {
		WorkflowTaskCommand command = WorkflowTaskCommands
				.getCommand(TaskCommand.CREATE);
		Assert.assertNotNull(command);
	}

	@Test
	public void containsFalse() {
		Assert.assertFalse(WorkflowTaskHandlers.contains(null));
	}

	@Test
	public void containsTrue() {
		Assert.assertTrue(WorkflowTaskCommands.contains(TaskCommand.CREATE));
	}
}
