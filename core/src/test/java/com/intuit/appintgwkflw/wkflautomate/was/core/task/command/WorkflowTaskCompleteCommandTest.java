package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;

/**
 * Create Command is responsible to create entry of transaction and activity in DB to maintain
 * state.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskCompleteCommandTest {

  @InjectMocks
  private WorkflowTaskCompleteCommand completeCommand;

  @Mock
  private ActivityProgressDetailsRepository progressDetailRepo;

  @Mock
  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private PublishEventHandler publishEventHandler;

  @Mock
  private EventPublisherCapability eventPublisherCapability;
  
  @Mock
  private WorkflowHumanTask workflowHumanTask;
  @Mock
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;


  @Before
  public void init() {
    ReflectionTestUtils.setField(completeCommand, "contextHandler", contextHandler);
    ReflectionTestUtils
        .setField(completeCommand, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(completeCommand, "publishEventHandler", publishEventHandler);
  }


  @Test(expected = WorkflowGeneralException.class)
  public void execute_Failure_noAdaptorRegistered() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("assigneeId", "assignee1");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .workerId("worker1").build();

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, null);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    completeCommand.execute(taskRequest);

  }


  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_Failure_noActivityProgressRecordInDB() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .workerId("worker1").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    completeCommand.execute(taskRequest);

  }

  
  
  @Test
  public void execute_ActivityProgressRecordInDB_statusComplete() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .workerId("worker1").build();
    
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l)
    		.status(ActivityConstants.TASK_STATUS_COMPLETE).build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
            .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
            .id(taskRequest.getId()).status(ActivityConstants.TASK_STATUS_COMPLETE)
            .txnDetails(txnDetails)
            .name(taskRequest.getTaskAttributes().getModelAttributes()
                .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    completeCommand.execute(taskRequest);
    
    Mockito.verify(progressDetailRepo, Mockito.times(1))
    	.findById(Mockito.anyString());
    
    Mockito.verify(taskDBOperationManager, Mockito.never())
    .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
        any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

	Mockito.verify(eventPublisherCapability, Mockito.never())
    	.publish(any(), any());

	Mockito.verify(workflowHumanTask, Mockito.never()).complete(any());

  }

  /**
   * Success  (skipcallback = false) -- Make Downstream Call. -- Make Db Update -- Publish Event.
   */
  @Test
  public void execute_completeCall_success() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"completed\"]");
	modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(Collections.emptyMap()).build())
        .taskType(TaskType.HUMAN_TASK)
        .activityId("actId").publishExternalTaskEvent(true)
        .publishWorkflowStateTransitionEvent(true)
        .skipCallback(false).workerId("worker1")
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").endTime(Timestamp.from(Instant.now())).build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(any(), any())).thenReturn(null);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    
    WorkflowTaskResponse getResponse = new WorkflowTaskResponse();
    getResponse.setStatus(ActivityConstants.TASK_STATUS_CREATED);
    Mockito.when(workflowHumanTask.get(any(HumanTask.class)))
    	.thenReturn(getResponse);
    

    WorkflowTaskResponse completeResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status("complete").build();

    Mockito.when(workflowHumanTask.complete(any(HumanTask.class)))
        .thenReturn(completeResponse);

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    completeCommand.execute(taskRequest);

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class),
            any(WorkflowTaskResponse.class));

    Mockito.verify(contextHandler, Mockito.atLeastOnce())
        .get(Mockito.eq(WASContextEnums.INTUIT_TID));

    Mockito.verify(eventPublisherCapability, Mockito.times(2))
        .publish(any(), any());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).complete(any());
  }


  /**
   * Success, skipcallback=true -- Make Downstream Call. -- Make Db Update -- Publish Event.
   */
  @Test
  public void execute_completeCall_success_skipCallback() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
	modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(true).activityId("actId")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(Collections.emptyMap()).build())
        .workerId("worker1")
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").endTime(Timestamp.from(Instant.now())).build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class));

    completeCommand.execute(taskRequest);

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class));

    Mockito.verify(contextHandler, Mockito.never())
        .get(Mockito.eq(WASContextEnums.INTUIT_TID));

    Mockito.verify(eventPublisherCapability, Mockito.never())
        .publish(any(), any());

    Mockito.verify(workflowHumanTask, Mockito.never()).complete(any());
  }


  /*
   *   2. Downstream call fail.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_completeCall_downstreamFail() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");
    
    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(Collections.emptyMap()).build())
        .workerId("worker1")
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails).name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTaskResponse getResponse = new WorkflowTaskResponse();
    getResponse.setStatus(ActivityConstants.TASK_STATUS_CREATED);
    Mockito.when(workflowHumanTask.get(any(HumanTask.class)))
    	.thenReturn(getResponse);
    
    Mockito.when(workflowHumanTask.complete(any(HumanTask.class)))
        .thenThrow(new WorkflowGeneralException(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE));

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    completeCommand.execute(taskRequest);
  }


  /**
   * 4. Db Update Call fail.
   */
  @Test(expected = RuntimeException.class)
  public void execute_updateCall_DBUpdateFail() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .activityId("actId1")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(Collections.emptyMap()).build())
        .workerId("worker1")
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(taskDBOperationManager)
        .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTaskResponse completeResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status("complete").build();

    Mockito.when(workflowHumanTask.complete(any(HumanTask.class)))
        .thenReturn(completeResponse);
    
    
    WorkflowTaskResponse getResponse = new WorkflowTaskResponse();
    getResponse.setStatus(ActivityConstants.TASK_STATUS_CREATED);
    Mockito.when(workflowHumanTask.get(any(HumanTask.class)))
    	.thenReturn(getResponse);
   

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    completeCommand.execute(taskRequest);

  }

  /**
   * 5. Publish event fail.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_updateCall_EventPublishFail() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .activityId("actId").taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(Collections.emptyMap()).build())
        .publishExternalTaskEvent(true).workerId("worker1")
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").endTime(Timestamp.from(Instant.now())).build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markTxnDetailAndActivityProgressCompleteInDB(any(Task.class),
            any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");

    Mockito.when(publishEventHandler.buildEventPayload(any(), any()))
        .thenReturn(ExternalTaskAssigned.builder().build());

    Mockito.when(eventPublisherCapability.publish(any(), any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.KAFKA_PUBLISH_ERROR));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    
    WorkflowTaskResponse getResponse = new WorkflowTaskResponse();
    getResponse.setStatus(ActivityConstants.TASK_STATUS_CREATED);
    Mockito.when(workflowHumanTask.get(any(HumanTask.class)))
    	.thenReturn(getResponse);
    

    WorkflowTaskResponse completeResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status("complete").build();

    Mockito.when(workflowHumanTask.complete(any(HumanTask.class)))
        .thenReturn(completeResponse);

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    completeCommand.execute(taskRequest);

  }

  @Test
  public void execute_completeCall_success_downstream_get_complete() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"completed\"]");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes =
        WorkflowActivityAttributes.builder()
            .modelAttributes(modelDefAttributes)
            .runtimeAttributes(runtimeDefAttributes)
            .build();

    WorkflowTaskRequest taskRequest =
        WorkflowTaskRequest.builder()
            .id("ext1")
            .processInstanceId("proc1")
            .command(TaskCommand.CREATE)
            .status("created")
            .taskType(TaskType.HUMAN_TASK)
            .activityId("actId")
            .publishExternalTaskEvent(true)
            .publishWorkflowStateTransitionEvent(true)
            .skipCallback(false)
            .workerId("worker1")
            .taskAttributes(
                TaskAttributes.builder()
                    .modelAttributes(activityAttributes.getModelAttributes())
                    .runtimeAttributes(activityAttributes.getRuntimeAttributes())
                    .variables(runtimeAttributes)
                    .build())
            .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .version(1)
            .templateDetails(templateDtls)
            .recordType(RecordType.INVOICE)
            .build();
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .recordId("record1")
            .definitionDetails(definitionDetails)
            .ownerId(1l)
            .build();

    ActivityDetail activityDefinitionDetail =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();
    TransactionDetails txnDetails =
        TransactionDetails.builder()
            .id(1l)
            .status("created")
            .endTime(Timestamp.from(Instant.now()))
            .build();

    ActivityProgressDetails progressDetails =
        ActivityProgressDetails.builder()
            .attributes(
                ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
            .activityDefinitionDetail(activityDefinitionDetail)
            .id(taskRequest.getId())
            .processDetails(processDetails)
            .status("blocked")
            .txnDetails(txnDetails)
            .name(
                taskRequest
                    .getTaskAttributes()
                    .getModelAttributes()
                    .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME))
            .build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing()
        .when(taskDBOperationManager)
        .markTxnDetailAndActivityProgressCompleteInDB(
            any(Task.class), any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(any(), any())).thenReturn(null);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {});

    WorkflowTaskResponse getResponse = new WorkflowTaskResponse();
    getResponse.setStatus(ActivityConstants.TASK_STATUS_COMPLETE);
    Mockito.when(workflowHumanTask.get(any(HumanTask.class))).thenReturn(getResponse);

    Mockito.when(workflowHumanTask.getWASActivityStatus(Mockito.anyString()))
        .thenReturn(ActivityConstants.TASK_STATUS_COMPLETE);

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    completeCommand.execute(taskRequest);

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markTxnDetailAndActivityProgressCompleteInDB(
            any(Task.class), any(ActivityProgressDetails.class), any(WorkflowTaskResponse.class));

    Mockito.verify(contextHandler, Mockito.atLeastOnce())
        .get(Mockito.eq(WASContextEnums.INTUIT_TID));

    Mockito.verify(eventPublisherCapability, Mockito.times(2)).publish(any(), any());

    Mockito.verify(workflowHumanTask, Mockito.never()).complete(any());
    Mockito.verify(workflowHumanTask, Mockito.times(1)).get(any());
    Mockito.verify(workflowHumanTask, Mockito.times(1)).getWASActivityStatus(Mockito.anyString());
  }
}
