package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Failed Command is responsible to create entry of transaction and activity in DB to maintain
 * state.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskFailedCommandTest {

  @InjectMocks
  private WorkflowTaskFailedCommand failedCommand;

  @Mock
  private ProcessDetailsRepository processDetailRepo;

  @Mock
  private WorkflowHumanTask workflowHumanTask;

  @Mock
  private ActivityProgressDetailsRepository progressDetailRepo;

  @Mock
  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private PublishEventHandler publishEventHandler;

  @Mock
  private EventPublisherCapability eventPublisherCapability;

  @Mock
  private StateTransitionService stateTransitionService;


  @Before
  public void init() {
    ReflectionTestUtils.setField(failedCommand, "contextHandler", contextHandler);
    ReflectionTestUtils
        .setField(failedCommand, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(failedCommand, "publishEventHandler", publishEventHandler);
    ReflectionTestUtils.setField(failedCommand, "stateTransitionService", stateTransitionService);
  }

  @Test
  public void execute_NoActivityProgress_Success() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");
	    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.empty());

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();

    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    Mockito.when(processDetailRepo.findById(taskRequest.getProcessInstanceId()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(taskDBOperationManager
        .prepareActivityProgressAndSaveInFailedState(Mockito.any(), Mockito.any()))
        .thenReturn(ActivityProgressDetails.builder().build());

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(processDetailRepo, Mockito.times(1))
        .findById(taskRequest.getProcessInstanceId());

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .prepareActivityProgressAndSaveInFailedState(Mockito.any(), Mockito.any());

  }

  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_NoActivityProgress_ProcessNotFound() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.empty());

    Mockito.when(processDetailRepo.findById(taskRequest.getProcessInstanceId()))
        .thenReturn(Optional.empty());

    failedCommand.execute(taskRequest);
  }

  /**
   * skipCallback = false
   */
  @Test
  public void test_activityProgressPresent_UserFailedEvent() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(false)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1")
        .status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name("act1").build();

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    Mockito.when(workflowHumanTask.failed(Mockito.any()))
        .thenReturn(WorkflowTaskResponse.builder().txnId("txn1").build());

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.doNothing().when(taskDBOperationManager)
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).failed(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());
  }

  @Test
  public void test_activityProgressOpenState_DownstreamFailure() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");
	    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(true)
        .skipTxnDBUpdate(false)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails)
        .status("blocked")
        .name("act1").build();

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.never()).failed(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());
  }


  @Test
  public void test_activityProgressPresent_DownstreamFailure() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");  
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(true)
        .skipTxnDBUpdate(false)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1")
        .status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name("act1").build();

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.doNothing().when(taskDBOperationManager)
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.never()).failed(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());
  }


  @Test
  public void test_activityProgressPresent_WASFailure() {
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");
	
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(true)
        .skipTxnDBUpdate(true)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1")
        .status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name("act1").build();

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.never()).failed(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());
  }


  @Test
  public void test_activityProgressPresent_DownstreamFailure_NoTxnIdPresent() {
	  
	Map<String, String> modelDefAttributes = new HashMap<>();
	modelDefAttributes.put("domain", "QB_LIVE");  
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .activityId("act1").activityName("act1")
        .processInstanceId("proc1").command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .taskType(TaskType.HUMAN_TASK)
        .skipCallback(false)
        .skipTxnDBUpdate(true)
        .publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .name("act1").build();

    Mockito.when(progressDetailRepo.findById("ext1"))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    failedCommand.execute(taskRequest);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).findById("ext1");

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .markActivityProgressFailedInDB(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.never()).failed(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .markTxnDetailsFailedInDB(Mockito.any(), Mockito.any());
  }


  /**
   * No Handler registered.
   */
//  @Test(expected = WorkflowGeneralException.class)
  public void execute_Failure() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder().build())
        .taskType(TaskType.HUMAN_TASK).workerId("worker1").build();

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, null);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    failedCommand.execute(taskRequest);

  }


  //  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_Failure_noActivityProgressRecordInDB() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    failedCommand.execute(taskRequest);
  }


  /**
   * Success -- Make Downstream Call. -- Make Db Update -- PublishEvent.
   */
//  @Test
  public void execute_failedCommand_success() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeAttributes)
        		.variables(attributes).build())
        .publishExternalTaskEvent(true).taskType(TaskType.HUMAN_TASK)
        .publishWorkflowStateTransitionEvent(true)
        .workerId("worker1").build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(), Mockito.any())).thenReturn(null);

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    WorkflowTaskResponse failedResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status("failed").build();
    Mockito.when(workflowHumanTask.failed(Mockito.any(HumanTask.class)))
        .thenReturn(failedResponse);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    failedCommand.execute(taskRequest);

    Mockito.verify(contextHandler, Mockito.atLeastOnce())
        .get(Mockito.eq(WASContextEnums.INTUIT_TID));
    Mockito.verify(eventPublisherCapability, Mockito.times(2))
        .publish(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).failed(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));
  }


  /*
   *   2. Downstream call fail.
   */
//  @Test(expected = WorkflowGeneralException.class)
  public void execute_completeCall_downstreamFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .activityId("actId1").activityName("actName1")
        .taskAttributes(TaskAttributes.builder()
        		.runtimeAttributes(runtimeAttributes)
        		.variables(attributes)
        		.modelAttributes(modelAttributes).build())
        .workerId("worker1").build();

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    Mockito
        .when(workflowHumanTask.failed(Mockito.any(HumanTask.class)))
        .thenThrow(new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_TASK_DETAILS));

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    failedCommand.execute(taskRequest);

  }

  /**
   * 4. Db Update Call fail.
   */
//  @Test(expected = RuntimeException.class)
  public void execute_updateCall_DBUpdateFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.runtimeAttributes(runtimeAttributes)
        		.variables(attributes)
        		.modelAttributes(modelAttributes).build())
        .workerId("worker1").build();

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);
    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    Mockito
        .when(workflowHumanTask.failed(Mockito.any(HumanTask.class)))
        .thenReturn(new WorkflowTaskResponse());

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doThrow(new RuntimeException()).when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    failedCommand.execute(taskRequest);

  }

  /**
   * 5. Publish event fail.
   */
//  @Test(expected = WorkflowGeneralException.class)
  public void execute_updateCall_EventPublishFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder()
        		.runtimeAttributes(runtimeAttributes)
        		.variables(attributes)
        		.modelAttributes(modelAttributes).build())
        .publishExternalTaskEvent(true).workerId("worker1")
        .build();

    WorkflowHumanTask workflowHumanTask = Mockito.mock(WorkflowHumanTask.class);
    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });

    Mockito
        .when(workflowHumanTask.failed(Mockito.any(HumanTask.class)))
        .thenReturn(new WorkflowTaskResponse());

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INCORRECT_EVENT_PAYLOAD));

    failedCommand.execute(taskRequest);

  }

}
