package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.intuit.system.interfaces.BaseEntity;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Update Command is responsible to update status of transaction and activity in DB.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskUpdateCommandTest {

  @InjectMocks
  private WorkflowTaskUpdateCommand updateCommand;

  @Mock
  private WorkflowHumanTask workflowHumanTask;

  @Mock
  private ActivityProgressDetailsRepository progressDetailRepo;

  @Mock
  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private PublishEventHandler publishEventHandler;

  @Mock
  private EventPublisherCapability eventPublisherCapability;

  @Mock
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;

  @Before
  public void init() {
    ReflectionTestUtils.setField(updateCommand, "contextHandler", contextHandler);
    ReflectionTestUtils
        .setField(updateCommand, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(updateCommand, "publishEventHandler", publishEventHandler);
  }

  /**
   * No Handler registered.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_Failure() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(attributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, null);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    updateCommand.execute(taskRequest);

  }


  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_Failure_noActivityProgressRecordInDB() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).build())
        .workerId("worker1").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    updateCommand.execute(taskRequest);
  }


  /**
   * Success -- Make Downstream Call. -- Make Db Update -- Publish Event.
   */
  @Test
  public void execute_updateCall_success() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"update\"]");
	modelAttributes.put("domain", "QB_LIVE");

    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .publishExternalTaskEvent(true).taskType(TaskType.HUMAN_TASK)
        .publishWorkflowStateTransitionEvent(true)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes)
        		.variables(attributes).build())
        .workerId("worker1").build();

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    WorkflowTaskResponse updateResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status("blocked").build();
    Mockito.when(workflowHumanTask.update(Mockito.any(HumanTask.class))).thenReturn(updateResponse);

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(), Mockito.any())).thenReturn(null);

    updateCommand.execute(taskRequest);

    Mockito.verify(contextHandler, Mockito.atLeastOnce())
        .get(Mockito.eq(WASContextEnums.INTUIT_TID));
    Mockito.verify(eventPublisherCapability, Mockito.times(2))
        .publish(Mockito.any(), Mockito.any());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).update(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

  }


  /*
   *   2. Downstream call fail.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_updateCall_downstreamFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");
    modelAttributes.put("domain", "QB_LIVE");

    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(attributes).build())
        .workerId("worker1").build();

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito
        .when(workflowHumanTask.update(Mockito.any(HumanTask.class)))
        .thenThrow(new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_TASK_DETAILS));

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    updateCommand.execute(taskRequest);

  }


  /**
   * 4. Db Update Call fail.
   */
  @Test(expected = RuntimeException.class)
  public void execute_updateCall_DBUpdateFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");
    modelAttributes.put("domain", "QB_LIVE");
    
    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5);

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(attributes).build())
        .workerId("worker1").build();

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito
        .when(workflowHumanTask.update(Mockito.any(HumanTask.class)))
        .thenReturn(new WorkflowTaskResponse());

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doThrow(new RuntimeException()).when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    updateCommand.execute(taskRequest);

  }

  /**
   * 5. Publish event fail.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_updateCall_EventPublishFail() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");
    modelAttributes.put("domain", "QB_LIVE");
    
    Map<String, Object> runtimeAttributes = Collections.emptyMap();

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("xyz", "abc");
    attributes.put("estimate", 5); 

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskType(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(attributes).build())
        .publishExternalTaskEvent(true).workerId("worker1").build();

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito
        .when(workflowHumanTask.update(Mockito.any(HumanTask.class)))
        .thenReturn(new WorkflowTaskResponse());

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDtls)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();

    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(ObjectConverter.toJson(taskRequest.getTaskAttributes().getRuntimeAttributes()))
        .activityDefinitionDetail(activityDefinitionDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status("blocked")
        .txnDetails(txnDetails)
        .name(taskRequest.getTaskAttributes().getModelAttributes()
            .get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME)).build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(progressDetails));

    Mockito.doNothing().when(taskDBOperationManager)
        .updateStatusInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class));

    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.INTUIT_TID))).thenReturn("tid");
    Mockito.when(eventPublisherCapability.publish(Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INCORRECT_EVENT_PAYLOAD));

    updateCommand.execute(taskRequest);

  }

}
