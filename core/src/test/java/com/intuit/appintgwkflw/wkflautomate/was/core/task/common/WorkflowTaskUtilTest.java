package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 */
public class WorkflowTaskUtilTest {

  @Test
  public void test_containsExtenstionVariable() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put("abc", "abc");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(extensionProperties).build();
    Boolean flag = WorkflowTaskUtil.containsExtenstionVariable(actionRequest, "abc");
    Assert.assertTrue(flag);

    flag = WorkflowTaskUtil.containsExtenstionVariable(actionRequest, "def");
    Assert.assertFalse(flag);
  }


  @Test
  public void test_getExtenstionVariable() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put("abc", "abc");
    WorkerActionRequest actionRequest = WorkerActionRequest.builder()
        .extensionProperties(extensionProperties).build();
    String value = WorkflowTaskUtil.getExtenstionVariable(actionRequest, "abc");
    Assert.assertEquals("abc", value);

    value = WorkflowTaskUtil.getExtenstionVariable(actionRequest, "def");
    Assert.assertNull(value);
  }


  @Test
  public void test_getRequestVariable() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("abc", "abc");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .taskAttributes(taskAttributes)
        .build();

    String value = WorkflowTaskUtil.getRequestVariable(taskRequest, "abc");
    Assert.assertEquals("abc", value);

    value = WorkflowTaskUtil.getRequestVariable(taskRequest, "def");
    Assert.assertNull(value);
  }


  @Test
  public void test_containRequestVariable() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("abc", "abc");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .taskAttributes(taskAttributes)
        .build();
    Assert.assertTrue(WorkflowTaskUtil.containsRequestVariable(taskRequest, "abc"));
    Assert.assertFalse(WorkflowTaskUtil.containsRequestVariable(taskRequest, "def"));
  }


  @Test
  public void test_getTransactionVariable() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("abc", "abc");
    modelDefAttributes.put(ActivityConstants.EXTENSION_PROPERTY_TRANSACTION_VARIABLE, "trnstnId");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .taskAttributes(taskAttributes)
        .build();
    String txnIdVariable = WorkflowTaskUtil.getTransactionVariable(taskRequest);
    Assert.assertEquals("trnstnId", txnIdVariable);
  }

  @Test
  public void test_getTransactionVariable_default() {

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("abc", "abc");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .taskAttributes(taskAttributes)
        .build();

    String txnIdVariable = WorkflowTaskUtil.getTransactionVariable(taskRequest);
    Assert.assertEquals(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE, txnIdVariable);
  }

  /**
   * publishEvent from runtimeAttributes.
   */
  @Test
  public void test_getHandlerScope_runtimeAttribute() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("handlerDetails",
        "{       \"taskHandler\": \"was\",    \"handlerScope\": \"publishEvent\"     }");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(Collections.emptyMap()).runtimeAttributes(runtimeDefAttributes).build();

    Assert.assertEquals("publishEvent", WorkflowTaskUtil.getHandlerScope(taskAttributes));
  }

  /**
   * handlerScope from modelAttributes.
   */
  @Test
  public void test_getHandlerScope_modelAttribute() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("handlerDetails",
        "{       \"taskHandler\": \"was\",       \"handlerScope\": \"publishEvent\"     }");

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("engagementId",
        "${enagagementId}");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    Assert.assertEquals("publishEvent", WorkflowTaskUtil.getHandlerScope(taskAttributes));
  }


  /**
   * No runtime and model attribute present. Returns null.
   */
  @Test
  public void test_getHandlerScope_false() {

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(Collections.emptyMap()).runtimeAttributes(new HashMap<>()).build();

    Assert.assertNull(WorkflowTaskUtil.getHandlerScope(taskAttributes));
  }

  /**
   * model and runtime attribute present but no handlerDetails. Returns null.
   */
  @Test
  public void test_getHandlerScope_modelAttribute_false() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("events",
        "[\"start\",\"end\"]");
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("engagementId",
        "${enagagementId}");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    Assert.assertNull(WorkflowTaskUtil.getHandlerScope(taskAttributes));
  }

  @Test
  public void test_getTransitionEvent() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"created\"]");
    Set<String> enabledEvents = WorkflowTaskUtil.getTransitionEnabledEvents(modelDefAttributes);
    Assert.assertNotNull(enabledEvents);
    Assert.assertEquals(1, enabledEvents.size());
  }

  @Test
  public void test_getTransitionEvent_empty() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    Set<String> enabledEvents = WorkflowTaskUtil.getTransitionEnabledEvents(modelDefAttributes);
    Assert.assertEquals(0, enabledEvents.size());
  }
  
  @Test
  public void test_getTransitionEvent_null() {
    Set<String> enabledEvents = WorkflowTaskUtil.getTransitionEnabledEvents(null);
    Assert.assertEquals(0, enabledEvents.size());
  }

}
