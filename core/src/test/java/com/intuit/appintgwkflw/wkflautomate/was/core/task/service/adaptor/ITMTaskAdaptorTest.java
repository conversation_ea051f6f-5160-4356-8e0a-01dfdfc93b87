package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_IN_PROGRESS;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.ITMGraphqlClient;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ITMTaskAdaptorTest {

  @Mock private ITMGraphqlClient itmGraphqlClient;

  @InjectMocks ITMTaskAdaptor itmTaskAdaptor;

  @Test
  public void test_createITMTask() {

    TaskManagementCreateTaskMutation.Task createdITMTask =
        new TaskManagementCreateTaskMutation.Task(
            StringUtils.EMPTY,
            UUID.randomUUID().toString(),
            "Test Name",
            "Test Description",
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            "Test Task Type",
            "assigneeId",
            new ArrayList<>());

    Mockito.when(itmGraphqlClient.createTask(any(), any())).thenReturn(createdITMTask);

    Assert.assertEquals(
        createdITMTask, itmTaskAdaptor.createTask(null, new HashMap<>(), "ownerId"));
    Mockito.verify(itmGraphqlClient, Mockito.times(1)).createTask(any(), any());
  }

  @Test
  public void test_updateITMTask() {

    TaskManagementUpdateTaskMutation.Task updatedITMTask =
        new TaskManagementUpdateTaskMutation.Task(
            StringUtils.EMPTY,
            UUID.randomUUID().toString(),
            "Test Name",
            "Test Description",
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            "Test Task Type",
            new ArrayList<>());

    Mockito.when(itmGraphqlClient.updateTask(any(), any())).thenReturn(updatedITMTask);

    Assert.assertEquals(
        updatedITMTask, itmTaskAdaptor.updateTask(null, new HashMap<>(), "ownerId"));
    Mockito.verify(itmGraphqlClient, Mockito.times(1)).updateTask(any(), any());
  }

  @Test
  public void test_deleteITMTask() {
    Mockito.when(itmGraphqlClient.deleteTask(any(), any())).thenReturn(true);

    itmTaskAdaptor.deleteTask(null, new HashMap<>(), "ownerId");

    Mockito.verify(itmGraphqlClient, Mockito.times(1)).deleteTask(any(), any());
  }

  @Test
  public void test_getITMTask() {

    TaskManagementTaskQuery.TaskManagementTask fetchedITMTask =
        new TaskManagementTaskQuery.TaskManagementTask(
            StringUtils.EMPTY,
            UUID.randomUUID().toString(),
            "Test Name",
            "Test Description",
            ITM_STATUS_IN_PROGRESS,
            new DateTime(),
            1,
            "Test Task Type",
            "assigneeId",
            new ArrayList<>());

    Mockito.when(itmGraphqlClient.getTask(any(), any())).thenReturn(fetchedITMTask);

    Assert.assertEquals(fetchedITMTask, itmTaskAdaptor.getTask(null, new HashMap<>(), "ownerId"));
    Mockito.verify(itmGraphqlClient, Mockito.times(1)).getTask(any(), any());
  }
}
