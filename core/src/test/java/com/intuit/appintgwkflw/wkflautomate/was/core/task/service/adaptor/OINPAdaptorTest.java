package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.OINPHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.EventMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@SuppressWarnings("java:S5778")
public class OINPAdaptorTest {

  @InjectMocks
  private OINPAdaptor oinpAdaptor;

  @Mock
  private OINPHttpClient oinpHttpClient;

  @Mock
  private OfflineTicketClient offlineTicketClient;

  @Captor
  private ArgumentCaptor<WASHttpRequest<OINPEventRequest, Object>> wasHttpRequestArgumentCaptor;

  private OINPEventRequest oinpEventRequest;

  private WorkflowTaskConfigDetails configDetails;

  @Before
  public void initBefore(){
    MockitoAnnotations.initMocks(this);
    EventMetaData eventMetaData = EventMetaData.builder()
        .authId(9130355927583956L).build();

    Map<String, Object> map = new HashMap<>();
    map.put("email", "<EMAIL>");

    oinpEventRequest = OINPEventRequest.builder()
        .eventData(map)
        .eventMetaData(eventMetaData)
        .name("testName")
        .sourceObjectType("test")
        .sourceObjectId("test-obj-109")
        .sourceServiceName("Workflow")
        .build();
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()).thenReturn("auth header");
    configDetails = Mockito.mock(WorkflowTaskConfigDetails.class);
    Mockito.when(configDetails.getEndpoint()).thenReturn("url");
  }

  @Test
  public void testOINP_Positive(){
    WASHttpResponse<Object> response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(oinpHttpClient.httpResponse(wasHttpRequestArgumentCaptor.capture())).thenReturn(response);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    oinpAdaptor.sendEvent(oinpEventRequest, configDetails);
    Assert.assertNotNull(wasHttpRequestArgumentCaptor.getValue().getUrl());
    Assert.assertNotNull(wasHttpRequestArgumentCaptor.getValue().getRequestEntity().getBody().getEventMetaData().getCreatedDate());
  }

  @Test
  public void testOINP_Negative(){
    WASHttpResponse<Object> response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(oinpHttpClient.httpResponse(wasHttpRequestArgumentCaptor.capture())).thenReturn(response);
    Mockito.when(response.isSuccess2xx()).thenReturn(false);
    Exception exception = Assertions.assertThrows(WorkflowNonRetriableException.class, () ->
        oinpAdaptor.sendEvent(oinpEventRequest, configDetails));
    Assert.assertEquals(WorkflowError.SENDING_NOTIFICATION_FAILED.getErrorMessage(), exception.getMessage());
  }

  @Test
  public void testOINPBatch_Positive(){
    WASHttpResponse<Object> response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(oinpHttpClient.httpResponse(Mockito.any())).thenReturn(response);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);
    try {
      oinpAdaptor.sendBatchEvent(Collections.singletonList(oinpEventRequest), configDetails);
    }
    catch (Exception e){
      Assert.fail("This should not fail");
    }

  }

  @Test
  public void testOINPBatch_Negative(){
    WASHttpResponse<Object> response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(oinpHttpClient.httpResponse(wasHttpRequestArgumentCaptor.capture())).thenReturn(response);
    Mockito.when(response.isSuccess2xx()).thenReturn(false);
    Exception exception = Assertions.assertThrows(WorkflowNonRetriableException.class,
        () -> oinpAdaptor.sendBatchEvent(Collections.singletonList(oinpEventRequest), configDetails));
    Assert.assertEquals(WorkflowError.SENDING_NOTIFICATION_FAILED.getErrorMessage(), exception.getMessage());
  }

  @Test
  public void testOINP_Negative_Retryable(){
    Mockito.when(oinpHttpClient.httpResponse(wasHttpRequestArgumentCaptor.capture())).
        thenThrow( WorkflowRetriableException.class);
    Assertions.assertThrows(WorkflowRetriableException.class,
        () -> oinpAdaptor.sendBatchEvent(Collections.singletonList(oinpEventRequest), configDetails));
  }
}
