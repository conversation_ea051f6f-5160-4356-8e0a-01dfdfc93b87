package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.VariabilityGraphqlClient;
import com.intuit.variability.entity.graphql.GetAttributeQuery;

@RunWith(MockitoJUnitRunner.class)
public class VariabilityConnectorTest {

  @Mock private VariabilityGraphqlClient variabilityGraphqlClient;
  
  @Mock private WASContextHandler wasContextHandler;

  @InjectMocks VariabilityConnector variabilityAdaptor;

  @Test
  public void test_returns_map_of_decisions_and_values() {
      // Arrange
      VariabilityGraphqlClient variabilityGraphqlClient = mock(VariabilityGraphqlClient.class);
      WASContextHandler wasContextHandler = mock(WASContextHandler.class);
      VariabilityConnector variabilityConnector = new VariabilityConnector(variabilityGraphqlClient, wasContextHandler);
  
      List<String> decisions = Arrays.asList("decision1", "decision2");
      Map<String, String> headers = new HashMap<>();
      headers.put("header1", "value1");
  
      List<GetAttributeQuery.VariabilityEngine_getDecisionBatch> decisionsList = new ArrayList<>();
      GetAttributeQuery.VariabilityEngine_getDecisionBatch decision1 = new GetAttributeQuery.VariabilityEngine_getDecisionBatch(StringUtils.EMPTY,"decision1","WORKFLOWS",Boolean.TRUE);

      GetAttributeQuery.VariabilityEngine_getDecisionBatch decision2 = new GetAttributeQuery.VariabilityEngine_getDecisionBatch(StringUtils.EMPTY,"decision2","WORKFLOWS",Boolean.FALSE);

      decisionsList.add(decision1);
      decisionsList.add(decision2);
  
      when(variabilityGraphqlClient.getDecisionBatch(decisions, headers)).thenReturn(decisionsList);
  
      // Act
      Map<String, Boolean> result = variabilityConnector.getDecisions(decisions, headers);
  
      // Assert
      assertEquals(2, result.size());
      assertTrue(result.containsKey("decision1"));
      assertTrue(result.containsKey("decision2"));
      assertTrue(result.get("decision1"));
      assertFalse(result.get("decision2"));
  }
  
  @Test
  public void test_returns_empty_map_with_empty_list_of_decisions() {
      // Arrange
      VariabilityGraphqlClient variabilityGraphqlClient = mock(VariabilityGraphqlClient.class);
      WASContextHandler wasContextHandler = mock(WASContextHandler.class);
      VariabilityConnector variabilityConnector = new VariabilityConnector(variabilityGraphqlClient, wasContextHandler);
  
      List<String> decisions = Collections.emptyList();
      Map<String, String> headers = new HashMap<>();
      headers.put("header1", "value1");
  
      // Act
      Map<String, Boolean> result = variabilityConnector.getDecisions(decisions, headers);
  
      // Assert
      assertTrue(result.isEmpty());
  }
  
  @Test
  public void test_returns_empty_map_with_null_decisions() {
      // Arrange
      VariabilityGraphqlClient variabilityGraphqlClient = mock(VariabilityGraphqlClient.class);
      WASContextHandler wasContextHandler = mock(WASContextHandler.class);
      VariabilityConnector variabilityConnector = new VariabilityConnector(variabilityGraphqlClient, wasContextHandler);
  
      List<String> decisions = null;
      Map<String, String> headers = new HashMap<>();
      headers.put("header1", "value1");
  
      // Act
      Map<String, Boolean> result = variabilityConnector.getDecisions(decisions, headers);
  
      // Assert
      assertTrue(result.isEmpty());
  }
  
}
