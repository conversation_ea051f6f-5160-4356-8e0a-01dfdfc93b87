package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.v4.work.Project;
import com.intuit.v4.work.definitions.ExternalReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;

public class IPMMapperTest {

  @Test
  public void taskAdaptorMapperCreateTest_dueDate() {

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setPriority(3);
    humanTask.setProcessInstanceId("workflowId");
    humanTask.setComments("Comments");
    humanTask.setCreatedBy("taskManager");
    humanTask.setEstimate(1);
    humanTask.setDueDate("2021-12-31");
    humanTask.setTemplateId("1112334");
    humanTask.setEngagementId("ajsdhjdhj");
    humanTask.setTaskAttributes(taskAttributes());
    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.CREATE, 
        		contextHandler, null, "owner1");

    Assert.assertEquals(project.getName(), humanTask.getActivityName());
    Assert.assertEquals(project.getStatus(), "InProgress");
  }

  @Test
  public void taskAdaptorMapperCreateTest_invalid_dueDate() {

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setPriority(3);
    humanTask.setProcessInstanceId("workflowId");
    humanTask.setComments("Comments");
    humanTask.setCreatedBy("taskManager");
    humanTask.setEstimate(1);
    humanTask.setDueDate("2021-12-31T00:30:00.756Z");
    humanTask.setTaskAttributes(taskAttributes());
    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.CREATE, 
        		contextHandler, null, "owner1");

    Assert.assertEquals(project.getName(), humanTask.getActivityName());
  }

  @Test
  public void taskAdaptorMapperCreateTest() {

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setPriority(3);
    humanTask.setProcessInstanceId("workflowId");
    humanTask.setComments("Comments");
    humanTask.setCreatedBy("taskManager");
    humanTask.setEstimate(1);
    humanTask.setTaskAttributes(taskAttributes());
    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.CREATE, 
        		contextHandler, null, "owner1");

    Assert.assertEquals(project.getName(), humanTask.getActivityName());
    Map<String, Object> additionalTaskAttribute =
        ObjectConverter.fromJson(project.getExternalReferences().get(1).getExternalBlob(),
            new TypeReference<Map<String, Object>>() {
            });
    Assert.assertTrue(additionalTaskAttribute.containsKey("visibility"));
    Assert.assertTrue(additionalTaskAttribute.containsKey("journalNo"));
    Assert.assertFalse(additionalTaskAttribute.containsKey("customerId"));
    Assert.assertFalse(additionalTaskAttribute.containsKey("assigneeId"));
    Assert.assertFalse(additionalTaskAttribute.containsKey("abc"));
  }

  private TaskAttributes taskAttributes() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    Map<String, Object> variables = new HashMap<>();
    variables.put("journalNo", "j#150");
    variables.put("abc", "def");
    variables.put("customerId", "12345678");
    variables.put("assigneeId", "assignId");

    TaskAttributes taskAttributes = TaskAttributes.builder().modelAttributes(modelDefAttributes)
        .runtimeAttributes(runtimeDefAttributes).variables(variables).build();
    return taskAttributes;
  }

  @Test
  public void taskAdaptorMapperCreateTest_offeringId() {

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");
    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus(ActivityConstants.TASK_STATUS_CREATED);
    humanTask.setDescription("QBO test code");
    humanTask.setDueDate("2021-10-08");
    humanTask.setAssigneeId("assignId");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setPriority(3);
    humanTask.setProcessInstanceId("workflowId");
    humanTask.setComments("Comments");
    humanTask.setEstimate(1);
    humanTask.setTaskType("QB_INVOICE_APPROVAL");
    humanTask.setTaskAttributes(taskAttributes());
    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.CREATE, 
        		contextHandler, null, "owner1");
    Assert.assertEquals(project.getName(), humanTask.getActivityName());
    Assert.assertEquals(project.getStatus(), IPMMapper.PROJECT_SERVICE_STATUS_OPEN);
  }

  @Test
  public void taskAdaptorMapperUpdateTest() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setTxnId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29055623");
    humanTask.setPriority(3);
    humanTask.setEstimate(1);
    humanTask.setTaskType("QB_INVOICE_APPROVAL");
    humanTask.setTaskAttributes(taskAttributes());

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("abc", "def");
    attributes.put("visibility", "true");
    humanTask.getTaskAttributes().setVariables(attributes);

    Map<String, Object> additionalTaskMap = new HashMap<>();
    additionalTaskMap.put("visibility", "false");
    additionalTaskMap.put("journalNo", "j#150");

    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.UPDATE, contextHandler,
            Map.of(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES,
                new ExternalReference().externalReferenceId("1")
                    .externalKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES)
                    .externalBlob(ObjectConverter.toJson(additionalTaskMap)))
            , "owner1");

    Map<String, Object> additionalTaskAttribute =
        ObjectConverter.fromJson(project.getExternalReferences().get(0).getExternalBlob(),
            new TypeReference<Map<String, Object>>() {
            });
    Assert.assertNotNull(project.getId());
    Assert.assertTrue(additionalTaskAttribute.containsKey("visibility"));
    Assert.assertTrue(additionalTaskAttribute.containsKey("journalNo"));
    Assert.assertTrue(additionalTaskAttribute.containsKey("abc"));
    Assert.assertFalse(additionalTaskAttribute.containsKey("customerId"));
    Assert.assertFalse(additionalTaskAttribute.containsKey("assigneeId"));

  }

  @Test
  public void taskAdaptorMapperUpdateTest_dueDate_update() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setDueDate("2021-10-08");
    humanTask.setCustomerId("12345678");
    humanTask.setTxnId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29055623");
    humanTask.setPriority(3);
    humanTask.setTaskType("QB_INVOICE_APPROVAL");
    humanTask.setTaskAttributes(taskAttributes());

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("abc", "def");
    attributes.put("visibility", "true");
    humanTask.getTaskAttributes().setVariables(null);

    Map<String, Object> additionalTaskMap = new HashMap<>();
    additionalTaskMap.put("visibility", "false");
    additionalTaskMap.put("journalNo", "j#150");

    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.UPDATE, contextHandler,
            Map.of(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES,
                new ExternalReference().externalReferenceId("1")
                    .externalKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES)
                    .externalBlob(ObjectConverter.toJson(additionalTaskMap)))
            , "owner1");
    Assert.assertNotNull(project.getId());
  }


  @Test
  public void taskAdaptorMapperUpdateTest_noAdditionalTaskAttribute() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setTxnId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29055623");
    humanTask.setPriority(3);
    humanTask.setEstimate(1);
    humanTask.setTaskType("QB_INVOICE_APPROVAL");
    humanTask.setTaskAttributes(taskAttributes());
    humanTask.getTaskAttributes().setVariables(null);

    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.UPDATE, contextHandler, null, "owner1");
    Assert.assertNotNull(project.getId());
    Assert.assertEquals(0, project.getExternalReferences().size());
  }

  @Test
  public void taskAdaptorMapperUpdateTest_additionalTaskAttribute() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setTxnId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29055623");
    humanTask.setPriority(3);
    humanTask.setEstimate(1);
    humanTask.setTaskType("QB_INVOICE_APPROVAL");
    humanTask.setTaskAttributes(taskAttributes());

    Map<String, Object> attributes = new HashMap<>();
    attributes.put("abc", "def");
    attributes.put("isVisibility", true);
    humanTask.getTaskAttributes().setVariables(attributes);

    Map<String, ExternalReference> additionalTaskReference = new HashMap<>();
    ExternalReference externalReference = new ExternalReference()
        .externalKey(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES)
        .externalReferenceId("122")
        .externalBlob("{\"isVisibility\":false}");
    additionalTaskReference.put(ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES, externalReference);
    ExternalReference externalReference1 = new ExternalReference()
        .externalKey("anotherAttribute")
        .externalReferenceId("123")
        .externalBlob("{\"journalNo\":\"j#1\"}");
    additionalTaskReference.put("anotherAttribute", externalReference1);

    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.UPDATE, contextHandler,
            additionalTaskReference, "owner1");

    Assert.assertNotNull(project.getId());
    Optional<ExternalReference> extReference = project.getExternalReferences().stream()
        .filter(
            extRef -> ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES.equals(extRef.getExternalKey()))
        .findFirst();
    Assert.assertTrue(extReference.isPresent());

    ExternalReference additionalTaskRef = extReference.get();
    Assert.assertEquals("122", additionalTaskRef.getExternalReferenceId());
    Map<String, Object> additionalTaskAttrMap =
        ObjectConverter.fromJson(additionalTaskRef.getExternalBlob(),
            new TypeReference<Map<String, Object>>() {
            });
    Assert.assertTrue(additionalTaskAttrMap.containsKey("isVisibility"));
    Assert.assertTrue((boolean) additionalTaskAttrMap.get("isVisibility"));
    Assert.assertTrue(additionalTaskAttrMap.containsKey("abc"));
  }

  @Test
  public void taskAdaptorMapperCompleteTest() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("completed");
    humanTask.setDescription("QBO test code");
    humanTask.setDueDate("2021-10-08");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setTxnId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29055623");
    humanTask.setPriority(3);
    humanTask.setEstimate(1);
    humanTask.setTaskAttributes(taskAttributes());

    Map<String, ExternalReference> additionalTaskReference = new HashMap<>();
    ExternalReference externalReference1 = new ExternalReference()
        .externalKey("anotherAttribute")
        .externalReferenceId("123")
        .externalBlob("{\"journalNo\":\"j#1\"}");
    Map.of("anotherAttribute", externalReference1);

    Project project = IPMMapper
        .mapProjectServiceData(humanTask, Operation.COMPLETE, contextHandler,
            additionalTaskReference, "owner1");

    Assert.assertNotNull(project.getId());
    Assert.assertEquals(project.getStatus(), IPMMapper.PROJECT_SERVICE_STATUS_COMPLETE);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void taskAdaptorMapperUpdateTestFailure() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");
    HumanTask humanTask = new HumanTask();
    humanTask.setActivityName("TestTask-01");
    humanTask.setStatus("InProgress");
    humanTask.setDescription("QBO test code");
    humanTask.setDueDate("2021-10-08");
    humanTask.setAssigneeId("assignId");
    humanTask.setCreatedBy("taskManager");
    humanTask.setId("2323872737283382738273");
    humanTask.setPriority(2);
    humanTask.setCustomerId("12345678");
    humanTask.setPriority(3);
    humanTask.setEstimate(1);
    humanTask.setTaskAttributes(taskAttributes());
    IPMMapper.mapProjectServiceData(humanTask, Operation.UPDATE,
    		contextHandler, null, "owner1");
  }
  
  @Test
  public void test_getReversedStatus() {
	  Assert.assertEquals(IPMMapper
			  .getReversedStatus(IPMMapper.PROJECT_SERVICE_STATUS_OPEN),
			  ActivityConstants.TASK_STATUS_CREATED);
	  Assert.assertEquals(IPMMapper
			  .getReversedStatus(IPMMapper.PROJECT_SERVICE_STATUS_COMPLETE),
			  ActivityConstants.TASK_STATUS_COMPLETE);
	  Assert.assertEquals(IPMMapper
			  .getReversedStatus("Blocked"),"Blocked");
  }
}
