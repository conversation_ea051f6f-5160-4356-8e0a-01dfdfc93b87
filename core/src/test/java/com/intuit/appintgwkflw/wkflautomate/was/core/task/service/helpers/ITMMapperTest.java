package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;


import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_REFERENCES_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_REFERENCES_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_IN_PROGRESS;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceType;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;

public class ITMMapperTest {

  @Test
  public void taskAdaptorMapperCreateTest() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId("humanTask1");
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");
    humanTask.setProcessInstanceId("p1");
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
        ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    Assert.assertEquals(
        itmCreateTaskInput.metadata(),
        "{\"external_blob_metadata\":[{\"wasReferences\":[{\"blob\":\"{\\\"processId\\\":\\\"p1\\\",\\\"entityId\\\":\\\"1\\\",\\\"externalTaskId\\\":\\\"humanTask1\\\"}\"}]},{\"additionalTaskAttributes\":[{\"blob\":\"{\\\"visibility\\\":\\\"true\\\",\\\"entityType\\\":\\\"invoice\\\"}\"}]}]}");
  }
  
  @Test
  public void taskAdaptorMapperCreateTestWithReferences() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId(String.valueOf(UUID.randomUUID()));
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");
    humanTask.setReferences(List.of(
            ObjectConverter.toJson(Map.of(ITM_REFERENCES_TYPE, "INVOICE", ITM_REFERENCES_ID, "1"))
    ));
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
            ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    TaskManagement_ReferenceInput itmReference = itmCreateTaskInput.references().get(0);
    Assert.assertEquals(TaskManagement_ReferenceType.INVOICE, itmReference.referenceType());
    Assert.assertEquals("1", itmReference.referenceId());
  }

  @Test
  public void taskAdaptorMapperCreateTestWithInvalidReferencesJSON() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId(String.valueOf(UUID.randomUUID()));
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");

    humanTask.setReferences(List.of("invalid json"));
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
            ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    Assert.assertEquals(itmCreateTaskInput.references().size(), 0);
  }

  @Test
  public void taskAdaptorMapperCreateTestWithInvalidReferenceType() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId(String.valueOf(UUID.randomUUID()));
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");

    humanTask.setReferences(List.of(
            ObjectConverter.toJson(Map.of(ITM_REFERENCES_TYPE, "INVOICE1", ITM_REFERENCES_ID, "1"))
    ));
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
            ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    Assert.assertEquals(itmCreateTaskInput.references().size(), 0);
  }

  @Test
  public void taskAdaptorMapperCreateTestWithNoReferenceType() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId(String.valueOf(UUID.randomUUID()));
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");
    
    humanTask.setReferences(List.of(
            ObjectConverter.toJson(Map.of(ITM_REFERENCES_ID, "1"))
    ));
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
            ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    Assert.assertEquals(itmCreateTaskInput.references().size(), 0);
  }

  @Test
  public void taskAdaptorMapperCreateTestWithNoReferenceId() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setId(String.valueOf(UUID.randomUUID()));
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setStatus("created");
    humanTask.setDueDate("2021-12-31");
    humanTask.setTaskType("SAMPLE_TASK_TYPE");
    humanTask.setAssigneeId("assignId");
    humanTask.setRecordId("1");

    humanTask.setReferences(List.of(
            ObjectConverter.toJson(Map.of(ITM_REFERENCES_TYPE, "INVOICE"))
    ));
    humanTask.setTaskAttributes(taskAttributes());
    TaskManagement_CreateTaskInput itmCreateTaskInput =
            ITMMapper.createITMCreateTaskInput(humanTask);

    Assert.assertEquals(itmCreateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmCreateTaskInput.status(), ITM_STATUS_IN_PROGRESS);
    Assert.assertNotNull(itmCreateTaskInput.references());
    Assert.assertEquals(itmCreateTaskInput.references().size(), 0);
  }

  @Test
  public void taskAdaptorMapperUpdateTest() {
    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.OWNER_ID, "own1");
    contextHandler.addKey(WASContextEnums.OFFERING_ID, "off1");

    HumanTask humanTask = new HumanTask();
    humanTask.setTxnId("1");
    humanTask.setTaskName("TestTask-01");
    humanTask.setDescription("QBO test code");
    humanTask.setDueDate("2021-12-31");
    humanTask.setAssigneeId("assignId");
    TaskManagement_UpdateTaskInput itmUpdateTaskInput =
        ITMMapper.createITMUpdateTaskInput(humanTask);

    Assert.assertEquals(itmUpdateTaskInput.id(), humanTask.getTxnId());
    Assert.assertEquals(itmUpdateTaskInput.name(), humanTask.getTaskName());
    Assert.assertEquals(itmUpdateTaskInput.description(), humanTask.getDescription());
    Assert.assertTrue(itmUpdateTaskInput.dueDate() instanceof DateTime);
    Assert.assertEquals(itmUpdateTaskInput.assignee(), humanTask.getAssigneeId());
  }

  private TaskAttributes taskAttributes() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("assigneeId", "${taskAssignee}");
    runtimeDefAttributes.put("taskName", "Task - ${docNumber}");
    runtimeDefAttributes.put("entityType", "invoice");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("visibility", "true");

    Map<String, Object> variables = new HashMap<>();
    variables.put("taskAssignee", "assignId");
    variables.put("docNumber", "1010");
    variables.put("entityType", "invoice");
    variables.put("visibility", "true");

    return TaskAttributes.builder()
        .modelAttributes(modelDefAttributes)
        .runtimeAttributes(runtimeDefAttributes)
        .variables(variables)
        .build();
  }
}
