package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleAttributeConfigurations;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleHelperTest {

    private ThrottleConfigs throttleConfigs = new ThrottleConfigs();
    private ThrottleHelper throttleHelper;

    @Before
    public void setUp() {
       throttleHelper = new ThrottleHelper(throttleConfigs);
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_false_WorkflowsConfigNull() {
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("something", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_false_WorkflowDisabled() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        Map<ThrottleAttribute, Boolean> disabledMap = new HashMap<>();
        disabledMap.put(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, true);
        ThrottleAttributeConfigurations attributes = new ThrottleAttributeConfigurations();
        attributes.setDisable(disabledMap);
        attributesMap.put("definitionKey1", attributes);
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_false_WorkflowNotDisabled() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        Map<ThrottleAttribute, Boolean> disabledMap = new HashMap<>();
        disabledMap.put(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, false);
        ThrottleAttributeConfigurations attributes = new ThrottleAttributeConfigurations();
        attributes.setDisable(disabledMap);
        attributesMap.put("definitionKey1", attributes);
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_true_WorkflowConfigPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("definitionKey1", populateEntityAttributes(5));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertTrue(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_false_WorkflowConfigNotPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("definitionKey2", populateEntityAttributes(5));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_true_DefaultConfigPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("default", populateEntityAttributes(5));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertTrue(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
    }

    @Test
    public void testIsThrottlingEnabledForWorkflow_false_NoConfigPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertFalse(throttleHelper.isThrottlingEnabledForWorkflow("definitionKey1", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void getThreshold_whenThresholdPresentForWorkflow() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("definitionKey2", populateEntityAttributes(5));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertEquals((Integer) 5, throttleHelper.getThreshold("definitionKey2", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void getThreshold_whenDefaultThresholdPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("default", populateEntityAttributes(10));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertEquals((Integer) 10, throttleHelper.getThreshold("definitionKey2", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    // Overridden config should take precedence over default config
    @Test
    public void getThreshold_whenBothWorkflowAndDefaultThresholdPresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();

        attributesMap.put("default", populateEntityAttributes(10));
        attributesMap.put("definitionKey2", populateEntityAttributes(5));
        throttleConfigs.setWorkflow(attributesMap);

        Assert.assertEquals((Integer) 5, throttleHelper.getThreshold("definitionKey2", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }

    @Test
    public void getTimeframe_whenTimeframePresentForWorkflow() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("definitionKey3", populateTimeframe(100));
        throttleConfigs.setWorkflow(attributesMap);

        // 3 checks performed in same unit test method
        Assert.assertTrue(throttleHelper.isTimeframeDefined("definitionKey3", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
        Assert.assertFalse(throttleHelper.isTimeframeDefined("definitionKey4", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
        Assert.assertEquals((Integer) 100, throttleHelper.getTimeframeForWorkflow("definitionKey3", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
    }

    @Test
    public void getTimeframe_whenDefaultTimeframePresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();
        attributesMap.put("default", populateTimeframe(50));
        throttleConfigs.setWorkflow(attributesMap);
        Assert.assertTrue(throttleHelper.isTimeframeDefined("definitionKey3", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
        Assert.assertEquals((Integer) 50, throttleHelper.getTimeframeForWorkflow("definitionKey2", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
    }

    // Overridden config should take precedence over default config
    @Test
    public void getTimeframe_whenBothWorkflowAndDefaultTimeframePresent() {
        Map<String, ThrottleAttributeConfigurations> attributesMap = new HashMap<>();

        attributesMap.put("default", populateTimeframe(150));
        attributesMap.put("definitionKey2", populateTimeframe(250));
        throttleConfigs.setWorkflow(attributesMap);

        Assert.assertEquals((Integer) 250, throttleHelper.getTimeframeForWorkflow("definitionKey2", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME));
    }

    private ThrottleAttributeConfigurations populateEntityAttributes(Integer thresholdValue) {
        // Populate threshold values
        Map<ThrottleAttribute, Integer> thresholdMap = new HashMap<>();
        thresholdMap.put(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, thresholdValue);
        ThrottleAttributeConfigurations attributes = new ThrottleAttributeConfigurations();
        attributes.setThreshold(thresholdMap);
        return  attributes;
    }

    private ThrottleAttributeConfigurations populateTimeframe(Integer timeframe) {
        // Populate timeframe values
        Map<ThrottleAttribute, Integer> timeframeMap = new HashMap<>();
        timeframeMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, timeframe);
        ThrottleAttributeConfigurations attributes = new ThrottleAttributeConfigurations();
        attributes.setTimeframeMillis(timeframeMap);
        return  attributes;
    }
}
