package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleServiceHandlersTest {
    @Mock
    ThrottleService throttleService;

    @Test
    public void testGetThrottleHandlers() {
        ThrottleServiceHandlers.addHandler(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, throttleService);
        Assert.assertEquals(throttleService,
                ThrottleServiceHandlers.getHandler(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY));
    }
}
