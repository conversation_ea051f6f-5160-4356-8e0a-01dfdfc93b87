package com.intuit.appintgwkflw.wkflautomate.was.core.ticket;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OfflineTicketServiceTest {

  @Mock private AuthDetailsServiceHelper authDetailsServiceHelper;
  @Mock private WASContextHandler contextHandler;

  @InjectMocks private OfflineTicketServiceImpl offlineTicketServiceImpl;


  @Test
  public void testSuccess() {
    WorkflowGenericResponse response = offlineTicketServiceImpl.updateOfflineTicket();
    assertNotNull(response);
    assertEquals(ResponseStatus.SUCCESS, response.getStatus());
  }
}