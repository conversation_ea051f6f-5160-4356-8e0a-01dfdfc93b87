package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_USERID;

@RunWith(MockitoJUnitRunner.class)
public class AuthHelperTest {

    @Mock private WASContextHandler contextHandler;
    @InjectMocks private AuthHelper authHelper;

    @Test
    public void testCanExtractVariablesFromAuth() {
        Assert.assertTrue(authHelper.canExtractVariablesFromAuth(INTUIT_USERID));
        Assert.assertTrue(authHelper.canExtractVariablesFromAuth(INTUIT_REALMID));
        Assert.assertFalse(authHelper.canExtractVariablesFromAuth("txnAmount"));
    }

    @Test
    public void testGetAuthDetailsFromList(){
        AuthDetails authDetails = new AuthDetails();
        authDetails.setOwnerId(123456L);
        List<AuthDetails> authDetailsList = Arrays.asList(authDetails);
        Optional<AuthDetails> authDetails1 = authHelper.getAuthDetailsFromList(Optional.of(authDetailsList));
        Assert.assertEquals(authDetails1.get().getOwnerId(), authDetails.getOwnerId());
    }

    @Test
    public void testGetMultipleAuthDetailsFromList(){
        AuthDetails authDetails = new AuthDetails();
        authDetails.setOwnerId(123456L);
        AuthDetails authDetails2 = new AuthDetails();
        authDetails.setOwnerId(1234567L);
        List<AuthDetails> authDetailsList = Arrays.asList(authDetails, authDetails2);
        Optional<AuthDetails> authDetails1 = authHelper.getAuthDetailsFromList(Optional.of(authDetailsList));
        Assert.assertEquals(authDetails1.get().getOwnerId(), authDetails.getOwnerId());
    }

    @Test
    public void testGetAuthDetailsFromListEmpty(){
        List<AuthDetails> authDetailsList = new ArrayList<>();
        Optional<AuthDetails> authDetails1 = authHelper.getAuthDetailsFromList(Optional.of(authDetailsList));
        Assert.assertEquals(authDetails1, Optional.empty());
    }

    @Test
    public void testGetAuthValueForKey() {
        String header = "Intuit_IAM_Authentication intuit_userid=12378 ,intuit_realmid=9130347714346286";
        Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
        Assert.assertEquals("9130347714346286", authHelper.getOwnerId());
        Assert.assertEquals("12378", authHelper.getAuthValueForKey(INTUIT_USERID));
        Assert.assertNull(authHelper.getAuthValueForKey("aNewKey"));
    }

    @Test
    public void testGetAuthValueForKey_noRealmIdInHeader() {
        String header = "Intuit_IAM_Authentication intuit_userid=12378";
        Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("556677");
        Assert.assertEquals("556677", authHelper.getAuthValueForKey(INTUIT_REALMID));
    }
}
