package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.FlowNodeImpl;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaIn;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaOut;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaOutputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BpmnProcessorUtilTest {

    private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";
    private static final String INVOICE_APPROVAL_BPMN_DMN = "bpmn/invoiceApproval_serviceDmn.bpmn";
    private static final String INVOICE_NO_PROCESS = "bpmn/inv_no_process.bpmn";
    @Mock
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    private Map<String, String> operatorMap;
    private Map<String, String> operatorToSymbolMap;
    private BpmnModelInstance bpmnModelInstance;

    @Before
    @SneakyThrows
    public void setup() {
        operatorMap = new HashMap<>();
        operatorToSymbolMap = new HashMap<>();
        operatorMap.put("GT", ">");
        operatorMap.put("GTE", ">=");
        operatorMap.put("LT", "<");
        operatorMap.put("LTE", "<=");
        operatorMap.put("EQ", "==");
        operatorMap.put("CONTAINS", "contains");
        operatorMap.put("NOT_CONTAINS", "!contains");
        operatorToSymbolMap.put(">", "GT");
        operatorToSymbolMap.put(">=", "GTE");
        operatorToSymbolMap.put("<", "LT");
        operatorToSymbolMap.put("<=", "LTE");
        operatorToSymbolMap.put("==", "EQ");
        bpmnModelInstance =
                Bpmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_APPROVAL_BPMN));
    }

    @Test
    public void testGetDmnServiceTasks_NoServiceDmnTasks() {
        bpmnModelInstance =
                Bpmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_APPROVAL_BPMN));
        Assert.assertTrue(BpmnProcessorUtil.getDmnServiceTasks(bpmnModelInstance).isEmpty());
    }

    @Test
    public void testGetDmnServiceTasks_WithServiceDmnTasks() {
        BpmnModelInstance bpmnModelInstance =
                Bpmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_APPROVAL_BPMN_DMN));
        Assert.assertEquals(
                Collections.singletonList("decision_approval"),
                BpmnProcessorUtil.getDmnServiceTasks(bpmnModelInstance));
    }

    @Test
    public void testConvertBpmnModelInstanceToByteArray() {
        BpmnModelInstance bpmnModelInstance =
                Bpmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_APPROVAL_BPMN));
        Assert.assertNotNull(BpmnProcessorUtil.convertBpmnModelInstanceToByteArray(bpmnModelInstance));
    }

    @Test
    public void testConvertDmnModelInstanceToByteArray() {
        DmnModelInstance dmnModelInstance =
                Dmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_APPROVAL_DMN));
        Assert.assertNotNull(BpmnProcessorUtil.convertDmnModelInstanceToByteArray(dmnModelInstance));
    }

    @Test
    public void testReadBPMN() throws Exception {
        Assert.assertNotNull(BpmnProcessorUtil.readBPMN(IOUtils.toByteArray(BpmnProcessorUtilTest.class
                .getClassLoader()
                .getResourceAsStream(INVOICE_APPROVAL_BPMN))));
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testReadBPMN_Exception() throws Exception {
        BpmnProcessorUtil.readBPMN(IOUtils.toByteArray(BpmnProcessorUtilTest.class
                .getClassLoader()
                .getResourceAsStream(INVOICE_APPROVAL_DMN)));
    }

    @Test
    public void testReadDmn() throws Exception {
        Assert.assertNotNull(BpmnProcessorUtil.readDmn(IOUtils.toByteArray(BpmnProcessorUtilTest.class
                .getClassLoader()
                .getResourceAsStream(INVOICE_APPROVAL_DMN))));
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testReadDMN_Exception() throws Exception {
        BpmnProcessorUtil.readDmn(IOUtils.toByteArray(BpmnProcessorUtilTest.class
                .getClassLoader()
                .getResourceAsStream(INVOICE_APPROVAL_BPMN)));
    }

    @Test
    public void testgetSupportedOperator() {
        Map<String, String> response = BpmnProcessorUtil.getSupportedOperator("DAYS");
        Assert.assertNotNull(response);
        Assert.assertTrue(response.size() > 1);
    }

    @Test
    public void testGetSupportedOperatorString() {
        Map<String, String> response = BpmnProcessorUtil.getSupportedOperator("STRING");
        Assert.assertNotNull(response);
        Assert.assertEquals(2, response.size());
        Assert.assertEquals("Within", response.get(WorkflowConstants.CONTAINS_OPERATOR));
        Assert.assertEquals("Not Within", response.get(WorkflowConstants.NOT_CONTAINS));
        Assert.assertNull(response.get("testKey"));
    }

    @Test
    public void testgetListSupportedOperator() {
        Map<String, String> response = BpmnProcessorUtil.getSupportedOperator("LIST");
        Assert.assertNotNull(response);
        Assert.assertTrue(response.size() == 2);
    }

    @Test
    public void testgetSupportedOperatorNoMatch() {
        Map<String, String> response = BpmnProcessorUtil.getSupportedOperator("abc");
        Assert.assertNotNull(response);
        Assert.assertEquals(0, response.size());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testInvalidFileFormatTest() {
        BpmnModelInstance bpmnModelInstance =
                Bpmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream(INVOICE_NO_PROCESS));
        BpmnProcessorUtil.getDmnServiceTasks(bpmnModelInstance);
    }

    @Test
    public void verifyGetStartEvent() throws IOException {
        BpmnModelInstance modelInstance =
                BpmnProcessorUtil.readBPMN(
                        IOUtils.toByteArray(
                                BpmnProcessorUtilTest.class
                                        .getClassLoader()
                                        .getResourceAsStream(INVOICE_APPROVAL_BPMN)));
        FlowElement flowElement = BpmnProcessorUtil.findStartEventElement(modelInstance);

        Assert.assertNotNull(flowElement);
        Assert.assertEquals("Invoice create event", flowElement.getName());
        System.out.println(flowElement.getId());
        Assert.assertEquals("newInvoiceCreated_invoiceapproval", flowElement.getId());
        Assert.assertEquals("startEvent", flowElement.getElementType().getTypeName());
    }

    @Test
    public void testIsEndEventElement() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        FlowNode businessRuleElement = outgoingSequenceFlows.stream().findFirst().get().getTarget();
        Collection<SequenceFlow> outgoingSequences = businessRuleElement.getOutgoing();
        ArrayList<SequenceFlow> outgoingSequencesList = new ArrayList<>(outgoingSequences);
        Collection<SequenceFlow> calllActivityOutgoing = outgoingSequencesList.get(1).getTarget().getOutgoing();
        boolean result = BpmnProcessorUtil.isEndEventElement(calllActivityOutgoing.stream().findFirst().get().getTarget());
        Assert.assertTrue(result);
    }

    @Test
    public void testIsCallActivityElement() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        FlowNode businessRuleElement = outgoingSequenceFlows.stream().findFirst().get().getTarget();
        Collection<SequenceFlow> outgoingSequences = businessRuleElement.getOutgoing();
        ArrayList<SequenceFlow> outgoingSequencesList = new ArrayList<>(outgoingSequences);
        boolean result = BpmnProcessorUtil.isCallActivityElement(outgoingSequencesList.get(1).getTarget());
        Assert.assertFalse(result);
    }

    @Test
    public void testIsBusinessRuleTaskElement() {
        FlowElement startEventFlowElement = CustomWorkflowUtil.findStartEventElement(bpmnModelInstance);
        Collection<SequenceFlow> outgoingSequenceFlows = ((FlowNodeImpl) startEventFlowElement).getOutgoing();
        FlowNode businessRuleElement = outgoingSequenceFlows.stream().findFirst().get().getTarget();
        boolean result = BpmnProcessorUtil.isBusinessRuleTaskElement(businessRuleElement);
        Assert.assertTrue(result);
    }

    @Test
    public void testIfListHasDuplicateTriggerNames(){
        List<TriggerDetails> triggerDetailsList = new ArrayList<>();
        TriggerDetails triggerDetails1 = new TriggerDetails();
        triggerDetails1.setTriggerName("testName");
        TriggerDetails triggerDetails2 = new TriggerDetails();
        triggerDetails2.setTriggerName("testName");

        triggerDetailsList.add(triggerDetails1);
        triggerDetailsList.add(triggerDetails2);

        assertThrows(WorkflowGeneralException.class, () ->
                BpmnProcessorUtil.validateDuplicateTriggerNames(triggerDetailsList),
            WorkflowError.TEMPLATE_CONTAINS_DUPLICATE_TRIGGER_NAMES.getErrorMessage());
    }

    @Test
    public void testGetListOfCamundaInParametersForNullInput(){
        Assert.assertNull(BpmnProcessorUtil.getListOfCamundaInParameters(null));
    }

    @Test
    public void testGetListOfCamundaInParameters(){
        ExtensionElements extensionElements =bpmnModelInstance.newInstance(ExtensionElements.class);
        CamundaIn camundaIn = bpmnModelInstance.newInstance(CamundaIn.class);
        CamundaOut camundaOut = bpmnModelInstance.newInstance(CamundaOut.class);
        CamundaInputParameter camundaInputParameter = bpmnModelInstance.newInstance(CamundaInputParameter.class);
        CamundaOutputParameter camundaOutputParameter = bpmnModelInstance.newInstance(CamundaOutputParameter.class);
        CamundaInputOutput camundaInputOutput = bpmnModelInstance.newInstance(CamundaInputOutput.class);
        CamundaProperty camundaProperty = bpmnModelInstance.newInstance(CamundaProperty.class);
        CamundaProperties camundaProperties =  bpmnModelInstance.newInstance(CamundaProperties.class);

        extensionElements.addChildElement(camundaIn);
        extensionElements.addChildElement(camundaOut);
        extensionElements.addChildElement(camundaInputParameter);
        extensionElements.addChildElement(camundaOutputParameter);
        extensionElements.addChildElement(camundaInputOutput);
        extensionElements.addChildElement(camundaProperty);
        extensionElements.addChildElement(camundaProperties);

        Assert.assertEquals(1, BpmnProcessorUtil.getListOfCamundaInParameters(extensionElements).size());
    }

    @Test
    public void testGetListOfCamundaOutParametersForNullInput(){
        Assert.assertNull(BpmnProcessorUtil.getListOfCamundaOutParameters(null));
    }
    @Test
    public void testGetListOfCamundaOutParameters(){
        ExtensionElements extensionElements =bpmnModelInstance.newInstance(ExtensionElements.class);
        CamundaIn camundaIn = bpmnModelInstance.newInstance(CamundaIn.class);
        CamundaOut camundaOut = bpmnModelInstance.newInstance(CamundaOut.class);
        CamundaInputParameter camundaInputParameter = bpmnModelInstance.newInstance(CamundaInputParameter.class);
        CamundaOutputParameter camundaOutputParameter = bpmnModelInstance.newInstance(CamundaOutputParameter.class);
        CamundaInputOutput camundaInputOutput = bpmnModelInstance.newInstance(CamundaInputOutput.class);
        CamundaProperty camundaProperty = bpmnModelInstance.newInstance(CamundaProperty.class);
        CamundaProperties camundaProperties =  bpmnModelInstance.newInstance(CamundaProperties.class);

        extensionElements.addChildElement(camundaIn);
        extensionElements.addChildElement(camundaOut);
        extensionElements.addChildElement(camundaInputParameter);
        extensionElements.addChildElement(camundaOutputParameter);
        extensionElements.addChildElement(camundaInputOutput);
        extensionElements.addChildElement(camundaProperty);
        extensionElements.addChildElement(camundaProperties);

        Assert.assertEquals(1, BpmnProcessorUtil.getListOfCamundaOutParameters(extensionElements).size());
    }

    @Test
    public void testUpdateBpmnName(){
        Assert.assertNotEquals("testDisplayName",
            bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst().get().getName());

        BpmnProcessorUtil.updateBpmnName(bpmnModelInstance, "testDisplayName");

        Assert.assertEquals("testDisplayName",
            bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst().get().getName());
    }

    @Test
    public void testReadBpmnFile(){
        BpmnModelInstance result = BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");
        Assert.assertNotNull(result);
    }

    @Test
    public void testReadDmnFile(){
        DmnModelInstance result = BpmnProcessorUtil.readDMNFile("baseTemplates/dmn/customApprovalBaseTemplate.dmn");
        Assert.assertNotNull(result);

    }
}
