package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.DownStreamConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowCore;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CamundaRestUtilTest {

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Mock private OfferingConfig offeringConfig;
  @Mock private WorkflowCoreConfig workflowCoreConfig;
  @InjectMocks private CamundaRestUtil camundaRestUtil;

  @After
  public void clear() {
    WASContext.clear();
  }

  @Test
  public void testGetCamundaBaseURLOfferingIdInContext() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfig());
    Assert.assertEquals("url", camundaRestUtil.getCamundaBaseURL());
  }

  @Test
  public void testGetCamundaBaseURLNoOfferingIdInContext() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url2");
    Assert.assertEquals("url2", camundaRestUtil.getCamundaBaseURL());
  }

  @Test
  public void testGetCamundaBaseURLWithOfferingId() {
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfig());
    Assert.assertEquals("url", camundaRestUtil.getCamundaBaseURL(Optional.of("test")));
  }

  @Test
  public void testGetCamundaBaseURLWithEmptyOfferingId() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url3");
    Assert.assertEquals("url3", camundaRestUtil.getCamundaBaseURL(Optional.of(StringUtils.EMPTY)));
  }

  @Test
  public void testGetCamundaBaseURLWithNullOfferingId() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url3");
    Assert.assertEquals("url3", camundaRestUtil.getCamundaBaseURL(Optional.ofNullable(null)));
  }

  @Test
  public void testCamundaBaseUrlInvalidSource() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url4");
    Assert.assertEquals("url4", camundaRestUtil.getCamundaBaseURL(Optional.of("invalid")));
  }

  @Test
  public void testCamundaBaseUrlNoWorkflowConfig() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfigNoWorkflowCore());
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url5");
    Assert.assertEquals("url5", camundaRestUtil.getCamundaBaseURL(Optional.of("test")));
  }

  @Test
  public void testCamundaBaseUrlOfferingWithNoBaseUrl() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfigNoBaseUrl());
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url6");
    Assert.assertEquals("url6", camundaRestUtil.getCamundaBaseURL(Optional.of("test")));
  }

  @Test
  public void testCamundaBaseUrlNoBaseUrl() {
    Mockito.when(workflowCoreConfig.getRestEndpointPrefix()).thenReturn("url4");
    Assert.assertEquals("url4", camundaRestUtil.getCamundaBaseURL(Optional.of("invalid")));
  }

  @Test
  public void testCamundaHostUrlOfferingIdInContext() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfig());
    Assert.assertEquals("hostUrl", camundaRestUtil.getCamundaHostUrl());
  }

  @Test
  public void testCamundaHostUrlNoOfferingIdInContext() {
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("hostUrl2");
    Assert.assertEquals("hostUrl2", camundaRestUtil.getCamundaHostUrl());
  }

  @Test
  public void testCamundaHostDefaultSource() {
    WASContext.setOfferingId("invalid");
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("hostUrl3");
    Assert.assertEquals("hostUrl3", camundaRestUtil.getCamundaHostUrl());
  }

  @Test
  public void testCamundaHostUrlNoWorkflowCore() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfigNoWorkflowCore());
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("hostUrl4");
    Assert.assertEquals("hostUrl4", camundaRestUtil.getCamundaHostUrl());
  }

  @Test
  public void testCamundaHostUrlWorkflowCoreNoHost() {
    WASContext.setOfferingId("test");
    Mockito.when(offeringConfig.getDownstreamServices()).thenReturn(getConfigNoHost());
    Mockito.when(workflowCoreConfig.getHostEndpointPrefix()).thenReturn("hostUrl5");
    Assert.assertEquals("hostUrl5", camundaRestUtil.getCamundaHostUrl());
  }

  private List<DownStreamConfig> getConfig() {
    List<DownStreamConfig> downStreamConfigs = new ArrayList<>();
    DownStreamConfig downStreamConfig = new DownStreamConfig();
    downStreamConfig.setOfferingId("test");

    WorkflowCore workflowCore = new WorkflowCore();
    workflowCore.setBaseUrl("url");
    workflowCore.setHostUrl("hostUrl");

    downStreamConfig.setWorkflowCore(workflowCore);
    downStreamConfigs.add(downStreamConfig);
    return downStreamConfigs;
  }

  private List<DownStreamConfig> getConfigNoWorkflowCore() {
    List<DownStreamConfig> downStreamConfigs = new ArrayList<>();
    DownStreamConfig downStreamConfig = new DownStreamConfig();
    downStreamConfig.setOfferingId("test");

    downStreamConfigs.add(downStreamConfig);
    return downStreamConfigs;
  }

  private List<DownStreamConfig> getConfigNoBaseUrl() {
    List<DownStreamConfig> downStreamConfigs = new ArrayList<>();
    DownStreamConfig downStreamConfig = new DownStreamConfig();
    downStreamConfig.setOfferingId("test");

    WorkflowCore workflowCore = new WorkflowCore();
    workflowCore.setHostUrl("hostUrl");

    downStreamConfig.setWorkflowCore(workflowCore);
    downStreamConfigs.add(downStreamConfig);
    return downStreamConfigs;
  }

  private List<DownStreamConfig> getConfigNoHost() {
    List<DownStreamConfig> downStreamConfigs = new ArrayList<>();
    DownStreamConfig downStreamConfig = new DownStreamConfig();
    downStreamConfig.setOfferingId("test");

    WorkflowCore workflowCore = new WorkflowCore();
    workflowCore.setBaseUrl("url");

    downStreamConfig.setWorkflowCore(workflowCore);
    downStreamConfigs.add(downStreamConfig);
    return downStreamConfigs;
  }
}