package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import java.lang.reflect.Field;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class CountSlidingWindowTest {

  @Test(expected = WorkflowGeneralException.class)
  public void testSlidingWindowZeroSize(){
    CountSlidingWindow.of(0, 0);
  }

  /**
   * Keep window and max score of size 1
   */
  @Test
  public void testSlidingWindowSizeOne(){
    CountSlidingWindow countSlidingWindow = CountSlidingWindow.of(1, 1);
    countSlidingWindow.recordFailure();
    Assert.assertEquals(1, countSlidingWindow.getFailureCount());
    Assert.assertEquals(1, countSlidingWindow.failureScore());
    countSlidingWindow.recordSuccess();
    Assert.assertEquals(0, countSlidingWindow.getFailureCount());
    Assert.assertEquals(0, countSlidingWindow.failureScore());
    countSlidingWindow.recordFailure();
    Assert.assertEquals(1, countSlidingWindow.getFailureCount());
    Assert.assertEquals(1, countSlidingWindow.failureScore());
    countSlidingWindow.recordSuccess();
    Assert.assertEquals(0, countSlidingWindow.getFailureCount());
    Assert.assertEquals(0, countSlidingWindow.failureScore());
  }

  /**
   * Adding 20 success samples in window..All will be zero values
   * then adding another 20 samples, out of which 4 are negative
   * Failure count will be 4
   * @throws NoSuchFieldException
   * @throws IllegalAccessException
   */
  @Test
  public void testSlidingWindowSize10() throws NoSuchFieldException, IllegalAccessException {
    CountSlidingWindow countSlidingWindow = CountSlidingWindow.of(10, 10);
    countSlidingWindow.recordFailure();
    Assert.assertEquals(1, countSlidingWindow.getFailureCount());
    Assert.assertEquals(1, countSlidingWindow.failureScore());
    countSlidingWindow.recordSuccess();
    Assert.assertEquals(1, countSlidingWindow.getFailureCount());
    Assert.assertEquals(1, countSlidingWindow.failureScore());

    for (int i = 0 ; i< 20; i++){
      countSlidingWindow.recordSuccess();
    }
    Assert.assertEquals(0, countSlidingWindow.getFailureCount());
    Assert.assertEquals(0, countSlidingWindow.failureScore());

    for (int i = 0 ; i <= 20; i++){
      if(i < 10 || i > 14) {
        countSlidingWindow.recordSuccess();
      }
      else{
        countSlidingWindow.recordFailure();
      }
    }
    Assert.assertEquals(4, countSlidingWindow.getFailureCount());
    Assert.assertEquals(4, countSlidingWindow.failureScore());

    Field field = countSlidingWindow.getClass().getDeclaredField("slidingWindowDeque");
    field.setAccessible(true);
    List<Integer> slidingWindowDeque = (List<Integer>) field.get(countSlidingWindow);
    Assert.assertEquals(10, slidingWindowDeque.size());
  }

}
