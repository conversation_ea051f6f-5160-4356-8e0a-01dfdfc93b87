package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_FIELD_PREFIX;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_FIELD_SUFFIX;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RunWith(MockitoJUnitRunner.class)
public class CustomAttributesUtilsTest {

  private Map<String, Attribute> nammeToAttributeMapping;
  private List<RuleLine.Rule> rulesWithCF;
  private List<RuleLine.Rule> rulesWithoutCF;
  private List<RuleLine.Rule> hybridRules;
  @Mock private Authorization authorization;
  private CustomWorkflowConfig customWorkflowConfig;

  @Before
  @SneakyThrows
  public void setup() {
    authorization = new Authorization();
    authorization.putRealm("12345");
    rulesWithCF = new ArrayList<RuleLine.Rule>();
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    nammeToAttributeMapping =
        customWorkflowConfig
            .getRecordObjForType(RecordType.INVOICE.getRecordType())
            .getAttributes()
            .stream()
            .collect(Collectors.toMap(Attribute::getName, Function.identity()));
    rulesWithCF.add(
        new RuleLine.Rule()
            .conditionalExpression("GT 500")
            .parameterName("302300000000000167501")
            .parameterType(FieldTypeEnum.DOUBLE));

    rulesWithCF.add(
        new RuleLine.Rule()
            .conditionalExpression("CONTAINS abc")
            .parameterName("3023000000000001675012")
            .parameterType(FieldTypeEnum.STRING));

    rulesWithoutCF = new ArrayList<RuleLine.Rule>();
    rulesWithoutCF.add(
        new RuleLine.Rule()
            .conditionalExpression("GT 500")
            .parameterName("TxnAmount")
            .parameterType(FieldTypeEnum.DOUBLE));
    rulesWithoutCF.add(
        new RuleLine.Rule()
            .conditionalExpression("AF 1")
            .parameterName("TxnDueDays")
            .parameterType(FieldTypeEnum.DAYS));

    hybridRules =
        Stream.of(rulesWithCF, rulesWithoutCF)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
  }

  @Test
  public void testRulesForCustomAttributes() {
    List<Attribute> Attributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, rulesWithCF);
    Assert.assertEquals(
        CUSTOM_FIELD_PREFIX + (rulesWithCF.get(0).getParameterName()), Attributes.get(0).getName());
    Assert.assertEquals(
        (rulesWithCF.get(0).getParameterType().toString().toLowerCase()),
        Attributes.get(0).getType());
    Assert.assertEquals((rulesWithCF.get(0).getParameterName()), Attributes.get(0).getId());

    Assert.assertEquals(
        CUSTOM_FIELD_PREFIX + (rulesWithCF.get(1).getParameterName()), Attributes.get(1).getName());
    Assert.assertEquals(
        (rulesWithCF.get(1).getParameterType().toString().toLowerCase()),
        Attributes.get(1).getType());
    Assert.assertEquals((rulesWithCF.get(1).getParameterName()), Attributes.get(1).getId());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRulesForCustomAttributesError() {
    List<RuleLine.Rule> rulesCFWithError = new ArrayList<RuleLine.Rule>();
    rulesCFWithError.add(
        new RuleLine.Rule()
            .conditionalExpression("GTE 100")
            .parameterName("3023000000000001675012"));
    List<Attribute> Attributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, rulesCFWithError);
  }

  @Test
  public void testRulesNonCustomAttributes() {
    List<Attribute> Attributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, rulesWithoutCF);
    Assert.assertEquals(Attributes.size(), 0);
  }

  @Test
  public void testRulesMixedAttributes() {
    List<Attribute> Attributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, hybridRules);
    Assert.assertEquals(
        CUSTOM_FIELD_PREFIX + (rulesWithCF.get(0).getParameterName()), Attributes.get(0).getName());
    Assert.assertEquals(
        (hybridRules.get(0).getParameterType().toString().toLowerCase()),
        Attributes.get(0).getType());
    Assert.assertEquals((hybridRules.get(0).getParameterName()), Attributes.get(0).getId());
  }

  @Test
  public void testgetCustomFieldName() {
    String paramName = CUSTOM_FIELD_PREFIX + "3600000000000155715";
    String expectedParamName = "3600000000000155715";
    paramName = CustomAttributesUtil.getCustomFieldName(paramName);
    Assert.assertEquals(paramName, expectedParamName);
  }

  @Test
  public void testGetGenericCustomFieldAttributeName() {
    FieldTypeEnum paramEnum = FieldTypeEnum.DOUBLE;
    String customAttributeName = CustomAttributesUtil.getGenericCustomFieldAttributeName(paramEnum);
    String expectedName =
        StringUtils.capitalize(paramEnum.toString().toLowerCase() + CUSTOM_FIELD_SUFFIX);
    Assert.assertEquals(customAttributeName, expectedName);
  }

  @Test
  public void testIsCustomFieldRuleLine() {
    Boolean isCustomField =
        CustomAttributesUtil.isCustomFieldRuleLine(nammeToAttributeMapping, rulesWithCF.get(0));
    Assert.assertEquals(isCustomField, true);
    Boolean isDefaultField =
        CustomAttributesUtil.isCustomFieldRuleLine(nammeToAttributeMapping, rulesWithoutCF.get(0));
    Assert.assertEquals(isDefaultField, false);
  }

  @Test
  public void testUpdateProcessVariableMapWithCustomFieldAttributes() {
    Map<String, ProcessVariableDetail> processNameToDetailMap = new HashMap<>();
    List<Attribute> customAttributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, rulesWithCF);
    Map<String, String> typeToNativeMap =
        customWorkflowConfig.getDataTypes().stream()
            .collect(Collectors.toMap(DataType::getType, DataType::getNativeType));
    CustomAttributesUtil.updateProcessVariableMapWithCustomFieldAttributes(
        customAttributes, processNameToDetailMap, typeToNativeMap);

    Assert.assertEquals(processNameToDetailMap.size(), rulesWithCF.size());
  }

  /**
   * When the same custom field was added in the condition blocks at different levels in VD
   * workflow creation failed since it was creating a DMN instance with duplicate errors
   * Fixing it by removing duplicate CF using a set
   */
  @Test
  public void testRulesForCustomAttributesInMultipleConditionBlocks(){
    List<RuleLine.Rule> duplicateCustomFieldRules = new ArrayList<>(hybridRules);
    duplicateCustomFieldRules.add(new RuleLine.Rule()
        .conditionalExpression("CONTAINS def")
        .parameterName("3023000000000001675012")
        .parameterType(FieldTypeEnum.STRING));
    List<Attribute> customAttributes =
        CustomAttributesUtil.getRulesForCustomAttributes(nammeToAttributeMapping, hybridRules);
    Assertions.assertEquals(2,customAttributes.size());


  }
}
