package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.mockMultiConditionDefinitionEntity;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.mockMultiConditionRecurringReminder;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.mockMultiConditionRecurringReminderWithMultipleconditionsInSameNode;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SEND_FOR_REMINDER_ACTION_ID;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.DataType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.EntityActionGroupSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.EntitySet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Handler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ParameterOverrideSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedMultiCondSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedParamSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import com.intuit.v4.workflows.definitions.Operator;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * UTs for the Utility Class containing Helper Methods for Rule Parsing
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class CustomWorkflowUtilTest {

  public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
  public static String ACTION_GROUP_KEY = "schema/testData/configV2ActionGroups.yaml";

  public static String YAML_ACTIONS = "schema/testData/actions.yml";
  public static String YAML_ENTITY_SET_KEY = "schema/testData/entity-set.yml";
  public static String YAML_ATTRIBUTES_KEY = "schema/testData/attributes.yml";
  public static String YAML_DATATYPES = "schema/testData/datatypes.yml";

  public static String YAML_PARAMETERS = "schema/testData/parameters.yml";
  public static String YAML_HANDLERS = "schema/testData/handlers.yml";
  public static String YAML_ENTITY_ACTION_GROUP = "schema/testData/entity-actiongroup-set.yml";
  public static String YAML_PARAMETER_OVERRIDE = "schema/testData/parameter-override-set.yml";

  public static String YAML_PRECANNED_SET = "schema/testData/precanned-set.yml";
  public static String YAML_PRECANNED_PARAM_SET = "schema/testData/precanned-param-set.yml";

  public static String YAML_PRECANNED_MULTI_COND_SET =
      "schema/testData/precanned-multicodn-set.yml";

  public static String YAML_KEY = "templateConfig";

  private CustomWorkflowConfig customWorkflowConfig;
  private String REALM_ID = "realmId";
  private String LOCAL_ID = "localId";
  private final TranslationService translationService = TestHelper.initTranslationService();
  private Definition definition;
  DmnModelInstance dmnModelInstance;
  DmnModelInstance invalidDmnModelInstance;
  private DefinitionInstance definitionInstance;
  BpmnModelInstance multiConditionBpmnModelInstance;

  private final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");

  private final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  private static final String CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow_withNoDecisionTable.dmn");

  private CustomConfigV2 customConfigV2;

  @Before
  @SneakyThrows
  public void setup() {
    ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory());
    Map<String, List<ActionGroup>> customConfigV2ActionGroup =
        objectMapper.readValue(
            TestHelper.readResourceAsString(ACTION_GROUP_KEY),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<ActionGroup> actionGroups =
        objectMapper.convertValue(
            customConfigV2ActionGroup.get("custom-config-v2-action-groups"),
            new TypeReference<List<ActionGroup>>() {});

    Map<String, List<Action>> actionMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_ACTIONS),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<Action> actions =
        objectMapper.convertValue(
            actionMap.get("custom-config-v2-actions"), new TypeReference<List<Action>>() {});

    Map<String, List<EntitySet>> entitySetMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_ENTITY_SET_KEY),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<EntitySet> entitySet =
        objectMapper.convertValue(
            entitySetMap.get("custom-config-v2-entity-set"),
            new TypeReference<List<EntitySet>>() {});
    Map<String, List<Attribute>> attributesMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_ATTRIBUTES_KEY),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<Attribute> attributes =
        objectMapper.convertValue(
            attributesMap.get("custom-config-v2-attributes"),
            new TypeReference<List<Attribute>>() {});

    Map<String, List<DataType>> datatypesMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_DATATYPES),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<DataType> dataTypes =
        objectMapper.convertValue(
            datatypesMap.get("custom-config-v2-datatypes"), new TypeReference<List<DataType>>() {});

    Map<String, List<Parameter>> paramsMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_PARAMETERS),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<Parameter> parameters =
        objectMapper.convertValue(
            paramsMap.get("custom-config-v2-parameters"), new TypeReference<List<Parameter>>() {});

    Map<String, List<Handler>> handlerMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_HANDLERS),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<Handler> handlers =
        objectMapper.convertValue(
            handlerMap.get("custom-config-v2-handlers"), new TypeReference<List<Handler>>() {});

    Map<String, List<EntityActionGroupSet>> entityActionMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_ENTITY_ACTION_GROUP),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<EntityActionGroupSet> entityActionGroupSets =
        objectMapper.convertValue(
            entityActionMap.get("custom-config-v2-entity-actiongroup-set"),
            new TypeReference<List<EntityActionGroupSet>>() {});

    Map<String, List<ParameterOverrideSet>> paramOverrideMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_PARAMETER_OVERRIDE),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<ParameterOverrideSet> parameterOverrideSets =
        objectMapper.convertValue(
            paramOverrideMap.get("custom-config-v2-parameter-override-set"),
            new TypeReference<List<ParameterOverrideSet>>() {});

    Map<String, List<PreCannedSet>> preCannedSetMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_PRECANNED_SET),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<PreCannedSet> preCannedSet =
        objectMapper.convertValue(
            preCannedSetMap.get("custom-config-v2-precanned-set"),
            new TypeReference<List<PreCannedSet>>() {});

    Map<String, List<PreCannedParamSet>> preCannedParamSetMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_PRECANNED_PARAM_SET),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<PreCannedParamSet> preCannedParamSet =
        objectMapper.convertValue(
            preCannedParamSetMap.get("custom-config-v2-precanned-param-set"),
            new TypeReference<List<PreCannedParamSet>>() {});

    Map<String, List<PreCannedMultiCondSet>> preCannedMultiCondnSetMap =
        objectMapper.readValue(
            TestHelper.readResourceAsString(YAML_PRECANNED_MULTI_COND_SET),
            objectMapper.getTypeFactory().constructMapType(Map.class, String.class, List.class));

    List<PreCannedMultiCondSet> preCannedMultiCondnSet =
        objectMapper.convertValue(
            preCannedMultiCondnSetMap.get("custom-config-v2-precanned-multicond-set"),
            new TypeReference<List<PreCannedMultiCondSet>>() {});

    customConfigV2 = new CustomConfigV2();
    customConfigV2.setActionGroups(actionGroups);
    customConfigV2.setActions(actions);
    customConfigV2.setAttributes(attributes);
    customConfigV2.setDataTypes(dataTypes);
    customConfigV2.setEntitySet(entitySet);
    customConfigV2.setHandlers(handlers);
    customConfigV2.setParameters(parameters);
    customConfigV2.setEntityActionGroupSet(entityActionGroupSets);
    customConfigV2.setParameterOverrideSet(parameterOverrideSets);
    customConfigV2.setPreCannedSet(preCannedSet);
    customConfigV2.setPreCannedParamSet(preCannedParamSet);
    customConfigV2.setPreCannedMultiCondSets(preCannedMultiCondnSet);
    OldCustomWorkflowConfig oldCustomWorkflowConfigV2 =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);

    CustomWorkflowConfig customWorkflowConfigV2 = new CustomWorkflowConfig();
    customWorkflowConfigV2.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfigV2, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfigV2.setOldConfig(oldCustomWorkflowConfigV2);
    customWorkflowConfigV2.getCustomWorkflowConfigFactory().afterPropertiesSet();
    this.customWorkflowConfig = customWorkflowConfigV2;

    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    this.customWorkflowConfig = customWorkflowConfig;
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    MockitoAnnotations.initMocks(this);
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    invalidDmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(
                CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML, Charset.defaultCharset()));

    definitionInstance =
        new DefinitionInstance(
            definition, null, Collections.singletonList(dmnModelInstance), new TemplateDetails());
  }

  @Test
  public void testgetOnDemandConfiguredActions(){
    assertEquals(List.of("createTask","sendCompanyEmail"), CustomWorkflowUtil.getOnDemandConfiguredActions(customWorkflowConfig,"bill","approval"));
    assertEquals(0, CustomWorkflowUtil.getOnDemandConfiguredActions(customWorkflowConfig,"estimate","approval").size());

  }

  @Test
  public void TestCreateRecordSuccessful() {
    Map<String, List<EntityActionGroupSet>> entiyActionGroupMap =
        CustomWorkflowConfigUtil.getActionGroups(customConfigV2);
    customConfigV2
        .getRecords()
        .add(
            CustomWorkflowConfigUtil.createEntityRecord(
                customConfigV2.getEntitySet().get(0), customConfigV2, entiyActionGroupMap));
    Assert.assertNotNull(customConfigV2.getRecords());

    Assert.assertEquals(1, customConfigV2.getRecords().size());
    Assert.assertEquals("invoice", customConfigV2.getRecords().get(0).getId());
    Assert.assertEquals(23, customConfigV2.getRecords().get(0).getAttributes().size());
    Assert.assertEquals(5, customConfigV2.getRecords().get(0).getActionGroups().size());
    Assert.assertEquals(9, customConfigV2.getRecords().get(0).getHelpVariables().size());
    Assert.assertEquals(1, customConfigV2.getRecords().get(0).getDefaultAttributes().size());

    // checking the action groups

    CustomWorkflowConfigUtil.populateRecordConfig(customConfigV2);
    Assert.assertEquals(
        "reminder", customConfigV2.getRecords().get(0).getActionGroups().get(0).getId());
    Assert.assertEquals(
        4, customConfigV2.getRecords().get(0).getActionGroups().get(0).getActions().size());
    Assert.assertEquals(
        "createTask",
        customConfigV2.getRecords().get(0).getActionGroups().get(0).getActions().get(0).getId());
    Assert.assertEquals(
        "wasCreateTask",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(0)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "sendExternalEmail",
        customConfigV2.getRecords().get(0).getActionGroups().get(0).getActions().get(1).getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(1)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(2)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(3)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "sendCompanyEmail",
        customConfigV2.getRecords().get(0).getActionGroups().get(0).getActions().get(2).getId());
    Assert.assertEquals(
        "sendPushNotification",
        customConfigV2.getRecords().get(0).getActionGroups().get(0).getActions().get(3).getId());

    Assert.assertEquals(
        "approval", customConfigV2.getRecords().get(0).getActionGroups().get(1).getId());
    Assert.assertEquals(
        4, customConfigV2.getRecords().get(0).getActionGroups().get(1).getActions().size());

    // checking handlers
    Assert.assertEquals(
        "Assignee",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(0)
            .getParameters()
            .get(0)
            .getName());

    Assert.assertEquals(
        "CloseTask",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(0)
            .getParameters()
            .get(1)
            .getName());

    Assert.assertEquals(
        "TaskName",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(1)
            .getActions()
            .get(0)
            .getParameters()
            .get(2)
            .getName());

    // checking helpVariables

    assertThat(
        customConfigV2
            .getRecords()
            .get(0)
            .getHelpVariables()
            .get(0)
            .contains("Company Name:CompanyName:string"));
    assertThat(
        customConfigV2
            .getRecords()
            .get(0)
            .getHelpVariables()
            .get(1)
            .contains("Company Email:CompanyEmail:string"));
    assertThat(
        customConfigV2
            .getRecords()
            .get(0)
            .getHelpVariables()
            .get(2)
            .contains("Customer Name:CustomerName:string"));
  }

  @Test
  public void TestCreatePrecannedSuccessful() {
    Map<String, List<EntityActionGroupSet>> entiyActionGroupMap =
        CustomWorkflowConfigUtil.getActionGroups(customConfigV2);
    customConfigV2
        .getRecords()
        .add(
            CustomWorkflowConfigUtil.createEntityRecord(
                customConfigV2.getEntitySet().get(0), customConfigV2, entiyActionGroupMap));
    Assert.assertNotNull(customConfigV2.getRecords());
    CustomWorkflowConfigUtil.createTemplateConfig(customConfigV2);
    CustomWorkflowConfigUtil.populateRecordConfig(customConfigV2);


    Assert.assertEquals(2, customConfigV2.getConfigTemplates().size());

    Assert.assertEquals("invoice", customConfigV2.getConfigTemplates().get(0).getRecord());
    Assert.assertEquals(2, customConfigV2.getConfigTemplates().get(0).getAttributes().size());
    Assert.assertEquals(1, customConfigV2.getConfigTemplates().get(0).getActionGroups().size());

    CustomWorkflowConfigUtil.populateTemplateConfig(customConfigV2);
    Assert.assertEquals(
        2, customConfigV2.getConfigTemplates().get(0).getDefaultAttributes().size());
    Assert.assertEquals(2, customConfigV2.getConfigTemplates().get(0).getLabels().size());

    // checking the action groups

    Assert.assertEquals(
        "reminder", customConfigV2.getConfigTemplates().get(0).getActionGroups().get(0).getId());
    Assert.assertEquals(
        4, customConfigV2.getConfigTemplates().get(0).getActionGroups().get(0).getActions().size());
    Assert.assertEquals(
        "createTask",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(0)
            .getId());
    Assert.assertEquals(
        "wasCreateTask",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(0)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "sendExternalEmail",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(1)
            .getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getRecords()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(1)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(2)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "wasSendNotification",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(3)
            .getHandler()
            .getId());
    Assert.assertEquals(
        "sendCompanyEmail",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(2)
            .getId());
    Assert.assertEquals(
        "sendPushNotification",
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getActionGroups()
            .get(0)
            .getActions()
            .get(3)
            .getId());
    // checking helpVariables

    assertThat(
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getHelpVariables()
            .get(0)
            .contains("Company Name:CompanyName:string"));
    assertThat(
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getHelpVariables()
            .get(1)
            .contains("Company Email:CompanyEmail:string"));
    assertThat(
        customConfigV2
            .getConfigTemplates()
            .get(0)
            .getHelpVariables()
            .get(2)
            .contains("Customer Name:CustomerName:string"));
  }

  @Test
  public void testMultiCondition() {
    Map<String, List<EntityActionGroupSet>> entiyActionGroupMap =
        CustomWorkflowConfigUtil.getActionGroups(customConfigV2);

    customWorkflowConfig
        .getRecords()
        .add(
            CustomWorkflowConfigUtil.createEntityRecord(
                customConfigV2.getEntitySet().get(0), customConfigV2, entiyActionGroupMap));
    Assert.assertNotNull(customConfigV2.getRecords());
    CustomWorkflowConfigUtil.createTemplateConfig(customConfigV2);

    Assert.assertEquals(2, customConfigV2.getConfigTemplates().size());

    Assert.assertEquals("Invoice", customConfigV2.getConfigTemplates().get(1).getRecord());
    Assert.assertEquals(
        "invoiceapproval-multicondition", customConfigV2.getConfigTemplates().get(1).getId());
    Assert.assertEquals(7, customConfigV2.getConfigTemplates().get(1).getSteps().size());

    Assert.assertEquals(
        Integer.valueOf("1"),
        customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getStepId());
    Assert.assertEquals(
        "condition", customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getStepType());
    Assert.assertEquals(
        2, customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getNexts().size());

    Assert.assertEquals(
        "2",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getNexts().get(0).getStepId());
    Assert.assertEquals(
        "yes",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getNexts().get(0).getLabel());

    Assert.assertEquals(
        "3",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getNexts().get(1).getStepId());
    Assert.assertEquals(
        "no",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getNexts().get(1).getLabel());

    Assert.assertEquals(
        1, customConfigV2.getConfigTemplates().get(1).getSteps().get(0).getAttributes().size());
    Assert.assertEquals(
        "txnAmount",
        customConfigV2
            .getConfigTemplates()
            .get(1)
            .getSteps()
            .get(0)
            .getAttributes()
            .get(0)
            .getId());

    Assert.assertEquals(
        Integer.valueOf("2"),
        customConfigV2.getConfigTemplates().get(1).getSteps().get(1).getStepId());
    Assert.assertEquals(
        "sendForApproval",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(1).getAction());
    Assert.assertEquals(
        "action", customConfigV2.getConfigTemplates().get(1).getSteps().get(1).getStepType());

    Assert.assertEquals(
        Integer.valueOf("3"),
        customConfigV2.getConfigTemplates().get(1).getSteps().get(2).getStepId());

    Assert.assertEquals(
        "4",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(2).getNexts().get(0).getStepId());
    Assert.assertEquals(
        "yes",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(2).getNexts().get(0).getLabel());

    Assert.assertEquals(
        "5",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(2).getNexts().get(1).getStepId());
    Assert.assertEquals(
        "no",
        customConfigV2.getConfigTemplates().get(1).getSteps().get(2).getNexts().get(1).getLabel());
  }

  @Test
  public void testPrepareOperator() {
    Operator val =
        CustomWorkflowUtil.transformOperator(
            customWorkflowConfig.getDataTypes().get(0).getOperators().get(0));
    Assert.assertEquals("is less than", translationService.getString(val.getDescription(), "en_US"));
    Assert.assertEquals("est inférieur à", translationService.getString(val.getDescription(), "fr_CA"));
    Assert.assertEquals("LT", val.getSymbol());
    Assert.assertEquals("LT", val.getTypeId());

    val =
        CustomWorkflowUtil.transformOperator(
            customWorkflowConfig.getDataTypes().get(0).getOperators().get(1));
    Assert.assertEquals(
        "is greater than", translationService.getString(val.getDescription(), "en_US"));
    Assert.assertEquals(
        "est supérieur à", translationService.getString(val.getDescription(), "fr_CA"));
    Assert.assertEquals("GT", val.getSymbol());
    Assert.assertEquals("GT", val.getTypeId());
  }

  @Test
  public void testGetAttributesDefaultValue() {
    Assert.assertEquals(
        "DATE_TODAY",
        CustomWorkflowUtil.getAttributesDefaultValue(customWorkflowConfig, "createDays"));
  }

  @Test
  public void testGetAttributesDefaultOperator() {
    Assert.assertEquals(
        "CONTAINS",
        CustomWorkflowUtil.getAttributesDefaultOperator(customWorkflowConfig, "customer"));
  }

  @Test
  public void testGetAttributesGlobalDisplayName() {
    Assert.assertEquals(
        "Customer",
        CustomWorkflowUtil.getAttributesGlobalDisplayName(customWorkflowConfig, "customer"));
  }

  @Test
  public void testIsCustomWorkflowSystemDefinition_True() {
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);

    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("customApproval");
    when(definitionDetails.getRecordType()).thenReturn(null);
    when(definitionDetails.getOwnerId()).thenReturn(Long.MIN_VALUE);

    assertTrue(CustomWorkflowUtil.isCustomWorkflowSystemDefinition(null,definitionDetails));

    // sendForApproval as process details
    ProcessDetails childProcessDetails = mock(ProcessDetails.class);
    ProcessDetails parentProcessDetails = mock(ProcessDetails.class);

    when(childProcessDetails.getParentProcessDetails()).thenReturn(parentProcessDetails);
    when(parentProcessDetails.getDefinitionDetails()).thenReturn(definitionDetails);
    assertTrue(CustomWorkflowUtil.isCustomWorkflowSystemDefinition(childProcessDetails,definitionDetails));

    // customApproval as process details
    assertTrue(CustomWorkflowUtil.isCustomWorkflowSystemDefinition(parentProcessDetails,definitionDetails));

    // child as system definition and parent as single definition
    when(definitionDetails.getRecordType()).thenReturn(RecordType.BUDGET);
    when(definitionDetails.getOwnerId()).thenReturn(1234L);
    assertFalse(CustomWorkflowUtil.isCustomWorkflowSystemDefinition(childProcessDetails,definitionDetails));
  }

  @Test
  public void testIfCustomWorkflow() {
    Assert.assertTrue(CustomWorkflowUtil.isCustomWorkflow(null));
    Assert.assertTrue(CustomWorkflowUtil.isCustomWorkflow(new Template()));
    Assert.assertFalse(
        CustomWorkflowUtil.isCustomWorkflow(
            new Template().id(GlobalId.create(REALM_ID, LOCAL_ID))));
    Assert.assertTrue(
        CustomWorkflowUtil.isCustomWorkflow(
            new Template().id(GlobalId.create(REALM_ID, LOCAL_ID)).category("CUSTOM")));
    Assert.assertTrue(
        CustomWorkflowUtil.isCustomWorkflow(
            new Template()
                .id(GlobalId.create(REALM_ID, LOCAL_ID))
                .category("CUSTOM")
                .name("customReminder")));
    Assert.assertFalse(
        CustomWorkflowUtil.isCustomWorkflow(
            new Template().id(GlobalId.create(REALM_ID, LOCAL_ID)).name("nonbyotemplate")));
  }

  @Test
  public void testIfCustomApprovalWorkflow() {
    Assert.assertFalse(CustomWorkflowUtil.isCustomApprovalWorkflow(null));
    Assert.assertFalse(
        CustomWorkflowUtil.isCustomApprovalWorkflow(
            new Template()
                .id(GlobalId.create(REALM_ID, LOCAL_ID))
                .category("custom")
                .name("customReminder")));
    Assert.assertTrue(
        CustomWorkflowUtil.isCustomApprovalWorkflow(
            new Template()
                .id(GlobalId.create(REALM_ID, LOCAL_ID))
                .category("CUSTOM")
                .name("customApproval")));
  }

  @Test
  public void testGetParameterDetailsFromActionParameter() {

    Parameter parameter = new Parameter();
    parameter.setId("1");
    parameter.setFieldType("FieldType");
    parameter.setHandlerFieldName("HandlerName");
    parameter.setName("ParameterName");

    try {
      CustomWorkflowUtil.getParameterDetailsFromActionParameter(parameter);
    } catch (Exception e) {
      Assert.fail("The Above method should not fail");
    }
  }

  @Test
  public void testtTransformActionToInputParameter() {

    Parameter parameter = new Parameter();
    parameter.setId("1");
    parameter.setHandlerFieldName("HandlerName");
    parameter.setName("ParameterName");
    try {
      CustomWorkflowUtil.transformActionToInputParameter(parameter);
    } catch (Exception e) {
      Assert.fail("The Above method should not fail");
    }
  }

  @Test
  public void getRulesFromDefinitionTestWithNegationForMCR(){
    definition = mockMultiConditionRecurringReminder();
    List<WorkflowStep> workflowsteps = definition.getWorkflowSteps();
    Map<String, WorkflowStep> workflowStepMap = workflowsteps.stream().collect(
        Collectors.toMap(workflowStep -> String.valueOf(workflowStep.getId()), Function.identity()));
    String currentId = definition.getWorkflowSteps(workflowsteps.size()-1).getId().toString();
    List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition, workflowStepMap,currentId);
    Assert.assertTrue(rules.get(0).getConditionalExpression().contains("NOT"));
    Assert.assertEquals(2,rules.size());
  }

  @Test
  public void getRulesFromDefinitionTestWithMultipleRulesInSingleNodeForMCR(){
    definition = mockMultiConditionRecurringReminderWithMultipleconditionsInSameNode();
    List<WorkflowStep> workflowsteps = definition.getWorkflowSteps();
    Map<String, WorkflowStep> workflowStepMap = workflowsteps.stream().collect(
        Collectors.toMap(workflowStep -> String.valueOf(workflowStep.getId()), Function.identity()));

    String currentId = definition.getWorkflowSteps(workflowsteps.size()-1).getId().toString();
    List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition, workflowStepMap,currentId);
    Assert.assertEquals(1,rules.size());

    currentId = definition.getWorkflowSteps(workflowsteps.size()-2).getId().toString();
    rules = CustomWorkflowUtil.getRulesFromDefinition(definition, workflowStepMap,currentId);
    Assert.assertEquals(3,rules.size());
  }

  @Test
  public void testgetRulesFromDefinitionWithEmptyCondition() {
    definition = TestHelper.mockDefinitionEntityWithNoCondition();
    List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition);
    Assert.assertTrue(rules.isEmpty());
  }

  @Test
  public void testgetRulesFromDefinitionWithOutEmptyCondition() {
    definition = TestHelper.mockDefinitionEntity();
    List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition);
    Assert.assertFalse(rules.isEmpty());
  }

  @Test
  public void testgetRulesOfCurrentStep() {
    definition = TestHelper.mockDefinitionEntity();
    List<RuleLine.Rule> rules =
        CustomWorkflowUtil.getRulesOfCurrentStep(definition.getWorkflowSteps(0));
    Assert.assertFalse(rules.isEmpty());
    Assert.assertTrue(rules.get(0).getParameterName().equals("amountEvaluation"));
  }

  @Test
  public void testgetRulesOfCurrentStepForMultiConditionDefinition() {
    definition = mockMultiConditionDefinitionEntity();
    List<RuleLine.Rule> rules =
        CustomWorkflowUtil.getRulesOfCurrentStep(definition.getWorkflowSteps(0));
    Assert.assertFalse(rules.isEmpty());
    Assert.assertTrue(rules.get(0).getParameterName().equals("TxnAmount"));
  }

  @Test
  public void testgetTriggerFromDefinition() {
    definition = TestHelper.mockDefinitionEntity();
    Map<String, HandlerDetails.ParameterDetails> triggers =
        CustomWorkflowUtil.getTriggerFromDefinition(definition);
    Assert.assertFalse(triggers.isEmpty());
  }

  @Test
  public void testgetTriggerFromDefinitionWithEmptyTrigger() {
    definition = TestHelper.mockDefinitionEntity();
    definition.getWorkflowSteps().get(0).setTrigger(null);
    Map<String, HandlerDetails.ParameterDetails> triggers =
        CustomWorkflowUtil.getTriggerFromDefinition(definition);
    Assert.assertTrue(triggers.isEmpty());
  }

  @Test
  public void testgetTriggerFromDefinitionWithEmptyTriggerParameters() {
    definition = TestHelper.mockDefinitionEntity();
    definition.getWorkflowSteps().get(0).getTrigger().setParameters(null);
    Map<String, HandlerDetails.ParameterDetails> triggers =
        CustomWorkflowUtil.getTriggerFromDefinition(definition);
    Assert.assertTrue(triggers.isEmpty());
  }

  @Test
  public void testisBoolean() {
    Assert.assertTrue(CustomWorkflowUtil.isBoolean("true"));
    Assert.assertTrue(CustomWorkflowUtil.isBoolean("false"));
    Assert.assertFalse(CustomWorkflowUtil.isBoolean("random"));
  }

  @Test
  public void testGenerateDefinitionInstanceForBpmnProcessing() {
    TemplateDetails templateDetails = TestHelper.mockTemplateDetailsObject();
    DefinitionInstance definitionInstance =
        CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
            multiConditionBpmnModelInstance, Arrays.asList(dmnModelInstance), templateDetails);
    Assert.assertNotNull(definitionInstance);
    Assert.assertNotNull(definitionInstance.getDefinition());
    Assert.assertNotNull(definitionInstance.getBpmnModelInstance());
    Assert.assertNotNull(definitionInstance.getDmnModelInstanceList());
    Assert.assertNotNull(definitionInstance.getTemplateDetails());
  }

  @Test
  public void testGetDefaultAttributesForCustom() {
    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType());
    List<Attribute> recordAttributes = record.getAttributes();
    Map<String, Attribute> recordAttributeMap =
        recordAttributes.stream().collect(Collectors.toMap(Attribute::getId, Function.identity()));
    ActionGroup actiongroup =
        record.getActionGroups().stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase("approval"))
            .findFirst()
            .get();
    Set<String> result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, actiongroup, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());
  }

  @Test
  public void testGetDefaultAttributesForPrecanned() {
    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.getRecordType());
    List<Attribute> recordAttributes = record.getAttributes();
    Map<String, Attribute> recordAttributeMap =
        recordAttributes.stream().collect(Collectors.toMap(Attribute::getId, Function.identity()));
    ActionGroup actiongroup =
        record.getActionGroups().stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase("approval"))
            .findFirst()
            .get();
    Set<String> result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, actiongroup, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    Steps configStep = new Steps();
    configStep.setStepId(1);
    configStep.setStepType(StepTypeEnum.CONDITION.value());
    Next yesPath = new Next();
    Next noPath = new Next();
    yesPath.setType(NextTypeEnum.ACTION.value());
    yesPath.setStepId("2");
    yesPath.setLabel(NextLabelEnum.YES.value());
    noPath.setType(StepTypeEnum.CONDITION.value());
    noPath.setStepId("3");
    noPath.setLabel(NextLabelEnum.NO.value());
    List<Next> nexts = new ArrayList<>();
    nexts.add(yesPath);
    nexts.add(noPath);
    configStep.setNexts(nexts);
    Attribute txnAttr = new Attribute();
    txnAttr.setName("TxnAmount");
    txnAttr.setId("txnAmount");
    txnAttr.setType("DOUBLE");
    txnAttr.setDefaultValue("500");
    txnAttr.setDefaultOperator("GTE");
    configStep.setAttributes(Arrays.asList(txnAttr));

    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, null, configStep);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    ConfigTemplate configTemplate = new ConfigTemplate();

    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, configTemplate, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    Set<String> attributeSet = new HashSet<>();
    attributeSet.add(configStep.getAttributes().get(0).getName());
    configTemplate.setDefaultAttributes(attributeSet);

    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, configTemplate, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    record.setDefaultAttributes(new HashSet<>());
    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());

    record.setId("invalid");
    result =
        CustomWorkflowUtil.getDefaultAttributes(
            recordAttributeMap, customWorkflowConfig, record, null, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.size());
  }

  @Test
  public void testPopulateRecordConfigWithActionIdMapperActionOverride(){
    OldCustomWorkflowConfig oldConfig = customWorkflowConfig.getOldConfig();
    // Pre checks
    Record invoice = oldConfig.getRecords().get(0);
//    All the overriden actions for the given record
    List<Action> invoiceReminderActions = invoice.getActionGroups().get(0).getActions();
    List<Action> invoiceApprovalActions = invoice.getActionGroups().get(1).getActions();
    Assert.assertNull(invoiceApprovalActions.stream().filter(action -> "sendForApproval".equalsIgnoreCase(action.getId())).findFirst().orElse(null));
    Assert.assertEquals(4,invoiceReminderActions.size());
    Action sendForReminderAction = invoiceReminderActions.stream().filter(action -> SEND_FOR_REMINDER_ACTION_ID.equals(action.getId())).findFirst().orElse(null);
    Assert.assertNotNull(sendForReminderAction);
    Assert.assertEquals(0,sendForReminderAction.getSubActions().size());

    CustomWorkflowConfigUtil.populateRecordConfig(oldConfig);

    // post population
    invoice = oldConfig.getRecords().get(0);
    invoiceReminderActions = invoice.getActionGroups().get(0).getActions();
    Assert.assertEquals(5,invoiceReminderActions.size());
    sendForReminderAction = invoiceReminderActions.stream().filter(action -> SEND_FOR_REMINDER_ACTION_ID.equals(action.getId())).findFirst().orElse(null);
    Assert.assertEquals(4,sendForReminderAction.getSubActions().size());
    Parameter filterCloseTaskCondition = sendForReminderAction.getParameters().stream().filter(parameter -> FILTER_CLOSE_TASK_CONDITIONS.equals(parameter.getName())).findFirst().orElse(null);
    Assert.assertNotNull(filterCloseTaskCondition);
    Assert.assertNotNull(invoice.getActionGroups().get(0).getActionIdMapper());
    Assert.assertEquals(SEND_FOR_REMINDER_ACTION_ID,invoice.getActionGroups().get(0).getActionIdMapper().getActionId());
    Assert.assertEquals(Arrays.asList("txn_sent","txn_paid"),filterCloseTaskCondition.getFieldValues());

    invoiceApprovalActions = invoice.getActionGroups().get(1).getActions();
    Assert.assertNotNull(invoiceApprovalActions.stream().filter(action -> "sendForApproval".equalsIgnoreCase(action.getId())).findFirst().orElse(null));

  }

  @Test
  public void testGetDecisionTable_Success() {
    Assert.assertNotNull(CustomWorkflowUtil.getDecisionTable(dmnModelInstance));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetDecisionTable_Failure() {
    CustomWorkflowUtil.getDecisionTable(invalidDmnModelInstance);
  }

  @Test
  public void testSetOutputDmnColumnType() {
    CustomWorkflowUtil.setOutputDmnColumnType(dmnModelInstance);
    DecisionTable decisionTable = CustomWorkflowUtil.getDecisionTable(dmnModelInstance);
    Assert.assertEquals(
        decisionTable.getOutputs().stream().findFirst().get().getTypeRef(), "String");
  }

  @Test
  public void testGetDmnModelInstanceSuccess() {
    String dmnElementId = "decisionElement";
    CustomWorkflowUtil.getDmnModelInstance(definitionInstance, dmnElementId);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetDmnModelInstanceFailure() {
    String dmnElementId = "invalidId";
    CustomWorkflowUtil.getDmnModelInstance(definitionInstance, dmnElementId);
  }

  @Test
  public void testIsCustomReminderWorkflow_true() {
    Definition definition1 = TestHelper.mockSingleStepRecurringReminderDefinitionEntity();
    Assert.assertTrue(CustomWorkflowUtil.isCustomReminderWorkflow(definition1));
  }

  @Test
  public void testIsCustomReminderWorkflow_false() {
    Definition definition1 = mockMultiConditionDefinitionEntity();
    Assert.assertFalse(CustomWorkflowUtil.isCustomReminderWorkflow(definition1));
  }
}
