package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;

/**
 * 
 * <AUTHOR>
 *
 */
public class DefinitionDeploymentUtilTest {

	/**
	 * Deployment Name for System and Single Definition.
	 */
	@Test
	public void getDeploymentName_templateDetails() {
		String deploymentName = DefinitionDeploymentUtil.getDeploymentName(TemplateDetails.builder()
				.templateName("tempName1").version(1).build());
		Assert.assertEquals(deploymentName, "tempName1_1");
	}
	
	/**
	 * Deployment Name for User Definition update.
	 */
	@Test
	public void getDeploymentName_definitionDetails_version() {
		Authorization authz = Mockito.mock(Authorization.class);
		Mockito.when(authz.getRealm()).thenReturn("11112223");
		WASContext.setAuthContext(authz);
		
		Definition definiton = new Definition();
		definiton.setDisplayName("Definition Name");
		definiton.setRecordType("invoice");
		definiton.setName("customReminder");
		DefinitionInstance definitionInstance = new DefinitionInstance(definiton, null, null, null);
		definitionInstance.setDefinitionDetails(DefinitionDetails.builder()
				.definitionName("definitionName").version(2).build());
		
		String deploymentName = DefinitionDeploymentUtil.getDeploymentName(definitionInstance);
		Assert.assertEquals(deploymentName, "customReminder_invoice_11112223_3");
	}
	
	/**
	 * Deployment Name for User Definition create.
	 */
	@Test
	public void getDeploymentName_Definition() {
		Authorization authz = Mockito.mock(Authorization.class);
		Mockito.when(authz.getRealm()).thenReturn("11112223");
		WASContext.setAuthContext(authz);
		
		Definition definiton = new Definition();
		definiton.setDisplayName("Definition Name");
		definiton.setRecordType("invoice");
		definiton.setName("customReminder");
		DefinitionInstance definitionInstance = new DefinitionInstance(definiton, null, null, null);
		
		String deploymentName = DefinitionDeploymentUtil.getDeploymentName(definitionInstance);
		Assert.assertEquals(deploymentName, "customReminder_invoice_11112223_1");
	}
	
}
