package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DECISION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DECISION_TABLE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INPUT;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.HitPolicy;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Definitions;
import org.camunda.bpm.model.dmn.instance.Input;
import org.camunda.bpm.model.dmn.instance.Output;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;


/** <AUTHOR> */
public class DmnProcessorUtilTest {

  private static final DmnModelInstance MODEL_INSTANCE = Dmn.createEmptyModel();
  private static final String DEFINITION_NAME = "byoReminder";
  private static final String DEFINITION_DISPLAY_NAME = "byoReminder Display Name";
  private static final String LABEL = "LABEL";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";
  private static final String INVOICE_APPROVAL_DMN_V1_3 = "dmn/decision_invoiceapprovalv1.3.dmn";
  
  @Test
  public void testPrepareDefinitions() {
    Assert.assertNotNull(MODEL_INSTANCE);
    Definitions definitions = DmnProcessorUtil.prepareDefinitionsMetaData(MODEL_INSTANCE);
    Assert.assertNotNull(definitions);
  }

  @Test
  public void testCreateElement() {
    Decision decision =
        DmnProcessorUtil.createElement(
            MODEL_INSTANCE,
            DmnProcessorUtil.prepareDefinitionsMetaData(MODEL_INSTANCE),
            MessageFormat.format("{0}_{1}", DECISION, DEFINITION_NAME),
            DEFINITION_DISPLAY_NAME,
            Decision.class);

    Assert.assertNotNull(decision);
    Assert.assertEquals(DEFINITION_DISPLAY_NAME, decision.getName());
    Assert.assertEquals(
        MessageFormat.format("{0}_{1}", DECISION, DEFINITION_NAME), decision.getId());
  }

  @Test(expected = RuntimeException.class)
  public void testCreateElementWithoutNameException() {
    DecisionTable decisionTable =
        DmnProcessorUtil.createElement(
            MODEL_INSTANCE,
            DmnProcessorUtil.prepareDefinitionsMetaData(MODEL_INSTANCE),
            DECISION_TABLE,
            null,
            DecisionTable.class);

    Assert.assertNotNull(decisionTable);
    Assert.assertNotNull(decisionTable.getId());
    Assert.assertEquals(DECISION_TABLE, decisionTable.getId());
  }

  @Test
  public void testCreateElementWithoutName() {
    DecisionTable decisionTable =
        DmnProcessorUtil.createElement(
            MODEL_INSTANCE,
            DmnProcessorUtil.createElement(
                MODEL_INSTANCE,
                DmnProcessorUtil.prepareDefinitionsMetaData(MODEL_INSTANCE),
                MessageFormat.format("{0}_{1}", DECISION, DEFINITION_NAME),
                DEFINITION_DISPLAY_NAME,
                Decision.class),
            DECISION_TABLE,
            null,
            DecisionTable.class);

    Assert.assertNotNull(decisionTable);
    Assert.assertNotNull(decisionTable.getId());
    Assert.assertEquals(DECISION_TABLE, decisionTable.getId());
  }

  @Test
  public void testCreateWithLabel() {
    Input input =
        DmnProcessorUtil.createElementWithLabel(
            MODEL_INSTANCE,
            DmnProcessorUtil.createElement(
                MODEL_INSTANCE,
                DmnProcessorUtil.createElement(
                    MODEL_INSTANCE,
                    DmnProcessorUtil.prepareDefinitionsMetaData(MODEL_INSTANCE),
                    MessageFormat.format("{0}_{1}", DECISION, DEFINITION_NAME),
                    DEFINITION_DISPLAY_NAME,
                    Decision.class),
                DECISION_TABLE,
                null,
                DecisionTable.class),
            MessageFormat.format("{0}{1}", INPUT, 1),
            LABEL,
            Input.class);
    Assert.assertNotNull(input);
    Assert.assertNotNull(input.getId());
    Assert.assertEquals(LABEL, input.getLabel());
  }
  
  @Test
  public void validateDMN_success() throws ParserConfigurationException {
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      dbf.setFeature(Constants.EXTERNAL_GENERAL_ENTITIES, false);
      dbf.setFeature(Constants.EXTERNAL_PARAMETER_ENTITIES, false);
      DmnProcessorUtil.validateDMN(DmnProcessorUtilTest.class.getClassLoader()
			  .getResourceAsStream(INVOICE_APPROVAL_DMN_V1_3));
  }
  
  @Test(expected = IOException.class)
  public void validateDMN_success3() throws Exception{
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      dbf.setFeature(Constants.EXTERNAL_GENERAL_ENTITIES, false);
      dbf.setFeature(Constants.EXTERNAL_PARAMETER_ENTITIES, false);
      InputStream is = DmnProcessorUtilTest.class.getClassLoader()
	  .getResourceAsStream(INVOICE_APPROVAL_DMN_V1_3);
	  DmnProcessorUtil.validateDMN(is);
	  is.read();
  }
  
  @Test(expected = WorkflowGeneralException.class)
  public void validateDMN_failed() throws ParserConfigurationException {
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      dbf.setFeature(Constants.EXTERNAL_GENERAL_ENTITIES, false);
      dbf.setFeature(Constants.EXTERNAL_PARAMETER_ENTITIES, false);
      DmnProcessorUtil.validateDMN(DmnProcessorUtilTest.class.getClassLoader()
			  .getResourceAsStream(INVOICE_APPROVAL_DMN));
  }
  
  @Test(expected = WorkflowGeneralException.class)
  public void validateDMN_failed2() throws Exception{
      DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
      dbf.setFeature(Constants.EXTERNAL_GENERAL_ENTITIES, false);
      dbf.setFeature(Constants.EXTERNAL_PARAMETER_ENTITIES, false);
      InputStream resourceAsStream = DmnProcessorUtilTest.class.getClassLoader()
			  .getResourceAsStream(INVOICE_APPROVAL_DMN);
	  resourceAsStream.close();
	DmnProcessorUtil.validateDMN(resourceAsStream);
  }
  
  @Test
  public void validateDefinitionXMLNode_definitionNotFound(){
	  NodeList nodeList = null;
	  boolean flag = (boolean) ReflectionTestUtils.invokeMethod(DmnProcessorUtil.class, "validateDefinitionXMLNode", nodeList);
	  Assert.assertTrue(flag);
  }
  
  @Test
  public void validateDefinitionXMLNode_definitionLength(){
	  NodeList nodeList = Mockito.mock(NodeList.class);
	  Mockito.when(nodeList.getLength()).thenReturn(0);
	  boolean flag = (boolean)ReflectionTestUtils.invokeMethod(DmnProcessorUtil.class, "validateDefinitionXMLNode", nodeList);
	  Assert.assertTrue(flag);
  }
  
  @Test
  public void validateDefinitionXMLNode_definitionNoAttributes(){
	  NodeList defintionNodeList = Mockito.mock(NodeList.class);
	  Mockito.when(defintionNodeList.getLength()).thenReturn(1);
	  Node defintionNode = Mockito.mock(Node.class);
	  
	  Mockito.when(defintionNode.getAttributes()).thenReturn(null);
	  Mockito.when(defintionNodeList.item(Mockito.eq(0))).thenReturn(defintionNode);
	  
	  boolean flag = (boolean)ReflectionTestUtils.invokeMethod(DmnProcessorUtil.class, "validateDefinitionXMLNode", defintionNodeList);
	  Assert.assertTrue(flag);
  }
  
  @Test
  public void validateDefinitionXMLNode_success(){
	  NodeList defintionNodeList = Mockito.mock(NodeList.class);
	  Mockito.when(defintionNodeList.getLength()).thenReturn(1);
	  Node defintionNode = Mockito.mock(Node.class);
	  
	  NamedNodeMap attributeNode = Mockito.mock(NamedNodeMap.class);
	  Node xmlnsNode = Mockito.mock(Node.class);
	  Mockito.when(attributeNode.getNamedItem(Mockito.anyString())).thenReturn(xmlnsNode);
	  Mockito.when(defintionNode.getAttributes()).thenReturn(attributeNode);
	  Mockito.when(defintionNodeList.item(Mockito.eq(0))).thenReturn(defintionNode);
	  
	   boolean flag = (boolean)ReflectionTestUtils.invokeMethod(DmnProcessorUtil.class, "validateDefinitionXMLNode", defintionNodeList);
	   Assert.assertFalse(flag);
	  
  }

  @Test
  public void testPrepareDecisionMetaDataFromBaseTemplateWithDefaultInput(){
      DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
      DmnModelInstance baseTemplateDmnModel = Dmn.createEmptyModel();

      Decision result = DmnProcessorUtil.prepareDecisionMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
      Assert.assertNotNull(result);
      Assert.assertNull(result.getName());
  }

  @Test
  public void testPrepareDecisionMetaDataFromBaseTemplate(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    DmnModelInstance baseTemplateDmnModel = BpmnProcessorUtil.readDMNFile("baseTemplates/dmn/customApprovalBaseTemplate.dmn");

    Decision result = DmnProcessorUtil.prepareDecisionMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
    Assert.assertNotNull(result);
    Assert.assertEquals("decisionElement", result.getId());
    Assert.assertEquals("BYO Decision", result.getName());
  }

  @Test
  public void testPrepareDecisionTableMetaDataFromBaseTemplateWithDefaultInput(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    DmnModelInstance baseTemplateDmnModel = Dmn.createEmptyModel();

    DecisionTable result = DmnProcessorUtil.prepareDecisionTableMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
    Assert.assertNotNull(result);
    Assert.assertEquals(HitPolicy.UNIQUE, result.getHitPolicy());
  }

  @Test
  public void testPrepareDecisionTableMetaDataFromBaseTemplate(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    DmnModelInstance baseTemplateDmnModel = BpmnProcessorUtil.readDMNFile("baseTemplates/dmn/customApprovalBaseTemplate.dmn");

    DecisionTable result = DmnProcessorUtil.prepareDecisionTableMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
    Assert.assertNotNull(result);
    Assert.assertEquals("decisionTable_1", result.getId());
    Assert.assertEquals(HitPolicy.FIRST, result.getHitPolicy());
  }

  @Test
  public void testPrepareOutputMetaDataFromBaseTemplateWithDefaultInput(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    DmnModelInstance baseTemplateDmnModel = Dmn.createEmptyModel();

    Output result = DmnProcessorUtil.prepareOutputMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getName());
  }

  @Test
  public void testPrepareOutputMetaDataFromBaseTemplate(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    DmnModelInstance baseTemplateDmnModel = BpmnProcessorUtil.readDMNFile("baseTemplates/dmn/customApprovalBaseTemplate.dmn");

    Output result = DmnProcessorUtil.prepareOutputMetaDataFromBaseTemplate(dmnModelInstance, baseTemplateDmnModel);
    Assert.assertNotNull(result);
    Assert.assertEquals("output_1", result.getId());
    Assert.assertEquals("decisionResult", result.getName());
    Assert.assertEquals("decisionResult", result.getLabel());
    Assert.assertEquals("boolean", result.getTypeRef());
  }

  @Test
  public void testUpdateDmnNameWithDefaultInput(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    Definitions definitions = dmnModelInstance.newInstance(Definitions.class);
    dmnModelInstance.setDefinitions(definitions);

    DmnProcessorUtil.updateDmnName(Collections.singletonList(dmnModelInstance), "testDmnName2");

    Assert.assertEquals(0, dmnModelInstance.getModelElementsByType(Decision.class).size());
  }

  @Test
  public void testUpdateDmnName(){
    DmnModelInstance dmnModelInstance = Dmn.createEmptyModel();
    Decision decision = dmnModelInstance.newInstance(Decision.class);
    decision.setName("testDmnName");
    Definitions definitions = dmnModelInstance.newInstance(Definitions.class);
    definitions.addChildElement(decision);
    dmnModelInstance.setDefinitions(definitions);

    Assert.assertEquals("testDmnName", dmnModelInstance.getModelElementsByType(Decision.class).stream().findFirst().get().getName());

    DmnProcessorUtil.updateDmnName
        (Collections.singletonList(dmnModelInstance), "testDmnName2");

    Assert.assertEquals("testDmnName2", dmnModelInstance.getModelElementsByType(Decision.class).stream().findFirst().get().getName());
  }

  
}