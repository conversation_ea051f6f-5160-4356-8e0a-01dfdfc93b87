package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class EventPublisherUtilTest {

  @Mock private OfferingConfig offeringConfig;

  @InjectMocks EventPublisherUtil eventPublisherUtil;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }
  
  @Test
  public void testGetOfferingId() {
    WASContext.setOfferingId("test");
    Assert.assertEquals("test", eventPublisherUtil.getOfferingId());
  }

  @Test
  public void testGetOfferingIdDefault() {
    Mockito.when(offeringConfig.getDefaultOffering()).thenReturn("test2");
    Assert.assertEquals("test2", eventPublisherUtil.getOfferingId());
  }

  @Test
  public void testEmptyOfferingId() {
    Mockito.when(offeringConfig.getDefaultOffering()).thenReturn(null);
    Assert.assertEquals(StringUtils.EMPTY, eventPublisherUtil.getOfferingId());
  }
}
