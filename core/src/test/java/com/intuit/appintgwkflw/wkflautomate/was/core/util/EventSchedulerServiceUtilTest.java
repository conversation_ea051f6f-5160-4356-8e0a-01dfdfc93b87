package com.intuit.appintgwkflw.wkflautomate.was.core.util;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.DailyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.MonthlyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventScheduleConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import com.intuit.v4.payments.schedule.WeekIndexType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;

public class EventSchedulerServiceUtilTest {

    private final String ERRORS_PATH = "eventScheduler/errors.json";
    private final String DATA_PATH = "eventScheduler/data.json";
    private final String ERRORS = TestHelper.readResourceAsString(ERRORS_PATH);
    private final String DATA = TestHelper.readResourceAsString(DATA_PATH);

    @Mock
    private DailyRecurrenceProcessor mockDailyRecurrenceProcessor;

    @Mock
    private MonthlyRecurrenceProcessor mockMonthlyRecurrenceProcessor;

    @Mock
    private EventScheduleConfig eventScheduleConfig;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        RecurrenceHandler.addHandler(RecurTypeEnum.DAILY, mockDailyRecurrenceProcessor);
        RecurrenceHandler.addHandler(RecurTypeEnum.MONTHLY, mockMonthlyRecurrenceProcessor);
        when(mockDailyRecurrenceProcessor.buildESSRecurrencePattern(any()))
                .thenReturn(new RecurrencePattern().interval(1).type(RecurrencePatternType.DAILY));
    }

    @Test
    public void getHttpHeaders() {
        HttpHeaders httpHeaders = EventScheduleServiceUtil.getHttpHeaders("1234", "ide-123");
        Assert.assertTrue(httpHeaders.get(EventScheduleConstants.COMPANY_ID).contains("1234"));
        Assert.assertTrue(httpHeaders.get(EventScheduleConstants.REQUEST_ID).contains("ide-123"));
    }

    @Test
    public void getCreateSchedule_PastDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2023-2-21"), new RecurrenceRule());
        Mockito.when(eventScheduleConfig.getTimezone()).thenReturn(EventScheduleConstants.TIMEZONE);
        List<EventSchedule> eventSchedules =
                EventScheduleServiceUtil.getCreateEventSchedulesPayload(Collections.singletonList(eventScheduleWorkflowActionModel), eventScheduleConfig);
        Assert.assertNotNull(eventSchedules);
        Assert.assertNotNull(eventSchedules.get(0));
        Assert.assertNotNull(eventSchedules.get(0).getRecurrence());
        Assert.assertEquals(EventScheduleConstants.TIMEZONE, eventSchedules.get(0).getRecurrence().getTimeZone());
        Assert.assertEquals(LocalDate.now(), eventSchedules.get(0).getRecurrence().getRange().getStartDate());
    }

    @Test
    public void getCreateSchedule_PresentDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", LocalDate.now(), new RecurrenceRule());
        List<EventSchedule> eventSchedules =
                EventScheduleServiceUtil.getCreateEventSchedulesPayload(Collections.singletonList(eventScheduleWorkflowActionModel), eventScheduleConfig);
        assertEventSchedule(eventSchedules, LocalDate.now(), EventScheduleConstants.TIMEZONE);
    }

    @Test
    public void getCreateSchedule_FutureDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", LocalDate.now().plusDays(3), new RecurrenceRule());
        List<EventSchedule> eventSchedules =
                EventScheduleServiceUtil.getCreateEventSchedulesPayload(Collections.singletonList(eventScheduleWorkflowActionModel), eventScheduleConfig);
        assertEventSchedule(eventSchedules, LocalDate.now().plusDays(3), EventScheduleConstants.TIMEZONE);
    }

    @Test
    public void getCreateSchedule_WithoutTimezoneConfiguration() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart",
                        new LocalDate("2023-2-21"), new RecurrenceRule());
        Mockito.when(eventScheduleConfig.getTimezone()).thenReturn(null);
        List<EventSchedule> eventSchedules =
                EventScheduleServiceUtil.getCreateEventSchedulesPayload(
                        Collections.singletonList(eventScheduleWorkflowActionModel), eventScheduleConfig);
        assertEventSchedule(eventSchedules, LocalDate.now(), EventScheduleConstants.TIMEZONE);
    }

    @Test
    public void getCreateSchedule_WithNullEventConfig() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart",
                        new LocalDate("2023-2-21"), new RecurrenceRule());
        Mockito.when(eventScheduleConfig.getTimezone()).thenReturn(null);
        List<EventSchedule> eventSchedules =
                EventScheduleServiceUtil.getCreateEventSchedulesPayload(
                        Collections.singletonList(eventScheduleWorkflowActionModel), null);
        assertEventSchedule(eventSchedules, LocalDate.now(), EventScheduleConstants.TIMEZONE);
    }

    @Test
    public void testChangeStatus() {
        assertInvalidInput(null, ScheduleStatus.DELETED, "realm-123");
        assertInvalidInput("", ScheduleStatus.DELETED, "realm-123");
        assertInvalidInput("123", null, "realm-123");
        assertInvalidInput("123", ScheduleStatus.DELETED, null);
        EventSchedule eventSchedule =
                EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                        "123", ScheduleStatus.DELETED, "realm-123");
        Assert.assertNotNull(eventSchedule);
        Assert.assertEquals(ScheduleStatus.DELETED, eventSchedule.getStatus());
        Assert.assertNull(eventSchedule.getRecurrence());
    }

    @Test
    public void testUpdateSchedules_PastDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel(
                        "customScheduledActions_customStart",
                        new LocalDate("2023-2-21"),
                        new LocalDate("2023-2-21"),
                        new RecurrenceRule().interval(3).recurType(RecurTypeEnum.DAILY));
        EventSchedule eventSchedule =
                EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                        "123", ScheduleStatus.ACTIVE, "realm-123", eventScheduleWorkflowActionModel,
                        eventScheduleConfig);

        Assert.assertNotNull(eventSchedule);
        Assert.assertEquals(eventSchedule.getStatus(), ScheduleStatus.ACTIVE);
        Assert.assertNotNull(eventSchedule.getRecurrence().getPattern());
        Assert.assertEquals(
                RecurrencePatternType.DAILY, eventSchedule.getRecurrence().getPattern().getType());
        Assert.assertEquals(new Integer(1), eventSchedule.getRecurrence().getPattern().getInterval());
        Assert.assertEquals(LocalDate.now(), eventSchedule.getRecurrence().getRange().getStartDate());
        Assert.assertEquals(LocalDate.now().plusDays(1), eventSchedule.getRecurrence().getRange().getEndDate());
    }

    @Test
    public void testUpdateSchedules_CurrentDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel(
                        "customScheduledActions_customStart",
                        new LocalDate(),
                        new LocalDate().plusDays(5),
                        new RecurrenceRule().interval(3).recurType(RecurTypeEnum.DAILY));
        EventSchedule eventSchedule =
                EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                        "123", ScheduleStatus.ACTIVE, "realm-123", eventScheduleWorkflowActionModel,
                        eventScheduleConfig);

        Assert.assertNotNull(eventSchedule);
        Assert.assertEquals(eventSchedule.getStatus(), ScheduleStatus.ACTIVE);
        Assert.assertNotNull(eventSchedule.getRecurrence().getPattern());
        Assert.assertEquals(
                RecurrencePatternType.DAILY, eventSchedule.getRecurrence().getPattern().getType());
        Assert.assertEquals(new Integer(1), eventSchedule.getRecurrence().getPattern().getInterval());
        Assert.assertEquals(LocalDate.now(), eventSchedule.getRecurrence().getRange().getStartDate());
        Assert.assertEquals(LocalDate.now().plusDays(5), eventSchedule.getRecurrence().getRange().getEndDate());
    }

    @Test
    public void testUpdateSchedules_FutureDate() {
        when(mockMonthlyRecurrenceProcessor.buildESSRecurrencePattern(any()))
                .thenReturn(
                        new RecurrencePattern()
                                .interval(3)
                                .type(RecurrencePatternType.RELATIVEMONTHLY)
                                .weekIndex(WeekIndexType.FIRST));

        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel(
                        "customScheduledActions_customStart",
                        new LocalDate().plusDays(5),
                        new RecurrenceRule()
                                .interval(3)
                                .recurType(RecurTypeEnum.MONTHLY)
                                .weekOfMonth(WeekOfMonthEnum.FIRST)
                                .daysOfWeek(DayOfWeekEnum.FRIDAY));
        EventSchedule eventSchedule =
                EventScheduleServiceUtil.getUpdateEventSchedulePayload(
                        "123", ScheduleStatus.ACTIVE, "realm-123", eventScheduleWorkflowActionModel,
                        eventScheduleConfig);

        Assert.assertNotNull(eventSchedule);
        Assert.assertEquals(eventSchedule.getStatus(), ScheduleStatus.ACTIVE);
        Assert.assertNotNull(eventSchedule.getRecurrence().getPattern());
        RecurrencePattern pattern = eventSchedule.getRecurrence().getPattern();
        Assert.assertEquals(RecurrencePatternType.RELATIVEMONTHLY, pattern.getType());
        Assert.assertEquals(new Integer(3), pattern.getInterval());
        Assert.assertEquals(WeekIndexType.FIRST, pattern.getWeekIndex());
        Assert.assertNull(eventSchedule.getRecurrence().getTimeZone());
        Assert.assertEquals(
                LocalDate.now().plusDays(5), eventSchedule.getRecurrence().getRange().getStartDate());
    }

    @Test
    public void getResponseErrors() {
        // check with null response
        Assert.assertNull(EventScheduleServiceUtil.getErrorsFromResponse(null));
        Assert.assertNull(EventScheduleServiceUtil.getErrorsFromResponse(new ArrayList<>()));
        List<EventScheduleResponse> responses =
                ObjectConverter.fromJson(ERRORS, new TypeReference<List<EventScheduleResponse>>() {
                });
        List<EventScheduleError> eventScheduleErrorList =
                EventScheduleServiceUtil.getErrorsFromResponse(responses);
        Assert.assertEquals(3, eventScheduleErrorList.size());
    }

    @Test
    public void getResponseData() {
        Assert.assertNull(EventScheduleServiceUtil.getDataFromResponse(null));
        Assert.assertNull(EventScheduleServiceUtil.getDataFromResponse(new ArrayList<>()));
        List<EventScheduleResponse> responses =
                ObjectConverter.fromJson(DATA, new TypeReference<List<EventScheduleResponse>>() {
                });
        // total errors should be four
        List<EventScheduleData> eventScheduleDataList =
                EventScheduleServiceUtil.getDataFromResponse(responses);
        Assert.assertEquals(3, eventScheduleDataList.size());
    }

    @Test
    public void getScheduleMetaData() {
        EventScheduleMetaData metaData =
                EventScheduleServiceUtil.getScheduleMetaData(
                        DefinitionDetails.builder()
                                .templateDetails(TemplateDetails.builder().templateName("w123").build())
                                .build(),
                        ScheduleStatus.ACTIVE);
        Assert.assertEquals("w123", metaData.getWorkflowName());
        Assert.assertEquals(ScheduleStatus.ACTIVE, metaData.getScheduleStatus());
    }

    @Test
    public void getScheduleMetaDataWithEmptyDefinitionDetails() {
        EventScheduleMetaData metaData =
                EventScheduleServiceUtil.getScheduleMetaData(null, ScheduleStatus.ACTIVE);
        Assert.assertNull(metaData);
    }

    @Test
    public void testLocalId_EmptyId() {
        Assert.assertNull(EventScheduleServiceUtil.getLocalId(null));
        Assert.assertEquals(" ", EventScheduleServiceUtil.getLocalId(" "));
    }

    @Test
    public void testLocalId_withV4Separator() {
        Assert.assertEquals(
                "local-123",
                EventScheduleServiceUtil.getLocalId("global" + GlobalId.V4_Separator + "local-123"));
    }

    @Test
    public void testLocalId_withoutV4Separator() {
        Assert.assertEquals("Id-123", EventScheduleServiceUtil.getLocalId("Id-123"));
    }

    private void assertEventSchedule(List<EventSchedule> eventSchedules, LocalDate expectedStartDate, String expectedTimeZone) {
        Assert.assertNotNull(eventSchedules);
        Assert.assertNotNull(eventSchedules.get(0));
        Assert.assertNotNull(eventSchedules.get(0).getRecurrence());
        Assert.assertEquals(expectedTimeZone, eventSchedules.get(0).getRecurrence().getTimeZone());
        Assert.assertEquals(expectedStartDate, eventSchedules.get(0).getRecurrence().getRange().getStartDate());
    }

    private void assertInvalidInput(String scheduleId, ScheduleStatus status, String realmId) {
        try {
            EventScheduleServiceUtil.getUpdateEventSchedulePayload(scheduleId, status, realmId);
            Assert.fail();
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(WorkflowError.INPUT_INVALID, workflowGeneralException.getWorkflowError());
        }
    }

}
