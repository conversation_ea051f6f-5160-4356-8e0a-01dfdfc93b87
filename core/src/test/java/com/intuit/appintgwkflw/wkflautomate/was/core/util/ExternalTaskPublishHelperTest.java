package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.ExternalTaskAttributes;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class ExternalTaskPublishHelperTest {

  @Test
  public void testbuildEventPayload(){
    ProcessDetails processDetails;
    TemplateDetails templateDetails = TemplateDetails.builder()
        .templateName("templateName")
        .build();

    DefinitionDetails definitionDetails = DefinitionDetails.builder()
        .recordType(RecordType.INVOICE)
        .templateDetails(templateDetails)
        .build();

    processDetails = ProcessDetails.builder()
            .definitionDetails(definitionDetails)
            .processId("pid")
            .ownerId(123L)
            .recordId("rId")
            .build();

    Map<String, String> extension = new HashMap<>();
    extension.put("key1", "value1");

    Map<String, Object> variables = new HashMap<>();
    variables.put(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE, "txId");

    ExternalTaskAttributes externalTaskAttributes = ExternalTaskAttributes.builder()
        .extensionAttributes(extension)
        .variableMap(variables)
        .build();

    ExternalTaskAssigned externalTaskAssigned = ExternalTaskPublishHelper.buildEventPayload(processDetails, externalTaskAttributes);

    Assert.assertEquals("txId", externalTaskAssigned.getTxnId());
    Assert.assertEquals("pid", externalTaskAssigned.getWorkflowMetadata().getProcessInstanceId());
    Assert.assertEquals("rId", externalTaskAssigned.getBusinessEntityId());
    Assert.assertEquals(RecordType.INVOICE.getRecordType(), externalTaskAssigned.getBusinessEntityType());

    Assert.assertFalse(externalTaskAssigned.getExtensions().isEmpty());

    Assert.assertFalse(externalTaskAssigned.getVariables().isEmpty());

  }

  @Test
  public void testEntityId_Positive(){
    String entityId = ExternalTaskPublishHelper.transformEntityId("task", "worker");
    Assert.assertEquals("task:worker", entityId);
  }

  @Test
  public void testEntityId_Negative(){
    String entityId = ExternalTaskPublishHelper.transformEntityId("task", "");
    Assert.assertNull(entityId);
  }
}
