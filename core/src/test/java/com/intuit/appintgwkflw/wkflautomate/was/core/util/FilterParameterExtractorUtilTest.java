package com.intuit.appintgwkflw.wkflautomate.was.core.util;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import static org.mockito.ArgumentMatchers.any;

/**
 * Class to hold UTs for FilterParameterExtractorUtil.
 */
@RunWith(SpringRunner.class)
public class FilterParameterExtractorUtilTest {

    // The class to be mocked and tested is CreateCustomDefinitionHandler
    // Since it has 2 dependencies, therefore its annotated with @InjectMocks and
    // placeholderExtractorProvider and ConditionalElementHandler with @Mock
    @InjectMocks
    private FilterParameterExtractorUtil filterParameterExtractorUtil;

    // Spy allows mocks to be initialised, @Mock will not initialise the object
    @Spy
    private CustomWorkflowConfig customWorkflowConfig = initCustomWorkflowConfig();
    @Mock
    private DefinitionServiceHelper definitionServiceHelper;
    @Mock private TemplateDetails templateDetails;
    private Definition definition;
    private Authorization authorization;

    @Before
    public void setUp() {
        authorization = new Authorization();
        authorization.putRealm("12345");
    }

    @Test
    public void testSingleDefinition_filterParamsWithDefinition(){
        definition = TestHelper.mockCustomWorkflowDefinition(RecordType.STATEMENT.getRecordType());
        Map<String, HandlerDetails.ParameterDetails> parameterDetails = filterParameterExtractorUtil.getFilterParameterDetails(definition);
        Assert.assertNotNull(parameterDetails.get(WorkflowConstants.FILTER_CONDITION));
        Assert.assertNotNull(parameterDetails.get(WorkflowConstants.FILTER_RECORD_TYPE));
    }

    @Test
    public void testSingleDefinition_filterParamsWithDefinitionDetails(){

        DefinitionDetails definitionDetails =
                TestHelper.mockDefinitionDetailsWithId(
                        templateDetails, authorization, "etet2-2434j2-3232fl-33ff");
        definitionDetails.setRecordType(RecordType.STATEMENT);
        DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
        definitionDetailsDmn.setParentId(definitionDetails.getDefinitionId());
        definitionDetailsDmn.setDefinitionId("94f4777f-8da1-480f-8be6-f4b3835824a8");
        definitionDetailsDmn.setDefinitionName("statement");
        definitionDetailsDmn.setModelType(ModelType.DMN);
        String FilterConditions =
                "{\"rule_line_variables\":[{\"parameterName\":\"Customer\", \"parameterType\": \"LIST\", \"$sdk_validated\":  \"true\" ,\"conditionalExpression\":\"CONTAINS 1\"},{\"parameterName\":\"StatementType\",\"parameterType\": \"LIST\", \"$sdk_validated\":  \"true\", \"conditionalExpression\":\"CONTAINS BALANCE_FORWARD\"}]}";
        definitionDetailsDmn.setPlaceholderValue(FilterConditions);
        Optional<List<DefinitionDetails>> definitionDetailsDmnOptional = Optional.of(Arrays.asList(definitionDetailsDmn));
        Mockito.when(definitionServiceHelper.findByParentId(any()))
                .thenReturn(definitionDetailsDmnOptional);
        Map<String, HandlerDetails.ParameterDetails> parameterDetails = filterParameterExtractorUtil.getFilterParameterDetails(definitionDetails);
        Assert.assertNotNull(parameterDetails.get(WorkflowConstants.FILTER_CONDITION));
        Assert.assertNotNull(parameterDetails.get(WorkflowConstants.FILTER_RECORD_TYPE));
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testSingleDefinition_filterParamsWithoutFilterCloseTaskWithDefinition(){
        definition = TestHelper.mockCustomWorkflowDefinitionWithoutCloseTaskFieldValue(RecordType.INVOICE.getRecordType());
        Map<String, HandlerDetails.ParameterDetails> parameterDetails = filterParameterExtractorUtil.getFilterParameterDetails(definition);
    }

    private CustomWorkflowConfig initCustomWorkflowConfig() {
        CustomWorkflowConfig loadedCustomWorkflowConfig = null;
        try {
            loadedCustomWorkflowConfig = TestHelper.loadCustomConfigIncludingStatements();
        } catch (Exception e) {
            return null;
        }
        return loadedCustomWorkflowConfig;
    }

}
