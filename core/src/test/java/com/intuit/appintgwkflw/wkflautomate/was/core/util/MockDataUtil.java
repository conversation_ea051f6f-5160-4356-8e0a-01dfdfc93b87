package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.google.common.collect.ImmutableList;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import java.util.List;
import java.util.UUID;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MockDataUtil {

  public List<TemplateDetails> buildTemplateDetails() {

    final TemplateDetails hubTemplate =
        TemplateDetails.builder()
            .id(UUID.randomUUID().toString())
            .templateCategory("hub")
            .templateName("invoice_approval.bpmn")
            .build();

    final TemplateDetails systemTemplate =
        TemplateDetails.builder()
            .id(UUID.randomUUID().toString())
            .templateCategory("system")
            .templateName("subscription_downgrade.bpmn")
            .build();

    return ImmutableList.of(hubTemplate, systemTemplate);
  }
}
