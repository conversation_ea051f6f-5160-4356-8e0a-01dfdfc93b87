package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.OutgoingActivityMapperFactoryTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionIdMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.Query;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepUtilTest {
    private static final String multiSplitStr = "{\"1\":{\"stepId\":1,\"action\":null,\"stepType\":\"condition\",\"nexts\":[{\"label\":\"yes\",\"type\":null,\"stepId\":\"2\"},{\"label\":\"no\",\"type\":null,\"stepId\":\"3\"}],\"attributes\":[{\"id\":\"txnAmount\",\"name\":\"TxnAmount\",\"type\":\"double\",\"multiSelect\":false,\"fieldValueOptions\":null,\"defaultOperator\":\"BTW\",\"defaultValue\":\"0,1000\",\"unsupportedOperators\":[],\"required\":null,\"configurable\":true,\"hidden\":null,\"controllerFF\":null,\"obfuscate\":null}]},\"2\":{\"stepId\":2,\"action\":\"sendForApproval\",\"stepType\":\"action\",\"nexts\":null,\"attributes\":null},\"3\":{\"stepId\":3,\"action\":null,\"stepType\":\"condition\",\"nexts\":[{\"label\":\"yes\",\"type\":null,\"stepId\":\"4\"},{\"label\":\"no\",\"type\":null,\"stepId\":\"5\"}],\"attributes\":[{\"id\":\"txnAmount\",\"name\":\"TxnAmount\",\"type\":\"double\",\"multiSelect\":false,\"fieldValueOptions\":null,\"defaultOperator\":\"BTW\",\"defaultValue\":\"1001,5000\",\"unsupportedOperators\":[],\"required\":null,\"configurable\":true,\"hidden\":null,\"controllerFF\":null,\"obfuscate\":null}]},\"4\":{\"stepId\":4,\"action\":\"sendForApproval\",\"stepType\":\"action\",\"nexts\":null,\"attributes\":null},\"5\":{\"stepId\":5,\"action\":null,\"stepType\":\"condition\",\"nexts\":[{\"label\":\"yes\",\"type\":null,\"stepId\":\"6\"},{\"label\":\"no\",\"type\":null,\"stepId\":\"7\"}],\"attributes\":[{\"id\":\"txnAmount\",\"name\":\"TxnAmount\",\"type\":\"double\",\"multiSelect\":false,\"fieldValueOptions\":null,\"defaultOperator\":\"BTW\",\"defaultValue\":\"5001,10000\",\"unsupportedOperators\":[],\"required\":null,\"configurable\":true,\"hidden\":null,\"controllerFF\":null,\"obfuscate\":null}]},\"6\":{\"stepId\":6,\"action\":\"sendForApproval\",\"stepType\":\"action\",\"nexts\":null,\"attributes\":null},\"7\":{\"stepId\":7,\"action\":\"sendForApproval\",\"stepType\":\"action\",\"nexts\":null,\"attributes\":null}}";
    private static final String CUSTOM_WORKFLOW_DMN_XML =
        TestHelper.readResourceAsString("dmn/customWorkflow.dmn");
    private static final String MULTI_CONDITION_DMN_XML =
        TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");
    private static final String MULTI_CONDITION_CUSTOM_APPROVAL_BPMN = "bpmn/customApproval_multiCondition.bpmn";
    public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
    public static String YAML_KEY = "templateConfig";
    public static String RECORD_TYPE_INVOICE = "invoice";
    public static String RECORD_TYPE_BILL = "bill";
    public static String REMINDER_ACTION_KEY = "reminder";
    public static String APPROVAL_ACTION_KEY = "approval";
    private CustomWorkflowConfig customWorkflowConfig;
    private Definition definition;
    private Definition multiConditionDefintion;
    @Mock
    private WASContextHandler wasContextHandler;
    private DefinitionInstance definitionInstance;
    private DmnModelInstance multiConditionDmnModelInstance;
    private DmnModelInstance multiRuleDmnModelInstance;
    private BpmnModelInstance multiConditionBpmnModelInstance;
    private Definition singleStepMultiConditionDefinition;

    private static BpmnModelInstance readBPMNFile(String fileName) {
        return Bpmn.readModelFromStream(
            OutgoingActivityMapperFactoryTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    @Before
    @SneakyThrows
    public void setup() {
        definition = TestHelper.mockDefinitionEntity();
        multiConditionDefintion = TestHelper.mockMultiConditionDefinitionEntity();
        singleStepMultiConditionDefinition = TestHelper.mockSingleStepMultiConditionDefinitionEntity();
        OldCustomWorkflowConfig oldCustomWorkflowConfig =
            new ObjectMapper(new YAMLFactory())
                .readValue(
                    TestHelper.readResourceAsString(DICTIONARY_PATH),
                    new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
                .get(YAML_KEY);
        oldCustomWorkflowConfig.afterPropertiesSet();
        CustomConfigV2 customConfigV2 = new CustomConfigV2();
        CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
        customWorkflowConfig.setCustomWorkflowConfigFactory(
            new CustomWorkflowConfigFactory(
                oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
        customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
        customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
        this.customWorkflowConfig=customWorkflowConfig;

        multiConditionDmnModelInstance =
            Dmn.readModelFromStream(
                IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

        multiRuleDmnModelInstance =
            Dmn.readModelFromStream(
                IOUtils.toInputStream(MULTI_CONDITION_DMN_XML, Charset.defaultCharset()));

        multiConditionBpmnModelInstance = readBPMNFile(MULTI_CONDITION_CUSTOM_APPROVAL_BPMN);
        MockitoAnnotations.initMocks(this);

        definitionInstance = new DefinitionInstance(multiConditionDefintion,
            multiConditionBpmnModelInstance,
            Collections.singletonList(multiConditionDmnModelInstance), new TemplateDetails());

    }

    @Test
    public void testbuildConfigStepIdMap() {
        Record recordTypeObj = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        Map<Integer, Steps> result = MultiStepUtil.buildConfigStepIdMap(customWorkflowConfig, recordTypeObj, APPROVAL_ACTION_KEY, false);
        Assert.assertNotNull(result);
        Assert.assertEquals(6, result.size());
        Assert.assertEquals("condition", result.get(1).getStepType());
        Assert.assertNotNull(result.get(1).getNexts());
        Assert.assertEquals(2, result.get(1).getNexts().size());
        Assert.assertNotNull(result.get(1).getAttributes());
        Assert.assertNull(result.get(1).getAction());
        Assert.assertEquals("action", result.get(2).getStepType());
        Assert.assertNull(result.get(2).getNexts());
        Assert.assertNull(result.get(2).getAttributes());
        Assert.assertNotNull(result.get(2).getAction());
        Assert.assertEquals("sendForApproval", result.get(2).getAction());
    }

    @Test
    public void testbuildConfigStepIdMapForPrecanned() {
        Record record = new Record();
        record.setId("invoice");

        ActionGroup actionGroup = new ActionGroup();
        actionGroup.setId("approval");
        ActionIdMapper actionIdMapper = new ActionIdMapper();
        List<String> subactionIds = new ArrayList<>();
        subactionIds.add("createTask");
        subactionIds.add("sendCompanyEmail");
        actionIdMapper.setActionId("sendForApproval");
        actionIdMapper.setSubActionIds(subactionIds);
        actionGroup.setActionIdMapper(actionIdMapper);
        record.setActionGroups(Arrays.asList(actionGroup));

        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(StepTypeEnum.CONDITION.value());
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(NextTypeEnum.ACTION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);

        Attribute txnAttr = new Attribute();
        txnAttr.setName("TxnAmount");
        txnAttr.setId("txnAmount");
        txnAttr.setType("DOUBLE");
        txnAttr.setDefaultValue("500");
        txnAttr.setDefaultOperator("GTE");

        configStep.setAttributes(Arrays.asList(txnAttr));
        record.setSteps(Arrays.asList(configStep));

        Map<Integer, Steps> result = MultiStepUtil.buildConfigStepIdMap(customWorkflowConfig, record, APPROVAL_ACTION_KEY, true);
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("CONDITION", result.get(1).getStepType());
        Assert.assertNotNull(result.get(1).getNexts());
        Assert.assertEquals(2, result.get(1).getNexts().size());
        Assert.assertNotNull(result.get(1).getAttributes());
        Assert.assertNull(result.get(1).getAction());

        record.setSteps(Collections.emptyList());
        result = MultiStepUtil.buildConfigStepIdMap(customWorkflowConfig, record, APPROVAL_ACTION_KEY, true);
        Assert.assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testgetTemplateDataForReadOneTemplate() {
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        boolean returnTemplateSteps = MultiStepUtil.isTemplateDataForReadOneTemplate(record, APPROVAL_ACTION_KEY);
        Assert.assertTrue(returnTemplateSteps);
        record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        returnTemplateSteps = MultiStepUtil.isTemplateDataForReadOneTemplate(record, REMINDER_ACTION_KEY);
        Assert.assertTrue(returnTemplateSteps);
        record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_BILL);
        returnTemplateSteps = MultiStepUtil.isTemplateDataForReadOneTemplate(record, APPROVAL_ACTION_KEY);
        Assert.assertFalse(returnTemplateSteps);
        record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_BILL);
        returnTemplateSteps = MultiStepUtil.isTemplateDataForReadOneTemplate(record, REMINDER_ACTION_KEY);
        Assert.assertFalse(returnTemplateSteps);
    }

    @Test
    public void isTemplateDataPassed() {
        Query query = new Query();
        List<Query.PreparedQuery> queryList = new ArrayList<>();
        Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();

        workflowStepsQuery.type("/workflows/WorkflowStep");
        workflowStepsQuery.name("WorkflowStep");
        workflowStepsQuery.setProperties(0, "id");
        queryList.add(workflowStepsQuery);
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        preparedQuery.setType("/workflows/Template");
        preparedQuery.setName("templates");
        preparedQuery.setSubQueries(queryList);
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);

        boolean result = MultiStepUtil.isTemplateDataPassed(queryHelper.getPreparedQuery().getSubQueries());
        Assert.assertFalse(result);

        Query.PreparedQuery templateDataQuery = new Query.PreparedQuery();
        templateDataQuery.setType("/workflows/templateData");
        templateDataQuery.setName("templateData");
        templateDataQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
        queryList.add(templateDataQuery);
        result = MultiStepUtil.isTemplateDataPassed(queryHelper.getPreparedQuery().getSubQueries());
        Assert.assertTrue(result);
    }

    @Test
    public void testMultiConditionResponseTransformer_ForNullAsInput() {
        List<Map<String, Object>> decisionResult = null;
        List<Map<String, Object>> actualResult = MultiStepUtil.multiConditionResponseTransformer(decisionResult);
        Assert.assertEquals(null, actualResult);
    }

    @Test
    public void testMultiConditionResponseTransformer_ForEmptyListAsInput() {
        List<Map<String, Object>> decisionResult = new ArrayList<>();
        List<Map<String, Object>> actualResult = MultiStepUtil.multiConditionResponseTransformer(decisionResult);
        Assert.assertEquals(decisionResult, actualResult);
    }

    @Test
    public void testMultiConditionResponseTransformer_ForActivityIdAsInput() {
        List<Map<String, Object>> decisionResult = new ArrayList<>();
        Map<String, Object> decisionResultMap = new HashMap<>();
        decisionResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "action-2");
        decisionResult.add(decisionResultMap);
        List<Map<String, Object>> actualResult = MultiStepUtil.multiConditionResponseTransformer(decisionResult);
        List<Map<String, Object>> expectedResult = new ArrayList<>();
        Map<String, Object> expectedResultMap = new HashMap<>();
        expectedResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, Boolean.TRUE);
        expectedResult.add(expectedResultMap);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testMultiConditionResponseTransformer_ForFalseAsInput() {
        List<Map<String, Object>> decisionResult = new ArrayList<>();
        Map<String, Object> decisionResultMap = new HashMap<>();
        decisionResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "false");
        decisionResult.add(decisionResultMap);
        List<Map<String, Object>> actualResult = MultiStepUtil.multiConditionResponseTransformer(decisionResult);
        List<Map<String, Object>> expectedResult = new ArrayList<>();
        Map<String, Object> expectedResultMap = new HashMap<>();
        expectedResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, Boolean.FALSE);
        expectedResult.add(expectedResultMap);
        Assert.assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testIsYesLabel() {
        Assert.assertTrue(MultiStepUtil.isYesLabel(NextLabelEnum.YES));
        Assert.assertFalse(MultiStepUtil.isYesLabel(NextLabelEnum.NO));
    }

    @Test
    public void testIsNoLabel() {
        Assert.assertFalse(MultiStepUtil.isNoLabel(NextLabelEnum.YES));
        Assert.assertTrue(MultiStepUtil.isNoLabel(NextLabelEnum.NO));
    }

    @Test
    public void testIsActionStep() {
        WorkflowStep actionWorkflowStep = new WorkflowStep().stepType(StepTypeEnum.ACTION);
        actionWorkflowStep.setActionGroup(new com.intuit.v4.workflows.ActionGroup());
        Assert.assertTrue(MultiStepUtil.isActionStep(actionWorkflowStep));
        WorkflowStep conditionWorkflowStep = new WorkflowStep().stepType(StepTypeEnum.CONDITION);
        Assert.assertFalse(MultiStepUtil.isActionStep(conditionWorkflowStep));
    }

    @Test
    public void testIsConditionStep() {
        WorkflowStep actionWorkflowStep = new WorkflowStep().stepType(StepTypeEnum.ACTION);
        Assert.assertFalse(MultiStepUtil.isConditionStep(actionWorkflowStep));
        WorkflowStep conditionWorkflowStep = new WorkflowStep().stepType(StepTypeEnum.CONDITION);
        conditionWorkflowStep.setWorkflowStepCondition(new WorkflowStepCondition());
        Assert.assertTrue(MultiStepUtil.isConditionStep(conditionWorkflowStep));
    }

    @Test
    public void testIsCompositeStep() {
        WorkflowStep compositeWorkflowStep = new WorkflowStep().stepType(StepTypeEnum.WORFKLOWSTEP);
        compositeWorkflowStep.setWorkflowStepCondition(new WorkflowStepCondition());
        Assert.assertFalse(MultiStepUtil.isCompositeStep(compositeWorkflowStep));
        Assert.assertFalse(MultiStepUtil.isCompositeStep(compositeWorkflowStep));
        compositeWorkflowStep.setActionGroup(new com.intuit.v4.workflows.ActionGroup());
        Assert.assertTrue(MultiStepUtil.isCompositeStep(compositeWorkflowStep));
    }

    @Test
    public void testComputeLeftChildIndex() {
        Assert.assertEquals(5, MultiStepUtil.computeLeftChildIndex(2));
        Assert.assertEquals(9, MultiStepUtil.computeLeftChildIndex(4));
    }

    @Test
    public void testComputeRightChildIndex() {
        Assert.assertEquals(6, MultiStepUtil.computeRightChildIndex(2));
        Assert.assertEquals(10, MultiStepUtil.computeRightChildIndex(4));
    }

    @Test
    public void testIsMultiCondition() {
        Assert.assertFalse(MultiStepUtil.isMultiCondition(definition));
        Assert.assertTrue(MultiStepUtil.isMultiCondition(multiConditionDefintion));
        Assert.assertTrue(MultiStepUtil.isMultiCondition(singleStepMultiConditionDefinition));
    }

    @Test
    public void testFindFirstWorkflowStep() {
        WorkflowStep expectedFirstWorkflowStep =
            MultiStepUtil.findFirstWorkflowStep(multiConditionDefintion.getWorkflowSteps());
        WorkflowStep actualFirstWorkflowStep = multiConditionDefintion.getWorkflowSteps(0);
        Assert.assertEquals(expectedFirstWorkflowStep, actualFirstWorkflowStep);
        Assert.assertNotEquals(expectedFirstWorkflowStep, multiConditionDefintion.getWorkflowSteps(1));
        List<WorkflowStep> workflowSteps = multiConditionDefintion.getWorkflowSteps();
        workflowSteps.remove(0);
        workflowSteps.add(actualFirstWorkflowStep);
        multiConditionDefintion.setWorkflowSteps(workflowSteps);
        expectedFirstWorkflowStep =
            MultiStepUtil.findFirstWorkflowStep(multiConditionDefintion.getWorkflowSteps());
        Assert.assertEquals(expectedFirstWorkflowStep, actualFirstWorkflowStep);
    }

    @Test
    public void testFindFirstWorkflowSteps() {
        Map<Integer, Steps> configMap = new HashMap<>();
        Steps step1 = Steps.builder().stepId(1).stepType("CONDITION").build();
        Steps step2 = Steps.builder().stepId(2).stepType("ACTION").build();
        Next next = new Next();
        next.setStepId("2");
        next.setLabel(NextLabelEnum.YES.name());
        List<Next> nexts = new ArrayList<>();
        nexts.add(next);
        step1.setNexts(nexts);

        configMap.put(1, step1);
        configMap.put(2, step2);
        List<Steps> steps = MultiStepUtil.findFirstWorkflowSteps(configMap);

        Assert.assertEquals(steps.size(), 1);
    }

    @Test
    public void testFindFirstWorkflowStepsMultipleRootNodes() {
        Map<Integer, Steps> configMap = new HashMap<>();
        Steps step1 = Steps.builder().stepId(1).stepType("CONDITION").build();
        Steps step2 = Steps.builder().stepId(2).stepType("ACTION").build();
        Steps step3 = Steps.builder().stepId(3).stepType("CONDITION").build();

        Next next = new Next();
        next.setStepId("2");
        next.setLabel(NextLabelEnum.YES.name());
        List<Next> nexts = new ArrayList<>();
        nexts.add(next);
        step1.setNexts(nexts);

        configMap.put(1, step1);
        configMap.put(2, step2);
        configMap.put(3, step3);
        List<Steps> steps = MultiStepUtil.findFirstWorkflowSteps(configMap);

        Assert.assertEquals(steps.size(), 2);
        Assert.assertEquals(steps.get(0).getStepId().toString(), "1");
        Assert.assertEquals(steps.get(1).getStepId().toString(), "3");
    }

    @Test
    public void testIsSingleStepMultiCondition() {
        Assert.assertFalse(MultiStepUtil.isSingleStepMultiCondition(multiConditionDefintion));
        Assert.assertTrue(MultiStepUtil.isSingleStepMultiCondition(singleStepMultiConditionDefinition));
        Assert.assertFalse(MultiStepUtil.isSingleStepMultiCondition(definition));
    }

    @Test
    public void testIsDecisionResultTypeCondition() {
        Assert.assertFalse(MultiStepUtil.isDecisionResultTypeCondition("action-1"));
        Assert.assertTrue(MultiStepUtil.isDecisionResultTypeCondition("1"));
        Assert.assertTrue(MultiStepUtil.isDecisionResultTypeCondition("false"));
        Assert.assertFalse(MultiStepUtil.isDecisionResultTypeCondition("true"));
    }

    @Test
    public void testGetWorkflowStepTypeId() {
        Assert.assertEquals("action-1", MultiStepUtil.getWorkflowStepTypeId("action-1"));
        Assert.assertEquals(WorkflowConstants.CUSTOM_DECISION_ELEMENT, MultiStepUtil.getWorkflowStepTypeId("true"));
        Assert.assertEquals(WorkflowConstants.CUSTOM_DECISION_ELEMENT, MultiStepUtil.getWorkflowStepTypeId("1"));
    }

    @Test
    public void testGenerateWorkflowStepId() {
        Assert.assertNotNull(MultiStepUtil.generateWorkflowStepId(
            "1234",
            WorkflowConstants.CUSTOM_DECISION_ELEMENT,
            wasContextHandler));
    }

    @Test
    public void testGetFilteredRecordActionGroup() {
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        ActionGroup actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, APPROVAL_ACTION_KEY);
        Assert.assertNotNull(actionGroup);
        Assert.assertNotNull(actionGroup.getActionIdMapper());
        Assert.assertNotNull(actionGroup.getActionIdMapper().getActionId());
        Assert.assertEquals("sendForApproval", actionGroup.getActionIdMapper().getActionId());
        Assert.assertNotNull(actionGroup.getActionIdMapper().getSubActionIds());
        Assert.assertEquals(3, actionGroup.getActionIdMapper().getSubActionIds().size());
        Assert.assertEquals(3, actionGroup.getActions().size());
        Assert.assertNotNull(actionGroup.getPrecannedTemplateId());
        Assert.assertEquals("invoiceapproval-multicondition", actionGroup.getPrecannedTemplateId());
        Assert.assertNotNull(actionGroup.getUnsupportedAttributes());
    }

    @Test
    public void testGetFilteredRecordActionGroupWithMatch() {
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        ActionGroup actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, REMINDER_ACTION_KEY);
        Assert.assertNotNull(actionGroup);
    }

    @Test
    public void testGetFilteredRecordActionGroupInvalid() {
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        ActionGroup actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, null);
        Assert.assertNull(actionGroup);
        record.getActionGroups().get(1).getActionIdMapper().setActionId(null);
        actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, "approval");
        Assert.assertNull(actionGroup);
        record.getActionGroups().get(1).setActionIdMapper(null);
        actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, null);
        Assert.assertNull(actionGroup);
        actionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, "invalid");
        Assert.assertNull(actionGroup);
    }

    @Test
    public void testIsDmnStep_Success() {
        BaseElement element =
            multiConditionBpmnModelInstance.getModelElementsByType(BusinessRuleTask.class).stream().findFirst().get();
        Assert.assertTrue(MultiStepUtil.isDmnStep(element.getId(), definitionInstance));
    }

    @Test
    public void testIsDmnStep_Failure() {
        BaseElement element =
            multiConditionBpmnModelInstance.getModelElementsByType(CallActivity.class).stream().findFirst().get();
        Assert.assertFalse(MultiStepUtil.isDmnStep(element.getId(), definitionInstance));
    }

    @Test
    public void testisNotEmptyRuleLine() {
        Collection<DecisionTable> decisionTables = multiRuleDmnModelInstance.getModelElementsByType(
            DecisionTable.class);
        DecisionTable decisionTable = decisionTables.stream().findFirst().get();
        List<Rule> decisionRules = new LinkedList<>(decisionTable.getRules());
        List<InputEntry> nonEmptyInputEntries = new ArrayList<>(decisionRules.get(0).getInputEntries());
        Assert.assertTrue(MultiStepUtil.isNotEmptyRuleLine(nonEmptyInputEntries));
        List<InputEntry> emptyInputEntries = new ArrayList<>(decisionRules.get(1).getInputEntries());
        Assert.assertFalse(MultiStepUtil.isNotEmptyRuleLine(emptyInputEntries));
    }

    @Test
    public void testParentProcessDetails() {
        ProcessDetails processDetails = MultiStepUtil.fetchParentProcessDetails(buildParentProcessDetails());
        Assert.assertEquals(processDetails.getProcessId(), "Parent-P11");
    }

    @Test
    public void testProcessDetails() {
        ProcessDetails processDetails = MultiStepUtil.fetchParentProcessDetails(buildProcessDetails("P11", null));
        Assert.assertEquals(processDetails.getProcessId(), "P11");
    }

    @Test
    public void testIsChildProcessForParentProcess() {
        Assert.assertTrue(MultiStepUtil.isChildProcess(buildParentProcessDetails()));
    }
    @Test
    public void testIsChildProcess() {
        Assert.assertFalse(MultiStepUtil.isChildProcess(buildProcessDetails("P11", null)));
    }

    @Test
    public void testEntityTypeForParentProcess() {
        ProcessDetails processDetails = buildParentProcessDetails();
        Assert.assertEquals(MultiStepUtil.getEntityType(processDetails) , RecordType.INVOICE.getRecordType());
    }

    @Test
    public void testNullEntityTypeForParentProcessForODA(){
        ProcessDetails processDetails = buildParentProcessDetails();
        processDetails.getParentProcessDetails().getDefinitionDetails().setRecordType(null);
        Assert.assertNull(MultiStepUtil.getEntityType(processDetails));
    }

    @Test
    public void testEntityTypeForParentProcessWithNullRecordType() {
        ProcessDetails processDetails = buildProcessDetails("P11", null);
        Assert.assertNull(MultiStepUtil.getEntityType(processDetails));
    }

    @Test
    public void testEntityTypeForParentProcessWithRecordType() {
        ProcessDetails processDetails = buildProcessDetails("P11", RecordType.INVOICE);
        Assert.assertEquals(MultiStepUtil.getEntityType(processDetails), RecordType.INVOICE.getRecordType());
    }

    @Test
    public void testIsConditionType() {
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        activityInstance.setChildActivityInstances(null);
        Assert.assertFalse(MultiStepUtil.isDmnTypeActivityInstance(activityInstance));
        activityInstance.setDmnModelInstance(multiConditionDmnModelInstance);
        Assert.assertTrue(MultiStepUtil.isDmnTypeActivityInstance(activityInstance));
    }

    @Test
    public void testIsCallActivityType() {
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        Assert.assertTrue(MultiStepUtil.isCallActivityTypeActivityInstance(activityInstance));
    }

    @Test
    public void testComputeChildIndex() {
        Assert.assertEquals(MultiStepUtil.computeChildIndex(2, 3, 1), 7);
    }

    @Test
    public void testIsRuleComposite() {
        RuleLine.Rule rule = new RuleLine.Rule();
        Assert.assertFalse(MultiStepUtil.isRuleTypeComposite(rule));

        rule.setParameterType(FieldTypeEnum.DAYS);
        Assert.assertTrue(MultiStepUtil.isRuleTypeComposite(rule));

        rule.setParameterType(FieldTypeEnum.STRING);
        Assert.assertFalse(MultiStepUtil.isRuleTypeComposite(rule));
    }

    @Test
    public void testIsStepTypeComposite() {
        Steps configStep = new Steps();
        Assert.assertFalse(MultiStepUtil.isStepTypeComposite(configStep));

        Attribute attribute = new Attribute();
        attribute.setType(FieldTypeEnum.DAYS.name());
        configStep.setAttributes(List.of(attribute));
        Assert.assertTrue(MultiStepUtil.isStepTypeComposite(configStep));

        attribute.setType(FieldTypeEnum.DOUBLE.name());
        Assert.assertFalse(MultiStepUtil.isStepTypeComposite(configStep));
    }

    private ProcessDetails buildParentProcessDetails() {

        ProcessDetails processDetails = buildProcessDetails("P11", null);
        ProcessDetails parentProcessDetails = buildProcessDetails("Parent-P11", RecordType.INVOICE);
        processDetails.setParentId("Parent-P11");
        processDetails.setParentProcessDetails(parentProcessDetails);
        return processDetails;
    }

    private ProcessDetails buildProcessDetails(String processId, RecordType recordType) {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setRecordType(recordType);
        return ProcessDetails.builder()
            .processId(processId)
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(definitionDetails)
            .ownerId(123L)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .parentId(null)
            .build();
    }

    @Test
    public void test_getParentWorkflowStep() {
        Definition definition = TestHelper.mockMultiConditionDefinitionEntity();

        WorkflowStep parentWorkflowStep = MultiStepUtil.getParentWorkflowStep(
            definition.getWorkflowSteps(), definition.getWorkflowSteps().get(1).getId());

        Assert.assertEquals(definition.getWorkflowSteps().get(0), parentWorkflowStep);
        Assert.assertNull(MultiStepUtil.getParentWorkflowStep(
            definition.getWorkflowSteps(), definition.getWorkflowSteps().get(0).getId()));
    }

    @Test
    public void isLeafNodeTest() {
        Steps configStep = new Steps();
        Assert.assertTrue(MultiStepUtil.isLeafNode(configStep));

        List<Next> nexts = new ArrayList<>();
        nexts.add(new Next());
        configStep.setNexts(nexts);

        Attribute attribute = new Attribute();
        attribute.setType(FieldTypeEnum.DAYS.name());
        configStep.setAttributes(List.of(attribute));
        Assert.assertTrue(MultiStepUtil.isLeafNode(configStep));

        attribute.setType(FieldTypeEnum.STRING.name());
        Assert.assertFalse(MultiStepUtil.isLeafNode(configStep));
    }

    @Test
    public void fetchListOfNodesInPathTest(){
        definition = TestHelper.mockMultiConditionRecurringReminder();
        List<WorkflowStep> workflowSteps = definition.getWorkflowSteps();
        Map<String, WorkflowStep> workflowStepMap = workflowSteps.stream().collect(
            Collectors.toMap(workflowStep -> String.valueOf(workflowStep.getId()), Function.identity()));
        Assert.assertEquals(3, MultiStepUtil.fetchListOfNodesInPath(workflowSteps.get(0),workflowSteps.get(
            workflowSteps.size()-1).getId().toString(),workflowStepMap).size());
    }

}