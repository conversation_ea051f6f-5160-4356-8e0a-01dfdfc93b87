package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TransactionEntityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalDetailResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalGroupResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalRequestResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OnDemandHelperTest {

    @Mock
    private WASContextHandler contextHandler;

    @Mock
    private V3RunTimeHelper v3RunTimeHelper;

    @InjectMocks
    private OnDemandHelper onDemandHelper;

    @Mock
    private TriggerProcessDetails triggerProcessDetails;

    @Mock
    private TransactionEntity transactionEntity;

    @Mock
    private CustomWorkflowConfig customWorkflowConfig;


    private static final String CREATED_EVENT = "created";

    @Before
    public void setUp() {

        triggerProcessDetails = TriggerHandlerTestData.prepareV3TriggerMessage(CREATED_EVENT);
        transactionEntity =  TransactionEntityFactory.getInstanceOf(triggerProcessDetails.getTriggerMessage(), contextHandler);
    }

    @Test
    public void testCheckIfOnDemand_ForTriggerFlowTrue() {
        Mockito.when(v3RunTimeHelper.getEligibleDefinitions(any(TransactionEntity.class),eq(false))).thenReturn(new ArrayList<>());
        assertTrue(onDemandHelper.checkIfOnDemand(transactionEntity, Optional.empty()));
    }

    @Test
    public void testCheckIfOnDemand_ForTriggerFlowFalse() {
        Mockito.when(v3RunTimeHelper.getEligibleDefinitions(any(TransactionEntity.class),eq(false))).thenReturn(Collections.singletonList(new DefinitionDetails()));
        assertFalse(onDemandHelper.checkIfOnDemand(transactionEntity, Optional.empty()));
    }

    @Test
    public void testCheckIfOnDemand_ForSignalFlowTrue() {
        assertTrue(onDemandHelper.checkIfOnDemand(transactionEntity, Optional.of(processDetailsOnDemand())));
    }

    @Test
    public void testisCustomApprovalSingleDefinitionPresent(){
        DefinitionDetails definitionDetails1 = new DefinitionDetails();
        definitionDetails1.setInternalStatus(InternalStatus.STALE_DEFINITION);
        Mockito.when(v3RunTimeHelper.getEligibleDefinitions(any(TransactionEntity.class),eq(false))).thenReturn(
            List.of(definitionDetails1));

        assertFalse(onDemandHelper.isCustomApprovalSingleDefinitionPresent(transactionEntity));

    }

    @Test
    public void isRequestTypeOnDemandAprovalTest() {
        Mockito.when(customWorkflowConfig.getRecordObjForType("test")).thenReturn(
            Record.builder().source("NON_QBO").build());
        Mockito.when(customWorkflowConfig.getRecordObjForType("bill")).thenReturn(
            Record.builder().source("QBO").build());
        assertTrue(onDemandHelper.isRequestTypeOnDemandAproval(WorkerActionRequest.builder().inputVariables(
            Map.of("entityType","bill", WorkflowConstants.ON_DEMAND_APPROVAL,"true")).build()));
        assertFalse(onDemandHelper.isRequestTypeOnDemandAproval(WorkerActionRequest.builder().inputVariables(
            Map.of("entityType","test", WorkflowConstants.ON_DEMAND_APPROVAL,"true")).build()));

        assertFalse(onDemandHelper.isRequestTypeOnDemandAproval(WorkerActionRequest.builder().inputVariables(
            Map.of("entityType","abc", WorkflowConstants.ON_DEMAND_APPROVAL,"true")).build()));
    }


    private ProcessDetails processDetailsOnDemand(){
        ProcessDetails processDetails = new ProcessDetails();
        processDetails.setProcessId("12343");
        processDetails.setDefinitionDetails(DefinitionDetails.builder().definitionId("1234").ownerId(Long.MIN_VALUE).version(12).entityVersion(12).build());
        return processDetails;
    }




    private ApprovalGroupResponse approvalGroupResponseOnDemand(){
        ApprovalGroupResponse approvalGroupResponse = new ApprovalGroupResponse();
        approvalGroupResponse.setId("1a9e9f97-1375-4763-b409-2d60ed946b6f");
        approvalGroupResponse.setStatus("NOT_APPROVED");
        approvalGroupResponse.setEntityId("1234");
        approvalGroupResponse.setEntityType("INVOICE");
        ApprovalRequestResponse approvalRequestResponse = new ApprovalRequestResponse();
        approvalRequestResponse.setId("74bcc4b4-78a0-42fc-915e-9f1dc870f664");
        approvalRequestResponse.setStatus("CREATED");
        approvalRequestResponse.setType("SEQUENTIAL");

        ApprovalDetailResponse approvalDetailResponse1 = new ApprovalDetailResponse();
        approvalDetailResponse1.setApproverId("3422131");
        approvalDetailResponse1.setStatus("CREATED");
        ApprovalDetailResponse approvalDetailResponse2 = new ApprovalDetailResponse();
        approvalDetailResponse2.setApproverId("34434565");
        approvalDetailResponse2.setStatus("CREATED");

        approvalRequestResponse.setApprovalDetailResponse(Arrays.asList(approvalDetailResponse1, approvalDetailResponse2));
        approvalGroupResponse.setApprovalRequestResponse(approvalRequestResponse);

        return approvalGroupResponse;

    }

    private ApprovalGroupResponse approvalGroupResponseCustom(){
        ApprovalGroupResponse approvalGroupResponse = new ApprovalGroupResponse();
        approvalGroupResponse.setId("1a9e9f97-1375-4763-b409-2d60ed946b6f");
        approvalGroupResponse.setStatus("NOT_APPROVED");
        approvalGroupResponse.setEntityId("1234");
        approvalGroupResponse.setEntityType("INVOICE");
        ApprovalRequestResponse approvalRequestResponse = new ApprovalRequestResponse();
        approvalRequestResponse.setId("74bcc4b4-78a0-42fc-915e-9f1dc870f664");
        approvalRequestResponse.setStatus("CREATED");
        approvalRequestResponse.setType("SEQUENTIAL");
        approvalGroupResponse.setApprovalRequestResponse(approvalRequestResponse);

        return approvalGroupResponse;

    }
}
