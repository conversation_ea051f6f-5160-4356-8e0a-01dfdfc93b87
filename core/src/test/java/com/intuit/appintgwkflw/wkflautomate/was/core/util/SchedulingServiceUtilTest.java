package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingError;
import com.intuit.async.execution.request.State;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.joda.time.LocalDate;
import org.json.JSONObject;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/** <AUTHOR> */
public class SchedulingServiceUtilTest {
    @Mock
    private ActionModelToScheduleRequestMapper mapper;
    private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customScheduledActionsTest2.bpmn");
    private static final String CUSTOM_WORKFLOW_REMINDER_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");
    private static final String CUSTOM_PLACEHOLDER_VALUES_PATH = "placeholder/custom_placeholder_value_invoice_with_recurrence.json";
    private static final String CUSTOM_PLACEHOLDER_VALUES_WITHOUT_RECURRENCE = "placeholder/custom_placeholder_value_invoice_without_recurrence.json";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test_getSchedulingSvcRequestsPayload_success() {
        List<EventScheduleWorkflowActionModel> models = new ArrayList<>();
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        models.add(eventScheduleWorkflowActionModel);

        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);

        List<SchedulingSvcRequest> result = SchedulingServiceUtil.getSchedulingSvcRequestsPayload(models, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), new State());

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void test_getSchedulingSvcRequestsPayload_emptyList() {
        List<EventScheduleWorkflowActionModel> models = Collections.emptyList();

        List<SchedulingSvcRequest> result = SchedulingServiceUtil.getSchedulingSvcRequestsPayload(models, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").status(Status.ACTIVE).build(), new State());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_success() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);

        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), new State());

        assertNotNull(result);
        assertEquals(result.getUseCase(), "workflow-scheduling");
        assertEquals("definitionKey:customReminder_customStart", result.getReferenceId());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_nullRecurrence() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), null);
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);
        State state = new State();
        state.addValue(AsyncTaskConstants.IS_ESS_TO_SCHEDULING_MIGRATION, true);
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "736817263871283713");

        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), state);

        assertNotNull(result);
        assertEquals(true, request.getMigration());
        assertEquals(result.getUseCase(), "workflow-scheduling");
        assertEquals("definitionKey:customReminder_customStart", result.getReferenceId());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_successNullZoneId() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        ScheduleInfo scheduleInfo = new ScheduleInfo();
        request.setScheduleInfo(scheduleInfo);
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);
        State state = new State();
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "23468263");
        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), state);

        assertNotNull(result);
        assertEquals(request.getScheduleInfo().getZoneId().toString(), "America/Los_Angeles");
    }


    @Test
    public void test_getSchedulingSvcRequestPayload_successEmptyScheduleInfo() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);

        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), new State());

        assertNotNull(result);
        assertEquals("definitionKey:customReminder_customStart", result.getReferenceId());
        verify(mapper, times(0)).addStartDateFromActionModel(any(), any());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_successEmptyStartDate() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        request.setScheduleInfo(new ScheduleInfo());
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);

        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), new State());

        assertNotNull(result);
        assertEquals("definitionKey:customReminder_customStart", result.getReferenceId());
        verify(mapper, times(1)).addStartDateFromActionModel(any(), any());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_successForMigration() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        ScheduleInfo scheduleInfo = new ScheduleInfo();
        request.setScheduleInfo(scheduleInfo);
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);
        State state = new State();
        state.addValue(AsyncTaskConstants.IS_ESS_TO_SCHEDULING_MIGRATION, true);
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "23468263");
        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), state);

        assertNotNull(result);
        assertTrue(result.getMigration());
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_AfterMigrationRecurrenceNull() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);
        State state = new State();
        state.addValue(AsyncTaskConstants.IS_MIGRATED_TO_SCHEDULING, true);
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "23468263");
        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), state);

        assertNotNull(result);
    }

    @Test
    public void test_getSchedulingSvcRequestPayload_AfterMigrationRecurrenceNonNull() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        ScheduleInfo scheduleInfo = new ScheduleInfo();
        request.setScheduleInfo(scheduleInfo);
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);
        State state = new State();
        state.addValue(AsyncTaskConstants.IS_MIGRATED_TO_SCHEDULING, true);
        state.addValue(AsyncTaskConstants.REALM_ID_KEY, "23468263");
        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayload(eventScheduleWorkflowActionModel, mapper, SchedulingMetaData.builder().definitionKey("definitionKey").workflowName("customScheduledActions").status(Status.ACTIVE).build(), state);

        assertNotNull(result);
    }

    @Test
    public void test_getSchedulingSvcRequestForPartialUpdatePayload_success() {
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(mapper.convertToScheduleRequest(any(), any())).thenReturn(request);

        SchedulingSvcRequest result = SchedulingServiceUtil.getSchedulingSvcRequestPayloadForPartialUpdate(eventScheduleWorkflowActionModel, mapper, "definitionKey", Status.ACTIVE);

        assertNotNull(result);
        assertEquals("definitionKey:customReminder_customStart", result.getReferenceId());
    }


    @Test
    public void test_getScheduleIdsForSchedules_success() {
        List<EventScheduleWorkflowActionModel> models = new ArrayList<>();
        EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel =
                new EventScheduleWorkflowActionModel("customReminder_customStart", new LocalDate("2099-1-2"), new RecurrenceRule());
        models.add(eventScheduleWorkflowActionModel);

        List<String> result = SchedulingServiceUtil.getScheduleIds(models, "definitionKey");

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void test_getScheduleIdsForSchedules_emptyList() {
        List<EventScheduleWorkflowActionModel> models = Collections.emptyList();

        List<String> result = SchedulingServiceUtil.getScheduleIds(models, "definitionKey");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_getScheduleIdsForSchedulesWithWorkflowName(){
        List<String> result = SchedulingServiceUtil.getScheduleIds("key", "customScheduledActions");
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("key:customScheduledActions_customStart"));
    }

    @Test
    public void test_getFFName(){
        String result = SchedulingServiceUtil.getFFName("customScheduledActions");
        assertNotNull(result);
        assertEquals("SBSEG-wkflatmnsvc-invoke-scheduling-flow-customScheduledActions", result);
    }

    @Test
    public void testGetStatusFromWorkflowStatusEnum() {
        assertEquals(Status.ACTIVE, SchedulingServiceUtil.getStatus(WorkflowStatusEnum.ENABLED));
        assertEquals(Status.INACTIVE, SchedulingServiceUtil.getStatus(WorkflowStatusEnum.DISABLED));
    }

    @Test
    public void testGetStatusFromEntityStatus() {
        assertEquals(Status.ACTIVE, SchedulingServiceUtil.getStatus(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED));
        assertEquals(Status.INACTIVE, SchedulingServiceUtil.getStatus(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.DISABLED));
    }

    @Test
    public void testGetSchedulingMetaDataFromDefinition() {
        Definition definition = Mockito.mock(Definition.class);
        Template template = new Template();
        template.setName("name");
        when(definition.getName()).thenReturn("name");
        Mockito.when(definition.getDefinitionKey()).thenReturn("defKey");
        Mockito.when(definition.getId()).thenReturn(GlobalId.builder().setLocalId("id").build());
        Mockito.when(definition.getTemplate()).thenReturn(template);

        SchedulingMetaData metaData = SchedulingServiceUtil.getSchedulingMetaData(definition, Status.ACTIVE);

        assertEquals("defKey", metaData.getDefinitionKey());
        assertEquals(Status.ACTIVE, metaData.getStatus());
        assertEquals("name", metaData.getWorkflowName());
    }

    @Test
    public void testGetSchedulingMetaDataFromDefinitionDetails() {
        DefinitionDetails definitionDetails = Mockito.mock(DefinitionDetails.class);
        Mockito.when(definitionDetails.getDefinitionKey()).thenReturn("defKey");
        TemplateDetails templateDetails = Mockito.mock(TemplateDetails.class);
        Mockito.when(templateDetails.getTemplateName()).thenReturn("templateName");
        Mockito.when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
        SchedulingMetaData metaData = SchedulingServiceUtil.getSchedulingMetaData(definitionDetails, Status.ACTIVE);

        assertEquals("defKey", metaData.getDefinitionKey());
        assertEquals(Status.ACTIVE, metaData.getStatus());
        assertEquals("templateName", metaData.getWorkflowName());
    }

    @Test
    public void testIsSchedulingFlowEnabled_RecurrenceWithPlaceholderValuesNull() {
        assertFalse(SchedulingServiceUtil.isSchedulingFlowEnabled((String) null));
    }

    @Test
    public void testIsSchedulingFlowEnabled_RecurrenceWithPlaceholderValuesUserVariablesNull() {
        assertFalse(SchedulingServiceUtil.isSchedulingFlowEnabled("{\"" + WorkflowConstants.USER_VARIABLES + "\": null}"));
    }

    @Test
    public void testIsSchedulingFlowEnabled_RecurrenceWithPlaceholderValuesEmptyUserVariables() {
        assertFalse(SchedulingServiceUtil.isSchedulingFlowEnabled("{\"" + WorkflowConstants.USER_VARIABLES + "\": {}}"));
    }

    @Test
    public void testIsSchedulingFlowEnabled_RecurrenceWithPlaceholderValuesRecurrence() {
        JSONObject userVariables = new JSONObject();
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        recurrenceRule.setTimeZone("UTC");
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(5);
        timeDuration.setMinutes(45);
        recurrenceRule.setRecurrenceTime(timeDuration);
        userVariables.put(WorkFlowVariables.RECURRENCE_RULE_KEY.getName(), ObjectConverter.toJson(recurrenceRule));

        String placeholderValues = new JSONObject()
                .put(WorkflowConstants.USER_VARIABLES, userVariables)
                .toString();
        assertTrue(SchedulingServiceUtil.isSchedulingFlowEnabled(placeholderValues));
    }

    @Test
    public void testGetSchedulingError_validError() {
        String error = "Error={\"errorCode\":\"INVALID_START_DATE_TIME\",\"message\":\"Error while validating start date time\",\"statusCode\":400,\"statusName\":\"BAD_REQUEST\"}";

        SchedulingError result = SchedulingServiceUtil.getSchedulingError(error);

        assertNotNull(result);
        assertEquals("INVALID_START_DATE_TIME", result.getErrorCode());
    }

    @Test
    public void testGetSchedulingError_invalidError() {
        String error = "Invalid error format";
        SchedulingError result = SchedulingServiceUtil.getSchedulingError(error);
        assertNull(result);
    }

    @Test
    public void testGetSchedulingError_nullError() {
        String error = null;
        SchedulingError result = SchedulingServiceUtil.getSchedulingError(error);
        assertNull(result);
    }

    @Test
    public void testGetSchedulingError_emptyError() {
        String error = "";
        SchedulingError result = SchedulingServiceUtil.getSchedulingError(error);
        assertNull(result);
    }

    @Test
    public void testGetUpdateDefinitionDataWithTime() {
        byte[] result = SchedulingServiceUtil.getUpdateDefinitionDataWithTime(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes(), "1234");

        assertNotNull(result);
        assertTrue(verifyRecurrenceTimeInData(result, "1234"));
    }

    @Test
    public void testGetUpdateDefinitionDataWithTimeForReminder() {
        byte[] result = SchedulingServiceUtil.getUpdateDefinitionDataWithTime(CUSTOM_WORKFLOW_REMINDER_BPMN_XML.getBytes(), "1234");

        assertNotNull(result);
        assertTrue(verifyRecurrenceTimeInData(result, "1234"));
    }

    @Test
    public void testGetUpdatedPlaceholderValuesWithTime() {
        String placeholderValue = TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH);
        String result = SchedulingServiceUtil.getUpdatedPlaceholderValuesWithTime(placeholderValue, "1234");

        assertNotNull(result);
        assertTrue(verifyPlaceholderValues(result, "1234"));
    }

    @Test
    public void testGetUpdatedPlaceholderValuesWithTimeReminder() {
        String placeholderValue = TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_WITHOUT_RECURRENCE);
        String result = SchedulingServiceUtil.getUpdatedPlaceholderValuesWithTime(placeholderValue, "1234");

        assertNotNull(result);
        assertTrue(verifyPlaceholderValues(result, "1234"));
    }

    @Test
    public void testGetTime() {
        LocalTime time = SchedulingServiceUtil.getTime("12343");
        assertEquals(LocalTime.of(8, 45), time);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withValidRecurrenceRule() {
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(10);
        timeDuration.setMinutes(30);
        recurrenceRule.setTimeZone("Asia/Calcutta");
        recurrenceRule.setRecurrenceTime(timeDuration);

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertTrue(result);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withEmptyTimeZone() {
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(10);
        timeDuration.setMinutes(30);
        recurrenceRule.setRecurrenceTime(timeDuration);

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertTrue(result);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withNullRecurrenceRule() {
        RecurrenceRule recurrenceRule = null;

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertFalse(result);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withEmptyRecurrenceTime() {
        RecurrenceRule recurrenceRule = new RecurrenceRule();

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertFalse(result);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withEmptyHours() {
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setMinutes(30);
        recurrenceRule.setRecurrenceTime(timeDuration);

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertFalse(result);
    }

    @Test
    public void testIsSchedulingFlowEnabled_withEmptyMinutes() {
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        TimeDuration timeDuration = new TimeDuration();
        timeDuration.setHours(10);
        recurrenceRule.setRecurrenceTime(timeDuration);

        boolean result = SchedulingServiceUtil.isSchedulingFlowEnabled(recurrenceRule);
        assertFalse(result);
    }

    private boolean verifyRecurrenceTimeInData(byte[] data, String realmId){
        int[] HOURS_BUCKET = {7, 8, 9};
        int[] MINUTES_BUCKET = {0, 15, 30, 45};
        int id = Integer.parseInt(realmId);
        int hours = HOURS_BUCKET[id % HOURS_BUCKET.length];
        int minutes = MINUTES_BUCKET[id % MINUTES_BUCKET.length];
        BpmnModelInstance modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(data));
        FlowElement startEventElement = BpmnProcessorUtil.findStartEventElement(modelInstance);
        Optional<CamundaProperty> camundaPropertyOptional =
                BpmnProcessorUtil.getCamundaProperty(
                        startEventElement, WorkFlowVariables.RECURRENCE_RULE_KEY.getName());

        if (camundaPropertyOptional.isPresent()) {
            JSONObject recurrenceRuleJson =
                    ObjectConverter.convertObject(
                            camundaPropertyOptional.get().getCamundaValue(), JSONObject.class);
            RecurrenceRule recurrenceRule = RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson);
            return !ObjectUtils.isEmpty(recurrenceRule.getRecurrenceTime()) && recurrenceRule.getRecurrenceTime().getHours() == hours && recurrenceRule.getRecurrenceTime().getMinutes() == minutes && ObjectUtils.isEmpty(recurrenceRule.getTimeZone());
        }
        return false;
    }

    private boolean verifyPlaceholderValues(String placeholderValue, String realmId){
        int[] HOURS_BUCKET = {7, 8, 9};
        int[] MINUTES_BUCKET = {0, 15, 30, 45};
        int id = Integer.parseInt(realmId);
        int hours = HOURS_BUCKET[id % HOURS_BUCKET.length];
        int minutes = MINUTES_BUCKET[id % MINUTES_BUCKET.length];
        JSONObject placeholderValueJson = new JSONObject(placeholderValue);
        JSONObject userVariables = placeholderValueJson.optJSONObject(WorkflowConstants.USER_VARIABLES);
        JSONObject recurrenceRuleJson = ObjectConverter.convertObject(userVariables.get(WorkFlowVariables.RECURRENCE_RULE_KEY.getName()), JSONObject.class);
        RecurrenceRule recurrenceRule = RecurrenceParserUtil.toRecurrenceRule(recurrenceRuleJson);
        return !ObjectUtils.isEmpty(recurrenceRule.getRecurrenceTime()) && recurrenceRule.getRecurrenceTime().getHours() == hours && recurrenceRule.getRecurrenceTime().getMinutes() == minutes && ObjectUtils.isEmpty(recurrenceRule.getTimeZone());
    }
}
