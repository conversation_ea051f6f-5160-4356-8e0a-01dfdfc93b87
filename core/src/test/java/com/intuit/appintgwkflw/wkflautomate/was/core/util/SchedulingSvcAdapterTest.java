package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.SchedulingSvcClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.Collections;
import java.util.List;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class SchedulingSvcAdapterTest {
    @Mock
    private SchedulingSvcConfig schedulingSvcConfig;

    @Mock
    private SchedulingSvcClient schedulingSvcClient;

    @Mock
    private HeaderPopulator headerPopulator;

    @Mock
    private WASContextHandler wasContextHandler;

    @Mock
    private OfflineTicketClient offlineTicketClient;

    @InjectMocks
    private SchedulingSvcAdapter schedulingSvcAdapter;

    @Before
    public void setUp() {
        when(schedulingSvcConfig.getBaseUrl())
                .thenReturn("https://schedulingsvc-qal.api.intuit.com/v1/schedule");
        when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    }

    @Test
    public void test_invokeScheduleServiceForCreate() {
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(schedulingSvcResponse)
                .isSuccess2xx(true)
                .build());

        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(
                Collections.singletonList(request), false, "realm");

        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(schedulingSvcClient, times(1)).httpResponse(any(WASHttpRequest.class));
    }

    @Test
    public void createSchedulesWithBadRequestError() {

        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.BAD_REQUEST)
                .response(null)
                .isSuccess2xx(false)
                .error("Error={\"errorCode\":\"INVALID_START_DATE_TIME\",\"message\":\"Error while validating start date time\",\"statusCode\":400,\"statusName\":\"BAD_REQUEST\"}")
                .build());

        try {
            schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(
                    Collections.singletonList(request), false, "realm");
            Assert.fail();
        } catch (WorkflowGeneralException workflowGeneralException) {
            assertEquals(WorkflowError.SCHEDULING_SERVICE_BAD_REQUEST_FAILURE, workflowGeneralException.getWorkflowError());
            Assert.assertEquals(
                    "SCHEDULING_ERROR_INVALID_START_DATE_TIME", workflowGeneralException.getError().getMessage());
            Assert.assertEquals(
                    "Bad request occurred in Scheduling Service.Error=Error while validating start date time", workflowGeneralException.getError().getCode());
        }
    }

    @Test
    public void createSchedulesWithGenericError() {

        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .response(null)
                .isSuccess2xx(false)
                .error("Error={\"errorCode\":\"INVALID_START_DATE_TIME\",\"message\":\"Error while validating start date time\",\"statusCode\":400,\"statusName\":\"BAD_REQUEST\"}")
                .build());

        try {
            schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(
                    Collections.singletonList(request), false, "realm");
            Assert.fail();
        } catch (WorkflowGeneralException workflowGeneralException) {
            assertEquals(WorkflowError.SCHEDULING_SERVICE_CALL_FAILURE, workflowGeneralException.getWorkflowError());
            Assert.assertEquals(
                    "INVALID_START_DATE_TIME", workflowGeneralException.getError().getMessage());
        }
    }

    @Test
    public void createSchedulesWithNullResponse() {

        SchedulingSvcRequest request = new SchedulingSvcRequest();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(null)
                .isSuccess2xx(true)
                .error(null)
                .build());

        try {
            schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(
                    Collections.singletonList(request), false, "realm");
            Assert.fail();
        } catch (WorkflowGeneralException workflowGeneralException) {
            assertEquals(WorkflowError.SCHEDULING_SERVICE_CALL_FAILURE, workflowGeneralException.getWorkflowError());
            Assert.assertEquals(
                    "W0065", workflowGeneralException.getError().getMessage());
            Assert.assertEquals(
                    "Some unknown error occured", workflowGeneralException.getError().getCode());
        }
    }

    @Test
    public void test_invokeScheduleServiceForUpdate() {
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(schedulingSvcResponse)
                .isSuccess2xx(true)
                .build());

        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForCreateUpdate(
                Collections.singletonList(request), true, "realm");

        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(schedulingSvcClient, times(1)).httpResponse(any(WASHttpRequest.class));
    }

    @Test
    public void test_invokeScheduleServiceForGet() {
        SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(schedulingSvcResponse)
                .isSuccess2xx(true)
                .build());

        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForGet(
                Collections.singletonList("scheduleId"), "realm");

        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(schedulingSvcClient, times(1)).httpResponse(any(WASHttpRequest.class));
    }

    @Test
    public void test_invokeScheduleServiceForDelete() {
        SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
        when(schedulingSvcClient.httpResponse(any())).thenReturn(WASHttpResponse.builder()
                .status(HttpStatus.OK)
                .response(schedulingSvcResponse)
                .isSuccess2xx(true)
                .build());

        List<SchedulingSvcResponse> responses = schedulingSvcAdapter.invokeScheduleServiceForDelete(
                Collections.singletonList("scheduleId"), "realm");

        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(schedulingSvcClient, times(1)).httpResponse(any(WASHttpRequest.class));
    }

    @Test
    public void test_prepareRequestPayloadForCreateUpdate() {
        SchedulingSvcRequest request = new SchedulingSvcRequest();
        WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> httpRequest =
                schedulingSvcAdapter.prepareRequestPayloadForCreateUpdate(request,  false, "realm");

        assertNotNull(httpRequest);
        assertEquals(HttpMethod.POST, httpRequest.getHttpMethod());
        assertEquals("https://schedulingsvc-qal.api.intuit.com/v1/schedule", httpRequest.getUrl());
        assertEquals(request, httpRequest.getRequest());
        assertEquals(MediaType.APPLICATION_JSON, httpRequest.getRequestHeaders().getContentType());
    }

    @Test
    public void test_prepareRequestPayloadForGet() {
        WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> httpRequest =
                schedulingSvcAdapter.prepareRequestPayloadForGet("scheduleId", "realm");

        assertNotNull(httpRequest);
        assertEquals(HttpMethod.GET, httpRequest.getHttpMethod());
        assertEquals("https://schedulingsvc-qal.api.intuit.com/v1/schedule/scheduleId", httpRequest.getUrl());
        assertEquals(MediaType.APPLICATION_JSON, httpRequest.getRequestHeaders().getContentType());
    }

    @Test
    public void test_prepareRequestPayloadForDelete() {
        WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> httpRequest =
                schedulingSvcAdapter.prepareRequestPayloadForDelete("scheduleId", "realm");
        assertNotNull(httpRequest);
        assertEquals(HttpMethod.DELETE, httpRequest.getHttpMethod());
        assertEquals("https://schedulingsvc-qal.api.intuit.com/v1/schedule/scheduleId", httpRequest.getUrl());
        assertEquals(MediaType.APPLICATION_JSON, httpRequest.getRequestHeaders().getContentType());
    }

    @Test
    public void test_prepareHttpHeadersForOffline() {
        Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("realm")).thenReturn("ticket");
        HttpHeaders headers = schedulingSvcAdapter.prepareHttpHeaders("realm");
        assertNotNull(headers);
        assertEquals(MediaType.APPLICATION_JSON, headers.getContentType());
        assertEquals("ticket", headers.getFirst(HttpHeaders.AUTHORIZATION));
        assertEquals("tid", headers.getFirst(INTUIT_TID));
    }


}
