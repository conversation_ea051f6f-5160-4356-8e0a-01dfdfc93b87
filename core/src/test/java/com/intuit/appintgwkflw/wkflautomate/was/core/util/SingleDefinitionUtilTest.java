package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.*;
import com.intuit.v4.workflows.definitions.InputParameter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Class to hold UTs for SingleDefinitionUtil.
 */
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class SingleDefinitionUtilTest {

    @Mock private TemplateDetails templateDetails;

    @Mock private FeatureFlagManager featureFlagManager;

    private Definition definition;
    private Authorization authorization;
    private DefinitionDetails definitionDetails;
    private Template template;

    @Before
    public void setUp() {
        definition  = TestHelper.mockDefinitionEntity();
        definition.setId(TestHelper.getGlobalId(DefinitionTestConstants.DEF_ID));
        authorization = new Authorization();
        authorization.putRealm("12345");
        definitionDetails = TestHelper.mockDefinitionDetails(definition, templateDetails,
                authorization);
        template = TestHelper.mockTemplateEntity();
    }

    @Test
    public void testisSingleDefPrecannedCreateAllowed(){
        template.setName("invoiceApproval");
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.SINGLE_DEF_WORKFLOW_CREATE_PRECANNED, false, template.getName()))
            .thenReturn(true);
        Assert.assertTrue(SingleDefinitionUtil.isSingleDefPrecannedCreateAllowed(featureFlagManager, template));
    }

    @Test
    public void testisSingleDefPrecannedCreateAllowedWithCustomTemplate(){
        template.setName("customReminder");
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.SINGLE_DEF_WORKFLOW_CREATE_PRECANNED, false, template.getName()))
            .thenReturn(true);
        Assert.assertFalse(SingleDefinitionUtil.isSingleDefPrecannedCreateAllowed(featureFlagManager, template));
    }

  @Test
  public void testOnDemandApproval(){
    template.setName("customApproval");
    definitionDetails.setOwnerId(Long.valueOf(WorkflowConstants.SYSTEM_OWNER_ID));
    definitionDetails.setPlaceholderValue(null);
    definitionDetails.setRecordType(null);
    Assert.assertTrue(SingleDefinitionUtil.isSingleDefCustomTriggerFeasible(template, definitionDetails));
  }

    @Test
    public void testisSingleDefPrecannedCreateAllowedWithFFOff(){
        template.setName("invoiceApproval");
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.SINGLE_DEF_WORKFLOW_CREATE_PRECANNED, false, template.getName()))
            .thenReturn(false);
        Assert.assertFalse(SingleDefinitionUtil.isSingleDefPrecannedCreateAllowed(featureFlagManager, template));
    }

    @Test
    public void testisSingleDefCustomTriggerFeasible(){
        setContext();
        template.setName("customReminder");
        TemplateDetails tempDetails = TemplateDetails.builder().templateName("customReminder").build();
        definitionDetails.setTemplateDetails(tempDetails);
        Assert.assertTrue(SingleDefinitionUtil.isSingleDefCustomTriggerFeasible(template, definitionDetails));
        WASContext.clear();
    }

    @Test
    public void testisSingleDefCustomTriggerFeasibleWithoutCustomTemplate(){
        template.setName("invoiceApproval");
        TemplateDetails tempDetails = TemplateDetails.builder().templateName("invoiceApproval").build();
        definitionDetails.setTemplateDetails(tempDetails);
        Assert.assertFalse(SingleDefinitionUtil.isSingleDefCustomTriggerFeasible(template, definitionDetails));
    }

    @Test
    public void isisSingleDefPrecannedTriggerFeasible(){
        setContext();
        template.setName("invoiceApproval");
        TemplateDetails tempDetails = TemplateDetails.builder().templateName("invoiceApproval").build();
        definitionDetails.setTemplateDetails(tempDetails);
        Assert.assertTrue(SingleDefinitionUtil.isSingleDefPrecannedTriggerFeasible(template, definitionDetails));
        WASContext.clear();
    }

    @Test
    public void isisSingleDefPrecannedTriggerFeasibleWithCustomTemplate(){
        template.setName("customReminder");
        TemplateDetails tempDetails = TemplateDetails.builder().templateName("customReminder").build();
        definitionDetails.setTemplateDetails(tempDetails);
        Assert.assertFalse(SingleDefinitionUtil.isSingleDefPrecannedTriggerFeasible(template, definitionDetails));
    }

    private void setContext() {
        Authorization authorization = new Authorization();
        authorization.putRealm(Long.toString(definitionDetails.getOwnerId()));
        WASContext.setAuthContext(authorization);
    }

  @Test
  public void testGetRuleLinesForWorkflowCondition() {
    WorkflowStepCondition condition = new WorkflowStepCondition();
    RuleLine ruleLine = new RuleLine();
    List<RuleLine> ruleLines = new ArrayList<>();
    ruleLines.add(ruleLine);
    condition.setRuleLines(ruleLines);
    try {
      Assert.assertNotNull(SingleDefinitionUtil.getRuleLinesForWorkflowCondition(definition));
    } catch (Exception e) {
      Assert.fail("The Above method should not throw exception");
    }
  }

    @Test
    public void testGetRuleLinesForWorkflowConditionNull() {
        try {
           Assert.assertEquals(0,SingleDefinitionUtil.getRuleLinesForWorkflowCondition(null).size());
        } catch (Exception e) {
            Assert.fail("The Above method should not throw exception");
        }
    }

    @Test
    public void testActionPlaceholderValue() {
        try {
            definitionDetails.setPlaceholderValue("{\n"
                + "\t\"user_variables\": {\n"
                + "\t\t\"reminder:createTask\": {\n"
                + "\n"
                + "\t\t}\n"
                + "\n"
                + "\t}\n"
                + "}");
            Assert.assertNotNull(SingleDefinitionUtil.getActionPlaceholderValue(definitionDetails, "reminder", "createTask"));
        } catch (Exception e) {
            Assert.fail("The Above method should not throw exception");
        }
    }

    @Test
    public void testgetLocaleFromPlaceHolderValuesWithNullPlaceholder() {
        definitionDetails.setPlaceholderValue(null);
        Assert.assertNull(SingleDefinitionUtil.getLocaleFromPlaceHolderValues(definitionDetails));
    }

    @Test
    public void test_getTriggerVariables() {
        Trigger trigger = new Trigger();
        InputParameter parameter = new InputParameter();
        parameter.setParameterName("entityOperation");
        parameter.setFieldValues(0, "create");
        parameter.setFieldValues(1, "update");
        trigger.add("parameters", List.of(parameter));
        Map<String, Object> triggerVariables = SingleDefinitionUtil.getTriggerVariables(trigger);
        Assert.assertNotNull(triggerVariables);
        Assert.assertTrue(triggerVariables.containsKey("parameters"));
        Assert.assertEquals(2, triggerVariables.size());
        Map<String, List<String>> parameters = (Map<String, List<String>>) triggerVariables.get("parameters");
        Assert.assertTrue(parameters.containsKey("entityOperation"));
        List<String> operations = ((Map<String, List<String>>) parameters.get("entityOperation")).get(("fieldValue"));
        Assert.assertTrue(operations.contains("create,update"));
    }

}
