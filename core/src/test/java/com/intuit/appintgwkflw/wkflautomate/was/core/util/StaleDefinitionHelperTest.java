package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;


public class StaleDefinitionHelperTest {

    @Mock
    private ProcessDetailsRepository processDetailsRepository;

    @Mock
    private DefinitionDetailsRepository definitionDetailsRepository;

    @Mock
    private DataStoreDeleteTaskService mockDataStoreDeleteTaskService;

    @Mock
    private HistoryConfig historyConfig;

    @InjectMocks
    private StaleDefinitionHelper staleDefinitionHelper;

    private final DefinitionDetails definitionDetails = buildDefinitionDetails();

    private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customScheduledActionsTest2.bpmn");

    @Before
    public void init() {
        historyConfig = new HistoryConfig();
        historyConfig.setTtl(30);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @DisplayName("Delete Stale Definition and Ended Process")
    public void shouldDeleteEndedProcessesAndStaleDefinition() {

        List<String> endedProcessIds = Arrays.asList("process1", "process2", "process3");

        when(processDetailsRepository.findEndedProcessesModifiedBefore(any(), any(Timestamp.class)))
                .thenReturn(Optional.of(endedProcessIds.stream().map(id -> new ProcessDetails()).collect(Collectors.toList())));

        when(definitionDetailsRepository.findTopByDefinitionKeyOrderByVersionDesc(any()))
                .thenReturn(buildDefinitionDetails());

        when(definitionDetailsRepository.findByParentId("123"))
                .thenReturn(Optional.of(buildDMNDefinitionDetails()));

        staleDefinitionHelper.deleteStaleDefinitionAndEndedProcesses(definitionDetails);

        Mockito.verify(mockDataStoreDeleteTaskService, never()).deleteProcess(anyList());
        Mockito.verify(mockDataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(Collections.singletonList(definitionDetails));
    }

    @Test
    @DisplayName("Delete Stale Definition When no Process is existing")
    public void shouldDeleteStaleDefinitions() {

        when(processDetailsRepository.findEndedProcessesModifiedBefore(any(), any(Timestamp.class)))
                .thenReturn(Optional.of(Collections.emptyList()));

        when(definitionDetailsRepository.findTopByDefinitionKeyOrderByVersionDesc(any()))
                .thenReturn(buildDefinitionDetails());

        when(definitionDetailsRepository.findByParentId("123"))
                .thenReturn(Optional.of(buildDMNDefinitionDetails()));

        staleDefinitionHelper.deleteStaleDefinitionAndEndedProcesses(definitionDetails);

        Mockito.verify(mockDataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(Collections.singletonList(definitionDetails));
    }

    @Test
    @DisplayName("Stale Definition should not delete as active Process is present")
    public void testDeleteEndedProcessAndStaleDefinitionsActiveProcessesExist() {

        when(processDetailsRepository.findEndedProcessesModifiedBefore(any(), any(Timestamp.class)))
                .thenReturn(Optional.of(List.of(new ProcessDetails())));

        when(processDetailsRepository.countByDefinitionDetails(any())).thenReturn(1L);

        when(definitionDetailsRepository.findTopByDefinitionKeyOrderByVersionDesc(any()))
                .thenReturn(buildDefinitionDetails());

        when(definitionDetailsRepository.findByParentId("123"))
                .thenReturn(Optional.of(buildDMNDefinitionDetails()));

        staleDefinitionHelper.deleteStaleDefinitionAndEndedProcesses(definitionDetails);

//        Mockito.verify(mockDataStoreDeleteTaskService, Mockito.times(1)).deleteProcess(anyList());
        Mockito.verify(mockDataStoreDeleteTaskService, never()).deleteDefinitions(anyList());
    }

    @Test
    @DisplayName("Should not update Original Setup Date as its present")
    public void testUpdateOriginalSetupDate() {

        when(processDetailsRepository.findEndedProcessesModifiedBefore(any(), any(Timestamp.class)))
                .thenReturn(Optional.of(List.of(new ProcessDetails())));

        when(processDetailsRepository.countByDefinitionDetails(any())).thenReturn(1L);

        when(definitionDetailsRepository.findTopByDefinitionKeyOrderByVersionDesc(any()))
                .thenReturn(buildDefinitionDetails());

        when(definitionDetailsRepository.findByParentId("123"))
                .thenReturn(Optional.of(buildDMNDefinitionDetails()));

        staleDefinitionHelper.deleteStaleDefinitionAndEndedProcesses(definitionDetails);

        Mockito.verify(definitionDetailsRepository, never()).findByParentId(any());
    }

    private DefinitionDetails buildDefinitionDetails() {

        DefinitionDetails definitionDetail = new DefinitionDetails();
        definitionDetail.setDefinitionId("353b0efa-cd76-4fbf-9b5b-dd68813b08da");
        definitionDetail.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        definitionDetail.setOriginalSetupUser(123L);
        definitionDetail.setVersion(0);
        definitionDetail.setDefinitionName("statement");
        definitionDetail.setModelType(ModelType.BPMN);
        definitionDetail.setRecordType(RecordType.STATEMENT);
        definitionDetail.setStatus(Status.ENABLED);
        definitionDetail.setOriginalSetupDate(Timestamp.valueOf(LocalDateTime.now()));
        definitionDetail.setDescription("Description");
        definitionDetail.setDefinitionName("displayName");
        definitionDetail.setDefinitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
        return definitionDetail;
    }

    private List<DefinitionDetails> buildDMNDefinitionDetails() {

        DefinitionDetails definitionDetail = new DefinitionDetails();
        definitionDetail.setDefinitionId("353b0efa-cd76-4fbf-9b5b-dd68813b081234da");
        definitionDetail.setDefinitionName("statement");
        definitionDetail.setModelType(ModelType.DMN);
        definitionDetail.setRecordType(RecordType.STATEMENT);
        definitionDetail.setStatus(Status.ENABLED);
        definitionDetail.setDescription("Description");
        definitionDetail.setDefinitionName("displayName");
        definitionDetail.setDefinitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
        return List.of(definitionDetail);
    }

}