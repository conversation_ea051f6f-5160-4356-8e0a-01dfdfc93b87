package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Test;

public class StateTransitionServiceHelperTest {

  @Test
  public void populateActivityMetaData_success() throws Exception {
    String execId = "execId1";
    String activityId = "activityId";
    String processInstanceId = "4d71eb45-e342-11eb-9f49-fe807654b1d6";
    ExternalTaskDetail extTaskResp = new ExternalTaskDetail();
    extTaskResp.setExecutionId(execId);
    extTaskResp.setActivityId(activityId);
    extTaskResp.setProcessInstanceId(processInstanceId);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    String statusVariableMapJson = ObjectConverter.toJson(statusVariableMap);

    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("key1", "value1");
    variableMap.put(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE, statusVariableMapJson);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId(activityId).activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .build();

    ActivityMetaData actual = StateTransitionServiceHelper.populateActivityMetaData(
        activityProgressDetails, variableMap);

    Assert.assertNotNull(actual);
    Assert.assertEquals(ActivityConstants.SCOPE_ACTIVITY, actual.getScope());
    Assert.assertEquals(activityId, actual.getActivityId());
    Assert.assertEquals("actName", actual.getActivityName());
    Assert.assertNotNull(actual.getVariables());
    Assert.assertNotNull(actual.getVariables().get("key1"));
    Assert.assertNotNull(actual.getVariables().get(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE));
  }

  @SuppressWarnings("deprecation")
  @Test
  public void populateWorkflowMetaData_success() {
    String execId = "execId1";
    String processDefId = "4d71eb45-e342-11eb-9f49-fe807654b1d6";
    String activityInstanceId = "Activity_2:e46390db-e4c6-11eb-9f49-fe807654b1d5";
    ExternalTaskDetail extTaskDtlResponse = new ExternalTaskDetail();
    extTaskDtlResponse.setExecutionId(execId);
    extTaskDtlResponse.setProcessInstanceId(activityInstanceId);
    extTaskDtlResponse.setProcessDefinitionId(processDefId);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .recordType(RecordType.ENGAGEMENT)
                .definitionName("defName").version(1).build())
            .build())
        .build();

    WorkflowMetaData actual = StateTransitionServiceHelper
        .populateWorkflowMetaData(extTaskDtlResponse,
            activityProgressDetails);

    Assert.assertNotNull(actual);
    Assert.assertEquals(activityInstanceId, actual.getProcessInstanceId());
    Assert.assertEquals(processDefId, actual.getProcessDefinitionId());
    Assert.assertEquals("defName", actual.getWorkflowName());
    Assert.assertEquals(new Integer(1), actual.getWorkflowVersion());
  }


  @Test
  public void test_eventHeaderEntity() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID,
        "37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
    headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());
    EventHeaderEntity eventHeaderEntity = StateTransitionServiceHelper
        .eventHeaderEntity("exec1", headers);
    Assert.assertEquals(EventEntityType.WORKFLOW_TRANSITION_EVENTS,
        eventHeaderEntity.getEventEntityType());
    Assert.assertEquals(PublishEventType.WORKFLOW_TRANSITION_EVENTS,
        eventHeaderEntity.getPublishEventType());
    Assert.assertEquals("exec1", eventHeaderEntity.getIdempotencyKey());
  }

  @Test
  public void prepareWorkflowStateTransitionEvent() {

    String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
    ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
        ExternalTaskDetail.class);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    String statusVariableMapJson = ObjectConverter.toJson(statusVariableMap);

    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("key1", "value1");
    variableMap.put(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE, statusVariableMapJson);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l)
            .recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .recordType(RecordType.ENGAGEMENT)
                .definitionName("defName").version(1).build())
            .build())
        .build();

    WorkflowStateTransitionEvents event = StateTransitionServiceHelper
        .prepareWorkflowStateTransitionEvent(externalTaskDtl, activityProgressDetails,
            variableMap, ActivityConstants.TASK_EVENT_TYPE_UPDATE, Instant.now().toEpochMilli());
    Assert.assertEquals("serviceTask", event.getActivityType());
    Assert.assertEquals(ActivityConstants.TASK_EVENT_TYPE_UPDATE, event.getEventType());
    Assert.assertEquals("in-process", event.getStatus());
    Assert.assertEquals(RecordType.ENGAGEMENT.getRecordType(), event.getBusinessEntityType());
    Assert.assertEquals("rec1", event.getBusinessEntityId());
  }
  
  @Test
  public void prepareWorkflowStateTransitionEvent_noTimestamp() {

    String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
    ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
        ExternalTaskDetail.class);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    String statusVariableMapJson = ObjectConverter.toJson(statusVariableMap);

    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("key1", "value1");
    variableMap.put(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE, statusVariableMapJson);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l)
            .recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .recordType(RecordType.ENGAGEMENT)
                .definitionName("defName").version(1).build())
            .build())
        .build();

    WorkflowStateTransitionEvents event = StateTransitionServiceHelper
        .prepareWorkflowStateTransitionEvent(externalTaskDtl, activityProgressDetails,
            variableMap, ActivityConstants.TASK_EVENT_TYPE_UPDATE, null);
    Assert.assertEquals("serviceTask", event.getActivityType());
    Assert.assertEquals(ActivityConstants.TASK_EVENT_TYPE_UPDATE, event.getEventType());
    Assert.assertEquals("in-process", event.getStatus());
    Assert.assertEquals(RecordType.ENGAGEMENT.getRecordType(), event.getBusinessEntityType());
    Assert.assertEquals("rec1", event.getBusinessEntityId());
    Assert.assertNotNull(event.getTimestamp());
  }

  @Test
  public void prepareWorkflowStateTransitionEvent_emptyBusinessEntityType() {

    String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
    ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
        ExternalTaskDetail.class);

    Map<String, Object> statusVariableMap = new HashMap<>();
    statusVariableMap
        .put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
    statusVariableMap
        .put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, ExternalTaskStatus.BLOCKED.name());

    String statusVariableMapJson = ObjectConverter.toJson(statusVariableMap);

    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("key1", "value1");
    variableMap.put(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE, statusVariableMapJson);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l)
            .recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName").version(1).build())
            .build())
        .build();

    WorkflowStateTransitionEvents event = StateTransitionServiceHelper
        .prepareWorkflowStateTransitionEvent(externalTaskDtl, activityProgressDetails,
            variableMap, ActivityConstants.TASK_EVENT_TYPE_UPDATE, Instant.now().toEpochMilli());
    Assert.assertEquals("serviceTask", event.getActivityType());
    Assert.assertEquals(ActivityConstants.TASK_EVENT_TYPE_UPDATE, event.getEventType());
    Assert.assertEquals("in-process", event.getStatus());
    Assert.assertNull(event.getBusinessEntityType());
    Assert.assertEquals("rec1", event.getBusinessEntityId());
  }

  @Test
  public void test_isStateTransitionEventPublishEnabled_emptyList() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    boolean flag = StateTransitionServiceHelper
        .isStateTransitionEventPublishEnabled(ActivityConstants.TASK_STATUS_FAILED,
            modelDefAttributes);
    Assert.assertFalse(flag);
  }

  @Test
  public void test_isStateTransitionEventPublishEnabled_nullList() {
    boolean flag = StateTransitionServiceHelper
        .isStateTransitionEventPublishEnabled(ActivityConstants.TASK_STATUS_FAILED,
            null);
    Assert.assertFalse(flag);
  }

  @Test
  public void test_isStateTransitionEventPublishEnabled_noMatch() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"created\",\"created\",\"completed\",\"update\"]");
    boolean flag = StateTransitionServiceHelper
        .isStateTransitionEventPublishEnabled(ActivityConstants.TASK_STATUS_FAILED,
            modelDefAttributes);
    Assert.assertFalse(flag);
  }

  @Test
  public void test_isStateTransitionEventPublishEnabled_match_success() {
    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes
        .put(WorkFlowVariables.EVENTS.getName(), "[\"CREATED\",\"completed\",\"update\"]");

    boolean flag = StateTransitionServiceHelper
        .isStateTransitionEventPublishEnabled(ActivityConstants.TASK_STATUS_CREATED,
            modelDefAttributes);
    Assert.assertTrue(flag);
  }
}
