package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.text.StrSubstitutor;
import org.junit.Assert;
import org.junit.Test;

public class SubstitutePlaceholderUtilTest {

  @Test
  public void testPlaceHolderSubstitution1(){
    Map<String, String> input = new HashMap<>();
    input.put("TxnId", "1001");
    input.put("NotificationAction", "qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]");
    input.put("RealmId", "1234567");

    Map<String, String> output = SubstitutePlaceholderUtil.substitutePlaceholder(input, false);

    Assert.assertEquals("qb001://open/invoice/?id=1001&companyid=1234567", output.get("NotificationAction"));
    Assert.assertNotNull(output.get("TxnId"));
    Assert.assertNotNull(output.get("RealmId"));
  }

  @Test
  public void testPlaceHolderSubstitution2(){
    Map<String, String> input = new HashMap<>();
    input.put("TxnId", "1001");
    input.put("NotificationAction", "qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]");
    input.put("RealmId", "1234567");
    input.put("Action", "[[NotificationAction]]");

    Map<String, String> output = SubstitutePlaceholderUtil.substitutePlaceholder(input, false);

    Assert.assertEquals("qb001://open/invoice/?id=1001&companyid=1234567", output.get("NotificationAction"));
    Assert.assertEquals("qb001://open/invoice/?id=1001&companyid=1234567", output.get("Action"));
    Assert.assertNotNull(output.get("TxnId"));
    Assert.assertNotNull(output.get("RealmId"));
  }


  @Test
  public void testPlaceHolderSubstitution3(){
    Map<String, String> input = new HashMap<>();
    input.put("TxnId", "1001");
    input.put("RealmId", "1234567");
    input.put("1234567TxnId", "[[[[RealmId]][[TxnId]]]]");
    input.put("12345671001", "Hello Nested");

    Map<String, String> output = SubstitutePlaceholderUtil.substitutePlaceholder(input, true);

    Assert.assertEquals("Hello Nested", output.get("1234567TxnId"));
  }

  @Test
  public void testPlaceHolderSubstitution4_noSubstitution(){
    Map<String, String> input = new HashMap<>();
    input.put("TxnId", "1001");
    input.put("NotificationAction", "qb001://open/invoice/?id=121&companyid=[[RealmId1]]");
    input.put("RealmId", "1234567");

    Map<String, String> output = SubstitutePlaceholderUtil.substitutePlaceholder(input, false);

    Assert.assertEquals("qb001://open/invoice/?id=121&companyid=[[RealmId1]]", output.get("NotificationAction"));
    Assert.assertNotNull(output.get("TxnId"));
    Assert.assertNotNull(output.get("RealmId"));
  }

  @Test
  public void testPlaceHolderSubstitution5_nullInput(){
    Map<String, String> output = SubstitutePlaceholderUtil.substitutePlaceholder(null, false);
    Assert.assertTrue(output.isEmpty());
  }

}
