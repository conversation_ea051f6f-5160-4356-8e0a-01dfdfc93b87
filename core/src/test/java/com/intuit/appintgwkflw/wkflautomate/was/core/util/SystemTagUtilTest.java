package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.checkAndSetSystemTagObjectFromTemplate;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.getVersionFromTag;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.tagSemverVersionComparator;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.validateTemplateTagVersion;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import com.vdurmont.semver4j.SemverException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
public class SystemTagUtilTest {

  @Test(expected = SemverException.class)
  public void testValidateTemplateTagVersionInvalid() throws IOException {
    String version = "abc-2";
    validateTemplateTagVersion(version);
  }

  @Test
  public void testValidateTemplateTagVersionValid() throws IOException {
    String version = "1.2.3", versionWithNumeric = "1.2.3-alpha";
    validateTemplateTagVersion(version);
    assertEquals(version, "1.2.3");
    validateTemplateTagVersion(versionWithNumeric);
    assertEquals(versionWithNumeric, "1.2.3-alpha");
  }

  @Test
  public void testCheckAndSetSystemTagObjectFromTemplate() throws IOException {
    String versionString = "{\"system_tag\": {\"version\": \"1.2.3.4\"}}";
    SystemTags tag = new SystemTags();
    checkAndSetSystemTagObjectFromTemplate(tag, versionString);
    assertNotNull(tag);
    assertNotNull(tag.tagsMapInstance.get(SYSTEM_TAG));
    assertEquals(tag.getTag(SYSTEM_TAG).getVersion(), "1.2.3.4");
    versionString = StringUtils.EMPTY;
    SystemTags emptyTag = new SystemTags();
    checkAndSetSystemTagObjectFromTemplate(tag, versionString);
    assertEquals(ObjectUtils.isEmpty(emptyTag.tagsMapInstance), true);
  }

  @Test(expected = NullPointerException.class)
  public void testGetSystemTagWithEmpty() throws IOException {
    String versionString = "{\"system_tag\": {\"version\": \"1.2.3.4\"}}";
    String version = "1.2.3.4";
    assertEquals(getVersionFromTag(versionString), version);
    versionString = "";
    assertEquals(getVersionFromTag(versionString), StringUtils.EMPTY);
  }

  @Test
  public void testSemverComparator() {
    String firstSemver = "{\"system_tag\": {\"version\": \"1.2.3\"}}",
        secondSemver = "{\"system_tag\": {\"version\": \"1.2.1\"}}";
    assertEquals(tagSemverVersionComparator(firstSemver, secondSemver), -1);
    firstSemver = "{\"system_tag\": {\"version\": \"1.1.1\"}}";
    secondSemver = "{\"system_tag\": {\"version\": \"1.2.3\"}}";
    assertEquals(tagSemverVersionComparator(firstSemver, secondSemver), 1);
  }
}
