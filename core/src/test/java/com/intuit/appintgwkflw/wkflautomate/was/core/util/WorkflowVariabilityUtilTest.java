package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowVariabilityUtilTest {

  @Mock
  private FeatureManager featureManager;

  @Test
  public void testIsSubscriptionDataAvailable_EmptySubscriptionList() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, "[]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Assert.assertFalse(WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager));
  }

  @Test
  public void testIsSubscriptionDataAvailable_EmptySubscriptions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, "");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);


    Assert.assertFalse(WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager));
  }

  @Test
  public void testIsSubscriptionDataAvailable_NullSubscriptions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, null);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);


    Assert.assertFalse(WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager));
  }

  @Test
  public void testIsSubscriptionDataAvailable_VariabilityFFDisabled() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.SUBSCRIPTIONS, null);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(false);


    Assert.assertFalse(WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager));
  }

  @Test
  public void testIsSubscriptionDataAvailable_Success() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(
        WorkflowConstants.SUBSCRIPTIONS,
        "[{\"offeringId\": \"Intuit.sbe.salsa.default\", \"offeringType\": \"QBO_PLUS\"}, {\"offeringId\": \"Intuit.core.billpay\", \"offeringType\": \"BILLPAY_ELITE\"}]");

    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Mockito.when(
        featureManager.getBoolean(
            eq(WorkflowConstants.WORKFLOW_VARIABILITY_DOWNGRADE_FF), anyString()
        )).thenReturn(true);

    Assert.assertTrue(WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager));
  }

  @Test
  public void testGetDefinitionsPendingDeletion_MultipleDefinitions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(
        WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION,
        "[{\"definitionId\":\"defId1\",\"definitionKey\":null,\"recordType\":\"bill\",\"internalStatus\":null,\"templateName\":\"customApproval\"},{\"definitionId\":\"defId2\",\"definitionKey\":null,\"recordType\":\"bill\",\"internalStatus\":\"STALE_DEFINITION\",\"templateName\":\"customApproval\"},{\"definitionId\":\"defId4\",\"definitionKey\":null,\"recordType\":\"invoice\",\"internalStatus\":null,\"templateName\":\"customReminder\"}]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Assert.assertEquals(
        3, WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest).size());
  }

  @Test
  public void testGetDefinitionsPendingDeletion_EmptyDefinitions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, "[]");
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Assert.assertEquals(
        0, WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest).size());
  }

  @Test
  public void testGetDefinitionsPendingDeletion_NullDefinitions() {
    Map<String, String> testSchema = new HashMap<>();
    testSchema.put(WorkflowConstants.INTUIT_REALMID, "1234");
    testSchema.put(WorkflowConstants.DEFINITION_DETAILS_PENDING_DELETION, null);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("aId")
            .processDefinitionId(null)
            .processInstanceId("iId")
            .inputVariables(testSchema)
            .handlerId("hId")
            .ownerId(1234L)
            .build();

    Assert.assertEquals(
        0, WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest).size());
  }

  @Test
  public void testGetDefinitionList_success() {
    DefinitionPendingDeletion definitionPendingDeletion1 =
        new DefinitionPendingDeletion().builder().id("defid1").key("defkey1").build();

    DefinitionPendingDeletion definitionPendingDeletion2 =
        new DefinitionPendingDeletion().builder().id("defid2").key("defkey2").build();

    List<DefinitionPendingDeletion> definitionPendingDeletionList =
        Arrays.asList(definitionPendingDeletion1, definitionPendingDeletion2);
    List<DefinitionDetails> definitionDetailsList =
        WorkflowVariabilityUtil.getDefinitionList(definitionPendingDeletionList);

    Assert.assertEquals(2, definitionDetailsList.size());
    Assert.assertEquals("defid1", definitionDetailsList.get(0).getDefinitionId());
    Assert.assertEquals("defid2", definitionDetailsList.get(1).getDefinitionId());
  }

  @Test
  public void testGetDefinitionList_emptyDefinitionList() {
    List<DefinitionPendingDeletion> definitionPendingDeletionList = Collections.emptyList();
    List<DefinitionDetails> definitionDetailsList =
        WorkflowVariabilityUtil.getDefinitionList(definitionPendingDeletionList);
    Assert.assertEquals(0, definitionDetailsList.size());
  }

  @Test
  public void testGetDistinctDefinitionKeys_success() {
    DefinitionPendingDeletion definitionPendingDeletion1 =
        new DefinitionPendingDeletion().builder().id("defid1").key("defkey1").build();

    DefinitionPendingDeletion definitionPendingDeletion2 =
        new DefinitionPendingDeletion().builder().id("defid2").key("defkey1").build();

    DefinitionPendingDeletion definitionPendingDeletion3 =
        new DefinitionPendingDeletion().builder().id("defid3").key("defkey2").build();

    List<DefinitionPendingDeletion> definitionPendingDeletionList =
        Arrays.asList(
            definitionPendingDeletion1, definitionPendingDeletion2, definitionPendingDeletion3);

    Set<String> definitionKeys =
        WorkflowVariabilityUtil.getDistinctDefinitionKeys(definitionPendingDeletionList);
    Assert.assertEquals(Set.of("defkey1", "defkey2"), definitionKeys);
  }

  @Test
  public void testGetDistinctDefinitionKeys_emptyDefinitionPendingDeletionList() {
    List<DefinitionPendingDeletion> definitionPendingDeletionList = Collections.emptyList();

    Set<String> definitionKeys =
        WorkflowVariabilityUtil.getDistinctDefinitionKeys(definitionPendingDeletionList);
    Assert.assertEquals(Collections.emptySet(), definitionKeys);
  }
}
