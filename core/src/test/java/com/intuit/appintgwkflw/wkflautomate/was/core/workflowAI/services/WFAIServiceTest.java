package com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.services;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.WFAIConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.objects.AdminDetails;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.*;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.*;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CONDITION_ID_DMN;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class WFAIServiceTest {
    @Mock
    WFAIConfig wfaiConfig;

    @Mock
    private WASContextHandler wasContextHandler;

    @InjectMocks
    WFAIService wfaiService;

    private Map<String, List<AdminDetails>> companyAdminMap = new HashMap<>();


    @Before
    public void setUp() {
        companyAdminMap.put("1234", Arrays.asList(new AdminDetails("1234", true, false, "999", 5),
                new AdminDetails("1234", true, false, "998", 4),
                new AdminDetails("1234", true, false, "997", 2),
                new AdminDetails("1234", true, false, "996", 1)));

        doReturn("1234").when(wasContextHandler).get(Mockito.anyObject());
    }


    @Test
    public void emptyFeatureTemplates() throws Exception {
        doReturn(null).when(wfaiConfig).getTemplates();
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(new WorkflowStep()));
        wfaiService.populateAssignee(template);
    }

    @Test
    public void nonFeatureTemplate() throws Exception {
        doReturn(Set.of("xyz")).when(wfaiConfig).getTemplates();
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(new WorkflowStep()));
        wfaiService.populateAssignee(template);
    }
    @Test
    public void emptyAdminList() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(new HashMap<>()).when(wfaiConfig).getCompanyAdminMap();
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(new WorkflowStep()));
        wfaiService.populateAssignee(template);
    }

    @Test
    public void noConditionStep() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(companyAdminMap).when(wfaiConfig).getCompanyAdminMap();
        WorkflowStep workflowStep = new WorkflowStep();
        workflowStep.setStepType(StepTypeEnum.ACTION);
        workflowStep.setId(GlobalId.builder().setRealmId(REALM_ID).setLocalId("1234").build());
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(workflowStep));
        wfaiService.populateAssignee(template);
    }

    @Test
    public void noConditionBTW() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(companyAdminMap).when(wfaiConfig).getCompanyAdminMap();
        WorkflowStep workflowStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID));
        RuleLine ruleLineDmn =
                new RuleLine()
                        .mappedActionKey(MAPPED_KEY_DMN)
                        .rule(
                                new RuleLine.Rule()
                                        .conditionalExpression("GT  500")
                                        .parameterName("txn").parameterType(FieldTypeEnum.STRING));
        WorkflowStepCondition conditionDmn =
                new WorkflowStepCondition().id(TestHelper.getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
                        .description("descriotion");
        workflowStep.workflowStepCondition(conditionDmn);
        workflowStep.setStepType(StepTypeEnum.CONDITION);
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(workflowStep));
        wfaiService.populateAssignee(template);
    }

    @Test
    public void noNextStep() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(companyAdminMap).when(wfaiConfig).getCompanyAdminMap();
        WorkflowStep workflowStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID));
        RuleLine ruleLineDmn =
                new RuleLine()
                        .mappedActionKey(MAPPED_KEY_DMN)
                        .rule(
                                new RuleLine.Rule()
                                        .conditionalExpression("BTW 0,1000")
                                        .parameterName("Amount").parameterType(FieldTypeEnum.STRING));
        WorkflowStepCondition conditionDmn =
                new WorkflowStepCondition().id(TestHelper.getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
                        .description("descriotion");
        workflowStep.workflowStepCondition(conditionDmn);
        WorkflowStep.StepNext stepNext =
                new WorkflowStep.StepNext()
                        .workflowStepId(TestHelper.getGlobalId(WORKFLOW_STEP_ID).toString() + ":next")
                        .label(NextLabelEnum.YES);
        workflowStep.setStepType(StepTypeEnum.CONDITION);
        workflowStep.next(stepNext);

        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(workflowStep));
        wfaiService.populateAssignee(template);
    }


    @Test
    public void noActionNextStep() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(companyAdminMap).when(wfaiConfig).getCompanyAdminMap();
        WorkflowStep workflowStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID));
        RuleLine ruleLineDmn =
                new RuleLine()
                        .mappedActionKey(MAPPED_KEY_DMN)
                        .rule(
                                new RuleLine.Rule()
                                        .conditionalExpression("BTW 0,1000")
                                        .parameterName("Amount").parameterType(FieldTypeEnum.STRING));
        WorkflowStepCondition conditionDmn =
                new WorkflowStepCondition().id(TestHelper.getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
                        .description("descriotion");
        workflowStep.workflowStepCondition(conditionDmn);

        WorkflowStep workflowNextStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID+":next"));

        workflowNextStep.setStepType(StepTypeEnum.CONDITION);
        workflowNextStep.workflowStepCondition(conditionDmn);


        WorkflowStep.StepNext stepNext =
                new WorkflowStep.StepNext()
                        .workflowStepId(workflowNextStep.getId().toString())
                        .label(NextLabelEnum.YES);
        workflowStep.setStepType(StepTypeEnum.CONDITION);
        workflowStep.next(stepNext);
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(workflowStep, workflowNextStep));
        wfaiService.populateAssignee(template);
    }


    @Test
    public void assignedSuccess() throws Exception {
        doReturn(Set.of("invoiceapproval-multicondition")).when(wfaiConfig).getTemplates();
        doReturn(companyAdminMap).when(wfaiConfig).getCompanyAdminMap();
        WorkflowStep workflowStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID));
        RuleLine ruleLineDmn =
                new RuleLine()
                        .mappedActionKey(MAPPED_KEY_DMN)
                        .rule(
                                new RuleLine.Rule()
                                        .conditionalExpression("BTW 0,1000")
                                        .parameterName("Amount").parameterType(FieldTypeEnum.STRING));
        WorkflowStepCondition conditionDmn =
                new WorkflowStepCondition().id(TestHelper.getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn)
                        .description("descriotion");
        workflowStep.workflowStepCondition(conditionDmn);
        workflowStep.setStepType(StepTypeEnum.CONDITION);


        WorkflowStep workflowNextStep = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID+":next"));
        workflowNextStep.setStepType(StepTypeEnum.ACTION);
        InputParameter assigneeParameter  = new InputParameter().parameterName("Assignee");
        Action action =
                new Action().parameter(assigneeParameter).parameter(new InputParameter().parameterName("xyz"));
        ActionGroup actionGroup =
                new ActionGroup().action(action);
        workflowNextStep.setActionGroup(actionGroup);


        WorkflowStep workflowNextStepNO = new WorkflowStep().id(TestHelper.getGlobalId(WORKFLOW_STEP_ID+":next-NO"));
        workflowNextStepNO.setStepType(StepTypeEnum.ACTION);
        InputParameter assigneeParameterNO  = new InputParameter().parameterName("Assignee");
        Action actionNo =
                new Action().parameter(assigneeParameterNO).parameter(new InputParameter().parameterName("xyz"));
        ActionGroup actionGroupNo =
                new ActionGroup().action(actionNo);
        workflowNextStepNO.setActionGroup(actionGroupNo);

        WorkflowStep.StepNext stepNext =
                new WorkflowStep.StepNext()
                        .workflowStepId(workflowNextStep.getId().toString())
                        .label(NextLabelEnum.YES);

        WorkflowStep.StepNext stepNextNo =
                new WorkflowStep.StepNext()
                        .workflowStepId(workflowNextStepNO.getId().toString())
                        .label(NextLabelEnum.NO);

        workflowStep.next(stepNext);
        workflowStep.next(stepNextNo);
        Template template = new Template();
        template.setId(TestHelper.getGlobalId("invoiceapproval-multicondition"));
        template.setWorkflowSteps(List.of(workflowStep, workflowNextStep, workflowNextStepNO));
        wfaiService.populateAssignee(template);
        Assert.assertNotEquals(assigneeParameter.getFieldValues().size(), 0);
        Assert.assertEquals(assigneeParameter.getFieldValues().get(0), "996");
        Assert.assertNotEquals(assigneeParameterNO.getFieldValues().size(), 0);
        Assert.assertEquals(assigneeParameterNO.getFieldValues().get(0), "999");
    }

}
