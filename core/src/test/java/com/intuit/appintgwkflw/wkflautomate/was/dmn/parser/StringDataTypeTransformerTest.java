package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class StringDataTypeTransformerTest {

  @InjectMocks private StringDataTypeTransformer stringTransformer;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testTransformContainsOp() {
    String userFriendlyExpression = "CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("customer.equals(\"1\") || customer.equals(\"2\")", output);
  }

  @Test
  public void testTransformContainsOp_FeelExprTrue() {
    String userFriendlyExpression = "CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", true);
    Assert.assertEquals("\"1\",\"2\"", output);

    Assert.assertEquals(
        "\"1\"",
        stringTransformer.transformToDmnFriendlyExpression("CONTAINS 1", parameterName, "string", true));
  }
  @Test
  public void testTransformContainsOp_FeelExprFalse() {
    String userFriendlyExpression = "CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("customer.equals(\"1\") || customer.equals(\"2\")", output);

    Assert.assertEquals(
        "customer.equals(\"1\")",
        stringTransformer.transformToDmnFriendlyExpression("CONTAINS 1", parameterName, "string", false));
  }

  @Test
  public void testTransformNotContainsOp_FeelExprTrue() {
    String userFriendlyExpression = "NOT_CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", true);
    Assert.assertEquals("not(\"1\",\"2\")", output);

    Assert.assertEquals(
        "not(\"1\")",
        stringTransformer.transformToDmnFriendlyExpression(
            "NOT_CONTAINS 1", parameterName, "string", true));
  }
  @Test
  public void testTransformNotContainsOp_FeelExprFalse() {
    String userFriendlyExpression = "NOT_CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.equals(\"1\") && !customer.equals(\"2\")", output);

    Assert.assertEquals(
        "!customer.equals(\"1\")",
        stringTransformer.transformToDmnFriendlyExpression(
            "NOT_CONTAINS 1", parameterName, "string", false));
  }

  @Test
  public void testTransformAnyMatchOp_FeelExprTrue() {
    String userFriendlyExpression = "ANY_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", true);
    Assert.assertEquals("contains(customer, \"1\") or contains(customer, \"2\")", output);

    Assert.assertEquals(
        "contains(customer, \"1\")",
        stringTransformer.transformToDmnFriendlyExpression("ANY_MATCH 1", parameterName, "string", true));
  }
  @Test
  public void testTransformAnyMatchOp_FeelExprFalse() {
    String userFriendlyExpression = "ANY_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("customer.contains(\"1\") || customer.contains(\"2\")", output);

    Assert.assertEquals(
        "customer.contains(\"1\")",
        stringTransformer.transformToDmnFriendlyExpression("ANY_MATCH 1", parameterName, "string", false));
  }

  @Test
  public void testTransformNoMatchOp_FeelExprTrue() {
    String userFriendlyExpression = "NO_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", true);
    Assert.assertEquals("not(contains(customer, \"1\") or contains(customer, \"2\"))", output);

    Assert.assertEquals(
        "not(contains(customer, \"1\"))",
        stringTransformer.transformToDmnFriendlyExpression("NO_MATCH 1", parameterName, "string", true));
  }
  @Test
  public void testTransformNoMatchOp_FeelExprFalse() {
    String userFriendlyExpression = "NO_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.contains(\"1\") && !customer.contains(\"2\")", output);

    Assert.assertEquals(
        "!customer.contains(\"1\")",
        stringTransformer.transformToDmnFriendlyExpression("NO_MATCH 1", parameterName, "string", false));
  }

  @Test
  public void testTransformNotContainsOp() {
    String userFriendlyExpression = "NOT_CONTAINS 1";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.equals(\"1\")", output);
  }

  @Test
  public void testTransformNotContainsMultipleValuesOp() {
    String userFriendlyExpression = "NOT_CONTAINS 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.equals(\"1\") && !customer.equals(\"2\")", output);
  }

  @Test
  public void testTransformContainsNotContainsOp() {
    String userFriendlyExpression = "CONTAINS 1 && NOT_CONTAINS 2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals(output, "customer.equals(\"1\") && !customer.equals(\"2\")");
  }

  @Test
  public void testTransformAnyMatchOp() {
    String userFriendlyExpression = "ANY_MATCH 1";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals(output, "customer.contains(\"1\")");
  }

  @Test
  public void testTransformAnyMatchMultipleValuesOp() {
    String userFriendlyExpression = "ANY_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("customer.contains(\"1\") || customer.contains(\"2\")", output);
  }

  @Test
  public void testTransformNotAnyMatchOp() {
    String userFriendlyExpression = "NO_MATCH 1";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.contains(\"1\")", output);
  }

  @Test
  public void testTransformNotAnyMatchMultipleValuesOp() {
    String userFriendlyExpression = "NO_MATCH 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("!customer.contains(\"1\") && !customer.contains(\"2\")", output);
  }

  @Test
  public void testTransformInvalidOp() {
    String userFriendlyExpression = "Invalid_op 1,2";
    String parameterName = "customer";
    String output =
        stringTransformer.transformToDmnFriendlyExpression(
            userFriendlyExpression, parameterName, "string", false);
    Assert.assertEquals("", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionJuelSingleItem(){
    String dmnExpression = "DisplayName.equals(\"abc\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("CONTAINS abc", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionJuelNegateSingleItem(){
    String dmnExpression = "!DisplayName.equals(\"abc\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("NOT_CONTAINS abc", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionJuelListItem(){
    String dmnExpression = "DisplayName.equals(\"abc\") || DisplayName.equals(\"def\") || DisplayName.equals(\"ghi\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("CONTAINS abc,def,ghi", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionJuelNegateListItem(){
    String dmnExpression = "!DisplayName.equals(\"abc\") || !DisplayName.equals(\"def\") || !DisplayName.equals(\"ghi\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("NOT_CONTAINS abc,def,ghi", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionFeelSingleItem(){
    String dmnExpression = "\"abc\"";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("CONTAINS abc", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionFeelNegateSingleItem(){
    String dmnExpression = "not(\"abc\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("NOT_CONTAINS abc", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionFeelListItem(){
    String dmnExpression = "\"abc\",\"def\",\"ghi\"";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("CONTAINS abc,def,ghi", output);
  }

  @Test
  public void testTransformToUserFriendlyExpressionFeelNegateListItem(){
    String dmnExpression = "not(\"abc\",\"def\",\"ghi\")";
    String parameterName = "DisplayName";
    String output = stringTransformer.transformToUserFriendlyExpression(dmnExpression, parameterName);
    Assert.assertEquals("NOT_CONTAINS abc,def,ghi", output);
  }
}
