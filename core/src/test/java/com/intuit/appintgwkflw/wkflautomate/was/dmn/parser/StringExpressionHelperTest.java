package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class StringExpressionHelperTest {

  @Test
  public void testDmnExprWithContainsOpForList() {
    //expression = "CONTAINS 1,2";
    String parameterName = "Customer";
    List<String> values = List.of("1", "2");
    String output =
        StringExpressionHelper.prepareContainsAnyElementExpressionForList(parameterName, "NOT_CONTAINS", values);
    Assert.assertEquals("not(containsAnyElement([\"1\",\"2\"], Customer))", output);
  }
  @Test
  public void testDmnExprWithNotContainsOpForList() {
    //expression = "CONTAINS 1,2";
    String parameterName = "Customer";
    List<String> values = List.of("1", "2");
    String output =
        StringExpressionHelper.prepareContainsAnyElementExpressionForList(parameterName, "CONTAINS", values);
    Assert.assertEquals("containsAnyElement([\"1\",\"2\"], Customer)", output);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void invalidOperation() {
    //expression = "CREATE 1";
    String parameterName = "customer";
    List<String> values = List.of("1");
    StringExpressionHelper.prepareContainsAnyElementExpressionForList(parameterName, "CREATE", values);
  }

  @Test
  public void testUIExprForEqualsOpForList() {
    String parameterName = "Customer";
    String expression =
        "Customer.equals(\"a\") || Customer.equals(\"b c\") || Customer.equals(\" d\")";
    String result =
        StringExpressionHelper.transformEqualsUserFriendlyExpression(expression);
    Assert.assertEquals("CONTAINS a,b c,d", result);
    expression = "Customer.equals(\"a\") || Customer.equals(\"  b\")";
    result = StringExpressionHelper.transformEqualsUserFriendlyExpression(expression);
    Assert.assertEquals("CONTAINS a,b", result);
  }

  @Test
  public void testUIExprForNotContainsOpForList() {
    String expression =
        "not(containsAnyElement([\"1\",\"11\"], ItemList))";
    String result =
        StringExpressionHelper.transformListContainsUserFriendlyExpression(expression);
    Assert.assertEquals("NOT_CONTAINS 1,11", result);
  }
  @Test
  public void testUIExprForNotContainsOpForLis2t() {
    String expression =
        "containsAnyElement([\"1\",\"11\"], ItemList)";
    String result =
        StringExpressionHelper.transformListContainsUserFriendlyExpression(expression);
    Assert.assertEquals("CONTAINS 1,11", result);
  }

  @Test
  public void testUIExprForContainsOpForList() {
    String expression =
        "containsAnyElement([\"1\",\"11\"], Item)";
    String result =
        StringExpressionHelper.transformEqualsUserFriendlyExpression(expression);
    Assert.assertEquals("CONTAINS 1,11", result);

    expression = "containsAnyElement([\"UNPAID\"], TxnPaymentStatus)";
    result =
        StringExpressionHelper.transformEqualsUserFriendlyExpression(expression);
    Assert.assertEquals("CONTAINS UNPAID", result);
  }

}