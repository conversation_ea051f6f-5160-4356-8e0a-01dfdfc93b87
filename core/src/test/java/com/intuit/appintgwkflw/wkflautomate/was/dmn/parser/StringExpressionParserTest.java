package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Arrays;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class StringExpressionParserTest {

  @Test
  public void parseStringWithContains() {
    String expression = "CONTAINS 1";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "customer.equals(\"1\")");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void invalidOperation() {
    String expression = "CREATE 1";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    StringExpressionParser.prepareStringExpressionForDMN(
        WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
  }

  @Test
  public void testFreeFormStringRuleLines() {
    String parameterName = "Customer";
    String expression =
        "Customer.equals(\"a\") || Customer.equals(\"b c\") || Customer.equals(\" d\")";
    String result =
        StringExpressionParser.transformEqualsExpressionforUI(expression, parameterName);
    Assert.assertEquals("CONTAINS a,b c, d", result);
    expression = "Customer.equals(\"a\") || Customer.equals(\"  b\")";
    result = StringExpressionParser.transformEqualsExpressionforUI(expression, parameterName);
    Assert.assertEquals("CONTAINS a,  b", result);
  }

  @Test
  public void parseStringWithOrContains() {
    String expression = "CONTAINS 1,2";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "customer.equals(\"1\") || customer.equals(\"2\")");
  }

  @Test
  public void parseStringWithNotContains() {
    String expression = "NOT_CONTAINS 1";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "!customer.equals(\"1\")");
  }

  @Test
  public void parseStringWithNotAndContains() {
    String expression = "NOT_CONTAINS 1,2";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "!customer.equals(\"1\") && !customer.equals(\"2\")");
  }

  @Test
  public void parseStringWithAndContainsNotContains() {
    String expression = "CONTAINS 1 && NOT_CONTAINS 2";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, expression, parameterName, output);
    Assert.assertEquals(output, "customer.equals(\"1\") && !customer.equals(\"2\")");
  }

  @Test
  public void parseStringWithAnyMatch() {
    String anyMatchExpression = "ANY_MATCH 1";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = anyMatchExpression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "customer.contains(\"1\")");
  }

  @Test
  public void parseStringWithAndAnyMatch() {
    String expression = "ANY_MATCH 1,2";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = expression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, expression, parameterName, output);
    Assert.assertEquals(output, "customer.contains(\"1\") || customer.contains(\"2\")");
  }

  @Test
  public void parseStringWithNotAnyMatch() {
    String anyMatchExpression = "NO_MATCH 1";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = anyMatchExpression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "!customer.contains(\"1\")");
  }

  @Test
  public void parseStringWithAndNotAnyMatch() {
    String anyMatchExpression = "NO_MATCH 1,2";
    String parameterName = "customer";
    String output = WorkflowConstants.BLANK;
    String[] stringToken = anyMatchExpression.split(WorkflowConstants.SPACE);
    List<String> values = Arrays.asList(stringToken[1].split(WorkflowConstants.COMMA));
    output =
        StringExpressionParser.prepareStringExpressionForDMN(
            WorkflowConstants.STRING_MODIFIER, values, parameterName, stringToken[0], output);
    Assert.assertEquals(output, "!customer.contains(\"1\") && !customer.contains(\"2\")");
  }
}
