package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy;
import org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


public class BaseDataSourceConfiguration {

  protected String resolveProperty(String property, String defaultProperty) {
    return Optional.ofNullable(property).orElse(defaultProperty);
  }

  protected Map<String, Object> jpaProperties(JpaProperties jpaProperties) {
    Map<String, Object> props = new HashMap<>();
    props.putAll(jpaProperties.getProperties());
    props.put("hibernate.physical_naming_strategy", SpringPhysicalNamingStrategy.class.getName());
    props.put("hibernate.implicit_naming_strategy", SpringImplicitNamingStrategy.class.getName());
    return props;
  }
}
