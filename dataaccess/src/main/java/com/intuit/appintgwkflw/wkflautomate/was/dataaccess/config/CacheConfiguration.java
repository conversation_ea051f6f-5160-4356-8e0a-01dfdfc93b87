package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Getter
@Setter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "cache")
public class CacheConfiguration {
    private boolean enabled;
    private boolean clusterMode = Boolean.TRUE;
    private List<String> hostUrls;
    private String password;
    private int scanInterval;

    // If not set, no ttl is set on the keys (stored infinitely)
    private Long ttlDurationMillis;

    // Connection configurations
    // minimum number of idle connections to each slave
    private int slaveMinConnectionPoolSize;
    // maximum number of idle connections to each slave
    private int slaveMaxConnectionPoolSize;
    // minimum number of idle connections to master
    private int masterMinConnectionPoolSize;
    // refer to the doc
    private int masterMaxConnectionPoolSize;
    // If pooled connection is not used for this duration and current connections
    // amount bigger than minimum idle connections pool size, then it will
    // be closed and removed from pool
    private int idleConnectionTimeoutMillis;
}