package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
/**
 * Config for DB properties of different offerings
 *
 * <AUTHOR>
 */
@Data
public class DataSource {

  private String url;
  private String readerUrl;
  private String username;
  private String password;
  private LiquibaseProperties liquibase;
}
