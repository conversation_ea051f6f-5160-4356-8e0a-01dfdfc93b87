package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Injects the multi offering data source for all the offering that will be onboarded for a
 * particular Swimlane.
 */
@Configuration
@EnableTransactionManagement
@EnableConfigurationProperties({JpaProperties.class})
@EnableJpaRepositories(
    basePackages = { DataAccessConstants.WRITER_PACKAGE_SCAN, DataAccessConstants.GC_PACKAGE_SCAN },
    entityManagerFactoryRef = DataAccessConstants.OFFERING_AWARE_EM,
    transactionManagerRef = DataAccessConstants.OFFERING_AWARE_TM)
public class DataSourceConfiguration extends BaseDataSourceConfiguration{

  @Autowired private JpaProperties jpaProperties;
  @Autowired DataSourceProperties defaultDataSourceProperties;
  @Autowired OfferingConfig offeringConfig;

  /**
   * Iterates through the offerings listed in the application config file and creates
   * the @HikariDataSource
   *
   * @return Map of offering data sources that are read from the application config file
   */
  @Bean(name = DataAccessConstants.DATA_SOURCES)
  @Primary
  public Map<Object, Object> getDataSources() {

    return offeringConfig
        .getDownstreamServices()
        .stream()
        .collect(
            Collectors.toMap(
                downstream -> downstream.getOfferingId(),
                downstream -> {
                  DataSource dataSource =
                      downstream.getDataSource() == null
                          ? new DataSource()
                          : downstream.getDataSource();

                  return  this.getHikariDataSource(dataSource);
                }));
  }

  /**
   * Creates new Hikari data source for the given tenant config. This method uses the tenant
   * specific URL/User/PWD for the data source if given. Else, it will pick the data source
   * properties from spring.datasource property
   *
   * @param downStreamProperty Tenant specific data source property
   * @return Datasource
   */
  public HikariDataSource getHikariDataSource(DataSource dataSourceProperty) {
    HikariDataSource dataSource = new HikariDataSource();
    getHikariConfig().copyStateTo(dataSource);

    dataSource.setJdbcUrl(
        resolveProperty(dataSourceProperty.getUrl(), defaultDataSourceProperties.determineUrl()));
    dataSource.setUsername(
        resolveProperty(
            dataSourceProperty.getUsername(), defaultDataSourceProperties.determineUsername()));
    dataSource.setPassword(
        resolveProperty(
            dataSourceProperty.getPassword(), defaultDataSourceProperties.determinePassword()));
    return dataSource;
  }

  /**
   * Configures all the data source with hikari pool properties
   *
   * @return HikariConfig
   */
  @ConfigurationProperties(prefix = "spring.datasource.hikari")
  @Bean
  @Primary
  public HikariConfig getHikariConfig() {
    return new HikariConfig();
  }

  /**
   * Injects Multi offering data source which wraps the map of data sources for different offerings
   *
   * @param dataSources Map of datasources for different offerings that are defined in the
   *     application config.
   * @return Wrapper Routing data source
   */
  @Bean(name = DataAccessConstants.ROUTING_DATASOURCE)
  @DependsOn(DataAccessConstants.DATA_SOURCES)
  @Primary
  // @RefreshScope
  public javax.sql.DataSource dataSource(Map<Object, Object> dataSources) {
    AbstractRoutingDataSource tenantRoutingDataSource = new OfferingAwareWriterDataSource();
    tenantRoutingDataSource.setTargetDataSources(dataSources);
    Object defaultDataSource = null;
    if (offeringConfig.getDefaultOffering() != null
        && !dataSources.isEmpty()
        && dataSources.get(offeringConfig.getDefaultOffering()) != null) {
      defaultDataSource = dataSources.get(offeringConfig.getDefaultOffering());
    } else {
      // An empty data source config will create the default data source from the spring data
      // source property
      defaultDataSource = getHikariDataSource(new DataSource());
    }
    tenantRoutingDataSource.setDefaultTargetDataSource(defaultDataSource);
    tenantRoutingDataSource.afterPropertiesSet();
    return tenantRoutingDataSource;
  }

  @Primary
  @Bean(name = DataAccessConstants.OFFERING_AWARE_EM)
  public LocalContainerEntityManagerFactoryBean multiEntityManager(
      @Qualifier(DataAccessConstants.ROUTING_DATASOURCE)
          javax.sql.DataSource tenantRoutingDataSource) {
    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(tenantRoutingDataSource);
    em.setPackagesToScan(DataAccessConstants.PACKAGE_SCAN_ENTITY, DataAccessConstants.GC_PACKAGE_SCAN_ENTITY);
    em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
    em.setJpaPropertyMap(jpaProperties(jpaProperties));
    em.setPersistenceUnitName(DataAccessConstants.OFFERING_AWARE_EM);
    return em;
  }


  @Primary
  @Bean(name = DataAccessConstants.OFFERING_AWARE_TM)
  public PlatformTransactionManager multiTransactionManager(
      @Qualifier(DataAccessConstants.OFFERING_AWARE_EM)
          LocalContainerEntityManagerFactoryBean entityManagerFactoryBean) {
    return new JpaTransactionManager(entityManagerFactoryBean.getObject());
  }

  @Data
  @AllArgsConstructor
  private class OfferingIdDataSource {
    private Object tenantId;
    private HikariDataSource dataSource;
  }
}
