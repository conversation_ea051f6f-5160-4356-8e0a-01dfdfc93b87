package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.zaxxer.hikari.HikariDataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import javax.sql.DataSource;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.liquibase.DataSourceClosingSpringLiquibase;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.task.TaskExecutor;
import org.springframework.util.StopWatch;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;

/**
 * This class is responsible for running Liquibase changelogs for all the offerings listed for a
 * swimlane
 */
@Data
@RefreshScope
public class MultiOfferingDataSourceSpringLiquibase
    implements InitializingBean, ResourceLoaderAware {

  public static final String DISABLED_MESSAGE = "Liquibase is disabled";
  public static final String STARTING_ASYNC_MESSAGE =
      "Starting Liquibase asynchronously, your database might not be ready at startup!";
  public static final String STARTING_SYNC_MESSAGE = "Starting Liquibase synchronously";
  public static final String STARTED_MESSAGE = "Liquibase has updated your database in {} ms";
  public static final String EXCEPTION_MESSAGE =
      "Liquibase could not start correctly, your database is NOT ready: " + "{}";

  public static final long SLOWNESS_THRESHOLD = 5; // seconds
  public static final String SLOWNESS_MESSAGE =
      "Warning, Liquibase took more than {} seconds to start up!";

  private final Map<String, DataSource> dataSources = new HashMap<>();
  private final Map<String, LiquibaseProperties> propertiesDataSources = new HashMap<>();

  private ResourceLoader resourceLoader;

  private LiquibaseProperties properties;

  private final TaskExecutor taskExecutor;

  private String user;

  private String password;

  public MultiOfferingDataSourceSpringLiquibase(
      @Qualifier("taskExecutor") TaskExecutor taskExecutor) {
    this.taskExecutor = taskExecutor;
  }

  public MultiOfferingDataSourceSpringLiquibase() {
    taskExecutor = null;
  }

  public void addDataSource(String tenant, DataSource dataSource) {
    this.dataSources.put(tenant, dataSource);
  }

  public void addLiquibaseProperties(String tenant, LiquibaseProperties properties) {
    this.propertiesDataSources.put(tenant, properties);
  }

  /**
   * Since this is Initializing bean, this method will be called after all its properties are set,
   * i.e post initialization
   *
   * @throws Exception
   */
  @Override
  public void afterPropertiesSet() throws Exception {
    WorkflowLogger.logInfo(
        "Multiple offering based datasource enabled, total detected datasources : %s",
        dataSources.size());
    runOnAllDataSources();
  }

  /**
   * Iterates through all the offering data sources and initialize the DB.
   */
  private void runOnAllDataSources(){
    dataSources.forEach(
        (tenant, dataSource) -> {
          WorkflowLogger.logInfo("Initializing Liquibase for offering data source %s", tenant);

          // If there is liquibase property overridden on per tenant basis, use that.
          // If not available use the spring.liquibase properties
          final LiquibaseProperties lProperty = propertiesDataSources.get(tenant);
          SpringLiquibase liquibase =
              lProperty != null
                  ? getSpringLiquibase(dataSource, lProperty)
                  : getSpringLiquibase(dataSource, properties);

          WorkflowLogger.logInfo(
              "Running Liquibase for LB schema %s", liquibase.getLiquibaseSchema());

          // Run the scripts asynchronously
          if (taskExecutor != null) {
            taskExecutor.execute(() -> invokeLiquibase(liquibase, STARTING_ASYNC_MESSAGE));
          } else {
            invokeLiquibase(liquibase, STARTING_SYNC_MESSAGE);
          }
          WorkflowLogger.logInfo("Liquibase ran for data source %s", tenant);
        });
  }

  private void invokeLiquibase(SpringLiquibase liquibase, String message) {
    try {
      WorkflowLogger.logInfo(message);
      initDb(liquibase);
    } catch (LiquibaseException e) {
      WorkflowLogger.logError(e, EXCEPTION_MESSAGE);
    }
  }

  /**
   * Run the Change log for the offering specific liquibase
   *
   * @param liquibase Offering specific liquibase
   * @throws LiquibaseException
   */
  private void initDb(SpringLiquibase liquibase) throws LiquibaseException {
    StopWatch watch = new StopWatch();
    watch.start();
    // This will kick running the scripts for a particular offering specific datasource
    liquibase.afterPropertiesSet();
    watch.stop();

    WorkflowLogger.logDebug(STARTED_MESSAGE, watch.getTotalTimeMillis());
    if (watch.getTotalTimeMillis() > SLOWNESS_THRESHOLD * 1000L) {
      WorkflowLogger.logWarn(SLOWNESS_MESSAGE, SLOWNESS_THRESHOLD);
    }
  }

  /**
   * Returns the spring liquibase configured with the offering specific data source. It uses the
   * Liquibase properties specified for the offering, if provided
   *
   * @param dataSource
   * @param offeringProps
   * @return
   */
  private SpringLiquibase getSpringLiquibase(
      DataSource dataSource, LiquibaseProperties offeringProps) {
    SpringLiquibase liquibase = createSpringLiquibase(dataSource, offeringProps);
    liquibase.setChangeLog(offeringProps.getChangeLog());
    liquibase.setChangeLogParameters(offeringProps.getParameters());
    liquibase.setContexts(offeringProps.getContexts());
    liquibase.setLabels(offeringProps.getLabels());
    liquibase.setDropFirst(offeringProps.isDropFirst());
    liquibase.setShouldRun(offeringProps.isEnabled());
    liquibase.setRollbackFile(offeringProps.getRollbackFile());
    liquibase.setResourceLoader(resourceLoader);
    liquibase.setDefaultSchema(offeringProps.getDefaultSchema());
    liquibase.setLiquibaseSchema(
        offeringProps.getLiquibaseSchema() != null
            ? offeringProps.getLiquibaseSchema()
            : properties.getLiquibaseSchema());
    liquibase.setLiquibaseTablespace(offeringProps.getLiquibaseTablespace());
    liquibase.setDatabaseChangeLogTable(offeringProps.getDatabaseChangeLogTable());
    liquibase.setDatabaseChangeLogLockTable(offeringProps.getDatabaseChangeLogLockTable());
    return liquibase;
  }

  /**
   * Offering specific Liquibase
   *
   * @param dataSource
   * @param offeringProps
   * @return
   */
  private SpringLiquibase createSpringLiquibase(
      DataSource dataSource, LiquibaseProperties offeringProps) {
    SpringLiquibase liquibase = null;
    // Create datasource from offering specific liquibase data source config,if provided.
    // If not available, use the global liquibase datasource config.
    DataSource liquibaseDataSource =
        getLiquiBaseDataSource((HikariDataSource) dataSource, offeringProps);
    if (liquibaseDataSource != null) {
      // If the datasource is created specific for Liquibase run, close it once migration is done.
      WorkflowLogger.logInfo("Creating DataSourceClosingSpringLiquibase based on liquibase props");
      liquibase = new DataSourceClosingSpringLiquibase();
      liquibase.setDataSource(liquibaseDataSource);
    } else {

      // If no Liquibase specific datasource is specified, use the offering specific datasource.
      // This will not close the datasource.
      WorkflowLogger.logInfo("Creating SpringLiquibase based on offering data source");
      liquibase = new SpringLiquibase();
      liquibase.setDataSource(dataSource);
    }

    return liquibase;
  }

  private DataSource getLiquiBaseDataSource(
      HikariDataSource dataSource, LiquibaseProperties offeringProperties) {
    HikariDataSource liquibaseDataSource = null;
    LiquibaseProperties props = null;
    
    if (offeringProperties != null && offeringProperties.getUser() != null) {
      props = offeringProperties;
    } else if (properties != null && properties.getUser() != null) {
      props = properties;
    }
    
    if (props != null) {
      String url = dataSource.getJdbcUrl(); // getProperty(props::getUrl, dataSource::getJdbcUrl);
      String user = getProperty(props::getUser, dataSource::getUsername);
      String password = getProperty(props::getPassword, dataSource::getPassword);
      liquibaseDataSource =
          DataSourceBuilder.create()
              .type(HikariDataSource.class)
              .url(url)
              .username(user)
              .password(password)
              .build();

      WorkflowLogger.logInfo(
          "Setting up Liquibase datasource to Schema :  %s", dataSource.getSchema());
      liquibaseDataSource.setSchema(dataSource.getSchema());
    }
    return liquibaseDataSource;
  }

  private String getProperty(Supplier<String> property, Supplier<String> defaultValue) {
    String value = property.get();
    return (value != null) ? value : defaultValue.get();
  }

  public void refresh() {
    // Dummy comments added for sonar critical issue check
  }

}
