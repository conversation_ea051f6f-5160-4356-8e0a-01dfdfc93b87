package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.sql.Connection;
import java.sql.SQLException;

public abstract class OfferingAwareDataSource extends AbstractRoutingDataSource {

  @Override
  protected abstract String determineCurrentLookupKey();

  @Override
  public Connection getConnection() throws SQLException {
    Connection conn = super.getConnection();
    // conn.setSchema(determineCurrentLookupKey());
    return conn;
  }

  @Override
  public void afterPropertiesSet() {
    super.afterPropertiesSet();
    if (SpringContext.getApplicationContext() != null) {
      // TODO refresh only new DB!
      MultiOfferingDataSourceSpringLiquibase lb =
          SpringContext.getApplicationContext()
              .getBean(
                  DataAccessConstants.OFFERING_AWARE_LIQUIBASE,
                  MultiOfferingDataSourceSpringLiquibase.class);
      lb.refresh();
    }
  }
}
