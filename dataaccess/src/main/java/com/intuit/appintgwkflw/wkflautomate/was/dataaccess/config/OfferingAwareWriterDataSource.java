package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.sql.Connection;
import java.sql.SQLException;

public class OfferingAwareWriterDataSource extends OfferingAwareDataSource {

  @Override
  protected String determineCurrentLookupKey() {
    WorkflowLogger.debug(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Resolving datasource using the offering id: %s",
                    WASContext.getOfferingId().orElse("Default"))
                .className(this.getClass().getSimpleName()));
    return WASContext.getOfferingId().orElse(null);
  }
}
