package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "offerings")
public class OfferingConfig {

  private String defaultOffering;

  private Filter filter;

  private List<DownStreamConfig> downstreamServices = new ArrayList<>();

  private List<String> allowedSystemUserUrls;

}
