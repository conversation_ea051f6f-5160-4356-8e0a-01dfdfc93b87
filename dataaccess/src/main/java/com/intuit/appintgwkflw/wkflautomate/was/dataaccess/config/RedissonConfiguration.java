package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import lombok.AllArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

@Configuration
@AllArgsConstructor
@ConditionalOnExpression("${cache.enabled:false}")
public class RedissonConfiguration {

    private final CacheConfiguration cacheConfiguration;

    @RefreshScope
    @Bean(name = DataAccessConstants.REDISSON_CLIENT)
    public RedissonClient redissonClient() {

        WorkflowLogger.logInfo("Establishing connection with Cache");

        Config cacheConfig =  cacheConfiguration.isClusterMode() ? configureClusterRedisClient() : configureStandaloneRedisClient();

        cacheConfig.setCodec(new StringCodec());

        return Redisson.create(cacheConfig);
    }

    @PreDestroy
    public void shutDownConnection() {
        WorkflowLogger.logInfo("Closing Redisson connections");
        redissonClient().shutdown();
    }

    private Config configureClusterRedisClient() {
        Config config = new Config();
        config.useClusterServers()
                // .setScanInterval(cacheSource.getScanInterval())
                .addNodeAddress(cacheConfiguration.getHostUrls().toArray(new String[0]))
                .setPassword(cacheConfiguration.getPassword())
                .setReadMode(ReadMode.MASTER_SLAVE)
                .setSlaveConnectionMinimumIdleSize(cacheConfiguration.getSlaveMinConnectionPoolSize())
                .setSlaveConnectionPoolSize(cacheConfiguration.getSlaveMaxConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(cacheConfiguration.getMasterMinConnectionPoolSize())
                .setMasterConnectionPoolSize(cacheConfiguration.getMasterMaxConnectionPoolSize())
                .setIdleConnectionTimeout(cacheConfiguration.getIdleConnectionTimeoutMillis());
        return config;
    }

    private Config configureStandaloneRedisClient() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(cacheConfiguration.getHostUrls().get(0))
                .setConnectionMinimumIdleSize(cacheConfiguration.getSlaveMinConnectionPoolSize())
                .setConnectionPoolSize(cacheConfiguration.getSlaveMaxConnectionPoolSize())
                .setIdleConnectionTimeout(cacheConfiguration.getIdleConnectionTimeoutMillis());
        return config;
    }
}