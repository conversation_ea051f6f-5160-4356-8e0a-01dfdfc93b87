package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class StateTransitionConfig {

  private int maxResult;
  private Set<String> enableProcessEvents = new HashSet<>();
  // Disable Transition Event Publish in SLA
  private Set<String> disableActivityEvents = new HashSet();
}
