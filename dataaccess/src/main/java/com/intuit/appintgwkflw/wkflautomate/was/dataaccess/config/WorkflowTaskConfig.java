package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "workflowtask")
public class WorkflowTaskConfig {

  private boolean enable;
 
  private StateTransitionConfig stateTransitionConfig;
  
  private Map<TaskType, WorkflowTaskConfigDetails> taskConfig;

}
