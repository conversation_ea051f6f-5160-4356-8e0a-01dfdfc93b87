package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class DataAccessConstants {
  public static final String DATA_SOURCES = "dataSources";

  public static final String PACKAGE_SCAN_ENTITY = "com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities";
  public static final String GC_PACKAGE_SCAN_ENTITY = "com.intuit.guranteedconsumer.commons.core.model";
  public static final String READER_PACKAGE_SCAN = "com.intuit.appintgwkflw.wkflautomate.was.dataaccess.reader.repository*";
  public static final String WRITER_PACKAGE_SCAN = "com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository*";
  public static final String GC_PACKAGE_SCAN = "com.intuit.guranteedconsumer.commons.core.repository";
  public static final String DATA_SOURCES_READER = "dataSourcesReader";
  public static final String ROUTING_DATASOURCE = "tenantRoutingDataSource";

  public static final String ROUTING_DATASOURCE_READER = "tenantRoutingDataSourceReader";
  public static final String OFFERING_AWARE_EM = "multiEntityManager";
  public static final String OFFERING_AWARE_TM = "multiTransactionManager";

  public static final String OFFERING_AWARE_EM_READER = "multiEntityManagerReader";
  public static final String OFFERING_AWARE_TM_READER = "multiTransactionManagerReader";
  public static final String OFFERING_AWARE_LIQUIBASE = "multiTenantLiquiBase";

  public static final String READER_DATABASE_PROPERTIES = "readerDatabaseProperties";
  public static final String OFFERING_FILTER = "offeringFilter";
  public static final String OWNER_ID = "ownerId";
  public static final String REDISSON_CLIENT = "redissonClient";
}
