package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import lombok.Getter;

import java.util.function.Function;

@Getter
public enum FilterType {

    TEMPLATE_NAME("template.name", "templateDetails", "templateName"),

    TEMPLATE_CATEGORY("template.category", "templateDetails", "templateCategory"),

    LOOKUP_KEY("lookupKeys.key", "definitionDetails", "lookupKeys"),

    LOOKUP_VALUE("lookupKeys.value", "definitionDetails", "lookupKeys"),

    RECORD_TYPE("recordType", "definitionDetails", "recordType", RecordType::fromType);

    private final String filterType;
    private final String table;
    private final String column;
    private final Function<String, Object> valueConversionFunction;

    FilterType(String filterType, String table, String column) {
        this.filterType = filterType;
        this.table = table;
        this.column = column;
        this.valueConversionFunction = null;
    }

    FilterType(String filterType, String table, String column, Function<String, Object> valueConversionFunction) {
        this.filterType = filterType;
        this.table = table;
        this.column = column;
        this.valueConversionFunction = valueConversionFunction;
    }

    public static boolean isValidFilterType(String type) {
        for (FilterType filterType : FilterType.values()) {
            if (filterType.getFilterType().equals(type)) {
                return true;
            }
        }
        return false;
    }

    public static FilterType fromType(String type) {
        for (FilterType filterType : FilterType.values()) {
            if (filterType.getFilterType().equals(type)) {
                return filterType;
            }
        }
        throw new UnsupportedOperationException("The code " + type + " is not supported!");
    }
}
