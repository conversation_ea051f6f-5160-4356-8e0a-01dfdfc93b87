package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;

import javax.persistence.AttributeConverter;

public class InternalStatusConverter implements AttributeConverter<InternalStatus, String> {

  @Override
  public String convertToDatabaseColumn(InternalStatus attribute) {
    return attribute != null ? attribute.name(): null;
  }

  @Override
  public InternalStatus convertToEntityAttribute(String dbData) {
    return isNotEmpty(dbData) ? InternalStatus.valueOf(dbData) : null;
  }
  
}

