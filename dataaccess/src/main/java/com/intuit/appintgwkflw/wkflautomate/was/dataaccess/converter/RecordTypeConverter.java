package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;

import javax.persistence.AttributeConverter;
import java.util.Objects;

/** <AUTHOR> */
public class RecordTypeConverter implements AttributeConverter<RecordType, String> {

  @Override
  public String convertToDatabaseColumn(RecordType attribute) {
    return attribute != null ? attribute.getRecordType() : null;
  }

  // In case of null, it handles and warns about the recordType not-present.
  @Override
  public RecordType convertToEntityAttribute(String dbData) {
    RecordType recordType = RecordType.fromType(dbData);
    if (Objects.nonNull(recordType)) return recordType;

    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Type is not present for record=%s", dbData)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .className(this.getClass().getSimpleName()));

    return null;
  }
}
