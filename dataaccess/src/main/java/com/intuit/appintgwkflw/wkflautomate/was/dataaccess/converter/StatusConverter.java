package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import javax.persistence.AttributeConverter;

public class StatusConverter implements AttributeConverter<Status, String> {

  @Override
  public String convertToDatabaseColumn(Status attribute) {
    return attribute != null ? attribute.getStatus(): null;
  }

  @Override
  public Status convertToEntityAttribute(String dbData) {
    return dbData != null ? Status.lookupStatus(dbData) : null;
  }

}

