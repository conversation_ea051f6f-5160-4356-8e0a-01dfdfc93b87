package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import javax.persistence.AttributeConverter;

public class TaskTypeConverter implements AttributeConverter<TaskType, String> {

  @Override
  public String convertToDatabaseColumn(TaskType attribute) {
    return attribute != null ? attribute.name() : null;
  }

  @Override
  public TaskType convertToEntityAttribute(String dbData) {
    return isNotEmpty(dbData) ? TaskType.valueOf(dbData) : null;
  }

}
