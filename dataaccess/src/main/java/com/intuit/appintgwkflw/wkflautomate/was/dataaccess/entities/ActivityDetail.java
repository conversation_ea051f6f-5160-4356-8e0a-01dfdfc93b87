package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.TaskTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Entity
@Table(name = "de_activity_details",
        indexes = {
                @Index(name = "template_id_activity_id_idx", columnList = "template_id, activityId", unique = false)})
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ActivityDetail {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private long id;

  @Convert(converter = TaskTypeConverter.class)
  private TaskType type;

  private String activityId;

  private String activityName;

  private String activityType;

  private long parentId;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String attributes;

  @ManyToOne
  @JoinColumn(name = "template_id", foreignKey = @ForeignKey(name = "template_id"))
  private TemplateDetails templateDetails;

  public ActivityDetail(long id, TaskType type, String activityId, String activityName,
                        String activityType, Object attributes, long parentId, String templateId){
    this.id = id;
    this.type = type;
    this.activityId = activityId;
    this.activityName = activityName;
    this.activityType = activityType;
    this.parentId = parentId;
    this.attributes = (String)attributes;
    this.templateDetails = TemplateDetails.builder().id(templateId).build();
  }
}
