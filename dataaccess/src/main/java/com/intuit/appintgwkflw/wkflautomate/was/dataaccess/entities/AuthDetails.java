package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(

    name = "de_auth_details",
    indexes = {@Index(name = "owner_id_auth_details_idx", columnList = "ownerId")})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AuthDetails {

  @Id
  @Column(name = "auth_details_id", unique = true, nullable = false)
  private String authDetailsId;

  private String subscriptionId;

  private Long createdByUserId;

  @Column(unique = true)
  private Long ownerId;

  private Timestamp createdDate;

  private Timestamp modifiedDate;
}
