package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

/**
 * It's a database entity object pointing to definition activity detail table
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "de_definition_activity_details",
    indexes = {
        @Index(name = "definition_details_idx", columnList = "definition_details_definition_id")})
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@Getter
@Setter
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DefinitionActivityDetail {

  @Id
  @Column(name = "id", unique = true, nullable = false)
  private String id;

  private String activityId;

  @ManyToOne
  @JoinColumn(name = "parent_id")
  private DefinitionActivityDetail parentActivityDetail;

  @OneToMany(mappedBy = "parentActivityDetail", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  private List<DefinitionActivityDetail> childActivityDetails = new ArrayList<>();

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String userAttributes;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "definition_id"))
  private DefinitionDetails definitionDetails;

  public DefinitionActivityDetail(String id, String activityId, Object userAttributes,
      String parentId, String definitionId) {
    this.id = id;
    this.activityId = activityId;
    this.parentActivityDetail = DefinitionActivityDetail.builder().id(parentId).build();
    this.userAttributes = (String) userAttributes;
    this.definitionDetails = DefinitionDetails.builder().definitionId(definitionId).build();
  }
}