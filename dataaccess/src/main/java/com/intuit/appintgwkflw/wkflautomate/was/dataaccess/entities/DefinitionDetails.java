package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.InternalStatusConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.ModelTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.RecordTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.StatusConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import java.sql.Timestamp;
import java.util.Date;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;

@Entity
@Table(
    name = "de_definition_details",
    indexes = {
      @Index(
          name = "owner_id_template_details_idx",
          columnList = "ownerId,template_details_template_id")
    })
@TypeDefs({
        @TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DefinitionDetails {

  @Id
  @Column(name = "definition_id", unique = true, nullable = false)
  private String definitionId;

  private String definitionKey;

  private String parentId;

  @Convert(converter = ModelTypeConverter.class)
  private ModelType modelType;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "template_id"))
  private TemplateDetails templateDetails;

  @Convert(converter = StatusConverter.class)
  private Status status;

  @Convert(converter = InternalStatusConverter.class)
  private InternalStatus internalStatus;

  // companyId
  private Long ownerId;

  // appId of the application creating the definition
  private Long offeringId;

  private Long createdByUserId;

  private Long modifiedByUserId;

  // this is the workflowId created in appconnect
  private String workflowId;

  // Name of definition given by user or default to name specified in XML.
  private String definitionName;

  // Definition description given by user or default to description specified in XML.
  private String description;

  // Record type associated with definition
  // RecordType can be null for on demand use cases
  @Convert(converter = RecordTypeConverter.class)
  @Nullable
  private RecordType recordType;

  private Timestamp createdDate;

  private Timestamp modifiedDate;

  private int version;

  @Column(name = "entity_version")
  private int entityVersion;

  // BPMN XML Definition
  private byte[] definitionData;

  //JSON string for placeholder value in case of Single Definition
  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String placeholderValue;

  //JSON string for lookup keys "{"envelopeId":"1234", "customId":"1234"}"
  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String lookupKeys;

  private Timestamp originalSetupDate;

  private Long originalSetupUser;

  /*Custom constructor that fetches all the data except definition_data and placeholder values */
  public DefinitionDetails(String definitionId, String definitionKey, String parentId,
      ModelType modelType, Status status, InternalStatus internalStatus, Long ownerId,
      Long offeringId, String workflowId, String definitionName, String description,
      RecordType recordType, int version, Object lookupKeys, int entityVersion, String templateId,
      DefinitionType definitionType, String deployedDefinitionId, String templateName,
      String templateCategory, int templateVersion, Date createdDate, Date modifiedDate,
      Long createdByUserId, Date originalSetupDate, Long originalSetupUser, Long modifiedByUserId) {

    this(definitionId, definitionKey, parentId, modelType, status, internalStatus, ownerId,
        offeringId, workflowId, definitionName,
        description, recordType, version, null, lookupKeys, entityVersion, templateId,
        definitionType, deployedDefinitionId, templateName
    );
    this.templateDetails.setTemplateCategory(templateCategory);
    this.templateDetails.setVersion(templateVersion);
    this.createdDate = new Timestamp(createdDate.getTime());
    this.modifiedDate = new Timestamp(modifiedDate.getTime());
      
    //TODO:: Remove once migration is completed
    if(originalSetupDate != null)
        this.originalSetupDate = new Timestamp(originalSetupDate.getTime());
      
    this.originalSetupUser = originalSetupUser;
    this.createdByUserId = createdByUserId;
    this.modifiedByUserId = modifiedByUserId;
  }

  /*Custom constructor that fetches all the data except definitiondata */
  public DefinitionDetails(String definitionId, String definitionKey, String parentId,
      ModelType modelType, Status status, InternalStatus internalStatus, Long ownerId,
      Long offeringId, String workflowId, String definitionName, String description,
      RecordType recordType, int version, Object placeholderValue, Object lookupKeys, int entityVersion, String templateId,
      DefinitionType definitionType, String deployedDefinitionId, String templateName) {
    this.definitionId = definitionId;
    this.definitionKey = definitionKey;
    this.parentId = parentId;
    this.modelType = modelType;
    this.status = status;
    this.internalStatus = internalStatus;
    this.ownerId = ownerId;
    this.offeringId = offeringId;
    this.workflowId = workflowId;
    this.definitionName = definitionName;
    this.description = description;
    this.recordType = recordType;
    this.version = version;
    //Cast is required as JPA is returning Object type for the JSON placeholder value
    this.placeholderValue = (String) placeholderValue;
    this.lookupKeys = (String)lookupKeys;
    //defType and deployedDefinitionId is required to determine if its a SINGLE def and use the
    //deployedDefinitionId to trigger the process.
    this.templateDetails = TemplateDetails.builder().id(templateId).definitionType(
        definitionType).deployedDefinitionId(deployedDefinitionId)
        .templateName(templateName).build();

    this.entityVersion = entityVersion;
  }

  public DefinitionDetails(
      String definitionId,
      String definitionKey,
      ModelType modelType,
      Status status,
      InternalStatus internalStatus,
      Long ownerId,
      RecordType recordType,
      String templateId,
      String templateName) {
    this.definitionId = definitionId;
    this.definitionKey = definitionKey;
    this.modelType = modelType;
    this.status = status;
    this.internalStatus = internalStatus;
    this.ownerId = ownerId;
    this.recordType = recordType;
    this.templateDetails =
        TemplateDetails.builder().id(templateId).templateName(templateName).build();
  }

  public DefinitionDetails(String templateId) {
    this.templateDetails = TemplateDetails.builder().id(templateId).build();
  }

}
