package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import com.intuit.system.interfaces.BaseEntity;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.json.JSONObject;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "outbox")
@TypeDefs({@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)})
@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DomainEvent<T extends BaseEntity> {
  @Id
  @GeneratedValue
  @Column(name = "event_id")
  private UUID eventId;

  @Column(name = "topic")
  private String topic;

  @Column(name = "partition_key")
  private String partitionKey;

  @Column(name = "utc_time")
  @CreationTimestamp
  private Timestamp utcTime;

  @Column(name = "region")
  private String region;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb", name = "headers")
  private DomainEventHeaders headers;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb", name = "payload")
  @RedactSensitiveField
  private String payload;

  @Column(name = "version")
  private int version;

  public DomainEvent(
          UUID eventId, String topic, String partitionKey, String region, Object headers, Object payload, int version ) {
    this.eventId = eventId;
    this.topic = topic;
    this.partitionKey = partitionKey;
    this.region = region;
    this.headers = ObjectConverter.convertObject(headers, DomainEventHeaders.class);
    this.payload = ObjectConverter.convertObject(payload, JSONObject.class).toString();
    this.version = version;
  }
}
