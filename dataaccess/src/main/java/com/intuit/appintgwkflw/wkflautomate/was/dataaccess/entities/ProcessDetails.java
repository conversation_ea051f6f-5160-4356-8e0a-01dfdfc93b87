package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.InternalStatusConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.ProcessStatusConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;

import javax.persistence.Transient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Version;
import java.sql.Time;
import java.sql.Timestamp;

@Entity
@Table(
    name = "ru_process_details",
    indexes = {@Index(name = "record_id_idx", columnList = "recordId")})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProcessDetails {

  @Id
  @Column(name = "process_id", unique = true, nullable = false)
  private String processId;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "definition_id"))
  private DefinitionDetails definitionDetails;

  private Long ownerId;

  private String recordId;

  @Convert(converter = ProcessStatusConverter.class)
  private ProcessStatus processStatus;

  @Convert(converter = InternalStatusConverter.class)
  private InternalStatus internalStatus;

  private Timestamp createdDate;

  private Timestamp modifiedDate;

  private String parentId;

  @Transient
  private ProcessDetails parentProcessDetails;

  @Version
  @Column(name = "entity_version")
  private Integer entityVersion;

  public ProcessDetails(
      String processId,
      ProcessStatus processStatus,
      InternalStatus internalStatus,
      long ownerId,
      String recordId) {
    this.processId = processId;
    this.ownerId = ownerId;
    this.recordId = recordId;
    this.processStatus = processStatus;
    this.internalStatus = internalStatus;
  }

  //Constructor that fetches all data except parentProcessDetails, definitionData and templateData
  public ProcessDetails(
          String processId, Long ownerId, String recordId, ProcessStatus processStatus, InternalStatus internalStatus, String parentId, Integer entityVersion,
          String definitionId, String definitionKey, String definitionParentId,
          ModelType modelType, Status status, InternalStatus definitionInternalStatus, Long defintionOwnerId,
          Long offeringId, String workflowId, String definitionName, String description,
          RecordType recordType, int version, Object placeholderValue, Object lookupKeys, int definitionEntityVersion, String templateId,
          DefinitionType definitionType, String deployedDefinitionId, String templateName){

    this.processId = processId;
    this.ownerId = ownerId;
    this.recordId = recordId;
    this.processStatus = processStatus;
    this.internalStatus = internalStatus;
    this.parentId = parentId;
    this.entityVersion = entityVersion;

    this.definitionDetails = new DefinitionDetails(definitionId, definitionKey, definitionParentId, modelType, status, definitionInternalStatus, defintionOwnerId,
            offeringId, workflowId, definitionName, description, recordType, version, placeholderValue, lookupKeys, definitionEntityVersion, templateId,
            definitionType, deployedDefinitionId, templateName);

  }
}
