package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.SchedulerActionConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(
    name = "de_scheduler_details",
    indexes = {
      @Index(
          name = "scheduler_details_definition_details_definition_id_idx",
          columnList = "definition_details_definition_id")
    })
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SchedulerDetails {
  @Id
  @Column(name = "scheduler_id", unique = true, nullable = false)
  private String schedulerId;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "definition_id"))
  private DefinitionDetails definitionDetails;

  @Convert(converter = SchedulerActionConverter.class)
  private SchedulerAction schedulerAction;

  private Long ownerId;

  private Boolean isMigrated;

  /**
   * This constructor use for scheduler details without definitionData.
   *
   * @param schedulerId
   * @param schedulerAction
   * @param definitionId
   */
  public SchedulerDetails(
      String schedulerId,
      SchedulerAction schedulerAction,
      String definitionId) {
    this.schedulerId = schedulerId;
    this.schedulerAction = schedulerAction;
    this.definitionDetails = DefinitionDetails.builder().definitionId(definitionId).build();
  }
}
