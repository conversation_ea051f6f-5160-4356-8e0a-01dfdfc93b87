package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.sql.Timestamp;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;


/**
 * <AUTHOR>
 * <p>
 * <p>
 * TransactionDetails represents an entity(row) from table - ru_transaction_progress_details.
 * Downstream task(from ProjectService/OINP etc) state is saved along with other essentials.
 */

@Entity
@Table(name = "ru_transaction_progress_details", indexes = {
    @Index(name = "ru_transaction_progress_details_txnId_idx", columnList = "txn_id", unique = false)})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDetails {

  public TransactionDetails(long id) {
    this.id = id;
  }

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Long id;

  // txnId and processId(workflowId) will be used to uniquely identify a
  // transaction.
  @Column(name = "txn_id")
  private String txnId;

  //TODO: For future use cases.
//	@Column(name = "source")
//	private String source;

  @Column(name = "status")
  private String status;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String attributes;

  @Column(columnDefinition = "start_time")
  @CreationTimestamp
  private Timestamp startTime;

  @Column(columnDefinition = "end_time", nullable = true)
  private Timestamp endTime;

  @Version
  @Column(columnDefinition = "updated_time", nullable = true)
  @Type(type = "dbtimestamp")
  private Timestamp updatedTime;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumn(name = "txn_progress_details_id")
  @JsonManagedReference
  private List<ActivityProgressDetails> activityProgressDetails;

}