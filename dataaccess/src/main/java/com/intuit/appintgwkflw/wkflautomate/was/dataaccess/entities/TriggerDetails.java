package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ConnectionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TriggerType;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.RecordTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.TriggerDetailsConverter;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;
import javax.persistence.ForeignKey;
import javax.persistence.Convert;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;


@Entity
@Table( name = "de_trigger_details",
        indexes = {@Index(name="trigger_name_idx", columnList = "triggerName")})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class TriggerDetails {

  @Id
  @Column(unique = true)
  private String id;

  private String triggerName;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "template_id"))
  private TemplateDetails templateDetails;

  @ManyToOne
  @JoinColumn(foreignKey = @ForeignKey(name = "definition_id"))
  private DefinitionDetails definitionDetails;

  @Convert(converter =  TriggerDetailsConverter.class)
  private TriggerType triggerType;

  private boolean hasLinkedId;

  @Enumerated(EnumType.STRING)
  private ConnectionType connectionType;

  private String connectionPath;

  @Convert(converter =  RecordTypeConverter.class)
  private RecordType recordType;

  private String skipToTaskId;

  private Timestamp createdDate;

  private Timestamp modifiedDate;

  @PrePersist
  protected void onCreate() {
    createdDate = new Timestamp(System.currentTimeMillis());
  }

  @PreUpdate
  protected void onUpdate() {
    modifiedDate = new Timestamp(System.currentTimeMillis());
  }

}
