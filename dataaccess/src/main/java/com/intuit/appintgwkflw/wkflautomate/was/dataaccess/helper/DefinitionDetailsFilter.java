package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.FilterType;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.v4.query.FilterExpression;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DefinitionDetailsFilter {

    private final String DEFINITION_DETAILS_TABLE_NAME = "definitionDetails";

    public boolean validateFilter(FilterExpression filterExpression) {
        if(ObjectUtils.isEmpty(filterExpression)) return false;

        String property = filterExpression.getProperty();
        boolean isValidFilter = FilterType.isValidFilterType(property);
        WorkflowLogger.info(
                () ->
                        WorkflowLoggerRequest.builder()
                                .className(this.getClass().getSimpleName())
                                .message(String.format("Definition details filter type %s is %s", property, isValidFilter))
                                .downstreamComponentName(DownstreamComponentName.WAS)
                                .downstreamServiceName(DownstreamServiceName.WAS_DEFINITION_FILTER_TYPE));
        return isValidFilter;
    }

    public Predicate getFilter(FilterExpression filterExpression, Root<DefinitionDetails> detailsRoot) {
        String property = filterExpression.getProperty();
        FilterType filterType = FilterType.fromType(property);
        //LookupKeys filterType will come here in case of incorrect order (key not immediately followed by value or value is put first in the filter query)
        WorkflowVerfiy.verify(isLookupFilter(filterType), WorkflowError.INCORRECT_LOOKUP_FILTER_QUERY, "step=lookupKeysError Incorrect order of lookupKeys in filter expression");
        Expression expression = getRootForProperty(property, detailsRoot);

        if(filterType.getValueConversionFunction() != null) {
            return expression.in(filterExpression.getArgs().stream()
                    .map(argument -> filterType.getValueConversionFunction().apply((String) argument))
                    .collect(Collectors.toList()));
        } else {
            return expression.in(filterExpression.getArgs());
        }
    }

    private Expression<?> getRootForProperty(String property, Root<DefinitionDetails> detailsRoot) {
        //Todo: Will add filtering property for other definition details related fields
        FilterType filterType = FilterType.fromType(property);
        if (filterType != null) {
            if(filterType.getTable().equals(DEFINITION_DETAILS_TABLE_NAME))
                return detailsRoot.get(filterType.getColumn());
            else
                return detailsRoot.get(filterType.getTable()).get(filterType.getColumn());
        }
        return null;
    }

    /**
     * Create a predicate by getting argument values from lookupKeyExpression and lookupValueExpression
     * e.g. lookupKeyExpression = 'lookupKey.key = 'envelopeId'' and lookupValueExpression = 'lookupKey.value = '1234''
     * Result predicate = jsonb_extract_path(lookupKeys, 'envelopeId') := '1234'
     * @param criteriaBuilder
     * @param detailsRoot
     * @param lookupKeyExpression
     * @param lookupValueExpression
     * @return
     */
    public Predicate getPredicateForLookupExpression(CriteriaBuilder criteriaBuilder, Root<DefinitionDetails> detailsRoot,
                                                     FilterExpression lookupKeyExpression, FilterExpression lookupValueExpression){

        //get the value of lookupKey from expression arguments (first value from the argument list)
        //Only equality operation is supported, cannot have more than one argument
        WorkflowVerfiy.verify((lookupKeyExpression.getArgs().size()>1), WorkflowError.UNSUPPORTED_LOOKUP_FILTER_QUERY);
        String lookupKey = (String) lookupKeyExpression.getArgs().iterator().next();
        WorkflowVerfiy.verify(Strings.isBlank(lookupKey), WorkflowError.UNSUPPORTED_LOOKUP_FILTER_QUERY);

        List<Expression<String>> paramList = new ArrayList<>();
        paramList.add(detailsRoot.get(FilterType.LOOKUP_KEY.getColumn()));
        paramList.add(criteriaBuilder.literal(lookupKey));
        Expression<String>[] parameters = paramList.toArray(new Expression[0]);

        Expression<String> criteriaExpression = criteriaBuilder.function(WorkflowConstants.JSONB_EXTRACT_PATH_TEXT, String.class, parameters);
        return criteriaExpression.in(lookupValueExpression.getArgs());

    }

    private boolean isLookupFilter(FilterType filterType){
        return (filterType == FilterType.LOOKUP_KEY || filterType == FilterType.LOOKUP_VALUE);
    }

}
