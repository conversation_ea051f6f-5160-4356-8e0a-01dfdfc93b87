package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.helper;


import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.guranteedconsumer.commons.domain.interfaces.EntityManagerProvider;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Component
public class WASEntityManagerProvider implements EntityManagerProvider {

    @PersistenceContext(unitName = DataAccessConstants.OFFERING_AWARE_EM)
    private EntityManager entityManager;

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }
}