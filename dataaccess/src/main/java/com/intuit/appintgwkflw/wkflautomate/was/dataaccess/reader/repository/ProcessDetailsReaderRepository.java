package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.reader.repository;


import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.data.jpa.repository.JpaRepository;


@Retry(name = ResiliencyConstants.WAS_DB)
public interface ProcessDetailsReaderRepository
    extends JpaRepository<ProcessDetails, String> {

}
