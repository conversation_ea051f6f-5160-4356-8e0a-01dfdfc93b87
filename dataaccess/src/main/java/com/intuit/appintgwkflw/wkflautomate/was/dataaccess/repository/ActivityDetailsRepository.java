package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
/**
 *
 * <AUTHOR>
 *
 */
@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface ActivityDetailsRepository extends JpaRepository<ActivityDetail, String> {


  /**
   *
   * @param templateDetails
   * @param activityId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<ActivityDetail> findByTemplateDetailsAndActivityId(TemplateDetails templateDetails,
      String activityId);


  /**
   *
   * @param templateDetails
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  void deleteByTemplateDetails(TemplateDetails templateDetails);

  /**
   * Query based on processId and ownerId
   * @param processId processId
   * @param ownerId ownerId
   * @return list of ActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select new ActivityDetail (ad.id, ad.type, ad.activityId, ad.activityName, ad.activityType, ad.attributes, ad.parentId, ad.templateDetails.id)"
      + " from ActivityDetail ad left join TemplateDetails td on td.id = ad.templateDetails.id"
      + " left join DefinitionDetails dd on dd.templateDetails.id = td.id"
      + " left join ProcessDetails pd on pd.definitionDetails.definitionId = dd.definitionId"
      + " where pd.processId = :processId and pd.ownerId = :ownerId")
  List<ActivityDetail> findByProcessId(@Param("processId") String processId, @Param("ownerId") Long ownerId);

  /**
   * Query based on processId, activityId, ownerId
   * @param processId
   * @param activityId
   * @param ownerId
   * @return ActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select new ActivityDetail (ad.id, ad.type, ad.activityId, ad.activityName, ad.activityType, ad.attributes, ad.parentId, ad.templateDetails.id)"
      + " from ActivityDetail ad left join TemplateDetails td on td.id = ad.templateDetails.id"
      + " left join DefinitionDetails dd on dd.templateDetails.id = td.id"
      + " left join ProcessDetails pd on pd.definitionDetails.definitionId = dd.definitionId"
      + " where pd.processId = :processId and ad.activityId = :activityId and pd.ownerId = :ownerId")
  List<ActivityDetail> findByProcessIdAndActivityId(
      @Param("processId") String processId, @Param("activityId") String activityId, @Param("ownerId") Long ownerId);
  
  
  /**
   * Query Plan::
   * "Index Scan using template_id_activity_id_idx on de_activity_details  (cost=0.14..8.16 rows=1 width=2628)"
   *		Index Cond: ((template_id)::text = 'da154822-4c2f-4478-9c5f-8cafdb77e4d9'::text)
   *		Filter: ((type)::text = 'MILESTONE'::text)
   * Fetch all milestone elements for the given templateId.
   * @param templateId
   * @param type
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select ad from ActivityDetail ad "
	  + " where ad.templateDetails.id = :templateId and ad.type = :type ")
  List<ActivityDetail> findByTemplateIdAndType(@Param("templateId") String templateId,
		  @Param("type") TaskType type);


  /**
   * Fetches a list of ActivityDetail entities based on the provided template ID and activity type.
   *
   * @param templateId the ID of the template to filter the ActivityDetail entities
   * @param activityType the type of activity to filter the ActivityDetail entities
   * @return a list of ActivityDetail entities that match the given template ID and activity type
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select ad from ActivityDetail ad "
          + " where ad.templateDetails.id = :templateId and ad.activityType = :activityType ")
  List<ActivityDetail> findByTemplateIdAndActivityType(@Param("templateId") String templateId,
                                                       @Param("activityType") String activityType);

}