package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import io.github.resilience4j.retry.annotation.Retry;

/**
 * <AUTHOR>
 */
@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface ActivityProgressDetailsRepository 
  extends JpaRepository<ActivityProgressDetails, String> {


  /**
   * Delete activity by process id
   *
   * @param processDetails Collection of ProcessInstance ids.
   * @return No. of activities deleted by processInstance id
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Modifying
  @Query("delete from ActivityProgressDetails activityProgressDtls"
      + " where activityProgressDtls.id IN :activityDetailIds")
  int deleteByIdIn(@Param("activityDetailIds") final Set<String> activityDetailIds);


  /**
   * Find all activityProgressDetails from  Collection of ProcessInstance ids.
   *
   * @param processDetails : Collection of ProcessInstance ids.
   * @return Collection of activityProgressDetails.
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select new ActivityProgressDetails (activityProgressDtls.id, txnDetails) "
          + " from ActivityProgressDetails activityProgressDtls"
          + " left join TransactionDetails txnDetails on activityProgressDtls.txnDetails = txnDetails"
          + " where activityProgressDtls.processDetails.processId IN :processDetails")
  List<ActivityProgressDetails> findByProcessDetailsIn(
      @Param("processDetails") final List<String> processDetails);

  /**
   * Query based on taskId, ownerId
   *
   * @param taskId
   * @param ownerId
   * @return ActivityProgressDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      value =
          "select new ActivityProgressDetails( apd.id, apd.name, apd.status, apd.attributes, apd.startTime, apd.endTime,"
              + " apd.updatedTime, apd.processDetails.processId, td.id, td.txnId, ad.id, ad.type,"
              + " ad.activityId, ad.activityName, ad.activityType,"
              + " ad.attributes, ad.parentId, ad.templateDetails.id,apd.version)"
              + " from ActivityProgressDetails apd left join ActivityDetail ad on ad.id = apd.activityDefinitionDetail.id"
              + " left join ProcessDetails pd on pd.processId = apd.processDetails.processId"
              + " left join TransactionDetails td on td.id = apd.txnDetails.id"
              + " where apd.id = :taskId and pd.ownerId = :ownerId")
  Optional<ActivityProgressDetails> findByTaskId(
      @Param("taskId") String taskId, @Param("ownerId") Long ownerId);

  // TODO: Index on activity id??
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select count(apd) from ActivityProgressDetails apd join apd.activityDefinitionDetail ad "
          + "where ad.activityId = :activityId and apd.processDetails.processId = :processId")
  int getCountOfExternalTasksPerActivityPerProcess(@Param("activityId")String activityId, @Param("processId") String processId);
  
  
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select activityProgressDtls  from ActivityProgressDetails activityProgressDtls"
          + " left join TransactionDetails txnDetails on activityProgressDtls.txnDetails = txnDetails"
          + " where activityProgressDtls.processDetails.processId IN :processDetails"
          + " and activityProgressDtls.activityDefinitionDetail.type IN :type and activityProgressDtls.id NOT LIKE '%:%'")
  List<ActivityProgressDetails> findByProcessDetailsAndTypeIn(
      @Param("processDetails") final List<String> processDetails, 
      @Param("type") final List<TaskType> type,
      Pageable page);
  
  
  
  /**
   * Nested Loop  (cost=0.14..18.54 rows=1 width=4768)
   *		Join Filter: (de.id = ru.activity_details_id)
   *		->  Index Scan using ru_activity_progress_details_workflow_id_idx on ru_activity_progress_details ru  (cost=0.14..8.15 rows=1 width=2140)
   *	     	Index Cond: ((workflow_id)::text = '0b3425fa-d3cc-11ee-b5b2-4e56c0d25900'::text)
   *		->  Seq Scan on de_activity_details de  (cost=0.00..10.38 rows=1 width=2628)
   *    	 	Filter: ((type)::text = 'MILESTONE'::text)
   *    Fetch all Activity Details of a workflow filtered by Type.
   * @param processId
   * @param type
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select activityProgressDtls from ActivityProgressDetails activityProgressDtls join activityProgressDtls.activityDefinitionDetail ad "
          + " where activityProgressDtls.processDetails.processId = :processId and ad.type = :type")
  List<ActivityProgressDetails> findByProcessIdAndType(
		  @Param("processId")String processId, @Param("type")TaskType type);
  
  
}
