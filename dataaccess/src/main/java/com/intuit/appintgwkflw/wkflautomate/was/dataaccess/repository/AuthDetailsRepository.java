package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface AuthDetailsRepository extends JpaRepository<AuthDetails, String> {

  /**
   * Find the AuthDetails by realmId
   *
   * @param ownerId id of the realm
   * @return {@link AuthDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<AuthDetails>> findAuthDetailsByOwnerId(Long ownerId);

  /**
   * Find by definitionId
   * @param definitionId
   * @return
   */
  @Query(
      "select auth from AuthDetails auth,DefinitionDetails defn where defn.ownerId=auth.ownerId and defn.definitionId = ?1")
  @ServiceMetric(serviceName = WAS_DB)
  Optional<AuthDetails> findAuthByDefinitionId(@Param("definitionId") String definitionId);

  /**
   * Find by processId
   * @param processId
   * @return
   */
  @Query(
          "select auth from AuthDetails auth,ProcessDetails pdetails where pdetails.ownerId=auth.ownerId and pdetails.processId = ?1")
  @ServiceMetric(serviceName = WAS_DB)
  Optional<AuthDetails> findAuthByProcessId(@Param("processId") String processId);

  /** delete the auth details by owner id */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByOwnerId(long ownerId);
}
