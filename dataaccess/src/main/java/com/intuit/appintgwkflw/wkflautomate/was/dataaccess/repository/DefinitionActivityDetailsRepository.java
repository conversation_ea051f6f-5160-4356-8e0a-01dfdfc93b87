package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * This repository class is used to access definition activity detail table
 *
 * <AUTHOR>
 */
@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface DefinitionActivityDetailsRepository extends
    JpaRepository<DefinitionActivityDetail, String> {

  /**
   * Query used to fetch the definition activity details for the given activityId and a list of
   * definitionDetails
   *
   * @param definitionDetails
   * @param activityId
   * @return DefinitionActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<DefinitionActivityDetail> findByDefinitionDetailsInAndActivityId(
      List<DefinitionDetails> definitionDetails, String activityId);

  /**
   * Query used to fetch definition activity detail for a particular child task(for ex. create Task)
   * by using it's parent activity id, activity id and definition id
   *
   * @param definitionId
   * @param activityId
   * @param parentActivityId
   * @return DefinitionActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value =
      "select new DefinitionActivityDetail (childActivityDetail.id, childActivityDetail.activityId, "
          + "childActivityDetail.userAttributes, childActivityDetail.parentActivityDetail.id ,"
          + "childActivityDetail.definitionDetails.definitionId) "
          + "from DefinitionActivityDetail parentActivityDetail inner join DefinitionActivityDetail childActivityDetail "
          + "on parentActivityDetail.id = childActivityDetail.parentActivityDetail.id "
          + "where parentActivityDetail.definitionDetails.definitionId = :definitionId "
          + "and parentActivityDetail.activityId = :parentActivityId "
          + "and childActivityDetail.activityId = :activityId")
  Optional<DefinitionActivityDetail> findActivityDetailsByDefinitionIdAndParentId(
      @Param("definitionId") String definitionId, @Param("activityId") String activityId,
      @Param("parentActivityId") String parentActivityId);

  /**
   * Query to fetch definition activity details based on definitionId and activityId(for dmn/call
   * activity)
   *
   * @param activityId
   * @param definitionId
   * @return DefinitionActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<DefinitionActivityDetail> findDefinitionActivityDetailByActivityIdAndDefinitionDetails_DefinitionId(
      String activityId, String definitionId);

  /**
   * Query to fetch definition activity details based on definitionId
   *
   * @param definitionId definitionId
   * @return list of DefinitionActivityDetail
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionActivityDetail>> findAllByDefinitionDetails_DefinitionId(
      String definitionId);

  /**
   * This method deletes all the definition activity details for the given definitionDetails
   * @param definitionDetails
   * @return
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByDefinitionDetailsIn(final List<DefinitionDetails> definitionDetails);
}
