package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.interaction.query.QueryHelper;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

public interface DefinitionDetailsCustomRepository {

  /**
   * @param ownerId
   * @param modelType
   * @param query
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllDefinitionsWithoutWorkflowSteps(
      Long ownerId, ModelType modelType, QueryHelper query);

  /**
   * @param ownerId
   * @param modelType
   * @param query
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllDefinitions(
      Long ownerId, ModelType modelType, QueryHelper query);

  /**
   *
   *
   * <pre>
   * Get definitions for a given ownerId, model type and recordType with following
   * additional conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   *
   * @param ownerId
   * @param modelType
   * @param recordType
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdAndRecordTye(
      Long ownerId, ModelType modelType, RecordType recordType);

  /**
   *
   * <pre>
   * Get definitions for a given ownerId, model type and templatename with following
   * additional conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   *
   * @param ownerId
   * @param modelType
   * @param templateName
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdAndTemplateName(
      Long ownerId, ModelType modelType, String templateName, boolean isDefinitionDataRequired);


  /**
   *
   * Get definitions for a given ownerId, model type and template category
   * with following additional conditions -
   *  1. Status is enabled and internal status is null or STALE_DEFINITION
   *
   * @param ownerId
   * @param modelType
   * @param templateCategory
   * @param isDefinitionDataRequired
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdTemplateCategoryAndWorkflows(
          Long ownerId, ModelType modelType, String templateCategory, boolean isDefinitionDataRequired, Set<String> templateNames);

  /**
   * <pre>
   * Get definitions for a given ownerId, model type, template name and recordType with following
   * additional conditions -
   * Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   *
   * @param ownerId
   * @param modelType
   * @param recordType
   * @param templateName
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
      Long ownerId, ModelType modelType, RecordType recordType, String templateName);


  /**
   * <pre>
   * Get definition keys of definitions for a given ownerId, model type, template name and recordType with following
   * additional conditions -
   * Status is enabled and internal status is null or STALE_DEFINITION
   * </pre>
   *
   * @param ownerId
   * @param modelType
   * @param recordType
   * @param templateName
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<Set<String>> findDefinitionKeysOfAllEnabledDefinitionsForOwnerIdModelRecordTypeAndTemplateName(
          Long ownerId, ModelType modelType, RecordType recordType, String templateName);

  /**
   * <pre>
   * Get definitions for a given ownerId, model type, template name and recordType with following
   * additional conditions -
   * Status is enabled and internal status is null
   * </pre>
   *
   * @param ownerId
   * @param modelType
   * @param recordType
   * @param templateName
   * @param isDataDefinitionRequired
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
      Long ownerId, ModelType modelType, RecordType recordType, String templateName, boolean isDataDefinitionRequired);

  /**
   * @param ownerId
   * @param modelType
   * @param templateDetails
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllEnabledDefinitions(
      Long ownerId, ModelType modelType, TemplateDetails templateDetails);

  /**
   * @param ownerId
   * @return : Unique Definition Keys for the Realm Id and ModelType BPMN
   */
  @ServiceMetric(serviceName = WAS_DB)
  List<String> findDistinctByDefinitionKeyAndOwnerIdAndModelType(Long ownerId, ModelType modelType);

  /**
   * @param ownerId
   * @param workflowId
   * @return {@link DefinitionDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  DefinitionDetails findEnabledDefinitionForWorkflowId(Long ownerId, String workflowId);

  /**
   *
   *
   * <pre>
   * Get definitions for a given ownerId and provider workflow id with following additional
   * conditions -
   * 1. Status is enabled and internal status is null or STALE_DEFINITION
   * OR
   * 2. internal status is MARKED_FOR_DELETE or MARKED_FOR_DISABLE
   * </pre>
   *
   * @param ownerId
   * @param workflowId
   * @param isDefinitionDataRequired : If definitionData needs to be returned or not
   * @return {@link DefinitionDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  List<DefinitionDetails> findEnabledAndMarkedDefinitionForWorkflow(
      Long ownerId, String workflowId, boolean isDefinitionDataRequired);

  /**
   * return enabled definition details where status is enabled and internal status null or status
   * enabled and internal status stale. So that older version can still be signaled but new process
   * will be started with the latest enabled definition.
   *
   * @param modelType model type
   * @param templateDetails List<TemplateDetails> template details
   * @return {@link Optional<DefinitionDetails>}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findEnabledSystemDefinition(
      ModelType modelType, List<TemplateDetails> templateDetails);

  /**
   * return enabled definition details for the given template for on demand approval where
   * owner id is the system user and record type is null
   * status is enabled and internal status null or status enabled and internal status stale.
   * @param modelType
   * @param templateDetails
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findEnabledSystemDefinitionForOnDemandApproval(ModelType modelType, List<TemplateDetails> templateDetails);

  /**
   * This method return list of Definition Details object[without definitionData XML] of all the
   * definitions [be it enabled or disabled] which will be visible to the user on Workflow Hub UI.
   *
   * @param ownerId
   * @param definitionName
   * @return {@link List<DefinitionDetails>}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findDefinitionsForCustomWorkflow(
      String ownerId, String definitionName);


  /**
   * This method will fetch all the definition ( BPMN and DMN ) with supplied ids.
   *
   * @param ids : List of Definition IDs.
   * @param ids : List of parent IDs which refers to parentId for DMN
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findAllByDefinitionsInParentId(List<String> ids, List<String> parentId);

  /**
   * This method returns the enabled definitions WITHOUT the definition data xml
   * @param ownerId
   * @param templateId
   * @param recordType
   * @param modelType
   * @return Enabled Definitions already existing for a given template
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>>
  findEnabledDefinitionForOwnerIdTemplateIdRecordTypeAndModelType(
      long ownerId, String templateId, RecordType recordType, ModelType modelType);


  /**
   * Find the eligible definitions based on definitionKey param
   * @param definitionKey - definitionKey for the workflow
   * @param ownerId - companyId
   * @param isDefinitionDataRequired - should the definitionData be fetched
   * @return list of definitionDetails
   */
   List<DefinitionDetails> findDefinitionsByDefinitionKey(final String definitionKey,
                                                                final Long ownerId,
                                                                final boolean isDefinitionDataRequired );

  /**
   * Find the enabled definitions by definitionKey
   * @param definitionKey - definitionKey for the workflow
   * @param ownerId - companyId
   * @return definitionDetails for the definitionKey
   */
  DefinitionDetails findEnabledDefinitionForDefinitionKey(final Long ownerId,
                                                          final String definitionKey);


  /**
   * Finds a list of `DefinitionDetails` entities based on custom filter and selection criteria.
   *
   * <p>This method constructs a dynamic query using the provided filter and selection parameters.
   * It utilizes the JPA Criteria API to build the query, allowing for flexible and type-safe
   * query construction.
   *
   * @param filterParams a map containing field names as keys and their corresponding filter values.
   *                     If a value is null, the method will filter for records where the field is null.
   * @param selectParams a list of field names to be selected in the query. If the list is empty or null,
   *                     all fields will be selected. Fields from the `TemplateDetails` entity should be
   *                     prefixed with `RepositoryConstants.TEMPLATE_DETAILS_PATH`.
   * @return an `Optional` containing a list of `DefinitionDetails` entities that match the filter criteria.
   *         If no entities match, an empty list is returned.
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findDefinitionsWithCustomFilterAndSelection(
          Map<String, Object> filterParams, List<String> selectParams);

}
