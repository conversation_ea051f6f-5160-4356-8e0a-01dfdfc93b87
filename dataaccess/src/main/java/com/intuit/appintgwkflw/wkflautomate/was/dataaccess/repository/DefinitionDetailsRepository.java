package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import io.github.resilience4j.retry.annotation.Retry;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

@Retry(name = ResiliencyConstants.WAS_DB)
public interface DefinitionDetailsRepository
    extends JpaRepository<DefinitionDetails, Long>, DefinitionDetailsCustomRepository {

  /**
   * Retrieve the list of workflow definitions details for a template and company
   *
   * @param ownerId companyId for which definition is required
   * @param templateDetails {@link TemplateDetails}
   * @return list of {@link DefinitionDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByOwnerIdAndTemplateDetails(
      long ownerId, TemplateDetails templateDetails);

  /**
   * @param templateDetails
   * @param internalStatus
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByTemplateDetailsAndInternalStatus(
      TemplateDetails templateDetails, InternalStatus internalStatus);

  @ServiceMetric(serviceName = WAS_DB)
  DefinitionDetails findTopByDefinitionKeyOrderByVersionDesc(String definitionKey);

  /**
   * @param ownerId
   * @param modelType
   * @param status
   * @param parentId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>>
      findByOwnerIdAndModelTypeAndStatusAndParentIdInOrderByCreatedDateDesc(
          long ownerId, ModelType modelType, Status status, List<String> parentId);

  /**
   * Delete entries from Database by DefinitionId ( bpmnId) or by parent id ( to delete dmn )
   *
   * @param definitionId BMPNDefinitionId
   * @param parentId ParentID
   * @return how many entries deleted
   * <AUTHOR> Gupta
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByDefinitionIdOrParentId(String definitionId, String parentId);

  /**
   * Updates the workflowId in DB
   *
   * @param definitionId
   * @param workflowId
   */
  @Query(
      "update DefinitionDetails def set def.workflowId = :workflowId WHERE def.definitionId = :definitionId")
  @Modifying
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  void setWorkflowId(
      @Param("definitionId") String definitionId, @Param("workflowId") String workflowId);

  @Query("update DefinitionDetails dd set dd.definitionData = :definitionData, dd.placeholderValue = :placeholderValue where dd.definitionId = :definitionId")
  @Modifying
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  void setDefinitionDataAndPlaceholderValues(
          @Param("definitionId") String definitionId, @Param("definitionData") byte[] definitionData, @Param("placeholderValue") String placholderValue);

  /**
   * @param definitionId input definition id
   * @return {@link DefinitionDetails} associated with given definition id
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<DefinitionDetails> findByDefinitionId(String definitionId);

  /**
   * @param definitionId : input definition id
   * @param parentId : definition id which will be a parent id for DMN definitions
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByDefinitionIdOrParentId(
      String definitionId, String parentId);

  /**
   * @param parentId : Parent ID points to the Bpmn XML file which has a reference of DMN
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByParentId(String parentId);

  /**
   * @param parentId : Parent ID points to the Bpmn XML file which has a reference of DMN
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByParentIdIn(List<String> parentId);

  @Transactional
  @Modifying
  @Query(
      "update DefinitionDetails dd set dd.status=:status,dd.internalStatus=:internalStatus where dd.definitionId=:definitionId or dd.parentId=:definitionId")
  @ServiceMetric(serviceName = WAS_DB)
  int updateInternalStatusAndStatus(
      @Param("status") Status status,
      @Param("internalStatus") InternalStatus internalStatus,
      @Param("definitionId") String definitionId);

  /**
   * @param ownerId
   * @param recordType
   * @param status
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>>
      findByOwnerIdAndRecordTypeAndStatusAndModelTypeOrderByCreatedDateDesc(
          long ownerId, RecordType recordType, Status status, ModelType modelType);

  /**
   * @param ownerId
   * @param templateId
   * @param status
   * @param modelType
   * @return Enabled Definitions already existing for a given template, only bpmn
   */
  Optional<List<DefinitionDetails>>
      findByOwnerIdAndTemplateDetails_IdAndStatusAndModelTypeAndInternalStatusIsNull(
          long ownerId, String templateId, Status status, ModelType modelType);

  /**
   * @param ownerId
   * @param recordType
   * @param templateName
   * @param status
   * @param modelType
   * @return Enabled Definitions already existing for a given template, only bpmn
   */
  Optional<List<DefinitionDetails>>
  findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
      long ownerId, RecordType recordType, String templateName, Status status, ModelType modelType);

  /**
   * @param ownerId
   * @param recordType
   * @param templateName
   * @param status
   * @param modelType
   * @return Enabled Definitions already existing for a given template, only bpmn
   */
  Optional<List<DefinitionDetails>>
  findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatus(
      long ownerId, RecordType recordType, String templateName, Status status, ModelType modelType, InternalStatus internalStatus
  );

  @Transactional
  @Modifying
  @Query(
      "update DefinitionDetails dd set dd.internalStatus=:internalStatus where dd.definitionId in (:definitionIds) or dd.parentId in (:definitionIds)")
  @ServiceMetric(serviceName = WAS_DB)
  int updateInternalStatus(
      @Param("internalStatus") InternalStatus internalStatus,
      @Param("definitionIds") List<String> definitionIds);

  /**
   * @param ownerId input ownerId
   * @param definitionId input definition id
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<DefinitionDetails> findByDefinitionIdAndOwnerId(String definitionId, long ownerId);

  /**
   * @param workflowId : App Connect workflow id
   * @param ownerId : Owner Id
   * @return List of Definition IDs with a particular company and workflow id
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByWorkflowIdAndOwnerId(String workflowId, Long ownerId);

  /**
   * @param definitionKey : Definition Key
   * @param ownerId       : Owner Id
   * @return List of Definition details for a given owner id and definition key
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByDefinitionKeyAndOwnerId(String definitionKey,
      Long ownerId);

  /**
   * Finds the top (most recent) DefinitionDetails entry by definition key and owner ID, ordered by version in descending order.
   *
   * @param definitionKey the key of the definition to search for
   * @param ownerId the ID of the owner to search for
   * @return the most recent DefinitionDetails entry matching the given definition key and owner ID, ordered by version in descending order
   */
  @ServiceMetric(serviceName = WAS_DB)
  DefinitionDetails findTopByDefinitionKeyAndOwnerIdOrderByVersionDesc(String definitionKey, Long ownerId);

  /**
   * @param ownerId input ownerId
   * @param modelType input model type
   * @return list of definition for given company
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DefinitionDetails>> findByOwnerIdAndModelType(long ownerId, ModelType modelType);

  /**
   * @param ownerId input company id
   * @return count of definitions for a given company
   */
  @ServiceMetric(serviceName = WAS_DB)
  long countByOwnerId(long ownerId);

  /**
   * //TODO : Need to add check of user/system templates here?
   *
   * <p>Method to return Definitions for Realm
   *
   * @param ownerId : Realm Id of the company
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  List<DefinitionDetails> findByOwnerId(Long ownerId);

  /**
   * This method will delete all the definition with supplied ids.
   *
   * @param ids : List of Definition IDs.
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Transactional
  void deleteAllByDefinitionIdIn(List<String> ids);

  /**
   * This method will get the dmn definition details for the given provider workflow ID. For this we
   * first self join the defintion details table on version, parentId being the same as defintionId
   * and ownerId and query for a given ownerId and providerWorkflowId and get the DMN with highest version
   * @param providerWorkflowId
   * @param ownerId
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      nativeQuery = true,
      value = "select dmnDetails.* from de_definition_details bpmnDetails INNER JOIN de_definition_details as dmnDetails ON " +
          "bpmnDetails.version = dmnDetails.version and bpmnDetails.definition_id = dmnDetails.parent_id and " +
          "bpmnDetails.owner_id = dmnDetails.owner_id where " +
          "dmnDetails.owner_id =:ownerId and bpmnDetails.workflow_id =:providerWorkflowId order by dmnDetails.version desc limit 1")
  Optional<DefinitionDetails> findDmnDefinitionDetailsByWorkflowIdAndOwnerId(
      @Param("providerWorkflowId") String providerWorkflowId, @Param("ownerId") Long ownerId);

  /**
   * Find a definition by ownerId and definitionKey
   * @param definitionKey
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  DefinitionDetails findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
      Long ownerId, String definitionKey);

  /**
   * Get the count of definition created/updated for a workflow and owner/realm.
   * If count for definitions are created/updated for a given workflow and realm exceed the throttle limit we have provided
   * then we want it to throttle. It takes into consideration the total number definition in db not bound to a time window.
   *
   * @param templateName
   * @param ownerId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select count(dd) from DefinitionDetails dd join dd.templateDetails td " +
          "where td.templateName =:templateName and " +
          "dd.ownerId = :ownerId and " +
          "dd.modelType = 'bpmn' and " +
          "dd.status = 'enabled' and " +
          "dd.internalStatus is null")
  Integer getCountOfDefinitionsPerWorkflowPerRealm(
          @Param("templateName") String templateName, @Param("ownerId") Long ownerId);

  // If a definition is created and then disabled/deleted/updated immediately - and this happens continuously, we want to throttle
  // this scenario. For this, we'll have to fetch definitions of all status (i.e., all definitions that were disabled/deleted/updated) in the last window.
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select count(dd) from DefinitionDetails dd join dd.templateDetails td " +
          "where td.templateName =:templateName and " +
          "dd.createdDate >:timeframeStart and " +
          "dd.createdDate <=:timeframeEnd and " +
          "dd.modelType = 'bpmn'")
  Integer getCountOfDefinitionsPerWorkflowInTimeframe(
          @Param("templateName") String templateName, @Param("timeframeStart") Timestamp timeframeStart, @Param("timeframeEnd")Timestamp timeframeEnd);

  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "SELECT new DefinitionDetails (d.definitionId, d.definitionKey,"
          + " d.modelType, d.status, d.internalStatus, "
          + " d.ownerId, d.recordType, "
          + " d.templateDetails.id, d.templateDetails.templateName) from DefinitionDetails d "
          + "where d.ownerId = :ownerId and d.modelType = :modelType and (d.internalStatus NOT IN :internalStatuses or d.internalStatus is null)")
  Optional<List<DefinitionDetails>>
      findByOwnerIdModelTypeAndInternalStatusNotInWithoutDefinitionData(
          @Param("ownerId") Long ownerId,
          @Param("modelType") ModelType modelType,
          @Param("internalStatuses") List<InternalStatus> internalStatuses);

  /**
   * Find the smallest positive ownerId in de_definition_details table
   * This is used by custom item reader to start fetching definitions from this ownerId
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query("SELECT min(d.ownerId) from DefinitionDetails d where d.ownerId > 0")
  Optional<Long> findSmallestPositiveOwnerId();

  /**
   * Find the distinct ownerIds for a given templateIds
   * @param templateIds - templateIds for which definitions will be queried
   * @param ownerId - ownerId after which to start querying
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query("SELECT distinct d.ownerId from " +
          "DefinitionDetails d where d.modelType = 'bpmn' and " +
          "d.status = 'enabled' and " +
          "(d.internalStatus is null or d.internalStatus = 'STALE_DEFINITION') and " +
          "d.templateDetails.id IN (:templateIds) and d.ownerId > :ownerId ")
  List<Long> findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(@Param("ownerId") Long ownerId, @Param("templateIds") List<String> templateIds, Pageable pageable);

  /**
   * This method will delete all the definition with supplied parent ids.
   *
   * @param ids : List of Parent IDs.
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Transactional
  void deleteAllByParentIdIn(List<String> ids);
}
