package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface DomainEventRepository
    extends JpaRepository<DomainEvent, UUID>, DomainEventCustomRepository {

  long countByPartitionKey(String partitionKey);
}
