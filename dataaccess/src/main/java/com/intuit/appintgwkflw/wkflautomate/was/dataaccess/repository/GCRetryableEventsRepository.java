package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.guranteedconsumer.commons.core.model.GCInbox;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface GCRetryableEventsRepository extends JpaRepository<GCInbox, UUID> {

    @ServiceMetric(serviceName = WAS_DB)
    Optional<GCInbox> findById(UUID id);

    @Transactional
    @Modifying
    @Query("UPDATE GCInbox e SET e.retryCount = :retryCount, e.status = :status WHERE e.id IN :ids")
    @ServiceMetric(serviceName = WAS_DB)
    int updateRetryCountAndStatus(@Param("ids") List<UUID> ids, @Param("retryCount") int retryCount, @Param("status") String status);

    @Transactional
    @Modifying
    @Query("SELECT e.id FROM GCInbox e WHERE e.entityType = :entityType AND e.retryCount = :maxRetryCount AND e.createdAt BETWEEN :startTime AND :endTime AND e.status = 'FAILED'")
    @ServiceMetric(serviceName = WAS_DB)
    List<UUID> findRecordIdsByEntityTypeAndTimeFrame(@Param("entityType") String entityType, @Param("maxRetryCount") int maxRetryCount, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    @Transactional
    @Modifying
    @Query("UPDATE GCInbox e SET e.retryCount = 0, e.status = 'ERROR' WHERE e.entityType = :entityType AND e.retryCount = :maxRetryCount AND e.createdAt BETWEEN :startTime AND :endTime AND e.status = 'FAILED'")
    @ServiceMetric(serviceName = WAS_DB)
    List<UUID> updateRetryCountAndStatus(@Param("entityType") String entityType, @Param("maxRetryCount") int maxRetryCount, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
