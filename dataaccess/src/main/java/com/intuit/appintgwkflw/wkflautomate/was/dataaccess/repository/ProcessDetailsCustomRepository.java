package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;

import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

public interface ProcessDetailsCustomRepository {

  @ServiceMetric(serviceName = WAS_DB)
  Optional<ProcessDetails> getProcessDetailsWithoutDefinitionData(String processId);
}
