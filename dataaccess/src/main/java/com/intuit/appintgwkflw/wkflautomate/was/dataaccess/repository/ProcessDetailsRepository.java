package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

@Retry(name = ResiliencyConstants.WAS_DB)
public interface ProcessDetailsRepository
    extends JpaRepository<ProcessDetails, String>, ProcessDetailsCustomRepository {

  /**
   * Retrieve list of {@link ProcessDetails} by recordId
   *
   * @param recordId for which process-details need to be fetched
   * @return list of {@link ProcessDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<ProcessDetails> findByRecordIdAndOwnerId(String recordId, long OwnerId);

  /**
   * Retrieves process details by recordId, ownerId and status for all parent processes
   * @param recordId
   * @param ownerId
   * @param processStatus
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>>
  findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndParentIdIsNull(
          String recordId,
          long ownerId,
          List<ProcessStatus> processStatus);

  /**
   * Delete process by definition id
   *
   * @param definitionDetails definitionDetails
   * @return delete process by definition id
   * <AUTHOR> Gupta
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByDefinitionDetails(final DefinitionDetails definitionDetails);

  /**
   * @param definitionDetails definitionDetails
   * @param status Process Status
   * @return return count of process Details filter by definition id and process status
   */
  @ServiceMetric(serviceName = WAS_DB)
  long countByDefinitionDetailsAndProcessStatus(
      DefinitionDetails definitionDetails, ProcessStatus status);

  @ServiceMetric(serviceName = WAS_DB)
  @Query("select p from ProcessDetails p where p.processStatus = 'ended' and p.modifiedDate <= :modifiedDate and p.definitionDetails.definitionId = :definitionId")
  Optional<List<ProcessDetails>> findEndedProcessesModifiedBefore(@Param("definitionId") String definitionId,
                                                                  @Param("modifiedDate") Timestamp modifiedDate);

  @ServiceMetric(serviceName = WAS_DB)
  Long countByDefinitionDetails(DefinitionDetails definitionDetails);

  /** update the processStatus for given process id */
  @Transactional
  @Modifying
  @Query("update ProcessDetails pd set pd.processStatus= :status where pd.processId= :processId")
  @ServiceMetric(serviceName = WAS_DB)
  int updateProcessStatus(
      @Param("processId") String processId, @Param("status") ProcessStatus status);

  /** update the processStatus for multiple process ids */
  @Transactional
  @Modifying
  @Query("update ProcessDetails pd set pd.processStatus= :status where pd.processId in (:processIds)")
  @ServiceMetric(serviceName = WAS_DB)
  int updateProcessStatusForProcesses(
      @Param("status") ProcessStatus status,
      @Param("processIds") List<String> processIds);

  @Transactional
  @Modifying
  @Query(
      "update ProcessDetails pd set pd.internalStatus=:internalStatus where pd.processId in (:processIds)")
  @ServiceMetric(serviceName = WAS_DB)
  int updateStatus(
      @Param("internalStatus") InternalStatus internalStatus,
      @Param("processIds") List<String> processIds);

  @Transactional
  @Modifying
  @Query(
      "update ProcessDetails pd set pd.definitionDetails.definitionId=:definitionId where pd.processId=:processId")
  @ServiceMetric(serviceName = WAS_DB)
  int updateDefinitionId(
      @Param("definitionId") String definitionId,
      @Param("processId") String processId);

  /**
   * This method returns all the BPMN definitions which have process status active and internal
   * status =null for definition details in the list ordered in descending order by created date.
   *
   * @param recordId : Record ID
   * @param ownerId : Company ID
   * @param processStatus : process status
   * @param definitionDetailsList : List of Definitions
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>>
      findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
          String recordId,
          long ownerId,
          List<ProcessStatus> processStatus,
          List<DefinitionDetails> definitionDetailsList);

  /**
   * @param ownerId input company id
   * @return count of definitions for a given company
   */
  @ServiceMetric(serviceName = WAS_DB)
  long countByOwnerId(long ownerId);

  /**
   * @param definitionDetailsList : List of DefinitionDetails
   * @param processStatus : Status of Process
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByDefinitionDetailsInAndProcessStatus(
      List<DefinitionDetails> definitionDetailsList, ProcessStatus processStatus);

  /**
   * @param ownerId : Company id
   * @param processStatus : Process status
   * @return : All the processes for Realm id based on the Process status
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByOwnerIdAndProcessStatus(
      Long ownerId, ProcessStatus processStatus);

  /**
   * It returns the process details for active,error status for a given definition by recordId and
   * ownerId
   *
   * @param recordId input record id
   * @param processStatus process status
   * @param definitionDetails input definition details
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<ProcessDetails>
  findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInAndParentIdIsNull(
          String recordId,
          long ownerId,
          List<ProcessStatus> processStatus,
          List<DefinitionDetails> definitionDetails);


  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByProcessStatusAndParentIdIn(ProcessStatus processStatus, List<String> parentIds);


  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByParentIdIn(List<String> parentIds);


  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByProcessStatusAndParentIdAndProcessIdIsNot(
      ProcessStatus processStatus, String parentId, String processId
  );


  /**
   * It returns all the Active process except the current process for a realm Id;
   *
   * @param ownerId
   * @param processStatus
   * @param processId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByOwnerIdAndProcessStatusAndProcessIdNot(
      long ownerId, ProcessStatus processStatus, String processId);

  @ServiceMetric(serviceName = WAS_DB)
  List<ProcessDetails> findByOwnerIdAndDefinitionDetailsIn(Long realmId,List<DefinitionDetails> definitionDetails);

  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByDefinitionDetailsIn(final List<DefinitionDetails> definitionDetails);

  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByProcessIdIn(final List<String> processIds);

  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<ProcessDetails>> findByRecordIdAndOwnerIdAndProcessStatusAndDefinitionDetails(
      String entityId, long ownerId, ProcessStatus status, DefinitionDetails definitionDetails);

  @ServiceMetric(serviceName = WAS_DB)
  Optional<ProcessDetails> findByProcessId(String processId);

  /**
   * This method returns the definition details without the definitiondata. The lazy fetching isnt
   * working, so custom query for it.
   *
   * @param processId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select new DefinitionDetails (t.definitionDetails.definitionId, t.definitionDetails.definitionKey," +
              " t.definitionDetails.parentId,t.definitionDetails.modelType, t.definitionDetails.status," +
              " t.definitionDetails.internalStatus, t.definitionDetails.ownerId,t.definitionDetails.offeringId, " +
              "t.definitionDetails.workflowId, t.definitionDetails.definitionName," +
              " t.definitionDetails.description, t.definitionDetails.recordType, t.definitionDetails.version," +
              " t.definitionDetails.placeholderValue, t.definitionDetails.lookupKeys, t.definitionDetails.entityVersion, t.definitionDetails.templateDetails.id," +
              " t.definitionDetails.templateDetails.definitionType," +
              " t.definitionDetails.templateDetails.deployedDefinitionId, " +
              "t.definitionDetails.templateDetails.templateName) from ProcessDetails t where t.processId = :processId")
  Optional<DefinitionDetails> findDefinitionDetailsUsingProcessId(
      @Param("processId") String processId);

  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query("select p.processId from ProcessDetails p where p.definitionDetails IN :definitionDetails")
  List<String> findByDefinitionDetailsIn(
      @Param("definitionDetails") final List<DefinitionDetails> definitionDetails);

  @ServiceMetric(serviceName = WAS_DB)
  @Query("select pd.ownerId from ProcessDetails pd where pd.processId = :processId")
  Optional<Long> findOwnerId(@Param("processId") String processId);

  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select p from ProcessDetails p where p.recordId= :recordId and p.definitionDetails.templateDetails.templateName = :workflowName")
  Optional<ProcessDetails> findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(
      @Param("recordId") String recordId,
      @Param("workflowName") final String workflowName);

  @Transactional
  @Modifying
  @Query(
      "update ProcessDetails pd set pd.entityVersion=:entityVersion where pd.processId in (:processId)")
  @ServiceMetric(serviceName = WAS_DB)
  int updateEntityVersion(@Param("processId")String processId, @Param("entityVersion")Integer entityVersion);

  @Transactional
  @Modifying
  @Query(
          "update ProcessDetails pd set pd.entityVersion=:entityVersion, pd.processStatus=:processStatus  where pd.processId in (:processId)")
  @ServiceMetric(serviceName = WAS_DB)
  void updateProcessStatusAndEntityVersion(@Param("processId")String processId,  @Param("processStatus")ProcessStatus processStatus,  @Param("entityVersion")Integer entityVersion);


  @ServiceMetric(serviceName = WAS_DB)
  @Query(
          "select new ProcessDetails (t.processId, t.ownerId, t.recordId, t.processStatus, t.internalStatus, t.parentId, t.entityVersion, " +
                  "t.definitionDetails.definitionId, t.definitionDetails.definitionKey," +
                  " t.definitionDetails.parentId,t.definitionDetails.modelType, t.definitionDetails.status," +
                  " t.definitionDetails.internalStatus, t.definitionDetails.ownerId,t.definitionDetails.offeringId, " +
                  "t.definitionDetails.workflowId, t.definitionDetails.definitionName," +
                  " t.definitionDetails.description, t.definitionDetails.recordType, t.definitionDetails.version," +
                  " t.definitionDetails.placeholderValue, t.definitionDetails.lookupKeys, t.definitionDetails.entityVersion, t.definitionDetails.templateDetails.id," +
                  " t.definitionDetails.templateDetails.definitionType," +
                  " t.definitionDetails.templateDetails.deployedDefinitionId, " +
                  "t.definitionDetails.templateDetails.templateName) from ProcessDetails t where t.processId = :processId")
  Optional<ProcessDetails> findByIdWithoutDefinitionData(
          @Param("processId") String processId);
  
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query("select p.processId from ProcessDetails p where p.recordId= :recordId")
  List<String> findByRecordId(@Param("recordId") String recordId);
  
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select p.processId from ProcessDetails p where p.recordId= :recordId and p.definitionDetails.templateDetails.templateName in (:workflowNames)")
  List<String> findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(
      @Param("recordId") String recordId,
      @Param("workflowNames") final List<String> workflowNames);
  
  
  @ServiceMetric(serviceName = WAS_DB)
  @Query("select p from ProcessDetails p where p.recordId= :recordId")
  List<ProcessDetails> findProcessDetailsByRecordId(@Param("recordId") String recordId);

}