package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;

import java.util.List;
import java.util.Optional;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import io.github.resilience4j.retry.annotation.Retry;

@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface  TemplateDetailsRepository extends JpaRepository<TemplateDetails, String> {

  /**
   * @param parentId
   * @param version
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TemplateDetails>> getTemplateDetailsByParentIdAndVersion(
      String parentId, Integer version);

  /**
   * Get TemplateDetails which matches templateName
   *
   * @param templateName query parameter
   * @return {@link TemplateDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByTemplateNameOrderByVersionDesc(String templateName);

  /**
   * Retrieves the ID of the most recent version of a template with the specified name.
   *
   * @param templateName the name of the template for which to find the most recent version's ID
   * @return an {@link Optional} containing the ID of the most recent version of the template,
   *         or an empty {@link Optional} if no matching template is found
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "SELECT td.template_id FROM de_template_details td WHERE td.template_name = :templateName and td.status = :status and td.model_type = :modelType ORDER BY td.version DESC LIMIT 1", nativeQuery = true)
  Optional<String> findTemplateIdForTopByTemplateNameOrderByVersionDesc(@Param("templateName") String templateName, @Param("status") String status,
                                                                        @Param("modelType") String modelType);

  /**
   * Get TemplateDetails which matches templateName and version
   *
   * @param templateName query parameter
   * @return {@link TemplateDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findByTemplateNameAndVersion(String templateName, int version);

  /**
   * Get TemplateDetails which matches templateName and version
   *
   * @param templateName query parameter
   * @return {@link TemplateDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByTemplateNameAndStatusOrderByCreatedDateDesc(String templateName, Status status);

  /**
   * Get TemplateDetails which matches template id
   *
   * @param id query parameter
   * @return {@link TemplateDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByIdOrderByVersionDesc(String id);

  /**
   * @param templateId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TemplateDetails>> findByParentId(String templateId);

  /**
   * @param templateId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(value = "select td from TemplateDetails td where td.id =:templateId")
  TemplateDetails findByTemplateId(@Param("templateId") String templateId);
  /*
   * Update template detail status for bpmn and referenced dmn.
   * Parent_id reference is present only to the referenced DMNs
   *
   * @param templateId
   * @param status
   * @return
   */
  @Transactional
  @Modifying
  @Query(
      "update TemplateDetails set status= :status, modifiedByUserId= :modifiedByUser where id= :templateId OR parentId= :templateId")
  @ServiceMetric(serviceName = WAS_DB)
  void updateTemplateDetailsStatus(
      @Param("templateId") String templateId,
      @Param("status") Status status,
      @Param("modifiedByUser") Long modifiedByUser);

  /**
   * @param parentId
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByParentIdOrderByVersionDesc(String parentId);

  /**
   * * This method returns the resulting latest versions of DMNs and BPMNs stored in the Template
   * Details table
   *
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  List<TemplateDetails> getAllRecords();

  /**
   * @param id : Template ID
   * @param status : ENABLED/DISABLED Status
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByIdAndStatusOrderByCreatedDate(
      String id, Status status);

  /**
   * This method returns the list of templates that have an enabled defintion both in the templatedetails
   * table as well as defintion details table and avoiding stale templates via a NULL check on internalStatus
   * @param templateNames
   * @param ownerId
   * @return List of template details matching the description.
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select d.templateDetails from DefinitionDetails d where d.templateDetails.templateName in(:templateNames) "
          + "and d.templateDetails.status ='enabled' and d.status='enabled' and d.ownerId=:ownerId "
          + "and d.internalStatus IS NULL")
  Optional<List<TemplateDetails>> findAllValidEligibleDefinitions(
      @Param("templateNames") List<String> templateNames, @Param("ownerId") Long ownerId);

  /**
   * This method returns the templateDetails without the templateData. The lazy fetching isnt working, so custom query for it.
   * @param templateName
   * @param status
   * @param modelType
   * @return
   */

  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select new TemplateDetails(t.id, t.templateName, t.description, t.allowMultipleDefinitions, t.templateCategory, t.version, t.parentId, t.offeringId, t.displayName, t.ownerId, t.recordType, t.definitionType, t.tag) "
          + "from TemplateDetails t where t.templateName = :templateName and t.status = :status and t.modelType = :modelType")
  Optional<List<TemplateDetails>> findTemplateDetailsExceptTemplateData(
      @Param("templateName") String templateName, @Param("status") Status status,
      @Param("modelType") ModelType modelType);

  @Cacheable
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findByTemplateAdjacencyValuesMd5Sum(String templateAdjacencyValuesMd5Sum);

  @ServiceMetric(serviceName = WAS_DB)
  Optional<TemplateDetails> findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
      String templateName, Status status, List<Integer> version);

  @ServiceMetric(serviceName =  WAS_DB)
  @Query(
          "select td.id from TemplateDetails td where td.templateCategory = 'CUSTOM' and td.status = 'enabled' and td.modelType = 'bpmn' and td.templateName IN (:templateNames)")
  List<String> findCustomTemplateIds(@Param("templateNames") List<String> templateNames);

  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TemplateDetails>> findTopByTemplateNameInAndModelTypeAndStatusOrderByCreatedDateDesc(
      List<String> templateNames, ModelType modelType, Status status);
}