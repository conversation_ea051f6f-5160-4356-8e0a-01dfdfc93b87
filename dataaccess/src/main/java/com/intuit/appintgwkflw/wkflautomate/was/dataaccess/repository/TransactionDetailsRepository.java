package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface TransactionDetailsRepository extends JpaRepository<TransactionDetails, Long> {

  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TransactionDetails>> findByTxnId(String txnId);


  /**
   * Delete Collection of txnDetails by id's
   *
   * @param ids Collection of transaction id.
   * @return No. of activities deleted by TransactionDetails
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  @Modifying
  @Query("delete from TransactionDetails txnDetails where txnDetails.id IN :ids")
  int deleteByIdIn(@Param("ids") final Set<Long> ids);
}
