package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Retry(name = ResiliencyConstants.WAS_DB)
public interface TriggerDetailsRepository extends JpaRepository<TriggerDetails, String> {

  /**
   * Get TriggerDetails which matches triggerName
   *
   * @param triggerName query parameter
   * @return {@link TriggerDetails}
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<TriggerDetails> findByTriggerName(String triggerName);

  /**
   *
   * @param templateDetails
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TriggerDetails>> findByTemplateDetails(TemplateDetails templateDetails);

  /**
   *
   * @param templateDetails
   * @param recordType
   * @return
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  void deleteByTemplateDetails(TemplateDetails templateDetails);

  @Query(
      "select tds from DefinitionDetails def,TriggerDetails tds where def.definitionId=:definitionId and "
          + "def.templateDetails=tds.templateDetails and tds.recordType=def.recordType")
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<TriggerDetails>> findTriggerDetailsByDefinitionId(
      @Param("definitionId") String definitionId);
}
