package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType.BPMN;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory.CUSTOM;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.FilterUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsCustomRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.v4.interaction.query.QueryHelper;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.javatuples.Pair;
import org.springframework.util.CollectionUtils;


@RequiredArgsConstructor
public class DefinitionDetailsCustomRepositoryImpl implements DefinitionDetailsCustomRepository {

  @PersistenceContext(unitName = DataAccessConstants.OFFERING_AWARE_EM)
  private EntityManager entityManager;

  private final FilterQueryPredicateProcessor filterQueryPredicateProcessor;

  /**
   * * This method returns all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Disabled and Internal Status = MARKED_FOR_DISABLE or null for a
   * company sorted by descending order with applied filters. This Query doesnt fetch Definition
   * data and lookup keys from the DB saving DB compute
   *
   * @param ownerId   : Company ID
   * @param modelType : modelType i.e. BPMN
   * @param query     : graphQl query
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllDefinitionsWithoutWorkflowSteps(Long ownerId,
                                                                                  ModelType modelType, QueryHelper query) {
    Pair<CriteriaQuery<DefinitionDetails>, Root<DefinitionDetails>> criteriaQueryRootPair =
        getCriteriaForAllDefinitions(ownerId, modelType, query);

    prepareSelectVariablesWithoutPlaceholderValues(criteriaQueryRootPair.getValue1(), criteriaQueryRootPair.getValue0());
    return Optional.ofNullable(
        entityManager.createQuery(criteriaQueryRootPair.getValue0()).getResultList());
  }

  /**
   * * This method returns all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Disabled and Internal Status = MARKED_FOR_DISABLE or null for a
   * company sorted by descending order with applied filters. It DOES NOT Fetch definition data and
   * placeholder values.
   *
   * @param ownerId   : Company ID
   * @param modelType : modelType i.e. BPMN
   * @param query     : graphQl query
   * @return Optional<List<DefinitionDetails>> Definition details without definition data and placeholder values
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllDefinitions(Long ownerId, ModelType modelType,
      QueryHelper query) {
    Pair<CriteriaQuery<DefinitionDetails>, Root<DefinitionDetails>> criteriaQueryRootPair =
        getCriteriaForAllDefinitions(ownerId, modelType, query);
    return Optional.ofNullable(
        entityManager.createQuery(criteriaQueryRootPair.getValue0()).getResultList());
  }

  private Pair<CriteriaQuery<DefinitionDetails>, Root<DefinitionDetails>> getCriteriaForAllDefinitions(
      Long ownerId, ModelType modelType, QueryHelper query) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate statusEnabledAndInternalStateNull =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);

    Predicate statusDisabled =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.STATUS), Status.DISABLED);
    Predicate internalStateDisable =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
            InternalStatus.MARKED_FOR_DISABLE);
    Predicate internalStateNull =
        criteriaBuilder.isNull(detailsRoot.get(RepositoryConstants.INTERNAL_STATUS));
    Predicate internalStatusPredicate = criteriaBuilder.or(internalStateNull, internalStateDisable);
    Predicate statusDisabledAndInternalStateDisable =
        criteriaBuilder.and(statusDisabled, internalStatusPredicate);
    Predicate finalPredicate =
        criteriaBuilder.or(
            statusEnabledAndInternalStateNull, statusDisabledAndInternalStateDisable);
    Predicate ownerIdPred =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate resultPredicate;

    resultPredicate = criteriaBuilder.and(finalPredicate, ownerIdPred, modelTypePred);

    // Handling query filters
    try {
      com.intuit.v4.query.Expression filterQueryExpression = FilterUtil.getQueryFilterExpression(
          query);
      if (Objects.nonNull(filterQueryExpression)) {
        Predicate filterResultPredicate =
            filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder,
                detailsRoot, filterQueryExpression);
        resultPredicate = criteriaBuilder.and(resultPredicate, filterResultPredicate);
      }
    } catch (Exception exception) {
      WorkflowLogger.logError(exception);
    }

    criteriaQuery.where(resultPredicate);
    // Adding sorting by Created Date
    criteriaQuery.orderBy(criteriaBuilder.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));

    return new Pair<>(criteriaQuery, detailsRoot);
  }


  /**
   * * This method returns all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Enabled and Internal Status = STALE_DEFINITION for a company and a
   * particular record type(like invoice, for example) sorted by descending order
   *
   * @param ownerId : Company ID
   * @param modelType : modelType i.e. BPMN,DMN etc
   * @param recordType : RecordType like invoice etc
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdAndRecordTye(
      Long ownerId, ModelType modelType, RecordType recordType) {

    CriteriaBuilder criteriaBuilderQuery = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilderQuery.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate finalPredicate =
            getStaleAndEnabledDefinitionPredicate(criteriaBuilderQuery, detailsRoot);
    Predicate ownerIdPred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate recordTypePred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.RECORD_TYPE), recordType);
    Predicate resultPredicate =
            criteriaBuilderQuery.and(finalPredicate, ownerIdPred, modelTypePred, recordTypePred);

    criteriaQuery.where(resultPredicate);
    // Adding sorting by Created Date
    criteriaQuery.orderBy(
        criteriaBuilderQuery.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  /**
   * * This method returns all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Enabled and Internal Status = STALE_DEFINITION for a company and a
   * particular record type(like invoice, for example) sorted by descending order
   *
   * @param ownerId : Company ID
   * @param modelType : modelType i.e. BPMN,DMN etc
   * @param templateName : Template name
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdAndTemplateName(
      Long ownerId, ModelType modelType, String templateName, final boolean isDefinitionDataRequired) {

    CriteriaQuery<DefinitionDetails> criteriaQuery = getQueryForEnabledDefinitionsForOwnerIdAndTemplateName(ownerId, modelType, templateName, isDefinitionDataRequired);
    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  @Override
  @ServiceMetric(serviceName = WAS_DB)
  public Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdTemplateCategoryAndWorkflows(
          Long ownerId, ModelType modelType, String templateCategory, final boolean isDefinitionDataRequired, Set<String> workflows) {

    CriteriaBuilder criteriaBuilderQuery = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
            criteriaBuilderQuery.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);
    if (!isDefinitionDataRequired) {
      prepareSelectVariables(detailsRoot, criteriaQuery);
    }
    Predicate finalPredicate =
            getStaleAndEnabledDefinitionPredicate(criteriaBuilderQuery, detailsRoot);
    Predicate ownerIdPred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate templateCategoryPred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_CATEGORY), templateCategory);

    Predicate resultPredicate;

    // If workflows filter is empty, it fetches definitions of all templateNames
    if(!CollectionUtils.isEmpty(workflows)) {
      Predicate workflowPredicate = detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_NAME).in(workflows);
      resultPredicate = criteriaBuilderQuery.and(finalPredicate, ownerIdPred, modelTypePred, templateCategoryPred, workflowPredicate);
    }
    else {
      resultPredicate =
      criteriaBuilderQuery.and(finalPredicate, ownerIdPred, modelTypePred, templateCategoryPred);
    }

    criteriaQuery.where(resultPredicate);
    // Adding sorting by Created Date
    criteriaQuery.orderBy(
            criteriaBuilderQuery.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  private CriteriaQuery<DefinitionDetails> getQueryForEnabledDefinitionsForOwnerIdAndTemplateName(
          Long ownerId, ModelType modelType, String templateName, final boolean isDefinitionDataRequired) {
    CriteriaBuilder criteriaBuilderQuery = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
            criteriaBuilderQuery.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);
    if (!isDefinitionDataRequired) {
      prepareSelectVariables(detailsRoot, criteriaQuery);
    }
    Predicate finalPredicate =
            getStaleAndEnabledDefinitionPredicate(criteriaBuilderQuery, detailsRoot);
    Predicate ownerIdPred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate recordTypePred = criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_NAME), templateName);

    Predicate resultPredicate =
            criteriaBuilderQuery.and(finalPredicate, ownerIdPred, modelTypePred, recordTypePred);

    criteriaQuery.where(resultPredicate);
    // Adding sorting by Created Date
    criteriaQuery.orderBy(
            criteriaBuilderQuery.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));
    return criteriaQuery;
  }

  /**
   * * This method returns all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Enabled and Internal Status = STALE_DEFINITION for a company and a
   * particular record type(like invoice, for example) sorted by descending order
   *
   * @param ownerId       : Company ID
   * @param modelType     : modelType i.e. BPMN,DMN etc
   * @param recordType    : RecordType like invoice etc
   * @param templateName: template name
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
      Long ownerId, ModelType modelType, RecordType recordType, String templateName) {
    return findEnabledDefinitionsforOwnerModelRecordAndTemplateName(ownerId, modelType, recordType,
        templateName, true, true);
  }

  /**
   * * This method returns all the BPMN definitions which have Status = Enabled and Internal Status
   * = null for a company and a particular record type(like invoice, for example) sorted by
   * descending order
   *
   * @param ownerId       : Company ID
   * @param modelType     : modelType i.e. BPMN,DMN etc
   * @param recordType    : RecordType like invoice etc
   * @param templateName : template name
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
          Long ownerId, ModelType modelType, RecordType recordType, String templateName, boolean isDefinitionDataRequired) {
    return findEnabledDefinitionsforOwnerModelRecordAndTemplateName(ownerId, modelType, recordType,
        templateName, isDefinitionDataRequired, false);

  }

  /**
   * This method returns the enabled definitions based on the status predicate
   * @param ownerId
   * @param modelType
   * @param recordType
   * @param templateName
   * @return
   */
  private Optional<List<DefinitionDetails>> findEnabledDefinitionsforOwnerModelRecordAndTemplateName(
      Long ownerId, ModelType modelType, RecordType recordType, String templateName, boolean isDefinitionDataRequired, boolean isStaleRequired) {

    CriteriaBuilder criteriaBuilderQuery = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilderQuery.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    if(!isDefinitionDataRequired){
      prepareSelectVariables(detailsRoot, criteriaQuery);
    }

    Predicate resultPredicate = getCriteriaForEnabledDefinitions(
            ownerId, modelType, recordType, templateName, isStaleRequired, criteriaBuilderQuery, detailsRoot
    );

    criteriaQuery.where(resultPredicate);
    // Adding sorting by Created Date
    criteriaQuery.orderBy(
        criteriaBuilderQuery.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());

  }

  /**
   * This method returns definition keys of all the BPMN definitions which have 1. Status = Enabled and Internal
   * Status = null 2. Status = Enabled and Internal Status = STALE_DEFINITION for a company and a
   * particular record type(like invoice, for example) sorted by descending order
   *
   * @param ownerId       : Company ID
   * @param modelType     : modelType i.e. BPMN,DMN etc
   * @param recordType    : RecordType like invoice etc
   * @param templateName: template name
   * @return
   */
  public Optional<Set<String>> findDefinitionKeysOfAllEnabledDefinitionsForOwnerIdModelRecordTypeAndTemplateName(
          Long ownerId, ModelType modelType, RecordType recordType, String templateName) {

    CriteriaBuilder criteriaBuilderQuery = entityManager.getCriteriaBuilder();
    CriteriaQuery<String> criteriaQuery =
            criteriaBuilderQuery.createQuery(String.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    criteriaQuery.select(detailsRoot.get(RepositoryConstants.DEFINITION_KEY));

    Predicate resultPredicate = getCriteriaForEnabledDefinitions(
            ownerId, modelType, recordType, templateName, true, criteriaBuilderQuery, detailsRoot
    );

    criteriaQuery.where(resultPredicate);

    return Optional.ofNullable(new HashSet<>(entityManager.createQuery(criteriaQuery).getResultList()));

  }

  /**
   * This method returns the predicate for enabled definitions based on the passed parameters
   * Enabled definitions might include STALE_DEFINITION or not
   * depending on the isStaleRequired flag
   * @param ownerId
   * @param modelType
   * @param recordType
   * @param templateName
   * @param isStaleRequired
   * @param criteriaBuilderQuery
   * @param detailsRoot
   * @return
   */
  private Predicate getCriteriaForEnabledDefinitions(
          Long ownerId,
          ModelType modelType,
          RecordType recordType,
          String templateName,
          Boolean isStaleRequired,
          CriteriaBuilder criteriaBuilderQuery,
          Root<DefinitionDetails> detailsRoot
          ) {

    Predicate enabledStatusPredicate = isStaleRequired ? getStaleAndEnabledDefinitionPredicate(criteriaBuilderQuery, detailsRoot): getEnabledStatusPredicate(criteriaBuilderQuery, detailsRoot);

    Predicate ownerIdPred =
        criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
        criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate recordTypePred =
        criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.RECORD_TYPE), recordType);
    Predicate templateNamePred =
        criteriaBuilderQuery.equal(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS)
            .get(RepositoryConstants.TEMPLATE_NAME), templateName);
    Predicate resultPredicate =
        criteriaBuilderQuery
            .and(enabledStatusPredicate, ownerIdPred, modelTypePred, recordTypePred, templateNamePred);

    return resultPredicate;

  }

  /**
   * This method checks if there exists an enabled definition for a given Realm id, having modelType
   * as bpmn which has status as enabled and internalStatus is null. Since create definition needs
   * no enabled definitions to be there so we're not returning any disabled definition here.
   *
   * @param ownerId : Company/Realm Id
   * @param modelType : Model Type
   * @param templateDetails : Template Details
   * @return
   */
  @Override
  public Optional<List<DefinitionDetails>> findAllEnabledDefinitions(
      Long ownerId, ModelType modelType, TemplateDetails templateDetails) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate statusEnabledAndInternalStateNull =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);

    Predicate ownerIdPred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate templateDetailsPred =
            criteriaBuilder.equal(
                    detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS), templateDetails);
    Predicate resultPredicate =
            criteriaBuilder.and(statusEnabledAndInternalStateNull, ownerIdPred, modelTypePred, templateDetailsPred);

    criteriaQuery.where(resultPredicate);
    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  /**
   * This method returns the BPMN definition which has Status = Enabled and Internal Status = null
   * for a company and a particular workflow id
   *
   * @param ownerId : Company ID
   * @param workflowId : Workflow ID from AppConnect
   * @return the optional definition details
   */
  @Override
  public DefinitionDetails findEnabledDefinitionForWorkflowId(
      final Long ownerId, final String workflowId) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate enabledAndNullInternalStatus =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);
    Predicate owner = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate workflow =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.WORKFLOW_ID), workflowId);

    Predicate resultPredicate = criteriaBuilder.and(enabledAndNullInternalStatus, owner, workflow);

    criteriaQuery.where(resultPredicate);

    return entityManager.createQuery(criteriaQuery).getSingleResult();
  }

  @Override
  public List<DefinitionDetails> findEnabledAndMarkedDefinitionForWorkflow(
      final Long ownerId, final String workflowId, final boolean isDefinitionDataRequired) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    //  Custom query to not fetch definitionData since lazy loading doesnt work.
    if(!isDefinitionDataRequired)
    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate owner = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate workflow =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.WORKFLOW_ID), workflowId);

    Predicate statusPredicate =
        criteriaBuilder.or(
            getStaleAndEnabledDefinitionPredicate(criteriaBuilder, detailsRoot),
            getMarkedStatusPredicate(criteriaBuilder, detailsRoot));
    Predicate resultPredicate = criteriaBuilder.and(statusPredicate, owner, workflow);
    criteriaQuery.where(resultPredicate);
    return entityManager.createQuery(criteriaQuery).getResultList();
  }

  /**
   * This method returns unique Definition Keys for the Realm Id. This will return List<String> of
   * definitionKeys which are unique in the realm</>
   *
   * @param ownerId : Realm id
   * @return
   */
  @Override
  public List<String> findDistinctByDefinitionKeyAndOwnerIdAndModelType(
      Long ownerId, ModelType modelType) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<String> criteriaQuery = criteriaBuilder.createQuery(String.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate ownerIdPred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);
    Predicate where = criteriaBuilder.and(ownerIdPred, modelTypePred);
    criteriaQuery
        .select(detailsRoot.get(RepositoryConstants.DEFINITION_KEY))
        .where(where)
        .distinct(true);
    return entityManager.createQuery(criteriaQuery).getResultList();
  }

  @Override
  public Optional<List<DefinitionDetails>> findEnabledSystemDefinition(
      ModelType modelType, List<TemplateDetails> templateDetails) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    //  Custom query to not fetch definitionData since lazy loading doesnt work.
    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate statusEnabledAndInternalStateNull =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);

    Predicate statusEnabled =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.STATUS), Status.ENABLED);

    Predicate internalStatusStale =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS), InternalStatus.STALE_DEFINITION);

    Predicate statusEnabledAndInternalStateStale =
        criteriaBuilder.and(statusEnabled, internalStatusStale);

    // status enabled and internal status null or status enabled and internal status Stale
    Predicate finalPredicate =
        criteriaBuilder.or(statusEnabledAndInternalStateNull, statusEnabledAndInternalStateStale);

    Predicate modelTypePred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);

    Predicate templateDetailsPred =
            criteriaBuilder
                    .in(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS))
                    .value(templateDetails);

    Predicate resultPredicate = criteriaBuilder.and(finalPredicate, modelTypePred, templateDetailsPred);

    criteriaQuery.where(resultPredicate);

    criteriaQuery.orderBy(criteriaBuilder.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  @Override
  public Optional<List<DefinitionDetails>> findEnabledSystemDefinitionForOnDemandApproval(
      ModelType modelType, List<TemplateDetails> templateDetails) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate statusEnabledAndInternalStateNull =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);

    Predicate statusEnabled =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.STATUS), Status.ENABLED);

    Predicate internalStatusStale =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS), InternalStatus.STALE_DEFINITION);

    Predicate statusEnabledAndInternalStateStale =
        criteriaBuilder.and(statusEnabled, internalStatusStale);

    // status enabled and internal status null or status enabled and internal status stale
    Predicate finalPredicate =
        criteriaBuilder.or(statusEnabledAndInternalStateNull, statusEnabledAndInternalStateStale);

    Predicate modelTypePred =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), modelType);

    Predicate templateDetailsPred =
        criteriaBuilder
            .in(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS))
            .value(templateDetails);

    // Record type is null
    Predicate nullRecordType = criteriaBuilder.isNull(detailsRoot.get(RepositoryConstants.RECORD_TYPE));


    /**
     * owner id as system id
     * Fails with HibernateException: Could not parse literal [9223372036854775808L] as java.lang.Long
     * Predicate systemOwnerId = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID),
       Long.parseLong(WorkflowConstants.SYSTEM_OWNER_ID));

     * Hibernate seems to be parsing the negative sign and the value separately causing it to fail since
     * Positive 9223372036854775808L is out of bound for LONG data type
     * Similar to : https://github.com/orientechnologies/orientdb/issues/8365
     */

    Predicate resultPredicate = criteriaBuilder.and(finalPredicate, modelTypePred, templateDetailsPred, nullRecordType);

    criteriaQuery.where(resultPredicate);

    criteriaQuery.orderBy(criteriaBuilder.desc(detailsRoot.get(RepositoryConstants.CREATED_DATE)));
    List<DefinitionDetails> definitionDetailsList = entityManager.createQuery(criteriaQuery).getResultList();
    if(!Objects.isNull(definitionDetailsList)){
     definitionDetailsList = definitionDetailsList.stream().filter(d-> WorkflowConstants.SYSTEM_OWNER_ID.equalsIgnoreCase(
         String.valueOf(d.getOwnerId()))).collect(Collectors.toList());
    }

    return Optional.ofNullable(definitionDetailsList);
  }


  @Override
  public Optional<List<DefinitionDetails>> findDefinitionsForCustomWorkflow(
      String ownerId, String definitionName) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate ownerIdPred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate modelTypePred =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE), BPMN);

    Expression<String> trimmedDefinitionName =
        criteriaBuilder.upper(detailsRoot.get(RepositoryConstants.DEFINITION_NAME));

    Predicate definitionNamePred =
            criteriaBuilder.equal(
                    criteriaBuilder.trim(trimmedDefinitionName), definitionName.toUpperCase());
    Predicate templateCategoryPred =
            criteriaBuilder.equal(
                    detailsRoot
                            .get(RepositoryConstants.TEMPLATE_DETAILS)
                            .get(RepositoryConstants.TEMPLATE_CATEGORY),
                    CUSTOM.name());

    Predicate internalStatusDisable =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
            InternalStatus.MARKED_FOR_DISABLE);
    Predicate internalStateNull =
        criteriaBuilder.isNull(detailsRoot.get(RepositoryConstants.INTERNAL_STATUS));

    Predicate in = criteriaBuilder.or(internalStatusDisable, internalStateNull);
    Predicate where = criteriaBuilder.and(ownerIdPred, modelTypePred, definitionNamePred, templateCategoryPred);
    Predicate finalPredicate = criteriaBuilder.and(where, in);

    criteriaQuery.where(finalPredicate);
    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  @Override
  public Optional<List<DefinitionDetails>> findAllByDefinitionsInParentId(List<String> ids, List<String> parentId) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
            criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate parentIdList =
            criteriaBuilder
                    .in(detailsRoot.get(RepositoryConstants.PARENT_ID))
                    .value(ids);

    Predicate definitionIdList =
            criteriaBuilder
                    .in(detailsRoot.get(RepositoryConstants.DEFINITION_ID))
                    .value(ids);

    Predicate finalPredicate = criteriaBuilder.or(parentIdList, definitionIdList);

    criteriaQuery.where(finalPredicate);

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  @Override
  public Optional<List<DefinitionDetails>> findEnabledDefinitionForOwnerIdTemplateIdRecordTypeAndModelType(
      long ownerId, String templateId, RecordType recordType, ModelType modelType) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
        criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    //  Custom query to not fetch definitionData since lazy loading doesnt work.
    prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate owner = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate templateIdPredicate =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_ID), templateId);
    Predicate recordTypePredicate = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.RECORD_TYPE),
        recordType);
    Predicate modelTypePredicate = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.MODEL_TYPE),
        modelType);
    Predicate statusEnabledAndInternalStateNull =
        getEnabledStatusPredicate(criteriaBuilder, detailsRoot);

    Predicate resultPredicate = criteriaBuilder.and(statusEnabledAndInternalStateNull, owner, templateIdPredicate, recordTypePredicate, modelTypePredicate);
    criteriaQuery.where(resultPredicate);
    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

  /**
   * Custom Query to fetch without definition data and lookup keys. It further optimizes the query.
   * @param detailsRoot
   * @param criteriaQuery
   */
  private void prepareSelectVariablesWithoutPlaceholderValues(
      Root<DefinitionDetails> detailsRoot, CriteriaQuery<DefinitionDetails> criteriaQuery) {
    criteriaQuery.multiselect(
        detailsRoot.get(RepositoryConstants.DEFINITION_ID),
        detailsRoot.get(RepositoryConstants.DEFINITION_KEY),
        detailsRoot.get(RepositoryConstants.PARENT_ID),
        detailsRoot.get(RepositoryConstants.MODEL_TYPE),
        detailsRoot.get(RepositoryConstants.STATUS),
        detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
        detailsRoot.get(RepositoryConstants.OWNER_ID),
        detailsRoot.get(RepositoryConstants.OFFERING_ID),
        detailsRoot.get(RepositoryConstants.WORKFLOW_ID),
        detailsRoot.get(RepositoryConstants.DEFINITION_NAME),
        detailsRoot.get(RepositoryConstants.DESCRIPTION),
        detailsRoot.get(RepositoryConstants.RECORD_TYPE),
        detailsRoot.get(RepositoryConstants.VERSION),
        detailsRoot.get(RepositoryConstants.LOOKUP_KEYS),
            detailsRoot.get(RepositoryConstants.ENTITY_VERSION),
            detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_ID),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.DEFINITION_TYPE),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.DEPLOYED_DEFINITION_ID),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_NAME),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_CATEGORY),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.VERSION),
        detailsRoot.get(RepositoryConstants.CREATED_DATE),
        detailsRoot.get(RepositoryConstants.MODIFIED_DATE),
        detailsRoot.get(RepositoryConstants.CREATED_BY_USER_ID),
        detailsRoot.get(RepositoryConstants.ORIGINAL_SETUP_DATE),
        detailsRoot.get(RepositoryConstants.ORIGINAL_SETUP_USER_ID),
        detailsRoot.get(RepositoryConstants.MODIFIED_BY_USER_ID));
  }

  private void prepareSelectVariables(
      Root<DefinitionDetails> detailsRoot, CriteriaQuery<DefinitionDetails> criteriaQuery) {
    //  Custom query to not fetch definitionData since lazy loading doesnt work.
    criteriaQuery.multiselect(
        detailsRoot.get(RepositoryConstants.DEFINITION_ID),
        detailsRoot.get(RepositoryConstants.DEFINITION_KEY),
        detailsRoot.get(RepositoryConstants.PARENT_ID),
        detailsRoot.get(RepositoryConstants.MODEL_TYPE),
        detailsRoot.get(RepositoryConstants.STATUS),
        detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
        detailsRoot.get(RepositoryConstants.OWNER_ID),
        detailsRoot.get(RepositoryConstants.OFFERING_ID),
        detailsRoot.get(RepositoryConstants.WORKFLOW_ID),
        detailsRoot.get(RepositoryConstants.DEFINITION_NAME),
        detailsRoot.get(RepositoryConstants.DESCRIPTION),
        detailsRoot.get(RepositoryConstants.RECORD_TYPE),
        detailsRoot.get(RepositoryConstants.VERSION),
        detailsRoot.get(RepositoryConstants.PLACEHOLDER_VALUE),
        detailsRoot.get(RepositoryConstants.LOOKUP_KEYS),
             detailsRoot.get(RepositoryConstants.ENTITY_VERSION),
            detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_ID),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.DEFINITION_TYPE),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.DEPLOYED_DEFINITION_ID),
        detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS).get(RepositoryConstants.TEMPLATE_NAME));
  }

  /**
   * return predicate where status is enabled and internal status is null
   *
   * @param criteriaBuilder criteria builder details
   * @param detailsRoot definition details
   * @return {@link Predicate}
   */
  private Predicate getEnabledStatusPredicate(
      CriteriaBuilder criteriaBuilder, Root<DefinitionDetails> detailsRoot) {
    Predicate statusEnabled =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.STATUS), Status.ENABLED);
    Predicate internalStateNull =
        criteriaBuilder.isNull(detailsRoot.get(RepositoryConstants.INTERNAL_STATUS));
    return criteriaBuilder.and(statusEnabled, internalStateNull);

  }

  /**
   * @param criteriaBuilder criteria builder details
   * @param detailsRoot definition details
   * @return {@link Predicate}
   */
  private Predicate getMarkedStatusPredicate(
      CriteriaBuilder criteriaBuilder, Root<DefinitionDetails> detailsRoot) {
    Predicate markedForDelete =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS), InternalStatus.MARKED_FOR_DELETE);
    Predicate markedForDisabled =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
            InternalStatus.MARKED_FOR_DISABLE);

    return criteriaBuilder.or(markedForDelete, markedForDisabled);
  }

  /**
   * Create the condition for stale and enabled definitions
   *
   * @param criteriaBuilder
   * @param detailsRoot
   * @return
   */
  private Predicate getStaleAndEnabledDefinitionPredicate(
      CriteriaBuilder criteriaBuilder, Root<DefinitionDetails> detailsRoot) {
    Predicate enabledStatus = getEnabledStatusPredicate(criteriaBuilder, detailsRoot);
    Predicate statusEnabled =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.STATUS), Status.ENABLED);
    Predicate internalStatusStale =
        criteriaBuilder.equal(
            detailsRoot.get(RepositoryConstants.INTERNAL_STATUS), InternalStatus.STALE_DEFINITION);
    Predicate staleStatusPredicate = criteriaBuilder.and(statusEnabled, internalStatusStale);
    return criteriaBuilder.or(enabledStatus, staleStatusPredicate);
  }


  /**
   * Find the eligible definitions based on definitionKey param
   * @param definitionKey - definitionKey for the workflow
   * @param ownerId - companyId
   * @param isDefinitionDataRequired - should the definitionData be fetched
   * @return list of definitionDetails
   */
  @Override
  public List<DefinitionDetails> findDefinitionsByDefinitionKey(final String definitionKey,
                                                                final Long ownerId,
                                                                final boolean isDefinitionDataRequired ) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
            criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    //  Custom query to not fetch definitionData since lazy loading doesnt work.
    if(!isDefinitionDataRequired)
      prepareSelectVariables(detailsRoot, criteriaQuery);

    Predicate owner = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate workflow =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.DEFINITION_KEY), definitionKey);

    Predicate statusPredicate = getStaleAndEnabledDefinitionPredicate(criteriaBuilder, detailsRoot);
    Predicate resultPredicate = criteriaBuilder.and(statusPredicate, owner, workflow);
    criteriaQuery.where(resultPredicate);
    return entityManager.createQuery(criteriaQuery).getResultList();
  }

  /**
   * This method returns the BPMN definition which has Status = Enabled and Internal Status = null
   * for a company and a particular workflow id
   *
   * @param ownerId : Company ID
   * @param definitionKey : definitionKey for the workflow
   * @return the optional definition details
   */
  @Override
  public DefinitionDetails findEnabledDefinitionForDefinitionKey(final Long ownerId,
                                                                 final String definitionKey) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery =
            criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    Predicate enabledAndNullInternalStatus =
            getEnabledStatusPredicate(criteriaBuilder, detailsRoot);
    Predicate owner = criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.OWNER_ID), ownerId);
    Predicate workflow =
            criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.DEFINITION_KEY), definitionKey);

    Predicate resultPredicate = criteriaBuilder.and(enabledAndNullInternalStatus, owner, workflow);

    criteriaQuery.where(resultPredicate);

    return entityManager.createQuery(criteriaQuery).getSingleResult();
  }

  // Note:: Create appropriate constructor in DefinitionDetails class to fetch only few selected records
  @Override
  public Optional<List<DefinitionDetails>> findDefinitionsWithCustomFilterAndSelection(
          Map<String, Object> filterParams, List<String> selectParams) {

    WorkflowVerfiy.verify(MapUtils.isEmpty(filterParams), WorkflowError.FILTER_PARAMS_NOT_FOUND);

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DefinitionDetails> criteriaQuery = criteriaBuilder.createQuery(DefinitionDetails.class);
    Root<DefinitionDetails> detailsRoot = criteriaQuery.from(DefinitionDetails.class);

    // Apply filters
    Predicate[] predicates = filterParams.entrySet().stream()
            .map(entry -> {
              if (entry.getValue() == null) {
                return criteriaBuilder.isNull(detailsRoot.get(entry.getKey()));
              }
              return criteriaBuilder.equal(detailsRoot.get(entry.getKey()), entry.getValue());
            })
            .toArray(Predicate[]::new);
    criteriaQuery.where(predicates);

    // Select specific fields
    if (!CollectionUtils.isEmpty(selectParams)) {
      List<javax.persistence.criteria.Selection<?>> selections = selectParams.stream()
              .map(param -> param.startsWith(RepositoryConstants.TEMPLATE_DETAILS_PATH)
                      ? detailsRoot.get(RepositoryConstants.TEMPLATE_DETAILS)
                      .get(param.substring(RepositoryConstants.TEMPLATE_DETAILS_PATH.length()))
                      : detailsRoot.get(param))
              .collect(Collectors.toList());
      criteriaQuery.multiselect(selections);
    }

    return Optional.ofNullable(entityManager.createQuery(criteriaQuery).getResultList());
  }

}
