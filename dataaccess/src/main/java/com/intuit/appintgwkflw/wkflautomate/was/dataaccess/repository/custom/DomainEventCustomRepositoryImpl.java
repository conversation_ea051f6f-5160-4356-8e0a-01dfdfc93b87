package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventCustomRepository;
import org.javatuples.Pair;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class DomainEventCustomRepositoryImpl implements DomainEventCustomRepository {

  public static final String EVENT_ID = "eventId";
  public static final String TOPIC = "topic";
  public static final String PARTITION_KEY = "partitionKey";
  public static final String UTC_TIME = "utcTime";
  public static final String REGION = "region";
  public static final String HEADERS = "headers";
  public static final String PAYLOAD = "payload";
  public static final String VERSION = "version";

  @PersistenceContext(unitName = DataAccessConstants.OFFERING_AWARE_EM)
  private EntityManager entityManager;

  @Override
  public Optional<List<DomainEvent>> findDomainEventByPartitionKey(String partitionKey) {
    Pair<CriteriaQuery<DomainEvent>, Root<DomainEvent>> criteriaQueryRootPair =
        prepareQuery(partitionKey);
    return Optional.ofNullable(
        entityManager.createQuery(criteriaQueryRootPair.getValue0()).getResultList());
  }

  @Override
  public Optional<List<?>> findByPartitionKey(String partitionKey) {
    Pair<CriteriaQuery<DomainEvent>, Root<DomainEvent>> criteriaQueryRootPair =
        prepareQuery(partitionKey);
    return Optional.ofNullable(
        entityManager.createQuery(criteriaQueryRootPair.getValue0()).getResultList());
  }

  @Override
  public DomainEvent findByEventId(String eventId) {
    Pair<CriteriaQuery<DomainEvent>, Root<DomainEvent>> criteriaQueryRootPair =
        prepareQueryByEventId(eventId);
    return Optional.ofNullable(
            entityManager.createQuery(criteriaQueryRootPair.getValue0()).getSingleResult())
        .orElse(null);
  }

  private Pair<CriteriaQuery<DomainEvent>, Root<DomainEvent>> prepareQueryByEventId(
      String eventId) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DomainEvent> criteriaQuery = criteriaBuilder.createQuery(DomainEvent.class);
    Root<DomainEvent> detailsRoot = criteriaQuery.from(DomainEvent.class);
    prepareSelectVariables(detailsRoot, criteriaQuery);
    Predicate partitionKeyPredicate =
        criteriaBuilder.equal(detailsRoot.get(EVENT_ID), UUID.fromString(eventId));
    criteriaQuery.where(partitionKeyPredicate);
    return new Pair<>(criteriaQuery, detailsRoot);
  }

  /**
   * Returns the records sorted by utc_time in descending order [latest to oldest]
   * @param partitionKey
   * @return
   */
  public Pair<CriteriaQuery<DomainEvent>, Root<DomainEvent>> prepareQuery(String partitionKey) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<DomainEvent> criteriaQuery = criteriaBuilder.createQuery(DomainEvent.class);
    Root<DomainEvent> detailsRoot = criteriaQuery.from(DomainEvent.class);
    prepareSelectVariables(detailsRoot, criteriaQuery);
    Predicate partitionKeyPredicate =
        criteriaBuilder.equal(detailsRoot.get(PARTITION_KEY), partitionKey);
    criteriaQuery.where(partitionKeyPredicate);
    criteriaQuery.orderBy(criteriaBuilder.desc(detailsRoot.get(UTC_TIME)));
    return new Pair<>(criteriaQuery, detailsRoot);
  }

  /**
   * Returning data without payload
   *
   * @param detailsRoot
   * @param criteriaQuery
   */
  private void prepareSelectVariables(
      Root<DomainEvent> detailsRoot, CriteriaQuery<DomainEvent> criteriaQuery) {
    criteriaQuery.multiselect(
        detailsRoot.get(EVENT_ID),
        detailsRoot.get(TOPIC),
        detailsRoot.get(PARTITION_KEY),
        detailsRoot.get(REGION),
        detailsRoot.get(HEADERS),
        detailsRoot.get(PAYLOAD),
        detailsRoot.get(VERSION));
  }
}
