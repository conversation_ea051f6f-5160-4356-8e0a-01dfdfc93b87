package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.AND_MODIFIER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.OR_MODIFIER;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.FilterType;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.helper.DefinitionDetailsFilter;
import com.intuit.v4.query.CompoundExpression;
import com.intuit.v4.query.Expression;
import com.intuit.v4.query.FilterExpression;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * This class generates appropriate predicate for the given v4 filter query expression
 * The predicate generation is supporetd for simple and compound filter expressions
 */
@Component
@AllArgsConstructor
public class FilterQueryPredicateProcessor {

  private DefinitionDetailsFilter definitionDetailsFilter;


  /**
   * * Converts the query filter expression (can be either simple or compound) supplied in the graphql
   * query to a predicate in the context of the current repository to query on DefinitionDetails.
   * Grammar for Queries :- [Compound Filter Expression = CE, Simple Filter Expression = SE]
   * 1. CE → SE operand CE ;
   *    where operand ∈ {&&/AND, ||/OR}
   * 2. SE → Literal operand ValueToCheck ;
   *    where operand ∈ {=, IN}, and
   *          Literal ∈ {template.name, template.category, recordType}
   * @param criteriaBuilder : Criteria builder from entity manager of current repository context
   * @param detailsRoot : Created from the criteria query on DefinitionDetails by criteria builder
   * @param expression : Expression in filter in graphql query (can be both compound or simple). Compound expressions
   *                   can be combination of simple or compound expressions, and are separated by && (AND) or || (OR).
   *                   Simple filter expressions are literals seperated by an operand.
   * @return : Predicate formed by the expression in filter after validating it, returns null in
   *     other cases
   */
  public Predicate getPredicateFromQueryExpression(CriteriaBuilder criteriaBuilder,
                                                   Root<DefinitionDetails> detailsRoot,
                                                   Expression expression){
    if (expression instanceof FilterExpression) {
      FilterExpression simpleFilterExpression = (FilterExpression) expression;
      WorkflowVerfiy.verify(!definitionDetailsFilter.validateFilter(simpleFilterExpression),
          WorkflowError.UNSUPPORTED_FILTER_EXPRESSION);
      return definitionDetailsFilter.getFilter(simpleFilterExpression, detailsRoot);
    }

    return getPredicateForCompoundFilterExpression(criteriaBuilder, detailsRoot, (CompoundExpression) expression);
  }


  /**
   * * Converts the compound query filter expression supplied in the graphql query to a predicate in the context of the
   * current repository to query on DefinitionDetails.
   *
   * In case of lookupKeys, the expected expression should be in the format: lookupKeys.key=x and lookupKeys.value=y
   * lookup key should always be immediately followed by value in the expression, else incorrect evaluation will happen
   *
   * @param criteriaBuilder : Criteria builder from entity manager of current repository context
   * @param detailsRoot : Created from the criteria query on DefinitionDetails by criteria builder
   * @param compoundExpression : Compound Expression in filter in graphql query. Compound expressions can be a
   *                             combination of simple or compound expressions, & are separated by && (AND) or || (OR)
   * @return : Predicate formed by the expression in filter after validating it, returns null in
   *     other cases
   */
  private Predicate getPredicateForCompoundFilterExpression(CriteriaBuilder criteriaBuilder,
                                                            Root<DefinitionDetails> detailsRoot,
                                                            CompoundExpression compoundExpression) {

    List<Predicate> predicateList = new ArrayList<>();
    ListIterator<Expression> expressionListIterator = compoundExpression.getArgs().listIterator();

    while(expressionListIterator.hasNext()){
      Predicate expressionPredicate;
      Expression expressionInCompoundExpression = expressionListIterator.next();

      //If filter type is not lookupKey
      if(!compareFilterType(expressionInCompoundExpression, FilterType.LOOKUP_KEY)){
        expressionPredicate = getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, expressionInCompoundExpression);
      }
      else {
        expressionPredicate = getPredicateForLookupFilterExpression(criteriaBuilder, detailsRoot, compoundExpression, expressionInCompoundExpression, expressionListIterator);
      }
      Optional.ofNullable(expressionPredicate).ifPresent(predicate -> predicateList.add(predicate));
    }

    WorkflowVerfiy.verify(predicateList.isEmpty(), WorkflowError.UNSUPPORTED_FILTER_EXPRESSION);

    if(compoundExpression.getOp().equals(AND_MODIFIER)) {
      return criteriaBuilder.and(predicateList.toArray(new Predicate[0]));
    }
    else if(compoundExpression.getOp().equals(OR_MODIFIER)) {
      return criteriaBuilder.or(predicateList.toArray(new Predicate[0]));
    }

    throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_FILTER_EXPRESSION);
  }

  /**
   * If filterType is lookupKey, get the next expression with lookupValue and create the predicate
   * Query:  "lookupKeys.key='xyz' and lookupKeys.value='2345'"
   */
  private Predicate getPredicateForLookupFilterExpression(CriteriaBuilder criteriaBuilder,
                                                          Root<DefinitionDetails> detailsRoot,
                                                          CompoundExpression compoundExpression,
                                                          Expression expressionInCompoundExpression,
                                                          ListIterator<Expression> expressionListIterator){
    //get and verify lookupKeyExpression
    FilterExpression lookupKeyExpression = getLookupKeyFilterExpression(expressionInCompoundExpression);

    WorkflowVerfiy.verify(!expressionListIterator.hasNext(), WorkflowError.UNSUPPORTED_LOOKUP_FILTER_QUERY);

    FilterExpression lookupValueExpression = getLookupValueFilterExpression(expressionListIterator.next());

    //Verify the operation between lookupKey and lookupValue is always AND
    verifyOperationInLookupExpression(compoundExpression);

    return definitionDetailsFilter.getPredicateForLookupExpression(criteriaBuilder, detailsRoot, lookupKeyExpression, lookupValueExpression);
  }

  /**
   *  Get lookup key expression and verify non null
   */
  private FilterExpression getLookupKeyFilterExpression(Expression expression){
    FilterExpression lookupKeyExpression = Optional.ofNullable(getFilterExpression(expression)).orElse(null);
    WorkflowVerfiy.verify(Objects.isNull(lookupKeyExpression), WorkflowError.UNSUPPORTED_LOOKUP_FILTER_QUERY);
    return lookupKeyExpression;
  }

  /**
   * Get lookupValue expression and verify its filterType
   */
  private FilterExpression getLookupValueFilterExpression(Expression expression){
    FilterExpression lookupValueExpression = Optional.ofNullable(getFilterExpression(expression)).orElse(null);
    //Verify lookupKeys is followed by lookup value
    WorkflowVerfiy.verify(Objects.isNull(lookupValueExpression), WorkflowError.UNSUPPORTED_LOOKUP_FILTER_QUERY);
    WorkflowVerfiy.verify(!compareFilterType(expression, FilterType.LOOKUP_VALUE), WorkflowError.INCORRECT_LOOKUP_FILTER_QUERY);
    return lookupValueExpression;
  }

  /**
   * Only AND operation is allowed between lookupKey and lookupValue
   */
  private void verifyOperationInLookupExpression(Expression compoundExpression){
    if(!compoundExpression.getOp().equals(AND_MODIFIER)){
      WorkflowLogger.logError("step=lookupKeysError Invalid operation between lookup key and value");
      throw new WorkflowGeneralException(WorkflowError.INCORRECT_LOOKUP_FILTER_QUERY);
    }
  }

  private boolean compareFilterType(Expression expression, FilterType filterType) {
    FilterExpression filterExpression = Optional.ofNullable(getFilterExpression(expression)).orElse(null);
    return (Objects.nonNull(filterExpression) &&
            filterExpression.getProperty().equalsIgnoreCase(filterType.getFilterType()));
  }

  private FilterExpression getFilterExpression(Expression expression){
    if(expression instanceof FilterExpression) {
      return (FilterExpression) expression;
    }
    return null;
  }

}
