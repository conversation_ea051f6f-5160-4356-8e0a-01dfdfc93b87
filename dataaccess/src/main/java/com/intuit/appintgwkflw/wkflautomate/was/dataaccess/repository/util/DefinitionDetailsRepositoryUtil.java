package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.util;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import lombok.experimental.UtilityClass;

import java.util.Map;

/**
 * Utility class for handling operations related to the Definition Details Repository.
 * <p>
 * This class provides utility methods to manipulate and manage filter parameters
 * for querying definition details in the repository.
 * </p>
 *
 */
@UtilityClass
public class DefinitionDetailsRepositoryUtil {

    /**
     * Sets the filter parameters to enable status predicates for querying.
     * <p>
     * This method updates the provided map of filter parameters to include the
     * status as {@code ENABLED} and sets the internal status to {@code null}.
     * This is typically used to filter records that are currently enabled.
     * </p>
     *
     * @param filterParams a map of filter parameters to be used in repository queries.
     *                     The map will be updated to include the status and internal status.
     */
    public void setEnabledStatusPredicate(Map<String, Object> filterParams) {
        filterParams.put(RepositoryConstants.STATUS, Status.ENABLED);
        filterParams.put(RepositoryConstants.INTERNAL_STATUS, null);
    }
}
