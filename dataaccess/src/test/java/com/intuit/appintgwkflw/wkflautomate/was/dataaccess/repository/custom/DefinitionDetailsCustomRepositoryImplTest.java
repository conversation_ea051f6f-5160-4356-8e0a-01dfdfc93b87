package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.util.DefinitionDetailsRepositoryUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.intuit.v4.Query;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.FilterExpression;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionDetailsCustomRepositoryImplTest {

  private static final Long OWNER_ID = 50000000L;

  private static final String WORKFLOW_ID = UUID.randomUUID().toString();

  @Mock
  private EntityManager entityManager;

  private CriteriaBuilder criteriaBuilder;

  @Mock
  private FilterQueryPredicateProcessor filterQueryPredicateProcessor;

  @InjectMocks
  private DefinitionDetailsCustomRepositoryImpl definitionDetailsCustomRepository;

  @Before
  public void setUp() {

    prepareMocks();
  }

  @Test
  public void whenDefinition_isPresent_thenReturnOneItem() {

    TypedQuery<?> query = mock(TypedQuery.class);
    doReturn(mock(DefinitionDetails.class)).when(query).getSingleResult();
    doReturn(query).when(entityManager).createQuery(any(CriteriaQuery.class));

    DefinitionDetails definitionDetails = definitionDetailsCustomRepository
        .findEnabledDefinitionForWorkflowId(OWNER_ID, WORKFLOW_ID);

    assertNotNull(definitionDetails);
  }

  @Test
  public void whenDefinition_isNotPresent_thenReturnEmpty() {

    TypedQuery<?> query = mock(TypedQuery.class);
    doReturn(query).when(entityManager).createQuery(any(CriteriaQuery.class));

    DefinitionDetails definitionDetails = definitionDetailsCustomRepository
        .findEnabledDefinitionForWorkflowId(OWNER_ID, WORKFLOW_ID);
    assertNull(definitionDetails);
  }

  @Test
  public void testFindEnabledAndMarkedDefinitions() {
    TypedQuery<?> query = mock(TypedQuery.class);
    doReturn(Collections.singletonList(mock(DefinitionDetails.class))).when(query).getResultList();
    doReturn(query).when(entityManager).createQuery(any(CriteriaQuery.class));

    List<DefinitionDetails> definitionDetails = definitionDetailsCustomRepository
        .findEnabledAndMarkedDefinitionForWorkflow(OWNER_ID, WORKFLOW_ID,false);
    assertNotNull(definitionDetails);
    Mockito.verify(criteriaBuilder, times(2)).equal(any(), any(Status.class));
    Mockito.verify(criteriaBuilder, times(3)).equal(any(), any(InternalStatus.class));
    Mockito.verify(criteriaBuilder, times(1)).equal(any(), eq(WORKFLOW_ID));
    Mockito.verify(criteriaBuilder, times(1)).equal(any(), eq(OWNER_ID));
  }


  @Test
  public void testFindTemplateIdForEnabledDefinitionForWorkflowId() {
    Long ownerId = 1L;
    String workflowId = "workflow123";
    String expectedTemplateId = "template123";

    Map<String, Object> filterParams = new HashMap<>();
    List<String> selectParams = new ArrayList<>();
    filterParams.put(RepositoryConstants.OWNER_ID, ownerId);
    filterParams.put(RepositoryConstants.WORKFLOW_ID, workflowId);
    DefinitionDetailsRepositoryUtil.setEnabledStatusPredicate(filterParams);
    selectParams.add(RepositoryConstants.TEMPLATE_DETAILS_TEMPLATE_ID);

    TypedQuery<DefinitionDetails> query = Mockito.mock(TypedQuery.class);

    // Mock the behavior of the EntityManager and Criteria API
    Mockito.when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
    Mockito.when(entityManager.createQuery(ArgumentMatchers.any(CriteriaQuery.class))).thenReturn(query);
    Mockito.when(query.getResultList()).thenReturn(Collections.singletonList(new DefinitionDetails(expectedTemplateId)));

    // Call the method under test
    Optional<List<DefinitionDetails>> result = definitionDetailsCustomRepository.findDefinitionsWithCustomFilterAndSelection(filterParams, selectParams);

    // Verify the result
    Assert.assertNotNull(result.get());
    Assert.assertEquals(expectedTemplateId, result.get().get(0).getTemplateDetails().getId());

    // Verify interactions
    Mockito.verify(entityManager).getCriteriaBuilder();
    Mockito.verify(criteriaBuilder).createQuery(DefinitionDetails.class);
    Mockito.verify(entityManager).createQuery(ArgumentMatchers.any(CriteriaQuery.class));
    Mockito.verify(query).getResultList();
  }

  @Test
  public void testFindTemplateIdForEnabledDefinitionForDefinitionKey() {
    Long ownerId = 1L;
    String expectedTemplateId = "template13";

    TypedQuery<DefinitionDetails> query = Mockito.mock(TypedQuery.class);

    String definitionKey = "definitionKey";
    // Mock the behavior of the EntityManager and Criteria API
    Mockito.when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
    Mockito.when(entityManager.createQuery(ArgumentMatchers.any(CriteriaQuery.class))).thenReturn(query);
    Mockito.when(query.getResultList()).thenReturn(Collections.singletonList(new DefinitionDetails(expectedTemplateId)));

    Map<String, Object> filterParams = new HashMap<>();
    List<String> selectParams = new ArrayList<>();
    filterParams.put(RepositoryConstants.OWNER_ID, ownerId);
    filterParams.put(RepositoryConstants.DEFINITION_KEY, definitionKey);
    DefinitionDetailsRepositoryUtil.setEnabledStatusPredicate(filterParams);
    selectParams.add(RepositoryConstants.TEMPLATE_DETAILS_TEMPLATE_ID);
    // Call the method under test
    Optional<List<DefinitionDetails>> result = definitionDetailsCustomRepository.findDefinitionsWithCustomFilterAndSelection(filterParams, selectParams);

    // Verify the result
    Assert.assertNotNull(result.get());
    Assert.assertEquals(expectedTemplateId, result.get().get(0).getTemplateDetails().getId());

    // Verify interactions
    Mockito.verify(entityManager).getCriteriaBuilder();
    Mockito.verify(criteriaBuilder).createQuery(DefinitionDetails.class);
    Mockito.verify(entityManager).createQuery(ArgumentMatchers.any(CriteriaQuery.class));
    Mockito.verify(query).getResultList();
  }

  @Test
  public void testWithoutFilterParamsForEnabledDefinitionForDefinitionKey() {

    List<String> selectParams = new ArrayList<>();
    selectParams.add(RepositoryConstants.TEMPLATE_DETAILS_TEMPLATE_ID);
    Assert.assertThrows(WorkflowGeneralException.class, () -> definitionDetailsCustomRepository
    .findDefinitionsWithCustomFilterAndSelection(null, selectParams));

    // Verify interactions
    Mockito.verify(entityManager, Mockito.times(0)).getCriteriaBuilder();
    Mockito.verify(criteriaBuilder, Mockito.times(0)).createQuery(DefinitionDetails.class);
    Mockito.verify(entityManager, Mockito.times(0)).createQuery(ArgumentMatchers.any(CriteriaQuery.class));
  }

  @Test
  public void testEnabledSystemDefinitionLazy() {
    TypedQuery<?> query = mock(TypedQuery.class);
    DefinitionDetails definitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
        Status.ENABLED, InternalStatus.DELETED, 1L,
        1L, "wkflw", "engagement", "mock",
        RecordType.ENGAGEMENT, 1, "{}","{}", 0, "test-id",
        DefinitionType.SINGLE, "depId", "customNotification");
    doReturn(Collections.singletonList(definitionDetails)).when(query).getResultList();
    doReturn(query).when(entityManager).createQuery(any(CriteriaQuery.class));

    TemplateDetails templateDetail = new TemplateDetails();
    templateDetail.setId("111");
    templateDetail.setModelType(ModelType.BPMN);
    templateDetail.setDefinitionType(DefinitionType.USER);
    templateDetail.setDeployedDefinitionId("defId");

    definitionDetails.setTemplateDetails(templateDetail);

    List<TemplateDetails> templateDetails = new ArrayList<>();
    templateDetails.add(templateDetail);

    Optional<List<DefinitionDetails>> responseDetails = definitionDetailsCustomRepository
        .findEnabledSystemDefinition(ModelType.BPMN, templateDetails);
    assertNotNull(responseDetails.get());
    assertNotNull(responseDetails.get().get(0).getDefinitionId());
    assertNull(responseDetails.get().get(0).getDefinitionData());
    assertNotNull(responseDetails.get().get(0).getTemplateDetails().getId());
    assertNull(responseDetails.get().get(0).getTemplateDetails().getTemplateData());
    assertNotNull(responseDetails.get().get(0).getTemplateDetails().getDeployedDefinitionId());
    assertNotNull(responseDetails.get().get(0).getTemplateDetails().getDefinitionType());
    assertEquals("defId",
        responseDetails.get().get(0).getTemplateDetails().getDeployedDefinitionId());
    assertEquals(DefinitionType.USER, responseDetails.get().get(0).getTemplateDetails().getDefinitionType());
  }

  @Test
  public void testEnabledSystemDefinitionForOnDemandApproval() {
    TypedQuery<?> query = mock(TypedQuery.class);
    DefinitionDetails definitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
        Status.ENABLED, InternalStatus.STALE_DEFINITION, Long.valueOf(WorkflowConstants.SYSTEM_OWNER_ID),
        1L, "wkflw", "custom approval system definition", "mock",
        null, 1, null,null, 0, "test-id",
        DefinitionType.SYSTEM, "defId", "customApproval");
    doReturn(Collections.singletonList(definitionDetails)).when(query).getResultList();
    doReturn(query).when(entityManager).createQuery(any(CriteriaQuery.class));

    TemplateDetails templateDetail = new TemplateDetails();
    templateDetail.setId("111");
    templateDetail.setModelType(ModelType.BPMN);
    templateDetail.setDefinitionType(DefinitionType.SYSTEM);
    templateDetail.setDeployedDefinitionId("defId");

    definitionDetails.setTemplateDetails(templateDetail);

    List<TemplateDetails> templateDetails = new ArrayList<>();
    templateDetails.add(templateDetail);

    Optional<List<DefinitionDetails>> result = definitionDetailsCustomRepository
        .findEnabledSystemDefinitionForOnDemandApproval(ModelType.BPMN, templateDetails);
    Assert.assertEquals(1, result.get().size());
    Assert.assertEquals(definitionDetails, result.get().get(0));
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq(InternalStatus.STALE_DEFINITION));
    verify(criteriaBuilder, Mockito.times(2)).isNull(Mockito.any()); // internal status and record type
    verify(criteriaBuilder, Mockito.times(2)).equal(Mockito.any(), Mockito.eq(Status.ENABLED));

  }


  @Test
  public void testFindAllDefinitionWithFilterQuery() {
    TypedQuery<?> criteriaQuery = mock(TypedQuery.class);
    doReturn(Collections.singletonList(mock(DefinitionDetails.class))).when(criteriaQuery).getResultList();
    doReturn(criteriaQuery).when(entityManager).createQuery(any(CriteriaQuery.class));

    doReturn(mock(Predicate.class)).when(filterQueryPredicateProcessor).getPredicateFromQueryExpression(any(), any(), any());

    Optional<List<DefinitionDetails>> definitionDetails = definitionDetailsCustomRepository
            .findAllDefinitions(OWNER_ID, ModelType.BPMN, prepareQueryHelper("recordType", "invoice"));
    assertNotNull(definitionDetails);
    Mockito.verify(criteriaBuilder, times(1)).and(any(Predicate.class), any(Predicate.class));
    Mockito.verify(criteriaBuilder, times(2)).equal(any(), any(Status.class));
    Mockito.verify(criteriaBuilder, times(1)).equal(any(), any(InternalStatus.class));
  }

  @Test
  public void testFindAllDefinitionWithoutFilterQuery() {
    TypedQuery<?> criteriaQuery = mock(TypedQuery.class);
    DefinitionDetails mockDefinitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
        Status.ENABLED, InternalStatus.DELETED, 1L,
        1L, "wkflw", "engagement", "mock",
        RecordType.ENGAGEMENT, 1, "{}","{}", 0,  "test-id",
        DefinitionType.SINGLE, "depId", "customNotification");
    mockDefinitionDetails.setDefinitionData("test".getBytes(StandardCharsets.UTF_8));
    doReturn(Collections.singletonList(mockDefinitionDetails)).when(criteriaQuery).getResultList();
    doReturn(criteriaQuery).when(entityManager).createQuery(any(CriteriaQuery.class));

    Optional<List<DefinitionDetails>> definitionDetails = definitionDetailsCustomRepository
            .findAllDefinitions(OWNER_ID, ModelType.BPMN, prepareQueryHelper(null, null));
    assertNotNull(definitionDetails);
    assertNotNull(definitionDetails.get().get(0).getDefinitionData());
    assertNotNull(definitionDetails.get().get(0).getPlaceholderValue());
    Mockito.verify(criteriaBuilder, times(0)).and(any(Predicate.class), any(Predicate.class));
    Mockito.verify(criteriaBuilder, times(2)).equal(any(), any(Status.class));
    Mockito.verify(criteriaBuilder, times(1)).equal(any(), any(InternalStatus.class));
  }

  @Test
  public void testFindAllDefinitionWithoutWorkflowStepsWithNoFilterQuery() {
    TypedQuery<?> criteriaQuery = mock(TypedQuery.class);
    DefinitionDetails mockDefinitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
        Status.ENABLED, InternalStatus.DELETED, 1L,
        1L, "wkflw", "engagement", "mock",
        RecordType.ENGAGEMENT, 1,"{}", 0,  "test-id",
        DefinitionType.SINGLE, "{}", "depId", "customNotification", 0,new Date(), new Date(), 1234L, new Date(), 1234L, 123241234L);
    doReturn(Collections.singletonList(mockDefinitionDetails)).when(criteriaQuery).getResultList();
    doReturn(criteriaQuery).when(entityManager).createQuery(any(CriteriaQuery.class));

    Optional<List<DefinitionDetails>> definitionDetails = definitionDetailsCustomRepository
        .findAllDefinitionsWithoutWorkflowSteps(OWNER_ID, ModelType.BPMN, prepareQueryHelper(null, null));

    assertNotNull(definitionDetails);

    assertNull(definitionDetails.get().get(0).getDefinitionData());
    assertNull(definitionDetails.get().get(0).getPlaceholderValue());
    assertEquals(definitionDetails.get().get(0).getCreatedDate(), mockDefinitionDetails.getCreatedDate());
    assertEquals(definitionDetails.get().get(0).getModifiedDate(), mockDefinitionDetails.getModifiedDate());
    assertEquals(definitionDetails.get().get(0).getModifiedByUserId(), mockDefinitionDetails.getModifiedByUserId());
    assertEquals(definitionDetails.get().get(0).getTemplateDetails().getVersion(), 0);

    Mockito.verify(criteriaBuilder, times(0)).and(any(Predicate.class), any(Predicate.class));
    Mockito.verify(criteriaBuilder, times(2)).equal(any(), any(Status.class));
    Mockito.verify(criteriaBuilder, times(1)).equal(any(), any(InternalStatus.class));
  }

  @Test
  public void test_findDefinitionsByDefinitionKey() {

    TypedQuery<?> criteriaQuery = mock(TypedQuery.class);
    DefinitionDetails mockDefinitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
            Status.ENABLED, InternalStatus.DELETED, 1L,
            1L, "wkflw", "engagement", "mock",
            RecordType.ENGAGEMENT, 1,"{}", 0,  "test-id",
            DefinitionType.SINGLE, "{}", "depId", "customNotification", 0,new Date(), new Date(), 1234L, new Date(), 1234L,123241234L);
    doReturn(Collections.singletonList(mockDefinitionDetails)).when(criteriaQuery).getResultList();
    doReturn(criteriaQuery).when(entityManager).createQuery(any(CriteriaQuery.class));
    List<DefinitionDetails> result = definitionDetailsCustomRepository.findDefinitionsByDefinitionKey("test", 12345L, false);
    Assert.assertEquals(1, result.size());
    Assert.assertEquals(mockDefinitionDetails, result.get(0));
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq(12345L));
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq("test"));
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq(InternalStatus.STALE_DEFINITION));
    verify(criteriaBuilder, Mockito.times(1)).isNull(Mockito.any());
    verify(criteriaBuilder, Mockito.times(2)).equal(Mockito.any(), Mockito.eq(Status.ENABLED));

  }

  @Test
  public void test_findEnabledDefinitionForDefinitionKey() {

    TypedQuery<?> criteriaQuery = mock(TypedQuery.class);
    DefinitionDetails mockDefinitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
            Status.ENABLED, InternalStatus.DELETED, 1L,
            1L, "wkflw", "engagement", "mock",
            RecordType.ENGAGEMENT, 1,"{}", 0,  "test-id",
            DefinitionType.SINGLE, "{}", "depId", "customNotification", 0,new Date(), new Date(), 1234L, new Date(), 1234L, 123241234L);
    doReturn(mockDefinitionDetails).when(criteriaQuery).getSingleResult();
    doReturn(criteriaQuery).when(entityManager).createQuery(any(CriteriaQuery.class));
    DefinitionDetails result = definitionDetailsCustomRepository.findEnabledDefinitionForDefinitionKey(12345L, "test");
    Assert.assertEquals(mockDefinitionDetails, result);
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq(12345L));
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq("test"));
    verify(criteriaBuilder, Mockito.times(1)).isNull(Mockito.any());
    verify(criteriaBuilder, Mockito.times(1)).equal(Mockito.any(), Mockito.eq(Status.ENABLED));

  }


  private void prepareMocks() {
    criteriaBuilder = spy(CriteriaBuilder.class);
    CriteriaQuery<?> criteriaQuery = mock(CriteriaQuery.class);
    Root<?> root = mock(Root.class);
    doReturn(root).when(criteriaQuery).from(any(Class.class));
    Path<?> path = mock(Path.class);
    doReturn(path).when(root).get(anyString());
    doReturn(criteriaQuery).when(criteriaBuilder).createQuery(any());
    doReturn(criteriaBuilder).when(entityManager).getCriteriaBuilder();
    Predicate predicate = mock(Predicate.class);
    doReturn(predicate).when(criteriaBuilder).and(any(), any(), any());
    Predicate In = spy(CriteriaBuilder.In.class);
    doReturn(In).when(criteriaBuilder).in(any());
    ReflectionTestUtils.setField(definitionDetailsCustomRepository, "entityManager", entityManager);
  }

  public QueryHelper prepareQueryHelper(String filterExpressionKey, String filterExpressionValue) {
    Query query = new Query();
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    query.setPreparedQuery(preparedQuery);

    if( Objects.nonNull(filterExpressionKey) && Objects.nonNull(filterExpressionValue)) {
      FilterExpression filterExpression = new FilterExpression();
      filterExpression.setProperty(filterExpressionKey);
      filterExpression.addArgs(filterExpressionValue);
      preparedQuery.setWhere(filterExpression);
    }

    return new QueryHelper(query);
  }
}
