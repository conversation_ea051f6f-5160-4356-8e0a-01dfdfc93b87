version: "3"
services:
  camunda:
    container_name: camunda_db
    image: 'postgres'
    ports:
    - '5432:5432'
    environment:
      POSTGRES_PASSWORD: Intuit01
      POSTGRES_DB: camunda
      POSTGRES_USER: sas
    volumes:
    - '${WORKDIR}/database:/var/lib/postgresql'
    
  # Creating new DB container for "demo-offering" for multi offering support
  demo-offering:
    container_name: demo-offering_db
    image: 'postgres'
    ports:
    - '5433:5432'
    environment:
      POSTGRES_PASSWORD: Intuit01
      POSTGRES_DB: demo-offering
      POSTGRES_USER: sas
    volumes:
    - '${WORKDIR}/database-demo-offering:/var/lib/postgresql'  

  # Creating docker image for ESS testing locally
  was-sqs:
    image: docker.intuit.com/appintgwkflw/wkflautomate/wkflatmnsvc/service/sqs
    container_name: was_sqs
    ports:
      - "9324:9324"
      - "9325:9325"
    stdin_open: true
    tty: true