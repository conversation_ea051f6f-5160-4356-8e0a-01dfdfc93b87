package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import lombok.Builder;
import lombok.Getter;
import org.springframework.util.MultiValueMap;

/** <AUTHOR> */
@Getter
public class AppConnectExecuteDuzzitRequest extends AppActionBaseRequest {
  private String instanceId;
  private String providerAppId;
  private String providerWorkflowId;
  private String realmId;
  private String externalTaskId;
  private MultiValueMap<String, String> inputs;
  /**
   * @param endpoint
   * @param realmId
   * @param instanceId
   * @param providerAppId
   * @param providerWorkflowId
   * @param inputs
   */
  @Builder
  public AppConnectExecuteDuzzitRequest(
      String endpoint,
      String instanceId,
      String providerAppId,
      String providerWorkflowId,
      String realmId,
      String externalTaskId,
      MultiValueMap<String, String> inputs) {
    super(endpoint);
    this.instanceId = instanceId;
    this.providerAppId = providerAppId;
    this.providerWorkflowId = providerWorkflowId;
    this.realmId = realmId;
    this.inputs = inputs;
    this.externalTaskId = externalTaskId;
  }
}
