package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import lombok.Builder;
import lombok.Getter;

/** <AUTHOR> */
@Getter
public class AppConnectTaskHandlerRequest extends AppActionBaseRequest {
  private String instanceId;
  private String providerAppId;
  private String providerWorkflowId;
  private String workflowId;
  private String realmId;
  private String externalTaskId;
  private WorkflowTaskHandlerAction action;
  /**
   * @param endpoint
   * @param realmId
   * @param instanceId
   * @param providerAppId
   * @param providerWorkflowId
   * @param workflowId
   * @param action
   */
  @Builder
  public AppConnectTaskHandlerRequest(
      String endpoint,
      String instanceId,
      String providerAppId,
      String providerWorkflowId,
      String workflowId,
      String realmId,
      String externalTaskId,
      WorkflowTaskHandlerAction action) {
    super(endpoint);
    this.instanceId = instanceId;
    this.providerAppId = providerAppId;
    this.providerWorkflowId = providerWorkflowId;
    this.workflowId = workflowId;
    this.realmId = realmId;
    this.action = action;
    this.externalTaskId = externalTaskId;
  }
}
