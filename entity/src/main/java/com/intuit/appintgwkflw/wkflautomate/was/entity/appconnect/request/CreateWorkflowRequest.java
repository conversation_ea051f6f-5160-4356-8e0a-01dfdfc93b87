package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.AppConnectWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

@Getter
public class CreateWorkflowRequest extends AppActionBaseRequest {

  private String name;

  private AppConnectWorkflowType workflowType;

  private String externalId;

  private List<Model> models;

  private Status status;

  @Getter
  @AllArgsConstructor
  public static class Status {

    private PollingStatus pollingStatus;
  }

  @Getter
  @AllArgsConstructor
  public static class PollingStatus {
    
    private long pollingFrequency;
  }

  @Data
  public static class Model {

    private String content;
    private ModelType modelType;
  }

  @Builder
  public CreateWorkflowRequest(
      String endpoint,
      String name,
      AppConnectWorkflowType workflowType,
      String externalId,
      Status status,
      @Singular List<Model> models) {
    super(endpoint);
    this.name = name;
    this.workflowType = workflowType;
    this.externalId = externalId;
    this.status = status;
    this.models = models;
  }
}
