package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import lombok.Builder;
import lombok.Getter;

@Getter
public class RegisterTokenRequest extends AppActionBaseRequest {

  String entityType;
  String entityOperations;
  String companyId;
  String appId;

  @Builder
  public RegisterTokenRequest(String endpoint, String entityType,
      String entityOperations, String companyId, String appId) {
    super(endpoint);
    this.entityType = entityType;
    this.entityOperations = entityOperations;
    this.companyId = companyId;
    this.appId = appId;
  }
}
