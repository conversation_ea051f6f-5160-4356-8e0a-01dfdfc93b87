package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AppConnectWorkflowResponse {
  private String id;
  private String guid;
  private String name;
  private String description;
  private boolean track;
  private String workflowType;
  // Null in case of Non-WAS type workflows
  private String externalId;
  private Timestamp created;
  private Timestamp modified;

  private Subscription subscription;
  private Status status;

  @Data
  public static class Subscription {
    private String id;
    private String appId;
    private String appConnectAppId;
    private String companyId;
    private Integer appVersion;
    private String name;
    private String state;
    private Timestamp created;
    private Timestamp modified;
  }

  @Data
  public static class Status {
    private String workflowId;
    private String status;
    private Long actionRunCount;
    private Long eventRunCount;
    private Timestamp lastEventRunDate;
    private Long failedAcknowledgedCount;
    private Long failedCount;
  }
}
