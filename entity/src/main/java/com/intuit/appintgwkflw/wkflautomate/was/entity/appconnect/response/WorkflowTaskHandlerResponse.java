package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/** <AUTHOR> */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowTaskHandlerResponse {

  private WorkflowTaskHandlerHeaders header;

  private String success;

  private String error;

  @JsonProperty("NumberRecipients")
  private String numberRecipients;

  //generic response from appconnect in data field
  private Map<String, Object> data;

  private String downstreamResponseStatus;

  private String downstreamResponseBody;
}
