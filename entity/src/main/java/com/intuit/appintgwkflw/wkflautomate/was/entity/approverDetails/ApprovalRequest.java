package com.intuit.appintgwkflw.wkflautomate.was.entity.approverDetails;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalRequest {
  private String levelId;
  public static final String DEFAULT_APPROVAL_TYPE = "SEQUENTIAL";
  private String approvalType = DEFAULT_APPROVAL_TYPE;
  private List<ApproverDetail> approverDetails;
}
