package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

/**
 *
 * <AUTHOR>
 */
@UtilityClass
public class ApprovalTaskConstants {
  public final String ENTITY_TYPE = "entityType";
  public final String APPROVER_DETAILS = "approverDetails";
  public final String APPROVER_TYPE = "approvalType";
  public final String FIELD_VALUE = "fieldValue";
  public final String PARAMETERS = "parameters";
  public final String TYPE = "approvalType";
  public final String ASSIGNEE = "Assignee";
  public final String APPROVER_ID = "approverId";
  public final String APPROVER_REQUEST_LIST  = "approvalRequestList";
  public final String CREATE_APPROVAL_REQUEST_HANDLER_ID = "createApprovalRequest";
}
