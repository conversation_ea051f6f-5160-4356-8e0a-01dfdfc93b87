package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class AsyncTaskConstants {

  public final String REALM_ID_KEY = "realmIdKey";
  public final String BPMN_ENGINE_DEPLOY_REQUEST_KEY = "bpmnDeployReq";
  public final String BPMN_ENGINE_DEPLOY_RESPONSE_KEY = "bpmnDeployRes";
  public final String SAVE_DEFINITION_RESPONSE_KEY = "saveDefinitionRes";
  public final String SAVE_DEFINITION_ACTIVITY_RESPONSE_KEY = "saveCallActivityRes";
  public final String SAVE_TEMPLATE_RESPONSE_KEY = "saveTemplateRes";
  public final String BPMN_INSTANCE_KEY = "bpmnInstanceKey";
  public final String WORKFLOW_ID_KEY = "workflowIdKey";
  public final String SUBSCRIPTION_ID_KEY = "subscriptionIdKey";
  public final String AUTH_DETAILS_KEY = "authDetailsKey";
  public final String DEFINITION_ID_KEY = "definitionIdKey";
  public final String ACTIVATE_RESPONSE_KEY = "activateResponseKey";
  public final String DEFINITION_KEY = "definitionKey";
  public final String WORKFLOW_NAME_KEY = "workflowNameKey";
  public final String OFFLINE_TICKET_KEY = "offlineTicketKey";
  public final String SAVE_AUTH_DETAILS_RESPONSE_KEY = "saveAuthDetailsResponseKey";
  public final String APP_CONNECT_TASK_FAILURE = "appConnectTaskFailure";
  public final String APP_CONNECT_EXCEPTION = "appConnectException";
  public final String APP_CONNECT_ERROR_MESSAGE = "appConnectError";
  public final String SAVE_DEFINITION_TASK_FAILURE = "saveDefinitionTaskFailure";
  public final String SAVE_TEMPLATE_TASK_FAILURE = "saveTemplateTaskFailure";
  public final String SAVE_DEFINITION_EXCEPTION = "saveDefinitionException";
  public final String SAVE_TEMPLATE_EXCEPTION = "saveTemplateException";
  public final String SAVE_DEFINITION_ERROR_MESSAGE = "saveDefinitionError";
  public final String SAVE_TEMPLATE_ERROR_MESSAGE = "saveTemplateError";
  public final String UPDATE_DEFINITION_TASK_FAILURE = "updateDefinitionTaskFailure";
  public final String UPDATE_DEFINITION_EXCEPTION = "updateDefinitionException";
  public final String UPDATE_DEFINITION_ERROR_MESSAGE = "updateDefinitionError";
  public final String EXPIRY_DATE_KEY = "expiryDateKey";
  public final String ACTIVATE_ACTION_KEY = "activateActionKey";
  public final String CAMUNDA_MESSAGE_NAME = "messageName";
  public final String BPMN_DETAILS_KEY = "bpmnDetailsKey";
  public final String DMN_DETAILS_KEY = "dmnDetailsKey";
  public final String TRIGGER_DETAILS_KEY = "triggerDetailsKey";
  public final String DEPLOYMENT_ID_KEY = "deploymentIdKey";
  public final String IS_SINGLE_DEFINITION = "isSingleDefinition";
  public final String BPMN_START_EVENTS = "bpmnStartEvents";
  public final String TRIGGER_TRANSACTION_ENTITY = "transactionEntity";
  public final String SAVE_SYSTEM_DEF_TASK_FAILURE = "saveSystemDefinitionTaskFailure";
  public final String SAVE_SYSTEM_DEF_EXCEPTION = "saveSystemDefinitionException";
  public final String SAVE_SYSTEM_DEF_ERROR_MESSAGE = "saveSystemDefinitionError";
  public final String IS_OFFLINE_KEY = "isOfflineKey";
  public final String IS_CREATE_SUBSCRIPTION_REQD = "isCreateSubscriptionReqd";
  public final String DEFINITION_DETAILS = "definitionDetailsKey";
  public final String CORRELATE_KEYS = "correlateKeys";
  public final String CORRELATE_ALL_ERROR = "correlateAllError";
  public final String CORRELATE_ALL_EXCEPTION = "correlateAllException";
  public final String WORKFLOW_DEF_EVENT_PUBLISH_EXCEPTION =
      "workflowDefinitionEventPublishException";
  public final String EVALUATE_REQUEST_KEY = "evaluateRequestKey";
  public final String DMN_DATA_KEY = "dmnDataKey";
  public final String EVALUATE_DMN_RESPONSE = "evaluateDMNResponse";
  public final String NO_FORMAT_VARIABLES_KEY = "formatVariablesKey";
  public final String RECURRENCE_START_PROCESS_TASK_FAILURE = "recurrenceStartProcessTaskFailure";
  public final String RECURRENCE_START_PROCESS_EXCEPTION = "recurrenceStartProcessTaskException";
  public final String START_RECURRING_PROCESS_ERROR_MESSAGE = "startRecurringProcessError";
  public final String CUSTOM_WORKFLOW_CONFIG = "customWorkflowConfig";
  public final String CAMUNDA_INPUT_VARIABLE_MAP = "camundaInputVariableMap";
  public final String CAMUNDA_EXTERNAL_TASK = "camundaExternalTask";
  public final String HANDLER_DETAILS = "handlerDetails";
  public final String TASKS_DETAILS_KEY = "tasksDetailsKey";
  public final String SAVE_TASK_DETAILS_FAILURE = "saveTaskDetailsFailure";
  public final String SAVE_TASK_DETAILS_EXCEPTION = "saveTaskDetailsException";
  public final String SAVE_TASK_DETAILS_ERROR_MESSAGE = "saveTaskErrorMessage";
  public final String CRITERIA_BUILDER = "criteriaBuilder";
  public final String DETAILS_ROOT = "detailsRoot";
  public final String COMPOUND_EXPRESSION = "compoundExpression";
  public final String RESULT_PREDICATE = "resultPredicate";
  public final String ENTITY_OPERATION = "entityOperation";
  public final String OFFLINE_V2_MIGRATION = "offlineV2Migration";
  public final String EVENT_SCHEDULE_REQUEST ="eventScheduleRequest";
  public final String EVENT_SCHEDULE_TASK_FAILURE = "eventScheduleTaskFailure";
  public final String EVENT_SCHEDULING_TASK_FAILURE = "eventSchedulingTaskFailure";
  public final String SAVE_SCHEDULE_ERROR_MESSAGE = "saveScheduleErrorMessage";
  public final String UPDATE_SCHEDULE_ERROR_MESSAGE = "updateScheduleErrorMessage";
  public final String EVENT_SCHEDULE_EXCEPTION = "eventScheduleException";
  public final String SAVE_SCHEDULE_IN_DATASTORE_EXCEPTION = "saveEventScheduleInDataStoreException";
  public final String UPDATE_SCHEDULE_IN_DATASTORE_EXCEPTION = "updateEventScheduleInDataStoreException";
  public final String EVENT_SCHEDULE_RESPONSE ="eventScheduleResponse";
  public final String EVENT_SCHEDULE_STATUS ="eventScheduleStatus";
  public final String EVENT_SCHEDULE_IDS ="eventScheduleIds";
  public final String EVENT_SCHEDULER_DETAILS_MAP ="eventSchedulerDetailsMap";
  public final String SAVE_SCHEDULE_DETAILS_TASK_FAILURE = "saveScheduleDetailTaskFailure";
  public final String SAVE_SCHEDULE_DETAILS_ERROR_MESSAGE = "saveScheduleDetailsErrorMessage";
  public final String UPDATE_SCHEDULE_DETAILS_TASK_FAILURE = "updateScheduleDetailTaskFailure";
  public final String DEFINITION_INSTANCE = "definitionInstance";
  public final String IS_UPDATE = "isUpdate";
  public final String AUTHORIZATION_KEY = "authorization";
  public final String DEFINITION_SERVICE_HELPER_OBJECT = "definitionServiceHelperObject";
  public final String IS_MULTI_CONDITION = "isMultiCondition";
  public final String IS_UPDATE_COMPLETE_SCHEDULE = "isUpdateCompleteSchedule";
  public final String SAVE_TEMPLATE_DETAILS_LIST_KEY = "saveTemplateDetailsList";

  public final String PROCESS_INSTANCE_QUERY = "processInstanceQuery";
  public static final String CORRELATE_ASYNC_ENABLED_FF = "qbo-adv-correlate-async-enabled";
  public static final String IS_CORRELATE_ASYNC = "isCorrelateAsync";
  public final String IS_SCHEDULING_FLOW_ENABLED = "isSchedulingEnabled";
  public final String IS_APPCONNECT_UPDATE_DISABLED = "isAppconnectUpdateDisabled";
  public final String SCHEDULING_META_DATA = "schedulingMetaData";
  public static final String UPDATE_EVENT_SCHEDULE_TASK_FAILURE = "updateEventScheduleTaskFailure";
  public static final String IS_ESS_TO_SCHEDULING_MIGRATION = "isEssToSchedulingMigration";
  public static final String IS_MIGRATED_TO_SCHEDULING = "isMigratedToScheduling";
  public static final String TRIGGER_NOW_REQUEST = "triggerNowRequest";
  public static final String TRIGGER_NOW_ASYNC_RESPONSE_KEY = "triggerNowAsyncResponse";
  public static final String TRIGGER_NOW_ASYNC_EXCEPTION_KEY = "triggerNowAsyncException";
  public static final String TRIGGER_NOW_ASYNC_TASK_FAILURE = "triggerNowAsyncTaskFailure";
  public static final String TRIGGER_NOW_ASYNC_ERROR_MESSAGE = "triggerNowAsyncErrorMessage";
  public static final String DEFINITION_DETAILS_LIST = "definitionDetailsList";
}