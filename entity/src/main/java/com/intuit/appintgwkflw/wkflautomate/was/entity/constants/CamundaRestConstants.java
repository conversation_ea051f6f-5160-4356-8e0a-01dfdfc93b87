package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

/**
 * Author: <PERSON><PERSON> Date: 16/01/20 Description: Contains all constants which is related to
 * camunda
 */
@UtilityClass
public class CamundaRestConstants {

  public final String SUSPENDED = "suspended";
  public final String INCLUDE_PROCESS_INSTANCES = "includeProcessInstances";
  public final String CASCADE = "cascade";
  public final String SKIP_CUSTOM_LISTENERS = "skipCustomListeners";
  public final String MISMATCH_MESSAGE_CORRELATION = "MismatchingMessageCorrelationException";
  public final String EXTERNAL_TASK_IS_SUSPENDED = "(?s).*ExternalTask with id '[^']*' is suspended.*";
  public final String ASYNC_CORRELATION_NO_ACTIVE_PROCESSES_ERROR =
      ".*\"type\":\"BadUserRequestException\",\"message\":\"Process instance ids cannot be empty: process instance ids is empty\".*";
}
