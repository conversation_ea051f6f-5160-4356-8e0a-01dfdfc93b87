package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR> stripathy1 Event Header constants which will be passed during publishing an event
 */
@UtilityClass
public class EventHeaderConstants {

  public final String INTUIT_TID = "intuitTid";
  public final String PUBLISHING_ASSET_ALIAS = "publishingAssetAlias";
  public final String ACTOR_ID = "actorId";
  public final String EVENT_TYPE = "eventType";
  public final String ENTITY_ID = "entityId";
  public final String ENTITY_TYPE = "entityType";
  public final String OFFERING_ID = "offeringId";
  public final String ENTITY_VERSION = "entityVersion";
  public final String TARGET_ASSET_ALIAS = "targetAssetAlias";
  public final String IDEMPOTENCY_KEY = "idempotenceKey";
  public final String OWNER_ID = "ownerId";
  public final String DOMAIN_EVENT = "domainEvent";
  public final String KAFKA_HEADERS_OVERRIDE = "kafka_headers_override";
  public final String HANDLER_ID = "handlerId";
  public final String INTUIT_USER_ID = "intuitUserId";
  public final String ACCOUNT_ID = "entityId-accountId";

  // numaflow kafka constants

  public final String KAFKA_TOPIC_NAME = "kafka_topic_name";
  public final String KAFKA_TIMESTAMP = "kafka_timestamp";
  public final String KAFKA_TIMESTAMP_TYPE = "kafka_timestamp_type";
  public final String EVENT_ENTITY_TYPE = "eventEntityType";

  // gc constants

  public final String TOPIC = "topic";
  public final String CONSUMER_GROUP = "consumerGroup";
}
