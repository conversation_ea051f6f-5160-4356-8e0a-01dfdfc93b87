package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

public interface EventScheduleConstants {
  // API headers
  String COMPANY_ID = "intuit_companyid";
  String REQUEST_ID = "intuit_requestid";
  String SCHEDULE_TYPE = "intuit_scheduletype";

  // API headers value
  String SCHEDULE_TYPE_VALUE = "USERS";

  // Config path
  String CONFIG_PATH = "event-scheduler";
  String TIMEZONE = "America/Los_Angeles";
  String END_DATE = "9999-12-31";
  Integer INTERVAL = 1;

  // sdkProvider
  String PROVIDER_TYPE = "$type";
}
