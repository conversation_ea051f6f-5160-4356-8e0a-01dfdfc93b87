package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class ITMConstants {
  public static final String ITM_STATUS_IN_PROGRESS = "Inprogress";
  public static final String ITM_STATUS_COMPLETE = "Complete";

  public static final String ITM_REFERENCES_TYPE = "referenceType";
  public static final String ITM_REFERENCES_ID = "referenceId";

  public static final String ITM_TASK_EXTERNAL_BLOB_METADATA = "external_blob_metadata";
  public static final String ITM_TASK_BLOB_METADATA = "blob";
}
