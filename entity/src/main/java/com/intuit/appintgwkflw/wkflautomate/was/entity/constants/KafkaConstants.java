package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

public class KafkaConstants {
    public static final String SECURITY_PROTOCOL_CONFIG = "security.protocol";
    public static final String SSL_ENABLED_PROTOCOLS_CONFIG = "ssl.enabled.protocols";
    public static final String ACTOR_ID = "SYSTEM";
    //  Currently this value is hardcoded to 1. Eventually after IEDM migration this version would be fetched from IEDM entityVersionid
    public static final String VERSION_ID = "1";
    public static final String KAFKA_TOPIC_HEADER = "kafka_receivedTopic";
    public static final String KAFKA_DLQ_SUFFIX = "-dlq";

    /** Error messages for logging Kafka Publisher errors */
    public static final String PUBLISH_ENTITY_NULL_ERROR = "Publish entity is null";
    public static final String EVENT_HEADER_ENTITY_NULL_ERROR = "Event header entity received is null";
    public static final String EVENT_PUBLISHER_DISABLED_ERROR = "Event publisher is disabled";
    public static final String ENTITY_ID_NULL_ERROR = "Entity Id is null";
    public static final String TARGET_ASSET_ALIAS_NULL_ERROR = "Target Asset Alias Id is null";
    public static final String PUBLISH_EVENT_TYPE_NULL_ERROR = "Publish Event Id is null";
    public static final String OFFERING_ID_NULL_ERROR = "Offering Id is null";
    public static final String PRODUCER_ENTITY_TOPIC_MAPPING_NULL_ERROR = "Producer entity topic mapping is null";
    public static final String TOPIC_NAME_NULL_ERROR = "Topic name is null";
    public static final String EVENT_PAYLOAD_NULL_ERROR = "Event payload is null";
    public static final String EVENT_CONFIGURATION_NULL_ERROR = "Event configuration is null";
    public static final String PRODUCER_EVENT_CONFIGURATION_NULL_ERROR = "Producer config in Event configuration is null";
    public static final String CONSUMER_EVENT_CONFIGURATION_NULL_ERROR = "Consumer config in event configuration is null";
    public static final String CONSUMER_RETRY_CONFIG_NULL_ERROR = "Consumer retry config in event configuration is null";
    public static final String BOOTSTRAP_SERVER_NULL_ERROR = "Bootstrap servers not present in config";
    public static final String INVALID_PRODUCER_ENVIRONMENT_ERROR = "Environment set in producer config is invalid";
    public static final String PARTITION_KEY_NULL = "Partition key is null";

    /** Ids assigned to Kafka listeners to access specific listeners */
    public static final String EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID = "ExternalTaskDLQKafkaConsumerId";
}