package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OfflineTicketClientConstants {

  public static final String SERVICE_TYPE = "was";

  public static final String INTUIT_TOKEN = "intuit_token";

  public static final int HARD_EXPIRATION_IN_SECONDS = 60 * 60 * 24 * 365 * 5;// 5 years

  public static final int REFRESH_WINDOW_FOR_SOFT_EXPIRATION_IN_DAYS = 14;

// Ticket expires somewhere between 2-10 minutes.
  public static final int CBT_EXPIRATION_MINUTES = 6;

  public static final String JOB_PURPOSE = "Intuit.appintgwkflw.wkflautomate.v2offlinewas";

  //Authorizing Intuit to perform any action means it has API has to provide subject_id as 50million
  public static final String SUBJECT_ID = "50000000";
}
