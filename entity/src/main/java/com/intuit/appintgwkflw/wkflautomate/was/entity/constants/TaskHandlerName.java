package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * This action name is used to fetch the Handler implementation for external task.Handler name is
 * handlerName_actionname
 *
 * <AUTHOR>
 */
@Getter
public enum TaskHandlerName {
  APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER("appconnect_executeWorkflowAction"),
  APP_CONNECT_DUZZIT_REST_ACTION_HANDLER("appconnect_executeDuzzitRestAction"),
  WAS_UPDATE_PROCESS_STATUS_ACTION_HANDLER("was_updateProcessStatus"),
  WAS_APP_CONNECT_WORKFLOW_DELETE_HANDLER("was_executeDeleteAction"),
  WAS_APP_CONNECT_WORKFLOW_DISABLE_HANDLER("was_executeDisableAction"),
  DUMMY_ACTION_HANDLER("appconnect_executeDummyAction"),
  WAS_DATA_STORE_DELETION_HANDLER("was_dataStoreDeletion"),
  CAMUNDA_DELETION_HANDLER("was_camundaDeletion"),
  DELETE_APPCONNECT_WORKFLOW_HANDLER("was_executeDeleteActionNonWas"),
  EXECUTE_APPCONNECT_DUZZIT_HANDLER("was_executeAppConnectDuzzit"),
  WAS_UNSUBSCRIBE("was_unsubscribeAuthDelete"),
  WAS_PUBLISH_EVENT_HANDLER("was_publishEvent"),
  WAS_PROCESS_STATUS_CHECKER_ACTION_HANDLER("was_checkProcessStatus"),
  WAS_CORRELATE_NON_ENDED_PROCESS("was_correlateNonEndedProcess"),
  WAS_TRIGGER_HANDLER("was_triggerHandler"),
  WAS_EVALUATE_DMN_HANDLER("was_evaluateDMN"),
  TEST_ACTION_HANDLER("was_test"),
  WAS_WORKFLOW_CUSTOM_TASK_HANDLER("was_workflowCustomTaskHandler"),
  WAS_INIT_CALL_ACTIVITY("was_initCallActivity"),
  WAS_APPROVAL_HANDLER("was_approvalHandler"),
  WORKFLOW_VARIABILITY_BASED_FILTER_HANDLER("was_workflowVariabilityBasedFilter"),
  BULK_DELETE_DEFINITIONS_HANDLER("was_bulkDeleteDefinitions"),
  EXPERIENCE_DECISION_HANDLER("was_experienceDecisionHandler"),
  BULK_UPDATE_APPROVAL_PREFS_HANDLER("was_bulkUpdateApprovalPrefs"),
  WAS_CREATE_VOC_JIRA("was_createVocJira");

  private String taskHandlerName;

  TaskHandlerName(String taskHandlerName) {
    this.taskHandlerName = taskHandlerName;
  }

  private static Map<String, TaskHandlerName> reverseActionHandlerMap = addActionHandlerMapping();

  /** @return reverse mapping of handler name to Task Hanlder name */
  public static Map<String, TaskHandlerName> addActionHandlerMapping() {
    Map<String, TaskHandlerName> actionHandlerMap = new HashMap<>();
    TaskHandlerName[] actionHandlers = TaskHandlerName.values();
    Arrays.stream(actionHandlers)
        .forEach(
            handler -> {
              actionHandlerMap.put(handler.getTaskHandlerName(), handler);
            });
    return actionHandlerMap;
  }

  public static TaskHandlerName getActionFromName(String handlerName) {
    return reverseActionHandlerMap.get(handlerName);
  }
}
