package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.Getter;

@Getter
public enum WorkFlowVariables {
  ERROR("error"),
  DELEGATOR_NAME("delegator"),
  RESPONSE("response"),
  ACTION_ID("ID"),
  ACTION_URL("URL"),
  PARAMETERS_KEY("parameterDetails"),
  HANDLER_DETAILS_KEY("handlerDetails"),
  TASK_DETAILS_KEY("taskDetails"),
  STEP_DETAILS_KEY("stepDetails"),
  RECURRENCE_DETAILS_KEY("recurrenceDetails"),
  RECURRENCE_RULE_KEY("recurrenceRule"),
  PROCESS_VARIABLE_DETAILS_KEY("processVariablesDetails"),
  CURRENT_STEP_DETAILS("currentStepDetails"),
  PROCESS_STARTABLE_EVENTS_KEY("startableEvents"),
  PENDING_EXTEND_LOCK("pendingExtendLock"),
  ACTIVITY_TYPE("type"),
  EVENTS("events"),
  MULTI_LEVEL("multiLevel"),
  IGNORE_NON_ENTITY_PROCESS_VARIABLES_DETAILS("ignoreNonEntityProcessVariablesDetails"),
  IGNORE_PROCESS_VARIABLES_DETAILS("ignoreProcessVariablesDetails"),
  IS_THIRD_PARTY_TXN("isThirdPartyTxn"),
  DECISION("decision");

  private String name;

  WorkFlowVariables(String name) {
    this.name = name;
  }
}
