package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

/** <AUTHOR> */
public final class WorkflowBeansConstants {

  public static final String UPDATE_DEFINITION_HANDLER = "updateDefinitionHandler";
  public static final String CREATE_DEFINITION_HANDLER = "creteDefinitionHandler";
  public static final String DELETE_DEFINITION_HANDLER = "deleteDefinitionHandler";
  public static final String DISABLE_DEFINITION_HANDLER = "disableDefinitionHandler";
  public static final String ENABLE_DEFINITION_HANDLER = "enableDefinitionHandler";
  public static final String V3_TRIGGER_HANDLER = "v3TriggerHandler";
  public static final String V3_RULE_HANDLER = "v3RuleHandler";
  public static final String MULTIPLE_USER_DEFINITION_TRIGGER_HANDLER =
      "multipleUserDefinitionTriggerHandler";
  public static final String SYSTEM_DEFINITION_TRIGGER_HANDLER = "systemDefinitionTriggerHandler";
  public static final String CREATE_CUSTOM_DEFINITION_HANDLER = "CreateCustomDefinitionHandler";
  public static final String CREATE_MULTI_STEP_DEFINITION_HANDLER = "CreateMultiStepDefinitionHandler";
  public static final String CREATE_MULTI_SPLIT_RULE_LINE_PROCESSOR = "CreateMultiSplitRuleLineProcessor";
  public static final String CUSTOM_DEFINITION_PLACEHOLDER_EXTRACTOR = "customDefinitionPlaceHolderExtractor";
  public static final String PRECANNED_DEFINITION_PLACEHOLDER_EXTRACTOR = "precannedDefinitionPlaceholderExtractor";
  public static final String ACTIVITY_RUNTIME_HANDLER = "activityDomainEventRuntimeHandler";
  public static final String PROCESS_HANDLER = "processDomainEventHandler";
  public static final String DEFINITION_HANDLER = "definitionDomainEventHandler";
  public static final String TEMPLATE_HANDLER = "templateDomainEventHandler";
  public static final String MULTI_STEP_TEMPLATE_BUILDER = "multiStepTemplateBuilder";
  public static final String TEMPLATE_BUILDER = "templateBuilder";
  public static final String MULTI_STEP_BPMN_PARSER_IMPL = "MultiStepBpmnProcessorImpl";
  public static final String BPMN_PROCESSOR_IMPL = "BpmnProcessorImpl";
  public static final String APPCONNECT_RECORD_FETCHER = "appconnectRecordFetcher";
  public static final String MULTI_SPLIT_DEFINITION_CONDITION_BUILDER = "multiSplitDefinitionConditionBuilder";

  private WorkflowBeansConstants() {}
}
