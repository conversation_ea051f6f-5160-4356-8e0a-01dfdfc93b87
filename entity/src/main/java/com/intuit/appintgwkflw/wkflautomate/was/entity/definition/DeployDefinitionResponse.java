package com.intuit.appintgwkflw.wkflautomate.was.entity.definition;

import lombok.Data;

import java.util.Map;

@Data
public class DeployDefinitionResponse {
  //id of the deployment
  private String id;

  private Map<String, DeployedDefinition> deployedProcessDefinitions;

  private Map<String, DeployedDefinition> deployedDecisionDefinitions;

  @Data
  public static class DeployedDefinition {
    private String id;
    private String key;
    private String activityId;
  }
}
