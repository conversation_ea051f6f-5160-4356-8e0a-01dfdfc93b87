package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import java.util.LinkedList;
import java.util.List;
import lombok.Data;

@Data
public class Action {
  private String id;
  private String name;
  private Handler handler;
  private Boolean selected;
  private Boolean required;
  private Boolean obfuscate;
  private List<Parameter> parameters = new LinkedList<>();
  private List<Action> subActions = new LinkedList<>();
  private List<String> helpVariables = new LinkedList<>();
}
