package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActionGroup {
  private String id;
  private List<String> actionIds = new LinkedList<>();
  private ActionIdMapper actionIdMapper;
  private List<Action> actions = new LinkedList<>();
  private List<String> onDemandActionIds = new LinkedList<>();
  // This specifies the list of ids that needs to be excluded in case we want different condition
  // parameters for different CTAs
  private Set<String> unsupportedAttributes;
  // precanned template id which will be used to set workflowSteps if defined under a particular
  // actiongroup
  private String precannedTemplateId;
  // Default Attributes per actiongroup
  private Set<String> defaultAttributes;
  private List<Steps> steps;
  private Trigger trigger;
}
