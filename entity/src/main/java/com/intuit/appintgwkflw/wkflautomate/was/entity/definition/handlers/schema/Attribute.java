package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import java.util.LinkedList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> This class contains the meta information about various Attributes/Operators on
 *     which rules can be created.
 */

@Data
public class Attribute {
  private String id;
  private String name;
  private String type;
  private Boolean multiSelect;
  private String fieldValueOptions;
  private String defaultOperator;
  private String defaultValue;
  private List<String> unsupportedOperators = new LinkedList<>();
  private Boolean required;
  private Boolean configurable;
  private Boolean hidden;
  // If there is any FF associated, check if FF is on to list the attribute in rules list
  private String controllerFF;
  // To signal UI to obfuscate the values. For Rendering template in UCS flow.
  private Boolean obfuscate;

  public String getId()
  {
    return this.id;
  }
}
