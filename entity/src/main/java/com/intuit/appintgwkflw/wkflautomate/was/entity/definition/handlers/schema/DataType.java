package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import java.util.LinkedList;
import java.util.List;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 *     <p>Template Operator Metadata
 */
@Data
public class DataType {
  private String type;
  // This is the type inferred by the camunda engine. For example days should be interpreted as
  // integer
  private String nativeType;
  private List<Operator> operators = new LinkedList<>();

  public String getNativeType() {
    return StringUtils.isEmpty(nativeType) ? type : nativeType;
  }
}
