package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import lombok.Data;

/**
 * <AUTHOR> <br>
 *     </>Opearator level details. 1. id [The symbol should come in the Rules in case any
 *     operator is selected on the UI. For instance, "GREATER_THAN" selected then send '>' as a
 *     symbol to backend.] <br>
 *     </>2. DisplayName [Value to be displayed in the UI.]<br>
 */
@Data
public class Operator {
  private String name;
  private String id;
}
