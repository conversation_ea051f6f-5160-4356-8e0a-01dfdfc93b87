package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.UIActions;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class Parameter {
  private String id;
  private String name;
  private String fieldType;
  private List<String> possibleFieldValues = new LinkedList<>();
  private String handlerFieldName;
  private Boolean configurable;
  private Boolean requiredByHandler;
  private Boolean requiredByUI;
  private Boolean helpVariablesRequired;
  private List<String> helpVariables = new LinkedList<>();
  private Boolean multiSelect;
  private UIActions actionByUI;
  private List<String> fieldValues = new LinkedList<>();
  private ParameterDetailsValueType valueType;
  private ComputedVariableType computedVariableType;
  private Map<String, String> computedVariableDetails;
  private String getOptionsForFieldValue;
  private Boolean obfuscate;
  private Boolean isOverridable;

  // helpVariable contains following in order separated by COLON
  // displayName : handlerFieldName : fieldType
  public List<String> getHelpVariableDisplayNames() {
    return helpVariables.stream()
        .map(helpVariable -> helpVariable.split(WorkflowConstants.COLON)[0])
        .collect(Collectors.toList());
  }
}
