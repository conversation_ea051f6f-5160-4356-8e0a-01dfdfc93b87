package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;
/**
 * Workflows consists of the action group set for a particular entity type.
 * for example: SendExternalEmail is an action group set for Invoice.
 * this parameter will be populated differently for different entities and actiongroups.
 * for example: Invoice -> SendExternalEmail -> SendExternalEmailAction
 * */
@Data
public class ParameterOverrideSet {
  private String entityId;
  private String actionGroup;
  List<Action> actions = new LinkedList<>();
}
