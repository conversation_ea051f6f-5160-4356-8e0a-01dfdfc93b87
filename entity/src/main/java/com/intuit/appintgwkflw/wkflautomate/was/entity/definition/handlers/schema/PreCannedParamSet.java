package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;
/**
 * This class contains the overridden actions for a particular precanned templates.
 * for example: SendExternalEmail is an action group set for InvoiceUnsentReminder will have
 * different message compared to OverdueReminder.
 * */
@Data
public class PreCannedParamSet {
    private String id;
    private List<Action> actions = new LinkedList<>();
}
