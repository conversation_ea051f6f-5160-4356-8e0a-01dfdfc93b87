package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <br>
 *     </>
 *     <p>This is the Record Class for BMOW Templates. Record refers to the the various RecordTypes
 *     like Invoice, Estimates etc.
 *     <p>Records will have list of Default Attributes[DMN Condition Parameter], Default Actions
 *     associated with the Record Type.
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class Record {
  private String source;
  private String id;
  private Set<String> defaultAttributes;
  private List<ActionGroup> actionGroups = new LinkedList<>();
  private List<Attribute> attributes = new LinkedList<>();
  private List<String> helpVariables = new LinkedList<>();
  private List<Steps> steps;

  /**
   * Filter record level actionGroups based on actionKey passed
   * @param actionKey string action key
   * @return optional of actionGroup
   */
  public Optional<ActionGroup> getFilteredActiongroup(String actionKey) {
    return Optional.ofNullable(actionGroups.stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
            .collect(Collectors.toList())
            .stream().findFirst().orElse(null));
  }
}
