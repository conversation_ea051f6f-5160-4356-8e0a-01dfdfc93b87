package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;
import java.util.List;
import lombok.Data;
import org.joda.time.DateTime;

/**
 * Pojo to map a subset view between V4 recurrence and
 * a config defined recurrence for one click statement setup.
 * TODO Look at getting parity between config and V4 objects directly.
 */
@Data
public class RecurrenceRuleMapper {
    private RecurTypeEnum recurType;
    private Integer interval;
    private Integer dayOfMonth;
    private DayOfWeekEnum dayOfWeek;
    private MonthsOfYearEnum monthOfYear;
    private WeekOfMonthEnum weekOfMonth;
    private List<Integer> daysOfMonth;
    private List<DayOfWeekEnum> daysOfWeek;
    private List<MonthsOfYearEnum> monthsOfYear;
    private List<WeekOfMonthEnum> weeksOfMonth;
    private TimeDuration recurrenceTime;
    private DateTime startDate = new DateTime();
    private String timeZone;
    private Boolean active;
}
