package com.intuit.appintgwkflw.wkflautomate.was.entity.downgrade;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class Unsubscribe {
    private Downgrade downgrade;

    @Getter
    @Setter
    public static class Downgrade {
        private List<Template> templates;
    }

    @Getter
    @Setter
    public static class Template {
        private String workflow;
        private String entityType;
        private String entityChangeType;
    }
}
