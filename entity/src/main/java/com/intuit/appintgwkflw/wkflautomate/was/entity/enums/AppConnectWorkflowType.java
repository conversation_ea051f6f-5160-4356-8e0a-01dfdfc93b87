package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum AppConnectWorkflowType {

  BPMN_WAS("bpmn_was");

  @JsonValue
  private String workflowType;

  AppConnectWorkflowType(String workflowType) {
    this.workflowType = workflowType;
  }

  public static AppConnectWorkflowType fromType(String type) {
    for (AppConnectWorkflowType appConnectWorkflowType : AppConnectWorkflowType.values()) {
      if (appConnectWorkflowType.workflowType.equals(type)) {
        return appConnectWorkflowType;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }
}
