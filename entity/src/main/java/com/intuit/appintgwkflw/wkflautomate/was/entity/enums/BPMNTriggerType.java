package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum BPMNTriggerType {
  TASK("task"),
  BOUNDARY_EVENT("boundaryEvent"),
  EVENT("event");

  private String bpmnTriggerType;

  BPMNTriggerType(String bpmnTriggerType) {
    this.bpmnTriggerType = bpmnTriggerType;
  }

  @Override
  public String toString() {
    return bpmnTriggerType;
  }

  public static BPMNTriggerType fromType(String type) {
    for (BPMNTriggerType triggerType : BPMNTriggerType.values()) {
      if (triggerType.getBpmnTriggerType().equals(type)) {
        return triggerType;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }
}
