package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@Getter
public enum BooleanOperator {
  EQUALS("EQ", "Equals");
  private final String symbol;
  private final String description;

  public static Map<String, String> possibleOperatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (BooleanOperator s : values()) {
      possibleOperatorValueMap.put(s.symbol, s.description);
    }
    return possibleOperatorValueMap;
  }
}
