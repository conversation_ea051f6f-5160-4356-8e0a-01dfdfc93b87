package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BpmnComponentType {
  START_EVENT("startEvent"),
  END_EVENT("endEvent"),
  BOUNDARY_EVENT("boundaryEvent"),
  TIMER_EVENT("timerEvent"),
  PARALLEL_GATEWAY("parallelGateway"),
  EXCLUSIVE_GATEWAY("exclusiveGateway"),
  INCLUSIVE_GATEWAY("inclusiveGateway"),
  TASK("task"),
  BUSINESS_RULE_TASK("businessRuleTask"),
  SEND_TASK("sendTask"),
  RECEIVE_TASK("receiveTask"),
  SERVICE_TASK("serviceTask"),
  SEQUENCE_FLOW("sequenceFlow"),
  SUB_PROCESS("subProcess"),
  CALL_ACTIVITY("callActivity");

  private String name;

  BpmnComponentType(String name) {
    this.name = name;
  }

  @Override
  public String toString() {
    return name;
  }
}
