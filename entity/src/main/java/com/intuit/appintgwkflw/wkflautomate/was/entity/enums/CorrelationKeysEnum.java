package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * This enum can be used as a place/lookup for setting global keys mandatory across all workflows.
 */
@RequiredArgsConstructor
@Getter
public enum CorrelationKeysEnum {
  TEMPLATE_NAME("templateName", "String"),
  DEFINITION_KEY("definitionKey", "String");
  private final String name;
  private final String dataType;
}
