package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum CreatorType {
  SYSTEM("system"),
  USER("user");

  private String creatorType;

  CreatorType(String creatorType) {
    this.creatorType = creatorType;
  }

  @Override
  public String toString() {
    return creatorType;
  }

  public static CreatorType fromType(String type) {
    for (CreatorType creatorType : CreatorType.values()) {
      if (creatorType.getCreatorType().equals(type)) {
        return creatorType;
      }
    }
    throw new UnsupportedOperationException("The type " + type + " is not supported!");
  }
}
