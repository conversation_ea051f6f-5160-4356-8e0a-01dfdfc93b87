package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.Arrays;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.springframework.util.ObjectUtils;

/** This Enum has mapping of template Name wrt to CTA [like <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> etc.] */
@Getter
@RequiredArgsConstructor
public enum CustomWorkflowType {
  REMINDER("reminder", "customReminder", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY),
  APPROVAL("approval", "customApproval", MultipleDefType.SINGLE_DEFINITION_PER_ENTITY),
  SCHEDULEDACTIONS("scheduledActions", "customScheduledActions", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY),
  NOTIFICATION("notification", "customNotification", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY),
  SCHEDULEDTRANSFER("scheduledTransfer", "customScheduledTransfer", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY),
  SEND("send", "customSendEntity", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY),
  UPDATE("update", "customUpdateEntity", MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY);

  private final String actionKey;
  private final String templateName;
  private final MultipleDefType multipleDefType;
  private static Map<String, String> actionTemplateNameMap;


  private enum MultipleDefType {
    MULTIPLE_DEFINITION_PER_ENTITY,
    SINGLE_DEFINITION_PER_ENTITY
  }

  public boolean canCreateMultipleEnitityDefinitions() {
    return this.getMultipleDefType().equals(MultipleDefType.MULTIPLE_DEFINITION_PER_ENTITY);
  }

  private static Map<String, String> actionTemplateNameMapping() {
    if (ObjectUtils.isEmpty(actionTemplateNameMap)) {
      actionTemplateNameMap = new HashMap<>();
      for (CustomWorkflowType s : values()) {
        actionTemplateNameMap.put(s.actionKey, s.templateName);
      }
    }
    return actionTemplateNameMap;
  }
  public static Map<String, String> templateNameActionKeyMapping() {
    Map<String, String> templateNameActionKeyMapping = new HashMap<>();
    for (CustomWorkflowType s : values()) {
      templateNameActionKeyMapping.put(s.templateName, s.actionKey);
    }
    return templateNameActionKeyMapping;
  }

  public static String getTemplateName(String actionKey) {
    return actionTemplateNameMapping().get(actionKey);
  }

	/**
	 * @param lookupKey actionKey or workflowName
	 * @return CustomWorkflowType by doing a lookup on actionKey or workflow name
	 */
	public static CustomWorkflowType get(String lookupKey) {
		CustomWorkflowType customWorkflowType = getCustomWorkflow(lookupKey);
		return Objects.nonNull(customWorkflowType) ? customWorkflowType : getCustomWorkflowForTemplateName(lookupKey);
	}
  
  public static CustomWorkflowType getCustomWorkflow(String actionKey) {
    return Arrays.stream(CustomWorkflowType.values()).filter(customWorkflow -> customWorkflow.getActionKey().equalsIgnoreCase(actionKey)).findFirst().orElse(null);
  }

  public static CustomWorkflowType getCustomWorkflowForTemplateName(String templateName) {
    return Arrays.stream(CustomWorkflowType.values()).filter(customWorkflow -> customWorkflow.getTemplateName().equalsIgnoreCase(templateName)).findFirst().orElse(null);
  }

  public static String getActionKey(String templateName) {
    return templateNameActionKeyMapping().get(templateName);
  }
}
