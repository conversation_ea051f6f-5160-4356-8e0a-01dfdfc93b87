package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.Arrays;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/** <AUTHOR>
 *
 * Contains all the Custom DMN supported operator.
 * */
public enum DMNSupportedOperator {
  DAYS,
  LIST,
  STRING,
  DEFAULT;
  private static final Map<String, DMNSupportedOperator> populateOperatorValueMap =
      operatorValueMap();

  private static Map<String, DMNSupportedOperator> operatorValueMap() {
    return Arrays.stream(DMNSupportedOperator.values())
        .collect(Collectors.toMap(DMNSupportedOperator::name, Function.identity()));
  }

  public static DMNSupportedOperator value(String type) {
    if(type != null) {
      type = type.toUpperCase();
    }
    return populateOperatorValueMap.getOrDefault(type, DEFAULT);
  }
}
