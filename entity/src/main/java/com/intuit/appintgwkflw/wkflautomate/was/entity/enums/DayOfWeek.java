package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum DayOfWeek {
  SUNDAY("SUNDAY", 1),
  MONDAY("MONDAY", 2),
  TUESDAY("TUESDAY", 3),
  WEDNESDAY("WEDNESDAY", 4),
  THURSDAY("THURSDAY", 5),
  FRIDAY("FRIDAY", 6),
  SATURDAY("SATURDAY", 7);

  private final String name;
  private final int index;
  private static Map<String, Integer> dayOfWeekIndexMap;

  private static Map<String, Integer> dayOfWeekNameIndexMapping() {
    if (ObjectUtils.isEmpty(dayOfWeekIndexMap)) {
      dayOfWeekIndexMap = new HashMap<>();
      for (DayOfWeek s : values()) {
        dayOfWeekIndexMap.put(s.name, s.index);
      }
    }
    return dayOfWeekIndexMap;
  }

  public static int getDayOfWeekIndex(String dayOfWeek) {
    return dayOfWeekNameIndexMapping().get(dayOfWeek);
  }
}
