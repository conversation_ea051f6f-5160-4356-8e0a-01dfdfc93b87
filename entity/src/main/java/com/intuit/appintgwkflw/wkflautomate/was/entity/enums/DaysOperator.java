package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Supported operator by DAYS.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DaysOperator {
  BEFORE("BF", "Before"),
  AFTER("AF", "After"),
  ON("ON", "On"),
  EVERY_DAY("ALL", "Every");

  private final String symbol;
  private final String description;

  private static final Map<String, String> populateOperatorValueMap = operatorValueMap();

  private static Map<String, String> operatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (DaysOperator dueDateOperator : values()) {
      possibleOperatorValueMap.put(dueDateOperator.symbol, dueDateOperator.description);
    }
    return possibleOperatorValueMap;
  }

  /** @return map of supported operators. */
  public static Map<String, String> possibleOperatorValueMap() {
    return populateOperatorValueMap;
  }
}
