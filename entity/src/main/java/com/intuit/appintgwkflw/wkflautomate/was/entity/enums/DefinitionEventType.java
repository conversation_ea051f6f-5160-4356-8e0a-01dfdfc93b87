package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

/**
 * Enum storing type of Definition Event type which is to be used to get definition event processor
 */
@Getter
public enum DefinitionEventType {
  CUSTOM_REMINDER_FETCH_TRANSACTIONS("customReminderFetchTransactions");

  private String eventType;

  DefinitionEventType(String eventType) {
    this.eventType = eventType;
  }

  public static DefinitionEventType fromType(String type) {
    for (DefinitionEventType definitionEventType : DefinitionEventType.values()) {
      if (definitionEventType.eventType.equals(type)) {
        return definitionEventType;
      }
    }
    throw new UnsupportedOperationException(
        "The handleDefinitionEventType " + type + " is not supported!");
  }

  @Override
  public String toString() {
    return eventType;
  }
}
