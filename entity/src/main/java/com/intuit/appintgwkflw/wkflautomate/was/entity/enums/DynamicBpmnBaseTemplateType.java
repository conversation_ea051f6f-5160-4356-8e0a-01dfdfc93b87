package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 *
 * This Enum has mapping of base template resource name wrt to CTA for dynamic multi condition workflows
 * [like <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> etc.]
 */

@Getter
@RequiredArgsConstructor
public enum DynamicBpmnBaseTemplateType {
  APPROVAL("approval", "customApprovalBaseTemplate"),
  REMINDER("reminder", "customReminderBaseTemplate");


  private final String actionKey;
  private static Map<String, String> actionTemplateNameMap = actionTemplateNameMapping();
  private final String baseTemplateResourceName;

  private static Map<String, String> actionTemplateNameMapping() {
    if (ObjectUtils.isEmpty(actionTemplateNameMap)){
      actionTemplateNameMap = new HashMap<>();
      for (DynamicBpmnBaseTemplateType s : values()) {
        actionTemplateNameMap.put(s.actionKey, s.baseTemplateResourceName);
      }
    }
    return actionTemplateNameMap;
  }

  public static String getTemplateResourceName(String actionKey) {
    return actionTemplateNameMapping().get(actionKey);
  }


}