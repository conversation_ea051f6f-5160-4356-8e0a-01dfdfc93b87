package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum ModelType {
  BPMN("bpmn"),
  DMN("dmn");

  private String modelType;

  ModelType(String modelType) {
    this.modelType = modelType;
  }

  @Override
  public String toString() {
    return modelType;
  }

  public static ModelType fromType(String type) {
    for (ModelType modelType : ModelType.values()) {
      if (modelType.getModelType().equals(type)) {
        return modelType;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }
}
