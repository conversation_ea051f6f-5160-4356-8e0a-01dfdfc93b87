package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * This enum stores the conditional expression and its corresponding negation used for creating the
 * filter conditions in MCR
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum OperatorNegation {
  LESS_THAN("LT", "NOT_LT"),
  GREATER_THAN("GT", "NOT_GT"),
  LESS_THAN_EQUAL_TO("LTE", "NOT_LTE"),
  GREATER_THAN_EQUAL_TO("GTE", "NOT_GTE"),
  BETWEEN("BTW", "NOT_BTW"),
  EQUALS("EQ", "NOT_EQ"),
  CONTAINS("CONTAINS", "NOT_CONTAINS"),
  NOT_CONTAINS("NOT_CONTAINS","CONTAINS");

  private final String symbol;
  private final String negation;

  private static final Map<String, String> operatorNegationMap = new HashMap<>();

  static {
    // Initializing the map
    for (OperatorNegation operatorNegation : values()) {
      operatorNegationMap.put(operatorNegation.symbol, operatorNegation.negation);
    }
  }

  public static String getNegation(String operator) {
    return operatorNegationMap.get(operator);
  }
}
