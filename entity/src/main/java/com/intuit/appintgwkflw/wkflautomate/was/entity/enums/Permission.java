package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 *     <p></>This Enum will be used for Workflow Permission(s)
 */
@Getter
public enum Permission {
  TASK_READ("intuit.sbseg.was.task.read", "read"),
  TASK_UPDATE("intuit.sbseg.was.task.update", "update");

  private final String name;
  private final String humanTaskPermission;

  Permission(String name, String humanTaskPermission) {
    this.name = name;
    this.humanTaskPermission = humanTaskPermission;
  }

  @Override
  public String toString() {
    return name;
  }
}
