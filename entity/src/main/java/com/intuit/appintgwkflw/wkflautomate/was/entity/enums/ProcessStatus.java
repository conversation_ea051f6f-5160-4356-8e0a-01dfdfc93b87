package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ProcessStatus {
  ACTIVE("active"),
  SUSPENDED("suspended"),
  CANCELLED("cancelled"),
  // For Processes Terminated externally. For All Processes deleted from Cockpit
  // individually or via batch.
  ENDED("ended"),
  ERROR("error");

  @JsonValue private String processStatus;

  ProcessStatus(String templateCategory) {
    this.processStatus = templateCategory;
  }

  public static ProcessStatus lookupProcessStatus(String type) {
    for (ProcessStatus processStatus : ProcessStatus.values()) {
      if (processStatus.getProcessStatus().equals(type)) {
        return processStatus;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }

  @Override
  public String toString() {
    return processStatus;
  }
}
