package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * This enum file contains ProjectType used for creating workflow task in project service
 */
@AllArgsConstructor
@Getter
public enum ProjectType {
  QB_INVOICE_REMINDER(RecordType.INVOICE, TaskType.QB_INVOICE, CustomWorkflowType.REMINDER,
      CloseTaskType.CLOSE_MANUALLY),
  QB_INVOICE_DUE_REMINDER(RecordType.INVOICE, TaskType.QB_INVOICE, CustomWorkflowType.REMINDER,
      CloseTaskType.TXN_PAID),
  QB_INVOICE_UNSENT_REMINDER(RecordType.INVOICE, TaskType.QB_INVOICE, CustomWorkflowType.REMINDER,
      CloseTaskType.TXN_SENT),
  QB_INVOICE_APPROVAL(RecordType.INVOICE, TaskType.QB_INVOICE, CustomWorkflowType.APPROVAL,
      CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_BILL_REMINDER(RecordType.BILL, TaskType.QB_BILL, CustomWorkflowType.REMINDER,
      CloseTaskType.CLOSE_MANUALLY),
  QB_BILL_DUE_REMINDER(RecordType.BILL, TaskType.QB_BILL, CustomWorkflowType.REMINDER,
      CloseTaskType.TXN_PAID),
  QB_ESTIMATE_UNSENT_REMINDER(RecordType.ESTIMATE, TaskType.QB_ESTIMATE,
      CustomWorkflowType.REMINDER, CloseTaskType.TXN_SENT),
  QB_ESTIMATE_STATUS_REMINDER(RecordType.ESTIMATE, TaskType.QB_ESTIMATE,
      CustomWorkflowType.REMINDER, CloseTaskType.TXN_ACCEPTED),
  QB_ESTIMATE_REMINDER(RecordType.ESTIMATE, TaskType.QB_ESTIMATE,
      CustomWorkflowType.REMINDER, CloseTaskType.CLOSE_MANUALLY),
  QB_BILL_APPROVAL(RecordType.BILL, TaskType.QB_BILL, CustomWorkflowType.APPROVAL,
      CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_PO_REMINDER(RecordType.PURCHASE_ORDER, TaskType.QB_PURCHASE_ORDER, CustomWorkflowType.REMINDER,
          CloseTaskType.CLOSE_MANUALLY),
  QB_PO_UNSENT_REMINDER(RecordType.PURCHASE_ORDER, TaskType.QB_PURCHASE_ORDER, CustomWorkflowType.REMINDER,
          CloseTaskType.TXN_SENT),
  QB_PO_STATUS_REMINDER(RecordType.PURCHASE_ORDER, TaskType.QB_PURCHASE_ORDER, CustomWorkflowType.REMINDER,
          CloseTaskType.TXN_CLOSED),
  QB_PO_APPROVAL(RecordType.PURCHASE_ORDER, TaskType.QB_PURCHASE_ORDER, CustomWorkflowType.APPROVAL,
          CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_BILL_PAYMENT_NOTIFICATION(RecordType.BILL_PAYMENT, TaskType.QB_BILL_PAYMENT, CustomWorkflowType.NOTIFICATION,
          CloseTaskType.CLOSE_MANUALLY),
  QB_PAYMENT_NOTIFICATION(RecordType.PAYMENT, TaskType.QB_PAYMENT, CustomWorkflowType.NOTIFICATION,
          CloseTaskType.CLOSE_MANUALLY),
  QB_EEMEXPENSE_APPROVAL(RecordType.EEM_EXPENSE, TaskType.QB_EEMEXPENSE, CustomWorkflowType.APPROVAL,
      CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_VENDOR_CREDIT_REMINDER(RecordType.VENDOR_CREDIT, TaskType.QB_VENDOR_CREDIT, CustomWorkflowType.REMINDER,
          CloseTaskType.CLOSE_MANUALLY),
  QB_SALES_RECEIPT_REMINDER(RecordType.SALES_RECEIPT, TaskType.QB_SALES_RECEIPT, CustomWorkflowType.REMINDER,
          CloseTaskType.CLOSE_MANUALLY),
  QB_EMPLOYEE_REMINDER(RecordType.EMPLOYEE, TaskType.QB_EMPLOYEE, CustomWorkflowType.REMINDER,
          CloseTaskType.CLOSE_MANUALLY),
  QB_CUSTOMER_REMINDER(RecordType.CUSTOMER, TaskType.QB_CUSTOMER, CustomWorkflowType.REMINDER,CloseTaskType.CLOSE_MANUALLY),
  QB_VENDOR_REMINDER(RecordType.VENDOR, TaskType.QB_VENDOR, CustomWorkflowType.REMINDER,CloseTaskType.CLOSE_MANUALLY),
  QB_ESTIMATE_APPROVAL(RecordType.ESTIMATE, TaskType.QB_ESTIMATE, CustomWorkflowType.APPROVAL,
      CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_SALES_TAX_REMINDER(RecordType.SALES_TAX, TaskType.QB_SALES_TAX, CustomWorkflowType.REMINDER,CloseTaskType.CLOSE_MANUALLY),
  QB_PROJECT_REMINDER(RecordType.PROJECT, TaskType.QB_PROJECT, CustomWorkflowType.REMINDER,
          CloseTaskType.CLOSE_MANUALLY),
  QB_BUDGET_APPROVAL(RecordType.BUDGET, TaskType.QB_BUDGET, CustomWorkflowType.APPROVAL,
          CloseTaskType.TXN_APPROVAL_CHANGED),
  QB_PROJECT_FINANCIAL_REMINDER(RecordType.PROJECT, TaskType.QB_PROJECT, CustomWorkflowType.REMINDER,
          CloseTaskType.PROJECT_FINANCIAL_CHANGED),
  QB_BANK_DEPOSIT_REMINDER(RecordType.DEPOSIT, TaskType.QB_ACCOUNT, CustomWorkflowType.REMINDER,
                      CloseTaskType.CREATE_DEPOSIT),
  QB_BILL_PAYMENT_APPROVAL(RecordType.BILL_PAYMENT, TaskType.QB_BILL_PAYMENT, CustomWorkflowType.APPROVAL,
      CloseTaskType.TXN_APPROVAL_CHANGED);

  private final RecordType recordType;
  private final TaskType taskType;
  private final CustomWorkflowType customWorkflowType;
  private final CloseTaskType closeTaskType;

  /**
   * Get the project type based on record, workflow type and close task value
   * @param recordType
   * @param actionKey
   * @param closeTaskType
   * @return
   */
  public static ProjectType getProject(String recordType, String actionKey, String closeTaskType) {
    RecordType record = RecordType.fromType(recordType);
    CustomWorkflowType customWorkflowType = CustomWorkflowType.getCustomWorkflow(actionKey);
    return Arrays.stream(ProjectType.values())
        .filter(
            projectType ->
                projectType.recordType.equals(record)
                    && projectType.customWorkflowType.equals(customWorkflowType)
                    && projectType.closeTaskType.name().equalsIgnoreCase(closeTaskType))
        .findFirst()
        .orElse(null);
  }
}
