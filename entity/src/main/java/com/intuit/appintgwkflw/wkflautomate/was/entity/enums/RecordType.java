package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RecordType {
  ESTIMATE("estimate", "Estimate"),
  INVOICE("invoice", "Invoice"),
  DEPOSIT("deposit", "Deposit"),
  BILL("bill", "Bill"),
  PURCHASE("purchase", "Purchase"),
  TIMESHEET("timesheet", "Timesheet"),
  STAGE_CONNECTION("stageconnection", "Stageconnection"),
  STAGE_ENTITY("stageentity", "Stageentity"),
  SUBSCRIPTION("subscription", "Subscription"),
  CANDIDATE("candidate", "Candidate"),
  ENGAGEMENT("engagement", "Engagement"),
  EXPERT("expert", "Expert"),
  APPOINTMENT("appointment", "Appointment"),
  ENVELOPE("envelope", "Envelope"),
  GENERAL_JOURNAL("general_journal","Journal"),

  // Note: EEM - Employee Expense Management
  EEM_EXPENSE("eemExpense", "EEMExpense"),
  EEM_EXPENSE_REPORT("eemExpenseReport", "EEMExpenseReport"),
  PURCHASE_ORDER("purchaseorder", "Purchase order"),
  STATEMENT("statement", "Statement"),
  BILL_PAYMENT("billpayment", "Bill Payment"),
  IBOSS("iboss", "IBOSS"),

  PROJECT("project", "Project"),
  PAYMENT("payment", "Payment"),
  REPORT("report", "Report"),

  VENDOR_CREDIT("vendorcredit", "Vendor Credit"),
  SALES_RECEIPT("salesreceipt", "Sales Receipt"),
  JOURNAL_ENTRY("journalentry", "Journal Entry"),
  VENDOR("vendor" , "Vendor"),
  DEPARTMENT("department", "Department"),
  TERM("term", "Term"),
  ACCOUNT("account", "Account"),
  CUSTOMER("customer", "Customer"),
  EMPLOYEE("employee", "Employee"),
  TASK("task", "Task"),
  SAVING_OPPORTUNITY("savingOpportunity", "Saving Opportunity"),
  TAX("tax", "Tax"),
  PARTNER("partner", "Partner"),
  COMPANY_DATA("companyData", "Company Data"),
  SALES_TAX("salestax","Sales Tax"),
  CDD("cdd", "cdd"),
  WORKER("worker", "Worker"),
  ORDER("order", "Order"),
  BUDGET("budget","Budget"),
  DLCP("dlcp","dlcp"),
  STS_SUBSCRIPTIONS("stssubscriptions","STS Subscriptions"),
  // Added for Overwatch Automation Tests
  TEST("test", "Test"),
  VOC_JIRA("vocjira", "vocjira"),
  CHECK("check","Check"),
  // Added for Demand Supply Balancer workflows
  DSB("dsb", "dsb"),
  IES_RRT("iesrrt", "iesrrt"),

  // Added for CMS approval workflow
  CAS("cas", "CAS"),
  
  // Added for creative management workflow
  CMS("cms", "CMS");
  
  @JsonValue private String recordType;
  private String displayValue;

  @Override
  public String toString() {
    return recordType;
  }

  public static RecordType fromType(String type) {
    for (RecordType recordType : RecordType.values()) {
      if (recordType.getRecordType().equalsIgnoreCase(type)) {
        return recordType;
      }
    }
    return null;
  }

  /**
   * This method filter's the RecordType with displayValue or recordType
   * @param  value it could be recordType or displayValue
   * @return RecordType
   */
  public static RecordType fromTypeOrValue(String value) {
    for (RecordType recordType : RecordType.values()) {
      if (recordType.getDisplayValue().equalsIgnoreCase(value)
          || recordType.getRecordType().equalsIgnoreCase(value)) {
        return recordType;
      }
    }
    return null;
  }
}
