package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import java.util.Map;
import lombok.Getter;

@Getter
public enum SchedulerAction {

  CUSTOM_REMINDER_CUSTOM_START(
      "customReminder_customStart",
      WorkflowConstants.NEW_CUSTOM_START,
      Map.of(WorkflowConstants.DEFAULT_KEY, WorkflowConstants.CUSTOM_REMINDER_START_DUZZIT),
      TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2),
  CUSTOM_REMINDER_CUSTOM_WAIT(
      "customReminder_customWait",
      WorkflowConstants.CUSTOM_WAIT_EVENT,
      Map.of(WorkflowConstants.DEFAULT_KEY, WorkflowConstants.CUSTOM_REMINDER_WAIT_DUZZIT),
      TriggerTargetAPI.TRIGGER_V2),
  CUSTOM_REMINDER_CUSTOM_RECUR(
      "customReminder_customRecur",
      WorkflowConstants.CUSTOM_RECUR_EVENT,
      Map.of(WorkflowConstants.DEFAULT_KEY, WorkflowConstants.CUSTOM_REMINDER_RECUR_DUZZIT),
      TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2),
  CUSTOM_SCHEDULEDACTIONS_CUSTOM_START(
      "customScheduledActions_customStart", WorkflowConstants.ENTITY_CHANGE_TYPE_CREATED, Map.of(),
      TriggerTargetAPI.TRIGGER_V2),
  CUSTOM_UPDATE_ENTITY_CUSTOM_START(
      "customUpdateEntity_customStart",
      WorkflowConstants.NEW_CUSTOM_START,
      Map.of(WorkflowConstants.DEFAULT_KEY, WorkflowConstants.CUSTOM_REMINDER_START_DUZZIT),
      TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2),
  CUSTOM_SEND_ENTITY_CUSTOM_START(
      "customSendEntity_customStart",
      WorkflowConstants.NEW_CUSTOM_START,
      Map.of(WorkflowConstants.DEFAULT_KEY, WorkflowConstants.CUSTOM_REMINDER_START_DUZZIT),
      TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2);

  private final String action;
  private final String entityChangeType;
  // This map contains the recordType's connectorId; the defaultKey is used for all recordTypes; if
  // you want to specify a connectorId specific to a recordType, add a new value to this map.
  private final Map<String, String> recordTypeConnectorIdMap;
  private final TriggerTargetAPI triggerTargetAPI;

  SchedulerAction(
      String action, String entityChangeType, Map<String, String> recordTypeConnectorIdMap, TriggerTargetAPI triggerTargetAPI) {
    this.action = action;
    this.entityChangeType = entityChangeType;
    this.recordTypeConnectorIdMap = recordTypeConnectorIdMap;
    this.triggerTargetAPI = triggerTargetAPI;
  }

  @Override
  public String toString() {
    return action;
  }

  public static SchedulerAction fromAction(String action) {
    for (SchedulerAction schedulerAction : SchedulerAction.values()) {
      if (schedulerAction.getAction().equals(action)) {
        return schedulerAction;
      }
    }
    throw new UnsupportedOperationException("The action " + action + " is not supported!");
  }

  /**
   * This method returns the connectorId for the given recordType, if recordType does not exists
   * then return the default value
   *
   * @param recordType
   * @return
   */
  public String getConnectorId(RecordType recordType) {
    return recordTypeConnectorIdMap.getOrDefault(
        recordType.getRecordType(), recordTypeConnectorIdMap.get(WorkflowConstants.DEFAULT_KEY));
  }
}
