package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum Stores the configuration which is returned to the front end for String related parameters.
 */
@Getter
@RequiredArgsConstructor
public enum StringOperator {
  /**
   * WITHIN: absolute check to see if a given attribute is equal to a given value
   * NOT_WITHIN: absolute check to see if a given attribute is not equal to a given value
   *
   * WITHIN internally uses the java String.equals() method
   */
  WITHIN("CONTAINS", "Within"),
  NOT_WITHIN("NOT_CONTAINS", "Not Within");

  private final String symbol;
  private final String description;

  public static Map<String, String> possibleOperatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (StringOperator s : values()) {
      possibleOperatorValueMap.put(s.symbol, s.description);
    }
    return possibleOperatorValueMap;
  }
}
