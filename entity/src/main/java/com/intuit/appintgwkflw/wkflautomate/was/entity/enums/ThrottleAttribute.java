package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum ThrottleAttribute {
    EXTERNAL_TASKS_PER_ACTIVITY("externalTasksPerActivity"),
    DEFINITIONS_PER_WORKFLOW_PER_REALM("definitionsPerWorkflowPerRealm"),
    DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME("definitionsPerWorkflowInTimeframe");

    private String throttleAttribute;

    ThrottleAttribute(String attribute){
        this.throttleAttribute = attribute;
    }

    @Override
    public String toString(){ return throttleAttribute; }
}
