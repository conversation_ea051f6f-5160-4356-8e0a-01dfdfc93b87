package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum TriggerType {
  STARTPROCESS("startprocess"),
  WFTASK("wftask"),
  BOUNDARYEVENT("boundaryevent"),
  SKIPTASK("skiptask");

  private String triggerType;

  TriggerType(String triggerType) {
    this.triggerType = triggerType;
  }

  @Override
  public String toString() {
    return triggerType;
  }

  public static TriggerType fromType(String type) {
    for (TriggerType triggerType : TriggerType.values()) {
      if (triggerType.getTriggerType().equals(type)) {
        return triggerType;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }
}
