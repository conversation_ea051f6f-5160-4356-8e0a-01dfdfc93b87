package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum EventEntityType {
  EXTERNALTASK("externalTask", MetricName.EVENT_EXTERNAL_TASK),
  EXTERNALTASKTEST("externalTaskTest", MetricName.EVENT_EXTERNAL_TASK_TEST),
  EXTERNALTASKREST("externalTaskRest", MetricName.EVENT_EXTERNAL_TASK_REST),
  TRIGGER("trigger", MetricName.EVENT_TRIGGER),
  INCIDENT("incident", MetricName.INTERNAL_CAMUNDA_CONSUMER_INCIDENT),
  SERVICE_TASK(WorkflowConstants.SERVICE_TASK, MetricName.INTERNAL_CAMUNDA_SERVICE_TASK_EVENT),
  WORKFLOW_TRANSITION_EVENTS(WorkflowConstants.WORKFLOW_TRANSITION_EVENTS, MetricName.EVENT_WORKFLOW_TRANSITION_EVENT),
  WORKFLOW_DEFINITION(WorkflowConstants.WORKFLOW_DEFINITION, MetricName.EVENT_WORKFLOW_DEFINITION),
  DEFINITION_EVENT("definitionEvent", MetricName.DEFINITION_EVENT),
  EXTERNAL_TASK_PUSH("externalTaskPush", MetricName.EVENT_EXTERNAL_PUSH_TASK),
  SCHEDULING("scheduling", MetricName.SCHEDULING_EVENT);

  private String entityType;
  private MetricName metricName;

  private static Map<String, EventEntityType> reverseEventEntityMapping =
      addEventEntityTypeMapping();

  public static Map<String, EventEntityType> addEventEntityTypeMapping() {
    Map<String, EventEntityType> entityTypeMap = new HashMap<>();
    Arrays.stream(EventEntityType.values())
        .forEach(entity -> entityTypeMap.putIfAbsent(entity.getEntityType(), entity));
    return entityTypeMap;
  }

  public static EventEntityType valueOfEntity(String entityType) {
    return reverseEventEntityMapping.get(entityType);
  }
  public MetricName geMetricName() {
    return this.metricName;
  }
}
