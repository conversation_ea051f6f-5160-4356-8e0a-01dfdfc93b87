package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR> Contract class for communication between EventEntity and Workers. This is the
 * Object that will be send to EventPublisherCapability
 */

@Getter
@Builder
@ToString
public class EventHeaderEntity {
  private final String idempotencyKey;
  private final String targetAssetAlias;
  private final String entityId;
  private final PublishEventType publishEventType;
  private final EventEntityType eventEntityType;
  private final String offeringId;
  private final String tid;
  private final String ownerId;
  private final String handlerId;
  private final String intuitUserId;
}
