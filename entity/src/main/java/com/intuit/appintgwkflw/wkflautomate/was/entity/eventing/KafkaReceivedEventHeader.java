package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Cast Eventing headers to this class to avoid logging any PII
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KafkaReceivedEventHeader {
  
  @RedactSensitiveField
  private String kafka_acknowledgment;
  @JsonAnySetter
  private Map<String, Object> additionalModelAttributes = new HashMap<>();
}
