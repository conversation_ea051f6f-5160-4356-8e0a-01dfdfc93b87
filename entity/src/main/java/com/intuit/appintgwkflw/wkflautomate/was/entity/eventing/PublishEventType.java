package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.Getter;

/** <AUTHOR> Type of PublishEventType. It can be externalTask, processStart etc
 *  <AUTHOR> handler scope for testing the vep flow
 * */
@Getter
public enum PublishEventType {

  EXTERNAL_TASK(WorkflowConstants.EXTERNAL_TASK, WorkflowConstants.EXTERNAL_TASK, HandlerScope.TOPIC_NAME),
  EXTERNAL_TASK_TEST(WorkflowConstants.EXTERNAL_TASK, "externalTaskTest", HandlerScope.TEST_TOPIC_NAME),
  INCIDENT("incident" , "incident", HandlerScope.TOPIC_NAME),
  SERVICE_TASK(WorkflowConstants.SERVICE_TASK, WorkflowConstants.SERVICE_TASK, HandlerScope.TOPIC_NAME),
  WORKFLOW_DEFINITION(WorkflowConstants.WORKFLOW_DEFINITION, WorkflowConstants.WORKFLOW_DEFINITION, HandlerScope.TOPIC_NAME),
  SERVICE_TASK_TEST(WorkflowConstants.SERVICE_TASK, WorkflowConstants.SERVICE_TASK_TEST, HandlerScope.TEST_TOPIC_NAME),
  WORKFLOW_TRANSITION_EVENTS(WorkflowConstants.WORKFLOW_TRANSITION_EVENTS , WorkflowConstants.WORKFLOW_TRANSITION_EVENTS, HandlerScope.TOPIC_NAME),
  WORKFLOW_TRANSITION_EVENTS_TEST(WorkflowConstants.WORKFLOW_TRANSITION_EVENTS , WorkflowConstants.WORKFLOW_TRANSITION_EVENTS_TEST, HandlerScope.TEST_TOPIC_NAME),
  TRIGGER(WorkflowConstants.TRIGGER, WorkflowConstants.TRIGGER, HandlerScope.TOPIC_NAME);
  private String type;
  private String name;
  private HandlerScope handlerScope;

  /**
   *
   * @param type type of publish event (same for all handlerScope)
   * @param name name of the topic
   * @param handlerScope scope of the event (test or default)
   */
  PublishEventType(String type, String name, HandlerScope handlerScope) {
    this.type = type;
    this.name = name;
    this.handlerScope = handlerScope;
  }

  /**
   *
   * @return key of publishEventMapping : publishEventType+HandlerScopeName
   */
  private String getKeyMapper(){
    return this.getType() + this.getHandlerScope().getName();
  }

  private static final Map<String, PublishEventType> publishEventMapping =
          Arrays.stream(PublishEventType.values()).
                  collect(Collectors.toMap(PublishEventType::getKeyMapper, Function.identity()));

  /**
   *
   *  @param publishEventType type of publish event (same for all handlerScope)
   * @param handlerScope scope of the event (test or default)
   *                     Set handlerScope to default if null
   * @return Publish event based on handler scope
   */
  public static PublishEventType getPublishEventType(PublishEventType publishEventType, String handlerScope) {
    return publishEventMapping.get(publishEventType.getType()
        + Optional.ofNullable(handlerScope).orElse(HandlerScope.TOPIC_NAME.getName()));
  }

  @Getter
  public enum HandlerScope {
    TOPIC_NAME("default"),
    TEST_TOPIC_NAME("test");
    private String name;

    /**
     *
     * @param name Name of the handler scope (default or test)
     */
    HandlerScope(String name) {
      this.name = name;
    }

  }

}
