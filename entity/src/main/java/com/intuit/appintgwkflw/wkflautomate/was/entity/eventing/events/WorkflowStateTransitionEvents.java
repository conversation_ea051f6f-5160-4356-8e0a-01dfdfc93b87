package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** <AUTHOR> */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowStateTransitionEvents {
  private String businessEntityId;
  private String businessEntityType;
  private String activityType;
  private WorkflowMetaData workflowMetadata;
  private WorkflowMetaData migratedFrom;
  private ActivityMetaData activityMetadata;
  private String eventType;
  private long timestamp;
  private String status;
  //Downstream Id.
  private String txnId;
  // Unique id for Camunda task executions.
  private String executionId;
  // Unique id for Camunda activity elements. Id is unique for looping constructs as well
  private String activityInstanceId;
}
