package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask;

import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * This is a POJO for External Task which camunda publish
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class ExternalTaskEvent {
	private String activityId;
	private String activityInstanceId;
	private String errorMessage;
	private String errorDetails;
	private String executionId;
	private String id;
	private Date lockExpirationTime;
	private String processDefinitionId;
	private String processDefinitionKey;
	private String processDefinitionVersionTag;
	private String processInstanceId;
	private Integer retries;
	private String workerId;
	private String topicName;
	private String tenantId;
	private long priority;
	/**
	 * here variables are the variables passed from camunda in string, object format without its type
	 */
	private Map<String, Object> variables;
	private String businessKey;
	private Map<String, String> extensionProperties;
	/**
	 * here variable map is the instance of VariableMap, which consist of variable along with their type
	 */
	private Map<String, Object> variableMap;

}
