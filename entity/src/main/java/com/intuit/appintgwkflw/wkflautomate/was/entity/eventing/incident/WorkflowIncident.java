package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.incident;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> WorkflowIncident will be part of all publishers who are publishing to Event
 * bus. It uses WorkflowMetaDataEntity Currently this POJO is written manually, later after IEDM
 * onboarding the schema will be moved to IEDM. These POJO will be automatially gennerated and can
 * be deleted from this folder
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowIncident {

  private WorkflowMetaData workflowMetadata;
  private String activityName;
  private String activityId;
  private String incidentMsg;
  private String businessEntityType;
  private String businessEntityId;
  @RedactSensitiveField
  private Map<String, Object> variables;
  private String externalTaskId;
  private String executionId;
}
