package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger;

import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Trigger object for events
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
public class Trigger {
  private MetaData metaData;
  private Variables variables;
  @RedactSensitiveField
  private Map<String, Object> entity;
}
