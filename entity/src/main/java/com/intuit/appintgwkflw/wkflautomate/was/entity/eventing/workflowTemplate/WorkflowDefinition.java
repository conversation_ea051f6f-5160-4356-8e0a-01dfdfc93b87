package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.workflowTemplate;

import lombok.Builder;
import lombok.Getter;

/**
 * WorkflowDefinition class with details of newly added/updated workflow definitions(templates).
 * Containing definition key, name, version and type. Also includes the businessEntityType.
 * */

@Getter
@Builder
public class WorkflowDefinition {
    private String businessEntityType;
    private String definitionKey;
    private String definitionName;
    private int definitionVersion;
    private String definitionType;
}
