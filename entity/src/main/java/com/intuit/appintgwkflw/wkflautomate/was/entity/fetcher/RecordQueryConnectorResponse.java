package com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;

/** <AUTHOR> */

/** This class holds the response data received after querying the connectors */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RecordQueryConnectorResponse {
  private String success;
  private String error;

  @JsonProperty("Output")
  private List<Map<String, String>> recordList;
}
