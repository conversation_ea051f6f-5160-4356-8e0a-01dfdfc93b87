package com.intuit.appintgwkflw.wkflautomate.was.entity.graphql;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Filter {

  private String key;
  private List<Object> value;
  private String operator;

  public static Filter generateFilterObj(String key, List<Object> value, String operator) {
    Filter filterObj = new Filter();
    filterObj.setKey(key);
    filterObj.setValue(value);
    filterObj.setOperator(operator);
    return filterObj;
  }
}

