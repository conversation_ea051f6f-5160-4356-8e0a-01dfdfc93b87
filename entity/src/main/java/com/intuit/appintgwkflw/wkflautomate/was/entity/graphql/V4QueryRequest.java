package com.intuit.appintgwkflw.wkflautomate.was.entity.graphql;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@Setter
public class V4QueryRequest {
  private String type;
  private List<Filter> whereFilters;
  private List<Filter> withFilters;

}

