package com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request;

import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * <PERSON>ra<PERSON>ield
 */
@Getter
@Setter
@Builder
public class JiraField {

  private Project project;
  private String summary;
  private String description;
  private IssueType issuetype;
  private List<Components> components;
  private List<String> labels;
  private List<FieldValueMap> customfield_11801;
  private FieldValueMap customfield_17002;
  private FieldValueMap customfield_13505;
  private String customfield_13504;
}
