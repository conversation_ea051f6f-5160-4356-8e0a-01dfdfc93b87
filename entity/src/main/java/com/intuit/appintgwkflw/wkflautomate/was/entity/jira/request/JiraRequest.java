package com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request;

import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Jira request
 */
@Builder
@Getter
@Setter
public class JiraRequest {

  private String project;
  private String summary;
  private String description;
  private String issueType;
  private String component;
  private List<String> label;
  private String customfield_11801; // region
  private String customfield_17002;  //
  private String customfield_13505; //
  private String customfield_13504; // due date
}
