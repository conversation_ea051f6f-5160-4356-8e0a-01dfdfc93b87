package com.intuit.appintgwkflw.wkflautomate.was.entity.logger;

/**
 * <AUTHOR>
 */
public enum DownstreamServiceName {
  WORKFLOW_TASK_HANDLER,
  CAMUNDA_EXTERNAL_TASK_COMPLETE,
  CAMUNDA_EXTERNAL_TASK_FAILURE,
  TEMPLATE_DETAILS,
  WAS_GLOBAL_ERROR,
  CAMUNDA_EVALUATE_RULES,
  WAS_PRE_CANNED_TEMPLATE,
  CAMUNDA_EXTERNAL_TASK,
  WAS_EXECUTION_CHAIN,
  UPDATE_PROCESSS_STATUS,
  UPDATE_DEFINITION_STATUS,
  CAMUNDA_TRIGGER_PROCESS,
  CAMUNDA_START_PROCESS,
  CAMUNDA_DELETE_DEFINITION,
  WAS_DB_GET_DEFINITIONS,
  WAS_DB_GET_PROCESS,
  WAS_DB_DELETE_DEFINITION,
  WAS_DB_UPDATE_DEFINITION,
  WAS_DB_NO_MULTIPLE_DEFINITIONS,
  WAS_V3_PAYLOAD_PROCESS,
  WAS_GET_TEMPALTE_PROPERTIES,
  SAVE_AUTH_DETAILS,
  POPULATE_AUTH_DETAILS,
  CREATE_REALM_OFFLINE_TICKET,
  RENEW_REALM_OFFLINE_TICKET,
  REALM_OFFLINE_TICKET_READER,
  REALM_OFFLINE_TICKET_WRITE_LISTENER,
  REALM_OFFLINE_TICKET_PROCESSOR,
  REALM_OFFLINE_TICKET_REFRESH_JOB,
  USER_NOT_REALM_MEMBER,
  INVALID_IAM_TICKET,
  TICKET_VERIFICATION_FAILED,
  WAS_TRIGGER_RULE_HANDLER,
  GET_SUBSCRIPTION_DETAILS,
  CREATE_SUBSCRIPTION_DETAILS,
  DELETE_WORKFLOW_APPCONNECT,
  DISABLE_WORKFLOW_APPCONNECT,
  DISABLE_DELETE_WORKFLOW,
  WAS_DELETE_DEFINITION,
  WAS_UPDATE_DEFINITION,
  DATE_UTILS,
  WAS_DISABLE_DEFINITION,
  WAS_ENABLE_DEFINITION,
  WAS_DELETE_AUTH_DETAILS,
  WAS_MIGRATE_DEFINITION,
  WAS_DOWNGRADE,
  UNSUBSCRIBE_APPCONNECT,
  WORKER_EXECUTOR,
  WAS_GET_AUTH_DETAILS,
  WAS_DEFINITION_FILTER_TYPE,
  WAS_TEMPLATE_FILTER_TYPE,
  WAS_DELETE_FILE,
  WAS_USER_DEFINITION_TRIGGER_HANDLER,
  DELETE_DEFINITION,
  WAS_UNSUBSCRIBE,
  CAMUNDA_SIGNAL_PROCESS,
  WAS_EVENT_PROCESSOR,
  APP_CONNECT_WORKFLOW_TASK_HANDLER,
  EVALUATE_DMN,
  WAS_DB_SAVE_TASK_DETAILS,
  WAS_DB_DELETE_TASKS_DEFINITION,
  PROJECT_SERVICE_CREATE_UPDATE_PROJECT,
  TASK_SERVICE_CREATE_UPDATE_TASK,
  IUS_GET_PERSONA_DETAILS,
  IUS_AUTHORIZATION_DELEGATION,
  CLEANUP_DEFINITION,
  DISABLE_DEFINITION,
  UNAUTHORIZED_RESOURCE_ACCESS,
  BATCH_JOB,
  WAS_DB_TASK_MUTATION,
  AUTHZ_CLIENT_INITIALIZATION,
  AUTHZ_CLIENT_REQUEST,
  UPDATE_SCHEDULES_STATUS_ESS,
  UPDATE_SCHEDULING_STATUS ,
  APPCONNECT_DUZZITS,
  WAS_ESS_MESSAGE_PROCESSOR,
  VARIABILITY_SERVICE_QUERY;

}
