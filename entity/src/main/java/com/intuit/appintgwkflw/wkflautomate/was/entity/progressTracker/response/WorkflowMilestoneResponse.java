package com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowMilestoneResponse {

  private String workflowId;
  private String workflowName;
  private String milestoneId;
  private String milestoneName;
  private WorkflowMilestoneStatus status;
  private String createdDate;
  private String updatedDate;
  private String completedDate;
  private List<Attributes> attributes;
  
}
