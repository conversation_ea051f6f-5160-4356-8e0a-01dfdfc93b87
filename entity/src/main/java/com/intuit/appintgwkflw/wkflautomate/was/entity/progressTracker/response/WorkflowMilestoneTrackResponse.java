package com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response;

import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowResponse;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
public class WorkflowMilestoneTrackResponse extends WorkflowResponse {

	private List<WorkflowMilestoneResponse> milestones;
}
