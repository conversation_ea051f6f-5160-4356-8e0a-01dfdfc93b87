package com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII;

import com.fasterxml.jackson.databind.introspect.Annotated;
import com.fasterxml.jackson.databind.introspect.NopAnnotationIntrospector;

/**
 * <AUTHOR>
 * This is an annotation introspector used in conjuction with ReadactsensitiveField annotation.
 * Whenever this annotation is encountered, this processor returns the custom implementation of a serializer
 * which converts the marked field value to a default masked value
 */
public class RedactSensitiveFieldAnnotationIntrospector extends NopAnnotationIntrospector {
  private static final long serialVersionUID = 1L;

  @Override
  public Object findSerializer(Annotated am) {
    RedactSensitiveField annotation = am.getAnnotation(RedactSensitiveField.class);
    if (annotation != null) {
      return new RedactSensitiveFieldCustomSerializer(annotation.value());
    }

    return super.findSerializer(am);
  }
}
