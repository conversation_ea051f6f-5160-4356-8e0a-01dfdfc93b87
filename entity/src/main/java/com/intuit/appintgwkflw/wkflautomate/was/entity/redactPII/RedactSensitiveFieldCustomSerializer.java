package com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

/**
 * <AUTHOR>
 * This is a custom serializer implementation to be used with RedactSensitiveField annotation
 * If a field is marked with the annoattion, then this serializer returns a default mask value
 */
public class RedactSensitiveFieldCustomSerializer extends StdSerializer<Object> {

  private String maskValue;

  protected RedactSensitiveFieldCustomSerializer() {
    super(Object.class);
  }

  protected RedactSensitiveFieldCustomSerializer(String maskValue) {
    this();
    this.maskValue = maskValue;
  }

  //Whenever a field with annotation RedactSensitiveField is serialized, this serializer will return maskValue
  /*
  Sample ExternalTaskAssigned payload when serialized, variables are assigned default masked value
  {"workflowMetadata":{"workflowOwnerId":"9130352403806696","processInstanceId":"4ab2813c-26ae-11ec-a82c-fa46319d80b3","processDefinitionId":null,"workflowName":"engagementTTLiveFullServiceTestConsumerTest",
  "workflowVersion":null},"taskName":null,"businessEntityType":"engagement","businessEntityId":"cto:a6489789-8b69-7-sab4","variables":"*****"}
   */
  @Override
  public void serialize(Object value, JsonGenerator gen, SerializerProvider provider) throws IOException {
    gen.writeObject(maskValue);
  }
}
