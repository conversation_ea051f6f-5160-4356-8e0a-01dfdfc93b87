package com.intuit.appintgwkflw.wkflautomate.was.entity.request;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR> Request class for domain enttity
 * @param <T>
 */
@Builder
@Data
@Getter
@Setter
@RequiredArgsConstructor
@AllArgsConstructor
public class DomainEntityRequest<T extends Object> {
  private EventHeaderEntity eventHeaderEntity;
  private T request;
  private EntityChangeAction entityChangeAction;
  private DomainEventErrorDetails domainEventErrorDetails;
  private AdditionalDetails additionalDetails;
}
