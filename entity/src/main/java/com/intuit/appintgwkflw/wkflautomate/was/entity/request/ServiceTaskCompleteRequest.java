package com.intuit.appintgwkflw.wkflautomate.was.entity.request;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder;

/**
 * Service task Complete Request Entity
 * Author: ragarwal7
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public final class ServiceTaskCompleteRequest {

    private String executionId;
    private Map<String, Object> payload;
    private boolean failed;
    private String failureMessage;
}
