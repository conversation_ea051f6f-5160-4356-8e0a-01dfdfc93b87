package com.intuit.appintgwkflw.wkflautomate.was.entity.request;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Permission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WasAuthorizeRequest {
  @NonNull private Long workflowOwnerId;
  @NonNull private Permission permission;
  private Long RequestOwnerId;
  private String onBehalfOf;
  private String assignee;
  private Long taskOwnerId;
  private String domain;
  private String taskType;
  private String taskId;
}
