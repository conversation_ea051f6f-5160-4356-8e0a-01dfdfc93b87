package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Setter
public class ApprovalDetailResponse {
    private String id;
    private String requestedApproverId;
    private String approverId;
    private String status;
    private Date createdDate;
    private Date approvalDate;
}
