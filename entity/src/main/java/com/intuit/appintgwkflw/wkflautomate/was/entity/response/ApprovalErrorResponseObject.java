package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalErrorResponseObject {

    private String status;
    private Object data;
    private ErrorDetails errorDetails;

    // getters and setters

    @Getter
    @Setter
    public static class ErrorDetails {
        private String errorCode;
        private String errorMessage;
        private String errorDescription;

        // getters and setters
    }

}
