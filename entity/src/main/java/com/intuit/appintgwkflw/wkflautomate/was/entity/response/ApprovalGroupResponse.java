package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Setter
public class ApprovalGroupResponse {
    private String id;
    private String status;
    private String entityType;
    private String entityId;
    @JsonProperty("approvalRequest")
    private ApprovalRequestResponse approvalRequestResponse;
    private Date createdDate;
    private Date approvalDate;

    public ApprovalGroupResponse(Map<String, Object> result){

    }

}
