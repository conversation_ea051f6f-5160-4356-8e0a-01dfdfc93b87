package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalRequestResponse {
    String id;
    String status;
    String type;
    @JsonProperty("approvalDetails")
    List<ApprovalDetailResponse> approvalDetailResponse;
    private Date createdDate;
    private Date approvalDate;

}
