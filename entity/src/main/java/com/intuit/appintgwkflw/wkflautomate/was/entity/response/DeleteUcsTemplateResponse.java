package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> <br>
 *     </>POJ<PERSON> for Bulk Delete API from UCS
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DeleteUcsTemplateResponse {

  /** id of the deleted template */
  private String id;

  /** An alphanumeric code that denotes a success or error state, i.e. s9200 or e9404 */
  private String statusCode;

  /** True/False status, if the mutation was successful */
  private boolean success;

  /** A user-facing message for display when 'success' is not true */
  private String message;
}
