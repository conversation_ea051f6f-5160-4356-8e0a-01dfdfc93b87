package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExternalTaskDetail extends WorkflowResponse {

	private String activityId;
	private String errorMessage;
	private String executionId;
	private String id;
	private String processDefinitionId;
	private String processInstanceId;
	private Integer retries;

}
