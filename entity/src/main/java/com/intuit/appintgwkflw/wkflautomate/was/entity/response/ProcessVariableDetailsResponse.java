package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessVariableDetailsResponse {
	private String type;
	private Object value;
	private Map<String, Object> valueInfo;
	private String id;
	private String name;
	private String processDefinitionKey;
	private String processDefinitionId;
	private String processInstanceId;
	private String executionId;
	private String activityInstanceId;
	private String caseDefinitionKey;
	private String caseDefinitionId;
	private String caseInstanceId;
	private String caseExecutionId;
	private String taskId;
	private String errorMessage;
	private String tenantId;
	private String state;
	private Date createTime;
	private Date removalTime;
	private String rootProcessInstanceId;
}
