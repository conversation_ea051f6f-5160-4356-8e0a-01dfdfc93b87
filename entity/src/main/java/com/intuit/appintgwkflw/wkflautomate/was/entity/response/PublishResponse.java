package com.intuit.appintgwkflw.wkflautomate.was.entity.response;


import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder(toBuilder = true)
@Getter
public class PublishResponse extends WorkflowResponse{

  private final Map<String, String> actionTidMap;
  
  private final Map<String, String> triggerDetails;

}
