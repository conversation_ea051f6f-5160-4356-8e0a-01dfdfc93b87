package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/** <AUTHOR> */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowGenericResponse {

  // setting default to success
  @Builder.Default private ResponseStatus status = ResponseStatus.SUCCESS;

  private WorkflowResponse response;

  @JsonProperty("error")
  private WorkflowErrorDetails errorDetails;
}
