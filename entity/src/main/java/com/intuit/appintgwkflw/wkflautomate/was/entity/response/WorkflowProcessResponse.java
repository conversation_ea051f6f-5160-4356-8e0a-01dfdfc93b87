package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class WorkflowProcessResponse  {

    private String processId;
    private String recordId;
    private ProcessStatus processStatus;
    private Timestamp createdDate;
    private Timestamp modifiedDate;
}
