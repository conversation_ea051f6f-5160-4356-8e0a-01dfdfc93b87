package com.intuit.appintgwkflw.wkflautomate.was.entity.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;

/** <AUTHOR> */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder(toBuilder = true)
@Getter
public class WorkflowTriggerResponse extends WorkflowResponse {

  private String definitionId;
  private String definitionName;
  private String processId;
  private TriggerStatus status;
}
