package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Persona {
  
  @JsonProperty(value = "personaId")
  public String personaId;

  private String userId;

  private Email email;

}