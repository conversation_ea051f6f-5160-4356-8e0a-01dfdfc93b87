package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
public class CorrelateAllMessage {

  private String messageName;
  private String businessKey;
  private final boolean all = true;
  // returns the result of all the signalled process instance by this message
  private final boolean resultEnabled = true;
  private Map<String, CorrelateKey> correlationKeys;

  @RequiredArgsConstructor
  @Getter
  public static class CorrelateKey {

    private final String value;
    private final String type;
  }

}
