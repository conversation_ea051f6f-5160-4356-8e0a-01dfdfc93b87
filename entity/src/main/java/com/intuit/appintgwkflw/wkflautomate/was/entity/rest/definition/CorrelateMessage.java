package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import java.util.Map;

import lombok.Builder;
import lombok.Getter;

/** Message payload to signal the waiting process */
@Builder
@Getter
public class CorrelateMessage {
  private String messageName;
  private String processInstanceId;
  private Map<String, Object> processVariables;
  private Map<String, Object> processVariablesLocal;
}
