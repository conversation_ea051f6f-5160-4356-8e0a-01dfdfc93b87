package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import lombok.Builder;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Class representing the result of evaluated decision from Camunda.
 * @See https://docs.camunda.org/manual/7.15/reference/rest/decision-definition/post-evaluate/#result
 */
@Builder
@Getter
public class EvaluateRuleResponse {

  private String type;

  @Builder.Default
  private Map<String, Object> valueInfo = new HashMap<>();

  private Object value;
}