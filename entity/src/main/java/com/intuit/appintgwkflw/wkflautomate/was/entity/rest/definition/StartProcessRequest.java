package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/** Start process request payload */
@Data
@AllArgsConstructor
public class StartProcessRequest {
  private String definitionId;
  private String businessKey;
  private Map<String, Object> variables;
  // This variable indicates <PERSON>unda to send the list of all initialized variables.
  private final Boolean withVariablesInReturn = true;
}
