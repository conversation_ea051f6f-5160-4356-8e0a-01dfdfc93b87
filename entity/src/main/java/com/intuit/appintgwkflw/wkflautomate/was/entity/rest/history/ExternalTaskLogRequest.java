package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class ExternalTaskLogRequest {

  private String externalTaskId;
  private String processInstanceId;
  private List activityIdIn;
  private boolean creationLog;
  private int firstResult;
  private int maxResults;

}