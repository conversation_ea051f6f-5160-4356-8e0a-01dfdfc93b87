package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public  class ProcessVariableDetailsRequest {

	private  int firstResult;
	private  int maxResults;
	private  boolean deserializeValues;
	private  String processInstanceId;
	private  String processDefinitionId;
	private  String processDefinitionKey;
	private  String caseInstanceId;
	private  String variableName;
	private  String variableNameLike;
	private  Object variableValue;
	private  Boolean variableValuesIgnoreCase;
	private  Boolean variableNamesIgnoreCase;
	private  String[] variableTypeIn;
	private  String[] executionIdIn;
	private  String[] taskIdIn;
	private  String[] activityInstanceIdIn;
	private  String[] caseExecutionIdIn;
	private  String[] caseActivityIdIn;
	private  String[] processInstanceIdIn;
	private  List<String> tenantIds;
	private  Boolean withoutTenantId;
	private  boolean includeDeleted;
	private  List<ProcessVariableSortCriteria> sorting;
}
