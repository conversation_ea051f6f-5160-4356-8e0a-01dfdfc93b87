package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TriggerNowRequest {

	private String definitionId;

	public TriggerNowRequest(String definitionId) {
		this.definitionId = definitionId;
	}

	/**
	 * Scope of the event: The application can identify the scope of the event based on the scope.
	 * Currently, it includes a single scope, "test"
	 * The reason for having a scope is to identify the source of the event.
	 * If the source is from overwatch, we should pass the scope as "test".
	 * Because in overwatch automations, we have different templates deployed in camunda by using TemplateMetaData.getForkForOverwtach() as true.
	 * So, we need to identify the source of the event.
	 *
	 * The reason for having 2 templates for same workflow is to have different configurations for the same workflow.
	 * The topic in overwatch automation is different from the topic in normal automation.
	 * The reason for having different topics is to have different configurations for the same workflow can be found under java doc of TemplateMetaData.getForkForOverwtach()
	 *
	 * Make sure we have a test overwatch template deployed in camunda using forkForOverwatch as true before sending the event with scope as TEST.
	 * Or else the test flow will be breaked.
	 *
	 * For now use this scope only for overwatch purposes and use for only approval and reminder workflows.
	 *
	 * The allowed types are present in Scope.class.getName()
	 */
	private String scope = null;
}
