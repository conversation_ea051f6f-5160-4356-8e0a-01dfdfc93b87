package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * Input Entity getting passed tto SingledefMigrationController.
 */
@Data
public class SingleDefInputMigration {
//The list of definitions for which migration will be performed
  private List<String> bpmnDefinitionIds;
// Destination templateId to be migrated
  private String updatedTemplateId;
// Flag to see, if Offline-Ticket Refresh is needed for users RealmId. Realm Offline ticket should not fail because of rollback.
  private boolean offlineTicketRefresh;

}
