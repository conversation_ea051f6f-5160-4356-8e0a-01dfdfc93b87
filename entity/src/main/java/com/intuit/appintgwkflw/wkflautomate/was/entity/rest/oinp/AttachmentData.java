package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Class used for storing Document meta data passed in along with the event
 * payload
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentData {

  private List<Document> documents;

  @Getter
  @Setter
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Document {

    private String source; //can be defaulted to "documentService", as only this is supported in OINP for now.
    // If optionalDocument is true then if retrieval of the document fails, we will
    // log the failure but still finish sending the event, this field is optional
    // and will default to false
    private boolean optionalDocument;

    @RedactSensitiveField
    private DocumentMetaData documentMetaData;

  }

  @Builder
  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  public static class DocumentMetaData {
    String documentId;
    String sourceKey;
  }
}
