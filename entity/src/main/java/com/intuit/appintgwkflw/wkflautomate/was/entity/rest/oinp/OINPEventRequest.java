package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@Builder
@Setter
@Getter
public class OINPEventRequest {
  /**
   * event name.
   */
  private String name;
  /**
   * source service name.
   */
  private String sourceServiceName;
  /**
   * source object id.
   */
  private String sourceObjectId;
  /**
   * source object type.
   */
  private String sourceObjectType;
  /**
   * event data.
   */
  @RedactSensitiveField
  private Map<String, Object> eventData;
  /**
   * event meta date.
   */
  private EventMetaData eventMetaData;
  /**
   * Attachment Data
   */
  private AttachmentData attachmentData;
  /**
   * masked fields.
   */
  private String maskedFields;

}
