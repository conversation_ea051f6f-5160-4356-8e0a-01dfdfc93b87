package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums;

/**
 * Represents the end type of the schedule
 * <p>
 * NO_END_DATE: The schedule has no end date
 * END_AFTER_OCCURRENCES: The schedule ends after a certain number of occurrences
 * END_BY_DATE: The schedule ends by a certain date
 * </p>
 *
 * <AUTHOR>
 */
public enum EndType {
    NO_END_DATE,
    END_AFTER_OCCURRENCES,
    END_BY_DATE;
}
