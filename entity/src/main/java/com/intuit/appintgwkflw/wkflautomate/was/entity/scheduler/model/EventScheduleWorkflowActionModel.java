package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model;

import org.joda.time.LocalDate;

import com.intuit.v4.common.RecurrenceRule;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class EventScheduleWorkflowActionModel {

  private String actionName;
  private LocalDate startDate;
  private LocalDate endDate;
  private RecurrenceRule recurrenceRule;
  
  // adding existing constructor with 3 fields to avoid code refractoring
  public EventScheduleWorkflowActionModel(String actionName,LocalDate startDate,RecurrenceRule recurrenceRule) {
	  this.actionName = actionName;
	  this.startDate = startDate;
	  this.recurrenceRule = recurrenceRule;
  }
}
