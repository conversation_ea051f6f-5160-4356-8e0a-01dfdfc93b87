package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;


import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.EndType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.RecurrenceType;

import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.WeekOfMonthEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * This class is used to encapsulate the details required to define a schedule.
 *
 * <p>Schema:</p>
 * <pre>
 * {
 *   "recurrenceType": "DAILY",
 *   "zoneId": "Asia/Kolkata",
 *   "on": "2024-07-31T07:26:18.513701",
 *   "startDate": "2024-07-31",
 *   "time": "07:26:18.513701",
 *   "endType": "END_BY_DATE",
 *   "endDate": "2024-08-31T07:26:18.513701",
 *   "interval": 1,
 *   "occurrences": 10,
 *   "dayOfWeek": "MONDAY",
 *   "dayOfMonth": 15,
 *   "monthOfYear": 6,
 *   "weekOfMonth": "FIRST"
 * }
 * </pre>
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleInfo {
    private RecurrenceType recurrenceType;
    private ZoneId zoneId;
    private LocalDateTime on;
    private LocalDate startDate;
    private LocalTime time;
    private EndType endType;
    private LocalDateTime endDateTime;
    private int interval;
    private Integer occurrences;
    private DayOfWeekEnum dayOfWeek;
    private List<DayOfWeekEnum> daysOfWeek;
    private Integer dayOfMonth;
    private List<Integer> daysOfMonth;
    private Integer monthOfYear;
    private List<Integer> monthsOfYear;
    private WeekOfMonthEnum weekOfMonth;
    private List<WeekOfMonthEnum> weeksOfMonth;
    @Builder.Default private boolean rrule = true;
}
