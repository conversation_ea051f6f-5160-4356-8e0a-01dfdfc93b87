package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.v4.common.RecurrenceRule;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;


/** <AUTHOR> */
@Getter
@Setter
@Builder
public class SchedulingMetaData {

    private String definitionKey;
    private String definitionId;
    private Status status;
    private String workflowName;
    private RecurrenceRule recurrenceRule;
}
