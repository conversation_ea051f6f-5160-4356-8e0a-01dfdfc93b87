package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EventScheduleError {
  private String code;
  private String type;
  private String message;
  private String detail;
}
