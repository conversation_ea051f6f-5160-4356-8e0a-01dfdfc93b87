package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * Represents the error response from the scheduling service.
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SchedulingError {
    private String errorCode;
    private String message;
    private String statusCode;
    private String statusName;
}