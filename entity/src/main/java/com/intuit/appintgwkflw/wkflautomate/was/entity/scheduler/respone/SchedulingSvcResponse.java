package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import lombok.Data;

import java.time.LocalDateTime;

/** <AUTHOR> */
@Data
public class SchedulingSvcResponse {
    private String scheduleDefinitionId;
    private String referenceId;
    private String type;
    private String useCase;
    private Status status;
    private String metadata;
    private Integer priority;
    private ScheduleInfo scheduleInfo;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdByUser;
    private String modifiedByUser;
    private String offering;
    private Integer version;
    private String ownerId;
}
