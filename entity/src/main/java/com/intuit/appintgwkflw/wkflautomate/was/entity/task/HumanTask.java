package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder.Default;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Modeling attributes for Work Item
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Slf4j
public class HumanTask extends Task {

  // user id to which this task will be assigned
  private String assigneeId;

  // use to calculate due date
  @Default
  private Integer estimate = 1;

  // default to [Todays date + estimate]
  // Expected Date in yyyy-MM-dd format.
  private String dueDate;

  // on behalf of a customer or user
  private String customerId;
  
  //EngagementId associated with workflow. 
  private String engagementId;
 
  // Template id to be used for task creation
  private String templateId;

  // any comments/notes associated with the tasks
  private String comments;

  // priority of a task
  private Integer priority;

  // can be used to group task default to offering id
  private String createdBy;

  // task type to be passed, eg: QB_BANK_DEPOSIT_REMINDER
  private String taskType;

  //Target App - Custom header. Optional Parameter
  private String app;

  //Target Domain - Custom header. Optional Parameter
  private String domain;
  
  //Target Usecase - Custom header. Optional Parameter
  private String usecase;

  //Task Owner - realmId with which task will be created.
  private String ownerId;

  @Default
  private String txnMode = TaskMode.SYNC.name();

  @Default
  private String txnVariable = ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE;
  
  private Boolean project;

  private List<String> references;

  @Override
  public TaskType getType() {
    return TaskType.HUMAN_TASK;
  }

  public String getDueDate() {
    if (!StringUtils.isEmpty(dueDate)) {
      try {
        DateTimeFormatter.ofPattern(ActivityConstants.DUE_DATE_FORMAT).parse(dueDate);
        return dueDate;
      } catch (DateTimeParseException ex) {
    	  //Log and do nothing.
        log.error("DueDate:: {} received is not in expected format of {}",
            dueDate, ActivityConstants.DUE_DATE_FORMAT, ex);
      }
    }
    return LocalDateTime.now().plusDays(estimate)
        .format(DateTimeFormatter.ofPattern(ActivityConstants.DUE_DATE_FORMAT));
  }

  @JsonIgnore
  public boolean isDueDateStrPresent() {
    return !StringUtils.isEmpty(dueDate);
  }
  
  @Override
  public boolean isValid() {
	  return !StringUtils.isEmpty(domain);
  }
  
  
  @Override
  public void addMandatoryParams() {
	  if(!CollectionUtils.isEmpty(this.getTaskAttributes().getModelAttributes()) &&
			  this.getTaskAttributes().getModelAttributes().containsKey(ActivityConstants.ACTIVITY_DOMAIN)) {
		  this.setDomain(this.getTaskAttributes().getModelAttributes().get(ActivityConstants.ACTIVITY_DOMAIN));
	  }
      if (!CollectionUtils.isEmpty(this.getTaskAttributes().getModelAttributes()) &&
              this.getTaskAttributes().getModelAttributes().containsKey(ActivityConstants.ACTIVITY_USECASE)) {
        this.setUsecase(this.getTaskAttributes().getModelAttributes().get(ActivityConstants.ACTIVITY_USECASE));
      }
      if (!CollectionUtils.isEmpty(this.getTaskAttributes().getModelAttributes()) &&
              this.getTaskAttributes().getModelAttributes().containsKey(ActivityConstants.ACTIVITY_PROJECT)) {
        this.setProject(
                Boolean.valueOf(this.getTaskAttributes().getModelAttributes().get(ActivityConstants.ACTIVITY_PROJECT))
        );
      }
  }
  
  public boolean isProject() {
    return Objects.isNull(project) ?
        Boolean.TRUE : project;
  }

}
