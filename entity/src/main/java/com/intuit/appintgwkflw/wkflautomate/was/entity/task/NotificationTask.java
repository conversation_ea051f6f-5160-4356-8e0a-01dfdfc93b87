package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.AttachmentData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.EventMetaData;

import java.io.IOException;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

/**
 * Modeling attributes for OINP notification.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTask extends Task {

  private static final ObjectMapper objectMapper = new ObjectMapper();

  /** name of the notification event.Maps to eventName in OINP. */
  @NonNull private String notificationName;

  /** service name */
  @NonNull private String serviceName;

  /** sourceObjectId Used for idempotency.Default to external task id. */
  @Nullable private String idempotencyKey;

  /** underlying object type this event will be using. */
  @NonNull private String notificationDataType;

  /**
   * Refer {@link EventMetaData} it contains keys like authId,locale,tid,device tokens etc. <br>
   *
   * Example : <br>
   * 	{
   * 	 "authId": "MANDATORY", @NonNull
   * 	 "createdDate": "Default -> system Date UTC",
   *     "intuitTid": "Default -> UUID" ,
   *     "locale" "Default -> en-US"
   *     }
   */
  @NonNull
  @JsonDeserialize(using = EventMetaDataDeserializer.class)
  private EventMetaData notificationMetaData;

  /** Key value event data that can be used in notification template. */
  @JsonDeserialize(using = NotificationDataDeserializer.class)
  @NonNull private Map<String, Object> notificationData;

  /** Refer {@link AttachmentData} */
  @JsonDeserialize(using = AttachmentDataDataDeserializer.class)
  @Nullable private AttachmentData attachment;

  /** any fields that needs to be masked in event data. */
  @Nullable private String maskedFields;

  @Override
  public TaskType getType() {
    return TaskType.NOTIFICATION_TASK;
  }

  /**
   * {@link #notificationData} deserialization
   */
  public static class NotificationDataDeserializer extends JsonDeserializer<Map<String, Object>> {

    @Override
    public Map<String, Object> deserialize(JsonParser jp, DeserializationContext ctxt)
        throws IOException {
      JsonNode node = jp.getCodec().readTree(jp);
      return convertJsonNode(node, new TypeReference<Map<String, Object>>() {});
    }
  }

  /**
   * {@link #notificationMetaData} deserialization
   */
  public static class EventMetaDataDeserializer extends JsonDeserializer<EventMetaData> {

    @Override
    public EventMetaData deserialize(JsonParser jp, DeserializationContext ctxt)
        throws IOException {
      JsonNode node = jp.getCodec().readTree(jp);
      return convertJsonNode(node, new TypeReference<EventMetaData>() {});
    }
  }

  /**
   * {@link #attachment} deserialization
   */
  public static class AttachmentDataDataDeserializer extends JsonDeserializer<AttachmentData> {

    @Override
    public AttachmentData deserialize(JsonParser jp, DeserializationContext ctxt)
        throws IOException {
      JsonNode node = jp.getCodec().readTree(jp);
      return convertJsonNode(node, new TypeReference<AttachmentData>() {});
    }
  }

  /**
   * Deserializes the {@link JsonNode} into given TypeReference<T></T>
   * @param node json object
   * @param typeReference deserialization type
   * @return object of type T
   * @throws JsonProcessingException
   */
  private static <T> T convertJsonNode(JsonNode node, TypeReference<T> typeReference)
      throws JsonProcessingException {
    if (node.isObject()){
      return objectMapper.convertValue(node, typeReference);
    }
    return objectMapper.readValue(node.asText(), typeReference);
  }
}