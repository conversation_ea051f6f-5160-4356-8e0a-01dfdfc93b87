package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder.Default;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.springframework.util.StringUtils;

/**
 * Modeling attributes for any Workflow tasks.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class Task {

  private String activityId;

  //This is externalTaskId
  private String id;

  //TxnId is provided by downstream service.
  private String txnId;

  // This is processInstanceId.
  private String processInstanceId;

  // task name
  private String taskName;

  // name of task
  private String activityName;

  //type of activity
  private String activityType;

  // task description
  private String description;

  // default to system tasks
  @Default
  private TaskType type = TaskType.SYSTEM_TASK;

  /**
   * Status of the task. Any additional status value if required can be set by caller while
   * designing the bpmn or at runtime, default will be created. This is externalTask Status.
   */
  @Default
  private String status = "open";

  //recordId of the workflow
  private String recordId;

  //Contains execution attributes.
  private TaskAttributes taskAttributes;

  //workerId of the task.
  private String workerId;

  /**
   * Defaults to activityName in case not provided.
   *
   * @return String taskName.
   */
  public String getTaskName() {
    if (StringUtils.isEmpty(taskName)) {
      return activityName;
    }
    return taskName;
  }
  
  public boolean isValid() {
	  return true;
  }
  
  public void addMandatoryParams() {
	  /**
	   * To be overridden by individual adaptors in case of Update/Complete/Failed command.
	   * Add task params from ModelAttributes
	   */
  }

}