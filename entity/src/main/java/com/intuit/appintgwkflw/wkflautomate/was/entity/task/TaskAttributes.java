package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class TaskAttributes {

  /**
   * runtime def i.e. i/o variables from bpmn model.
   */
  private Map<String, Object> runtimeAttributes;
  /**
   * extensions variables from bpmn model.
   */
  private Map<String, String> modelAttributes;
  /**
   * variables at execution time.
   */
  private Map<String, Object> variables;
  
}
