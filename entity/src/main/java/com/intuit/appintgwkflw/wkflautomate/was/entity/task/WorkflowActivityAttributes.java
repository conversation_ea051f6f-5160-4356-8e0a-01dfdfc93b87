package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * activity attributes keeping extension and i/o attributes map saved in definition. this is used
 * later to prepare additional property bag to be sent to downstream.
 *
 * <AUTHOR>
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class WorkflowActivityAttributes {
  /**
   * runtime def i.e. i/o variables from bpmn model.
   */
  private Map<String, Object> runtimeAttributes;
  /**
   * extensions variables from bpmn model.
   */
  private Map<String, String> modelAttributes;

}
