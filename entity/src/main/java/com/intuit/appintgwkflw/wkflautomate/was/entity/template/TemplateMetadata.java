package com.intuit.appintgwkflw.wkflautomate.was.entity.template;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** Model representing template metadata when a template is saved/updated */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMetadata {

  private CreatorType creatorType = CreatorType.SYSTEM;

  private Status status = Status.ENABLED;

  private boolean allowMultipleDefs = false;

  private DefinitionType definitionType = DefinitionType.USER;

  private TemplateCategory templateCategory = TemplateCategory.HIDDEN;

  private boolean validateTriggerNames = Boolean.TRUE;

  //Enforces the async behaviour of start process
  // See https://camunda.com/blog/2014/07/advanced-asynchronous-continuations/
  private boolean canStartProcessAsync = Boolean.TRUE;

  //Enforce History TTL validation for bpmn save and update call
  private boolean validateHistoryTTL = Boolean.TRUE;

  private String templateAdjacencyValuesMd5Sum;

  private boolean isTemplateCreatedDynamically;

  public TemplateMetadata(
          CreatorType creatorType,
          Status status,
          boolean allowMultipleDefs,
          DefinitionType definitionType,
          TemplateCategory templateCategory,
          boolean validateTriggerNames,
          boolean canStartProcessAsync,
          boolean validateHistoryTTL,
          String templateAdjacencyValuesMd5Sum,
          boolean isTemplateCreatedDynamically) {
    this.creatorType = creatorType;
    this.status = status;
    this.allowMultipleDefs = allowMultipleDefs;
    this.definitionType = definitionType;
    this.templateCategory = templateCategory;
    this.validateTriggerNames = validateTriggerNames;
    this.canStartProcessAsync = canStartProcessAsync;
    this.validateHistoryTTL = validateHistoryTTL;
    this.templateAdjacencyValuesMd5Sum = templateAdjacencyValuesMd5Sum;
    this.isTemplateCreatedDynamically = isTemplateCreatedDynamically;
  }

  /**
   * This flag determines whether the template should be forked for Overwatch purposes.
   * In the context of automations for approvals and reminders, significant delays in the task queue have been observed,
   * causing the automation tests to exceed the expected completion time of 15-20 minutes.
   * To mitigate this issue, the template can be forked with a new name and topic details specific to Overwatch,
   * thus avoiding reliance on the main BPMN topics and instead using the Overwatch topics specified in the configuration.
   * <p>
   * Use case: We need to enable this property to true only from automations in approvals & reminders use case for callActivities BPMNs.
   * We have a custom logic in trigger API flow where we change the deployed camunda template id whenever we pass scope as Scope.TEST.
   * Also along with overriding topics, we need to override callActivities with overwatch callActivities.
   * <p>
   * This property helps us in creating overwatch templates accordingly with different topic configuration.
   * By enabling this property we deploy 2 templates. One for original business flow and another for automation.
   * The trigger flows will manage replacing the camunda template accordingly.
   */
  private boolean forkForOverwatch = Boolean.FALSE;
}
