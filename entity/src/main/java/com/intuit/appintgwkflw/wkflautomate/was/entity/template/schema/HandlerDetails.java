package com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.UIActions;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class HandlerDetails {
  private String taskHandler;
  private String actionName;
  private String handlerId;
  private RecordType recordType;
  private List<String> responseFields;

  private String handlerScope;

  @Data
  @JsonInclude(Include.NON_EMPTY)
  public static class ParameterDetails {
    private List<String> fieldValue = new LinkedList<>();
    private List<String> possibleFieldValues = new LinkedList<>();
    private boolean configurable;
    private boolean requiredByHandler;
    private boolean requiredByUI;
    private UIActions actionByUI;
    private boolean multiSelect;
    private List<String> helpVariables = new LinkedList<>();
    private String fieldType;
    private ParameterDetailsValueType valueType;
    private ComputedVariableType computedVariableType;
    private Map<String, String> computedVariableDetails;
    private String handlerFieldName;
    private String placeholder;
  }

  @Data
  public static class TaskDetails {
    private Boolean required;
    private Boolean fatal;
    private Integer retryCount;
    private String retryStrategyName;
  }

  @Data
  public static class CurrentStepDetails {
    private Boolean required;
  }
}
