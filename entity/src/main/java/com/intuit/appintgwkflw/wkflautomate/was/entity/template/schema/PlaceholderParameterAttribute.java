package com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * Entity Object to populate Parameter SDEF Placeholders from config.
 */
@Data
public class PlaceholderParameterAttribute {
  private String parameterName;
  private List<PlaceholderParameterDetailsAttribute> placeholderParameterDetailsAttributes = new ArrayList<>();
}


