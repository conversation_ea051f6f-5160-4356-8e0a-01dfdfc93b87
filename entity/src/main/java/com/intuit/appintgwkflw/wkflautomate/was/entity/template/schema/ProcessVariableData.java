package com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * The process variables extracted from the start-event of a workflow
 * Eg. "createTask": { "value": "true", "type": "String" }
 */
@Setter
@Getter
@AllArgsConstructor
@Builder
public class ProcessVariableData {
    @JsonInclude(Include.NON_EMPTY)
    private String value;
    private String type;
}