package com.intuit.appintgwkflw.wkflautomate.was.entity.variability;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Getter;

/**
 * <AUTHOR>
 * 
 * This class saves the entity/workflow name mapping with the feature name.
 *
 */
@Getter
public enum WorkflowFeatureMapping {

	PROJECT("project", "isProjectWorkflowsEnabled"),
	// the below feature is used in the context of Budget Approvals introduced for IES SKU
	BUDGET("budget", "isBudgetCollabEnabled");

	private static final Set<String> featureNames = prepareFeatureList();
	private static final Map<String, String> entityFeatureMapping = prepareEntityFeatureMapping();

	private final String featureName;
	private final String name;

	/**
	 * @param name name of the entity/workflow
	 * @param featureName feature name
	 */
	private WorkflowFeatureMapping(String name, String featureName) {
		this.featureName = featureName;
		this.name = name;
	}

	/**
	 * @return Map of name and featureName
	 */
	private static Map<String, String> prepareEntityFeatureMapping() {
		return Arrays.stream(WorkflowFeatureMapping.values())
				.collect(Collectors.toMap(name -> name.getName().toLowerCase(), WorkflowFeatureMapping::getFeatureName));
	}

	/**
	 * @return feature list
	 */
	private static Set<String> prepareFeatureList() {
		return Arrays.stream(WorkflowFeatureMapping.values()).map(featureName -> featureName.getFeatureName())
				.collect(Collectors.toSet());
	}

	/**
	 * @return list of all the features.
	 */
	public static Set<String> getAllFeatures() {
		return featureNames;
	}

	/**
	 * @return map of Map of name and featureName
	 */
	public static Map<String, String> getEntityFeatureMapping() {
		return entityFeatureMapping;
	}

}
