package com.intuit.appintgwkflw.wkflautomate.was.entity.worker;

import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import java.util.Map;
import java.util.Objects;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.camunda.bpm.engine.variable.VariableMap;

/** <AUTHOR> */
@Getter
@Builder(toBuilder = true)
public class WorkerActionRequest {

  private Map<String, String> inputVariables;
  private Map<String, String> extensionProperties;
  private VariableMap variableMap;
  private String processDefinitionId;
  @Setter
  private String activityId;
  private String processInstanceId;
  @Setter
  private String handlerId;
  private String workflowId;
  private Long ownerId;
  private String taskId;
  private String workerId;
  private String handlerScope;
  private HandlerDetails handlerDetails;
  private String definitionKey;
  private Integer retries;
  private String businessKey;

  @Setter private String rootProcessInstanceId;

  @Setter private boolean isCalledProcess;

  public String fetchParentProcessInstanceId() {
    return Objects.nonNull(rootProcessInstanceId) ? rootProcessInstanceId : processInstanceId;
  }
}
