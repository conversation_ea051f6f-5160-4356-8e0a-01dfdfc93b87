description: "Entity after a Workflow External Task is Assigned"
properties:
  workflowMetadata:
    type: "object"
    description: "Workflow Metadata that needs to be passed with each event"
    $ref: "/workflows/events/WorkflowMetadata"
    intuitDataClassification: 'RESTRICTED'
  taskName:
    type: "string"
    description: "Name of the current task which is set in the BPMN template. For Eg: Client Verification, Invoice Approval"
    intuitDataClassification: 'RESTRICTED'
  variables:
    type: "object"
    document: true
    description: "These **context variables** will be passed back OUT which is fetched from Camunda. Value will be a json object"
    intuitDataClassification: 'RESTRICTED'
additionalProperties: false
required:
  - "workflowMetadata"
# Mandatory Attribute for Topic schema creation
customerOwned: false
