properties:
  workflowOwnerId:
    type: "string"
    description: "Owner of the Workflow, which is currently the realmId"
    intuitDataClassification: 'RESTRICTED'
  processInstanceId:
    type: "string"
    description: "Process InstanceId of the workflow that initiated the external task"
    intuitDataClassification: 'RESTRICTED'
  businessEntityType:
    type: "string"
    description: "Type of entity for which workflow will be run. For eg: Invoice, Engagement"
    intuitDataClassification: 'RESTRICTED'
  businessEntityId:
    type: "string"
    description: "Id of the business EntityType, for eg: invoiceId, engagementId"
    intuitDataClassification: 'RESTRICTED'
  workflowName:
    type: "string"
    description: "template name or Definition key. For Eg: invoiceapproval/invoiceoverdue"
    intuitDataClassification: 'RESTRICTED'
  entityChangeType:
    type: "string"
    description: "represents the change on the entity. For Eg: created/updated"
    intuitDataClassification: 'RESTRICTED'
  offeringId:
    type: "string"
    description: "identifier for the offering. For Eg: qblive/ttlive"
    intuitDataClassification: 'RESTRICTED'  
additionalProperties: true
required:
  - "workflowOwnerId"
  - "processInstanceId"
  - "businessEntityType"
  - "businessEntityId"
  - "workflowName"
  - "offeringId"
