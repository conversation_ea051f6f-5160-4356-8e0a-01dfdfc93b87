package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@ConditionalOnExpression(
    "${event.consumer.enabled:false} && ${event.consumer.enabledEntities.definitionEvent:false}")
/**
 * Event consumer that listens with definition events as entity type
 *
 * <AUTHOR>
 */
public class DefinitionEventConsumer {
  private EventProcessor eventProcessor;

  @PostConstruct
  public void init() {
    EventingLoggerUtil.logInfo(
        "Initializing Kafka Consumer for DefinitionEventConsumer", this.getClass().getSimpleName());
  }

  /**
   * Receive an event and manually commit i.e. acknowledge Kafka offset.
   *
   * @param consumerRecord consumed record
   * @param messageHeaders message headers
   * @param acknowledgment manual acknowledge-er
   */
  @KafkaListener(
      topics = "#{eventConsumerUtil.getFilteredTopicList('definitionEvent',false)}",
      groupId = "${event.consumer.groupId}",
      containerFactory = "kafkaListenerFactory")
  @EventListener(name = MetricName.DEFINITION_EVENT, type = Type.EVENT_METRIC)
  public void receive(
      ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
      @Headers MessageHeaders messageHeaders,
      Acknowledgment acknowledgment) {
    EventingLoggerUtil.logDebug(
        "Reading message in DefinitionEventConsumer", this.getClass().getSimpleName());
    eventProcessor.process(messageHeaders, consumerRecord.value().get());
  }
}
