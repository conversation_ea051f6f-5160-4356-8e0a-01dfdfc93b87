package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *     <p>Event consumer that listens to one or more topics. Kafka offsets are committed by the
 *     client explicitly once a record is received It recieves events, enriches and publish it back
 *     to Incident Workflow Topics
 */
@Component
@AllArgsConstructor
@ConditionalOnExpression(
    "${event.consumer.enabled:false} && ${event.consumer.enabledEntities.serviceTask:false}")
public class ServiceTaskKafkaConsumer {
  private EventProcessor eventProcessor;

  @PostConstruct
  public void init() {
    EventingLoggerUtil.logInfo(
        "Starting Kafka Consumer for Service Task", this.getClass().getSimpleName());
  }

  /**
   * Receive an event and manually commit i.e. acknowledge Kafka offset.
   *
   * @param consumerRecord consumed record
   * @param messageHeaders message headers
   * @param acknowledgment manual acknowledge-er
   */
  @KafkaListener(
      topics = "#{eventConsumerUtil.getFilteredTopicList('serviceTask',false)}",
      groupId = "${event.consumer.groupId}",
      containerFactory = "kafkaListenerFactory")
  @EventListener(name = MetricName.INTERNAL_CAMUNDA_SERVICE_TASK_EVENT, type = Type.EVENT_METRIC)
  public void receive(
      ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
      @Headers MessageHeaders messageHeaders,
      Acknowledgment acknowledgment) {
    EventingLoggerUtil.logDebug(
        "Reading message in ServiceTaskKafkaConsumer", this.getClass().getSimpleName());
    eventProcessor.process(messageHeaders, consumerRecord.value().get());
  }
}
