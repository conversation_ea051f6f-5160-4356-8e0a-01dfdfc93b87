package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *     <p>Event consumer that listens to workflow state transition events published from camunda.
 */
@Component
@AllArgsConstructor
@ConditionalOnExpression(
    "${event.consumer.enabled:false} && ${event.consumer.enabledEntities.workflowTransitionEvents:false} ")
public class WorkflowTransitionEventsKafkaConsumer {
  private EventProcessor eventProcessor;
  private FilterEventUtil filterEventUtil;

  @PostConstruct
  public void init() {
    EventingLoggerUtil.logInfo(
        "Starting Kafka Consumer for workflow state transition events", this.getClass().getSimpleName());
  }

  /**
   * Receive an event and manually commit i.e. acknowledge Kafka offset.
   *
   * @param consumerRecord consumed record
   * @param messageHeaders message headers
   * @param acknowledgment manual acknowledge-er
   */
  @KafkaListener(
      topics = "#{eventConsumerUtil.getFilteredTopicList('workflowTransitionEvents',false)}",
      groupId = "${event.consumer.groupId}",
      containerFactory = "kafkaListenerFactory")
  @EventListener(name = MetricName.EVENT_WORKFLOW_TRANSITION_EVENT, type = Type.EVENT_METRIC)
  public void receive(
      ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
      @Headers MessageHeaders messageHeaders,
      Acknowledgment acknowledgment) {
    EventingLoggerUtil.logDebug(
        "Reading message in WorkflowTransitionEventsKafkaConsumer", this.getClass().getSimpleName());
    if (filterEventUtil.evaluate(EventEntityType.WORKFLOW_TRANSITION_EVENTS)) {
      EventingLoggerUtil.logInfo(
          "Skipping Event consumption, numaFlow=true eventEntityType=%s",
          this.getClass().getSimpleName(), EventEntityType.WORKFLOW_TRANSITION_EVENTS);
      return;
    }
    eventProcessor.process(messageHeaders, consumerRecord.value().get());
  }
}
