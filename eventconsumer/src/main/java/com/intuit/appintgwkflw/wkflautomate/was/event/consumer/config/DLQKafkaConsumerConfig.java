package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventRetryHandler;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;

import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.support.RetryTemplate;

/**
 * Kafka consumer factory for DLQ. This class has all the configuration used by the dlq kafka
 * consumer.
 *
 * Events that are processed and face a non-retriable exception in the DLQ are just logged or incident created
 *      depending on type of exception.
 * Events that are processed and face a retriable exception in the DLQ are retried for configured number of times
 *      and if even all the retries failed, incident is created in the RecoveryCallbackHandler.
 * Events that are not processed due to an open circuit are pushed back to the DLQ for re-processing
 *      in the RecoveryCallbackHandler without any retries.
 *
 * <AUTHOR>
 */
@EnableKafka
@EnableRetry
@Configuration
@AllArgsConstructor
@ConditionalOnExpression("${event.consumer.retryConfig.dlqEnabled:false}")
public class DLQKafkaConsumerConfig {

  private EventConfiguration eventConfiguration;
  private ConsumerFactory<String, Result<FormatException, String>> dlqConsumerFactory;
  private RetryTemplate dlqRetryTemplate;
  private EntityHandler entityHandler;
  private EventRetryHandler eventRetryHandler;
  private SimpleEventPublisher kafkaProducer;

  @SuppressWarnings("unchecked")
  @Bean("dlqKafkaListenerFactory")
  public ConcurrentKafkaListenerContainerFactory<String, Result<FormatException, String>>
      kafkaListenerContainerFactory() {

    WorkflowVerfiy.verifyNull(
        eventConfiguration.getConsumer().getRetryConfig(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.CONSUMER_RETRY_CONFIG_NULL_ERROR);

    ConcurrentKafkaListenerContainerFactory<String, Result<FormatException, String>> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(dlqConsumerFactory);
    factory.setConcurrency(eventConfiguration.getConsumer().getRetryConfig().getDlqConcurrency());
    // with MANUAL ackmode, the offsets are committed when all the results from the poll have been
    // processed. With MANUAL_IMMEDIATE mode, the offsets are committed one-by-one.
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
    // From Spring Kafka 2.3, the method .setAckOnError() has been deprecated.
    // The purpose of .setAckOnError(false) was to not acknowledge a message if the error handler
    // threw an exception. This happens by default now.
    factory.getContainerProperties();

    factory.setRetryTemplate(dlqRetryTemplate);

    factory.setRecoveryCallback(
        context -> {
          eventRetryHandler.handleRetryFailure(
              context,
              ((consumerRecord, headers) ->{ dlqRecoveryCallbackHandler(context, consumerRecord, headers); })
          );
          return Optional.empty();
        });

    // Stopping consumers from auto-starting for DR
    factory.setAutoStartup(false);
    return factory;
  }

  private void dlqRecoveryCallbackHandler(RetryContext context, ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
                                         Map<String, String> headers) {
      if (eventRetryHandler.isWorkflowCircuitOpenException(context)) {
          WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Pushing request back to DLQ.");
          kafkaProducer.send(
                  consumerRecord.value().get(),
                  headers,
                  consumerRecord.topic());
          return;
      }
      WorkflowEventHandlers.getHandler(
                      EventEntityType.valueOfEntity(entityHandler.getEntityType(headers)))
              .handleFailure(
                      consumerRecord.value().get(),
                      headers,
                      new WorkflowGeneralException(
                              WorkflowError.EVENT_RETRIES_FAILED,
                              context.getLastThrowable().getCause()));
  }
}
