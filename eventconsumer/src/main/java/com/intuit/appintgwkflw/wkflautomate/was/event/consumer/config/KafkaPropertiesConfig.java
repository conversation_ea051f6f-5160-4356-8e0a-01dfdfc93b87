package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.eventbus.DeserializingMessageExtractor;
import com.intuit.eventbus.configurations.ProvisioningMethods;
import com.intuit.eventbus.cryptography.IDPSDecrypter;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.impl.DeserializingMessageExtractorImpl;
import com.intuit.eventbus.kafka.serde.Deserializer;
import com.intuit.eventbus.message.MetadataV3;
import com.intuit.eventbus.utils.Result;
import com.intuit.eventbus.validation.validator.DefaultValidator;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

@Configuration
@AllArgsConstructor
@ConditionalOnExpression("${event.consumer.enabled:false}")
public class KafkaPropertiesConfig {

  private EventConfiguration eventConfiguration;

  private Map<String, Object> getKafkaProperties() {
    WorkflowVerfiy.verifyNull(
        eventConfiguration,
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.EVENT_CONFIGURATION_NULL_ERROR);

    WorkflowVerfiy.verifyNull(
        eventConfiguration.getConsumer(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.CONSUMER_EVENT_CONFIGURATION_NULL_ERROR);

    Map<String, Object> kafkaProps;
    if (MapUtils.isEmpty(eventConfiguration.getConsumer().getConfig())) {
      kafkaProps = setConfig();
    } else {
      kafkaProps = new HashMap<>(eventConfiguration.getConsumer().getConfig());
    }

    kafkaProps.put(
        ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, eventConfiguration.getConsumerRegionConfig().getBootStrapServers());
    kafkaProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    kafkaProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, Deserializer.class);

    return kafkaProps;
  }

  private Map<String, Object> setConfig() {
    Map<String, Object> kafkaProps = new HashMap<>();
    kafkaProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, Boolean.FALSE.toString());
    kafkaProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, eventConfiguration.getConsumer().getMaxPollRecords());
    kafkaProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, eventConfiguration.getConsumer().getHeartbeatIntervalMs());
    kafkaProps.put(ConsumerConfig.GROUP_ID_CONFIG, eventConfiguration.getConsumer().getGroupId());
    kafkaProps.put(
        ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
        eventConfiguration.getConsumer().getAutoOffsetReset());
    kafkaProps.put(
        ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
        eventConfiguration.getConsumer().getMaxPartitionFetchBytes());
    kafkaProps.put(
        ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
        eventConfiguration.getConsumer().getSessionTimeoutMs());
    kafkaProps.put(
        ConsumerConfig.FETCH_MIN_BYTES_CONFIG, eventConfiguration.getConsumer().getFetchMinBytes());
    kafkaProps.put(
        ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG,
        eventConfiguration.getConsumer().getFetchMaxWaitMs());
    kafkaProps.put(
        ConsumerConfig.RECEIVE_BUFFER_CONFIG,
        eventConfiguration.getConsumer().getReceiveBufferBytes());
    kafkaProps.put(
        ConsumerConfig.RECONNECT_BACKOFF_MS_CONFIG,
        eventConfiguration.getConsumer().getReconnectBackoffMs());
    kafkaProps.put(
        ConsumerConfig.RETRY_BACKOFF_MS_CONFIG,
        eventConfiguration.getConsumer().getRetryBackoffMs());
    kafkaProps.put(
        KafkaConstants.SECURITY_PROTOCOL_CONFIG, eventConfiguration.getSecurityProtocol());
    kafkaProps.put(
        KafkaConstants.SSL_ENABLED_PROTOCOLS_CONFIG, eventConfiguration.getSslEnabledProtocol());
    return kafkaProps;
  }

  @Bean
  public ConsumerFactory<String, Result<FormatException, String>> consumerFactory() {
    return new DefaultKafkaConsumerFactory<>(
        getKafkaProperties(), new StringDeserializer(), getValueDeserializer());
  }

  @Bean("dlqConsumerFactory")
  public ConsumerFactory<String, Result<FormatException, String>> dlqConsumerFactory() {
    Map<String, Object> kafkaProps = getKafkaProperties();
    kafkaProps.put(
        ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
        eventConfiguration.getConsumer().getRetryConfig().getDlqMaxPollIntervalMs());
    // Isolating normal flow and dlq group id and autoOffsetReset policy
    kafkaProps.put(ConsumerConfig.GROUP_ID_CONFIG, eventConfiguration.getConsumer().getRetryConfig().getDlqGroupId());
    if(Objects.nonNull(eventConfiguration.getConsumer().getRetryConfig().getDlqAutoOffsetReset())) {
      kafkaProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
          eventConfiguration.getConsumer().getRetryConfig().getDlqAutoOffsetReset());
    }
    return new DefaultKafkaConsumerFactory<>(
        kafkaProps, new StringDeserializer(), getValueDeserializer());
  }

  @Bean
  public RetryTemplate retryTemplate() {
    WorkflowVerfiy.verifyNull(
        eventConfiguration.getConsumer().getRetryConfig(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.CONSUMER_RETRY_CONFIG_NULL_ERROR);

    RetryTemplate retryTemplate = new RetryTemplate();
    FixedBackOffPolicy fixedBackOffPolicy = new FixedBackOffPolicy();
    fixedBackOffPolicy.setBackOffPeriod(
        eventConfiguration.getConsumer().getRetryConfig().getRetryBackoffPeriod());
    retryTemplate.setBackOffPolicy(fixedBackOffPolicy);

    retryTemplate.setRetryPolicy(
        new SimpleRetryPolicy(
            eventConfiguration.getConsumer().getRetryConfig().getRetryMaxAttempts()));
    return retryTemplate;
  }

  @Bean("dlqRetryTemplate")
  public RetryTemplate dlqRetryTemplate() {
    WorkflowVerfiy.verifyNull(
        eventConfiguration.getConsumer().getRetryConfig(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.CONSUMER_RETRY_CONFIG_NULL_ERROR);

    RetryTemplate retryTemplate = new RetryTemplate();
    ExponentialBackOffPolicy exponentialBackOffPolicy = new ExponentialBackOffPolicy();
    exponentialBackOffPolicy.setInitialInterval(
        eventConfiguration.getConsumer().getRetryConfig().getDlqBackoffInitialInterval());
    exponentialBackOffPolicy.setMultiplier(
        eventConfiguration.getConsumer().getRetryConfig().getDlqBackoffMultiplier());
    exponentialBackOffPolicy.setMaxInterval(
        eventConfiguration.getConsumer().getRetryConfig().getDlqBackoffMaxInterval());
    retryTemplate.setBackOffPolicy(exponentialBackOffPolicy);

    // In DLQ, any exception that should be retried before going to RecoveryCallbackHandler should be marked as true in the policyMap below
    Map<Class<? extends Throwable>, Boolean> policyMap = new HashMap<Class<? extends Throwable>, Boolean>();
    policyMap.put(WorkflowRetriableException.class, true);
    SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(eventConfiguration.getConsumer().getRetryConfig().getDlqRetryMaxAttempts(), policyMap, true);
    retryTemplate.setRetryPolicy(retryPolicy);

    return retryTemplate;
  }

  public Deserializer<String> getValueDeserializer() {
    DeserializingMessageExtractor<String, MetadataV3> extractor =
        new DeserializingMessageExtractorImpl.Config<>(
                new DefaultValidator.Config<>(),
                ProvisioningMethods.permissiveExtractor(
                    new IDPSDecrypter.Config(eventConfiguration.getIdpsConfig().getProperties())),
                com.intuit.eventbus.serialization.StringDeserializer::new)
            .instantiate();

    return new Deserializer<>(extractor);
  }
}
