package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * This config file is responsible for push based task config for event consumer
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties("push-task")
public class PushTaskConfig {
    private Map<String, Worker> topics;
    private boolean enabled;
}
