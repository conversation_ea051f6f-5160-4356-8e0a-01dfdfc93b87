package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.dlq;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Event consumer that listens to External Task DLQ topics. Kafka offsets are
 *     committed by the client explicitly once a record is received
 */
@Component
@AllArgsConstructor
@ConditionalOnExpression(
    "${event.consumer.retryConfig.dlqEnabled:false} && ${event.consumer.retryConfig.enabledEntities.externalTask:false}")
public class ExternalTaskDLQKafkaConsumer {
  private EventProcessor eventProcessor;

  @PostConstruct
  public void init() {
    EventingLoggerUtil.logInfo(
        "Starting DLQ Kafka Consumer for ExternalTask", this.getClass().getSimpleName());
  }

  /**
   * Receive an event and manually commit i.e. acknowledge Kafka offset.
   *
   * @param consumerRecord consumed record
   * @param messageHeaders message headers
   * @param acknowledgment manual acknowledge
   */
  @KafkaListener(
      id = KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID,
      topics = "#{eventConsumerUtil.getFilteredTopicList('externalTask',true)}",
      groupId = "${event.consumer.retryConfig.dlqGroupId}",
      containerFactory = "dlqKafkaListenerFactory")
  @EventListener(name = MetricName.EVENT_EXTERNAL_TASK_DLQ, type = Type.EVENT_METRIC)
  public void receive(
      ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
      @Headers MessageHeaders messageHeaders,
      Acknowledgment acknowledgment) {
    eventProcessor.process(messageHeaders, consumerRecord.value().get());
  }
}
