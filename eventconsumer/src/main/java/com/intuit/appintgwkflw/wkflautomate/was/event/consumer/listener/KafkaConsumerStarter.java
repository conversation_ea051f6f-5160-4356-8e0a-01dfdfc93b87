package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import lombok.AllArgsConstructor;
import org.springframework.context.Lifecycle;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@AllArgsConstructor
/**
 * Starts and stops Kafka Consumers
 *
 * <AUTHOR>
 */
public class KafkaConsumerStarter {
  
  private KafkaListenerEndpointRegistry registry;

  public enum Action {
    START,
    STOP
  }

  /** Starts all the registered Kafka Consumers whihc are not yet running */
  public void startAllConsumers() {
    startOrStop(Action.START);
  }

  /** Starts all the registered & already running Kafka Consumers */
  public void stopAllConsumers() {
    startOrStop(Action.STOP);
  }

  /** Pause consumer with the passed id */
  public void pauseConsumer(String consumerId) {
    WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Pause initiated for Kafka consumer for consumerId=%s", consumerId);
    Optional<MessageListenerContainer> messageListenerContainer = getMessageListenerContainer(consumerId);
    if(messageListenerContainer.isEmpty()){
      WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Pause for Kafka consumer with consumerId=%s aborted", consumerId);
      return;
    }
    messageListenerContainer.get().pause();
    WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Kafka consumer with consumerId=%s successfully paused", consumerId);
  }

  /** Resume consumer with the passed id */
  public void resumeConsumer(String consumerId){
    WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Resume initiated for Kafka consumer for consumerId=%s", consumerId);
    Optional<MessageListenerContainer> messageListenerContainer = getMessageListenerContainer(consumerId);
    if(messageListenerContainer.isEmpty()){
      WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Resume for Kafka consumer with consumerId=%s aborted", consumerId);
      return;
    }
    messageListenerContainer.get().resume();
    WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Kafka consumer with consumerId=%s successfully resumed", consumerId);
  }

  private Optional<MessageListenerContainer> getMessageListenerContainer(String consumerId) {
    Optional<MessageListenerContainer> messageListenerContainer = Optional.ofNullable(registry.getListenerContainer(consumerId));
    return messageListenerContainer.filter(Lifecycle::isRunning);
  }

  private void startOrStop(Action action) {
    if (registry != null) {
      registry
          .getListenerContainers()
          .forEach(
              consumer -> {
                if (action.equals(Action.START) && !consumer.isRunning()) {
                  WorkflowLogger.logInfo("RegionConfig:: Starting Kafka Consumer");
                  consumer.start();
                } else if (action.equals(Action.STOP) && consumer.isRunning()) {
                  WorkflowLogger.logInfo("RegionConfig:: Stopping Kafka Consumer");
                  consumer.stop();
                }
              });
    }
  }
}
