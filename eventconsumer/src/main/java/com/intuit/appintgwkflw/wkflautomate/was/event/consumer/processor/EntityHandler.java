package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class EntityHandler {

  private EventConsumerUtil eventConsumerUtil;

  public String getEntityType(Map<String, String> headers) {
    // Get entity type from Kafka topic name
    String kafkaTopic = headers.get(KafkaConstants.KAFKA_TOPIC_HEADER);
    WorkflowVerfiy.verifyNull(kafkaTopic, WorkflowError.MISSING_TOPIC_NAME);

    String entityType =
        eventConsumerUtil.getTopicToEntityTypeMapping().get(kafkaTopic);
    WorkflowVerfiy.verifyNull(entityType, WorkflowError.MISSING_TOPIC_ENTITY_MAPPING);
    WorkflowVerfiy.verify(
        !WorkflowEventHandlers.contains(EventEntityType.valueOfEntity(entityType)),
        WorkflowError.UNSUPPORTED_HANDLER_NAME);

    return entityType;
  }

  /**
   * This method return the entity type of given kafka topic
   *
   * @param kafkaTopic
   * @return
   */
  public String getEntityType(String kafkaTopic) {
    // Get entity type from Kafka topic name
    return getEntityType(Map.of(KafkaConstants.KAFKA_TOPIC_HEADER, kafkaTopic));
  }
}
