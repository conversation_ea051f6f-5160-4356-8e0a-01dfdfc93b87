package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class EventProcessor {

  private EntityHandler entityHandler;

  @SuppressWarnings("unchecked")
  public void process(MessageHeaders messageHeaders, String event) {

    EventingLoggerUtil.logInfo(
        "Processing event. step=eventConsumedAndValidated", this.getClass().getSimpleName());
    Map<String, String> headers = EventUtil.transformHeader(messageHeaders);

    WorkflowEventHandlers.getHandler(
            EventEntityType.valueOfEntity(entityHandler.getEntityType(headers)))
        .transformAndExecute(event, headers);
  }
}
