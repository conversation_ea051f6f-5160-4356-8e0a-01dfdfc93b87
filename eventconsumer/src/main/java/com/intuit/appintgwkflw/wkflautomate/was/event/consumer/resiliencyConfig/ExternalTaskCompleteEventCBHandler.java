package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.resiliencyConfig;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ExternalTaskCompleteEventCBHandler implements CircuitBreakerActionHandler {

    private final KafkaConsumerStarter kafkaConsumerStarter;
    private final WASCircuitBreakerService wasCircuitBreakerService;
    private final DLQResumeJobScheduler dlqResumeJobScheduler;

    @Override
    public CircuitBreakerActionType getName() {
        return CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT;
    }

    /**
     * Circuit breaker state-transition-event handler for external task complete event use cases.
     *
     * Pause DLQ only when state transition from CLOSED to OPEN if circuit breaker of just one
     *      offering opens
     * Resume DLQ when state transition from any other state to CLOSED only if circuit breakers
     *      of all offerings have closed.
     * On setting circuitBreakerEnabled flag as false, all circuit breakers are disabled leading to state transition
     *      to DISABLED. This state transition will in turn resume DLQ.
     *
     * @param stateTransitionEvent
     */
    @Override
    public void handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition stateTransitionEvent) {
        try {
            if (CircuitBreaker.StateTransition.CLOSED_TO_OPEN.equals(stateTransitionEvent)) {
                // TODO: re-close circuit if pause fails?
                pauseDLQ();
            }
            else if (CircuitBreaker.State.CLOSED.equals(stateTransitionEvent.getToState())
                    && wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT)) {
                // TODO: re-open circuit if resume fails?
                resumeDLQ();
            }
            else if(CircuitBreaker.State.DISABLED.equals(stateTransitionEvent.getToState())) {
                resumeDLQ();
            }
        }
        catch (Exception ex) {
            EventingLoggerUtil.logError(
                    ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Error in circuit breaker state transition event handler. Error=%s",
                    this.getClass().getSimpleName(),
                    ex
            );
        }
    }

    private void pauseDLQ() {
        kafkaConsumerStarter.pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        dlqResumeJobScheduler.startScheduler();
    }

    private void resumeDLQ() {
        kafkaConsumerStarter.resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        dlqResumeJobScheduler.stopScheduler();
    }
}
