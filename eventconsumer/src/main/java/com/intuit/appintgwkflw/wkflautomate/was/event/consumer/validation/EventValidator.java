package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import java.util.Map;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Validates events
 *
 * <AUTHOR>
 */
@UtilityClass
public class EventValidator {

  /**
   * Validates all the necessary headers are present and the event is not a duplicate
   *
   * @param headers
   */
  public void validate(Map<String, String> headers) {

    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(headers.get(EventHeaderConstants.INTUIT_TID)),
        WorkflowError.MISSING_EVENT_HEADERS,
        String.format(ValidationError.MISSING_INTUIT_TID_HEADER.getErrorMessage(), headers.get(EventHeaderConstants.ENTITY_ID)));

    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(headers.get(EventHeaderConstants.IDEMPOTENCY_KEY)),
        WorkflowError.MISSING_EVENT_HEADERS,
        ValidationError.MISSING_IDEMPOTENCY_KEY_HEADER.getErrorMessage());

  }

}
