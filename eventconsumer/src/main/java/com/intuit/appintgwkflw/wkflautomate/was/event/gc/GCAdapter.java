package com.intuit.appintgwkflw.wkflautomate.was.event.gc;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.GCRetryableEventsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.guranteedconsumer.commons.core.Enum.Status;
import com.intuit.guranteedconsumer.commons.core.model.GCInbox;
import com.intuit.guranteedconsumer.commons.domain.api.GCService;
import com.intuit.guranteedconsumer.commons.domain.model.ErroredEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Handles interactions with the GCService and GCConfig for recording, updating, and fetching events.
 * Provides methods to record new events, update existing events, handle failed events, and retrieve errored events.
 *
 * <AUTHOR>
 */
@Component
public class GCAdapter {
    @Autowired(required = false)
    private GCService gcService;
    @Autowired
    private GCConfig gcConfig;
    @Autowired
    private GCRetryableEventsRepository gcRetryableEventsRepository;



    /**
     * Records a new event in the GCService inbox with the status IN_PROGRESS.
     *
     * @param messageHeaders The headers of the message being recorded.
     * @param meta Additional metadata associated with the event.
     * @param payload The payload of the event.
     * @return The UUID of the newly created record.
     */
    public UUID recordEvent(MessageHeaders messageHeaders, HashMap<String, Object> meta, String payload, Status status, Throwable e) {
        UUID recordId = gcService.recordInboxEntry(messageHeaders, payload, meta,status, e);
        EventingLoggerUtil.logInfo(
                "Successfully Created the record in gc_inbox status=%s",
                this.getClass().getSimpleName(), status);
        return recordId;
    }

    /**
     * Updates an existing event in the GCService with the given status and throwable.
     *
     * @param id The UUID of the event to be updated.
     * @param status The new status of the event.
     * @param throwable The throwable associated with the event, if any.
     * @return The UUID of the updated event.
     */
    public UUID updateEvent(UUID id, Status status, Throwable throwable) {
        gcService.updateEvent(id, status, throwable);
        EventingLoggerUtil.logInfo(
                "Successfully Updated the record in gc_inbox status=%s, recordId=%s",
                this.getClass().getSimpleName(), status, id);
        return id;
    }

    /**
     * Records a failed event in the GCService failed event log.
     *
     * @param id The UUID of the original event.
     * @param messageHeaders The headers of the message being recorded.
     * @param meta Additional metadata associated with the event.
     * @param payload The payload of the event.
     * @param throwable The throwable that caused the event to fail.
     * @return The UUID of the newly created failed event record.
     */
    public UUID recordFailedEvent(UUID id, MessageHeaders messageHeaders, HashMap<String, Object> meta, String payload, Throwable throwable) {
        UUID recordId = gcService.recordFailedEventEntry(id, messageHeaders, payload, meta, throwable);
        EventingLoggerUtil.logInfo(
                "Successfully Created the record in gc_failed_event recordId=%s",
                this.getClass().getSimpleName(), recordId);
        return recordId;
    }

    /**
     * Updates an inbox record to FAILED status and creates a corresponding failed event record.
     * This operation is transactional.
     *
     * @param id The UUID of the event to be updated.
     * @param messageHeaders The headers of the message being recorded.
     * @param meta Additional metadata associated with the event.
     * @param payload The payload of the event.
     * @param throwable The throwable that caused the event to fail.
     * @return The UUID of the newly created failed event record.
     */
    @Transactional
    public UUID updateInboxRecordAndCreateFailedEvent(UUID id, MessageHeaders messageHeaders, HashMap<String, Object> meta, String payload, Throwable throwable) {
        updateEvent(id, Status.FAILED, throwable);
        return recordFailedEvent(id, messageHeaders, meta, payload, throwable);
    }

    /**
     * Retrieves a list of errored events from the GCService.
     *
     * @return A list of errored events.
     */
    public List<ErroredEvent> getErroredEvents(){
        EventingLoggerUtil.logInfo(
                "Fetching errored events from gc_inbox",
                this.getClass().getSimpleName());
        return gcService.getErroredEvents(gcConfig.getUnmodifiedSinceSeconds(), gcConfig.getMaxRecordsToBeFetched());
    }

    /**
     * Updates the retry count to 0 and sets the status to
     * ERROR for the given list of record IDs.
     *
     * @param recordIds The list of UUIDs of the records to be updated.
     * @return The number of records that were updated.
     */
    public int retryFailedEventsWithinTimeFrame(List<UUID> recordIds){
        EventingLoggerUtil.logInfo(
                "Updating recordIds=%s for further retry processing",
                this.getClass().getSimpleName(), recordIds);
        return gcRetryableEventsRepository.updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name());
    }

    /**
     * Fetches the event for the given record ID.
     *
     * @param recordId The UUID of the record to be fetched.
     * @return The GCRetryableEvent object if found, otherwise null.
     */
    public GCInbox fetchEvent(UUID recordId){
        EventingLoggerUtil.logInfo(
                "Fetching event for recordId=%s",
                this.getClass().getSimpleName(), recordId);
        Optional<GCInbox> retryableEvent = gcRetryableEventsRepository.findById(recordId);
        return retryableEvent.orElse(null);
    }

    /**
     * Fetches the list of record IDs that have exceeded the maximum retry
     * count for a given entity type within a specified time frame.
     *
     * @param entityType The type of the entity for which to fetch the record IDs.
     * @param startTime The start time of the time frame.
     * @param endTime The end time of the time frame.
     * @return A list of UUIDs of the records that match the criteria.
     */
    public List<UUID> getFailedRecordIdsByEntityTypeAndTimeFrame(String entityType, LocalDateTime startTime, LocalDateTime endTime) {
        EventingLoggerUtil.logInfo(
                "Fetching max retries exceeded events for entityType=%s, startTime=%s, endTime=%s",
                this.getClass().getSimpleName(), entityType, startTime, endTime);
        return gcRetryableEventsRepository.findRecordIdsByEntityTypeAndTimeFrame(entityType, gcConfig.getMaxRetries() + 1, startTime, endTime);
    }

    /**
     * Updates the retry count to the maximum retries plus one and sets the status to ERROR
     * for the given entity type and time frame.
     *
     * @param entityType The type of the entity.
     * @param startTime The start time of the time frame.
     * @param endTime The end time of the time frame.
     * @return A list of UUIDs of the records that were updated.
     */
    public List<UUID> retryFailedEventsWithinTimeFrame(String entityType, LocalDateTime startTime, LocalDateTime endTime) {
        EventingLoggerUtil.logInfo(
                "Updating recordIds for further retry processing for entityType=%s",
                this.getClass().getSimpleName(), entityType);
        return gcRetryableEventsRepository.updateRetryCountAndStatus(entityType, 0, startTime, endTime);
    }
}
