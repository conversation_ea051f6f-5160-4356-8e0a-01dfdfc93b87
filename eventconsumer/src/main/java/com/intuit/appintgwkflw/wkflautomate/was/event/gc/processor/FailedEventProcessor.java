package com.intuit.appintgwkflw.wkflautomate.was.event.gc.processor;

import com.cronutils.utils.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.AsyncTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.DownStreamConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.constants.GCConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.guranteedconsumer.commons.core.Enum.Status;
import com.intuit.guranteedconsumer.commons.domain.model.ErroredEvent;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Processes failed events by retrying them according to the configuration.
 * Utilizes a thread pool to handle the processing of errored events concurrently.
 * Scheduled to run based on a cron expression defined in the configuration.
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnExpression("${gc.serviceEnabled:false}")
@SuppressWarnings("unchecked")
public class FailedEventProcessor implements AsyncTaskManager {
    private final GCConfig gcConfig;
    private final GCAdapter gcAdapter;
    private final ThreadPoolExecutor gcThreadPool;
    private final WASContextHandler contextHandler;
    private final OfferingConfig offeringConfig;

    private final AtomicBoolean isActive = new AtomicBoolean(false);

    @Autowired
    public FailedEventProcessor(GCConfig gcConfig, GCAdapter gcAdapter, @Qualifier(WorkflowConstants.GC_EXECUTOR_THREAD_BEAN) ThreadPoolExecutor gcThreadPool, WASContextHandler contextHandler, OfferingConfig offeringConfig) {
        this.gcConfig = gcConfig;
        this.gcAdapter = gcAdapter;
        this.gcThreadPool = gcThreadPool;
        this.contextHandler = contextHandler;
        this.offeringConfig = offeringConfig;
    }

    /**
     * Processes errored events by retrieving them from different DB's based on offering id with GC service and submitting them to the thread pool for processing.
     * This method is scheduled to run based on the cron expression defined in the configuration.
     */
    @Scheduled(cron = "${gc.cron}")
    @Metric(name = MetricName.GC_ERRORED_EVENT_RETRY, type = Type.APPLICATION_METRIC)
    public void processErroredEvents() {
        if(!isActive.get()) {
            WorkflowLogger.logInfo("Region inactive, skipping processing of failed events");
            return;
        }
        try {
            contextHandler.addKey(WASContextEnums.INTUIT_TID, GCConstants.FAILED_EVENT_PROCESSOR_TID);
            List<DownStreamConfig> downStreamConfigs = offeringConfig.getDownstreamServices();
            downStreamConfigs.forEach(downStreamConfig -> {
                gcThreadPool.submit(() -> processErroredEventsForOfferings(downStreamConfig));
            });
        } finally {
            contextHandler.clear();
        }
    }

    @VisibleForTesting
    void processErroredEventsForOfferings(DownStreamConfig downStreamConfig) {
        try{
            EventingLoggerUtil.logInfo(
                    "Fetching records for offering id %s from gc_inbox for processing",
                    this.getClass().getSimpleName(), downStreamConfig.getOfferingId());
            WASContext.setOfferingId(downStreamConfig.getOfferingId());
            List<ErroredEvent> erroredEvents = gcAdapter.getErroredEvents();
            erroredEvents.forEach(erroredEvent -> {
                UUID inboxRecordId = erroredEvent.getId();
                MessageHeaders headers = erroredEvent.getMessageHeaders();
                gcThreadPool.submit(() -> processErroredEvent(inboxRecordId, headers, erroredEvent, downStreamConfig.getOfferingId()));
            });
        } finally {
            contextHandler.clear();
        }
    }

    /**
     * Processes a single errored event by attempting to transform and execute it.
     * Handles retry logic and updates the event status accordingly.
     *
     * @param inboxRecordId The UUID of the inbox record.
     * @param headers The message headers associated with the event.
     * @param erroredEvent The errored event to be processed.
     */
    @VisibleForTesting
    void processErroredEvent(UUID inboxRecordId, MessageHeaders headers, ErroredEvent erroredEvent, String offeringId) {
        HashMap<String, Object> metaData = EventUtil.getGCMetadata(headers.get(EventHeaderConstants.KAFKA_TOPIC_NAME).toString(), gcConfig.getConsumerGroup());
        try {
            EventUtil.populateEventContext(contextHandler, EventUtil.transformHeader(headers));
            WASContext.setOfferingId(offeringId);
            if(erroredEvent.getRetryCount() > gcConfig.getMaxRetries()) {
                handleMaxRetries(inboxRecordId, headers, erroredEvent, metaData);
                return;
            }
            EventingLoggerUtil.logInfo(
                    "Processing Event recordId=%s eventEntityType=%s retryCount=%s maxRetries=%s",
                    this.getClass().getSimpleName(), inboxRecordId, headers.get(EventHeaderConstants.ENTITY_TYPE), erroredEvent.getRetryCount(), gcConfig.getMaxRetries());

            WorkflowEventHandlers.getHandler(EventEntityType.valueOf(headers.get(EventHeaderConstants.ENTITY_TYPE).toString()))
                    .transformAndExecute(erroredEvent.getPayload(), headers);
            gcAdapter.updateEvent(inboxRecordId, Status.SUCCESS, null);
        } catch (WorkflowRetriableException retryableException) {
            EventingLoggerUtil.logError(
                    "Retryable exception occurred while processing the recordId=%s exception=%s eventEntityType=%s",
                    retryableException,
                    this.getClass().getSimpleName(), inboxRecordId,
                    ExceptionUtils.getStackTrace(retryableException),
                    headers.get(EventHeaderConstants.ENTITY_TYPE), retryableException);
            gcAdapter.updateEvent(inboxRecordId, Status.ERROR, retryableException);
        } catch (Exception exception) {
            EventingLoggerUtil.logError(
                    "Exception occurred while processing the recordId=%s exception=%s eventEntityType=%s",
                    exception, this.getClass().getSimpleName(), inboxRecordId,
                    ExceptionUtils.getStackTrace(exception),
                    headers.get(EventHeaderConstants.ENTITY_TYPE));
            gcAdapter.updateInboxRecordAndCreateFailedEvent(inboxRecordId, headers, metaData, erroredEvent.getPayload(), exception);
        } finally {
            contextHandler.clear();
        }
    }

    /**
     * Handles the scenario where the maximum number of retries has been reached for an errored event.
     * Updates the event status to failed and logs the error.
     *
     * @param inboxRecordId The UUID of the inbox record.
     * @param headers The message headers associated with the event.
     * @param erroredEvent The errored event that has reached the maximum retries.
     * @param metaData Additional metadata associated with the event.
     */
    private void handleMaxRetries(UUID inboxRecordId, MessageHeaders headers, ErroredEvent erroredEvent, HashMap<String, Object> metaData) {
        EventingLoggerUtil.logError(
                "Max retries reached for recordId=%s eventEntityType=%s retryCount=%s maxRetries=%s",
                this.getClass().getSimpleName(), inboxRecordId, headers.get(EventHeaderConstants.ENTITY_TYPE), erroredEvent.getRetryCount(), gcConfig.getMaxRetries());
        gcAdapter.updateInboxRecordAndCreateFailedEvent(inboxRecordId, erroredEvent.getMessageHeaders(), metaData, erroredEvent.getPayload(), ObjectUtils.isEmpty(erroredEvent.getCause().getCause()) ? new WorkflowGeneralException(WorkflowError.ERROR_CAUSE_NOT_FOUND) : erroredEvent.getCause().getCause());
    }

    @Override
    public void start() {
        isActive.set(true);
    }

    @Override
    public void stop() {
        isActive.set(false);
    }

    @Override
    public boolean isActive() {
        return isActive.get();
    }
}
