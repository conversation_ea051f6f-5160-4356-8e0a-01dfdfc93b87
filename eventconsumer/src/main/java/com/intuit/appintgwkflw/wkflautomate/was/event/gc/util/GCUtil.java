package com.intuit.appintgwkflw.wkflautomate.was.event.gc.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants.EVENT_ENTITY_TYPE;

/**
 * This class is a utility class for GC.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class GCUtil {
    private final WASContextHandler contextHandler;
    private final GCConfig gcConfig;
    private final FeatureFlagManager featureFlagManager;

    /**
     * This method checks if the GC is enabled for the given eventEntityType.
     *
     * @param eventEntityType eventEntityType
     * @return true if GC is enabled for the given eventEntityType.
     */
    public boolean isGCEnabled(EventEntityType eventEntityType){
        return gcConfig.isServiceEnabled() && evaluate(eventEntityType);
    }

    /**
     * @param eventEntityType eventEntityType
     * @return true if message needs to be dropped or processed.
     */
    private boolean evaluate(EventEntityType eventEntityType) {
        String featureFlagName = WorkflowConstants.GC_ERROR_HANDLING_ENABLED_FF;
        Map<String, Object> contextMap = getContextMap(eventEntityType.getEntityType());
        EventingLoggerUtil.logInfo(
                "Evaluating FF=%s eventEntityType=%s contextMap=%s",
                this.getClass().getSimpleName(), featureFlagName, eventEntityType, contextMap
        );
        return featureFlagManager.getBooleanWithContextMap(featureFlagName, false, contextMap,
                null);
    }

    /**
     * This method return the context map for feature flag evaluation.
     *
     * @return
     */
    private Map<String, Object> getContextMap(String eventEntityType) {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put(EVENT_ENTITY_TYPE, eventEntityType);
        contextMap.put(WorkflowConstants.OFFERING_ID, contextHandler.get(WASContextEnums.OFFERING_ID));
        return contextMap;
    }
}
