package com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.async;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MaskedObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.KafkaReceivedEventHeader;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation.EventValidator;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import com.intuit.guranteedconsumer.commons.core.Enum.Status;
import io.numaproj.numaflow.sinker.Datum;
import io.numaproj.numaflow.sinker.Response;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.messaging.MessageHeaders;


/**
 * This class is responsible for processing a NumaFlow message and handling any retryable
 * exceptions. It logs events, metrics, and latencies and calls the appropriate WorkflowEventHandler
 * based on the EventEntityType
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class NumaFlowAsyncHandler implements Callable<Response> {

  private Datum datum;
  private EntityHandler entityHandler;
  private WASContextHandler contextHandler;
  private SimpleEventPublisher kafkaProducer;
  private MetricLogger metricLogger;
  private EventConsumerUtil eventConsumerUtil;
  private FilterEventUtil filterEventUtil;
  private final GCAdapter gcAdapter;
  private final GCConfig gcConfig;
  private final GCUtil gcUtil;

  /**
   * This method processes a  NumaFlow message and logs event, metric, and latency. Based on the
   * EventEntityType, it calls the appropriate WorkflowEventHandler. If a retryable exception
   * occurs, the method adds the message to the Dead Letter Queue (DLQ).
   *
   * @return The response object to indicate successful execution
   */
  @Override
  @SuppressWarnings("unchecked")
  public Response call() {
    final Instant start = Instant.now();
    processEventHeaders(datum.getHeaders());
    EventEntityType eventEntityType = getEventEntityType(datum.getHeaders());
    logEvent(datum, eventEntityType);
    // check ff is enabled or not
    if (!filterEventUtil.evaluate(eventEntityType)) {
      EventingLoggerUtil.logInfo(
          "Skipping Event consumption numaFlow=false  eventEntityType=%s",
          this.getClass().getSimpleName(), eventEntityType.getEntityType());
      contextHandler.clear();
      return Response.responseOK(datum.getId());
    }
   try {
     WorkflowEventHandlers.getHandler(eventEntityType)
         .transformAndExecute(EventUtil.convertBytesToString(datum.getValue()),
             new HashMap<>(
                 datum.getHeaders())); // datum.getHeaders() is immutable, we are updating headers in entityEventHandlers.
    } catch (WorkflowRetriableException retryableException) {
      EventingLoggerUtil.logError(
          "Retryable exception occurred while processing the messageId=%s exception=%s eventEntityType=%s",
          retryableException,
          this.getClass().getSimpleName(), datum.getId(),
          ExceptionUtils.getStackTrace(retryableException),
          eventEntityType.getEntityType());
      metricLogger.logErrorMetric(eventEntityType.geMetricName(), Type.EVENT_METRIC,
          retryableException);
     boolean isGcErrorHandlingEnabled = gcUtil.isGCEnabled(eventEntityType);
     if (isGcErrorHandlingEnabled) {
       handleRetryableExceptionWithGC(datum, eventEntityType, retryableException);
     }else{
       handleRetryableExceptionWithDlq(datum, eventEntityType);
     }
    } catch (Exception exception) {
      EventingLoggerUtil.logError(
          "Exception occurred while processing the messageId=%s exception=%s eventEntityType=%s",
          exception, this.getClass().getSimpleName(), datum.getId(),
          ExceptionUtils.getStackTrace(exception),
          eventEntityType.getEntityType());
      metricLogger.logErrorMetric(eventEntityType.geMetricName(),
          Type.EVENT_METRIC, exception);
    } finally {
      metricLogger.logLatencyMetric(eventEntityType.geMetricName(),
          Type.EVENT_METRIC, start);
      contextHandler.clear();
    }
    return Response.responseOK(datum.getId());
  }


  /**
   * This method add retryable exception to dlq
   *
   * @param datum
   * @param eventEntityType
   */
  private void handleRetryableExceptionWithDlq(Datum datum, EventEntityType eventEntityType) {
    String dlqTopicName = getDLQTopicName(datum.getHeaders());
    if (isDLQExists(eventEntityType, dlqTopicName)) {
      Map<String, String> headers = EventUtil.getDLQHeaders(datum.getHeaders(), dlqTopicName);
      EventingLoggerUtil.logInfo(
          "Publishing to DLQ for eventEntityType=%s messageId=%s headers=%s topicName=%s",
          this.getClass().getSimpleName(), eventEntityType, datum.getId(), headers, dlqTopicName);
      kafkaProducer.send(EventUtil.convertBytesToString(datum.getValue()), headers, dlqTopicName);
      return;
    }
    EventingLoggerUtil.logInfo(
        "DLQ is not found for messageId=%s eventEntityType=%s",
        this.getClass().getSimpleName(), datum.getId(), eventEntityType.getEntityType());

  }

  private void handleRetryableExceptionWithGC(Datum datum, EventEntityType eventEntityType, WorkflowRetriableException retryableException) {
    MessageHeaders headers = EventUtil.getGCHeaders(datum.getHeaders(), eventEntityType);
    UUID inboxRecordId = gcAdapter.recordEvent(headers, EventUtil.getGCMetadata(headers.get(EventHeaderConstants.KAFKA_TOPIC_NAME).toString(), gcConfig.getConsumerGroup()), EventUtil.convertBytesToString(datum.getValue()), Status.ERROR, retryableException);
    EventingLoggerUtil.logInfo(
            "Inserted record in gc inbox to retry inboxRecordId=%s eventEntityType=%s messageId=%s headers=%s",
            this.getClass().getSimpleName(), inboxRecordId, eventEntityType, datum.getId(), datum.getHeaders());
  }

  /**
   * Returns whether a DLQ exists for the given entity type and topic name.
   *
   * @param eventEntityType the EventEntityType for the DLQ
   * @param dlqTopicName    the name of the DLQ topic
   * @return whether a DLQ exists for the given entity type and topic name.
   */
  private boolean isDLQExists(EventEntityType eventEntityType, String dlqTopicName) {
    List<String> dlqTopicNames = eventConsumerUtil.getFilteredTopicList(
        eventEntityType.getEntityType(), true);
    return dlqTopicNames.stream().anyMatch(topicName -> topicName.equals(dlqTopicName));
  }

  /**
   * This method logs the events, produce to consume latency.
   *
   * @param datum
   */
  private void logEvent(Datum datum, EventEntityType eventEntityType) {
    EventingLoggerUtil.logInfo(
        "Received event for messageId=%s eventEntityType=%s",
        this.getClass().getSimpleName(), datum.getId(), eventEntityType
    );
    Map<String, String> headers = datum.getHeaders();
    KafkaReceivedEventHeader kafkaReceivedEventHeader = ObjectConverter.convertObject(headers,
        KafkaReceivedEventHeader.class);
    String maskedHeaders = MaskedObjectConverter.toJson(kafkaReceivedEventHeader);
    EventingLoggerUtil.logInfo(
        "Received event with headers=%s timeStamp=%s timeStampType=%s eventEntityType=%s",
        this.getClass().getSimpleName(), maskedHeaders,
        headers.get(EventHeaderConstants.KAFKA_TIMESTAMP),
        headers.get(EventHeaderConstants.KAFKA_TIMESTAMP_TYPE), eventEntityType.getEntityType());
    EventingLoggerUtil.logInfo(
        "Time difference between produce and consume time=%s eventEntityType=%s",
        this.getClass().getSimpleName(), (
            (Instant.now().getEpochSecond() - datum.getEventTime().getEpochSecond()) * 1000),
        eventEntityType.getEntityType());
  }

  /**
   * This method populate event context, validate and set offering id.
   *
   * @param headers
   */
  private void processEventHeaders(Map<String, String> headers) {
    EventUtil.populateEventContext(contextHandler, headers);
    EventValidator.validate(headers);
    EventUtil.setOfferingId(headers);
  }

  /**
   * This method return the eventEntityType using kafka topic name.
   *
   * @param headers
   * @return
   */
  private EventEntityType getEventEntityType(Map<String, String> headers) {
    return EventEntityType.valueOfEntity(
        entityHandler.getEntityType(headers.get(EventHeaderConstants.KAFKA_TOPIC_NAME)));
  }

  /**
   * This method constructs the dlq name from the given Kafka Topic Name.
   *
   * @param headers
   * @return
   */
  private String getDLQTopicName(Map<String, String> headers) {
    return headers.get(
        EventHeaderConstants.KAFKA_TOPIC_NAME).concat(KafkaConstants.KAFKA_DLQ_SUFFIX);
  }
}
