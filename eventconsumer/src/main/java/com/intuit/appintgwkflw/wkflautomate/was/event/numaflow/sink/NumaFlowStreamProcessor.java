package com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.sink;


import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricType;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.async.NumaFlowAsyncHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import io.numaproj.numaflow.sinker.Datum;
import io.numaproj.numaflow.sinker.DatumIterator;
import io.numaproj.numaflow.sinker.Response;
import io.numaproj.numaflow.sinker.ResponseList;
import io.numaproj.numaflow.sinker.Sinker;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import io.numaproj.numaflow.sinker.Server;

/**
 * <AUTHOR>
 */

/**
 * This class is responsible for processing a stream of NumaFlow messages.
 */
@Component
@ConditionalOnProperty(name = "numaflow.handler", havingValue = "was-sink")
public class NumaFlowStreamProcessor extends Sinker {

  private final EntityHandler entityHandler;
  private final WASContextHandler contextHandler;
  private final ThreadPoolExecutor numaflowThreadPool;
  private final SimpleEventPublisher kafkaProducer;
  private final MetricLogger metricLogger;

  private final EventConsumerUtil eventConsumerUtil;

  private final FilterEventUtil filterEventUtil;
  private final GCAdapter gcAdapter;
  private final GCConfig gcConfig;
  private final GCUtil gcUtil;

  private final Server server;


  public NumaFlowStreamProcessor(EntityHandler entityHandler, WASContextHandler contextHandler,
      @Qualifier(WorkflowConstants.NUMA_EXECUTOR_THREAD_BEAN) ThreadPoolExecutor numaflowThreadPool,
      SimpleEventPublisher kafkaProducer, MetricLogger metricLogger,
      EventConsumerUtil eventConsumerUtil, FilterEventUtil filterEventUtil, GCAdapter gcAdapter, GCConfig gcConfig, GCUtil gcUtil) {
    this.entityHandler = entityHandler;
    this.contextHandler = contextHandler;
    this.numaflowThreadPool = numaflowThreadPool;
    this.kafkaProducer = kafkaProducer;
    this.metricLogger = metricLogger;
    this.eventConsumerUtil = eventConsumerUtil;
    this.filterEventUtil = filterEventUtil;
    this.gcAdapter = gcAdapter;
    this.gcConfig = gcConfig;
    this.gcUtil = gcUtil;
    server = new Server(this);
  }

  /**
   * This method starts the NumaFlow server
   *
   * @throws Exception
   */
  public void startServer() throws Exception {
    EventingLoggerUtil.logInfo(
        "Starting NumaFlow Server", this.getClass().getSimpleName());
    server.start();
    EventingLoggerUtil.logInfo(
        "Started NumaFlow Server", this.getClass().getSimpleName());
  }

  /**
   * This method stops the NumaFlow server
   *
   * @throws Exception
   */
  public void stopServer() throws Exception {
    EventingLoggerUtil.logInfo(
        "Stopping NumaFlow Server", this.getClass().getSimpleName());
    server.stop();
  }


  /**
   * This method process all the stream of NumaFlow messages and execute them parallelly and wait
   * for results of all the messages to get completed.
   *
   * @param datumIterator
   * @return
   */
  @Override
  public ResponseList processMessages(DatumIterator datumIterator) {
    final Instant start = Instant.now();
    ResponseList.ResponseListBuilder responseListBuilder = ResponseList.newBuilder();
    EventingLoggerUtil.logInfo(
        "Start Processing a stream of NumaFlow messages", this.getClass().getSimpleName());

    Map<Future<Response>, String> futureMessageIdMap = new HashMap<>();
    // Process messages asynchronously and get the list of futures
    List<Future<Response>> futureList = processMessageStream(datumIterator,
        futureMessageIdMap);
    // Get response of all the futures
    futureList.forEach(future -> {
      EventingLoggerUtil.logInfo(
          "Getting message response", this.getClass().getSimpleName());
      try {
        responseListBuilder.addResponse(future.get());
      } catch (ExecutionException e) {
        // All ExecutionException are non-retryable exception, so we are logging and ack.
        EventingLoggerUtil.logError(
            "Execution Exception Occurred while getting response for messageId=%s", e,
            this.getClass().getSimpleName(), futureMessageIdMap.get(future));
        if (EventUtil.isWorkflowEventException(e.getCause())) {
          EventingLoggerUtil.logError(
              "Workflow Event Execution Exception Occurred while getting response for messageId=%s",
              e,
              this.getClass().getSimpleName(), futureMessageIdMap.get(future));
          responseListBuilder.addResponse(
              Response.responseFailure(futureMessageIdMap.get(future), e.getMessage()));
        } else {
          responseListBuilder.addResponse(Response.responseOK(futureMessageIdMap.get(future)));
        }

      } catch (InterruptedException e) {
        // For InterruptedException we have to add failure for those messages id, otherwise data will be lost.
        EventingLoggerUtil.logError(
            "Interrupted Exception Occurred while getting response for messageId=%s", e,
            this.getClass().getSimpleName(), futureMessageIdMap.get(future));
        responseListBuilder.addResponse(
            Response.responseFailure(futureMessageIdMap.get(future), e.getMessage()));
      }
    });
    EventingLoggerUtil.logInfo(
        "metricName=%s type=%s metricType=%s time=%s count=%s",
        this.getClass().getSimpleName(), MetricName.NUMAFLOW_MESSAGES_PROCESS,
        Type.EVENT_METRIC.getValue(), MetricType.LATENCY,
        ((Instant.now().getEpochSecond() - start.getEpochSecond()) * 1000), futureList.size());
    return responseListBuilder.build();
  }

  /**
   * This method processes a stream of NumaFlow messages and returns a list of future responses for
   * each message processed.
   *
   * @param datumIterator      The iterator for the stream of NumaFlow messages
   * @param futureMessageIdMap A map linking each future response to its corresponding message ID
   * @return The list of future responses for the processed messages
   */
  private List<Future<Response>> processMessageStream(DatumIterator datumIterator,
      Map<Future<Response>, String> futureMessageIdMap) {
    // Create an empty list of future responses
    List<Future<Response>> futureList = new ArrayList<>();

    // Process all the messages
    while (true) {
      try {
        // Get the next datum
        final Datum datum = datumIterator.next();
        if (datum == null) {
          // If there are no more datums, break the loop
          EventingLoggerUtil.logInfo(
              "There is no more datum breaking the loop",
              this.getClass().getSimpleName());
          break;
        }

        EventingLoggerUtil.logInfo(
            "Adding Message to threadPool messageId=%s, activeCount=%s",
            this.getClass().getSimpleName(), datum.getId(),
            numaflowThreadPool.getActiveCount());
        // Submit a new ExecuteMessageAsync task to the thread pool and get the future response
        Future<Response> responseFuture = numaflowThreadPool.submit(
            new NumaFlowAsyncHandler(datum, entityHandler, contextHandler, kafkaProducer,
                metricLogger, eventConsumerUtil, filterEventUtil, gcAdapter, gcConfig, gcUtil));
        futureMessageIdMap.put(responseFuture, datum.getId());
        // Add the future response to the list
        futureList.add(responseFuture);
      } catch (InterruptedException e) {
        EventingLoggerUtil.logError(
            "Interrupted exception while submitting message", e,
            this.getClass().getSimpleName());
        //If exceptions are thrown, container will be restarted and messages will not be acked.
        // Hence, the source will redeliver those messages. Therefore, No data loss
        throw new WorkflowGeneralException(WorkflowError.INTERNAL_EXCEPTION, e);

      }
    }

    // Return the list of future responses
    return futureList;
  }


}
