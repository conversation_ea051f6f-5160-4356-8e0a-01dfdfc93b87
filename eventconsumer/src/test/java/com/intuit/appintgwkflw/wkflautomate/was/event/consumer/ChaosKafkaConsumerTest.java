package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.ExternalTaskEventHandler;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import com.intuit.eventbus.utils.Result.Failure;
import com.intuit.eventbus.utils.Result.Success;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class ChaosKafkaConsumerTest {

    @Mock private Acknowledgment acknowledgment;
    @Mock private ExternalTaskEventHandler externalTaskEventHandler;
    private ExternalTaskChaosKafkaConsumer consumer;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        consumer = new ExternalTaskChaosKafkaConsumer(externalTaskEventHandler);
    }

    @Test
    public void testReceive() throws Exception {
        consumer.receive(
                getSuccessMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
        try {
            Mockito.verify(externalTaskEventHandler, times(1)).transformAndExecute(any(), any());
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test(expected = Exception.class)
    public void testReceiveFormatException() throws Exception {
        consumer.receive(
                getFailureMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
        Mockito.verify(externalTaskEventHandler, never()).transformAndExecute(any(), any());
    }

    @Test
    public void testReceiveWithNullAndByteValues() {
        Map<String, Object> map = new HashMap<>();
        map.put("test", null);
        map.put("entityId", "abc".getBytes());
        map.put("partition", "10");
        consumer.receive(getSuccessMessage(), new MessageHeaders(map), acknowledgment);
        Mockito.verify(externalTaskEventHandler, times(1)).transformAndExecute(any(), any());
    }

    private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
        Result<FormatException, String> body = new Success<FormatException, String>("value");
        return new ConsumerRecord<>(
                "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
    }

    private ConsumerRecord<String, Result<FormatException, String>> getFailureMessage() {
        Result<FormatException, String> body =
                new Failure<FormatException, String>(new FormatException());
        return new ConsumerRecord<>(
                "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
    }
}
