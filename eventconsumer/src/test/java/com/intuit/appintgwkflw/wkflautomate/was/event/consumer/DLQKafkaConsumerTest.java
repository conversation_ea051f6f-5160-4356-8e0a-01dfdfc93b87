package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import static org.mockito.Mockito.never;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.dlq.ExternalTaskDLQKafkaConsumer;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;

@RunWith(MockitoJUnitRunner.class)
public class DLQKafkaConsumerTest {
  @Mock private Acknowledgment acknowledgment;
  @Mock private EventProcessor eventProcessor;
  @InjectMocks private ExternalTaskDLQKafkaConsumer consumer;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testMessageReceive() throws Exception {
    consumer.receive(
        getSuccessMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
    try {
      Mockito.verify(eventProcessor).process(Mockito.any(), Mockito.any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = Exception.class)
  public void testMessageReceiveFormatException() throws Exception {
    consumer.receive(
        getFailureMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
    Mockito.verify(eventProcessor, never()).process(Mockito.any(), Mockito.any());
  }

  private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
    Result<FormatException, String> body = new Result.Success<FormatException, String>("value");
    return new ConsumerRecord<>(
        "topic-dlq", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
  }

  private ConsumerRecord<String, Result<FormatException, String>> getFailureMessage() {
    Result<FormatException, String> body =
        new Result.Failure<FormatException, String>(new FormatException());
    return new ConsumerRecord<>(
        "topic-dlq", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
  }

  @Test
  public void testMessageReceiveWithNullAndByteValues() {
    Map<String, Object> map = new HashMap<>();
    map.put("test", null);
    map.put("entityId", "abc".getBytes());
    map.put("partition", "12");
    consumer.receive(getSuccessMessage(), new MessageHeaders(map), acknowledgment);
    Mockito.verify(eventProcessor).process(Mockito.any(), Mockito.any());
  }
}
