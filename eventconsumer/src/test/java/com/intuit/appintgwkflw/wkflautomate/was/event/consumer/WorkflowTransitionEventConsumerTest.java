package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import com.intuit.eventbus.utils.Result.Failure;
import com.intuit.eventbus.utils.Result.Success;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.never;

@RunWith(SpringRunner.class)
public class WorkflowTransitionEventConsumerTest {

  @Mock private Acknowledgment acknowledgment;
  @Mock private EventProcessor eventProcessor;
  @InjectMocks private WorkflowTransitionEventsKafkaConsumer consumer;
  @Mock private FilterEventUtil filterEventUtil;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRecieve() throws Exception {
    consumer.receive(
        getSuccessMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
    try {
      Mockito.verify(eventProcessor).process(Mockito.any(), Mockito.any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test(expected = Exception.class)
  public void testRecieveFormatException() throws Exception {
    consumer.receive(
        getFailureMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
    Mockito.verify(eventProcessor, never()).process(Mockito.any(), Mockito.any());
  }

  private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
    Result<FormatException, String> body = new Success<FormatException, String>("value");
    return new ConsumerRecord<>(
        "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
  }

  private ConsumerRecord<String, Result<FormatException, String>> getFailureMessage() {
    Result<FormatException, String> body =
        new Failure<FormatException, String>(new FormatException());
    return new ConsumerRecord<>(
        "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
  }

  @Test
  public void testRecieveWithNullAndByteValues() {
    Map<String, Object> map = new HashMap<>();
    map.put("test", null);
    map.put("entityId", "abc".getBytes());
    map.put("partition", "10");
    consumer.receive(getSuccessMessage(), new MessageHeaders(map), acknowledgment);
    Mockito.verify(eventProcessor).process(Mockito.any(), Mockito.any());
  }

  @Test
  public void testReceiveFFEnabled() {
    Mockito.when(filterEventUtil.evaluate(EventEntityType.WORKFLOW_TRANSITION_EVENTS)).thenReturn(true);
    consumer.receive(
        getSuccessMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
    Mockito.verify(eventProcessor, Mockito.times(0))
        .process(Mockito.any(), Mockito.anyString());
  }
}
