package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ConsumerRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConsumer;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventRegionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.kafka.serde.Deserializer;
import com.intuit.eventbus.utils.Result;
import com.intuit.platform.jsk.config.client.idps.autoconfig.IdpsConnectionProperties;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class KafkaPropertiesConfigTest {

  private EventConfiguration eventConfiguration;
  private EventConsumer eventConsumer;
  private KafkaPropertiesConfig kafkaPropertiesConfig;
  private IdpsConnectionProperties idpsConnectionProperties;
  private ConsumerRetryConfig consumerRetryConfig;

  @Before
  public void setup() {
    eventConfiguration = new EventConfiguration();
    EventRegionConfig eventRegionConfig = new EventRegionConfig();
    eventRegionConfig.setBootStrapServers(Arrays.asList("abc"));
    eventConfiguration.setPrimary(eventRegionConfig);
    eventConfiguration.setSecurityProtocol("ssl");
    eventConfiguration.setSslEnabledProtocol("tls");

    idpsConnectionProperties = new IdpsConnectionProperties();
    idpsConnectionProperties.setApiKeyId("v2-e3b373bd2284e");
    idpsConnectionProperties.setApiSecretKey("../idps_config/key_v2-e3b373bd2284e.pem");
    idpsConnectionProperties.setEndpoint("kafkainfra-pre-production-l10maf.pd.idps.a.intuit.com");
    eventConfiguration.setIdpsConfig(idpsConnectionProperties);

    eventConfiguration.setConsumer(new EventConsumer());

    kafkaPropertiesConfig = new KafkaPropertiesConfig(eventConfiguration);
    consumerRetryConfig = new ConsumerRetryConfig();
  }

  @Test
  public void testInvalidIdpsPropertiesInitialisation() {
    try {
      eventConfiguration.setIdpsConfig(new IdpsConnectionProperties());
      eventConsumer = new EventConsumer();
      eventConsumer.setConfig(new HashMap<>());
      eventConfiguration.setConsumer(eventConsumer);
      kafkaPropertiesConfig = new KafkaPropertiesConfig(eventConfiguration);
      kafkaPropertiesConfig.consumerFactory();
    } catch (IllegalArgumentException e) {
      Assert.assertNotNull(e.getMessage());
    }
  }

  @Test
  public void testNullEventConfiguration() {
    try {
      kafkaPropertiesConfig = new KafkaPropertiesConfig(null);
      kafkaPropertiesConfig.consumerFactory();
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains(KafkaConstants.EVENT_CONFIGURATION_NULL_ERROR));
    }
  }

  @Test
  public void testNullEventConsumer() {
    try {
      eventConfiguration.setConsumer(null);
      kafkaPropertiesConfig = new KafkaPropertiesConfig(eventConfiguration);
      kafkaPropertiesConfig.consumerFactory();
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains(KafkaConstants.CONSUMER_EVENT_CONFIGURATION_NULL_ERROR));
    }
  }

  @Test
  public void testNullEventConsumerConfig() {
    eventConsumer = new EventConsumer();
    eventConsumer.setAutoOffsetReset("something");
    eventConsumer.setGroupId("something");
    eventConfiguration.setConsumer(eventConsumer);
    kafkaPropertiesConfig = new KafkaPropertiesConfig(eventConfiguration);
    KafkaPropertiesConfig kafkaPropertiesConfigSpy = Mockito.spy(kafkaPropertiesConfig);
    Mockito.doReturn(new Deserializer<>()).when(kafkaPropertiesConfigSpy).getValueDeserializer();

    ConsumerFactory<String, Result<FormatException, String>> consumerFactory =
        kafkaPropertiesConfigSpy.consumerFactory();
    Assert.assertEquals(
        Boolean.FALSE.toString(),
        consumerFactory.getConfigurationProperties().get(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG));
    Assert.assertEquals(
        "ssl",
        consumerFactory.getConfigurationProperties().get(KafkaConstants.SECURITY_PROTOCOL_CONFIG));
    Assert.assertEquals(
        "tls",
        consumerFactory
            .getConfigurationProperties()
            .get(KafkaConstants.SSL_ENABLED_PROTOCOLS_CONFIG));
    Assert.assertNotNull(
        consumerFactory.getConfigurationProperties().get(ConsumerConfig.GROUP_ID_CONFIG));
    Assert.assertNotNull(
            consumerFactory.getConfigurationProperties().get(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG));
    Assert.assertEquals(StringDeserializer.class, consumerFactory.getKeyDeserializer().getClass());
  }

  @Test
  public void testConsumerFactoryCreation() {
    eventConsumer = new EventConsumer();
    Map<String, String> eventConfig = new HashMap<>();
    eventConfig.put("enable.auto.commit", "false");
    eventConfig.put("security.protocol", "ssl");
    eventConfig.put("ssl.enabled.protocols", "tls");
    eventConsumer.setConfig(eventConfig);
    eventConfiguration.setConsumer(eventConsumer);
    kafkaPropertiesConfig = new KafkaPropertiesConfig(eventConfiguration);
    KafkaPropertiesConfig kafkaPropertiesConfigSpy = Mockito.spy(kafkaPropertiesConfig);
    Mockito.doReturn(new Deserializer<>()).when(kafkaPropertiesConfigSpy).getValueDeserializer();

    ConsumerFactory<String, Result<FormatException, String>> consumerFactory =
        kafkaPropertiesConfigSpy.consumerFactory();
    Assert.assertEquals(
        Boolean.FALSE.toString(),
        consumerFactory.getConfigurationProperties().get(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG));
    Assert.assertEquals(
        "ssl",
        consumerFactory.getConfigurationProperties().get(KafkaConstants.SECURITY_PROTOCOL_CONFIG));
    Assert.assertEquals(
        "tls",
        consumerFactory
            .getConfigurationProperties()
            .get(KafkaConstants.SSL_ENABLED_PROTOCOLS_CONFIG));
    Assert.assertNull(
        consumerFactory.getConfigurationProperties().get(ConsumerConfig.GROUP_ID_CONFIG));
    Assert.assertEquals(StringDeserializer.class, consumerFactory.getKeyDeserializer().getClass());
  }

  @Test
  public void testNullRetryConfig() {
    try {
      kafkaPropertiesConfig.retryTemplate();
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(e.getMessage().contains(KafkaConstants.CONSUMER_RETRY_CONFIG_NULL_ERROR));
    }
  }

  @Test
  public void testDLQRetryTemplateCreation() {
    consumerRetryConfig.setDlqBackoffInitialInterval(2000);
    consumerRetryConfig.setDlqBackoffMultiplier(2);
    consumerRetryConfig.setDlqBackoffMaxInterval(5000);
    consumerRetryConfig.setDlqRetryMaxAttempts(3);
    eventConsumer = new EventConsumer();
    eventConsumer.setRetryConfig(consumerRetryConfig);
    eventConfiguration.setConsumer(eventConsumer);
    RetryTemplate retryTemplate = kafkaPropertiesConfig.dlqRetryTemplate();
    try {
      retryTemplate.execute(new RetryCallback<Object, Exception>() {
        @Override
        public Object doWithRetry(RetryContext context) throws Exception {
          return "success";
        }
      });
    }
    catch (Exception ex){
      Assert.fail();
    }
  }
}
