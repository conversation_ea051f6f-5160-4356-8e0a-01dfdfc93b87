package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.context.ContextConfiguration;
import junit.framework.Assert;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
@ContextConfiguration(classes = EventConfiguration.class)
public class EntityHandlerTest {

  @Mock private WorkflowEventHandler externalTaskEventHandler;

  @Mock private EventConsumerUtil eventConsumerUtil;
  @InjectMocks private EntityHandler entityHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    WorkflowEventHandlers.addHandler(EventEntityType.EXTERNALTASK, externalTaskEventHandler);

    Map<String, String> topicEntityTypeMap = new HashMap<>();
    topicEntityTypeMap.putIfAbsent("workflow-externaltask-complete-vep", "externalTask");
    topicEntityTypeMap.putIfAbsent("workflow-trigger-complete-vep", "task");

    Mockito.when(eventConsumerUtil.getTopicToEntityTypeMapping()).thenReturn(topicEntityTypeMap);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingEventHeaders() {
    Map<String, String> header = new HashMap<>();
    entityHandler.getEntityType(header);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingTopicEntityTypeMapping() throws Exception {
    Map<String, String> headers = new HashMap<>();
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, "workflow-externaltask-vep");
    entityHandler.getEntityType(headers);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testMissingEventHandler() throws Exception {
    Map<String, String> headers = new HashMap<>();
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, "workflow-trigger-complete-vep");
    entityHandler.getEntityType(headers);
  }

  @Test
  public void testSuccess() {
    Map<String, String> headers = new HashMap<>();
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, "workflow-externaltask-complete-vep");
    String response = entityHandler.getEntityType(headers);
    Assert.assertEquals("externalTask", response);
  }

  @Test
  public void testSuccessWithTopicName() {
    String response = entityHandler.getEntityType("workflow-externaltask-complete-vep");
    Assert.assertEquals("externalTask", response);
  }
}
