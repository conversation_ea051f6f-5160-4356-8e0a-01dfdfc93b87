package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConsumer;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.ExternalTaskEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.ContextConfiguration;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
@ContextConfiguration(classes = EventConfiguration.class)
public class EventProcessorTest {

  @InjectMocks private EventProcessor eventProcessor;

  @Mock private EventConfiguration eventConfiguration;

  @Mock private EventConsumer eventConsumer;

  @Mock private EntityHandler entityHandler;

  @Mock private ExternalTaskEventHandler externalTaskEventHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    WorkflowEventHandlers.addHandler(EventEntityType.EXTERNALTASK, externalTaskEventHandler);
    Mockito.when(entityHandler.getEntityType(Mockito.anyMap())).thenReturn("externalTask");

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFailure() throws Exception {
    String event = "sample event";
    Map<String, Object> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OFFERING_ID, "value");
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, "workflow-externaltask-vep");
    MessageHeaders messageHeaders = new MessageHeaders(headers);
    Mockito.when(entityHandler.getEntityType(Mockito.anyMap()))
        .thenThrow(WorkflowGeneralException.class);

    eventProcessor.process(messageHeaders, event);
  }

  @Test
  public void testSuccess() {
    String event = "sample event";
    Map<String, Object> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OFFERING_ID, "value");
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, "workflow-externaltask-complete-vep");
    MessageHeaders messageHeaders = new MessageHeaders(headers);
    
    eventProcessor.process(messageHeaders, event);
    Mockito.verify(externalTaskEventHandler, times(1)).transformAndExecute(any(), any());
  }
}
