package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.resiliencyConfig;

import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.SpringContext;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class DLQResumeJobSchedulerTest {

    @Mock private KafkaConsumerStarter kafkaConsumerStarter;
    @Mock private WASCircuitBreakerService wasCircuitBreakerService;
    @Mock private ScheduledAnnotationBeanPostProcessor postProcessor;
    @InjectMocks
    private DLQResumeJobScheduler dlqResumeJobScheduler;

    @Before
    public void setup() {
        AnnotationConfigApplicationContext ac = new AnnotationConfigApplicationContext();
        ac.registerBean(DLQResumeJobScheduler.class, kafkaConsumerStarter, wasCircuitBreakerService, postProcessor);
        SpringContext sc = new SpringContext();
        sc.setApplicationContext(ac);
        ac.refresh();
    }

    @Test
    public void testCronWhenCircuitsClosed() {
        Mockito.when(wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(Mockito.any())).thenReturn(true);
        dlqResumeJobScheduler.checkDLQStatus();
        Mockito.verify(wasCircuitBreakerService, Mockito.times(0)).closeAllCircuitBreakersOfActionType(Mockito.any());
        Object running = ReflectionTestUtils.getField(dlqResumeJobScheduler, "dlqCronRunning");
        Assert.assertEquals("false", running.toString());
    }

    @Test
    public void testCronWhenCircuitOpenButMaxDurationNotReached() {
        Mockito.when(wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(Mockito.any())).thenReturn(false);
        ReflectionTestUtils.setField(dlqResumeJobScheduler, "MAX_DLQ_PAUSE_COUNT", 3);

        dlqResumeJobScheduler.checkDLQStatus();

        Mockito.verify(wasCircuitBreakerService, Mockito.times(0)).closeAllCircuitBreakersOfActionType(Mockito.any());
        Object running = ReflectionTestUtils.getField(dlqResumeJobScheduler, "dlqCronRunning");
        Assert.assertEquals("true", running.toString());
    }

    @Test
    public void testCronWhenCircuitOpenAndMaxDurationReached() {
        Mockito.when(wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(Mockito.any())).thenReturn(false);
        ReflectionTestUtils.setField(dlqResumeJobScheduler, "MAX_DLQ_PAUSE_COUNT", 3);

        dlqResumeJobScheduler.checkDLQStatus();
        dlqResumeJobScheduler.checkDLQStatus();
        dlqResumeJobScheduler.checkDLQStatus();
        dlqResumeJobScheduler.checkDLQStatus();

        Mockito.verify(wasCircuitBreakerService, Mockito.times(1)).closeAllCircuitBreakersOfActionType(Mockito.any());
        Object running = ReflectionTestUtils.getField(dlqResumeJobScheduler, "dlqCronRunning");
        Assert.assertEquals("false", running.toString());
    }

    @Test
    public void testStartScheduler() {
        dlqResumeJobScheduler.stopScheduler(); // to set dlqCronRunning to false before running startScheduler()
        dlqResumeJobScheduler.startScheduler();
        Object running = ReflectionTestUtils.getField(SpringContext.getApplicationContext().getBean(DLQResumeJobScheduler.class), "dlqCronRunning");
        Assert.assertEquals("true", running.toString());
    }

    @Test
    public void testStopScheduler() {
        dlqResumeJobScheduler.stopScheduler();
        Object running = ReflectionTestUtils.getField(dlqResumeJobScheduler, "dlqCronRunning");
        Assert.assertEquals("false", running.toString());
    }
}
