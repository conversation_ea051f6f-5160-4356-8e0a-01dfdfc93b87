package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.resiliencyConfig;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskCompleteEventCBHandlerTest {

    @Mock
    private KafkaConsumerStarter kafkaConsumerStarter;
    @Mock
    private WASCircuitBreakerService wasCircuitBreakerService;
    @Mock
    private DLQResumeJobScheduler dlqResumeJobScheduler;
    @InjectMocks
    private ExternalTaskCompleteEventCBHandler cbHandler;

    @Test
    public void testGetName() {
        Assert.assertEquals
                (CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT, cbHandler.getName());
    }

    @Test
    public void testOpenToHalfOpen() {
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.OPEN_TO_HALF_OPEN);
        Mockito.verify(kafkaConsumerStarter, times(0)).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testCloseToOpen() {
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.CLOSED_TO_OPEN);
        Mockito.verify(kafkaConsumerStarter).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testHalfOpenToOpen() {
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.OPEN_TO_HALF_OPEN);
        Mockito.verify(kafkaConsumerStarter, times(0)).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testToCloseWhenAllCBsClosed() {
        Mockito.when(wasCircuitBreakerService
                .areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT)).thenReturn(true);
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.HALF_OPEN_TO_CLOSED);
        Mockito.verify(kafkaConsumerStarter).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testToCloseWhenAllCBsNotClosed() {
        Mockito.when(wasCircuitBreakerService
                .areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT)).thenReturn(false);
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.HALF_OPEN_TO_CLOSED);
        Mockito.verify(kafkaConsumerStarter, times(0)).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testToDisabled() {
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.HALF_OPEN_TO_DISABLED);
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.OPEN_TO_DISABLED);
        cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.CLOSED_TO_DISABLED);
        Mockito.verify(kafkaConsumerStarter, Mockito.times(3)).resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
        Mockito.verify(kafkaConsumerStarter, times(0)).pauseConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
    }

    @Test
    public void testNoExceptionThrownFromHandler() {
        try {
            doThrow(new WorkflowGeneralException("testexception")).when(kafkaConsumerStarter).resumeConsumer(Mockito.anyString());
            cbHandler.handleCircuitBreakerStateTransitionEvent(CircuitBreaker.StateTransition.HALF_OPEN_TO_DISABLED);
        }
        catch (Exception e){
            Assert.fail();
        }
    }
}
