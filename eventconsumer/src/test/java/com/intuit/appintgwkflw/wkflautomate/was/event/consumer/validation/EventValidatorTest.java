package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import java.util.HashMap;
import org.junit.Assert;
import org.junit.Test;

public class EventValidatorTest {

  @Test(expected = WorkflowGeneralException.class)
  public void testValidateHeadersMissingTid() throws Exception {

    HashMap<String, String> headerMap = new HashMap<String, String>();
    headerMap.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotency");
    EventValidator.validate(headerMap);
  }

  @Test
  public void testValidateHeadersMissingTidWithEntityId() throws Exception {

    HashMap<String, String> headerMap = new HashMap<String, String>();
    headerMap.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotency");
    headerMap.put(EventHeaderConstants.ENTITY_ID, "test:worker");
    try {
      EventValidator.validate(headerMap);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      String errorMsg = String.format(WorkflowError.MISSING_EVENT_HEADERS.getErrorMessage(),
          String.format(ValidationError.MISSING_INTUIT_TID_HEADER.getErrorMessage(),
              headerMap.get(EventHeaderConstants.ENTITY_ID)));
      Assert.assertEquals(e.getMessage(), errorMsg);
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testValidateHeadersMissingIdempotencyKey() throws Exception {

    HashMap<String, String> headerMap = new HashMap<String, String>();
    headerMap.put(EventHeaderConstants.INTUIT_TID, "tid");
    EventValidator.validate(headerMap);
  }


  @Test
  public void testValidate() throws Exception {

    HashMap<String, String> headerMap = new HashMap<String, String>();
    headerMap.put(EventHeaderConstants.INTUIT_TID, "tid");
    headerMap.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotency");
    headerMap.put(EventHeaderConstants.OFFERING_ID, "id");

    EventValidator.validate(headerMap);
  }
}
