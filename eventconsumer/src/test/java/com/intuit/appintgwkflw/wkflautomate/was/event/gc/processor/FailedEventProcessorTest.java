package com.intuit.appintgwkflw.wkflautomate.was.event.gc.processor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.SchedulingEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.DownStreamConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.guranteedconsumer.commons.domain.model.ErroredEvent;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
public class FailedEventProcessorTest {
    @Mock
    private GCConfig gcConfig;

    @Mock
    private GCAdapter gcAdapter;

    @Mock
    private ThreadPoolExecutor gcThreadPool;

    @Mock
    private WASContextHandler contextHandler;

    @Mock
    private OfferingConfig offeringConfig;

    @InjectMocks
    private FailedEventProcessor failedEventProcessor;

    private ErroredEvent erroredEvent;
    private MessageHeaders headers;
    private SchedulingEventHandler schedulingEventHandler;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        erroredEvent = new ErroredEvent();
        erroredEvent.setId(UUID.randomUUID());
        erroredEvent.setRetryCount(0);
        failedEventProcessor.start();
        Map<String, Object> headers1 = new HashMap<>();
        headers1.put(EventHeaderConstants.ENTITY_TYPE, "SCHEDULING");
        headers1.put(EventHeaderConstants.KAFKA_TOPIC_NAME, "topicname");
        headers = new MessageHeaders(headers1);
        schedulingEventHandler = Mockito.mock(SchedulingEventHandler.class);
        WorkflowEventHandlers.addHandler(EventEntityType.SCHEDULING, schedulingEventHandler);
        ReflectionTestUtils.setField(failedEventProcessor, "gcConfig", gcConfig);
        ReflectionTestUtils.setField(failedEventProcessor, "gcThreadPool", gcThreadPool);
        ReflectionTestUtils.setField(failedEventProcessor, "gcAdapter", gcAdapter);
        ReflectionTestUtils.setField(failedEventProcessor, "contextHandler", contextHandler);
        ReflectionTestUtils.setField(failedEventProcessor, "offeringConfig", offeringConfig);
    }

    @Test
    public void processErroredEventsForOfferings_emptyErrorEvents() {
        when(gcAdapter.getErroredEvents()).thenReturn(Collections.emptyList());
        DownStreamConfig downStreamConfig = new DownStreamConfig();
        downStreamConfig.setOfferingId("qbo");
        failedEventProcessor.processErroredEventsForOfferings(downStreamConfig);
        verify(gcThreadPool, times(0)).submit(any(Runnable.class));
    }

    @Test
    public void processErroredEventsForOfferings_oneErrorEvent() {
        when(gcAdapter.getErroredEvents()).thenReturn(Collections.singletonList(erroredEvent));
        DownStreamConfig downStreamConfig = new DownStreamConfig();
        downStreamConfig.setOfferingId("qbo");
        failedEventProcessor.processErroredEventsForOfferings(downStreamConfig);
        verify(gcThreadPool, times(1)).submit(any(Runnable.class));
    }

    @Test
    public void processErroredEventsForOffering_twoErrorEvent() {
        List<ErroredEvent> erroredEvents = Collections.nCopies(2, erroredEvent);
        when(gcAdapter.getErroredEvents()).thenReturn(erroredEvents);
        when(gcAdapter.getErroredEvents()).thenReturn(erroredEvents);
        DownStreamConfig downStreamConfig = new DownStreamConfig();
        downStreamConfig.setOfferingId("qbo");
        failedEventProcessor.processErroredEventsForOfferings(downStreamConfig);
        verify(gcThreadPool, times(2)).submit(any(Runnable.class));
    }

    @Test
    public void testProcessErroredEvents_zeroOfferings() {
        when(offeringConfig.getDownstreamServices()).thenReturn(Collections.emptyList());
        failedEventProcessor.processErroredEvents();
        verify(gcThreadPool, times(0)).submit(any(Runnable.class));
        verify(contextHandler, times(1)).addKey(any(), any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testProcessErroredEvents_oneOfferings() {
        DownStreamConfig downStreamConfig1 = new DownStreamConfig();
        downStreamConfig1.setOfferingId("qbo");
        when(offeringConfig.getDownstreamServices()).thenReturn(Collections.singletonList(downStreamConfig1));
        failedEventProcessor.processErroredEvents();
        verify(gcThreadPool, times(1)).submit(any(Runnable.class));
        verify(contextHandler, times(1)).addKey(any(), any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testProcessErroredEvents_twoOfferings() {
        DownStreamConfig downStreamConfig1 = new DownStreamConfig();
        downStreamConfig1.setOfferingId("qbo");
        DownStreamConfig downStreamConfig2 = new DownStreamConfig();
        downStreamConfig2.setOfferingId("demo-offering");
        when(offeringConfig.getDownstreamServices()).thenReturn(Arrays.asList(downStreamConfig1, downStreamConfig2));
        failedEventProcessor.processErroredEvents();
        verify(gcThreadPool, times(2)).submit(any(Runnable.class));
        verify(contextHandler, times(1)).addKey(any(), any());
        verify(contextHandler, times(1)).clear();
    }



    @Test
    public void testProcessErroredEvent_maxRetriesExceeded() {
        erroredEvent.setRetryCount(3);
        when(gcConfig.getConsumerGroup()).thenReturn("consumerGroup");
        when(gcConfig.getMaxRetries()).thenReturn(2);
        when(gcAdapter.updateInboxRecordAndCreateFailedEvent(any(), any(), any(), any(), any())).thenReturn(UUID.randomUUID());
        failedEventProcessor.processErroredEvent(UUID.randomUUID(), headers, erroredEvent, "id");
        verify(gcAdapter, times(1)).updateInboxRecordAndCreateFailedEvent(any(), any(), any(), any(), any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testProcessErroredEvent_Success() {
        erroredEvent.setRetryCount(1);
        when(gcConfig.getConsumerGroup()).thenReturn("consumerGroup");
        when(gcConfig.getMaxRetries()).thenReturn(2);
        when(gcAdapter.updateEvent(any(), any(),any())).thenReturn(UUID.randomUUID());
        failedEventProcessor.processErroredEvent(UUID.randomUUID(), headers, erroredEvent, "id");
        verify(gcAdapter, times(1)).updateEvent(any(), any(),any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testProcessErroredEvent_retryableException() {
        erroredEvent.setRetryCount(1);
        when(gcConfig.getConsumerGroup()).thenReturn("consumerGroup");
        when(gcConfig.getMaxRetries()).thenReturn(5);
        when(gcAdapter.updateEvent(any(),any(),any())).thenReturn(UUID.randomUUID());
        Mockito.doThrow(new WorkflowRetriableException("Testing")).when(schedulingEventHandler)
                .transformAndExecute(any(), any());
        failedEventProcessor.processErroredEvent(UUID.randomUUID(), headers, erroredEvent, "id");
        verify(gcAdapter, times(1)).updateEvent(any(),any(),any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testProcessErroredEvent_exception() {
        erroredEvent.setRetryCount(1);
        when(gcConfig.getConsumerGroup()).thenReturn("consumerGroup");
        when(gcConfig.getMaxRetries()).thenReturn(5);
        when(gcAdapter.updateInboxRecordAndCreateFailedEvent(any(), any(), any(), any(), any())).thenReturn(UUID.randomUUID());
        Mockito.doThrow(new WorkflowGeneralException("test")).when(schedulingEventHandler)
                .transformAndExecute(any(), any());
        failedEventProcessor.processErroredEvent(UUID.randomUUID(), headers, erroredEvent, "id");
        verify(gcAdapter, times(1)).updateInboxRecordAndCreateFailedEvent(any(), any(), any(), any(), any());
        verify(contextHandler, times(1)).clear();
    }

    @Test
    public void testAsyncProcessor() {
        failedEventProcessor.start();
        Assertions.assertTrue(failedEventProcessor.isActive());
        failedEventProcessor.stop();
        Assertions.assertFalse(failedEventProcessor.isActive());
    }

    @Test
    public void test_whenRegionActiveIsFalse() {
        failedEventProcessor.stop();
        failedEventProcessor.processErroredEvents();
        verify(gcAdapter, times(0)).getErroredEvents();
        verify(gcThreadPool, times(0)).submit(any(Runnable.class));
        verify(contextHandler, times(0)).addKey(any(), any());
    }
}
