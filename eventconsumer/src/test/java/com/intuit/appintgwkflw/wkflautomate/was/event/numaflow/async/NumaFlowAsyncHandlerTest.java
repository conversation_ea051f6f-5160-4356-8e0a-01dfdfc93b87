package com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.async;


import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.TriggerEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowEventHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Variables;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation.ValidationError;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import io.numaproj.numaflow.sinker.Datum;
import io.numaproj.numaflow.sinker.Response;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(SpringRunner.class)
public class NumaFlowAsyncHandlerTest {

  @Mock
  private Datum datum;
  @Mock
  private EntityHandler entityHandler;
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private SimpleEventPublisher kafkaProducer;
  @Mock
  private MetricLogger metricLogger;

  @Mock
  private EventConsumerUtil eventConsumerUtil;

  @Mock
  private FilterEventUtil filterEventUtil;

  @Mock
  private GCConfig gcConfig;

  @Mock
  private GCAdapter gcAdapter;

  @Mock
  private GCUtil gcUtil;

  @InjectMocks
  private NumaFlowAsyncHandler numaFlowAsyncHandler;

  private final String MESSAGE_ID = "messageid";
  TriggerEventHandler triggerEventHandler = Mockito.mock(TriggerEventHandler.class);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(datum.getValue()).thenReturn(getTriggerPayload());
    Mockito.when(datum.getHeaders()).thenReturn(getHeaders());
    Mockito.when(datum.getId()).thenReturn(MESSAGE_ID);
    Mockito.when(datum.getEventTime()).thenReturn(Instant.now());
    Mockito.when(entityHandler.getEntityType(Mockito.anyString()))
        .thenReturn(EventEntityType.TRIGGER.getEntityType());
    Mockito.when(filterEventUtil.evaluate(Mockito.any())).thenReturn(true);
    WorkflowEventHandlers.addHandler(EventEntityType.TRIGGER, triggerEventHandler);
    ReflectionTestUtils.setField(numaFlowAsyncHandler, "gcUtil", gcUtil);
    ReflectionTestUtils.setField(numaFlowAsyncHandler, "gcAdapter", gcAdapter);
    ReflectionTestUtils.setField(numaFlowAsyncHandler, "gcConfig", gcConfig);
  }

  @Test
  public void transformAndExecute_success() {
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
    Mockito.verify(gcAdapter, Mockito.times(0)).recordEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    Mockito.verify(gcAdapter, Mockito.times(0)).updateEvent(Mockito.any(), Mockito.any(), Mockito.any());
  }

  @Test
  public void transformAndExecute_successWithGcEnabled() {
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Mockito.when(gcUtil.isGCEnabled(Mockito.any())).thenReturn(true);
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
    Mockito.verify(gcAdapter, Mockito.times(0)).recordEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
  }

  @Test
  public void transformAndExecute_throwRetryableException() {
    Mockito.doThrow(new WorkflowRetriableException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(
                    eventConsumerUtil.getFilteredTopicList(EventEntityType.TRIGGER.getEntityType(), true))
            .thenReturn(
                    List.of(
                            EventEntityType.TRIGGER.getEntityType().concat(KafkaConstants.KAFKA_DLQ_SUFFIX)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    Mockito.doNothing().when(kafkaProducer).send(Mockito.anyString(), Mockito.any(), Mockito.any());

    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(kafkaProducer).send(Mockito.anyString(), Mockito.any(), Mockito.any());
    Mockito.verify(kafkaProducer, Mockito.times(1))
            .send(Mockito.anyString(), Mockito.any(), Mockito.any());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
  }

  @Test
  public void transformAndExecute_throwRetryableException_gcErrorHandlingEnabled() {
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Mockito.when(gcUtil.isGCEnabled(Mockito.any())).thenReturn(true);
    Mockito.when(gcAdapter.recordEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(UUID.randomUUID());
    Mockito.doThrow(new WorkflowRetriableException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(
                    eventConsumerUtil.getFilteredTopicList(EventEntityType.TRIGGER.getEntityType(), true))
            .thenReturn(
                    List.of(
                            EventEntityType.TRIGGER.getEntityType().concat(KafkaConstants.KAFKA_DLQ_SUFFIX)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Mockito.when(gcConfig.isServiceEnabled()).thenReturn(true);
    Response response = numaFlowAsyncHandler.call();
    Mockito.doNothing().when(kafkaProducer).send(Mockito.anyString(), Mockito.any(), Mockito.any());

    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
    Mockito.verify(gcAdapter, Mockito.times(1)).recordEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
  }

  @Test
  public void transformAndExecute_throwRetryableException_NotMatched() {
    Mockito.doThrow(new WorkflowRetriableException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(
                    eventConsumerUtil.getFilteredTopicList(EventEntityType.TRIGGER.getEntityType(), true))
            .thenReturn(
                    List.of(
                            EventEntityType.TRIGGER.getEntityType().concat(KafkaConstants.KAFKA_DLQ_SUFFIX)
                                    .concat("test")));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(kafkaProducer, Mockito.times(0))
            .send(Mockito.anyString(), Mockito.any());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
  }

  @Test
  public void transformAndExecute_throwRetryableException_DLQ_Is_Not_Exists() {
    Mockito.doThrow(new WorkflowRetriableException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(kafkaProducer, Mockito.times(0))
            .send(Mockito.anyString(), Mockito.any());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
  }


  @Test
  public void transformAndExecute_throwException() {
    Mockito.doThrow(new RuntimeException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(kafkaProducer, Mockito.times(0))
            .send(Mockito.anyString(), Mockito.any());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
  }

  @Test
  public void transformAndExecute_throwException_gcErrorHandlingEnabled() {
    Mockito.doThrow(new RuntimeException("Testing")).when(triggerEventHandler)
            .transformAndExecute(Mockito.anyString(), Mockito.any());
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Mockito.when(gcUtil.isGCEnabled(Mockito.any())).thenReturn(true);
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(metricLogger)
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(metricLogger, Mockito.times(1))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
    Mockito.verify(gcAdapter, Mockito.times(0)).recordEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
  }


  private byte[] getTriggerPayload() {
    return ObjectConverter.toJson(getTriggerPayload("1", "invoice", "approval", "created"))
            .getBytes();
  }

  private Trigger getTriggerPayload(String id, String type, String workflow, String message) {
    MetaData metaData = getMetaData(id, type, workflow, message);
    Variables variables = getVariables();
    Trigger workflowTriggerEvent = new Trigger(metaData, variables, Collections.EMPTY_MAP);
    return workflowTriggerEvent;
  }

  private MetaData getMetaData(String id, String type, String workflow, String message) {
    EntityChangeIdentifier entityChangeIdentifier =
            new EntityChangeIdentifier(message);
    return MetaData.builder()
            .workflow(workflow)
            .entityType(type)
            .entityChangeIdentifier(entityChangeIdentifier)
            .entityId(id)
            .build();
  }

  @Test
  public void transformAndExecute_validateEventHeaders_missingTid() {
    Mockito.when(datum.getHeaders()).thenReturn(Map.of());
    try {
      numaFlowAsyncHandler.call();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.MISSING_EVENT_HEADERS, e.getWorkflowError());
      Assert.assertTrue(e.getMessage().contains(
              String.format(ValidationError.MISSING_INTUIT_TID_HEADER.getErrorMessage(), null)));
    }

  }

  @Test
  public void transformAndExecute_validateEventHeaders_missingIdempotencyKey() {
    Mockito.when(datum.getHeaders())
        .thenReturn(Map.of(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString()));
    try {
      numaFlowAsyncHandler.call();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.MISSING_EVENT_HEADERS, e.getWorkflowError());
      Assert.assertTrue(e.getMessage().contains(
          String.format(ValidationError.MISSING_IDEMPOTENCY_KEY_HEADER.getErrorMessage(), null)));
    }
  }


  @Test
  public void transformAndExecute_success_ff_disabled() {
    Mockito.when(filterEventUtil.evaluate(Mockito.any())).thenReturn(false);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("5928475928743");
    Response response = numaFlowAsyncHandler.call();
    // check response
    Assert.assertNotNull(response);
    Assert.assertEquals(true, response.getSuccess());
    Assert.assertEquals(MESSAGE_ID, response.getId());
    Mockito.verify(metricLogger, Mockito.times(0))
            .logLatencyMetric(
                    Mockito.eq(MetricName.EVENT_TRIGGER), Mockito.eq(Type.EVENT_METRIC), Mockito.any());
    Mockito.verify(contextHandler).clear();
    Mockito.verify(contextHandler, Mockito.times(1))
            .clear();
  }

  private Variables getVariables() {
    Variables variables = new Variables(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
    return variables;
  }

  private Map<String, String> getHeaders() {
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");
    headers.put(EventHeaderConstants.KAFKA_TOPIC_NAME, EventEntityType.TRIGGER.getEntityType());
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP, Instant.now().toString());
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP_TYPE, "logAppendTime");
    headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());
    headers.put(EventHeaderConstants.ENTITY_ID, UUID.randomUUID().toString());
    headers.put(EventHeaderConstants.OFFERING_ID, "default");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "default");
    return headers;
  }
}
