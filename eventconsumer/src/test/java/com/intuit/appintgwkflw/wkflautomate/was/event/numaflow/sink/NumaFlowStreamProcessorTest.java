package com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.sink;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.GCAdapter;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.util.GCUtil;
import com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.async.NumaFlowAsyncHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.FilterEventUtil;
import io.numaproj.numaflow.sinker.Datum;
import io.numaproj.numaflow.sinker.DatumIterator;
import io.numaproj.numaflow.sinker.Response;
import io.numaproj.numaflow.sinker.ResponseList;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class NumaFlowStreamProcessorTest {

  private NumaFlowStreamProcessor numaFlowStreamProcessor;
  @Mock
  private EntityHandler entityHandler;
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private ThreadPoolExecutor numaflowThreadPool;
  @Mock
  private SimpleEventPublisher kafkaProducer;

  @Mock
  private EventConsumerUtil eventConsumerUtil;
  @Mock
  private MetricLogger metricLogger;

  @Mock
  private FilterEventUtil filterEventUtil;

  @Mock
  private GCAdapter gcAdapter;
  @Mock
  private GCConfig gcConfig;
  @Mock
  private GCUtil gcUtil;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    numaFlowStreamProcessor = new NumaFlowStreamProcessor(entityHandler, contextHandler,
            numaflowThreadPool,
            kafkaProducer,
            metricLogger, eventConsumerUtil, filterEventUtil, gcAdapter, gcConfig, gcUtil);
  }

  @Test
  public void testProcessMessages() throws InterruptedException {
    DatumIterator datumIterator = Mockito.mock(DatumIterator.class);
    Mockito.when(datumIterator.next()).thenReturn(Mockito.mock(Datum.class), null);
    Response response = Response.responseOK("test-id");
    Future<Response> futureResponse = CompletableFuture.completedFuture(response);
    Mockito.when(numaflowThreadPool.submit(Mockito.any(NumaFlowAsyncHandler.class)))
        .thenReturn(futureResponse);
    ResponseList responseList = numaFlowStreamProcessor.processMessages(datumIterator);
    Mockito.verify(numaflowThreadPool, Mockito.times(1))
        .submit(Mockito.any(NumaFlowAsyncHandler.class));
    Assert.assertEquals(1, responseList.getResponses().size());
    Assert.assertEquals(response, responseList.getResponses().get(0));
  }


  @Test
  public void testProcessMessagesThrowsInterruptedException() throws Exception {
// Create a mock DatumIterator that throws an InterruptedException
    DatumIterator datumIterator = Mockito.mock(DatumIterator.class);
    Mockito.when(datumIterator.next()).thenThrow(new InterruptedException());
    try {
      numaFlowStreamProcessor.processMessages(datumIterator);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertEquals(WorkflowError.INTERNAL_EXCEPTION,
          workflowGeneralException.getWorkflowError());
    }


  }

  @Test
  public void testProcessMessagesWithExecutionException()
      throws InterruptedException {
    DatumIterator datumIterator = Mockito.mock(DatumIterator.class);
    Datum datum = Mockito.mock(Datum.class);
    Mockito.when(datum.getId()).thenReturn("testId");
    Mockito.when(datumIterator.next()).thenReturn(datum, null);

    ExecutionException executionException = Mockito.mock(ExecutionException.class);
    Future<Response> futureResponse = CompletableFuture.failedFuture(executionException);
    Mockito.when(numaflowThreadPool.submit(Mockito.any(NumaFlowAsyncHandler.class)))
        .thenReturn(futureResponse);

    ResponseList responseList = numaFlowStreamProcessor.processMessages(datumIterator);

    Mockito.verify(numaflowThreadPool, Mockito.times(1))
        .submit(Mockito.any(NumaFlowAsyncHandler.class));
    Assert.assertEquals(1, responseList.getResponses().size());
    Assert.assertEquals("testId",
        responseList.getResponses().get(0).getId());
    Assert.assertTrue(
        responseList.getResponses().get(0).getSuccess());
  }

  @Test
  public void testProcessMessagesWithExecutionEventException()
      throws InterruptedException {
    DatumIterator datumIterator = Mockito.mock(DatumIterator.class);
    Datum datum = Mockito.mock(Datum.class);
    Mockito.when(datum.getId()).thenReturn("testId");
    Mockito.when(datumIterator.next()).thenReturn(datum, null);
    Future<Response> futureResponse = CompletableFuture.failedFuture(
        new WorkflowEventException(new WorkflowRetriableException("Retryable exception")));
    Mockito.when(numaflowThreadPool.submit(Mockito.any(NumaFlowAsyncHandler.class)))
        .thenReturn(futureResponse);

    ResponseList responseList = numaFlowStreamProcessor.processMessages(datumIterator);

    Mockito.verify(numaflowThreadPool, Mockito.times(1))
        .submit(Mockito.any(NumaFlowAsyncHandler.class));
    Assert.assertEquals(1, responseList.getResponses().size());
    Assert.assertEquals("testId",
        responseList.getResponses().get(0).getId());
    Assert.assertNotNull(
        responseList.getResponses().get(0).getErr());
  }

  @Test
  public void testProcessMessagesWithInterruptedException()
      throws InterruptedException, ExecutionException {
    DatumIterator datumIterator = Mockito.mock(DatumIterator.class);
    Datum datum = Mockito.mock(Datum.class);
    Mockito.when(datum.getId()).thenReturn("testId");
    Mockito.when(datumIterator.next()).thenReturn(datum, null);
    InterruptedException interruptedException = Mockito.mock(InterruptedException.class);
    Future<Response> futureResponse = Mockito.mock(Future.class);
    Mockito.doThrow(interruptedException).when(futureResponse).get();
    Mockito.when(numaflowThreadPool.submit(Mockito.any(NumaFlowAsyncHandler.class)))
        .thenReturn(futureResponse);

    ResponseList responseList = numaFlowStreamProcessor.processMessages(datumIterator);

    Mockito.verify(numaflowThreadPool, Mockito.times(1))
        .submit(Mockito.any(NumaFlowAsyncHandler.class));
    Assert.assertEquals(1, responseList.getResponses().size());
    Assert.assertEquals("testId",
        responseList.getResponses().get(0).getId());
    Assert.assertFalse(
        responseList.getResponses().get(0).getSuccess());
  }


}
