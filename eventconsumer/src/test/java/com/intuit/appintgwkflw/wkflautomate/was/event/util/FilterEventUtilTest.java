package com.intuit.appintgwkflw.wkflautomate.was.event.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FilterEventUtilTest {

  private static final long MOCK_OWNER_ID = 12345L;
  private static final String MOCK_OFFERING_ID = "default";

  @Mock
  private WASContextHandler mockContextHandler;

  @Mock
  private FeatureFlagManager mockFeatureFlagManager;

  @InjectMocks
  private FilterEventUtil filterEventUtil;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    filterEventUtil = new FilterEventUtil(mockContextHandler, mockFeatureFlagManager);
    when(mockContextHandler.get(eq(WASContextEnums.OWNER_ID))).thenReturn(
        String.valueOf(MOCK_OWNER_ID));
    when(mockContextHandler.get(eq(WASContextEnums.OFFERING_ID))).thenReturn(MOCK_OFFERING_ID);
  }

  @Test
  public void testEvaluateBoolean_whenFeatureFlagEnabled_returnsTrue() {
    EventEntityType mockEventType = EventEntityType.TRIGGER;
    String mockFeatureFlagName = String.format(WorkflowConstants.NUMAFLOW_ENABLED_FF,
        mockEventType.getEntityType());
    Map<String, Object> mockContextMap = new HashMap<>();
    mockContextMap.put(WorkflowConstants.INTUIT_REALMID, String.valueOf(MOCK_OWNER_ID));
    mockContextMap.put(WorkflowConstants.OFFERING_ID, MOCK_OFFERING_ID);

    when(mockFeatureFlagManager.getBooleanWithContextMap(eq(mockFeatureFlagName), eq(false), eq(mockContextMap),
        eq(MOCK_OWNER_ID)))
        .thenReturn(true);
    boolean result = filterEventUtil.evaluate(mockEventType);
    assertTrue(result);
  }

  @Test
  public void testEvaluateBoolean_whenFeatureFlagEnabled_returnsTrue_nullOwnerId() {
    EventEntityType mockEventType = EventEntityType.TRIGGER;
    String mockFeatureFlagName = String.format(WorkflowConstants.NUMAFLOW_ENABLED_FF,
        mockEventType.getEntityType());
    Map<String, Object> mockContextMap = new HashMap<>();
    mockContextMap.put(WorkflowConstants.INTUIT_REALMID, null);
    mockContextMap.put(WorkflowConstants.OFFERING_ID, MOCK_OFFERING_ID);
    when(mockContextHandler.get(eq(WASContextEnums.OWNER_ID))).thenReturn(
        null);
    when(mockFeatureFlagManager.getBooleanWithContextMap(eq(mockFeatureFlagName), eq(false), eq(mockContextMap),
        Mockito.any()))
        .thenReturn(true);
    boolean result = filterEventUtil.evaluate(mockEventType);
    assertTrue(result);
  }

  @Test
  public void testEvaluateBoolean_whenFeatureFlagDisabled_returnsFalse() {
    EventEntityType mockEventType =  EventEntityType.TRIGGER;
    String mockFeatureFlagName = String.format(WorkflowConstants.NUMAFLOW_ENABLED_FF,
        mockEventType.getEntityType());
    Map<String, Object> mockContextMap = new HashMap<>();
    mockContextMap.put(WorkflowConstants.INTUIT_REALMID, String.valueOf(MOCK_OWNER_ID));
    mockContextMap.put(WorkflowConstants.OFFERING_ID, MOCK_OFFERING_ID);

    when(mockFeatureFlagManager.getBooleanWithContextMap(eq(mockFeatureFlagName), eq(false), eq(mockContextMap),
        eq(MOCK_OWNER_ID)))
        .thenReturn(false);

    boolean result = filterEventUtil.evaluate(mockEventType);

    assertFalse(result);
  }

}