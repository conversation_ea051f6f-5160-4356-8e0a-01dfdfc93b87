package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MaskedObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WorkflowSupplier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Capabilty class, from where event will be published by Workers
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class EventPublisherCapabilityImpl implements EventPublisherCapability {

  @Value("${security.intuit.appId}")
  private String appId;

  @Value("${intuit.pii.log.enable:false}")
  private boolean piiLogEnable;

  private final ApplicationContext applicationContext;
  private final EventConfiguration eventConfiguration;

  /**
   * @param eventHeaderEntity This is the async method that would publish to Kafka. Internally it
   *                          handles all the retries and errors.
   */
  @Metric(name = MetricName.EVENT_PUBLISH, type = Type.EVENT_METRIC)
  @Override
  @Retry(name = ResiliencyConstants.EVENT_PUBLISH)
  public <T> SendResult<String, String> publish(EventHeaderEntity eventHeaderEntity,
      T publishEntity) throws WorkflowEventException {

    return publishRecord(eventHeaderEntity, publishEntity, null);
  }

  /**
   *
   * @param eventHeaderEntity
   * @param publishEntity
   * @param key (partition key to maintain order of events while consumption
   * @return
   * @throws WorkflowEventException
   */
  @Metric(name = MetricName.EVENT_PUBLISH, type = Type.EVENT_METRIC)
  @Override
  public <T> SendResult<String, String> publish(EventHeaderEntity eventHeaderEntity, T publishEntity, String key)
      throws WorkflowEventException {

    WorkflowVerfiy.verifyNullAndThrow(key,
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.PARTITION_KEY_NULL));
        });

    return publishRecord(eventHeaderEntity, publishEntity, key);
  }
  
	@Metric(name = MetricName.EVENT_PUBLISH, type = Type.EVENT_METRIC)
	public <T> ListenableFuture<SendResult<String, String>> publishAsync(EventHeaderEntity eventHeaderEntity,
			T publishEntity, String key,final WorkflowSupplier input) throws WorkflowEventException {

		WorkflowVerfiy.verifyNullAndThrow(key, () -> {
			throw new WorkflowEventException(new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
					KafkaConstants.PARTITION_KEY_NULL));
		});

		return publishRecordAsync(eventHeaderEntity, publishEntity, key,input);
	}
  
	private <T> ListenableFuture<SendResult<String, String>> publishRecordAsync(EventHeaderEntity eventHeaderEntity,
			T publishEntity, String key, final WorkflowSupplier workflowSupplier) throws WorkflowEventException {
		KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);

		WorkflowVerfiy.verifyNullAndThrow(eventHeaderEntity, () -> {
			throw new WorkflowEventException(new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
					KafkaConstants.EVENT_HEADER_ENTITY_NULL_ERROR));
		});

		WorkflowVerfiy.verifyNullAndThrow(publishEntity, () -> {
			throw new WorkflowEventException(new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
					KafkaConstants.PUBLISH_ENTITY_NULL_ERROR));
		});

		// Config Check before publishing to Kafka. Disabling the config, wont produce
		// any event to Kafka.
		if (eventConfiguration.getProducer().isEnabled()) {
			try {
				ProducerRecord<String, String> producerRecord = toProducerRecord(eventHeaderEntity,
						getKafkaHeaders(eventHeaderEntity), publishEntity, key);

				ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(producerRecord);

				future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {

					@Override
					public void onSuccess(final SendResult<String, String> message) {
						log.info("sent message= " + message + " with offset= " + message.getRecordMetadata().offset());
					}

					@Override
					public void onFailure(final Throwable throwable) {
						log.error("Message={} could not be delivered. Error={} ", producerRecord.value(),
								throwable.getMessage());
						if (null != workflowSupplier) {
							log.error("Executing Supplier for Message={}.Error={} ", producerRecord.value(),
									throwable.getMessage());
							workflowSupplier.get();
						}
					}
				});
				return future;
			} catch (WorkflowEventException e) {
				// Throw WorkflowEventException which are thrown in toProducerRecord() and
				// getKafkaHeaders()
				throw e;
			} catch (Exception e) {
				// If Kafka is down we get EventBusConnectionException which is a
				// SocketTimeoutException
				// InterruptedException and ExecutionException are thrown by
				// Listenablefuture.get()
				// Catching all the Kafka publish related exceptions and throwing
				// RetriableException to kick in retry mechanism
        WorkflowLogger.logError(e, "Exception occurred while publishing the message exception=%s", e.getMessage());
				throw new WorkflowEventException(new WorkflowRetriableException(WorkflowError.KAFKA_PUBLISH_ERROR, e));
			}
		}

		return null;
	}

  private <T> SendResult<String, String> publishRecord(EventHeaderEntity eventHeaderEntity, T publishEntity, String key)
      throws WorkflowEventException {
    KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);

    WorkflowVerfiy.verifyNullAndThrow(eventHeaderEntity,
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.EVENT_HEADER_ENTITY_NULL_ERROR));
        });

    WorkflowVerfiy.verifyNullAndThrow(publishEntity,
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.PUBLISH_ENTITY_NULL_ERROR));
        });

    // Config Check before publishing to Kafka. Disabling the config, wont produce any event to Kafka.
    if (eventConfiguration.getProducer().isEnabled()) {
      try {
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send(
            toProducerRecord(eventHeaderEntity, getKafkaHeaders(eventHeaderEntity), publishEntity, key));
        SendResult<String, String> sendResult = listenableFuture.get();
        Optional.ofNullable(sendResult).map(SendResult::getRecordMetadata).ifPresent(recordMetadata -> {
          WorkflowLogger.logInfo("Message published timeStamp=%s partition=%s offset=%d topic=%s idempotencyKey=%s",
              recordMetadata.timestamp(), recordMetadata.partition(), recordMetadata.offset(), recordMetadata.topic(), eventHeaderEntity.getIdempotencyKey());
        });
        return sendResult;
      } catch (WorkflowEventException e) {
        // Throw WorkflowEventException which are thrown in toProducerRecord() and getKafkaHeaders()
        throw e;
      } catch (Exception e) {
        // If Kafka is down we get EventBusConnectionException which is a SocketTimeoutException
        // InterruptedException and ExecutionException are thrown by Listenablefuture.get()
        // Catching all the Kafka publish related exceptions and throwing RetriableException to kick in retry mechanism
        WorkflowLogger.logError(e, "Exception occurred while publishing the message exception=%s", e.getMessage());
        throw new WorkflowEventException(
            new WorkflowRetriableException(WorkflowError.KAFKA_PUBLISH_ERROR, e));
      }
    }

    return null;
  }

  private Headers getKafkaHeaders(EventHeaderEntity eventHeaderEntity) {

    WorkflowVerfiy.verifyNullAndThrow(eventHeaderEntity.getEntityId(),
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.ENTITY_ID_NULL_ERROR));
        });

    WorkflowVerfiy.verifyNullAndThrow(eventHeaderEntity.getPublishEventType(),
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.PUBLISH_EVENT_TYPE_NULL_ERROR));
        });

    WorkflowVerfiy.verifyNullAndThrow(eventHeaderEntity.getOfferingId(),
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.OFFERING_ID_NULL_ERROR));
        });

    Headers headers = new RecordHeaders();
//  Not a mandatory header.
    if (eventHeaderEntity.getTargetAssetAlias() != null) {
      headers.add(EventHeaderConstants.TARGET_ASSET_ALIAS, eventHeaderEntity.getTargetAssetAlias().getBytes());
    }
    if (eventHeaderEntity.getEventEntityType() != null) {
        headers.add(EventHeaderConstants.DOMAIN_EVENT, eventHeaderEntity.getEventEntityType().getEntityType().getBytes());
    }
    if (eventHeaderEntity.getOwnerId() != null) {
      headers.add(EventHeaderConstants.OWNER_ID, eventHeaderEntity.getOwnerId().getBytes());
    }
    if (eventHeaderEntity.getHandlerId() != null) {
      headers.add(EventHeaderConstants.HANDLER_ID, eventHeaderEntity.getHandlerId().getBytes());
    }
    if(Objects.nonNull(eventHeaderEntity.getIntuitUserId())) {
      headers.add(EventHeaderConstants.INTUIT_USER_ID, eventHeaderEntity.getIntuitUserId().getBytes());
    }

    headers.add(EventHeaderConstants.ENTITY_ID, eventHeaderEntity.getEntityId().getBytes());
    headers.add(EventHeaderConstants.ACTOR_ID, KafkaConstants.ACTOR_ID.getBytes());
    headers.add(EventHeaderConstants.PUBLISHING_ASSET_ALIAS, appId.getBytes());
    headers.add(EventHeaderConstants.ENTITY_VERSION, KafkaConstants.VERSION_ID.getBytes());
    headers.add(EventHeaderConstants.EVENT_TYPE, EntityEventType.DOMAIN_EVENT.name().getBytes());
    headers.add(EventHeaderConstants.IDEMPOTENCY_KEY,eventHeaderEntity.getIdempotencyKey().getBytes());
    headers.add(EventHeaderConstants.OFFERING_ID, eventHeaderEntity.getOfferingId().getBytes());
    headers.add(EventHeaderConstants.INTUIT_TID, eventHeaderEntity.getTid().getBytes());
    return headers;
  }

  private <T> ProducerRecord<String, String> toProducerRecord(EventHeaderEntity eventHeaderEntity,
      Headers headers, T publishEntity, String key) {

    WorkflowVerfiy.verifyNullAndThrow(eventConfiguration.getProducer().getEntityTopicsMapping(),
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.PRODUCER_ENTITY_TOPIC_MAPPING_NULL_ERROR));
        });

    String topicName = eventConfiguration.getProducer().getEntityTopicsMapping()
        .get(eventHeaderEntity.getPublishEventType().getName());

    WorkflowVerfiy.verifyNullAndThrow(topicName,
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_TOPIC_ERROR,
                  KafkaConstants.TOPIC_NAME_NULL_ERROR));
        });

    // Null checks done before
    String eventPayload = ObjectConverter.toJson(publishEntity);

    WorkflowVerfiy.verifyNullAndThrow(eventPayload,
        () -> {
          throw new WorkflowEventException(
              new WorkflowRetriableException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
                  KafkaConstants.EVENT_PAYLOAD_NULL_ERROR));
        });

    logEventPublish(eventHeaderEntity, publishEntity, key);
    return new ProducerRecord<>(topicName, null, System.currentTimeMillis(), key, eventPayload,
        headers);
  }


  private <T> void logEventPublish(EventHeaderEntity eventHeaderEntity, T publishEntity, String key){
    //Serializing using MaskedObjectConverter which will redact fields marked with annotation RedactSensitiveField

    String eventPayload = piiLogEnable ? ObjectConverter.toJson(publishEntity)
    		: MaskedObjectConverter.toJson(publishEntity);
    if(Objects.nonNull(key)){
      EventingLoggerUtil.logInfo("Publishing Payload step=publishPayload entityId=%s headers=%s, payload=%s partitionKey=%s",
          this.getClass().getName(), eventHeaderEntity.getEntityId(), eventHeaderEntity, eventPayload, key);
    }
    else{
      EventingLoggerUtil.logInfo("Publishing Payload step=publishPayload entityId=%s headers=%s, payload=%s",
          this.getClass().getName(), eventHeaderEntity.getEntityId(), eventHeaderEntity, eventPayload);
    }
  }

  @Override
  public void flush() {
    KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);
    kafkaTemplate.flush();
  }
}
