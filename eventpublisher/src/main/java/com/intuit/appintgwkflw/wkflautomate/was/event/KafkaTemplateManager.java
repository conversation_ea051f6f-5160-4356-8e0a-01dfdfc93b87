package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultSingletonBeanRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * Manages the creation and reinstialising of kafka bean for kafka template.
 */
@Component
@RequiredArgsConstructor
public class KafkaTemplateManager {
  @Autowired
  private final ApplicationContext applicationContext;

  private final String KAFKA_TEMPLATE = "kafkaTemplate";

  public void reinitializeBean(DefaultKafkaProducerFactory kafkaProducerFactory) {
    WorkflowLogger.logDebug("Reinitializing Bean for Kafka Template");

    DefaultSingletonBeanRegistry registry = (DefaultSingletonBeanRegistry) applicationContext.getAutowireCapableBeanFactory();
    registry.destroySingleton(KAFKA_TEMPLATE);
    registry.registerSingleton(KAFKA_TEMPLATE, new KafkaTemplate<>(kafkaProducerFactory));
  }
}
