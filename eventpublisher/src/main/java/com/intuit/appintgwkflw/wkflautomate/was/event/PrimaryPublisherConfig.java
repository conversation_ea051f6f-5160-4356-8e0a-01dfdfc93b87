package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;

/** <AUTHOR> Initialises the KafkaPublisher Bean from the config defined. */
@Configuration
@RequiredArgsConstructor
@ConditionalOnExpression("${event.producer.enabled:false}")
public class PrimaryPublisherConfig {

  private final EventConfiguration eventConfiguration;

  /**
   * @return DefaultKafkaProducerFactory It reads all the config and IDPS information from the
   * configs and initialises the Kafka Producer Bean
   */
  @Bean
  @Primary
  public DefaultKafkaProducerFactory primaryProducerFactory() {

    WorkflowVerfiy.verifyNull(eventConfiguration, WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.EVENT_CONFIGURATION_NULL_ERROR);
    WorkflowVerfiy
        .verifyNull(eventConfiguration.getProducer(), WorkflowError.INVALID_EVENT_CONFIG_ERROR,
            KafkaConstants.PRODUCER_EVENT_CONFIGURATION_NULL_ERROR);
    WorkflowVerfiy.verifyNull(eventConfiguration.getProducerRegionConfig().getBootStrapServers(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR, KafkaConstants.BOOTSTRAP_SERVER_NULL_ERROR);

    Map<String, Object> kafkaPropMap;
//      Old props are read to make it backward compatible. This if Condn will be removed later.
    if (eventConfiguration.getProducer().getConfig() == null || eventConfiguration.getProducer()
        .getConfig().isEmpty()) {
      kafkaPropMap = PublisherUtils.fetchConfig(eventConfiguration, PublisherType.DEFAULT);
    } else {
      kafkaPropMap = new HashMap<>(eventConfiguration.getProducer().getConfig());
      kafkaPropMap
          .put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, eventConfiguration.getProducerRegionConfig().getBootStrapServers());
    }

    DefaultKafkaProducerFactory kafkaProducerFactory =  PublisherUtils.getDefaultKafkaProducerFactory(kafkaPropMap, eventConfiguration);
    EventingLoggerUtil.logInfo("Initialized Kafka Properties step=initializationComplete",
        this.getClass().getName());

    return kafkaProducerFactory;
  }
}
