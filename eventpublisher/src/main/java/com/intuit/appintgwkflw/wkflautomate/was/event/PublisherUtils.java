package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.eventbus.SerializingMessageFormater;
import com.intuit.eventbus.cryptography.IDPSEncrypter;
import com.intuit.eventbus.impl.SerializingMessageFormaterImpl;
import com.intuit.eventbus.impl.v3.FormaterV3;
import com.intuit.eventbus.kafka.serde.Serializer;
import com.intuit.eventbus.message.MetadataV3;
import com.intuit.eventbus.utils.Environment;
import com.intuit.eventbus.validation.validator.DefaultValidator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.EnumUtils;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.jetbrains.annotations.NotNull;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;

/**
 * <AUTHOR>
 *
 * Utility class for publisher initialisation.
 */
@UtilityClass
public class PublisherUtils {

  @Deprecated
  public Map<String, Object> fetchConfig(EventConfiguration eventConfiguration, PublisherType publisherType) {
    Map<String, Object> kafkaProps = new HashMap<>();

    // TODO: Get rid of these properties in the code and directly read it from config.
    List<String> bootStrapServers = eventConfiguration.getProducerRegionConfig()
        .getBootStrapServers();

    if(PublisherType.SECONDARY.equals(publisherType)){
      bootStrapServers = eventConfiguration.getProducerSecondaryRegionConfig()
          .getBootStrapServers();
    }

    kafkaProps
        .put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServers);
    kafkaProps.put(ProducerConfig.ACKS_CONFIG, eventConfiguration.getProducer().getAcks());
    kafkaProps.put(ProducerConfig.RETRIES_CONFIG, eventConfiguration.getProducer().getRetries());
    kafkaProps.put(ProducerConfig.METADATA_MAX_AGE_CONFIG,
        eventConfiguration.getProducer().getMetadataMaxAgeMs());
    kafkaProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG,
        eventConfiguration.getProducer().getRequestTimeoutMs());
    kafkaProps.put(ProducerConfig.RECONNECT_BACKOFF_MAX_MS_CONFIG,
        eventConfiguration.getProducer().getReconnectBackoffMs());
    kafkaProps.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG,
        eventConfiguration.getProducer().getRetryBackoffMs());
    kafkaProps.put(ProducerConfig.BATCH_SIZE_CONFIG,
        eventConfiguration.getProducer().getBatchSizeBytes());
    kafkaProps.put(ProducerConfig.LINGER_MS_CONFIG, eventConfiguration.getProducer().getLingerMs());

    kafkaProps
        .put(KafkaConstants.SECURITY_PROTOCOL_CONFIG, eventConfiguration.getSecurityProtocol());
    kafkaProps.put(KafkaConstants.SSL_ENABLED_PROTOCOLS_CONFIG,
        eventConfiguration.getSslEnabledProtocol());
    return kafkaProps;
  }

  @NotNull
  public DefaultKafkaProducerFactory getDefaultKafkaProducerFactory(
      Map<String, Object> kafkaPropMap, EventConfiguration eventConfiguration) {
    com.intuit.eventbus.Configuration<SerializingMessageFormater<String, MetadataV3>> formatterConf = new SerializingMessageFormaterImpl.Config<>(
        new DefaultValidator.Config<>(),
        new FormaterV3.Config(new IDPSEncrypter.Config(getEnvironment(eventConfiguration),
            eventConfiguration.getIdpsConfig().getProperties())),
        com.intuit.eventbus.serialization.StringSerializer::new);

    Serializer<String> messageSerializer = new Serializer<>(() -> formatterConf.instantiate());

    return new DefaultKafkaProducerFactory<>(kafkaPropMap, new StringSerializer(),
        messageSerializer);
  }

  private Environment getEnvironment(EventConfiguration eventConfig) {
    if (EnumUtils.isValidEnum(Environment.class, eventConfig.getProducer().getEnvironment())) {
      return Environment.valueOf(eventConfig.getProducer().getEnvironment());
    }

    throw new WorkflowGeneralException(WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.INVALID_PRODUCER_ENVIRONMENT_ERROR);
  }
}
