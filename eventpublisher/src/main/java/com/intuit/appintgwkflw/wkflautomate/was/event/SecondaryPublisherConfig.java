package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;

/**
 * <AUTHOR>
 *
 * Initialises the secondary KafkaPublisher Bean from the config defined.
 */
@Configuration
@RequiredArgsConstructor
@ConditionalOnExpression("${event.producer.enabled:false}")
public class SecondaryPublisherConfig {

  private final EventConfiguration eventConfiguration;

  @Bean
  public DefaultKafkaProducerFactory secondaryProducerFactory() {

    WorkflowVerfiy.verifyNull(eventConfiguration, WorkflowError.INVALID_EVENT_CONFIG_ERROR,
        KafkaConstants.EVENT_CONFIGURATION_NULL_ERROR);
    WorkflowVerfiy
        .verifyNull(eventConfiguration.getProducer(), WorkflowError.INVALID_EVENT_CONFIG_ERROR,
            KafkaConstants.PRODUCER_EVENT_CONFIGURATION_NULL_ERROR);
    WorkflowVerfiy.verifyNull(eventConfiguration.getProducerSecondaryRegionConfig().getBootStrapServers(),
        WorkflowError.INVALID_EVENT_CONFIG_ERROR, KafkaConstants.BOOTSTRAP_SERVER_NULL_ERROR);

    Map<String, Object> kafkaPropMap;
//      Old props are read to make it backward compatible. This if Condn will be removed later.
    WorkflowVerfiy.verifyNull(eventConfiguration.getProducer(), WorkflowError.INVALID_PRODUCER_CONFIG);
    if (eventConfiguration.getProducer().getConfig() == null || eventConfiguration.getProducer()
        .getConfig().isEmpty()) {
      kafkaPropMap = PublisherUtils.fetchConfig(eventConfiguration, PublisherType.SECONDARY);
      EventingLoggerUtil.logInfo("Initialized Kafka Properties step=initializationComplete props=%s",
          this.getClass().getName(), kafkaPropMap);

    } else {
      kafkaPropMap = new HashMap<>(eventConfiguration.getProducer().getConfig());
      kafkaPropMap
          .put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, eventConfiguration.getProducerSecondaryRegionConfig().getBootStrapServers());
    }

    DefaultKafkaProducerFactory kafkaProducerFactory = PublisherUtils.getDefaultKafkaProducerFactory(
        kafkaPropMap, eventConfiguration);
    EventingLoggerUtil.logInfo("Initialized Kafka Secondary Properties step=initializationComplete",
        this.getClass().getName());

    return kafkaProducerFactory;
  }
}
