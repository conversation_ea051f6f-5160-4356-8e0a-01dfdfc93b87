package com.intuit.appintgwkflw.wkflautomate.was.event.api;

import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WorkflowSupplier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;

/**
 * <AUTHOR> Capabilty class, from where event will be published by Workers.
 */
public interface EventPublisherCapability {

  /**
   * @param headerEntity
   * @param publishEntity
   * @throws WorkflowEventException When topic name is not valid or Configurations are not set This
   *                                is the async method that would publish to Kafka. Internally it
   *                                handles all the retries and errors.
   */
  <T> SendResult<String, String> publish(EventHeaderEntity headerEntity, T publishEntity)
      throws WorkflowEventException;

  /**
   * @param headerEntity
   * @param publishEntity
   * @param key
   * @throws WorkflowEventException When topic name is not valid or Configurations are not set This
   *                                is the async method that would publish to Kafka with partition key.
   *                                Internally it handles all the retries and errors.
   */
  <T> SendResult<String, String> publish(EventHeaderEntity headerEntity, T publishEntity, String key)
      throws WorkflowEventException;
  
  /**
   * @param headerEntity
   * @param publishEntity
   * @param key
   * @throws WorkflowEventException When topic name is not valid or Configurations are not set This
   *                                is the async method that would publish to Kafka with partition key.
   *                                Internally it handles all the retries and errors.
   */
  <T> ListenableFuture<SendResult<String, String>> publishAsync(EventHeaderEntity headerEntity, T publishEntity, String key,final WorkflowSupplier workflowSupplier)
      throws WorkflowEventException;

  /**
   * Flushes the Kafka Cache and publishes everything. This will be used while shutting down.
   */
  void flush();
}
