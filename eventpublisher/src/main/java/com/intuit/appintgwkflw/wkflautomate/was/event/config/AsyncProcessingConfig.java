package com.intuit.appintgwkflw.wkflautomate.was.event.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.event.KafkaTemplateManager;
import com.intuit.appintgwkflw.wkflautomate.was.event.PrimaryPublisherConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.SecondaryPublisherConfig;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "async-processing")
@ConditionalOnExpression("${event.producer.enabled:false}")
public class AsyncProcessingConfig {
  private boolean enabled;
  private boolean publishToSecondary;
  private boolean enableForAllWorkflow;
  private List<String> workflows;
  private List<String> endpoints;

  @Autowired private SecondaryPublisherConfig secondaryPublisherConfig;
  @Autowired private PrimaryPublisherConfig publisherConfig;
  @Autowired private KafkaTemplateManager kafkaTemplateManager;
  
  @EventListener
  public void onRefreshScopeRefreshed(final RefreshScopeRefreshedEvent event) {
    WorkflowLogger.logInfo("Scope change detected. regionInactive/serverStartup " + publishToSecondary);

    if(publishToSecondary){
      kafkaTemplateManager.reinitializeBean(secondaryPublisherConfig.secondaryProducerFactory());
    } else{
      kafkaTemplateManager.reinitializeBean(publisherConfig.primaryProducerFactory());
    }
  }
}
