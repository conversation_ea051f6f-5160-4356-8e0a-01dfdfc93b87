package com.intuit.appintgwkflw.wkflautomate.was.event;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventProducer;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventRegionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import com.intuit.platform.jsk.config.client.idps.autoconfig.IdpsConnectionProperties;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * <AUTHOR>
 * @Test EventPublisherCapability
 */

@RunWith(MockitoJUnitRunner.class)
public class EventPublisherCapabilityImplTest {

  private EventPublisherCapability eventPublisherCapability;
  private EventHeaderEntity eventHeaderEntity;

  private ExternalTaskAssigned externalTaskAssigned;

  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private KafkaTemplate<String, String> kafkaTemplate;

  @Mock
  private ListenableFuture<SendResult<String, String>> listenableFuture;

  @Mock
  private SendResult<String, String> sendResult;

  private EventConfiguration eventConfiguration;


  private String partitionKey;

  @Before
  public void setUp() {
    eventConfiguration = new EventConfiguration();
    EventRegionConfig eventRegionConfig = new EventRegionConfig();
    eventRegionConfig.setBootStrapServers(Arrays.asList("abc"));
    eventConfiguration.setPrimary(eventRegionConfig);
    eventConfiguration.setSecurityProtocol("ssl");
    eventConfiguration.setSslEnabledProtocol("tls");
    eventConfiguration.setIdpsConfig(new IdpsConnectionProperties());
    EventProducer eventProducer = new EventProducer();
    eventProducer.setEnabled(Boolean.FALSE);
    eventConfiguration.setProducer(eventProducer);

    Mockito.when(applicationContext.getBean(KafkaTemplate.class)).thenReturn(kafkaTemplate);
    eventPublisherCapability = new EventPublisherCapabilityImpl(applicationContext, eventConfiguration);
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder().workflowName("engagement")
        .workflowName("111")
        .processInstanceId("12022")
        .build();
    externalTaskAssigned = ExternalTaskAssigned.builder().variables(new HashMap<>())
        .extensions(new HashMap<>())
        .workflowMetadata(workflowMetaData)
        .txnId("txn1").build();

    eventHeaderEntity = EventHeaderEntity.builder().
        publishEventType(PublishEventType.EXTERNAL_TASK)
            .idempotencyKey("111")
            .offeringId("was")
            .targetAssetAlias("aaa")
            .entityId("e11")
            .tid("tid")
            .eventEntityType(EventEntityType.EXTERNALTASK)
            .build();

    partitionKey = workflowMetaData.getProcessInstanceId();

    Mockito.when(sendResult.getRecordMetadata()).thenReturn(new RecordMetadata(new TopicPartition("test", 3), 0, 0, 0, 0L, 0, 0));
    ReflectionTestUtils.setField(eventPublisherCapability, "appId", "was");
  }

  private static String concatErrorMessage(String message, WorkflowError error) {
    return String.format(error.getErrorMessage(), message);
  }

  @Test(expected = WorkflowEventException.class)
  public void testPublishNullEntity() {
    eventPublisherCapability.publish(null, null);
  }

  @Test(expected = WorkflowEventException.class)
  public void testPublishNullExternalEntity() {
    eventHeaderEntity = EventHeaderEntity.builder().build();
    eventPublisherCapability.publish(eventHeaderEntity, null);
  }

  @Test
  public void testPublishDisableConfig() {
    eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);

    Mockito.verify(kafkaTemplate, Mockito.times(0)).send(Mockito.any(), Mockito.any());
  }

  @Test(expected = WorkflowEventException.class)
  public void testPublishEnableInvalidConfig() {

    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("external", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);

    eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
  }

  @Test
  public void testPublishNoMappingDefined() {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert
          .assertEquals(e.getMessage(), WorkflowError.INVALID_EVENT_CONFIG_ERROR.getErrorMessage());
    }
  }

  @Test
  public void testPublishInvalidTopicMappingDefined() {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask111", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert
          .assertEquals(e.getMessage(), WorkflowError.INVALID_EVENT_TOPIC_ERROR.getErrorMessage());
    }
  }

  @Test
  public void testPublishValidUseCase() throws ExecutionException, InterruptedException {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    Mockito.when(kafkaTemplate.send((ProducerRecord<String, String>) Mockito.any()))
        .thenReturn(listenableFuture);
    Mockito.when(listenableFuture.get()).thenReturn(sendResult);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
    } catch (WorkflowEventException e) {
      Assert.fail();
    }
  }

  @Test
  public void testKafkaHeaderNullEntityId() {
    EventHeaderEntity eventHeaderEntity = EventHeaderEntity.builder().build();
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(e.getWorkflowGeneralException().getMessage(),
          concatErrorMessage(KafkaConstants.ENTITY_ID_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testKafkaHeaderNullPublishEventType() {
    EventHeaderEntity eventHeaderEntity = EventHeaderEntity.builder()
        .idempotencyKey("Tid")
        .entityId("mock_entity")
        .targetAssetAlias("TargetAliasId")
        .build();

    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(e.getWorkflowGeneralException().getMessage(),
          concatErrorMessage(KafkaConstants.PUBLISH_EVENT_TYPE_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testKafkaHeaderNullOfferingId() {
    EventHeaderEntity eventHeaderEntity = EventHeaderEntity.builder()
        .idempotencyKey("Tid")
        .entityId("mock_entity")
        .targetAssetAlias("TargetAliasId")
        .publishEventType(PublishEventType.EXTERNAL_TASK)
        .build();

    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(
          e.getWorkflowGeneralException().getMessage(),
          concatErrorMessage(KafkaConstants.OFFERING_ID_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testListenableFutureInterruptionFailure()
      throws ExecutionException, InterruptedException {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    Mockito.when(kafkaTemplate.send((ProducerRecord<String, String>) Mockito.any()))
        .thenReturn(listenableFuture);
    Mockito.doThrow(new InterruptedException()).when(listenableFuture).get();
    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(WorkflowError.KAFKA_PUBLISH_ERROR.name(),
          e.getWorkflowGeneralException().getError().getMessage());
    }
  }

  @Test
  public void testListenableFutureExecutionFailure()
      throws ExecutionException, InterruptedException {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    Mockito.when(kafkaTemplate.send((ProducerRecord<String, String>) Mockito.any()))
        .thenReturn(listenableFuture);
    Mockito.doThrow(new ExecutionException(new RuntimeException())).when(listenableFuture).get();
    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(WorkflowError.KAFKA_PUBLISH_ERROR.name(),
          e.getWorkflowGeneralException().getError().getMessage());
    }
  }

  @Test
  public void testListenableFutureKafkaFailure() throws ExecutionException, InterruptedException {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    Mockito.when(kafkaTemplate.send((ProducerRecord<String, String>) Mockito.any()))
        .thenReturn(listenableFuture);
    Mockito.doThrow(new KafkaException("Exception")).when(listenableFuture).get();
    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
      Assert.assertEquals(WorkflowError.KAFKA_PUBLISH_ERROR.name(),
          e.getWorkflowGeneralException().getError().getMessage());
    }
  }

  //Test for publish method with partition key as process instance Id

  @Test(expected = WorkflowEventException.class)
  public void testPublishWithPartitionNullPartitionKey() {
    eventHeaderEntity = EventHeaderEntity.builder().build();
    eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned, null);
  }

  @Test
  public void testPublishWithPartitionValidUseCase() throws ExecutionException, InterruptedException {
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    Mockito.when(kafkaTemplate.send((ProducerRecord<String, String>) Mockito.any()))
        .thenReturn(listenableFuture);
    Mockito.when(listenableFuture.get()).thenReturn(sendResult);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned, partitionKey);
    } catch (WorkflowEventException e) {
      Assert.fail();
    }
  }

  @Test
  public void testPublishReturnNullWithPartition() {
    SendResult<String, String> result = eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned, partitionKey);
    Assert.assertEquals(result, null);
  }

  @Test
  public void testKafkaHeaderNullTxnId() {
    EventHeaderEntity eventHeaderEntity = EventHeaderEntity.builder().
        publishEventType(PublishEventType.EXTERNAL_TASK)
            .idempotencyKey("111")
            .offeringId("was")
            .targetAssetAlias("aaa")
            .entityId("e11")
            .tid("tid")
            .eventEntityType(EventEntityType.EXTERNALTASK)
            .build();
    eventConfiguration.getProducer().setEnabled(Boolean.TRUE);
    Map<String, String> entityTopicsMapping = new HashMap<>();
    entityTopicsMapping.put("externalTask", "abc");
    eventConfiguration.getProducer().setEntityTopicsMapping(entityTopicsMapping);

    try {
      eventPublisherCapability.publish(eventHeaderEntity, externalTaskAssigned);
      Assert.fail();
    } catch (WorkflowEventException e) {
    	Assert.assertEquals(WorkflowError.KAFKA_PUBLISH_ERROR.name(),
    	          e.getWorkflowGeneralException().getError().getMessage());
    }
  }

  /**
   * No Assertions possible on static method call.
   */
  @Test
  public void testLogEvent_piiLogEnable() {
	EventHeaderEntity eventHeaderEntity = EventHeaderEntity.builder().
		        publishEventType(PublishEventType.EXTERNAL_TASK)
		            .idempotencyKey("111")
		            .offeringId("was")
		            .targetAssetAlias("aaa")
		            .entityId("e11")
		            .tid("tid")
		            .eventEntityType(EventEntityType.EXTERNALTASK)
		            .build();
	ReflectionTestUtils.setField(eventPublisherCapability, "piiLogEnable", true);
	ReflectionTestUtils.invokeMethod(eventPublisherCapability, "logEventPublish", eventHeaderEntity,
			WorkflowStateTransitionEvents.builder()
			.activityMetadata(ActivityMetaData.builder().activityId("actId1").build()), "key");
  }

  @Test
  public void testGetKafkaHeaders_nullIntuitUserId() {
    EventHeaderEntity eventHeaderEntity =
        EventHeaderEntity.builder()
            .publishEventType(PublishEventType.EXTERNAL_TASK)
            .idempotencyKey("111")
            .offeringId("was")
            .entityId("e11")
            .tid("tid")
            .eventEntityType(EventEntityType.EXTERNALTASK)
            .build();
    RecordHeaders headers =
        ReflectionTestUtils.invokeMethod(
            eventPublisherCapability, "getKafkaHeaders", eventHeaderEntity);
    Assert.assertNotNull(headers);
    Iterable<Header> iter = headers.headers(EventHeaderConstants.INTUIT_USER_ID);
    Assert.assertFalse(iter.iterator().hasNext());
  }

  @Test
  public void testGetKafkaHeaders_presentIntuitUserId() {
    EventHeaderEntity eventHeaderEntity =
        EventHeaderEntity.builder()
            .publishEventType(PublishEventType.EXTERNAL_TASK)
            .idempotencyKey("111")
            .offeringId("was")
            .entityId("e11")
            .tid("tid")
            .intuitUserId("userId")
            .eventEntityType(EventEntityType.EXTERNALTASK)
            .build();
    RecordHeaders headers =
        ReflectionTestUtils.invokeMethod(
            eventPublisherCapability, "getKafkaHeaders", eventHeaderEntity);
    Assert.assertNotNull(headers);
    Iterable<Header> iter = headers.headers(EventHeaderConstants.INTUIT_USER_ID);
    Assert.assertTrue(iter.iterator().hasNext());
    Assert.assertEquals(
        "userId", new String(iter.iterator().next().value(), StandardCharsets.UTF_8));
  }
}
