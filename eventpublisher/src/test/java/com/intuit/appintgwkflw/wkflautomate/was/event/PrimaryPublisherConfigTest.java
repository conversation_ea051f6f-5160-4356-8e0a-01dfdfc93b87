package com.intuit.appintgwkflw.wkflautomate.was.event;


import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants.EVENT_CONFIGURATION_NULL_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants.INVALID_PRODUCER_ENVIRONMENT_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants.PRODUCER_EVENT_CONFIGURATION_NULL_ERROR;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventProducer;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventRegionConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.platform.jsk.config.client.idps.autoconfig.IdpsConnectionProperties;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * <AUTHOR>
 * @Test PublisherConfig
 */

@RunWith(MockitoJUnitRunner.class)
public class PrimaryPublisherConfigTest {

  private EventConfiguration eventConfiguration;

  private PrimaryPublisherConfig publisherConfig;
  private EventProducer eventProducer;

  @Before
  public void setUp() {
    eventProducer = new EventProducer();
    eventConfiguration = new EventConfiguration();
    EventRegionConfig eventRegionConfig = new EventRegionConfig();
    eventRegionConfig.setBootStrapServers(Arrays.asList("abc"));
    eventConfiguration.setPrimary(eventRegionConfig);
    eventConfiguration.setSecurityProtocol("ssl");
    eventConfiguration.setSslEnabledProtocol("tls");
    eventConfiguration.setIdpsConfig(new IdpsConnectionProperties());
    eventConfiguration.setProducer(eventProducer);

    publisherConfig = new PrimaryPublisherConfig(eventConfiguration);
  }

  // TODO: Will be fixed in another PR
//    @Test
//    public void testConfigValidCase() {
//
//        eventProducer.setEnvironment("qa");
//        setMockIdps();
//        publisherConfig = new PublisherConfig(eventConfiguration);
//        KafkaTemplate<String,String> kafkaTemplate = publisherConfig.kafkaTemplate();
//        Assert.assertNotNull(kafkaTemplate);
//        Assert.assertNotNull(kafkaTemplate.getProducerFactory());
//    }
//
//    @Test
//    public void testConfigValidCaseProducerFactory() {
//
//        eventProducer.setEnvironment("qa");
//        setMockIdps();
//        publisherConfig = new PublisherConfig(eventConfiguration);
//        DefaultKafkaProducerFactory producerFactory = publisherConfig.producerFactory();
//        Assert.assertNotNull(producerFactory);
//    }

  @Test
  public void testConfigInValidEnv() {
    try {
      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(),
          concatErrorMessage(INVALID_PRODUCER_ENVIRONMENT_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testConfigInValidEnumEnv() {
    try {
      eventProducer.setEnvironment("qal");
      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(),
          concatErrorMessage(INVALID_PRODUCER_ENVIRONMENT_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testConfigInValidIDPSConfig() {
    try {
      eventProducer.setEnvironment("qa");
      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (IllegalArgumentException e) {
      Assert.assertNotNull(e.getMessage());
    }
  }

  @Test
  public void testNullConfig() {
    try {
      publisherConfig = new PrimaryPublisherConfig(null);
      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(),
          concatErrorMessage(EVENT_CONFIGURATION_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testNullBootStrap() {
    try {
      EventConfiguration eventConfiguration = new EventConfiguration();
      publisherConfig = new PrimaryPublisherConfig(eventConfiguration);

      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(),
          concatErrorMessage(PRODUCER_EVENT_CONFIGURATION_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  @Test
  public void testNullProducerFactory() {
    try {
      publisherConfig = new PrimaryPublisherConfig(new EventConfiguration());
      new KafkaTemplate<>(publisherConfig.primaryProducerFactory());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(),
          concatErrorMessage(PRODUCER_EVENT_CONFIGURATION_NULL_ERROR,
              WorkflowError.INVALID_EVENT_CONFIG_ERROR));
    }
  }

  private static String concatErrorMessage(String message, WorkflowError error) {
    return String.format(error.getErrorMessage(), message);
  }
}
