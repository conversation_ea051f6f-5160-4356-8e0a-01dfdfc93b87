package com.intuit.appintgwkflw.wkflautomate.was.event;

import static com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher.parseKvMapString;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.batch.BatchAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.concurrent.ListenableFuture;

@RunWith(MockitoJUnitRunner.class)
public class SimpleEventPublisherTest {


  private SimpleEventPublisher simpleEventPublisher;
  @Mock
  private ApplicationContext applicationContext;

  @Mock
  private KafkaTemplate<String, String> kafkaTemplate;

  private ListenableFuture<SendResult<String, String>> listenableFuture;
  private SendResult<String, String> sendResult;

  private RecordMetadata recordMetadata = new RecordMetadata(new TopicPartition("topic", 0), 0, 0,
      1234567890L, 1234567890L, 1024, 1024);

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    simpleEventPublisher = new SimpleEventPublisher(applicationContext);

  }

  @Test
  public void testParseKvMapString() {
    String value = "key1=abc,key2=ef-g";
    ;
    Map<String, String> parseKvMapString = parseKvMapString(value, ",", "=");
    Assert.assertEquals("abc", parseKvMapString.getOrDefault("key1", "NA"));
    Assert.assertEquals("ef-g", parseKvMapString.getOrDefault("key2", "NA"));
  }

  @Test
  public void testParseKvMapStringWithEmptyValues() {
    String value = "key1=abc,key2=";
    Map<String, String> parseKvMapString = parseKvMapString(value, ",", "=");
    Assert.assertEquals(parseKvMapString.toString(), "{key1=abc}");
  }

  @Test
  public void testSend_success() throws ExecutionException, InterruptedException {
    SimpleEventPublisher eventPublisher = new SimpleEventPublisher(applicationContext);
    Mockito.when(applicationContext.getBean(KafkaTemplate.class)).thenReturn(kafkaTemplate);
    listenableFuture = Mockito.mock(ListenableFuture.class);
    sendResult = Mockito.mock(SendResult.class);
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID.toLowerCase(), "1234");
    headers.put(EventHeaderConstants.OWNER_ID.toLowerCase(), "5678");
    headers.put(EventHeaderConstants.INTUIT_USER_ID.toLowerCase(), "<EMAIL>");
    headers.put(KafkaHeaders.TOPIC, "test-topic");
    Mockito.when(kafkaTemplate.send(Mockito.any(Message.class))).thenReturn(listenableFuture);
    Mockito.when(listenableFuture.get()).thenReturn(sendResult);
    Mockito.when(sendResult.getRecordMetadata()).thenReturn(recordMetadata);
    eventPublisher.send("test message", headers);
    // verify that message was published successfully
    Mockito.verify(kafkaTemplate, Mockito.times(1)).send(Mockito.any(Message.class));
  }

  @Test
  public void testSend_failure() throws Exception {
    SimpleEventPublisher eventPublisher = new SimpleEventPublisher(applicationContext);
    Mockito.when(applicationContext.getBean(KafkaTemplate.class)).thenReturn(kafkaTemplate);
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID.toLowerCase(), "1234");
    headers.put(EventHeaderConstants.OWNER_ID.toLowerCase(), "5678");
    headers.put(EventHeaderConstants.INTUIT_USER_ID.toLowerCase(), "<EMAIL>");
    headers.put(KafkaHeaders.TOPIC, "test-topic");
    Mockito.when(kafkaTemplate.send(Mockito.any(Message.class)))
        .thenThrow(new RuntimeException("test"));
    try {
      eventPublisher.send("test message", headers);
      Assert.fail();
    } catch (WorkflowEventException workflowEventException) {
      Mockito.verify(kafkaTemplate, Mockito.times(1)).send(Mockito.any(Message.class));
    }


  }

  @Test
  public void testSend_successwithTopic() throws ExecutionException, InterruptedException {
    SimpleEventPublisher eventPublisher = new SimpleEventPublisher(applicationContext);
    Mockito.when(applicationContext.getBean(KafkaTemplate.class)).thenReturn(kafkaTemplate);
    listenableFuture = Mockito.mock(ListenableFuture.class);
    sendResult = Mockito.mock(SendResult.class);
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID.toLowerCase(), "1234");
    headers.put(EventHeaderConstants.OWNER_ID.toLowerCase(), "5678");
    headers.put(EventHeaderConstants.INTUIT_USER_ID.toLowerCase(), "<EMAIL>");
    headers.put(KafkaHeaders.TOPIC, "test-topic");
    Mockito.when(kafkaTemplate.send(Mockito.any(Message.class))).thenReturn(listenableFuture);
    Mockito.when(listenableFuture.get()).thenReturn(sendResult);
    Mockito.when(sendResult.getRecordMetadata()).thenReturn(recordMetadata);
    eventPublisher.send("test message", headers, "test");
    // verify that message was published successfully
    Mockito.verify(kafkaTemplate, Mockito.times(1)).send(Mockito.any(Message.class));
  }

  @Test
  public void testSend_failureWithTopic() throws Exception {
    SimpleEventPublisher eventPublisher = new SimpleEventPublisher(applicationContext);
    Mockito.when(applicationContext.getBean(KafkaTemplate.class)).thenReturn(kafkaTemplate);
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID.toLowerCase(), "1234");
    headers.put(EventHeaderConstants.OWNER_ID.toLowerCase(), "5678");
    headers.put(EventHeaderConstants.INTUIT_USER_ID.toLowerCase(), "<EMAIL>");
    headers.put(EventHeaderConstants.KAFKA_TOPIC_NAME.toLowerCase(), "test");
    Mockito.when(kafkaTemplate.send(Mockito.any(Message.class)))
        .thenThrow(new RuntimeException("test"));
    try {
      eventPublisher.send("test message", headers, "test");
      Assert.fail();
    } catch (WorkflowEventException workflowEventException) {
      Mockito.verify(kafkaTemplate, Mockito.times(1)).send(Mockito.any(Message.class));
    }


  }
}