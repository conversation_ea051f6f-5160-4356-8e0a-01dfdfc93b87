package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractGraphqlClient {

    @Autowired
    protected ApolloClientConfig apolloClientConfig;

    public abstract ServiceName serviceName();

    protected GraphqlRequest.GraphqlRequestBuilder baseRequest() {
        return GraphqlRequest.builder().serviceName(serviceName());
    }
}
