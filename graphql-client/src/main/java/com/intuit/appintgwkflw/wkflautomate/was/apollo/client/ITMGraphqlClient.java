package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.AuthorizationType;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementDeleteTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_DeleteTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Client class to invoke Task Management Graphql queries
 */
@Retry(name = ResiliencyConstants.APOLLO_GRAPHQL_CLIENT)
@Component
@AllArgsConstructor
public class ITMGraphqlClient extends AbstractGraphqlClient {

  private WASApolloClient wasApolloClient;

  @Override
  public ServiceName serviceName() {
    return ServiceName.ITM;
  }

  /**
   * Creates a flat task with the task management service
   *
   * @param taskManagementCreateTaskInput - Details of the new task to be created
   * @param customHeaders - custom headers to identify the task domain in task service
   * @return : The newly created task
   */
  @ServiceMetric(serviceName = ServiceName.ITM)
  public TaskManagementCreateTaskMutation.Task createTask(
      TaskManagement_CreateTaskInput taskManagementCreateTaskInput,
      Map<String, String> customHeaders) {
    GraphqlRequest graphqlRequest =
        baseRequest()
            .mutation(
                TaskManagementCreateTaskMutation.builder()
                    .input(taskManagementCreateTaskInput)
                    .build())
            .customHeaders(customHeaders)
            .authorizationType(AuthorizationType.SYSTEM_OFFLINE)
            .build();

    GraphqlResponse<TaskManagementCreateTaskMutation.Data> graphqlResponse =
        wasApolloClient.mutate(graphqlRequest);
    
    verifyError(graphqlRequest, graphqlResponse);
    
    TaskManagementCreateTaskMutation.Data createdTaskData = graphqlResponse.getResponse().getData();

    return createdTaskData.taskManagementCreateTask().task();
  }

  /**
   * Update a particular flat task with the task management service
   *
   * @param taskManagementUpdateTaskInput - Parameters of the task to be updated
   * @param customHeaders - custom headers to identify the task domain in task service
   * @return - The task post update
   */
  @ServiceMetric(serviceName = ServiceName.ITM)
  public TaskManagementUpdateTaskMutation.Task updateTask(
      TaskManagement_UpdateTaskInput taskManagementUpdateTaskInput,
      Map<String, String> customHeaders) {

    GraphqlRequest graphqlRequest =
        baseRequest()
            .mutation(
                TaskManagementUpdateTaskMutation.builder()
                    .input(taskManagementUpdateTaskInput)
                    .build())
            .customHeaders(customHeaders)
            .authorizationType(AuthorizationType.SYSTEM_OFFLINE)
            .build();

    GraphqlResponse<TaskManagementUpdateTaskMutation.Data> graphqlResponse =
        wasApolloClient.mutate(graphqlRequest);

    verifyError(graphqlRequest, graphqlResponse);

    TaskManagementUpdateTaskMutation.Data createdTaskData = graphqlResponse.getResponse().getData();

    return createdTaskData.taskManagementUpdateTask().task();
  }

  /**
   * Deletes a particular flat task with the task management service
   *
   * @param taskId - The id of the task to be deleted
   * @param customHeaders - custom headers to identify the task domain in task service
   * @return - Boolean if the task was deleted or not
   */
  @ServiceMetric(serviceName = ServiceName.ITM)
  public boolean deleteTask(String taskId, Map<String, String> customHeaders) {

    TaskManagement_DeleteTaskInput taskManagementDeleteTaskInput =
        TaskManagement_DeleteTaskInput.builder().id(taskId).build();

    GraphqlRequest graphqlRequest =
        baseRequest()
            .mutation(
                TaskManagementDeleteTaskMutation.builder()
                    .input(taskManagementDeleteTaskInput)
                    .build())
            .customHeaders(customHeaders)
            .authorizationType(AuthorizationType.SYSTEM_OFFLINE)
            .build();

    GraphqlResponse<TaskManagementDeleteTaskMutation.Data> graphqlResponse =
        wasApolloClient.mutate(graphqlRequest);

    verifyError(graphqlRequest, graphqlResponse);

    return true;
  }

  /**
   * Fetches a particular flat task with the task management service
   *
   * @param taskId - The id of the task to be fetched
   * @param customHeaders - custom headers to identify the task domain in task service
   * @return - The fetched task
   */
  @ServiceMetric(serviceName = ServiceName.ITM)
  public TaskManagementTaskQuery.TaskManagementTask getTask(
      String taskId, Map<String, String> customHeaders) {

    GraphqlRequest graphqlRequest =
        baseRequest()
            .query(TaskManagementTaskQuery.builder().taskManagementTaskId(taskId).build())
            .customHeaders(customHeaders)
            .authorizationType(AuthorizationType.SYSTEM_OFFLINE)
            .build();

    GraphqlResponse<TaskManagementTaskQuery.Data> graphqlResponse =
        wasApolloClient.query(graphqlRequest);
    
    verifyError(graphqlRequest, graphqlResponse);

    TaskManagementTaskQuery.Data createdTaskData = graphqlResponse.getResponse().getData();

    return createdTaskData.taskManagementTask();
  }

  private void verifyError(GraphqlRequest graphqlRequest, GraphqlResponse<?> graphqlResponse) {

    if (graphqlResponse.isHasErrorResponse()
            && graphqlResponse.getResponse().getErrors().stream()
            .anyMatch(
                    graphqlError ->
                            apolloClientConfig.getGraphqlServiceProperties(graphqlRequest.getServiceName())
                                    .getRetryErrorCodes().contains(graphqlError.getMessage()))) {
      throw new WorkflowRetriableException(
              WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, graphqlRequest.getServiceName(), graphqlResponse.getError()
      );
    }

    WorkflowVerfiy.verify(
        graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
        () -> {
          throw new WorkflowGeneralException(
              WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, graphqlResponse.getError());
        });
  }
}
