package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.AuthorizationType;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.OfflineTicketClientConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Email;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.identity.entity.graphql.CreateTrustGrantMutation;
import com.intuit.identity.entity.graphql.LookupFirmAssociationsQuery;
import com.intuit.identity.entity.graphql.ProfileSearchQuery;
import com.intuit.identity.entity.graphql.ProfilesQuery;
import com.intuit.identity.entity.graphql.type.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * Client class to invoke Identity Graphql queries
 */
@Component
@AllArgsConstructor
public class IdentityGraphqlClient extends AbstractGraphqlClient {

    public static final int MAX_ROWS = 100;

    private WASApolloClient wasApolloClient;

    @Override
    public ServiceName serviceName() {
        return ServiceName.IDENTITY;
    }

    /**
     * Fetch personaId of the current user
     *
     * @param realmId - companyId
     * @param authId  - authId
     * @return personaId
     */
    @ServiceMetric(serviceName = ServiceName.IDENTITY)
    public String getPersonaId(String realmId, String authId) {

        AccountInput accountInput = accountInput(realmId);
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .claimedByFilter(
                        ProfilesClaimedByFilterInput.builder()
                                .claimedBy(
                                        StringFilterInput.builder().eq(authId).build()
                                ).build()
                )
                .build();

        PaginationInput paginationInput = paginationInput(1, null);

        GraphqlResponse<ProfilesQuery.Data> graphqlResponse =
                invokeAccountProfilesFilterQuery(accountInput, paginationInput, profilesConnectionFilterInput, AuthorizationType.PROPAGATE);

        ProfilesQuery.Data dataResponse = graphqlResponse.getResponse().getData();
        Optional<ProfilesQuery.Profiles> optionalProfiles = Optional.ofNullable(dataResponse)
                .map(ProfilesQuery.Data::account)
                .map(ProfilesQuery.Account::profiles);

        String personaId = optionalProfiles.map(ProfilesQuery.Profiles::edges)
                .orElse(Collections.emptyList())
                .stream().findFirst()
                .map(edge -> edge.node().id()).orElse(null);
        WorkflowLogger.logInfo("Fetched personaId=%s", personaId);

        return personaId;
    }

    /**
     * Fetches the EMPLOYEE type profiles of the realm and filters them against the Set of userIds
     *
     * @param realmId - companyId/ownerId
     * @param userIds - list of userIds need to be filtered
     * @return Returns map of userId, Persona
     */
    @ServiceMetric(serviceName = ServiceName.IDENTITY)
    public Map<String, Persona> getRealmPersonas(
            @Tracer(key = WASContextEnums.OWNER_ID) final String realmId, Set<String> userIds) {

        return fetchPersonas(realmId, userIds);
    }

    /**
     * helper method to fetch the EMPLOYEE type profiles of the realm and filters them against the Set of userIds
     */
    private Map<String, Persona> fetchPersonas(String realmId,
                                               Set<String> userIds) {

        Map<String, Persona> responseMap = new HashMap<>();
        boolean hasNext = true;
        String endCursor = null;

        while (hasNext && responseMap.size() < userIds.size()) {
            GraphqlResponse<ProfilesQuery.Data> graphqlResponse =
                    prepareAndInvokeGetByProfilesRelationshipQuery(realmId, endCursor, AuthorizationType.SYSTEM_OFFLINE_CONTEXT_REALM);

            ProfilesQuery.Data dataResponse = graphqlResponse.getResponse().getData();
            Optional<ProfilesQuery.Profiles> optionalProfiles = Optional.ofNullable(dataResponse)
                    .map(ProfilesQuery.Data::account)
                    .map(ProfilesQuery.Account::profiles);
            hasNext = optionalProfiles.map(ProfilesQuery.Profiles::pageInfo)
                    .map(ProfilesQuery.PageInfo::hasNextPage)
                    .orElse(false);
            endCursor = optionalProfiles.map(ProfilesQuery.Profiles::pageInfo)
                    .map(ProfilesQuery.PageInfo::endCursor)
                    .orElse(null);
            optionalProfiles.map(ProfilesQuery.Profiles::edges)
                    .ifPresent(edges ->
                            edges.forEach(edge ->
                                    Optional.ofNullable(edge.node().claimedBy()).filter(userIds::contains).ifPresent(authId -> {
                                        String emailId = Optional.ofNullable(edge.node().personInfo())
                                                .map(ProfilesQuery.PersonInfo::contactInfo)
                                                .map(ProfilesQuery.ContactInfo::emails)
                                                .orElse(Collections.emptyList())
                                                .stream()
                                                .filter(email -> Objects.nonNull(email.email()))
                                                .findFirst().map(ProfilesQuery.Email::email).orElse(null);

                                        Optional.ofNullable(emailId).ifPresent(email -> {
                                            Persona persona = Persona.builder()
                                                    .personaId(edge.node().id())
                                                    .email(Email.builder().emailId(email).build())
                                                    .build();
                                            responseMap.put(authId, persona);

                                        });

                                    })));
        }
        return responseMap;

    }

    /**
     * Invokes account.profiles query with EMPLOYEE relationShip filter
     * @param realmId - ownerId
     * @param endCursor - endCursor
     * @param authorizationType - type of auth to be used
     */
    private GraphqlResponse<ProfilesQuery.Data> prepareAndInvokeGetByProfilesRelationshipQuery(String realmId,
                                                                                               String endCursor,
                                                                                               AuthorizationType authorizationType) {
        AccountInput accountInput = accountInput(realmId);
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true)
                        .includeProvisional(true)
                        .relationship(AccountRelationship.EMPLOYEE).build())
                .build();

        PaginationInput paginationInput = PaginationInput.builder()
                .first(MAX_ROWS)
                .after(endCursor)
                .build();

        return invokeAccountProfilesFilterQuery(accountInput, paginationInput, profilesConnectionFilterInput, authorizationType);
    }

    /**
     * Prepares AccountInput object
     * @param id - realmId
     */
    private AccountInput accountInput(String id) {
        return AccountInput.builder().id(id).build();
    }

    /**
     * Invokes account.profiles query
     * @param accountInput - input
     */
    private GraphqlResponse<ProfilesQuery.Data> invokeAccountProfilesFilterQuery(AccountInput accountInput,
                                                                                 PaginationInput paginationInput,
                                                                                 ProfilesConnectionFilterInput profilesConnectionFilterInput,
                                                                                 AuthorizationType authorizationType) {
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .pagination(paginationInput)
                .build();

        GraphqlRequest graphqlRequest = baseRequest().query(profilesQuery).authorizationType(authorizationType).build();

        GraphqlResponse<ProfilesQuery.Data> graphqlResponse = wasApolloClient.query(graphqlRequest);
        WorkflowVerfiy.verify(graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
                () -> {
                    throw new WorkflowGeneralException(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED, graphqlResponse.getError());
                });
        return graphqlResponse;
    }

    /**
     * Fetches personaId of the accountant user
     * @param realmId - client company in auth context
     * @param userId - authId of accountant
     */
    @ServiceMetric(serviceName = ServiceName.IDENTITY)
    public String getAccountantPersonaId(String realmId, String userId) {

        Map<String, String> mirrorAccountIdsAndPersonaMap = getAssociatedFirmMirrorAccountIds(realmId);

        boolean hasNext = true;
        String endCursor = null;
        Optional<String> result = Optional.empty();
        while (hasNext && result.isEmpty() && !mirrorAccountIdsAndPersonaMap.isEmpty()) {
            GraphqlResponse<ProfileSearchQuery.Data> graphqlResponse = invokeProfilesSearchQuery(userId, MAX_ROWS, endCursor);
            ProfileSearchQuery.Data response = graphqlResponse.getResponse().getData();
            Optional<ProfileSearchQuery.ProfileSearch> profileSearch = Optional.ofNullable(response)
                    .map(ProfileSearchQuery.Data::profileSearch);
            Optional<ProfileSearchQuery.PageInfo> pageInfo = profileSearch.map(ProfileSearchQuery.ProfileSearch::pageInfo);
            hasNext = pageInfo.map(ProfileSearchQuery.PageInfo::hasNextPage).orElse(false);
            endCursor = pageInfo.map(ProfileSearchQuery.PageInfo::endCursor).orElse(null);
            List<ProfileSearchQuery.Edge> profiles = profileSearch.map(ProfileSearchQuery.ProfileSearch::edges)
                    .orElse(Collections.emptyList());
            Optional<ProfileSearchQuery.Edge> edge = profiles.stream()
                    .filter(i -> mirrorAccountIdsAndPersonaMap.containsKey(i.node().accountId())).findFirst();
            result = edge.map(i -> mirrorAccountIdsAndPersonaMap.get(i.node().accountId()));
        }
        WorkflowLogger.logInfo("Fetched accountant personaId=%s", result.orElse(null));
        return result.orElseThrow(() -> new WorkflowGeneralException(WorkflowError.IDENTITY_INVALID_RESPONSE_RECEIVED));
    }

    /**
     * Fetches mirrorAccountId i.e. realmId of the accountants firms in the company
     */
    private Map<String, String> getAssociatedFirmMirrorAccountIds(String realmId) {
        GraphqlResponse<LookupFirmAssociationsQuery.Data> response = invokeLookFirmsAssociationQuery(realmId);
        Map<String, String> mirrorAccountIdsAndPersonaIdMap = new HashMap<>();
        LookupFirmAssociationsQuery.Data queryResponse = response.getResponse().getData();
        Optional.ofNullable(queryResponse)
                .map(LookupFirmAssociationsQuery.Data::account)
                .map(LookupFirmAssociationsQuery.Account::profiles)
                .ifPresent(profiles ->
                        profiles.edges()
                                .forEach(edge -> mirrorAccountIdsAndPersonaIdMap.put(edge.node().mirrorAccountId(), edge.node().id())));
        if (mirrorAccountIdsAndPersonaIdMap.isEmpty()){
            WorkflowLogger.logWarn("No accountant firm profiles were found");
        }
        WorkflowLogger.logInfo("Fetched mirrorAccount and persona map=%s", mirrorAccountIdsAndPersonaIdMap);
        return mirrorAccountIdsAndPersonaIdMap;
    }

    /**
     * Invokes LookFirmsAssociation query
     * @param realmId - companyId
     */
    private GraphqlResponse<LookupFirmAssociationsQuery.Data> invokeLookFirmsAssociationQuery(String realmId) {
        AccountInput accountInput = accountInput(realmId);
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true)
                        .includeProvisional(true)
                        .relationship(AccountRelationship.FIRM).build())
                .build();
        TrustGrantFilterInput trustGrantFilterInput = TrustGrantFilterInput.builder()
                .assignmentGrantsFilter(
                        AssignmentGrantFilterInput.builder()
                                .isPrimaryProfile(true)
                                .build()
                ).build();
        LookupFirmAssociationsQuery lookupFirmAssociationsQuery =
                LookupFirmAssociationsQuery.builder()
                        .input(accountInput)
                        .filterBy(profilesConnectionFilterInput)
                        .trustGrantsFilterBy2(trustGrantFilterInput)
                        .build();
        GraphqlRequest graphqlRequest = baseRequest().query(lookupFirmAssociationsQuery).build();
        GraphqlResponse<LookupFirmAssociationsQuery.Data> graphqlResponse = wasApolloClient.query(graphqlRequest);
        WorkflowVerfiy.verify(graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
                () -> {
                    throw new WorkflowGeneralException(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED, graphqlResponse.getError());
                });
        return graphqlResponse;

    }


    /**
     * Invokes profilesSearch query with userId in context
     * @param userId - authId
     * @param rows - no. of rows to fetch in single page
     * @param endCursor - cursor for the next page
     */
    private GraphqlResponse<ProfileSearchQuery.Data> invokeProfilesSearchQuery(String userId, int rows, String endCursor) {
        ProfileSearchInput profileSearchInput = ProfileSearchInput.builder()
                .claimedBy(
                        StringFilterInput.builder()
                                .eq(userId)
                                .build()
                ).build();

        ProfileSearchQuery profileSearchQuery = ProfileSearchQuery.builder()
                .filterBy(profileSearchInput)
                .pagination(paginationInput(rows, endCursor))
                .build();
        GraphqlRequest graphqlRequest = baseRequest().query(profileSearchQuery).build();
        GraphqlResponse<ProfileSearchQuery.Data> graphqlResponse = wasApolloClient.query(graphqlRequest);
        WorkflowVerfiy.verify(graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
                () -> {
                    throw new WorkflowGeneralException(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED, graphqlResponse.getError());
                });
        return graphqlResponse;
    }

    /**
     * Prepare PaginationInput object
     * @param rows - no. of rows to be fetched
     * @param endCursor - cursor of the next page.
     */
    private PaginationInput paginationInput(int rows, String endCursor) {
        return PaginationInput.builder()
                .first(rows)
                .after(endCursor)
                .build();
    }

    @ServiceMetric(serviceName = ServiceName.IDENTITY)
    public void createTrustGrant(String realmId) {
        CreateTrustGrantInput createTrustGrantInput = CreateTrustGrantInput.builder()
                .trustingEntityId(realmId)
                .trustedEntityId(OfflineTicketClientConstants.SUBJECT_ID)
                .associationType(AssociationType.DELEGATION)
                .authorizationGrantsScope(AuthorizationGrantsScopeInput.builder()
                        .roles(List.of(OfflineTicketClientConstants.JOB_PURPOSE))
                        .build()
                ).build();

        CreateTrustGrantMutation createTrustGrantMutation = CreateTrustGrantMutation.builder()
                .input(createTrustGrantInput)
                .build();
        GraphqlRequest graphqlRequest = baseRequest().mutation(createTrustGrantMutation).build();
        GraphqlResponse<CreateTrustGrantMutation.Data> graphqlResponse = wasApolloClient.mutate(graphqlRequest);
        WorkflowVerfiy.verify(graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
                () -> {
                    throw new WorkflowGeneralException(WorkflowError.IUS_AUTHORIZATION_DELEGATION_ERROR, graphqlResponse.getError());
                });
    }
}
