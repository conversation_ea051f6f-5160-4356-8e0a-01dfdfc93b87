package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.apollographql.apollo.api.Input;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.AuthorizationType;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.variability.entity.graphql.GetAttributeQuery;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Client class to invoke Variability Graphql queries
 */
@Retry(name = ResiliencyConstants.APOLLO_GRAPHQL_CLIENT)
@Component
@AllArgsConstructor
public class VariabilityGraphqlClient extends AbstractGraphqlClient {
  private WASApolloClient wasApolloClient;

  @Override
  public ServiceName serviceName() {
    return ServiceName.VARIABILITY;
  }

  @ServiceMetric(serviceName = ServiceName.VARIABILITY)
  public List<GetAttributeQuery.VariabilityEngine_getDecisionBatch> getDecisionBatch(
      List<String> decision, Map<String, String> customHeaders) {

    GraphqlRequest graphqlRequest =
        baseRequest()
            .query(GetAttributeQuery.builder().decisionsInput(Input.fromNullable(decision)).build())
            .customHeaders(customHeaders)
            .authorizationType(AuthorizationType.SYSTEM_OFFLINE_CONTEXT_REALM)
            .build();

    GraphqlResponse<GetAttributeQuery.Data> graphqlResponse = wasApolloClient.query(graphqlRequest);
    verifyError(graphqlRequest, graphqlResponse);
    GetAttributeQuery.Data response = graphqlResponse.getResponse().getData();
    return response.variabilityEngine_getDecisionBatch();
  }

  private void verifyError(GraphqlRequest graphqlRequest, GraphqlResponse<?> graphqlResponse) {
    if (graphqlResponse.isHasErrorResponse()
        && graphqlResponse.getResponse().getErrors().stream()
            .anyMatch(
                graphqlError ->
                    apolloClientConfig
                        .getGraphqlServiceProperties(graphqlRequest.getServiceName())
                        .getRetryErrorCodes()
                        .contains(graphqlError.getMessage()))) {
      throw new WorkflowRetriableException(
          WorkflowError.VARIABILITY_GRAPHQL_CLIENT_FAILURE,
          graphqlRequest.getServiceName(),
          graphqlResponse.getError());
    }

    WorkflowVerfiy.verify(
        graphqlResponse.isHasErrorResponse() || graphqlResponse.isHasExceptions(),
        () -> {
          throw new WorkflowGeneralException(
              WorkflowError.VARIABILITY_GRAPHQL_CLIENT_FAILURE, graphqlResponse.getError());
        });
  }
}
