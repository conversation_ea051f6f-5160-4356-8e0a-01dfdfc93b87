package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.apollographql.apollo.ApolloCall;
import com.apollographql.apollo.ApolloMutationCall;
import com.apollographql.apollo.ApolloQueryCall;
import com.apollographql.apollo.api.Error;
import com.apollographql.apollo.api.Response;
import com.apollographql.apollo.reactor.ReactorApollo;
import com.apollographql.apollo.request.RequestHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.AuthorizationType;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.util.ReactorLoggingUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

/**
 * <AUTHOR>
 * Provides implementation of Apollo Query and Mutation requests
 */
@Component
public class WASApolloClient {


    @Autowired
    private HeaderPopulator headerPopulator;

    @Autowired
    private WASContextHandler wasContextHandler;

    @Autowired
    private OfflineTicketClient offlineTicketClient;

    @Autowired
    private ApolloClientConfig apolloClientConfig;


    /**
     * Graphql query request
     */
    public <T> GraphqlResponse<T> query(GraphqlRequest graphqlRequest) {

        ApolloQueryCall<T> apolloQueryCall = apolloClientConfig.getApolloClient(graphqlRequest.getServiceName())
                .query(graphqlRequest.getQuery())
                .toBuilder()
                .requestHeaders(addRequestHeaders(graphqlRequest))
                .build();


        return execute(apolloQueryCall, graphqlRequest);
    }

    /**
     * Graphql mutation request
     */
    public <T> GraphqlResponse<T> mutate(GraphqlRequest graphqlRequest) {

        ApolloMutationCall<T> apolloCall = apolloClientConfig.getApolloClient(graphqlRequest.getServiceName())
                .mutate(graphqlRequest.getMutation())
                .toBuilder()
                .requestHeaders(addRequestHeaders(graphqlRequest))
                .build();


        return execute(apolloCall, graphqlRequest);
    }


    /**
     * Adds required headers to the graphql query/mutation requests
     * @param graphqlRequest
     * @return
     */
    private RequestHeaders addRequestHeaders(GraphqlRequest graphqlRequest) {
        String authHeader = getAuthorizationHeader(graphqlRequest.getAuthorizationType());

        RequestHeaders.Builder requestHeadersBuilder =
                RequestHeaders.builder()
                        .addHeader(WASContextEnums.AUTHORIZATION_HEADER.getValue(), authHeader)
                        .addHeader(
                                WorkflowConstants.INTUIT_TID,
                                Optional.ofNullable(wasContextHandler.get(WASContextEnums.INTUIT_TID))
                                        .orElse(UUID.randomUUID().toString()));

        if (!MapUtils.isEmpty(graphqlRequest.getCustomHeaders())) {
            requestHeadersBuilder.addHeaders(graphqlRequest.getCustomHeaders());
        }

        return requestHeadersBuilder.build();
    }

    /**
     * Fetches the auth header based ion authorization type
     */
    private String getAuthorizationHeader(AuthorizationType authorizationType) {
        switch (authorizationType){
            case PROPAGATE:
                return headerPopulator.constructAuthzHeaderFromContext();
            case SYSTEM_OFFLINE_CONTEXT_REALM:
                return offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(
                        wasContextHandler.get(WASContextEnums.OWNER_ID)
                );
            case SYSTEM_OFFLINE:
            default:
                return offlineTicketClient.getSystemOfflineHeadersForOfflineJob();
        }
    }

    /**
     * Executes the mutation/query request and returns error/success response
     */
    private <T> GraphqlResponse<T> execute(ApolloCall<T> apolloCall, GraphqlRequest graphqlRequest) {

        GraphqlResponse.GraphqlResponseBuilder<T> graphqlResponseBuilder = GraphqlResponse.builder();

        Mono.deferContextual(contextView -> ReactorApollo.from(apolloCall)
                        .doOnNext(tResponse -> {
                            graphqlResponseBuilder.response(tResponse);
                            Optional.ofNullable(tResponse.getErrors())
                                    .ifPresent(i -> {
                                        graphqlResponseBuilder.hasErrorResponse(true);
                                        Error error = i.get(0);
                                        ReactorLoggingUtils.executeInContext(contextView,
                                                () -> WorkflowLogger.logError("Error response received serviceName=%s error=%s ",
                                                        graphqlRequest.getServiceName(), error));
                                    });
                        })
                        .doOnError(throwable -> {
                            graphqlResponseBuilder.error(throwable).hasExceptions(true);
                            ReactorLoggingUtils.executeInContext(contextView,
                                    () -> WorkflowLogger.logError(throwable, "Graphql call failed serviceName=%s",
                                            graphqlRequest.getServiceName()));
                        })
                        .onErrorReturn((Response<T>) Response.builder(apolloCall.operation()).build()))
                .contextWrite(Context.of(ReactorLoggingUtils.MDC_KEY, ReactorLoggingUtils.getMDCCopy()))
                .block();

        return graphqlResponseBuilder.build();
    }
}
