package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config;

import com.apollographql.apollo.ApolloClient;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.ServiceProperties;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import lombok.AllArgsConstructor;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * Initializes beans related to Apollo client
 */
@AllArgsConstructor
@Configuration
public class ApolloClientConfig {

    private static final Map<ServiceName, ApolloClient> APOLLO_CLIENT_MAP = new HashMap<>();

    private GraphqlEndpointConfig graphqlEndpointConfig;

    private OkHttpConfig okHttpConfig;

    /**
     * Create OkHttpClient bean
     */
    OkHttpClient okHttpClient() {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(okHttpConfig.getMaxRequests());
        dispatcher.setMaxRequestsPerHost(okHttpConfig.getMaxRequestPerHost());
        return new OkHttpClient.Builder()
                .callTimeout(okHttpConfig.getCallTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(okHttpConfig.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(okHttpConfig.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .connectTimeout(okHttpConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .dispatcher(dispatcher)
                .build();
    }

    @PostConstruct
    public void initApolloClientMap() {
        OkHttpClient okHttpClient = okHttpClient();
        graphqlEndpointConfig.getServices().forEach(
                (k, v) -> {
                    ApolloClient apolloClient = ApolloClient.builder()
                            .serverUrl(v.getUrl())
                            .okHttpClient(okHttpClient)
                            .build();
                    APOLLO_CLIENT_MAP.put(k, apolloClient);
                }
        );
    }

    /**
     * Get the apollo client registered against serviceName
     * @param serviceName - name of service
     * @return ApolloClient
     */
    public ApolloClient getApolloClient(ServiceName serviceName) {
        return Optional.ofNullable(APOLLO_CLIENT_MAP.get(serviceName))
                .orElseThrow(
                        () -> new WorkflowGeneralException(
                                WorkflowError.APOLLO_CLIENT_CONFIG_NOT_DEFINED, serviceName.name()));
    }

    /**
     * Get the service properties for the graphql service
     * @param serviceName - name of service
     * @return ServiceProperties
     */
    public ServiceProperties getGraphqlServiceProperties(ServiceName serviceName) {
        return Optional.ofNullable(graphqlEndpointConfig.getServices().get(serviceName))
                .orElseThrow(
                        () -> new WorkflowGeneralException(
                                WorkflowError.APOLLO_CLIENT_CONFIG_NOT_DEFINED, serviceName.name()));
    }


}
