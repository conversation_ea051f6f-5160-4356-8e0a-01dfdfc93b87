package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.ServiceProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * Defines graphql endpoint configurations for apollo client
 */
@Getter
@AllArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "graphql-config")
public class GraphqlEndpointConfig {

    private Map<ServiceName, ServiceProperties> services;

}
