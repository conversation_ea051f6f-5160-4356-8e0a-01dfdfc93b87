package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * OkHttp client config
 */
@ConfigurationProperties(prefix = "okhttp-client")
@Configuration
@Getter
@Setter
public class OkHttpConfig {

    private int readTimeout;

    private int writeTimeout;

    private int connectTimeout;

    private int callTimeout;

    private int maxRequests;

    private int maxRequestPerHost;

}
