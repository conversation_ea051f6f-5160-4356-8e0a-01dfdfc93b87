package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity;

/**
 * <AUTHOR>
 * Defines various authorization types supported in client
 */
public enum AuthorizationType {

    PROPAGATE, //Propagate authorization info in MDC context by replacing appId and appSecret

    SYSTEM_OFFLINE, // Use System offline ticket

    SYSTEM_OFFLINE_CONTEXT_REALM // WASContextEnums.OWNER_ID must be present in MDC context
}
