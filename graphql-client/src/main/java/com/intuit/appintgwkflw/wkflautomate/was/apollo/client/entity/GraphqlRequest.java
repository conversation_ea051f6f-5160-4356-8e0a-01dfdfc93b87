package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity;

import com.apollographql.apollo.api.Mutation;
import com.apollographql.apollo.api.Query;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * Graphql request input to WASApolloClient
 */
@Getter
@Builder
public class GraphqlRequest {

    private ServiceName serviceName;

    //Query request
    private Query query;

    //Mutation request
    private Mutation mutation;

    private Map<String, String> customHeaders;

    @Builder.Default
    private AuthorizationType authorizationType = AuthorizationType.PROPAGATE;
}
