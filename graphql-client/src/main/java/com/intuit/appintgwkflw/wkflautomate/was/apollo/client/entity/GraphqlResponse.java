package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity;

import com.apollographql.apollo.api.Response;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * Response body from WAS Apollo client
 * @param <T> Response type of query/mutation request
 */
@Builder
@AllArgsConstructor
@Getter
public class GraphqlResponse<T> {

    private Throwable error;

    private Response<T> response;

    // set to true when http response is 200, but has errors fields populated
    private boolean hasErrorResponse;

    // set to true when http response is not 200
    private boolean hasExceptions;
}
