
## https://github.intuit.com/pages/identity-manage/docs/2.0/queries/account-profiles/
query Profiles($input: AccountInput!, $filterBy: ProfilesConnectionFilterInput!, $pagination: PaginationInput) {
    account (input: $input) {
        id,
        status,
        accountType,
        namespaceId,
        accountProfile{
            id,
            profileType,
            profileStatus
        },
        profiles(filterBy: $filterBy,  pagination: $pagination) {
            edges {
                node {
                    id
                    accountId
                    profileType
                    displayName
                    claimedBy
                    accountRelationships
                    profileStatus
                    preferences {
                        hidden
                    }
                    personInfo {
                        contactInfo {
                            emails {
                                id
                                email
                            }
                            phoneNumbers{
                                id
                                originalNumber
                            }
                        }
                    }
                }  ,
                cursor

            },
            pageInfo {
                endCursor
                hasNextPage
            }
        }
    }
}

# https://github.intuit.com/pages/identity-manage/docs/2.0/queries/profileSearch/#filter2-claimedby
query profileSearch ($filterBy: ProfileSearchInput!,$pagination: PaginationInput) {
    profileSearch (filterBy: $filterBy, pagination: $pagination) {
        edges {
            node {
                id
                accountId,
                profileStatus,
                profileType
                displayName
                claimedBy
                pseudonymId
            }
            cursor

        },
        pageInfo {
            endCursor
            hasNextPage
        }
    }
}

# https://github.intuit.com/pages/identity-manage/docs/2.0/queries/cams/#lookup-accounting-firms-for-a-qbo-realm
query lookupFirmAssociations($input: AccountInput!, $filterBy: ProfilesConnectionFilterInput!, $trustGrantsFilterBy2: TrustGrantFilterInput) {
    account(input: $input) {
        profiles(filterBy: $filterBy) {
            edges {
                node {
                    id
                    mirrorAccountId
                    trustGrants(filterBy: $trustGrantsFilterBy2) {
                        authorizationGrantsScope {
                            roles
                        }
                        assignments {
                            profile {
                                displayName
                                id
                            }
                        }
                    }
                    accountRelationships
                    profileType
                    businessInfo {
                        primaryPointOfContact {
                            email {
                                email
                            }
                        }
                    }
                }
            }
        }
        id
    }
}

# Create Trust Grant to consent to purposes
mutation CreateTrustGrant($input: CreateTrustGrantInput!) {
    createTrustGrant(input: $input) {
        id
        trustingEntityId
        trustedEntityId
        trustingEntityType
        trustedEntityType
        associationType
        authorizationType
        authorizationGrantsScope {
            roles
        }
    }
}
