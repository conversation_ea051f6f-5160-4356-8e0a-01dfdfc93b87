
mutation TaskManagementCreateTask($input: TaskManagement_CreateTaskInput!) {
  taskManagementCreateTask(input: $input) {
    task {
      id
      name
      description
      status
      dueDate
      priority
      type
      assignee
      references {
        referenceType
        referenceId
      }
    }
    success
    message
    code
  }
}

mutation TaskManagementUpdateTask($input: TaskManagement_UpdateTaskInput!) {
  taskManagementUpdateTask(input: $input) {
    task {
      id
      name
      description
      status
      dueDate
      priority
      assignee
      references {
        referenceType
        referenceId
      }
    }
    success
    message
    code
  }
}

mutation TaskManagementDeleteTask($input: TaskManagement_DeleteTaskInput!) {
  taskManagementDeleteTask(input: $input) {
    success
    message
    code
  }
}

query TaskManagementTask($taskManagementTaskId: Long!) {
  taskManagementTask(id: $taskManagementTaskId) {
    id
    name
    description
    status
    dueDate
    priority
    type
    assignee
    references {
      referenceType
      referenceId
    }
  }
}