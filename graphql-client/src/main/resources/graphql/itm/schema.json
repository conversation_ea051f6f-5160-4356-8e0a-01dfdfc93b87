{"data": {"__schema": {"queryType": {"name": "Query"}, "mutationType": {"name": "Mutation"}, "subscriptionType": null, "types": [{"kind": "SCALAR", "name": "DateTime", "description": "An ISO-8601 encoded UTC date string with millisecond precision (YYYY-MM-DDThh:mm:ss.mmmZ), e.g. '2021-08-26T23:18:50.870Z'", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "<PERSON>", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "JSON", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": null, "fields": [{"name": "taskManagementTask", "description": "Query a single task by id", "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementTasks", "description": "Query a list of tasks", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "after", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "filter", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_TaskFilter", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TaskManagement_OrderBy", "ofType": null}}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_TaskConnection", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementSchedule", "description": "Query a single schedule by id", "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementSchedules", "description": "Query a list of schedules", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "after", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "filter", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ScheduleFilter", "ofType": null}}, "defaultValue": null}, {"name": "orderBy", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TaskManagement_OrderBy", "ofType": null}}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_ScheduleConnection", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementPreference", "description": "Query a single preference by id", "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_Preference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementPreferences", "description": "Query a list of preferences", "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "after", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}, {"name": "filter", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_PreferenceFilter", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_PreferenceConnection", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "fields": [{"name": "taskManagementCreateTask", "description": "Create a task", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateTaskInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_CreateTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdateTask", "description": "Update a task", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdateTaskAssignee", "description": "Update a task's assignee", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskAssigneeInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdateTaskStatus", "description": "Update a task's status", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskStatusInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdateTaskPriority", "description": "Update a task's status", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskPriorityInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementDeleteTask", "description": "Delete a task", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DeleteTaskInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_DeleteTaskResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementProcessBatchTasks", "description": " Process tasks in batch ", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ProcessBatchTasksInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchTasksResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementCreateSchedule", "description": "Create a schedule", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateScheduleInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_CreateScheduleResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdateSchedule", "description": "Update a schedule", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateScheduleInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdateScheduleResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementDeleteSchedule", "description": "Delete a schedule", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DeleteScheduleInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_DeleteScheduleResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementUpdatePreference", "description": "Update a preference", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdatePreferenceInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_UpdatePreferenceResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "taskManagementProcessBatchPreferences", "description": "Update preferences in batch", "args": [{"name": "input", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ProcessBatchPreferencesInput", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchPreferencesResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateTaskInput", "description": null, "fields": null, "inputFields": [{"name": "idempotenceId", "description": "The idempotence id for the mutation. This uniquely identifies a task creation request.\nIf a task with the same idempotence id already exists, an error indicating the same will be returned.\nIf not, a new task will be created. This is useful in scenarios where the client is not sure if the task creation\nrequest was successful. Clients can generate a deterministic id based on unique attributes of the payload and use\nit as the idempotence id.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "name", "description": "The name of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "description", "description": "The description of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "status", "description": "The status of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "dueDate", "description": "The due date of the task", "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "defaultValue": null}, {"name": "type", "description": "The type of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "priority", "description": "The priority of the task", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "sequence", "description": " The sequence of the task ", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "parentId", "description": "The parent id of the task. If the task is a subtask, the parent id is the id of the parent task.", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": "The assignee of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "creator", "description": "The persona who created the task. In case of the task being created by an offline job, the authorization ticket contains the persona of the offline job.\nIn this case, you can specifically pass the persona of the user who actually created the task.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "client", "description": " The client for whom the task is created. This can be used by accountants to create tasks for their clients. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "customer", "description": " The customer for whom the task is created. This can be used by the small business to create tasks for their customers. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the task. This can be used to store additional information about the task that is not specifically mapped in the system. For e.g redirection URLs for dynamic CTAs ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "scheduleId", "description": " If this task is supposed to be created as part of a schedule, this will be the unique identifier for the schedule ", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "instanceId", "description": "If this task is supposed to be created as part of a schedule, this will be the id for the task instance in the schedule", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "references", "description": " References to the task. You can use this field to store references to external entities such as attachments, invoices, etc. ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceInput", "ofType": null}}, "defaultValue": null}, {"name": "tasks", "description": " SubTasks for the given task ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateTaskInput", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number of the entity. This should match the version number received during the fetch call. DO NOT MODIFY this value.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "name", "description": "The name of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "description", "description": "The description of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "status", "description": "The status of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "dueDate", "description": "The due date of the task", "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "defaultValue": null}, {"name": "priority", "description": "The priority of the task", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "sequence", "description": " The sequence of the task ", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "parentId", "description": "The parent id of the task. If the task is a subtask, the parent id is the id of the parent task.", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": "The assignee of the task", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "client", "description": " The client for whom the task is created. This can be used by accountants to create tasks for their clients. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "customer", "description": " The customer for whom the task is created. This can be used by the small business to create tasks for their customers. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the task. This can be used to store additional information about the task that is not specifically mapped in the system. For e.g redirection URLs for dynamic CTAs ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "scheduleId", "description": " If this task is supposed to be created/updated as part of a schedule, this will be the unique identifier for the schedule ", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "instanceId", "description": "If this task is supposed to be created as part of a schedule, this will be the id for the task instance in the schedule", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "references", "description": " References to the task. You can use this field to store references to external entities such as attachments, invoices, etc. ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceInput", "ofType": null}}, "defaultValue": null}, {"name": "tasks", "description": " Can be used to add additional subTasks for the given task ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateTaskInput", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskAssigneeInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number of the entity. This should match the version number received during the fetch call. DO NOT MODIFY this value.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": "The assignee of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskStatusInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number of the entity. This should match the version number received during the fetch call. DO NOT MODIFY this value.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "status", "description": "The status of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskPriorityInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number of the entity. This should match the version number received during the fetch call. DO NOT MODIFY this value.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "priority", "description": "The priority of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_DeleteTaskInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the task", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "scheduleId", "description": "The Schedule id of the task", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "instanceId", "description": "The id for the task instance in the schedule", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the reference", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "referenceType", "description": " The type of the reference ", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "ofType": null}}, "defaultValue": null}, {"name": "referenceId", "description": " The id of the reference ", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the reference. This can be used to store additional information about the reference that is not specifically mapped in the system. For e.g file name and file size for attachments. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ProcessBatchTasksInput", "description": null, "fields": null, "inputFields": [{"name": "create", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateTaskInput", "ofType": null}}}, "defaultValue": null}, {"name": "update", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateTaskInput", "ofType": null}}}, "defaultValue": null}, {"name": "delete", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DeleteTaskInput", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateReferenceInput", "description": null, "fields": null, "inputFields": [{"name": "taskId", "description": "The task id of the reference", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "referenceType", "description": "The type of the reference", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "ofType": null}}, "defaultValue": null}, {"name": "referenceId", "description": "The id of the reference", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the reference. This can be used to store additional information about the reference that is not specifically mapped in the system. For e.g file name and file size for attachments. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateReferenceInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the reference", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "referenceType", "description": "The type of the reference", "type": {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "ofType": null}, "defaultValue": null}, {"name": "referenceId", "description": "The id of the reference", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the reference. This can be used to store additional information about the reference that is not specifically mapped in the system. For e.g file name and file size for attachments. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_CreateScheduleInput", "description": null, "fields": null, "inputFields": [{"name": "idempotenceId", "description": "The idempotence id for the mutation. This uniquely identifies a schedule creation request.\nIf a schedule with the same idempotence id already exists, an error indicating the same will be returned.\nIf not, a schedule task will be created.\nThis is useful in scenarios where the client is not sure if the schedule creation request was successful.\nClients can generate a deterministic id based on unique attributes of the payload and use it as the idempotence id.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "frequency", "description": "The frequency of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleFrequencyType", "ofType": null}, "defaultValue": null}, {"name": "dayOfWeek", "description": "The dayOfWeek of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleDayOfWeekType", "ofType": null}, "defaultValue": null}, {"name": "weekOfMonth", "description": "The weekOfMonth of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleWeekOfMonthType", "ofType": null}, "defaultValue": null}, {"name": "dayOfMonth", "description": "The dayOfMonth of the schedule", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "monthOfYear", "description": "The monthOfYear of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleMonthOfYearType", "ofType": null}, "defaultValue": null}, {"name": "interval", "description": "The Interval of the schedule", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "startDate", "description": " The date on which the schedule should start ", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "defaultValue": null}, {"name": "endDate", "description": "  The date on which the schedule should end ", "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "defaultValue": null}, {"name": "name", "description": "The name of the task to be created", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "description", "description": "The description of the task to be created", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "type", "description": "The type of the task to be created by the schedule", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "status", "description": "The status of the task to be created", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "dueDateOffset", "description": "The due date offset of the task to be created", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "priority", "description": "The priority of the task to be created", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "sequence", "description": " The sequence of the task ", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "parentId", "description": "The parent id of the task to be created. If the task is a subtask, the parent id is the id of the parent task.", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": "The assignee of the task to be created", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "creator", "description": "The persona by whom the task should be created. In case of the task being created by an offline job, the authorization ticket contains the persona of the offline job.\nIn this case, you can specifically pass the persona of the user who actually would create the task.", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "client", "description": " The client for whom the task is to be created. This can be used by accountants to create tasks for their clients. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "customer", "description": " The customer for whom the task is created. This can be used by the small business to create tasks for their customers. ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the task to be created. This can be used to store additional information about the task that is not specifically mapped in the system. For e.g redirection URLs for dynamic CTAs ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "references", "description": " References to the task to be created. You can use this field to store references to external entities such as attachments, invoices, etc. ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceInput", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdateScheduleInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the schedule", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}, {"name": "version", "description": "The version number of the entity. This should match the version number received during the fetch call. DO NOT MODIFY this value.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "frequency", "description": "The frequency of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleFrequencyType", "ofType": null}, "defaultValue": null}, {"name": "dayOfWeek", "description": "The dayOfWeek of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleDayOfWeekType", "ofType": null}, "defaultValue": null}, {"name": "weekOfMonth", "description": "The weekOfMonth of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleWeekOfMonthType", "ofType": null}, "defaultValue": null}, {"name": "dayOfMonth", "description": "The dayOfMonth of the schedule", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "monthOfYear", "description": "The monthOfYear of the schedule", "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleMonthOfYearType", "ofType": null}, "defaultValue": null}, {"name": "interval", "description": "The Interval of the schedule", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "effectiveFrom", "description": "The effective from date of the schedule", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "defaultValue": null}, {"name": "endDate", "description": "The end of the schedule", "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "defaultValue": null}, {"name": "name", "description": "The name of the task to be created", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "description", "description": "The description of the task to be created", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "status", "description": "The status of the task to be created", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "dueDateOffset", "description": "The due date offset of the task to be created", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "priority", "description": "The priority of the task to be created", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "sequence", "description": " The sequence of the task ", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}, {"name": "parentId", "description": "The parent id of the task to be created. If the task is a subtask, the parent id is the id of the parent task.", "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": "The assignee of the task to be created", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "client", "description": " The client for whom the task is to be created ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "customer", "description": " The customer for whom the task is created ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "metadata", "description": " The metadata for the task to be created. This can be used to store additional information about the task that is not specifically mapped in the system. For e.g redirection URLs for dynamic CTAs ", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "references", "description": " References to the task to be created. You can use this field to store references to external entities such as attachments, invoices, etc. ", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceInput", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_DeleteScheduleInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the schedule", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdatePreferenceInput", "description": null, "fields": null, "inputFields": [{"name": "id", "description": "The id of the preference", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "value", "description": "The value of the preference", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ProcessBatchPreferencesInput", "description": null, "fields": null, "inputFields": [{"name": "update", "description": "The preferences to be updated", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_UpdatePreferenceInput", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_TaskFilter", "description": "Filtering for tasks", "fields": null, "inputFields": [{"name": "namespace", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceExpression", "ofType": null}, "defaultValue": null}, {"name": "status", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "assignee", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "creator", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "owner", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "client", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "type", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "reference", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceExpression", "ofType": null}, "defaultValue": null}, {"name": "dueDate", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateExpression", "ofType": null}, "defaultValue": null}, {"name": "includeRecurringTasks", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceFilter", "description": "Filtering for task references", "fields": null, "inputFields": [{"name": "taskId", "description": null, "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "defaultValue": null}, {"name": "referenceId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "referenceType", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceTypeExpression", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceFilter", "description": "Filtering for namespaces", "fields": null, "inputFields": [{"name": "app", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "domain", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "useCase", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "asset", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ScheduleFilter", "description": "Filtering for schedules", "fields": null, "inputFields": [{"name": "frequency", "description": null, "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleFrequencyType", "ofType": null}, "defaultValue": null}, {"name": "startDate", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateExpression", "ofType": null}, "defaultValue": null}, {"name": "endDate", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateExpression", "ofType": null}, "defaultValue": null}, {"name": "namespace", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceExpression", "ofType": null}, "defaultValue": null}, {"name": "reference", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceExpression", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_PreferenceFilter", "description": "Filtering for preferences", "fields": null, "inputFields": [{"name": "id", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}, {"name": "category", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceExpression", "description": null, "fields": null, "inputFields": [{"name": "equals", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceFilter", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_NamespaceFilter", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceExpression", "description": null, "fields": null, "inputFields": [{"name": "equals", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceFilter", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceFilter", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_ReferenceTypeExpression", "description": null, "fields": null, "inputFields": [{"name": "equals", "description": null, "type": {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_StringExpression", "description": null, "fields": null, "inputFields": [{"name": "equals", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "in", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateExpression", "description": null, "fields": null, "inputFields": [{"name": "between", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateRange", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TaskManagement_DateRange", "description": null, "fields": null, "inputFields": [{"name": "minDate", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "defaultValue": null}, {"name": "maxDate", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_OrderBy", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "DUE_DATE_ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DUE_DATE_DESC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PRIORITY_ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PRIORITY_DESC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NAME_ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NAME_DESC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "STATUS_ASC", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "STATUS_DESC", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_ReferenceType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ENGAGEMENT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DOCUMENT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ATTACHMENT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PROJECT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PROJECT_TEMPLATE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "TASK", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "INVOICE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "BILL", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ESTIMATE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CUSTOMER", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "TaskManagement_CreateTaskResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteTaskResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchTasksResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_CreateReferenceResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateReferenceResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteReferenceResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_CreateScheduleResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateScheduleResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteScheduleResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdatePreferenceResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchPreferencesResponse", "ofType": null}]}, {"kind": "OBJECT", "name": "TaskManagement_CreateTaskResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "task", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "task", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteTaskResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "task", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "UNION", "name": "TaskManagement_TaskResponse", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "TaskManagement_CreateTaskResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateTaskResponse", "ofType": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteTaskResponse", "ofType": null}]}, {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchTasksResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "result", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "UNION", "name": "TaskManagement_TaskResponse", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_CreateReferenceResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "reference", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateReferenceResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "reference", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteReferenceResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "reference", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_CreateScheduleResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "schedule", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdateScheduleResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "schedule", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_DeleteScheduleResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "schedule", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_UpdatePreferenceResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "preference", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Preference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_ProcessBatchPreferencesResponse", "description": null, "fields": [{"name": "code", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "success", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "result", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_UpdatePreferenceResponse", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "TaskManagement_MutationResponse", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_Task", "description": null, "fields": [{"name": "id", "description": " The unique identifier for the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "The version number of the entity.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": " The name of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": " The description of the task ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": " The status of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dueDate", "description": " The due date of the task ", "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "The type of the task", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "priority", "description": " The priority of the task ", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sequence", "description": " The sequence of the task ", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parentId", "description": " The parent of the task (if this task is a sub-task) ", "args": [], "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "assignee", "description": " The persona to whom the task is assigned ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "owner", "description": " The persona to whom the task belongs ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "creator", "description": " The persona who created the task ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "client", "description": " The client for whom the task is created ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customer", "description": " The customer for whom the task is created ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "metadata", "description": " The metadata attached to the task ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "schedule", "description": " The schedule to which the task belongs ", "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_TaskSchedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "references", "description": " References to the task ", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tasks", "description": " Any sub-tasks of the task ", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_Reference", "description": null, "fields": [{"name": "id", "description": " The unique identifier for the reference ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "keyId", "description": " The unique identifier of task or schedule to which the reference belongs ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "referenceType", "description": " The type of the reference ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "referenceId", "description": " The reference id ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metadata", "description": " The metadata attached to the reference ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_Schedule", "description": null, "fields": [{"name": "id", "description": " The unique identifier for the schedule ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "version", "description": "The version number of the entity.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "frequency", "description": " The frequency of the schedule ", "args": [], "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleFrequencyType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dayOfWeek", "description": "The dayOfWeek of the schedule", "args": [], "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleDayOfWeekType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "weekOfMonth", "description": "The weekOfMonth of the schedule", "args": [], "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleWeekOfMonthType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dayOfMonth", "description": "The dayOfMonth of the schedule", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "monthOfYear", "description": "The monthOfYear of the schedule", "args": [], "type": {"kind": "ENUM", "name": "TaskManagement_ScheduleMonthOfYearType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "interval", "description": "The Interval of the schedule", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "startDate", "description": " The start date of the schedule ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "endDate", "description": " The end date of the schedule ", "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": " The name of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": " The description of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "The type of the task to be created by the schedule", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "status", "description": " The status of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dueDateOffset", "description": " The due date offset of the task to be created ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "priority", "description": " The priority of the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "sequence", "description": " The sequence of the task ", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parentId", "description": " The parent of the task ", "args": [], "type": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "assignee", "description": " The persona to whom the task is assigned ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "owner", "description": " The persona to whom the task belongs ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "creator", "description": " The persona who created the task ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "client", "description": " The client for whom the task is created ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "customer", "description": " The customer for whom the task is created ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "references", "description": " References to the task ", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "metadata", "description": " The metadata attached to the schedule ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_TaskSchedule", "description": null, "fields": [{"name": "scheduleId", "description": " The unique identifier for the schedule ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "instanceId", "description": " The instance on which the task is supposed to be created in the schedule ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "<PERSON>", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_Preference", "description": null, "fields": [{"name": "id", "description": " The unique identifier for the preference ", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": " The name of the preference ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "category", "description": " The category of the preference ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": " The value of the preference ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": " The default value of the preference ", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_TaskConnection", "description": null, "fields": [{"name": "edges", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_TaskEdge", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nodes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_TaskEdge", "description": null, "fields": [{"name": "cursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "node", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Task", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_ReferenceConnection", "description": null, "fields": [{"name": "edges", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_ReferenceEdge", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nodes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_ReferenceEdge", "description": null, "fields": [{"name": "cursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "node", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Reference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_ScheduleConnection", "description": null, "fields": [{"name": "edges", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_ScheduleEdge", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nodes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_ScheduleEdge", "description": null, "fields": [{"name": "cursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "node", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Schedule", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_PreferenceConnection", "description": null, "fields": [{"name": "edges", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_PreferenceEdge", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "nodes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_Preference", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pageInfo", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "TaskManagement_PageInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_PreferenceEdge", "description": null, "fields": [{"name": "cursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "node", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "TaskManagement_Preference", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_Metadata", "description": null, "fields": [{"name": "created<PERSON>y", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastUpdatedBy", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastUpdatedAt", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_ScheduleFrequencyType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "DAILY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WEEKLY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MONTHLY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "YEARLY", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_ScheduleWeekOfMonthType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "FIRST", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "SECOND", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "THIRD", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FOURTH", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LAST", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_ScheduleDayOfWeekType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SUNDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MONDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "TUESDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "WEDNESDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "THURSDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FRIDAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "SATURDAY", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "TaskManagement_ScheduleMonthOfYearType", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "JANUARY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FEBRUARY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MARCH", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "APRIL", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "MAY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "JUNE", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "JULY", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "AUGUST", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "SEPTEMBER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "OCTOBER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "NOVEMBER", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DECEMBER", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "TaskManagement_PageInfo", "description": "The Relay compliant page information", "fields": [{"name": "hasPreviousPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasNextPage", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "startCursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "endCursor", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByUrl`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "tag", "description": null, "locations": ["FIELD_DEFINITION", "OBJECT", "INTERFACE", "UNION", "ARGUMENT_DEFINITION", "SCALAR", "ENUM", "ENUM_VALUE", "INPUT_OBJECT", "INPUT_FIELD_DEFINITION"], "args": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}, {"name": "link", "description": null, "locations": ["SCHEMA"], "args": [{"name": "url", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "import", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}, {"name": "annotate", "description": null, "locations": ["OBJECT", "FIELD_DEFINITION", "INPUT_OBJECT", "INPUT_FIELD_DEFINITION"], "args": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "type", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "inputs", "description": null, "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "defaultValue": null}, {"name": "target", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}]}, {"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "locations": ["FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\""}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behaviour of this scalar.", "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behaviour of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}]}}}