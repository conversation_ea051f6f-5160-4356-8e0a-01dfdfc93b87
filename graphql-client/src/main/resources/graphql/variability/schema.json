{"__schema": {"queryType": {"name": "VariabilityEngine_Query"}, "mutationType": null, "subscriptionType": null, "types": [{"kind": "SCALAR", "name": "JSON", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "DateTime", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_Query", "description": null, "fields": [{"name": "variabilityEngine_getDecisionBatch", "description": "query for getting a list of decisions", "args": [{"name": "decisions", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_Context", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_BooleanDecision", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_UserContext", "description": "Additional Custom Context from User for evaluating a decision to be passed by the caller. This will be a map of key, value.\nExample use-cases for these might be passing any dimension which will not be orchestrated by Variability Engine.\nBecause the way VE orchestrates and fetches the dimension response through CallGraph a modification of DAG (Currently using Kahn's Algorithm),\nthis won't be able to be passed when calling the batch query since the batch of decisions are evaluated through a single call-graph for optimization.\nExample:\n1. Turbotax might require a variation on the User's purchase year. Since this might not be orchestrated by VE,\nTurbotax would pass it as a User Context Object", "fields": null, "inputFields": [{"name": "key", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_Context", "description": "Static Context passed by the sdk. This would be additional metadata which the sdk is aware of about the composite app to be passed", "fields": null, "inputFields": [{"name": "key", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_NamespaceInput", "description": "TO BE DEPRECATED - input type for declaring a new namespace", "fields": null, "inputFields": [{"name": "namespace", "description": "name of the namespace", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_CreateAttributeInput", "description": "input type for creating a new attribute, not all attribute fields are editable after creation", "fields": null, "inputFields": [{"name": "name", "description": "name of the attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "rule", "description": "the rule used for resolving the dimensions into attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "description for attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "type of the rule to be used currently only expression is supported", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ruleType", "description": "type of the rule to be used currently only expression is supported", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "valueDataType", "description": "data type of attribute value", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeValueDataType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of dimensions required to resolve this attribute. This needs to be a non-recursive list", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_AttributeDimensionInput", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which attribute belongs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "default value for attribute", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "shouldErrorOnFailedResolution", "description": "whether this attribute should fail on error", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "isFeature", "description": "whether this decision is a feature", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_UpdateAttributeInput", "description": "input type for update an existing attribute, not all attribute fields are editable after creation", "fields": null, "inputFields": [{"name": "name", "description": "name of the attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "rule", "description": "the rule used for resolving the dimensions into attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "description for attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "type of the rule to be used currently only expression is supported", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ruleType", "description": "type of the rule to be used currently only expression is supported", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "valueDataType", "description": "data type of attribute value", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeValueDataType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of dimensions required to resolve this attribute. This needs to be a non-recursive list", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_AttributeDimensionInput", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which attribute belongs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "default value for attribute", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "shouldErrorOnFailedResolution", "description": "whether this attribute should fail on error", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "isFeature", "description": "whether this decision is a feature", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_AttributeInput", "description": "TO BE DEPRECATED - input type for declaring a new attribute", "fields": null, "inputFields": [{"name": "name", "description": "name of the attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "rule", "description": "the rule used for resolving the dimensions into attribute", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ruleType", "description": "type of the rule to be used currently only expression is supported", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of dimensions required to resolve this attribute. This needs to be a non-recursive list", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "UNION", "name": "VariabilityEngine_Metadata", "description": "union of VariabilityEngine Metadata with different data types supported", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "VariabilityEngine_StringMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_IntMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_FloatMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_DateTimeMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ObjectMetadata", "ofType": null}]}, {"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "description": null, "fields": [{"name": "id", "description": "id of attribute", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "attribute name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which attribute belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in attribute resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "returns a list of variable dimensions which are used to arrive at this decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "VariabilityEngine_StringMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_BooleanDecision", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_IntMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_FloatMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_DateTimeMetadata", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ObjectMetadata", "ofType": null}]}, {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "description": "currently this decision variables returns a String representation of corresponding dimension values. the below type is a simple representation of a map\nequivalent to defining Map<String, String> in java or a dictionary.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_StringMetadata", "description": "VariabilityEngine Metadata with value having string data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of decision evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_BooleanDecision", "description": "decision with value having boolean data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of attribute evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_IntMetadata", "description": "VariabilityEngine Metadata with value having integer data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of decision evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_FloatMetadata", "description": "VariabilityEngine Metadata with value having float data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of decision evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_DateTimeMetadata", "description": "VariabilityEngine Metadata with value having date time data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of decision evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ObjectMetadata", "description": "Variability Metadata with value having object data type", "fields": [{"name": "id", "description": "id of decision", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "decision name", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value of decision evaluated based on decision rule", "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "error", "description": "error in decision resolution, if any", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "decisionDimensions", "description": "dimension values involved in arriving at the decision", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_DimensionResolution", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_BaseDecision", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_AttributeDefinition", "description": "definition for attribute", "fields": [{"name": "id", "description": "generated id", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "name of the attribute", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "description", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which attribute belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "type of attribute", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "rule", "description": "the rule used for resolving the dimensions into attribute", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ruleType", "description": "type of the rule to be used currently only expression is supported", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parentDimensions", "description": "list of dimensions required to resolve this attribute. This needs to be a non-recursive list", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_AttributeDimension", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "default value for attribute", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "valueDataType", "description": "data type of value", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeValueDataType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "shouldErrorOnFailedResolution", "description": "should error upon failure to resolve", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "activeVersion", "description": "active attribute version for future use case when versioning is supported, defaults to 0 until then", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "editSequence", "description": "edit sequence", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "audit", "description": "AuditInfo about attribute definition", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_AuditInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_Decision", "description": "definition for Decision", "fields": [{"name": "id", "description": "generated id", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": "name of the attribute", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "description", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "feature", "description": "feature to which attribute belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "capability", "description": "L1, L2, or L3 capability to which decision belongs", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": "type of attribute", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "rule", "description": "the rule used for resolving the dimensions into attribute", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ruleType", "description": "type of the rule to be used currently only expression is supported", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parentDimensions", "description": "list of dimensions required to resolve this attribute. This needs to be a non-recursive list", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_AttributeDimension", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "default value for attribute", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "valueDataType", "description": "data type of value", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_AttributeValueDataType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "shouldErrorOnFailedResolution", "description": "should error upon failure to resolve", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "activeVersion", "description": "active attribute version for future use case when versioning is supported, defaults to 0 until then", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "editSequence", "description": "edit sequence", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "audit", "description": "AuditInfo about attribute definition", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_AuditInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_AttributeDimensionInput", "description": null, "fields": null, "inputFields": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_AttributeDimension", "description": "refactor string dimension to this type", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_AttributeError", "description": "DEPRECATED -- errors while resolving attributes", "fields": [{"name": "code", "description": "error code", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": "error message", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_Error", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "description": "errors while resolving decisions", "fields": [{"name": "code", "description": "error code", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": "error message", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_Error", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "INTERFACE", "name": "VariabilityEngine_Error", "description": "error interface for variability orchestrator", "fields": [{"name": "code", "description": "error code", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "message", "description": "error message", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "VariabilityEngine_AttributeError", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_DecisionError", "ofType": null}]}, {"kind": "OBJECT", "name": "VariabilityEngine_DimensionDefinition", "description": "definition for dimension", "fields": [{"name": "name", "description": "dimension name", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dataSourceType", "description": "type of dimension dataSource", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_DataSourceType", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "dataSource", "description": "definition of dataSource", "args": [], "type": {"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceDefinition", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "audit", "description": "AuditInfo about dimension definition", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_AuditInfo", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceDefinition", "description": "interface for defining datasource", "fields": [{"name": "name", "description": "name of dataSource", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for the dataSource", "args": [], "type": {"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceConfigDefinition", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "VariabilityEngine_APIDataSourceDefinition", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ContextDataSourceDefinition", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_SDKDataSourceDefinition", "ofType": null}]}, {"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceConfigDefinition", "description": "interface for defining data-source config", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": "allows Dimensions to provide specific input to the data-source without having to change the data-source config", "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "VariabilityEngine_APIDataSourceConfigDefinition", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_SDKDataSourceConfigDefinition", "ofType": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ContextDataSourceConfigDefinition", "ofType": null}]}, {"kind": "OBJECT", "name": "VariabilityEngine_APIDataSourceDefinition", "description": "definition for API DataSource. This inherits the properties of common DataSourceDefinition", "fields": [{"name": "name", "description": "name of dataSource", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for the api dataSource", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_APIDataSourceConfigDefinition", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of parent dimensions used for resolving this dimension", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ttl", "description": "time to live for the datasource (in seconds)", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ContextDataSourceDefinition", "description": "definition for Context DataSource. This inherits the properties of DataSourceDefinition", "fields": [{"name": "name", "description": "name of dataSource", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for context DataSource", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_ContextDataSourceConfigDefinition", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_SDKDataSourceDefinition", "description": "definition for SDK DataSource. This inherits the properties of DataSourceDefinition", "fields": [{"name": "name", "description": "name of dataSource", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for context DataSource", "args": [], "type": {"kind": "OBJECT", "name": "VariabilityEngine_SDKDataSourceConfigDefinition", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ttl", "description": "time to live for the datasource (in seconds)", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_APIDataSourceConfigDefinition", "description": "definition for API DataSource Config", "fields": [{"name": "name", "description": "name of api data source config", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "url", "description": "endpoint for fetching the dimension, must include full path. PathParams can include placeholders in the form of {dimensionName}. Ex: https://accounts.platform.intuit.com/v1/realms/{realmId}", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "method", "description": "rest Method to use for the request. GET/POST", "args": [], "type": {"kind": "ENUM", "name": "VariabilityEngine_HttpMethod", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "headers", "description": "any custom headers apart from authHeader and tid that should be sent for resolving the dimension", "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "VariabilityEngine_HeaderDefinition", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "outputPath", "description": "json path expression for selecting the output", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": "any request body which needs to be passed for resolving dimension. The parent dimension data required can be defined as a placeholder similar to {dimensionName}", "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceConfigDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_SDKDataSourceConfigDefinition", "description": "definition for SDK DataSource Config", "fields": [{"name": "name", "description": "name of sdk data source config", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "api", "description": "api (which method to call) for the sdk data source", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "outputPath", "description": "json path expression for selecting the output", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceConfigDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_ContextDataSourceConfigDefinition", "description": "definition for Context DataSource Config", "fields": [{"name": "name", "description": "name of context data source config", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "contextProperty", "description": "name of the header or attribute in case of Authorization header (intuit_realmid/intuit_userid)", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [{"kind": "INTERFACE", "name": "VariabilityEngine_DataSourceConfigDefinition", "ofType": null}], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_HeaderDefinition", "description": "type for defining a standard RFC 6648 HTTP Header", "fields": [{"name": "headerName", "description": "name of the <PERSON>er", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value for the Header", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_DimensionInput", "description": "input for creating or updating a dimension", "fields": null, "inputFields": [{"name": "name", "description": "dimension name", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dataSourceType", "description": "type of dimension dataSource", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_DataSourceType", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "default value for the dimension", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "contextDataSource", "description": "config to populate if dataSource is of type context", "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_ContextDataSourceInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "apiDataSource", "description": "config to populate if dataSource is of type api", "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_APIDataSourceInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "sdkDataSource", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_SDKDataSourceInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": "description for the dimension being created/updated", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_ContextDataSourceInput", "description": "input for creating a context dataSource", "fields": null, "inputFields": [{"name": "name", "description": "name of context data source", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for context data source", "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_ContextConfigInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "label", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_ContextConfigInput", "description": "input for context datasource config", "fields": null, "inputFields": [{"name": "path", "description": "name of the header to fetch the value from or mention the auth attribute for auth dimension", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_APIDataSourceInput", "description": "input for defining api data source", "fields": null, "inputFields": [{"name": "name", "description": "name of the api datasource", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for api datasource", "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_RestConfigInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of dimension required for resolving this. These should be defined as placeholder in while sending a downstream request", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "label", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ttl", "description": "time to live in VE cache (in seconds)", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_SDKDataSourceInput", "description": null, "fields": null, "inputFields": [{"name": "name", "description": "name of the sdk datasource", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "config", "description": "config for sdk datasource", "type": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_SDKConfigInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "dimensions", "description": "list of dimension required for resolving this. These should be defined as placeholder in while sending a downstream request", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "context", "description": null, "type": {"kind": "SCALAR", "name": "JSON", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "label", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "ttl", "description": "time to live in VE cache (in seconds)", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_SDKConfigInput", "description": null, "fields": null, "inputFields": [{"name": "api", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "outputPath", "description": "json path expression for defining output", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_RestConfigInput", "description": "config for rest data source", "fields": null, "inputFields": [{"name": "url", "description": "url for invoking the downstream. define dimensions like realmId with a placeholder", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "method", "description": "http method used for invoking the downstream", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "VariabilityEngine_HttpMethod", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "customHeaders", "description": "any custom headers that needs to be passed for downstream", "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_HeaderInput", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": "request body in case of POST call in JSON format", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "outputPath", "description": "json path expression for defining output", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "VariabilityEngine_HeaderInput", "description": "input for defining header for rest calls", "fields": null, "inputFields": [{"name": "name", "description": "name of the header", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": "value for the header", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_AuditInfo", "description": null, "fields": [{"name": "createdAt", "description": "creation timestamp", "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdByUser", "description": "the user that created the entity", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "createdByApp", "description": "the application that created the entity", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedAt", "description": "last updated timestamp", "args": [], "type": {"kind": "SCALAR", "name": "DateTime", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedByUser", "description": "the user that last updated the entity", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "updatedByApp", "description": "the application that last updated the entity", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "VariabilityEngine_Feature", "description": null, "fields": [{"name": "featureNumber", "description": "id of feature", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "featureCode", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "entitlementStatus", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "VariabilityEngine_AttributeType", "description": "enum for defining the type of an attribute", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "STATIC", "description": "attributes that resolve to a pre-configured value resolved using dimensions evaluated at run time. For eg: behavioral data", "isDeprecated": false, "deprecationReason": null}, {"name": "DYNAMIC", "description": "attributes that resolve to an evaluated value based on an expression composed of dimensions evaluated at run time. For eg: visibility/enablement data", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "VariabilityEngine_AttributeRuleType", "description": "enum for defining the rule types accepted for resolving an attribute", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "EXPRESSION", "description": "spring Expression format rule for defining the attribute", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIA", "description": "rule for varia based attribute", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "VariabilityEngine_DataSourceType", "description": "type of data source", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "API", "description": "dataSource which obtains data from API", "isDeprecated": false, "deprecationReason": null}, {"name": "CONTEXT", "description": "dataSource which obtains data from context", "isDeprecated": false, "deprecationReason": null}, {"name": "SDK", "description": "dataSource which obtains data from SDK", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "VariabilityEngine_HttpMethod", "description": "http method for data source config", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "GET", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "POST", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "VariabilityEngine_AttributeValueDataType", "description": "data type for attribute value", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "STRING", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "BOOLEAN", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "INT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "FLOAT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "DATETIME", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByUrl`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "isRepeatable": false, "locations": ["FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\"", "isDeprecated": false, "deprecationReason": null}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behaviour of this scalar.", "isRepeatable": false, "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behaviour of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}]}}