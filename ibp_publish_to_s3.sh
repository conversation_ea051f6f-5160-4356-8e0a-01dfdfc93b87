#!/bin/bash1

envName="$1"

echo "S3 Bucket Access Role: ${S3_IAM_ROLE_ARN}"

# Assuming the S3 Upload IAM Role
temp_role=$(aws sts assume-role --role-arn ${S3_IAM_ROLE_ARN} --role-session-name AWSCLI-Session)
echo temp_role: ${temp_role}

# Prevent the AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_SESSION_TOKEN from printing in the logs. These are secrets
set +x

export AWS_ACCESS_KEY_ID=$(echo ${temp_role} | jq .Credentials.AccessKeyId | xargs)
export AWS_SECRET_ACCESS_KEY=$(echo ${temp_role} | jq .Credentials.SecretAccessKey | xargs)
export AWS_SESSION_TOKEN=$(echo ${temp_role} | jq .Credentials.SessionToken | xargs)

# Re-enabled echoing commands
set -x

# Get the Jenkins name which is used as part of the s3 upload path
JENKINS_NAME=$(basename ${JENKINS_URL})

echo "aws s3 sync --acl bucket-owner-full-control ${WORKSPACE}/test/reports/${envName} s3://${S3_BUCKET_BASE_PATH}/${JENKINS_NAME}/${JOB_NAME}/${BUILD_NUMBER}/${envName}"
aws s3 sync --acl bucket-owner-full-control ${WORKSPACE}/test/reports/${envName} s3://${S3_BUCKET_BASE_PATH}/${JENKINS_NAME}/${JOB_NAME}/${BUILD_NUMBER}/${envName}

# Generate html link pointing to the s3 url for that report
echo "<a href=\"${S3_URL_BASE}/${JENKINS_NAME}/${JOB_NAME}/${BUILD_NUMBER}/${envName}/cucumber-html-reports/overview-features.html\">${envName}</a><br><br>" >> html_reports_in_s3.html
