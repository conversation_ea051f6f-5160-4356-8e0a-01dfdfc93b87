version: 1.0
recipes:
  - name: WorkflowAutomationTemplateSchedulee2e
    entity: urn:intuit:foundation:workflow:workflowautomation:Template
    schemaVersion: 1.2.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.template.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.template.v1
  - name: WorkflowAutomationDefinitionSchedulee2e
    entity: urn:intuit:foundation:workflow:workflowautomation:Definition
    schemaVersion: 1.2.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.definition.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.definition.v1
  - name: WorkflowAutomationActivityRuntimeSchedulee2e
    entity: urn:intuit:foundation:workflow:workflowautomation:ActivityRuntime
    schemaVersion: 1.2.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.activityruntime.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.activityruntime.v1
  - name: WorkflowAutomationProcessSchedulee2e
    entity: urn:intuit:foundation:workflow:workflowautomation:Process
    schemaVersion: 1.2.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.process.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: e2e.foundation.workflow.workflowautomation.process.v1