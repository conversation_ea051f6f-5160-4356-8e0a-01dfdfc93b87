version: 1.0
recipes:
  - name: WorkflowAutomationTemplateSchedule
    entity: urn:intuit:foundation:workflow:workflowautomation:Template
    schemaVersion: 1.6.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.template.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.template.v1
  - name: WorkflowAutomationDefinitionSchedule
    entity: urn:intuit:foundation:workflow:workflowautomation:Definition
    schemaVersion: 1.6.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.definition.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.definition.v1
  - name: WorkflowAutomationActivityRuntimeSchedule
    entity: urn:intuit:foundation:workflow:workflowautomation:ActivityRuntime
    schemaVersion: 1.6.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.activityruntime.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.activityruntime.v1
  - name: WorkflowAutomationProcessSchedule
    entity: urn:intuit:foundation:workflow:workflowautomation:Process
    schemaVersion: 1.6.0
    modules:
      - type: domain-event
        assetId: 7065039507767760447
        location:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.process.v1
      - type: lake-ingestion
        schedule: daily
        input:
          - type: kafka
            topicName: prd.foundation.workflow.workflowautomation.process.v1