package com.intuit.appintgwkflw.wkflautomate.was.entity.http;

import com.intuit.appintgwkflw.wkflautomate.telemetry.http.MonitoredHttpRequest;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

/**
 * The HttpRequest object of WAS.
 *
 * @param <T> the type parameter
 * @param <U> the type parameter
 * <AUTHOR>
 */
@Getter
@Builder(toBuilder = true)
public final class WASHttpRequest<T, U> implements MonitoredHttpRequest {

  private final String url;

  private final T request;

  private final ParameterizedTypeReference<U> responseType;

  private final HttpHeaders requestHeaders;

  private final HttpMethod httpMethod;

  @Setter @Builder.Default private RetryHandlerName retryHandler = RetryHandlerName.STATUS_CODE;

  @Setter(AccessLevel.PRIVATE)
  private HttpEntity<T> requestEntity;

  /**
   * Gets request entity.
   *
   * @return the request entity
   */
  public HttpEntity<T> getRequestEntity() {

    return new HttpEntity<>(this.request, this.requestHeaders);
  }

  /**
   * Gets the retry handler.
   *
   * @return the retry handler
   */
  public RetryHandlerName getRetryHandler() {

    return retryHandler;
  }

  @Override
  public String getMethod() {

    return this.httpMethod.name();
  }
}
