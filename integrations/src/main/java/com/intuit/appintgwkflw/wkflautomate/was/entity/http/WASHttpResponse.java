package com.intuit.appintgwkflw.wkflautomate.was.entity.http;

import com.intuit.appintgwkflw.wkflautomate.telemetry.http.MonitoredHttpResponse;
import lombok.Builder;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * The HttpResponse object of WAS.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
@Getter
@Builder
public final class WASHttpResponse<T> implements MonitoredHttpResponse {

  private final T response;

  private final String error;

  private final String downstreamError;

  private final HttpStatus status;

  private final boolean isSuccess2xx;

  @Override
  public int statusCode() {

    return this.status.value();
  }

  @Override
  public boolean isSuccess2xx() {

    return this.isSuccess2xx;
  }
}
