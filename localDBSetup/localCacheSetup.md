### Setting up Redis cache locally

1. To set up and use Redis server locally, change `cache.configuration.enabled` to `true` in the `application-default.yaml`.
2. Install Redis: `brew install redis`
3. You can set-up the cache either in `cluster` (several servers) or `standalone` (single server) mode as has been explained in the 2 sections below.
4. It is important that you set the `cache.source.clusterMode` flag accordingly


### Redis-cluster mode setup

Follow the steps mentioned [here](https://redis.io/docs/management/scaling/#create-a-redis-cluster) which has been briefly described below:

1. Change `cache.source.clusterMode` in `application-default.yaml` to `true`.
2. Say you want 3 servers in your cluster. Create 3 folders, say `7000`, `7001`, `7003`. 
3. Add a `redis.conf` file into each of them with the below configurations:
```yaml
port 7000 # Change this to 7001 or 7002 in the right folder
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
appendonly yes
```
4. Start the server in each of these folders by running `redis-server ./redis.conf`.
5. Run the below command to start the cluster: 
```
redis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 \
127.0.0.1:7002 \
--cluster-replicas 0
```
6. Enter one of the host urls, say `redis://127.0.0.1:7000` under `cache.source.hostUrls`.
7. We can even use standalone mode with one of these servers.


### Redis-standalone mode setup

Follow the steps mentioned [here](https://redis.io/docs/getting-started/installation/install-redis-on-mac-os/)  which has been briefly described below:

1. Change `cache.source.clusterMode` in `application-default.yaml` to `false`.
2. Just run `redis-server` once you have installed Redis. This generally spins a Redis standalone server on ``. Enter this URL into `cache.source.hostUrls`.

