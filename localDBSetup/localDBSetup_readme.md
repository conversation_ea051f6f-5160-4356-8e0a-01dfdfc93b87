##### 1. Setup DB locally in docker container
  1. CD to the directory of docker-compose.yml.
  2. Run ./setup-podman.sh
  3. Run ./setup-db.sh
  4. This should set up camunda db instance with required schema and user role

##### 3. Test DB connection with any SQL client 
  1. database name: camunda
  2. host: localhost
  3. JDBC url: ****************************************
  4. username: sas
  5. password: Intuit01

