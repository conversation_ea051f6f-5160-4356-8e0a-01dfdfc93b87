package com.intuit.appintgwkflw.wkflautomate.was.localisation.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * This is configuration class for mapping all the externalService Url say taskManager, bankDeposit.
 */
@Configuration
@ConfigurationProperties(prefix = "external-service-mapping")
public class ExternalServiceMapping {

  /**
   * This contains the map with externalService as key and url as value.
   *
   * <pre>
   * externalServiceUrlMapping:
   *   urls:
   *     taskManagerUrl: https://app.qal.qbo.intuit.com/app/taskmanager
   * </pre>
   */
  private Map<String, String> urls = new HashMap<>();

  /**
   * This method returns the URL on the basis of type defined as key in externalServiceURLMapping
   *
   * @param urlType
   * @return
   */
  public String getUrlFromUrlType(String urlType) {
    return urls.get(urlType);
  }

  public void setUrls(final Map<String, String> urls) {
    this.urls = urls;
  }
}
