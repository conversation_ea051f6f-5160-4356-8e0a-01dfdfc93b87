package com.intuit.appintgwkflw.wkflautomate.was.localisation.enums;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FILE_PREFIX;

import org.springframework.util.StringUtils;

/**
 * This enum used as type of resourceBundle. The main purpose of using this enum to check which type
 * of resource bundle we want to load. The type can be found by the prefix of resource bundle path.
 */
public enum ResourceBundleLoaderType {
  /** This type tells that we are going to load bundle from external file path */
  FILE_BUNDLE_TYPE(FILE_PREFIX),
  /** This type tells that we are going to load bundle from classpath using classloader */
  CLASSLOADER_BUNDLE_TYPE(null);
  private final String bundleTypePrefix;

  ResourceBundleLoaderType(final String bundleTypePrefix) {
    this.bundleTypePrefix = bundleTypePrefix;
  }

  public String getBundleTypePrefix() {
    return this.bundleTypePrefix;
  }

  /**
   * This method returns the type on the basis of prefix of bundle path.
   *
   * @param bundlePath prefix must start with one of the values of ResourceBundleLoaderType
   * @return ResourceBundleLocatorType if prefix null it returns the default <code>
   *     CLASSLOADER_BUNDLE_TYPE</code>
   */
  public static ResourceBundleLoaderType getResourceBundleLocatorTypeFromBundlePath(
      String bundlePath) {
    if (StringUtils.isEmpty(bundlePath)) {
      return CLASSLOADER_BUNDLE_TYPE;
    }
    for (ResourceBundleLoaderType resourceBundleLoaderType : ResourceBundleLoaderType.values()) {
      if (!StringUtils.isEmpty(resourceBundleLoaderType.getBundleTypePrefix())
          && bundlePath.startsWith(resourceBundleLoaderType.getBundleTypePrefix())) {
        return resourceBundleLoaderType;
      }
    }
    return CLASSLOADER_BUNDLE_TYPE;
  }
}
