package com.intuit.appintgwkflw.wkflautomate.was.localisation.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/** Contains all the template file mapping. We can have different filename w.r.t to template. */
public enum TemplateToFileMapping {
  /**
   * This denotes default template name and file name. If user does not specify the template name
   * then this default value will be used.
   */
  DEFAULT("CustomWorkflowTemplate", "workflowContent");
  private final String templateName;
  private final String fileName;

  private static final Map<String, String> templateToFileMappingMap = templateFileNameMap();

  private static Map<String, String> templateFileNameMap() {
    return Arrays.stream(TemplateToFileMapping.values())
        .collect(
            Collectors.toMap(
                TemplateToFileMapping::getTemplateName, TemplateToFileMapping::getFileName));
  }

  /**
   * This method returns the filename w.r.t templateName
   *
   * @param templateName templateName for which we are looking for fileName
   * @return
   */
  public static String getFileNameFromTemplateName(String templateName) {
    return templateToFileMappingMap.getOrDefault(templateName, null);
  }

  public String getTemplateName() {
    return this.templateName;
  }

  public String getFileName() {
    return this.fileName;
  }

  TemplateToFileMapping(final String templateName, final String fileName) {
    this.templateName = templateName;
    this.fileName = fileName;
  }
}
