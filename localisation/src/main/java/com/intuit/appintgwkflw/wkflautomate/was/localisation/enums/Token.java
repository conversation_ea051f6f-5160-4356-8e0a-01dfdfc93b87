package com.intuit.appintgwkflw.wkflautomate.was.localisation.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/** This enum used as constant for defining the token in the localised string */
public enum Token {
  /**
   * RECORD_TYPE used to find out the @RecordTypeTokenHandler.and parameter <code>
   *  recordType</code> denotes tokenType in localised string.
   */
  RECORD_TYPE("recordType"),
  /**
   * HELP_VARIABLE_RECORD_TYPE used to find out @HelpVariableRecordTypeTokenHandler and parameter
   * <code> helpVariableRecordType</code> denotes tokenType in localised string.
   */
  HELP_VARIABLE_RECORD_TYPE("helpVariableRecordType"),
  /**
   * URL_TYPE used to find out @UrlTokenHandler and parameter <code> url</code> denotes tokenType in
   * localised string.
   */
  URL_TYPE("url");

  /** The value of token enum */
  private final String tokenType;

  /** It just like caching the token w.r.t tokenType as key. */
  private static final Map<String, Token> tokenMap = getTokenMap();

  /**
   * @return Map of tokenType as key and Token as value
   */
  private static Map<String, Token> getTokenMap() {
    return Arrays.stream(Token.values())
        .collect(Collectors.toMap(Token::getTokenType, Function.identity()));
  }

  /**
   * Return the Token to which key <code>tokenType</code> specified in tokenMap.
   *
   * @param tokenType key
   * @return Token
   */
  public static Token getTokenFromTokenType(String tokenType) {
    return tokenMap.get(tokenType);
  }

  public String getTokenType() {
    return this.tokenType;
  }

  Token(final String tokenType) {
    this.tokenType = tokenType;
  }
}
