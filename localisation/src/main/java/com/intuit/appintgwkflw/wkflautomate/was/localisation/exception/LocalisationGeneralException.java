package com.intuit.appintgwkflw.wkflautomate.was.localisation.exception;

import org.apache.commons.lang3.ArrayUtils;

/**
 * <AUTHOR>
 */
public class LocalisationGeneralException extends RuntimeException {

  public LocalisationError getLocalisationError() {
    return this.localisationError;
  }

  private static final long serialVersionUID = 1L;
  private LocalisationError localisationError;

  public LocalisationGeneralException(
      LocalisationError localisationError, Object... workflowErrorMessageArgs) {

    super(formatErrorMessage(localisationError, workflowErrorMessageArgs));
    this.localisationError = localisationError;
  }

  /**
   * replaces the localisation error message with the message arguments.Input @LocalisationError
   * should not be null.
   *
   * @param localisationError workflow error
   * @param localisationErrorMessageArgs workflow error message arguments
   * @return WorkflowError with formated error message if required.
   * @throws NullPointerException if workflowError is null.
   */
  private static String formatErrorMessage(
      LocalisationError localisationError, Object... localisationErrorMessageArgs) {

    if (ArrayUtils.isEmpty(localisationErrorMessageArgs)) {
      return localisationError.getErrorMessage();
    }
    return String.format(localisationError.getErrorMessage(), localisationErrorMessageArgs);
  }
}
