package com.intuit.appintgwkflw.wkflautomate.was.localisation.factory;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FILE_PREFIX;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/** This class loads the resource the bundle from the external file path. */
@Component
public class FileResourceBundleLoader implements ResourceBundleLoader {

  /**
   * This method returns the classloader bundle type <code>
   * ResourceBundleLocatorType.FILE_BUNDLE_TYPE</code>
   *
   * @return It returns <code>ResourceBundleLocatorType.FILE_BUNDLE_TYPE</code>
   */
  @Override
  public ResourceBundleLoaderType getType() {
    return ResourceBundleLoaderType.FILE_BUNDLE_TYPE;
  }

  /**
   * In <code>FileResourceBundleLocator</code> file can be modified at runtime so we might need to
   * reload the file. Depending upon the properties we have defined. This methods call only when
   * bundle expired in the cache. Caller method first check bundle has expired or not. If it has
   * expired then it would check whether file has been changed or not. <br>
   * The unit of load time is <unit>milliSec</unit>.If file is not exists then we return true,
   * reason for that file might get deleted.
   *
   * @param basePath the base path of the resource bundle for which reloads require
   * @param locale locale of the resource bundle for which reloads require
   * @param loadTime the time when <code>bundle</code> was loaded and put in the cache.
   * @return true or false
   */
  @Override
  public boolean needsReload(String basePath, Locale locale, long loadTime) {
    String resourceName = getResourceName(basePath, locale);
    if (StringUtils.isEmpty(resourceName)) {
      return false;
    }
    resourceName = resourceName.replace(FILE_PREFIX, "");
    File file = new File(resourceName);

    return file.isFile() ? file.lastModified() >= loadTime : true;
  }

  /**
   * This method returns the resource bundle. Firstly it call <code>getResourceName</code> parent
   * method for creating exact bundle path.(Appends locale as folder name before file and as suffix
   * in file name).
   *
   * @param basePath the file path of the resource bundle
   * @param locale locale of the resource bundle
   * @return the resource bundle instance, or <code>null</code> if none could be found.
   * @throws IOException Exception
   */
  @Override
  public ResourceBundle getResourceBundle(String basePath, Locale locale) throws IOException {
    String resourceName = getResourceName(basePath, locale);
    if (StringUtils.isEmpty(resourceName)) {
      return null;
    }
    resourceName = resourceName.replace(FILE_PREFIX, "");
    File file = new File(resourceName);
    if (file.isFile()) { // Also checks for existence
      try (FileInputStream fis = new FileInputStream(file);
          InputStreamReader reader = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
        return new PropertyResourceBundle(reader);
      }
    }
    return null;
  }

  /**
   * This returns how long the bundle will be cached. Positive time-to-live values specify the
   * number of milliseconds a bundle can remain in the cache without being validated against the
   * source data from which it was constructed. The value 0 indicates that a bundle must be
   * validated each time it is retrieved from the cache. -1 specifies that loaded resource bundles
   * are not put in the cache. -2 specifies that loaded resource bundles are put in the cache with
   * no expiration control.
   *
   * @return <code>RESOURCE_BUNDLE.TIME_TO_LIVE</code> The time in milliseconds
   */
  @Override
  public long getTimeToLive() {
    return RESOURCE_BUNDLE.TIME_TO_LIVE;
  }

  /**
   * This method returns <code>File.separator</code> as a bundle separator.
   *
   * @return It should environment specific in case files.
   */
  @Override
  public char getBundleSeparator() {
    return File.separatorChar;
  }
}
