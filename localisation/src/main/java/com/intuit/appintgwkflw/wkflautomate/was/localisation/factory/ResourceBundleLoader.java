package com.intuit.appintgwkflw.wkflautomate.was.localisation.factory;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FILE_SEPARATOR;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FORMAT_PROPS_SUFFIX;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.UNDER_SCORE;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.DEFAULT_FOLDER_NAME;
import static java.util.ResourceBundle.Control.TTL_NO_EXPIRATION_CONTROL;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationUtility;
import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;
import org.springframework.util.StringUtils;

/** This is interface for loading resource bundle. */
public interface ResourceBundleLoader {

  ResourceBundleLoaderType getType();

  /**
   * @param basePath base path for which reload requires
   * @param locale locale for which reload requires
   * @param loadTime time when bundle put into cache
   * @return In default case we dont want reloading the bundle so return is <code>false</code>
   */
  default boolean needsReload(String basePath, Locale locale, long loadTime) {
    return false;
  }

  ResourceBundle getResourceBundle(String basePath, Locale locale) throws IOException;

  /**
   * In default semanario we want permanent cache The unit is <Unit>milliSec</Unit>
   *
   * @return <code> TTL_NO_EXPIRATION_CONTROL</code>
   */
  default long getTimeToLive() {
    return TTL_NO_EXPIRATION_CONTROL;
  }

  /**
   * This method used to append locale as folder name before filename and as a suffix in filename.
   * If locale is empty then it appends only default folder name to before the file name.
   *
   * <p>It replace the filepath separator with bundle separator.
   *
   * <p>At the end it appends <code>.properties</code> <br>
   * <Example>
   *
   * <p>
   *  Lets say we have filePath is :
   *  filePath = templates.sla.templateName.nls.filenName
   *  locale = <code>new Locale("fr", "ca")</code> means french with canada region
   *  Steps :
   *  1. It converts the Locale to folder name. It will be fr_ca for <code>new Locale("fr", "ca")</code>.
   *  2. Split the filePath and get the file name for the last index of <code>FILE_SEPARATOR</code>. In this it would be 'fileName'
   *  3. It appends locale as folder name as folder name before fileName. localeFilePath becomes like this : templates.sla.templateName.nls.fr_ca
   *  4. It appends locale in the fileName as well as suffix. FileName = fileName_fr_ca
   *  5. It appends the localeFilePath anf FileName together. FilePath = templates.sla.templateName.nls.fr_ca.fileName_fr_ca
   *  6. It replace all the <code>FILE_SEPARATOR</code> with <code>getBundleSeparator()</code>
   *  7. At the end it adds .properties extension to the filePath. The exact file path is like 'templates/sla/templateName/nls/fr_ca/fileName_fr_ca.properties
   *   </p>
   *
   * result : templates/sla/templateName/nls/fr_ca/fileName_fr_ca.properties <br/>
   *
   * Special Use Case :
   * If localeFolderName is empty menas we have given empty locale in this case if we have defined
   * defaultFolderName then it would be add as folder before fileName.
   *
   * Lets say :
   * locale = <code>new Locale("", "")
   * <code>DEFAULT_FOLDER_NAME</code> = _master
   * result :templates/sla/templateName/nls/_master/fileName.properties <br/>
   *
   * <p></Example>
   *
   * @param filePath base file path of the resource bundle
   * @param locale locale this is used as folder name and suffix in filename
   * @return It returns full absolute path
   */
  default String getResourceName(String filePath, Locale locale) {
    if (StringUtils.isEmpty(filePath)) {
      return filePath;
    }
    String localeFolderName = LocalisationUtility.getFolderName(locale);
    int lastIndexOfSep = filePath.lastIndexOf(FILE_SEPARATOR);
    if (lastIndexOfSep != -1) {
      String fileName = filePath.substring(lastIndexOfSep + 1);
      StringBuilder baseDirectoryWithLocale = new StringBuilder();
      baseDirectoryWithLocale.append(filePath, 0, lastIndexOfSep).append(FILE_SEPARATOR);

      if (StringUtils.isEmpty(localeFolderName)) {
        if (!StringUtils.isEmpty(DEFAULT_FOLDER_NAME)) {
          baseDirectoryWithLocale.append(DEFAULT_FOLDER_NAME).append(FILE_SEPARATOR);
        }
        // append locale in file name as well
        filePath = baseDirectoryWithLocale.toString() + fileName;
      } else {
        baseDirectoryWithLocale.append(localeFolderName).append(FILE_SEPARATOR);
        // append locale in file name as well
        filePath = baseDirectoryWithLocale.toString() + fileName + UNDER_SCORE + localeFolderName;
      }
    }

    filePath = filePath.replace(FILE_SEPARATOR, getBundleSeparator());

    // This will add extension to the file
    return filePath + '.' + FORMAT_PROPS_SUFFIX;
  }

  /**
   * Default separator is <code>'/'</code>
   *
   * @return <code>'/'</code>
   */
  default char getBundleSeparator() {
    return '/';
  }
}
