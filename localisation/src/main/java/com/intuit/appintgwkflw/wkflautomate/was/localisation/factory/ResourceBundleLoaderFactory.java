package com.intuit.appintgwkflw.wkflautomate.was.localisation.factory;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** This class acts as a factory to return ResourceBundleLoader. */

@Component
public class ResourceBundleLoaderFactory {

  @Autowired
  private  List<ResourceBundleLoader> resourceBundleLoaderList;

  @PostConstruct
  private void init() {
    for (ResourceBundleLoader resourceBundleLoader : resourceBundleLoaderList) {
      addLoader(resourceBundleLoader.getType(), resourceBundleLoader);
    }

  }
  private static final Map<ResourceBundleLoaderType, ResourceBundleLoader>
      RESOURCE_BUNDLE_LOADER_MAP = new EnumMap<>(ResourceBundleLoaderType.class);

  /**
   * Adds a loader.
   *
   * @param loaderType the loader type
   * @param resourceBundleLoader the resource bundle loader
   */
  public static void addLoader(
      ResourceBundleLoaderType loaderType, ResourceBundleLoader resourceBundleLoader) {
    RESOURCE_BUNDLE_LOADER_MAP.put(loaderType, resourceBundleLoader);
  }

  /**
   * Gets loader.
   *
   * @param resourceBundleLoaderType input loader type
   * @return action ResourceBundleLoader impl
   */
  public static ResourceBundleLoader getLoader(ResourceBundleLoaderType resourceBundleLoaderType) {
    return RESOURCE_BUNDLE_LOADER_MAP.get(resourceBundleLoaderType);
  }

  /**
   * Contains boolean.
   *
   * @param resourceBundleLoaderType input resourceBundleLoaderType
   * @return true /false if loader is present or not
   */
  public static boolean contains(ResourceBundleLoaderType resourceBundleLoaderType) {
    return RESOURCE_BUNDLE_LOADER_MAP.containsKey(resourceBundleLoaderType);
  }
}
