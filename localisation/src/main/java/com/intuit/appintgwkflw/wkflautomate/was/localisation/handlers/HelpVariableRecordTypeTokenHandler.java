package com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class HelpVariableRecordTypeTokenHandler implements TokenHandler<String> {

  /**
   * Returns the token after replacing with given recordType display value.
   *
   * <pre>
   *   format("[[%Record% Number]],Optional, Invoice) = [[Invoice Number]]
   * </pre>
   *
   * @param token Value which need to be formatted. If this is empty returns token as it is.
   * @param locale optional
   * @param recordTypeDisplayValue used to replace the %Record% texts in the given token. If
   *     recordType is null returns token as it is.
   * @return formatted string
   */
  @Override
  public String format(String token, String locale, String recordTypeDisplayValue) {
    if (StringUtils.isBlank(token) || StringUtils.isBlank(recordTypeDisplayValue)) {
      return token;
    }
    return token.replace(GLOBAL_PARAMETERS.RECORD, StringUtils.capitalize(recordTypeDisplayValue));
  }

  /**
   * This method returns the Token.HELP_VARIABLE_RECORD_TYPE enum.
   *
   * @return Token.HELP_VARIABLE_RECORD_TYPE enum
   */
  @Override
  public Token getTokenType() {
    return Token.HELP_VARIABLE_RECORD_TYPE;
  }
}
