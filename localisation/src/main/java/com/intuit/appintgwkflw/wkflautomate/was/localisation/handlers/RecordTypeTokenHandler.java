package com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.AppLocalisationResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class RecordTypeTokenHandler implements TokenHandler<String> {

  public RecordTypeTokenHandler(final AppLocalisationResourceBundle appLocalisationResourceBundle) {
    this.appLocalisationResourceBundle = appLocalisationResourceBundle;
  }

  private final AppLocalisationResourceBundle appLocalisationResourceBundle;

  /**
   * Returns the formatted and localised string.
   *
   * <pre>
   *   format("%Record%", "en_US", "Invoice") =  Invoice
   *   format("%Record%", "fr_CA", "Invoice") = Facture d'achat
   *   format("%record%s", "", "Invoice") =  invoices (default locale taken en_US).
   * </pre>
   *
   * @param token Value which need to be formatted and further localised.
   * @param locale after replacing <code>token</code> with recordType it acts as key for given
   *     locale.
   * @param recordTypeDisplayValue the value of recordType
   * @return Formatted and localised string
   */
  @Override
  public String format(String token, String locale, String recordTypeDisplayValue) {
    if (StringUtils.isBlank(token) || StringUtils.isBlank(recordTypeDisplayValue)) {
      return token;
    }
    String recordKey =
        token
            .replace(GLOBAL_PARAMETERS.RECORD, StringUtils.capitalize(recordTypeDisplayValue))
            .replace(GLOBAL_PARAMETERS.RECORD.toLowerCase(), recordTypeDisplayValue.toLowerCase())
            .replace(" ", "");
    return appLocalisationResourceBundle.getString(recordKey, locale);
  }

  /**
   * This method returns the Token.RECORD_TYPE enum.
   *
   * @return Token.RECORD_TYPE enum
   */
  @Override
  public Token getTokenType() {
    return Token.RECORD_TYPE;
  }
}
