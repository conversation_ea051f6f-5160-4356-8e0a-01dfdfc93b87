package com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;

/**
 * This is an interface for all the token Handlers
 *
 * @param <T> represent the data type of value in which given token to be formatted.
 */
public interface TokenHandler<T> {

  /**
   * Returns the formatted string.
   *
   * @param token Value which need to be formatted
   * @param locale Some formatHandler further requires localisation, this used as locale.
   * @param value this value used to format the given <code>format</code>
   * @return String
   */
  String format(String token, String locale, T value);

  /**
   * Return the token type of the Handler.
   *
   * @return Token type
   */
  Token getTokenType();
}
