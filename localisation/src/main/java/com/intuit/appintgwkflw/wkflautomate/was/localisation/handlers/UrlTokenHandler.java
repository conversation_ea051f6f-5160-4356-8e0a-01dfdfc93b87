package com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.ExternalServiceMapping;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import org.springframework.stereotype.Component;

@Component
public class UrlTokenHandler implements TokenHandler<String> {

  public UrlTokenHandler(final ExternalServiceMapping externalServiceMapping) {
    this.externalServiceMapping = externalServiceMapping;
  }

  private final ExternalServiceMapping externalServiceMapping;

  /**
   * Returns url on the basis of token
   *
   * <pre>
   *   format("taskManagerUrl", "Optional" "qal") = https://taskmanager.com
   * </pre>
   *
   * @param token Value which need to be formatted
   * @param locale Optional
   * @param value Optional.
   * @return returns the url
   */
  @Override
  public String format(String token, String locale, String value) {
    String url = externalServiceMapping.getUrlFromUrlType(token);
    return url == null ? token : url;
  }

  /**
   * This method returns the Token.URL_TYPE enum.
   *
   * @return Token.URL_TYPE enum
   */
  @Override
  public Token getTokenType() {
    return Token.URL_TYPE;
  }
}
