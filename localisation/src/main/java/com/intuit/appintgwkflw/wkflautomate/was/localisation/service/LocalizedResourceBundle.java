package com.intuit.appintgwkflw.wkflautomate.was.localisation.service;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.DEFAULT_LOCALE;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.RELOAD;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationVerify;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationUtility;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;
import org.springframework.stereotype.Component;

/**
 * <pre>
 * This class is the custom implementation of <code>ResourceBundle.Control<code/>.
 * Reason for this class :
 * We have different file structure for the localised resources. This class <code>ResourceBundle.Control<code/>
 * is not fulfilling  our all requirements.
 * </pre>
 */
@Component
public class LocalizedResourceBundle extends ResourceBundle.Control {

  /**
   *
   *
   * <pre>
   * This method used to know how long the bundle needs to be cached.
   * </pre>
   *
   * @param baseName the base name of the resource bundle for which the expiration value is
   *     specified.
   * @param locale the locale of the resource bundle for which the expiration value is specified.
   * @return return how long the bundle in cache.
   */
  @Override
  public long getTimeToLive(String baseName, Locale locale) {

    LocalisationVerify.verify(baseName, LocalisationError.INPUT_INVALID, "baseName");
    LocalisationVerify.verify(locale == null, LocalisationError.INPUT_INVALID, "locale");
    if (!RELOAD) {
      return super.getTimeToLive(baseName, locale);
    }
    return getWASResourceBundleLocator(baseName).getTimeToLive();
  }

  /**
   * This method used to load the bundle for the given filepath and locale.
   *
   * @param filePath the filePath of the resource bundle, a fully qualified path
   * @param locale the locale for which the resource bundle should be instantiated
   * @param format the resource bundle format to be loaded
   * @param loader the <code>ClassLoader</code> to use to load the bundle
   * @param reload the flag to indicate bundle reloading; <code>true</code> if reloading an expired
   *     resource bundle, <code>false</code> otherwise
   * @return the resource bundle instance, or <code>null</code> if none could be found.
   */
  @Override
  public ResourceBundle newBundle(
      String filePath, Locale locale, String format, ClassLoader loader, boolean reload) {
    if (!FORMAT_PROPERTIES.get(0).equals(format)) {
      return null;
    }
    if (locale == null) {
      return null;
    }
    try {
      return getWASResourceBundleLocator(filePath).getResourceBundle(filePath, locale);
    } catch (IOException e) {
      return null;
    }
  }

  /**
   * This method returns the list of format of files. We are reading only property files. This is
   * containing only one value <value>.properties</value>
   *
   * @param baseName the base name of the resource bundle, a fully qualified path
   * @return a <code>List</code> of <code>String</code>s containing <value>.properties</value> for
   *     loading resource bundles.
   */
  @Override
  public List<String> getFormats(String baseName) {
    LocalisationVerify.verify(baseName, LocalisationError.INPUT_INVALID, "baseName");
    return FORMAT_PROPERTIES;
  }

  /**
   * This method call when we are caching our resource bundle. It check do we need to reload the
   * bundle again into the cache.
   *
   * @param baseName the base bundle name of the resource bundle, a fully qualified path
   * @param locale the locale for which the resource bundle should be instantiated
   * @param format the resource bundle format to be loaded
   * @param loader the <code>ClassLoader</code> to use to load the bundle
   * @param bundle the resource bundle instance that has been expired in the cache
   * @param loadTime the time when <code>bundle</code> was loaded and put in the cache
   * @return <code>true</code> if the expired bundle needs to be reloaded; <code>false</code>
   *     otherwise.
   */
  @Override
  public boolean needsReload(
      String baseName,
      Locale locale,
      String format,
      ClassLoader loader,
      ResourceBundle bundle,
      long loadTime) {
    LocalisationVerify.verify(bundle == null, LocalisationError.INPUT_INVALID, "bundle");

    if (!FORMAT_PROPERTIES.get(0).equals(format)) {
      return false;
    }
    return getWASResourceBundleLocator(baseName).needsReload(baseName, locale, loadTime);
  }

  /**
   *
   *
   * <pre>
   * This method gives the default locale. If the bundle does not exists for the given locale then it will retry in the default locale bundle.
   * The value of default locale is defined in <code>DEFAULT_LOCALE</code>
   * e.g.
   * We are looking key in Locale fr but bundle does not exists for the fr locale. Then it will check in  the default locale bundle.
   *
   * If default locale same as <code>locale</code> parameter then it returns null because if key is not present in the default locale as well then it would stuck
   * in the infinite loop.
   * </pre>
   *
   * @param baseName the base name of the resource bundle, a fully qualified class name for which
   *     <code>ResourceBundle.getBundle</code> has been unable to find any resource bundles (except
   *     for the base bundle)
   * @param locale the <code>Locale</code> for which <code>ResourceBundle.getBundle</code> has been
   *     unable to find any resource bundles (except for the base bundle)
   * @return a <code>Locale</code> for the fallback search, or <code>null</code> if no further
   *     fallback search is desired.
   */
  @Override
  public Locale getFallbackLocale(String baseName, Locale locale) {
    Locale defaultLocale = LocalisationUtility.getLocale(DEFAULT_LOCALE);
    return locale.equals(defaultLocale) ? null : defaultLocale;
  }

  /**
   * The reason for overriding this method, in <code>ResourceBundle.Control</code> this method
   * splits the locale like this if we have locale say: en_US then it will convert into three
   * different locales "", en, en_US now "" act as parent for en and en acts as parent for "en_US"
   * First it will look into en_US if it is not present then it check in its parent say en. Anyhow
   * if key present in en (parent) then parent locale data will be returned. That would not be a
   * case, so we need to override this method.
   *
   * @param baseName the base name of the resource bundle, a fully qualified class name
   * @param locale the locale for which a resource bundle is desired
   * @return a <code>List</code> of candidate <code>Locale</code>s for the given <code>locale</code>
   */
  @Override
  public List<Locale> getCandidateLocales(String baseName, Locale locale) {
    return Collections.singletonList(locale);
  }

  /**
   * This method using <code>wasResourceBundleFactory</code> find the <code>WASResourceBundleLocator
   * </code>
   *
   * @param basePath the base path of the resource bundle, a fully qualified class name.
   * @return Instance <code>ResourceBundleLocator</code>
   */
  public ResourceBundleLoader getWASResourceBundleLocator(String basePath) {
    return ResourceBundleLoaderFactory.getLoader(
        ResourceBundleLoaderType.getResourceBundleLocatorTypeFromBundlePath(basePath));
  }
}
