package com.intuit.appintgwkflw.wkflautomate.was.localisation.service;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationVerify;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.TokenHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/** This class is responsible for translating the text and format the tokens. */
@Component
public class TranslationService {

  private static final String delimiter = "\\|";
  private static final String openBracket = "{";
  private static final String closeBracket = "}";
  private final AppLocalisationResourceBundle appLocalisationResourceBundle;
  private final List<TokenHandler> tokenHandlerList;
  private static Map<Token, TokenHandler> tokenHandlerMap;
  // This regex finds out all the strings starts with '{' and end with '}'.
  private static final String tokenRegex = "\\{.*?}";
  private static final Pattern pattern = Pattern.compile(tokenRegex);
  private static final String LOCALISED_PREFIX = "~";

  public TranslationService(final AppLocalisationResourceBundle appLocalisationResourceBundle, final List<TokenHandler> tokenHandlerList) {
    this.appLocalisationResourceBundle = appLocalisationResourceBundle;
    this.tokenHandlerList = tokenHandlerList;
  }

  @PostConstruct
  private void init() {
    setTokenHandlerMap();
  }

  /**
   * Returns a localised string using the specified key, localeStr.
   *
   * @param key localization key for which we have to fetch localized string.
   * @param localeStr the localeStr for which specified key mapped.
   * @return A localised string
   */
  public String getString(String key, String localeStr) {

    if (!isValidKey(key)) {
      return key;
    }
    // trim and remove tilda here
    key = key.trim().replace(LOCALISED_PREFIX, "");
    return appLocalisationResourceBundle.getString(key, localeStr);
  }

  /**
   * Returns a localised and formatted string using the specified key, localeStr and recordType.
   *
   * @param key localization key for which we have to fetch localized string.
   * @param localeStr the localeStr for which specified key mapped.
   * @param recordTypeDisplayValue the recordType used as value of token parameters. If it null then
   *     it throws WorkflowError.INPUT_INVALID error.
   * @return A localised and formatted string
   */
  public String getFormattedString(String key, String localeStr, String recordTypeDisplayValue) {
    LocalisationVerify.verify(
        StringUtils.isBlank(recordTypeDisplayValue),
        LocalisationError.INPUT_INVALID,
        "Record Type");
    return processTokens(key, getTokenValueMap(recordTypeDisplayValue), localeStr);
  }

  /**
   * Returns a localised and formatted string using the specified key, localeStr.
   *
   * @param key
   * @param localeStr
   * @return
   */
  public String getFormattedString(String key, String localeStr) {
    return processTokens(key, Map.of(), localeStr);
  }

  /**
   * Returns a localised and formatted string using the specified key, localeStr and tokenValueMap.
   * It performs two operations in step first it gets the localisedString for the key using given
   * localeStr. This localisedString can further contains various token parameters which can be
   * {recordType|%Record%}, {recordType|%record%s}, {helpVariableRecordType|[[%Record% Number]]} and
   * {url|taskManagerURL}. Using @TokenHandler all these tokens to be formatted. If there is not
   * token parameter exists then it returns the localised string as it is.
   *
   * @param key key localization key for which we have to fetch localized string.
   * @param tokenValueMap tokenValueMap contains values w.r.t tokenType {@link Token}
   * @param localeStr localeStr the localeStr for which specified key mapped.
   * @return A localised and formatted string
   */
  private String processTokens(String key, Map<Token, Object> tokenValueMap, String localeStr) {
    if (!isValidKey(key)) {
      return key;
    }
    String localisedContent = getString(key, localeStr);
    Set<String> tokens = getTokens(localisedContent);
    if (CollectionUtils.isEmpty(tokens)) {
      return localisedContent;
    }
    for (String token : tokens) {
      TokenHandler tokenHandler = getTokenHandler(token);
      // e.g. {[[Company Email]]} --> [[Company Email]]
      if (tokenHandler == null) {
        localisedContent = localisedContent.replace(openBracket + token + closeBracket, token);
      } else {
        String formattedValue =
            tokenHandler.format(
                getTokenParameter(token),
                localeStr,
                tokenValueMap.get(tokenHandler.getTokenType()));
        localisedContent =
            localisedContent.replace(openBracket + token + closeBracket, formattedValue);
      }
    }
    return localisedContent;
  }

  /**
   * Checks the specified key is starts with Tilda(~) or not.
   *
   * @param key key for which validation operation performed.
   * @return true if key is not empty and starts with Tilda(~).
   */
  private boolean isValidKey(String key) {
    return StringUtils.isNotBlank(key) && key.trim().startsWith(LOCALISED_PREFIX);
  }

  /**
   * Returns a set of all the tokens present in the localisedContent. Using this regex "{.*?}" it
   * find out the all the strings starts with { and end with }.
   *
   * <pre>
   *  For example we have localisedContent  = Hi,\n\n{recordType|%Record%} {helpVariableRecordType|[[%Record% Number]]} is pending
   *  approval. Please approve it at the earliest using this task manager link -{url|taskManagerUrl}.\n\nNote that {recordType|%record%s}
   *  that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n{[[CompanyName]]}.
   *
   * Then using regex "{.*?}" it find out all the strings starts with { and end with }".
   *
   * Using these indexes we find out the content present in between "{}".
   * In this localised string we have 5 tokens :
   *
   * 1. {recordType|%Record%}
   * 2. {helpVariableRecordType|[[%Record% Number]]}
   * 3. {url|taskManagerUrl}
   * 4. {recordType|%record%s}
   * 5. {[[CompanyName]]}
   *
   * It returns the set of all these tokens.
   * </pre>
   *
   * @param localisedContent
   * @return set of all the tokens
   */
  public Set<String> getTokens(String localisedContent) {
    Set<String> tokenSet = new HashSet<>();
    if (StringUtils.isNotBlank(localisedContent)) {
      Matcher matcher = pattern.matcher(localisedContent);
      while (matcher.find()) {
        int start = matcher.start();
        int end = matcher.end();
        // +1 to escape { and -1 to escape }
        tokenSet.add(localisedContent.substring(start + 1, end - 1));
      }
    }

    return Collections.unmodifiableSet(tokenSet);
  }

  /**
   * Returns the tokenType.
   *
   * <pre>
   *     getFormatType("recordType|%Record%") = recordType
   *     getFormatType("helpVariableRecordType|[[%Record% Number]]") = helpVariableRecordType
   *     getFormatType("url|taskManagerUrl") = url
   * </pre>
   *
   * @param token Split this using delimiter and return the first index value.
   * @return tokenType
   */
  private String getTokenType(String token) {
    return token.split(delimiter)[0];
  }

  /**
   * Returns the token parameter.
   *
   * <pre>
   *     getFormatType("recordType|%Record%") = %Record%
   *     getFormatType("helpVariableRecordType|[[%Record% Number]]") = [[%Record% Number]]
   *     getFormatType("url|taskManagerUrl") = taskManagerUrl
   * </pre>
   *
   * @param token Split this using delimiter and return the 2nd index value.
   * @return tokenParameter
   */
  private String getTokenParameter(String token) {
    return token.split(delimiter)[1];
  }

  /**
   * Return TokenHandler w.r.t to tokenType defined in token before "//|".
   *
   * <pre>
   * getFormatTypeHandler("recordType|%Record%") = RecordTypeTokenHandler
   * getFormatTypeHandler("helpVariableRecordType|[[%Record% Number]]") = HelpVariableRecordToken sHandler
   * getFormatTypeHandler("url") = UrlTokenHandler
   * </pre>
   *
   * @param token using this TokenHandlerFactory finds out the TokenHandler
   * @return TokenHandler
   */
  private TokenHandler getTokenHandler(String token) {
    return tokenHandlerMap.get(Token.getTokenFromTokenType(getTokenType(token)));
  }

  /**
   * Return a Immutable Map of Token as key and their associated values.
   *
   * @param recordTypeDisplayValue used as value for Token.RecordType and FormatType.HelpVariableRecordType key
   * @return Return a Immutable Map
   */
  private Map<Token, Object> getTokenValueMap(String recordTypeDisplayValue) {
    return Map.of(Token.RECORD_TYPE, recordTypeDisplayValue, Token.HELP_VARIABLE_RECORD_TYPE, recordTypeDisplayValue);
  }

  /** This method convert tokenHandlerList Into Map */
  public void setTokenHandlerMap() {
    tokenHandlerMap =
        tokenHandlerList.stream()
            .collect(Collectors.toMap(TokenHandler::getTokenType, Function.identity()));
  }
}
