package com.intuit.appintgwkflw.wkflautomate.was.localisation.util;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FILE_SEPARATOR;

public interface Constants {

  interface GLOBAL_PARAMETERS {

    String EMPTY = "";
    char UNDER_SCORE = '_';
    char FILE_SEPARATOR = '.';
    String FORMAT_PROPS_SUFFIX = "properties";
    String FILE_PREFIX = "file://";
    String RECORD = "%Record%";
  }

  interface RESOURCE_BUNDLE {

    String BASE_PATH =
        new StringBuilder()
            .append("templates")
            .append(FILE_SEPARATOR)
            .append("sla")
            .append(FILE_SEPARATOR)
            .toString();
    /** ##TODO
     * https://jira.intuit.com/browse/QBOES-12539
     * We are making default locale as empty because all the keys are not localised yet, once it localised then we change it to en.
     */
    //String DEFAULT_LOCALE = "en";
    String DEFAULT_LOCALE = "";
    boolean RELOAD = false;
    long TIME_TO_LIVE = 1000L;
    String SUFFIX_PATH = new StringBuilder().append("nls").append(FILE_SEPARATOR).toString();
    String DEFAULT_FOLDER_NAME = "_master";
  }
}
