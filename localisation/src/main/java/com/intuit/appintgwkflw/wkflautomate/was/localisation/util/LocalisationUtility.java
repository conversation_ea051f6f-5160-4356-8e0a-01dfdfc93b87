package com.intuit.appintgwkflw.wkflautomate.was.localisation.util;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.EMPTY;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.UNDER_SCORE;

import java.util.Locale;
import org.springframework.util.StringUtils;

public final class LocalisationUtility {
  private LocalisationUtility() {
    // Private constructor to prevent instantiation
  }

  /**
   * This method used to get the folder name from the locale.
   *
   * <pre>
   *   This method gets language , country and variant from the locale
   *   e.g.
   *   - Use case 1 :
   *        Locale = {language = "en", country = "", variant = ""}
   *        return "en"
   *    - Use case 2 :
   *        Locale = {language = "en", country = "US", variant = ""}
   *        return "en_US"
   *    - Use case 3 :
   *        Locale = {language = "en", country = "US", variant = "123"}
   *         return "en_US_123"
   * </pre>
   *
   * @param locale locale for folder name
   * @return if <code>locale</code> is root or null return empty else create folder name using lang,
   *     country and variant
   */
  public static String getFolderName(Locale locale) {
    if (locale == null || locale == Locale.ROOT) {
      return EMPTY;
    }

    String language = locale.getLanguage();
    String country = locale.getCountry();
    String variant = locale.getVariant();

    if (StringUtils.isEmpty(language)
        && StringUtils.isEmpty(country)
        && StringUtils.isEmpty(variant)) {
      return EMPTY;
    }
    // only with language e.g localeString = en
    StringBuilder sb = new StringBuilder(language);
    if (!StringUtils.isEmpty(country)) {
      //  with language and country e.g localeString = en_US
      sb.append(UNDER_SCORE).append(country);
      if (!StringUtils.isEmpty(variant)) {
        // with some variant as well e.g localeString = en_US_realmId
        sb.append(UNDER_SCORE).append(variant);
      }
    }
    return sb.toString();
  }

  /**
   * This method converts locale string to the <code>java.util.locale</code>
   *
   * <pre>
   *    1. If <code>localeString</code> is empty return <code>java.util.Locale.ROOT</code>
   *    2. Split with '_'
   *    3. 0th index represents the language.
   *    4. 1st index represents the country.
   *    5. 2nd index represents the variant.
   *
   *  e.g.
   *   - USE CASE 1 :
   *      localeString = "en"
   *      return new Locale("en);
   *   - USE CASE 2 :
   *      localeString = "en_US"
   *      return new Locale("en", "US");
   *   - USE CASE 2 :
   *       localeString = "en_US_123"
   *       return new Locale("en", "US","123");
   * </pre>
   *
   * @param localeString this is locale string for which <code>Locale</code> expected
   * @return <code>localeString</code> if it is empty simply return <code>Locale.Root</code>
   */
  public static Locale getLocale(String localeString) {
    if (StringUtils.isEmpty(localeString)) {
      return Locale.ROOT;
    }
    String[] localeSubStrings = localeString.split("_");
    String language = localeSubStrings[0];
    // only with language e.g localeString = en
    Locale locale = new Locale(language);
    if (localeSubStrings.length == 2) {
      //  with language and country e.g localeString = en_US
      String country = localeSubStrings[1];
      locale = new Locale(language, country);
    }
    if (localeSubStrings.length > 2) {
      // with some variant as well e.g localeString = en_US_realmId
      String country = localeSubStrings[1];
      String variant = localeSubStrings[2];
      locale = new Locale(language, country, variant);
    }

    return locale;
  }
}
