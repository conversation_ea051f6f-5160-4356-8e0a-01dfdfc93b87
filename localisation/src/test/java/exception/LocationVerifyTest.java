package exception;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationVerify;
import org.junit.Assert;
import org.junit.Test;

public class LocationVerifyTest {

    @Test(expected = LocalisationGeneralException.class)
    public void verifyTrue() {
        String x = null;
        LocalisationVerify.verify(x == null, LocalisationError.INTERNAL_EXCEPTION);
    }

    @Test
    public void verifyFalse() {
        String x = "acb";
        LocalisationVerify.verify(x == null, LocalisationError.INTERNAL_EXCEPTION);
    }

    @Test(expected = LocalisationGeneralException.class)
    public void verifyTrueWithoutError() {
        String x = null;
        LocalisationVerify.verify(x == null, null);
    }

    @Test(expected = LocalisationGeneralException.class)
    public void verifyFalseWithoutError() {
        String x = null;
        LocalisationVerify.verify(x == null, null);
    }

    @Test
    public void verifyFormatMsg() {
        String x = null;
        try {
            LocalisationVerify.verify(x == null, LocalisationError.OTHER_TEST, "errorArg1");
        } catch (LocalisationGeneralException e) {
            Assert.assertTrue(e.getMessage().contains("errorArg1"));
        }
    }

    @Test(expected = LocalisationGeneralException.class)
    public void verifyFormatMsg1() {
        String x = null;
        LocalisationVerify.verify(x == null, LocalisationError.OTHER_TEST, null);
    }

    @Test(expected = LocalisationGeneralException.class)
    public void verifyNullTrue() {
        String x = null;
        LocalisationVerify.verifyNull(x, LocalisationError.INTERNAL_EXCEPTION);
    }

    @Test
    public void verifyNullFalse() {
        String x = "abc";
        LocalisationVerify.verifyNull(x, LocalisationError.INTERNAL_EXCEPTION);
    }

    @Test(expected = LocalisationGeneralException.class)
    public void verifyNullWithoutError() {
        String x = null;
        LocalisationVerify.verifyNull(x, null);
    }

    @Test
    public void verifyStringNull() {
        String x = null;
        try {
            LocalisationVerify.verify(x, LocalisationError.OTHER_TEST, "errorArg1");
        } catch (LocalisationGeneralException e) {
            Assert.assertTrue(e.getMessage().contains("errorArg1"));
        }
    }

    @Test
    public void verifyStringEmpty() {
        String x = "";
        try {
            LocalisationVerify.verify(x, LocalisationError.OTHER_TEST, "errorArg1");
        } catch (LocalisationGeneralException e) {
            Assert.assertTrue(e.getMessage().contains("errorArg1"));
        }
    }
}


