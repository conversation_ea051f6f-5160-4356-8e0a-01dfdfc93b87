package factory;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.FileResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE;
import java.io.File;
import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class FileResourceBundleLoaderTest {
  private final String USER_DIRECTORY = System.getProperty("user.dir");
  private FileResourceBundleLoader fileResourceBundleLoader;
  private final String BASE_PATH =
      new StringBuilder(GLOBAL_PARAMETERS.FILE_PREFIX)
          .append(USER_DIRECTORY)
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("src")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("test")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("resources")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("templates")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("sla")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("CustomWorkflowTemplate")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("nls")
          .append(GLOBAL_PARAMETERS.FILE_SEPARATOR)
          .append("workflowContent")
          .toString();
  private final Locale CANADA_FRENCH_LOCALE = new Locale("fr", "ca");

  @Before
  public void init() {
    fileResourceBundleLoader = new FileResourceBundleLoader();
  }

  @Test
  public void getTypeShouldReturnFileBundleType() {
    Assert.assertTrue(
        fileResourceBundleLoader.getType().equals(ResourceBundleLoaderType.FILE_BUNDLE_TYPE));
  }

  @Test
  public void getTimeToLiveShouldReturnSameValueDefinedInConstant() {
    Assert.assertTrue(fileResourceBundleLoader.getTimeToLive() == RESOURCE_BUNDLE.TIME_TO_LIVE);
  }

  @Test
  public void testResourceNameWithCanadaFrenchLocale() {
    String resourceName =
        new StringBuilder(GLOBAL_PARAMETERS.FILE_PREFIX)
            .append(USER_DIRECTORY)
            .append(File.separatorChar)
            .append("src")
            .append(File.separatorChar)
            .append("test")
            .append(File.separatorChar)
            .append("resources")
            .append(File.separatorChar)
            .append("templates")
            .append(File.separatorChar)
            .append("sla")
            .append(File.separatorChar)
            .append("CustomWorkflowTemplate")
            .append(File.separatorChar)
            .append("nls")
            .append(File.separatorChar)
            .append("fr_CA")
            .append(File.separatorChar)
            .append("workflowContent_fr_CA.properties")
            .toString();
    Assert.assertTrue(
        resourceName.equals(
            fileResourceBundleLoader.getResourceName(BASE_PATH, CANADA_FRENCH_LOCALE)));
  }

  @Test
  public void testResourceNameWithEmptyLocale() {
    String resourceName =
        new StringBuilder(GLOBAL_PARAMETERS.FILE_PREFIX)
            .append(USER_DIRECTORY)
            .append(File.separatorChar)
            .append("src")
            .append(File.separatorChar)
            .append("test")
            .append(File.separatorChar)
            .append("resources")
            .append(File.separatorChar)
            .append("templates")
            .append(File.separatorChar)
            .append("sla")
            .append(File.separatorChar)
            .append("CustomWorkflowTemplate")
            .append(File.separatorChar)
            .append("nls")
            .append(File.separatorChar)
            .append(RESOURCE_BUNDLE.DEFAULT_FOLDER_NAME)
            .append(File.separatorChar)
            .append("workflowContent.properties")
            .toString();
    Assert.assertTrue(
        resourceName.equals(fileResourceBundleLoader.getResourceName(BASE_PATH, Locale.ROOT)));
  }

  @Test
  public void testResourceBundleWithCanadaFrenchLocaleShouldReturnNotNull() throws IOException {
    ResourceBundle resourceBundle =
        fileResourceBundleLoader.getResourceBundle(BASE_PATH, CANADA_FRENCH_LOCALE);
    Assert.assertNotNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithEmptyLocaleShouldReturnNotNull() throws IOException {
    ResourceBundle resourceBundle =
        fileResourceBundleLoader.getResourceBundle(BASE_PATH, Locale.ROOT);
    Assert.assertNotNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithEmptyBasePathShouldReturnNull() throws IOException {
    ResourceBundle resourceBundle = fileResourceBundleLoader.getResourceBundle("", Locale.ROOT);
    Assert.assertNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithNullBasePathShouldReturnNull() throws IOException {
    ResourceBundle resourceBundle = fileResourceBundleLoader.getResourceBundle(null, Locale.ROOT);
    Assert.assertNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithInvalidBasePath() throws IOException {
    ResourceBundle resourceBundle =
        fileResourceBundleLoader.getResourceBundle(BASE_PATH + "INVALID", Locale.ROOT);
    Assert.assertNull(resourceBundle);
  }

  @Test
  public void testBundleSeparatorShouldReturnFileSeparator() {
    Assert.assertTrue(fileResourceBundleLoader.getBundleSeparator() == File.separatorChar);
  }

  @Test
  public void testNeedReloadsWithZeroLoadTime() {
    // always need reload because load time of file is 0L
    Assert.assertTrue(fileResourceBundleLoader.needsReload(BASE_PATH, Locale.ROOT, 0L));
  }

  @Test
  public void testNeedReloadsWithEmptyBasePath() {
    Assert.assertFalse(fileResourceBundleLoader.needsReload("", Locale.ROOT, 0L));
  }

  @Test
  public void testNeedReloadsWithNullBasePath() {
    Assert.assertFalse(fileResourceBundleLoader.needsReload(null, Locale.ROOT, 0L));
  }

  @Test
  public void testNeedReloadsWithFileNotExist() {
    // this means file deleted at run time
    Assert.assertTrue(fileResourceBundleLoader.needsReload(BASE_PATH + "INVALID", Locale.ROOT, 0L));
  }

  @Test
  public void testNeedReloadsWithCurrentTime() {
    // dont need reload because load time of file is System.currentTimeMillis() which means modified
    // time always be lesser than current time
    Assert.assertFalse(
        fileResourceBundleLoader.needsReload(BASE_PATH, Locale.ROOT, System.currentTimeMillis()));
  }
}
