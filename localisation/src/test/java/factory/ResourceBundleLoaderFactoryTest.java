package factory;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.FileResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class ResourceBundleLoaderFactoryTest {

  private ClassloaderResourceBundleLoader classloaderResourceBundleLoader;

  private FileResourceBundleLoader fileResourceBundleLoader;

  @Before
  public void init() {
    classloaderResourceBundleLoader = new ClassloaderResourceBundleLoader();
    fileResourceBundleLoader = new FileResourceBundleLoader();
    ResourceBundleLoaderFactory.addLoader(
        classloaderResourceBundleLoader.getType(), classloaderResourceBundleLoader);
    ResourceBundleLoaderFactory.addLoader(
        fileResourceBundleLoader.getType(), fileResourceBundleLoader);
  }

  @Test
  public void testClassloaderResourceBundleExists() {
    ResourceBundleLoader resourceBundleLoader =
        ResourceBundleLoaderFactory.getLoader(ResourceBundleLoaderType.CLASSLOADER_BUNDLE_TYPE);
    Assert.assertTrue(classloaderResourceBundleLoader.equals(resourceBundleLoader));
  }

  @Test
  public void testFileResourceBundleExists() {
    ResourceBundleLoader resourceBundleLoader =
        ResourceBundleLoaderFactory.getLoader(ResourceBundleLoaderType.FILE_BUNDLE_TYPE);
    Assert.assertTrue(fileResourceBundleLoader.equals(resourceBundleLoader));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(
        ResourceBundleLoaderFactory.contains(ResourceBundleLoaderType.CLASSLOADER_BUNDLE_TYPE));
    Assert.assertTrue(
        ResourceBundleLoaderFactory.contains(ResourceBundleLoaderType.FILE_BUNDLE_TYPE));
  }
}
