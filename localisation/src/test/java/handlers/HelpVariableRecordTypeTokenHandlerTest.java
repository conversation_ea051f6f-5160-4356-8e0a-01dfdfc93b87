package handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.HelpVariableRecordTypeTokenHandler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class HelpVariableRecordTypeTokenHandlerTest {

  private HelpVariableRecordTypeTokenHandler helpVariableRecordFormatTypeHandler;
  private final String CANADA_FRENCH_LOCALE = "fr_CA";
  private final String format = "[[%Record% Number]]";
  private final String expected = "[[Invoice Number]]";
  private final String recordType = "Invoice";

  @Before
  public void init() {
    helpVariableRecordFormatTypeHandler = new HelpVariableRecordTypeTokenHandler();
  }

  @Test
  public void testFormatType() {
    Assert.assertEquals(
        Token.HELP_VARIABLE_RECORD_TYPE,
        helpVariableRecordFormatTypeHandler.getTokenType());
  }

  @Test
  public void testFormatWithFormatValue() {
    Assert.assertEquals(
        expected,
        helpVariableRecordFormatTypeHandler.format(format, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithEmptyFormatValueMap() {
    Assert.assertEquals(
        "", helpVariableRecordFormatTypeHandler.format("", CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithNullFormatValueMap() {
    Assert.assertEquals(
        null, helpVariableRecordFormatTypeHandler.format(null, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithFormatValueMapWithNullRecordType() {
    Assert.assertEquals(
        format, helpVariableRecordFormatTypeHandler.format(format, CANADA_FRENCH_LOCALE, null));
  }
}
