package handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.LocalisationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.RecordTypeTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.LocalizedResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.AppLocalisationResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class RecordTypeTokenHandlerTest {
  private RecordTypeTokenHandler recordTypeFormatTypeHandler;
  private LocalisationConfig localisationConfig;
  private final String CANADA_FRENCH_LOCALE = "fr_CA";
  private final String format = "%Record%";
  private final String expected = "fr_CAInvoice";
  private final String recordType = "Invoice";

  @Before
  public void init() {
    localisationConfig = new LocalisationConfig();
    localisationConfig.setBasePath("templates.sla.");
    localisationConfig.setFolderName("CustomWorkflowTemplate");
    localisationConfig.setFileName("workflowContent");
    ClassloaderResourceBundleLoader classloaderResourceBundleLoader =
        new ClassloaderResourceBundleLoader();

    ResourceBundleLoaderFactory.addLoader(
        classloaderResourceBundleLoader.getType(), classloaderResourceBundleLoader);
    recordTypeFormatTypeHandler =
        new RecordTypeTokenHandler(
            new AppLocalisationResourceBundle(new LocalizedResourceBundle(), localisationConfig));
  }

  @Test
  public void testFormatType() {
    Assert.assertEquals(Token.RECORD_TYPE, recordTypeFormatTypeHandler.getTokenType());
  }

  @Test
  public void testFormatWithFormatValue() {
    Assert.assertEquals(
        expected, recordTypeFormatTypeHandler.format(format, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithLowerCaseFormatValue() {
    Assert.assertEquals(
        "fr_CAinvoices",
        recordTypeFormatTypeHandler.format("%record%s", CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithFormatValueMissingInCanadaFrenchThrowsLKNE() {
    try {
      recordTypeFormatTypeHandler.format("%Record%", CANADA_FRENCH_LOCALE, "Bill");
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testFormatWithFormatValueWithEmptyLocale() {
    Assert.assertEquals(
        RESOURCE_BUNDLE.DEFAULT_LOCALE + "Invoice",
        recordTypeFormatTypeHandler.format("%Record%", "", recordType));
  }

  @Test
  public void testFormatWithFormatValueWithNullLocale() {
    Assert.assertEquals(
        RESOURCE_BUNDLE.DEFAULT_LOCALE + "Invoice",
        recordTypeFormatTypeHandler.format("%Record%", null, recordType));
  }

  @Test
  public void testFormatWithFormatValueWithInvalidKeyThrowsLKNE() {
    try {

      recordTypeFormatTypeHandler.format("%Record%_INVALID", null, "Statement");
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testFormatWithEmptyFormatValueMap() {
    Assert.assertEquals(
        "", recordTypeFormatTypeHandler.format("", CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithNullFormatValueMap() {
    Assert.assertEquals(
        null, recordTypeFormatTypeHandler.format(null, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormatWithFormatValueMapWithNullRecordType() {
    Assert.assertEquals(
        format, recordTypeFormatTypeHandler.format(format, CANADA_FRENCH_LOCALE, null));
  }
}
