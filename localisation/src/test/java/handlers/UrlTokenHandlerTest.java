package handlers;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.ExternalServiceMapping;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.Token;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.UrlTokenHandler;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class UrlTokenHandlerTest {

  private UrlTokenHandler urlTokenHandler;
  private final String CANADA_FRENCH_LOCALE = "fr_CA";
  private final String token = "taskManagerUrl";
  private final String taskManagerUrl = "https://app.qal.qbo.intuit.com/app/taskmanager";

  @Before
  public void init() {
    ExternalServiceMapping externalServiceMapping = new ExternalServiceMapping();
    externalServiceMapping.setUrls(Map.of(token, taskManagerUrl));
    urlTokenHandler = new UrlTokenHandler(externalServiceMapping);
  }

  @Test
  public void testTokenType() {
    Assert.assertEquals(Token.URL_TYPE, urlTokenHandler.getTokenType());
  }

  @Test
  public void testFormatWithExternalServiceTaskManagerUrl() {
    Assert.assertEquals(taskManagerUrl, urlTokenHandler.format(token, CANADA_FRENCH_LOCALE, ""));
  }

  @Test
  public void testFormatWithInvalidToken() {
    Assert.assertEquals(
        token + "Invalid", urlTokenHandler.format(token + "Invalid", CANADA_FRENCH_LOCALE, ""));
  }
}
