package service;

import static java.util.ResourceBundle.Control.FORMAT_PROPERTIES;
import static java.util.ResourceBundle.Control.TTL_NO_EXPIRATION_CONTROL;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.LocalizedResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationUtility;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.ResourceBundle;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class LocalizedResourceBundleTest {
  LocalizedResourceBundle localizedResourceBundle;

  ClassloaderResourceBundleLoader classloaderResourceBundleLoader;

  private final String BASE_PATH = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
  private final Locale CANADA_FRENCH_LOCALE = new Locale("fr", "ca");

  @Before
  public void init() {
    classloaderResourceBundleLoader = new ClassloaderResourceBundleLoader();
    localizedResourceBundle = new LocalizedResourceBundle();
    ResourceBundleLoaderFactory.addLoader(
        ResourceBundleLoaderType.CLASSLOADER_BUNDLE_TYPE, classloaderResourceBundleLoader);
  }

  @Test
  public void testNeedReloadsForClassloaderResourceBundleLoader() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle(BASE_PATH, CANADA_FRENCH_LOCALE);
    Assert.assertFalse(
        localizedResourceBundle.needsReload(
            BASE_PATH,
            CANADA_FRENCH_LOCALE,
            FORMAT_PROPERTIES.get(0),
            getClass().getClassLoader(),
            resourceBundle,
            0L));
  }

  @Test
  public void testNeedReloadsWithInvalidBundleThrowsInputInvalidError() {
    try {
      localizedResourceBundle.needsReload(
          BASE_PATH,
          CANADA_FRENCH_LOCALE,
          FORMAT_PROPERTIES.get(0),
          getClass().getClassLoader(),
          null,
          0L);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testNeedReloadsWithInvalidFormatProperties() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle(BASE_PATH, CANADA_FRENCH_LOCALE);
    Assert.assertFalse(
        localizedResourceBundle.needsReload(
            BASE_PATH,
            CANADA_FRENCH_LOCALE,
            "java.class",
            getClass().getClassLoader(),
            resourceBundle,
            0L));
  }

  @Test
  public void testTimeToLiveForClassloaderResourceBundleLoader() {
    Assert.assertTrue(
        localizedResourceBundle.getTimeToLive(BASE_PATH, CANADA_FRENCH_LOCALE)
            == TTL_NO_EXPIRATION_CONTROL);
  }

  @Test
  public void testTimeToLiveWithInvalidLocaleThrowsInputInvalidError() {
    try {
      localizedResourceBundle.getTimeToLive(BASE_PATH, null);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testTimeToLiveWithEmptyBundlePathThrowsInputInvalidError() {
    try {
      localizedResourceBundle.getTimeToLive("", CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testTimeToLiveWithNullBundlePathThrowsInputInvalidError() {
    try {
      localizedResourceBundle.getTimeToLive(null, CANADA_FRENCH_LOCALE);

    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testFormat() {
    Assert.assertTrue(FORMAT_PROPERTIES.equals(localizedResourceBundle.getFormats(BASE_PATH)));
  }

  @Test
  public void testFallbackLocale() {
    Locale defaultLocale = LocalisationUtility.getLocale(RESOURCE_BUNDLE.DEFAULT_LOCALE);
    Assert.assertTrue(
        defaultLocale.equals(CANADA_FRENCH_LOCALE)
            ? true
            : defaultLocale.equals(
                localizedResourceBundle.getFallbackLocale(BASE_PATH, CANADA_FRENCH_LOCALE)));
  }

  @Test
  public void testCandidateLocales() {
    Assert.assertTrue(
        Collections.singletonList(CANADA_FRENCH_LOCALE)
            .equals(localizedResourceBundle.getCandidateLocales(BASE_PATH, CANADA_FRENCH_LOCALE)));
  }

  @Test
  public void testWASResourceBundleLocatorForClassloaderBasePath() {
    Assert.assertTrue(
        classloaderResourceBundleLoader.equals(
            localizedResourceBundle.getWASResourceBundleLocator(BASE_PATH)));
  }

  @Test
  public void testWASResourceBundleLocatorWithFilePrefix() {
    Assert.assertFalse(
        classloaderResourceBundleLoader.equals(
            localizedResourceBundle.getWASResourceBundleLocator("file://" + BASE_PATH)));
  }

  @Test
  public void testWASResourceBundleLocatorWithEmptyBasePath() {
    Assert.assertTrue(
        classloaderResourceBundleLoader.equals(
            localizedResourceBundle.getWASResourceBundleLocator("")));
  }

  @Test
  public void testBundleForClassloaderWithValidBasePathAndCanadaFrenchLocale() {
    Assert.assertNotNull(
        localizedResourceBundle.newBundle(
            BASE_PATH,
            CANADA_FRENCH_LOCALE,
            FORMAT_PROPERTIES.get(0),
            getClass().getClassLoader(),
            false));
  }

  @Test
  public void testBundleWithInvalidFormatProperties() {
    Assert.assertNull(
        localizedResourceBundle.newBundle(
            BASE_PATH, CANADA_FRENCH_LOCALE, "java.class", getClass().getClassLoader(), false));
  }

  @Test
  public void testBundleWithNullLocale() {
    Assert.assertNull(
        localizedResourceBundle.newBundle(
            BASE_PATH, null, FORMAT_PROPERTIES.get(0), getClass().getClassLoader(), false));
  }
}
