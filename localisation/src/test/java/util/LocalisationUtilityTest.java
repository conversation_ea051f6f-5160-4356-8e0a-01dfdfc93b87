package util;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationUtility;
import java.util.Locale;
import org.junit.Assert;
import org.junit.Test;

public class LocalisationUtilityTest {

  @Test
  public void testFolderNameForCandaFrenchLocale() {
    String folderName = LocalisationUtility.getFolderName(Locale.CANADA_FRENCH);
    Assert.assertTrue("fr_CA".equals(folderName));
  }

  @Test
  public void testFolderNameNullLocale() {
    String folderName = LocalisationUtility.getFolderName(null);
    Assert.assertTrue("".equals(folderName));
  }

  @Test
  public void testFolderNameEmptyLocale() {
    String folderName = LocalisationUtility.getFolderName(new Locale("", "", ""));
    Assert.assertTrue("".equals(folderName));
  }

  @Test
  public void testFolderNameWithLanguageRegionAndVariant() {
    Locale locale = new Locale("en", "US", "global");
    String folderName = LocalisationUtility.getFolderName(locale);
    Assert.assertTrue("en_US_global".equals(folderName));
  }

  @Test
  public void testLocaleFromLocaleString() {
    Locale locale = new Locale("en", "US", "global");
    Locale localeFromString = LocalisationUtility.getLocale("en_US_global");
    Assert.assertTrue(locale.equals(localeFromString));
  }

  @Test
  public void testLocaleFromWithEmptyString() {
    Locale localeFromString = LocalisationUtility.getLocale("");
    Assert.assertTrue(Locale.ROOT.equals(localeFromString));
  }
}
