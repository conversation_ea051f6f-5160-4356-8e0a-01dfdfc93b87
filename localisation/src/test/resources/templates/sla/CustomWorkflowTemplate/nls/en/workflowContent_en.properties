# These files need to be saved in UTF-8 format (UTF-8 without BOM). ï¿½ Intuit Inc.
do_not_remove_this_key=
# Account dialog
invoice.reminder.sendPushNotification.Subject=An Invoice needs your attention
bill.notification.sendNotification.Subject=Payment remittance advice
Invoice=enInvoice
invoices=eninvoices
Bill=enBill
bills=enbills
invoice.approval.sendCompanyEmail.Message=enHi,\n\n{recordType|%Record%} {helpVariableRecordType|[[%Record% Number]]} is pending approval. Please approve it at the earliest using this task manager link -{url|taskManagerUrl}.\n\nNote that {recordType|%record%s} that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n{[[CompanyName]]}
bill.approval.sendCompanyEmail.Message=enHi,\n\n{recordType|%Record%} {helpVariableRecordType|[[%Record% Number]]} is pending approval. Please approve it at the earliest using this task manager link -{url|taskManagerUrl}.\n\nNote that {recordType|%record%s} that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n{[[CompanyName]]}
token.parameter.key=enHi i am testing token parameters for invoice {recordType|%Record%} {recordType|%record%s} {helpVariableRecordType|[[%Record% Number]]} {url|taskManagerUrl}