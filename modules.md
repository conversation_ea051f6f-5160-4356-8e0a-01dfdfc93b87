#### Modules

* ##### app
  The workflow automation application, v4 providers, run time APIs, template setup APIs. 
  
* ##### core
   Core module consisting of template/definition processing for read/write.Runtime action and trigger handlers.  
   
* #### [common](common/README.md "common readme")
  Provides components for cross cutting functionalities like Http client, async execution,
  aop for performance logging, validation etc.
  
* #### dataaccess
   JPA layer for accessing Entities for definition and runtime.
   
* #### entity
  Entities for BPMN mapping, pojos for REST payloads and response

* #### worker
  Integration of external worker client of camunda action execution interaction.
