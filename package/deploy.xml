<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
	<id>deploy</id>
	<formats>
		<format>zip</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <outputDirectory></outputDirectory>
            <directory>target</directory>
            <includes>
                <include>*.rpm</include>
            </includes>
        </fileSet>
    </fileSets>
	<files>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/appspec.yml</source>
            <destName>appspec.yml</destName>
            <filtered>true</filtered>
        </file>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/scripts/after-install.sh</source>
            <destName>scripts/after-install.sh</destName>
            <filtered>true</filtered>
        </file>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/scripts/application-start.sh</source>
            <destName>scripts/application-start.sh</destName>
            <filtered>true</filtered>
        </file>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/scripts/application-stop.sh</source>
            <destName>scripts/application-stop.sh</destName>
            <filtered>true</filtered>
        </file>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/scripts/before-install.sh</source>
            <destName>scripts/before-install.sh</destName>
            <filtered>true</filtered>
        </file>
        <file>
            <outputDirectory></outputDirectory>
            <source>../ops/codedeploy/scripts/validation-service.sh</source>
            <destName>scripts/validation-service.sh</destName>
            <filtered>true</filtered>
        </file>
    </files>
</assembly>
