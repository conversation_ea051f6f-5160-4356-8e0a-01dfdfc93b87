#!/bin/sh

if [ -f /etc/secrets/application.properties ]; then
  JAVA_OPTS="${JAVA_OPTS} -Dspring.config.location=/etc/secrets/application.properties"
fi

if [ -n "${CHAOS_MONKEY}" ] && [ "${CHAOS_MONKEY}" = "true" ] && [ -n "${APP_ENV}" ]; then
  JAVA_OPTS="${JAVA_OPTS} -Dspring.profiles.active=${APP_ENV},chaos-monkey"
elif [ -n "${APP_ENV}" ]; then
  JAVA_OPTS="${JAVA_OPTS} -Dspring.profiles.active=${APP_ENV}"
fi

JAVA_OPTS="${JAVA_OPTS}
  -XX:+UseG1GC -XX:+UseStringDeduplication \
  -XX:MaxRAMFraction=2 \
  -XshowSettings:vm"

# use app dir for tmp dir
JAVA_OPTS="${JAVA_OPTS} -Djava.io.tmpdir=/app/tmp"

# Is contrast enabled, yes or no
contrastassess_enabled=yes

# ENV for contrast assessment
contrastassess_env=qal
contrastassess_jar="/app/contrast/javaagent/contrast.jar"
if [ "${contrastassess_enabled}" = "yes" ] && [ "${APP_ENV}" = "${contrastassess_env}" ]; then
  JAVA_OPTS="${JAVA_OPTS} -javaagent:${contrastassess_jar}"
  JAVA_OPTS="${JAVA_OPTS} -Dcontrast.dir=/app/contrast/javaagent"
  JAVA_OPTS="${JAVA_OPTS} -Dcontrast.application.code=7065039507767760447"
  JAVA_OPTS="${JAVA_OPTS} -Dcontrast.application.name=appintgwkflw-wkflautomate-wkflatmnsvc"
  JAVA_OPTS="${JAVA_OPTS} -Dcontrast.inspect.allclasses=false -Dcontrast.process.codesources=false"
  JAVA_OPTS="${JAVA_OPTS} -Dcontrast.inventory.libraries=false"
fi

# Is appdynamics enabled, yes or no
appdynamics_enabled=yes

appdynamics_jar="/app/appdynamics/javaagent.jar"
if [ "${appdynamics_enabled}" = "yes" ] && [ -r ${appdynamics_jar} ] && [ -f /etc/secrets/appd-account-access-key ]; then
    export APPDYNAMICS_CONTROLLER_PORT=443
    export APPDYNAMICS_CONTROLLER_SSL_ENABLED=true

    export APPDYNAMICS_AGENT_ACCOUNT_ACCESS_KEY=`cat /etc/secrets/appd-account-access-key`

    JAVA_OPTS="$JAVA_OPTS -javaagent:${appdynamics_jar}"
    JAVA_OPTS="$JAVA_OPTS -Dappdynamics.agent.applicationName=${L1}-${L2}-${APP_NAME}-${APP_ENV}"
    JAVA_OPTS="$JAVA_OPTS -Dappdynamics.agent.tierName=${APPDYNAMICS_AGENT_TIER_NAME}"
    JAVA_OPTS="$JAVA_OPTS -Dappdynamics.agent.nodeName=${APPDYNAMICS_AGENT_TIER_NAME}_${HOSTNAME}"
fi

echo "MESH_ENABLED: $MESH_ENABLED"
if [ "$MESH_ENABLED" == "true" ]; then
    until (echo >/dev/tcp/localhost/$MESH_SIDECAR_PORT) &>/dev/null ; do echo Waiting for Sidecar; sleep 3 ; done ; echo Sidecar available;
fi

exec java $JAVA_OPTS -jar /app/was-app.jar
