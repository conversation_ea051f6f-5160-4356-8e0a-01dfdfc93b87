version: "3"
services:
  camunda_db:
    container_name: camunda_db
    image: 'postgres'
    ports:
      - '5432:5432'
    environment:
      POSTGRES_PASSWORD: Intuit01
      POSTGRES_DB: camunda
      POSTGRES_USER: sas
    volumes:
      - '${WORKDIR}/database:/var/lib/postgresql'
    deploy:
      resources:
        limits:
          memory: 500M
        reservations:
          memory: 300M
  wasapp:
    container_name: wasapp
    image: docker.artifactory.a.intuit.com/appintgwkflw/wkflautomate/wkflatmnsvc/service/wkflatmnsvc:${WAS_IMAGE_TAG}
    ports:
      - 8443:8443
    deploy:
      resources:
        limits:
          memory: 1400M
        reservations:
          memory: 700M
    environment:
      - APP_ENV=default
      - JAVA_OPTS=-Djsk.spring.config.idps.connection.api_secret_key=/app/idps_config/key_v2-3fadea6d5cea5.pem
        -Devent.idpsconfig.api_secret_key=/app/idps_config/key_v2-e3b373bd2284e.pem -Ddb.urlPrefix=********************************* ${JAVA_OPTS}

  camundaapp:
    container_name: camundaapp
    image: docker.artifactory.a.intuit.com/appintgwkflw/wkflautomate/camundaservice/service/camundaservice:${CAMUNDA_IMAGE_TAG}
    ports:
      - 8080:8080
    deploy:
      resources:
        limits:
          memory: 1400M
        reservations:
          memory: 700M
    environment:
      - APP_ENV=default
      - JAVA_OPTS=-Djsk.spring.config.idps.connection.api_secret_key=/app/idps_config/key_v2-f1e7ef16e4c97.pem -Devent.idpsconfig.api_secret_key=/app/idps_config/key_v2-e3b373bd2284e.pem
        -DdbHostPrefix=*********************************
