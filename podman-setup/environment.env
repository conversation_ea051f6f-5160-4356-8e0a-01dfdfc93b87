# Docker Image tag of WAS
WAS_IMAGE_TAG="develop-c4a81d7"

# Docker Image tag of Camunda
CAMUNDA_IMAGE_TAG="develop-ec62efe"

# If you need to run camunda in local, give this value as 'local'. Default value is camunda qal, for any other environment please provide the complete url
WORKFLOW_ENGINE_CORE="local"

# Change this value to give the branch name of updated config
CONFIG_BRANCH="master"

# Topic name or worker id of a new worker that needs to be created
EXTERNAL_TASK_TOPIC_NAME="worker-topic-name"
