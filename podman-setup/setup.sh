#!/bin/bash

#Create work dir and cd to the directory
export WORKDIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "Project folder: $WORKDIR"
cd $WORKDIR

# execute env file, so that variables are loaded.
set -a
source environment.env

export JAVA_OPTS=""

echo "WAS Image : ${WAS_IMAGE_TAG}"
echo "Camunda Image : ${CAMUNDA_IMAGE_TAG}"


#Create the container for database
mkdir -p database;
echo "SETTING UP DB..."
podman-compose up -d camunda_db


#Check if new config branch is provided in env variables
if [ -n "${CONFIG_BRANCH}" ]; then
  JAVA_OPTS="${JAVA_OPTS} -Dspring.cloud.config.label=${CONFIG_BRANCH}"
fi;

#Check if new worker config is provided in environment.env variables
if [ -n "${EXTERNAL_TASK_TOPIC_NAME}" ]; then
  JAVA_OPTS="${JAVA_OPTS} -DexternalTask.workers.test.topicName=${EXTERNAL_TASK_TOPIC_NAME}"
  JAVA_OPTS="${JAVA_OPTS} -DexternalTask.workers.test.workerId=${EXTERNAL_TASK_TOPIC_NAME}"
fi;

#CHeck if camunda server needs to be run or not
if [ -n "${WORKFLOW_ENGINE_CORE}" ] ; then
  if [ "${WORKFLOW_ENGINE_CORE}" = "local" ]; then
    echo "SETTING UP CAMUNDA CONTAINER..."
    JAVA_OPTS="${JAVA_OPTS} -Dworkflowcore.engine.host=http://camundaapp:8080"
    docker-compose up -d camundaapp
  else
    JAVA_OPTS="${JAVA_OPTS} -Dworkflowcore.engine.host=${WORKFLOW_ENGINE_CORE}"
  fi;
fi;

echo "JAVA OPTIONS USED: ${JAVA_OPTS}"
echo "SETTING UP WAS CONTAINER..."
podman-compose up -d wasapp