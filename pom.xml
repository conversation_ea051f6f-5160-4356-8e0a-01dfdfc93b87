<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


    <modelVersion>4.0.0</modelVersion>
    <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
    <artifactId>workflow-automation-service-aggregator</artifactId>
<!--    mvn versions:set -DnewVersion=1.0.0-->
    <version>1.1.20</version>
    <packaging>pom</packaging>

    <modules>
        <module>aop</module>
        <module>entity</module>
        <module>common</module>
        <module>graphql-client</module>
        <module>dataaccess</module>
        <module>core</module>
        <module>worker</module>
        <module>app</module>
        <module>package</module>
        <module>provideradapter</module>
        <module>report</module>
        <module>batch</module>
        <module>eventconsumer</module>
        <module>eventpublisher</module>
        <module>telemetry</module>
        <module>integrations</module>
        <module>localisation</module>
        <module>connector</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <maven.compiler.target>11</maven.compiler.target>
        <maven.compiler.source>11</maven.compiler.source>
        <central.repo>https://nexus.intuit.com/nexus/content/groups/public</central.repo>
        <fallback.repo>http://nexus.intuit.net/nexus/content/groups/public</fallback.repo>
        <jacoco-maven-plugin.version>0.8.5</jacoco-maven-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-surefire-report-plugin.version>2.19.1</maven-surefire-report-plugin.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <maven-springboot-plugin.version>2.6.3</maven-springboot-plugin.version>
        <project.testresult.directory>${project.build.directory}/test-results</project.testresult.directory>
        <workflows-bom.version>1.1.67</workflows-bom.version>
        <!-- IXP -->
        <ixp-java-sdk>7.0.8</ixp-java-sdk>
        <workflows-resources.version>1.0.76</workflows-resources.version>
        <eventbus.kafka.version>1.8.10</eventbus.kafka.version>
        <authn.sdk.version>1.0.0.46</authn.sdk.version>
        <!--Added for Sonar Exclusions-->
        <maven-compiler-plugin>3.8.1</maven-compiler-plugin>
        <sonar-maven-plugin>3.6.0.1398</sonar-maven-plugin>
        <sonar.coverage.exclusions>
            **/common/exception/**,**/entity/**,**/dataaccess/**,**/telemetry/**,**/config/**
        </sonar.coverage.exclusions>
        <postgresql.version>42.7.2</postgresql.version>
        <org.json.version>20231013</org.json.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <lombok.version>1.18.22</lombok.version>
        <numaflow.version>0.8.0</numaflow.version>
        <gc-event-handler.version>3.2.10</gc-event-handler.version>
        <scheduling-iedm-schema.version>1.2.0</scheduling-iedm-schema.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin}</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin}</version>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <executions>
                    <execution>
                        <id>default-deploy</id>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>pre-unit-tests</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <!-- Sets the name of the property containing the settings for JaCoCo
                                runtime agent. -->
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>

                    <!-- Ensures that the code coverage report for unit tests is created
                        after unit tests have been run -->
                    <execution>
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.00</minimum>
                                        </limit>
                                        <limit>
                                            <counter>BRANCH</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.00</minimum>
                                        </limit>
                                        <limit>
                                            <counter>CLASS</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.00</minimum>
                                        </limit>
                                        <limit>
                                            <counter>METHOD</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.00</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/Test*.java</include>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                        <include>**/*TestCase.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/lib/*</exclude>
                        <excludedGroups>componentTest</excludedGroups>
                    </excludes>
                    <useFile>true</useFile>
                    <argLine>${surefireArgLine}</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!--  JUnit 5’s vintage engine has been removed from spring-boot-starter-test.
        The vintage engine allows tests written with JUnit 4 to be run by JUnit 5.
        If you do not want to migrate your tests to JUnit 5 and wish to continue using JUnit 4,
         add a dependency on the Vintage Engine-->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${org.json.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
                <artifactId>workflows-bom</artifactId>
                <version>${workflows-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>central-repo</id>
            <url>${central.repo}</url>
            <name>Intuit Nexus Cache</name>
        </repository>
        <repository>
            <id>fallback-repo</id>
            <url>${fallback.repo}</url>
            <name>Intuit Nexus Home</name>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <uniqueVersion>false</uniqueVersion>
            <id>scm.dev.snap.repo</id>
            <name>SNAPSHOT REPO</name>
            <url>https://nexus.intuit.com/nexus/content/repositories/Intuit.CTO-Snapshots</url>
            <layout>default</layout>
        </snapshotRepository>
        <repository>
            <id>scm.int.rel.repo</id>
            <name>RELEASE REPO</name>
            <url>http://nexus.intuit.net/nexus/content/repositories/Intuit.CTO-Releases</url>
            <layout>default</layout>
        </repository>
    </distributionManagement>
</project>
