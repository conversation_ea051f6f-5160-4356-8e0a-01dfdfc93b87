package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter;

import com.intuit.appintgwkflw.wkflautomate.was.common.tags.Tag;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;

import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EventHeaders {

  private String entityChangeType;
  private RecordType entityType;
  private String workflow;
  private String entityId;
  // This is specific to calls from AppConnect
  private String providerWorkflowId;
  private Tag tags;
  /**
   * Flag to control process getting blocked via marking process in error state.Default to true so
   * that it is backward compatible.Caller can override this behavior if required and process will
   * remain active.
   *
   * <p>Existing Implementation: If signal of a process gets missed after retrying process gets
   * marked as error in DB to avoid any timer flows to kick in and prevent process from going into
   * any other loop.
   */
  private boolean blockProcessOnSignalFailure = true;
  // Set the businessKey of process
  private String businessKey;

  // Target trigger API
  private TriggerTargetAPI targetAPI;

  /**
   * Trigger the process using the definitionKey. This field is added to support 1-1 mapping b/w workflow and trigger event.
   * This is an alternative to providerWorkflowId - sent via appconnect call.
   */
  private String definitionKey;
  private boolean onDemandApproval;

  /**
    * Scope of the event: The application can identify the scope of the event based on the scope.
    * Currently, it includes a single scope, TEST.
    * The reason for having a scope is to identify the source of the event.
    * If the source is from overwatch, we should pass the scope as TEST.
    * Because in overwatch automations, we have different templates deployed in camunda by using TemplateMetaData.getForkForOverwtach() as true.
    * So, we need to identify the source of the event.
    *
    * The reason for having 2 templates for same workflow is to have different configurations for the same workflow.
    * The topic in overwatch automation is different from the topic in normal automation.
    * The reason for having different topics is to have different configurations for the same workflow can be found under java doc of TemplateMetaData.getForkForOverwtach()
    *
    * Make sure we have a test overwatch template deployed in camunda using forkForOverwatch as true before sending the event with scope as TEST.
    * Or else the test flow will be breaked.
    *
    * For now use this scope only for overwatch purposes and use for only approval and reminder workflows.
   */
  private Scope scope = null;
}
