package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enum representing different scopes within the application.
 * <p>
 * Currently, it includes a single scope, TEST.
 * We will be using TEST scope for overwatch automations
 * </p>
 */
@Getter
@RequiredArgsConstructor
public enum Scope {

    TEST("test");

    private final String scope;

    public static Scope fromType(String scopeType) {
        for (Scope scope : Scope.values()) {
            if (scope.getScope().equalsIgnoreCase(scopeType)) {
                return scope;
            }
        }
        return null;
    }

}
