package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_INPUT;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.EVENT_HEADERS;
import static java.util.Objects.isNull;
import static org.apache.commons.lang.StringUtils.isEmpty;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;

/** <AUTHOR> */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TransactionEntityHelper {

  /**
   * @param variableMap input trigger payload
   * @return {@link EventHeaders} detail
   */
  public static EventHeaders getEventHeaders(Map<String, Object> variableMap) {
    WorkflowVerfiy.verify(MapUtils.isEmpty(variableMap), INVALID_INPUT);
    EventHeaders eventHeaders =
        ObjectConverter.convertObject(
            variableMap.getOrDefault(EVENT_HEADERS, null), EventHeaders.class);
    eventHeaders.setOnDemandApproval((Boolean) variableMap.getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, false));

    WorkflowVerfiy.verify(
        isNull(eventHeaders)
            || isEmpty(eventHeaders.getWorkflow())
            || isNull(eventHeaders.getEntityType())
            || isNull(eventHeaders.getEntityChangeType()),
        INVALID_INPUT);

    return eventHeaders;
  }
}
