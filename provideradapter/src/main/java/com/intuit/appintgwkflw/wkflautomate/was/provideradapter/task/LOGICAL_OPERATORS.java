package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

@Getter
public enum LOGICAL_OPERATORS {

  AND("&&"), OR("||");

  private String denotions;
  private static Map<String, LOGICAL_OPERATORS> operatorMap = getOperator();

  private LOGICAL_OPERATORS(String denotions) {
    this.denotions = denotions;
  }

  public static LOGICAL_OPERATORS fromDenotions(String denotion) {
    return operatorMap.getOrDefault(denotion, null);
  }

  public static Map<String, LOGICAL_OPERATORS> getOperator() {
    Map<String, LOGICAL_OPERATORS> operatorMap = new HashMap<>();
    for (LOGICAL_OPERATORS operator : LOGICAL_OPERATORS.values()) {
      operatorMap.put(operator.getDenotions(), operator);
    }
    return operatorMap;
  }

}