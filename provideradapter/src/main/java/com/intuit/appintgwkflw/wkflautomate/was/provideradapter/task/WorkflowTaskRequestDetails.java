package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 *  Request POJO for Get Task Workflow (wkfl GraphQL Custom Operation).
 *
 */

@Data
@Builder
public class WorkflowTaskRequestDetails {

	private String recordId; 
	private List<TaskType> taskTypes; 
	private List<String> workflowNames;
	private int offset;
	private int limit;
	
}