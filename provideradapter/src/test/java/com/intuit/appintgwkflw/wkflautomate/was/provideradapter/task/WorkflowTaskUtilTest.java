package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.v4.Query;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.Expression;
import com.intuit.v4.query.visitable.FieldExpression;
import com.intuit.v4.query.visitable.FilterExpression;

public class WorkflowTaskUtilTest {

  /**
   * by: recordId = 'abc' filterBy: "workflowName in ('abc','def') and type in ('HUMAN_TASK')"
   */
  @Test
  public void test_in_workflowName_taskTypeEnum() {

    List<Expression> expressions = new ArrayList<>();

    Expression workflowNameExpression = new FilterExpression();
    workflowNameExpression.setOp("in");
    workflowNameExpression.set("property", "workflowName");
    List<String> workflowNames = new ArrayList<String>();
    workflowNames.add("abc");
    workflowNames.add("def");
    workflowNameExpression.set("args", workflowNames);
    expressions.add(workflowNameExpression);

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<String> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK.name());
    taskTypeExpression.set("args", taskTypes);
    expressions.add(taskTypeExpression);

    Expression whereOp = new FilterExpression();
    whereOp.setOp("&&");
    whereOp.set("args", expressions);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(whereOp);

    List<TaskType> taskTypeEnums = new ArrayList<>();
    taskTypeEnums.add(TaskType.HUMAN_TASK);
    
    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypeEnums, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }


  /**
   * by: recordId = 'abc' filterBy: "workflowName in ('abc','def') and type = 'HUMAN_TASK'"
   */
  @Test
  public void test_in_workflowName_equals_taskTypeEnum() {

    List<Expression> expressions = new ArrayList<>();

    Expression workflowNameExpression = new FilterExpression();
    workflowNameExpression.setOp("in");
    workflowNameExpression.set("property", "workflowName");
    List<String> workflowNames = new ArrayList<String>();
    workflowNames.add("abc");
    workflowNames.add("def");
    workflowNameExpression.set("args", workflowNames);
    expressions.add(workflowNameExpression);

    Expression taskTypeExpression = new FieldExpression();
    taskTypeExpression.setOp("=");
    taskTypeExpression.set("property", "type");
    taskTypeExpression.set("args", TaskType.HUMAN_TASK);
    expressions.add(taskTypeExpression);

    Expression whereOp = new FilterExpression();
    whereOp.setOp("&&");
    whereOp.set("args", expressions);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(whereOp);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }

  /**
   * by: recordId = 'abc' filterBy: "workflowName in ('abc','def')"
   */
  @Test
  public void test_in_workflowName_no_taskTypeEnum() {

    Expression workflowNameExpression = new FilterExpression();
    workflowNameExpression.setOp("in");
    workflowNameExpression.set("property", "workflowName");
    List<String> workflowNames = new ArrayList<String>();
    workflowNames.add("abc");
    workflowNames.add("def");
    workflowNameExpression.set("args", workflowNames);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(workflowNameExpression);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }


  /**
   * by: recordId = 'abc' filterBy: "workflowName = 'abc'"
   */
  @Test
  public void test_eq_workflowName_no_taskTypeEnum() {

    Expression workflowNameExpression = new FieldExpression();
    workflowNameExpression.setOp("=");
    workflowNameExpression.set("property", "workflowName");
    workflowNameExpression.set("args", "abc");

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(workflowNameExpression);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));

    List<String> workflowNames = new ArrayList<String>();
    workflowNames.add("abc");
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("WorkflowNames not empty.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }



  /**
   * by: recordId = 'abc' filterBy: "workflowName = 'abc' and type = 'HUMAN_TASK'"
   */
  @Test
  public void test_eq_workflowName_equals_taskTypeEnum() {

    List<Expression> expressions = new ArrayList<>();

    Expression workflowNameExpression = new FieldExpression();
    workflowNameExpression.setOp("eq");
    workflowNameExpression.set("property", "workflowName");
    workflowNameExpression.set("args", "abc");
    expressions.add(workflowNameExpression);

    Expression taskTypeExpression = new FieldExpression();
    taskTypeExpression.setOp("=");
    taskTypeExpression.set("property", "type");
    taskTypeExpression.set("args", TaskType.HUMAN_TASK);
    expressions.add(taskTypeExpression);

    Expression whereOp = new FilterExpression();
    whereOp.setOp("&&");
    whereOp.set("args", expressions);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(whereOp);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    List<String> workflowNames = new ArrayList<>();
    workflowNames.add("abc");

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }

  /**
   * by: recordId = 'abc' filterBy: "workflowName = 'abc' and type in ('HUMAN_TASK')"
   */
  @Test
  public void test_eq_workflowName_in_taskTypeEnum() {

    List<Expression> expressions = new ArrayList<>();

    Expression workflowNameExpression = new FieldExpression();
    workflowNameExpression.setOp("in");
    workflowNameExpression.set("property", "workflowName");
    workflowNameExpression.set("args", "abc");
    expressions.add(workflowNameExpression);

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<String> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK.name());
    taskTypeExpression.set("args", taskTypes);
    expressions.add(taskTypeExpression);

    Expression whereOp = new FilterExpression();
    whereOp.setOp("&&");
    whereOp.set("args", expressions);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(whereOp);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));

    List<String> workflowNames = new ArrayList<>();
    workflowNames.add("abc");

    List<TaskType> taskTypeEnums = new ArrayList<>();
    taskTypeEnums.add(TaskType.HUMAN_TASK);
    
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypeEnums, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", workflowNames, req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }


  /**
   * by: recordId = 'abc' filterBy: "type in ('HUMAN_TASK')"
   */
  @Test
  public void test_no_workflowName_in_taskTypeEnum() {

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<String> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK.name());
    taskTypeExpression.set("args", taskTypes);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(taskTypeExpression);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    
    List<TaskType> taskTypeEnums = new ArrayList<>();
    taskTypeEnums.add(TaskType.HUMAN_TASK);
    
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypeEnums, req.getTaskTypes());
    Assert.assertTrue("workflowNames not empty.", CollectionUtils.isEmpty(req.getWorkflowNames()));
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }

  /**
   * by: recordId = 'abc' filterBy: "type = 'HUMAN_TASK'"
   */
  @Test
  public void test_no_workflowName_eq_taskTypeEnum() {

    Expression taskTypeExpression = new FieldExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);
    taskTypeExpression.set("args", TaskType.HUMAN_TASK);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(taskTypeExpression);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertTrue("workflowNames not empty.", CollectionUtils.isEmpty(req.getWorkflowNames()));
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }


  /**
   * by: recordId = 'abc' filterBy: ''
   */
  @Test
  public void test_no_workflowName_no_taskTypeEnum() {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", Collections.emptyList(),
        req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 0, req.getOffset());
  }

  /**
   * by: recordId = 'abc' filterBy: '' offset: 40 limit: 40
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_limit_offset() {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setLimit(40);
    preparedQuery.setOffset("40");

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }

  /**
   * by: recordId = 'abc' filterBy: '' offset: 40
   */
  @Test
  public void test_no_limit_offset() {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setOffset("40");

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskRequestDetails req = WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
    Assert.assertEquals("RecordId not matching.", "record1", req.getRecordId());
    Assert.assertEquals("TaskTypes not matching.", taskTypes, req.getTaskTypes());
    Assert.assertEquals("workflowNames not matching.", Collections.emptyList(),
        req.getWorkflowNames());
    Assert.assertEquals("Limit not matching.", 25, req.getLimit());
    Assert.assertEquals("Offset not matching.", 40, req.getOffset());
  }

  /**
   * by: recordId = 'abc' filterBy: '' limit: 0
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_0_limit_no_offset() {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setLimit(0);

    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }

  /**
   * by: '' filterBy: ''
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_no_recordId() {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }

  /**
   * by: recordId = 'abc' filterBy: "workflowName in ('abc','def') || type in ('HUMAN_TASK')"
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_or_operator() {

    List<Expression> expressions = new ArrayList<>();

    Expression workflowNameExpression = new FilterExpression();
    workflowNameExpression.setOp("in");
    workflowNameExpression.set("property", "workflowName");
    List<String> workflowNames = new ArrayList<String>();
    workflowNames.add("abc");
    workflowNames.add("def");
    workflowNameExpression.set("args", workflowNames);
    expressions.add(workflowNameExpression);

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.HUMAN_TASK);
    taskTypeExpression.set("args", taskTypes);
    expressions.add(taskTypeExpression);

    Expression whereOp = new FilterExpression();
    whereOp.setOp("||");
    whereOp.set("args", expressions);

    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(whereOp);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }


  /**
   * by: recordId = 'abc' filterBy: "type in ('SYSTEM_TASK')"
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_type_in_SystemTask() {

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "type");
    List<String> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.SYSTEM_TASK.name());
    taskTypeExpression.set("args", taskTypes);


    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(taskTypeExpression);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }


  /**
   * by: recordId = 'abc' filterBy: "abc in ('SYSTEM_TASK')"
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_filterNotSupported() {

    Expression taskTypeExpression = new FilterExpression();
    taskTypeExpression.setOp("in");
    taskTypeExpression.set("property", "abc");
    List<TaskType> taskTypes = new ArrayList<>();
    taskTypes.add(TaskType.SYSTEM_TASK);
    taskTypeExpression.set("args", taskTypes);


    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    Map<String, String> recordTaskDetailsMap = new HashMap<>();
    recordTaskDetailsMap.put("recordId", "record1");
    preparedQuery.setArgs(WorkflowConstants.BY, recordTaskDetailsMap);
    preparedQuery.setWhere(taskTypeExpression);

    Query query = new Query();
    query.setPreparedQuery(preparedQuery);
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }


  /**
   * ''
   */
  @Test(expected = WorkflowGeneralException.class)
  public void test_noPrepareQuery() {
    Query query = new Query();
    WorkflowTaskUtil.getTaskRequest(new QueryHelper(query));
  }

}
