CREATE SCHEMA was;
CREATE USER wasapp WITH LOGIN PASSWORD '####';
GRANT SELECT ON ALL TABLES IN SCHEMA public,was TO wasapp;
GRANT CONNECT ON DATABASE "PostgresqlDbE2e" TO wasapp;
GRANT USAGE ON SCHEMA was,public TO wasapp;
-- Run below scripts to add grants automatically on any new tables created within existing schema
-- Recommended approach, run before any tables, sequences are created on new schema
ALTER DEFAULT PRIVILEGES IN SCHEMA was GRANT usage, select on sequences TO wasapp;
ALTER DEFAULT PRIVILEGES IN SCHEMA was GRANT select, insert, update, delete ON TABLES TO wasapp;

-- Run below scripts to add grants on existing tables
GRANT SELECT , USAGE ON ALL SEQUENCES IN SCHEMA public,was to wasapp;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public,was TO wasapp;


