# Introduction

* Execute following command `sh get-db-creds.sh`
* This is a utility to generate the temporary read/write database credentials for the RDS.
* Generated password is copied to the clip-board which can be used to connect to the RDS.
* Password will auto expire after 15 minutes.
* References [generate-db-auth-token.html] (https://docs.aws.amazon.com/cli/latest/reference/rds/generate-db-auth-token.html) || [/using-iam-authentication-to-connect-with-pgadmin-amazon-aurora-postgresql-or-amazon-rds-for-postgresq] (https://aws.amazon.com/blogs/database/using-iam-authentication-to-connect-with-pgadmin-amazon-aurora-postgresql-or-amazon-rds-for-postgresql/)