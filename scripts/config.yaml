WAS:
  E2EB:
    WEST:
      clusterId: was-e2e-slb-usw2-cluster-23
      bastionhost: ec2-54-185-15-100.us-west-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      region: us-west-2
      port: 5432
      dbname: PostgresqlDbE2e
    EAST:
      clusterId: was-e2e-slb-use2-cluster-24
      bastionhost: ec2-3-140-122-121.us-east-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbE2e
  E2EA:
    WEST:
      clusterId:  was-e2e-sla-usw2-cluster-2
      bastionhost: ec2-52-25-116-154.us-west-2.compute.amazonaws.com
      region: us-west-2
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      port: 3306
      dbname: PostgresqlDbE2e
  PRODB:
    WEST:
      clusterId: was-rds-slb-usw2-cluster-4
      bastionhost: ec2-44-242-53-210.us-west-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_ro_user # TODO update with rw user post adoption
      region: us-west-2
      port: 5432
      dbname: PostgresqlDbPrd
    EAST:
      clusterId: was-rds-slb-use2-cluster-5
      bastionhost: ec2-3-132-31-187.us-east-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_ro_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbPrd
  PRODA:
    WEST:
      clusterId: was-rds-databasecluster-1k40y1d9okis6
      bastionhost: ec2-34-213-21-43.us-west-2.compute.amazonaws.com
      region: us-west-2
      iamreaduser: was_ro_user
      iamwriteuser: was_ro_user # TODO update with rw user post adoption
      port: 5432
      dbname: PostgresqlDbPrd
    EAST:
      clusterId: was-rds-slb-use2-cluster-1
      bastionhost: ec2-3-136-63-151.us-east-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_ro_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbPrd
  QALB:
    WEST:
      clusterId: ec2-54-185-15-100.us-west-2.compute.amazonaws.com
      bastionhost: ec2-3-140-122-121.us-east-2.compute.amazonaws.com
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      region: us-west-2
      port: 5432
      dbname: PostgresqlDbQal
    EAST:
      clusterId: was-qal-slb-use2-cluster-2
      bastionhost: utf8
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbQal
  QALA:
    WEST:
      clusterId:  was-qal-sla-usw2-cluster-2
      bastionhost: ec2-52-25-116-154.us-west-2.compute.amazonaws.com
      region: us-west-2
      iamreaduser: was_ro_user
      iamwriteuser: was_rw_user
      port: 3306
      dbname: PostgresqlDbQal
CAMUNDA:
  E2EB:
    WEST:
      clusterId: globaldb-camunda-e2e-slb-cluster-1
      bastionhost: ec2-54-185-15-100.us-west-2.compute.amazonaws.com
      iamreaduser: camunda_ro_user
      iamwriteuser: camunda_rw_user
      region: us-west-2
      port: 5432
      dbname: PostgresqlDbE2e
    EAST:
      clusterId: camunda-e2e-slb-use2-cluster
      bastionhost: ec2-3-140-122-121.us-east-2.compute.amazonaws.com
      iamreaduser: camunda_ro_user
      iamwriteuser: camund_rw_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbE2e
  E2EA:
    WEST:
      clusterId:  camunda-e2e-sla-usw2-cluster-2
      bastionhost: ec2-52-25-116-154.us-west-2.compute.amazonaws.com
      region: us-west-2
      iamreaduser: camunda_ro_user
      iamwriteuser: camunda_rw_user
      port: 5432
      dbname: PostgresqlDbE2e
  QALB:
    WEST:
      clusterId: camunda-qal-slb-usw2-cluster
      bastionhost: ec2-54-185-15-100.us-west-2.compute.amazonaws.com
      iamreaduser: camunda_ro_user
      iamwriteuser: camunda_rw_user
      region: us-west-2
      port: 5432
      dbname: PostgresqlDbQal
    EAST:
      clusterId: camunda-qal-slb-use2-cluster
      bastionhost: ec2-3-140-122-121.us-east-2.compute.amazonaws.com
      iamreaduser: camunda_ro_user
      iamwriteuser: camund_rw_user
      region: us-east-2
      port: 5432
      dbname: PostgresqlDbQal
  QALA:
    WEST:
      clusterId:  camunda-qal-sla-usw2-cluster-2
      bastionhost: ec2-52-25-116-154.us-west-2.compute.amazonaws.com
      region: us-west-2
      iamreaduser: camunda_ro_user
      iamwriteuser: camunda_rw_user
      port: 3306
      dbname: PostgresqlDbQal