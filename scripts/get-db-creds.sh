#!/bin/sh

# include parse_yaml function
. parse_yaml.sh
. helper_func.sh

echo "Enter the App for which you want to generate DB password for ? (ex: WAS or CAMUNDA)"
read app

echo "Enter the Env ? (ex: PRODA/PRODB/E2EA/E2EB/QALB/QALA)"
read env

echo "Enter the region ? (ex: WEST or EAST)"
read region

echo "Enter the type of access ? (ex: READ or WRITE)"
read accessType

# read yaml file
eval $(parse_yaml config.yaml "config_")

# check if aws cli is installed or not
checkawscli

# populate db-params by reading details from config
populateDbParams

# validate db-params
validateDbParams

# generate temporary aws credentials
generateAWSTemporaryKey

# evaluate the RDS HOST details
# evaluateHost

# get rds host details
getRdsHost

# generate temporary credentials
generatePwd

# output details
outputDetails