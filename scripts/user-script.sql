-- Create oltp_ro_role
create role was_ro_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public,was TO "was_ro_role";
GRANT CONNECT ON DATABASE "PostgresqlDbE2e" TO "was_ro_role";
GRANT USAGE ON SCHEMA was,public TO "was_ro_role";
GRANT SELECT , USAGE ON ALL SEQUENCES IN SCHEMA public,was to "was_ro_role";

-- Create oltp_rw_role
create role was_rw_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public,was TO "was_rw_role";
GRANT CONNECT ON DATABASE "PostgresqlDbE2e" TO "was_rw_role";
GRANT USAGE ON SCHEMA was,public TO "was_rw_role";
GRANT SELECT , USAGE ON ALL SEQUENCES IN SCHEMA public,was to "was_rw_role";
grant select,update,delete,insert on all tables in schema public,was to "was_rw_role";

-- Create Read-only (RO) user and grant read-only role
create user was_ro_user with login;
grant rds_iam to was_ro_user;
grant was_ro_role to was_ro_user;

-- Create Read-write(RW) user and grant read-write role
create user was_rw_user with login;
grant rds_iam TO was_rw_user;
grant was_rw_role to was_rw_user;