package com.intuit.appintgwkflw.wkflautomate.telemetry.http;

/**
 * Marker interface for custom HTTP response objects.
 *
 * <AUTHOR>
 */
public interface MonitoredHttpResponse {

  /**
   * The HTTP status code.
   *
   * @return status code
   */
  int statusCode();

  /**
   * Is the response a successful 2xx one.
   *
   * @return true if it is 200 series else will return false.
   */
  boolean isSuccess2xx();
}
