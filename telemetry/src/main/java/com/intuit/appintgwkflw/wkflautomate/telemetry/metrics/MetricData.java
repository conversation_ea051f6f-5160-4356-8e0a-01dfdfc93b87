package com.intuit.appintgwkflw.wkflautomate.telemetry.metrics;

import java.util.HashSet;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;

/**
 * This class represents the data present in each metric emitted.
 *
 * <AUTHOR>
 */
@Getter
@Builder
public final class MetricData {

  private final String type;

  private final String name;

  private final String exception;

  private final Set<String> additionalTags = new HashSet<>();

  /**
   * Add an additional tag.
   *
   * @param tag the tag
   */
  public void addTag(final String tag) {

    this.additionalTags.add(tag);
  }
}
