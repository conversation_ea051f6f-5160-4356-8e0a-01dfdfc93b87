package com.intuit.appintgwkflw.wkflautomate.telemetry.metrics;

/**
 * <AUTHOR>
 * <p>Workflow Metric names
 */
public enum MetricName {

  READ_ALL_TEMPLATES,
  READ_ONE_TEMPLATE,
  READ_ALL_DEFINITION,
  READ_ONE_DEFINITION,
  CREATE_WORKFLOW_DEFINITION,
  CREATE_MULTI_STEP_WORKFLOW_DEFINITION,
  CALL_ACTIVITY_HANDLER,
  UPDATE_WORKFLOW_DEFINITION,
  ENABLE_WORKFLOW_DEFINITION,
  DISABLE_WORKFLOW_DEFINITION,
  DISABLE_WORKFLOW_DEFINITION_ASYNC_PROCESS,
  <PERSON><PERSON>TE_WORKFLOW_DEFINITION,
  DELETE_WORKFLOW_DEFINITION_ASYNC_PROCESS,
  DELETE_ALL_WORKFLOWS,
  DELETE_ALL_WORKFLOWS_ASYNC_PROCESS,
  TRIG<PERSON>R_PROCESS,
  TRIGGER_PROCESS_V2,
  EVAL<PERSON>ATE_AND_TRIGGER,
  RULE_EVALUATION,
  UPDATE_TEMPLATE,
  GET_TEMPLATE,
  GET_TEMPLATE_BY_VERSION,
  UPDATE_TEMPLATE_STATUS,
  SAVE_TEMPLATE,
  INVOKE_DUZZIT,
  VALIDATE_TEMPLATE,
  EVENT_EXTERNAL_TASK,
  EVENT_EXTERNAL_TASK_TEST,
  EVENT_EXTERNAL_TASK_DLQ,
  EVENT_TRIGGER,
  EVENT_TRIGGER_NO_ACTION,
  EVENT_TRIGGER_START_FAILED,
  EVENT_TRIGGER_SIGNAL_FAILED,
  EVENT_WORKFLOW_TRANSITION_EVENT,
  EVENT_TRIGGER_DLQ,
  EVENT_PUBLISH,
  FEATURE_FLAG_EVALUATION,
  CAMUNDA_INCIDENT,
  INTERNAL_CAMUNDA_CONSUMER_INCIDENT,
  GET_PROCESS_DETAILS,
  READ_CUSTOM_TEMPLATE,
  READ_PROCESS_DETAILS_FOR_DEFINITION,
  UPDATE_OFFLINE_TICKET,
  INTERNAL_CAMUNDA_SERVICE_TASK_EVENT,
  CAMUNDA_EXTERNAL_TASK_PUSH_EVENT,
  WORKFLOW_EVENTS_PARSER,
  WORKFLOW_TRANSITION_EVENTS,
  PRECANNED_DEFINITION_PLACEHOLDER_EXTRACTOR,
  CUSTOM_DEFINITION_PLACEHOLDER_EXTRACTOR,
  MULTI_STEP_DEFINITION_PLACEHOLDER_EXTRACTOR,
  COMPARE_WORKFLOW_DEFINITION,
  SDEF_MIGRATE_DEFINITION,
  CUSTOM_WORKFLOW_USER_CONFIGURED_PARAMETER_DETAILS_EXTRACTOR,
  CUSTOM_WORKFLOW_NON_USER_CONFIGURED_PARAMETER_DETAILS_EXTRACTOR,
  CUSTOM_SCHEDULED_ACTIONS_PARAMETER_DETAILS_EXTRACTOR,
  PRECANNED_WORKFLOW_PARAMETER_DETAILS_EXTRACTOR,
  SDEF_EVALUATE_DMN,
  CUSTOM_WORKFLOW_CONFIG_HANDLER_DETAILS_EXTRACTOR,
  NESTED_ACTION_GROUP_CONFIG_HANDLER_DETAILS_EXTRACTOR,
  CAMUNDA_JOB_QUEUE,
  CAMUNDA_TASK_QUEUE,
  OFFLINE_BATCH_CLEANUP,
  OFFLINE_BATCH_DISABLE,
  GET_PROCESS_VARIABLE_DETAILS,
  GET_WORKFLOW_TASKS,
  WORKFLOW_TASK_EXECUTION,
  UPDATE_WORKFLOW_TASK,
  WAS_BATCH_OPERATION,
  WAS_MIGRATE_DEFINITION,
  DOMAIN_EVENT_PUBLISH,
  DEFINITION_EVENT,
  GET_DOMAIN_EVENT,
  GET_DOMAIN_EVENT_LIST,
  GET_DOMAIN_EVENT_LIST_COUNT,
  MULTI_STEP_READ_PRECANNED_TEMPLATE_BUILDER,
  MULTI_STEP_READ_ONE_CUSTOM_TEMPLATE_BUILDER,
  DELETE_APPCONNECT_WORKFLOW,
  DISABLE_APPCONNECT_WORKFLOW,
  CAMUNDA_DELETION,
  CAMUNDA_CORRELATION,
  TRIGGER_HANDLER,
  APPCONNECT_UNSUBSCRIBE_WORKFLOW,
  UPDATE_PROCESS_STATUS,
  PROCESS_STATUS,
  EVALUATE_DMN,
  TEST,
  READ_ONE_MULTI_STEP_DEFINITION,
  ESS_SQS_MESSAGE,
  MULTI_STEP_READ_ONE_DEFINITION,
  MULTI_STEP_READ_DEFINITION_PLACEHOLDER_EXTRACTOR,
  CREATE_CALLED_PROCESS,
  MULTI_STEP_INVOKE_DUZZIT,
  CREATE_DMN_DEFINITION,
  BULK_DEFINITION_DELETE,
  BULK_APPROVAL_PREFS_UPDATE,
  WORKFLOW_VARIABILITY_BASED_FILTERING,
  ESS_SQS_MESSAGE_RETRY_EXHAUST,
  CREATE_AND_DEPLOY_BPMN_DYNAMICALLY,
  ESS_MIGRATE_DEFINITION,
  GET_WKFL_TASKS,
  READ_ONE_OBFUSCATE_DEFINITION,
  BATCH_JOB,
  CACHE_WARMUP_JOB,
  CLEANUP_JOB,
  CUSTOM_REMINDER_TO_ESS_MIGRATION_JOB,
  SINGLE_TO_SINGLE_DEFINITION_MIGRATION_JOB,
  USER_TO_SINGLE_DEFINITION_JOB,
  SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_JOB,
  SCHEDULING_SVC_MIGRATION_CLEANUP_JOB,
  GET_PROGRESS_TRACKING,
  VARIABILITY,
  EXPERIENCE_DECISION_EXTERNAL_TASK,
  TRIGGER_PROCESS_NOW,
  FETCH_APPCONNECT_RECORDS_MULTI_STEP_DEFINITION,

  EVENT_WORKFLOW_DEFINITION,
  EVENT_EXTERNAL_TASK_REST,
  NUMAFLOW_MESSAGES_PROCESS,
  EVENT_EXTERNAL_PUSH_TASK,
  SCHEDULING_EVENT,
  GC_ERRORED_EVENT_RETRY,
  TRIGGER_NOW_ASYNC_TASK,
  DELETE_DEFINITION_ASYNC_TASK,
}
