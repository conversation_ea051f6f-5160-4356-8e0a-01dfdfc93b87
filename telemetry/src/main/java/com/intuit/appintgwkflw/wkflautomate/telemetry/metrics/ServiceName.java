package com.intuit.appintgwkflw.wkflautomate.telemetry.metrics;

/**
 * The downstream service names.
 *
 * <AUTHOR>
 */
public enum ServiceName {

  WAS_DB,
  EVENT_BUS,
  APP_CONNECT,
  CAMUNDA,
  IUS,
  ACCESS,
  OINP,
  <PERSON>THER,
  <PERSON>UMAN_TASK,
  AUTH<PERSON>,
  EVENT_SCHEDULE,

  //Enum is used to identify the graphql client urls in was-graphql-client package.
  //config must be defined with the enum as key
  IDENTITY,
  CACHE,
  UCS,
  SCHEDULING_SVC,
  ITM,
  VARIABILITY
}
