package com.intuit.appintgwkflw.wkflautomate.was.worker.config;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExtensionAttributesConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.worker.ExternalTaskWorker;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Client;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ThreadPool;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.worker.factory.ExternalTaskClientFactory;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.impl.client.HttpClientBuilder;
import org.camunda.bpm.client.ExternalTaskClient;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class WorkerGenerator implements BeanFactoryAware {

  private static final String WORKER_GLOBAL_NAME = "global";
  private static final String SHARED_EXECUTOR_NAME = "executor-shared";
  private static final String RESERVED_EXECUTOR_NAME = "executor-%s";
  private static final String DOUBLE_UNDERSCORE = "__";
  public static final String WORKER_NAME_FORMAT = "worker-%s";

  @Value("${http.maxConnections:50}") // Default is 5 in camunda external-task-client jar
  private int maxHttpConnections;

  private final OfflineTicketClient offlineTicketClient;

  private final WorkerExecutor workerExecutor;

  private final WASContextHandler contextHandler;

  private final ExternalTaskConfiguration externalTaskConfiguration;

  private BeanFactory beanFactory;

  private final CamundaRestUtil camundaRestUtil;

  private final ExtensionAttributesConfig extensionAttributesConfig;

  private static HashSet<ExternalTaskWorker> workerSet = new HashSet<>();

  @PostConstruct
  public void onPostConstruct() {
    setHttpMaxConnections();

    final ConfigurableBeanFactory configurableBeanFactory = (ConfigurableBeanFactory) beanFactory;
    final Map<String, Worker> workers = externalTaskConfiguration.getWorkers();
    // Create and register the shared thread pool
    final ThreadPoolExecutor sharedExecutor = getSharedExecutor();
    configurableBeanFactory.registerSingleton(SHARED_EXECUTOR_NAME, sharedExecutor);
    workers.entrySet().stream()
        .filter(entry -> !entry.getValue().isDisable())
        .forEach(
            entry -> {
              final String name = entry.getKey();
              final Worker worker = entry.getValue();
              ThreadPoolExecutor reservedExecutor = null;
              if (!WORKER_GLOBAL_NAME.equals(name)) {
                // Create and register the reserved thread pool
                final String executorName = String.format(RESERVED_EXECUTOR_NAME, name);
                reservedExecutor = getReservedExecutor(worker, executorName);
                if (ObjectUtils.isNotEmpty(worker.getThreadPool())) {
                  WorkflowLogger.logInfo(
                      "Worker configured with executorName=%s, topicName=%s, MaxQueueSize=%s, IndividualMinThreads=%s, IndividualMaxThreads=%s",
                      executorName,
                      worker.getTopicName(),
                      worker.getThreadPool().getMaxQueueSize(),
                      worker.getThreadPool().getIndividualMinThreads(),
                      worker.getThreadPool().getIndividualMaxThreads());
                }
                configurableBeanFactory.registerSingleton(executorName, reservedExecutor);
              }
              // Create and register the worker
              final ExternalTaskWorker externalTaskWorker =
                  getExternalTaskWorker(
                      externalTaskConfiguration.getClient(),
                      worker,
                      sharedExecutor,
                      reservedExecutor);

              String businessKey = getBusinessKey(name);
              externalTaskWorker.initialize(businessKey);
              String workerName = String.format(WORKER_NAME_FORMAT, name);

              configurableBeanFactory.registerSingleton(workerName, externalTaskWorker);
              WorkflowLogger.logInfo(
                  "Worker configured with name=%s and businessKey=%s", name, businessKey);
              workerSet.add(externalTaskWorker);
            });
  }

  public void stopWorkers() {
    workerSet.forEach(
        worker -> {
          worker.stop();
        });
  }

  public void startWorkers() {
    workerSet.forEach(
        worker -> {
          worker.start();
        });
  }

  private ExternalTaskWorker getExternalTaskWorker(
      final Client client,
      final Worker worker,
      final ThreadPoolExecutor sharedExecutor,
      final ThreadPoolExecutor reservedExecutor) {

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client,
            worker,
            offlineTicketClient,
            camundaRestUtil.getCamundaBaseURL(Optional.ofNullable(worker.getOfferingId())),
            contextHandler);

    return new ExternalTaskWorker(
        externalTaskClient,
        worker,
        workerExecutor,
        sharedExecutor,
        reservedExecutor,
        contextHandler,
        extensionAttributesConfig);
  }

  private ThreadPoolExecutor getSharedExecutor() {
    return ThreadPoolExecutorFactory.createExecutor(
        SHARED_EXECUTOR_NAME,
        externalTaskConfiguration.getThreadPool().getMaxQueueSize(),
        externalTaskConfiguration.getThreadPool().getSharedMinThreads(),
        externalTaskConfiguration.getThreadPool().getSharedMaxThreads(),
        externalTaskConfiguration.getThreadPool().getIdleThreadTimeoutSec());
  }

  private ThreadPoolExecutor getReservedExecutor(final Worker worker, final String name) {
    return ThreadPoolExecutorFactory.createExecutor(
        name,
        Optional.ofNullable(worker.getThreadPool())
            .map(ThreadPool::getMaxQueueSize)
            .orElse(externalTaskConfiguration.getThreadPool().getMaxQueueSize()),
        Optional.ofNullable(worker.getThreadPool())
            .map(ThreadPool::getIndividualMinThreads)
            .orElse(externalTaskConfiguration.getThreadPool().getIndividualMinThreads()),
        Optional.ofNullable(worker.getThreadPool())
            .map(ThreadPool::getIndividualMaxThreads)
            .orElse(externalTaskConfiguration.getThreadPool().getIndividualMaxThreads()),
        Optional.ofNullable(worker.getThreadPool())
            .map(ThreadPool::getIdleThreadTimeoutSec)
            .orElse(externalTaskConfiguration.getThreadPool().getIdleThreadTimeoutSec()));
  }

  @Override
  public void setBeanFactory(final BeanFactory beanFactory) {

    this.beanFactory = beanFactory;
  }


  /**
   * Fetches businessKey from the string using {@link #DOUBLE_UNDERSCORE} as delimiter
   * @param name config key
   * @return businessKey if found else return null
   */
  private String getBusinessKey(String name){
    return name.contains(DOUBLE_UNDERSCORE) ? name.split(DOUBLE_UNDERSCORE)[1] : null;
  }

    /**
     * Default http connections in apache Http client(build using system properties) is 5.
     * In camunda external task client jar, it uses the system properties configuration to create
     * http pool. Default pool turns out to be bottleneck at high TPS of task execution.
     * {@link HttpClientBuilder#build()}
     *
     * It doesn't override the property when provided in bootstrap/applications yamls.
     * Hence, overriding it here.

     */
    void setHttpMaxConnections() {
        System.setProperty("http.maxConnections", String.valueOf(maxHttpConnections));
    }
}
