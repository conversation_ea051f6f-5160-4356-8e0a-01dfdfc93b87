package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This class adds shutdown hook to the thread pool which executes external task
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ExternalWorkerPool {

  private final List<ThreadPoolExecutor> threadPoolExecutors;

  private final ExternalTaskConfiguration externalTaskConfiguration;

  @PostConstruct
  public void init() {

    addShutDownHook();
  }

  private void addShutDownHook() {

    Runtime.getRuntime().addShutdownHook(new Thread() {

      @Override
      public void run() {

        WorkflowLogger.info(
            () -> WorkflowLoggerRequest.builder()
                .message(String.format("Trying to shut down %d external worker pools",
                    threadPoolExecutors.size()))
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                .className(this.getClass().getSimpleName()));
        threadPoolExecutors.forEach(this::shutdown);
      }

      private void shutdown(final ThreadPoolExecutor threadPoolExecutor) {

        threadPoolExecutor.shutdown();
        try {
          if (!threadPoolExecutor.awaitTermination(
              externalTaskConfiguration.getThreadPool().getAllowedGracefulShutDownTimeSec(),
              TimeUnit.SECONDS)) {

            final List<Runnable> droppedTasks = threadPoolExecutor.shutdownNow();

            WorkflowLogger.error(
                () ->
                    WorkflowLoggerRequest.builder()
                        .message(
                            "External worker pool did not terminate gracefully. %s tasks have been dropped",
                            droppedTasks.size())
                        .downstreamComponentName(DownstreamComponentName.WAS)
                        .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                        .className(this.getClass().getSimpleName()));
          }
        } catch (final InterruptedException e) {
          WorkflowLogger.error(
              () ->
                  WorkflowLoggerRequest.builder()
                      .message(
                          "Interrupt exception while awaiting termination of ExternalWorkerPool. Some tasks may not have been executed")
                      .downstreamComponentName(DownstreamComponentName.WAS)
                      .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                      .stackTrace(e)
                      .className(this.getClass().getSimpleName()));
        }
      }
    });
  }
}
