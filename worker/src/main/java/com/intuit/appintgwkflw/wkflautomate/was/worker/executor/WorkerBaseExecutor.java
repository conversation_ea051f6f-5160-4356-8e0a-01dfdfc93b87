package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttle;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.util.ExternalTaskUtil;
import io.reactivex.Observable;
import io.reactivex.functions.Predicate;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.camunda.bpm.client.exception.NotFoundException;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.springframework.beans.factory.annotation.Autowired;

/** <AUTHOR> */
public abstract class WorkerBaseExecutor {

  @Autowired private WASContextHandler contextHandler;

  @Autowired protected MetricLogger metricLogger;

  @Autowired private WorkerBaseExecutorHelper workerBaseExecutorHelper;

  @Autowired private CamundaRunTimeServiceRest camundaRunTimeServiceRest;

  public abstract Map<String, Object> executeTask(
      ExternalTask externalTask);

  /**
   * execute the task and if any error occurs call failure api with the retry details
   * ExtendLock is used  during eventing use-cases, when an  event is success, then extend the lock.
   * <p>First a Fetch&Lock happens, with a <b>small lock duration</b>, an  event is published to Eventbus, If success(with retries),
   * Then the lock is extended. If any machine crash happens in between, anathor pod picks up the job after the small local duration.
   * </p>
   * @param externalTask
   * @param externalTaskService
   */
  @Throttle(attribute = ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY)
  public void execute(ExternalTask externalTask, ExternalTaskService externalTaskService, Worker worker) {

    Observable.just(externalTask.getProcessInstanceId())
        .doOnNext(
            input -> {
              logStartEvent(externalTask);
            })
        .map(x -> executeAction(externalTask, externalTaskService))
        .subscribe(
            response -> {
//            If PublishHandler or CustomTaskHandler, then extend lock to wait till we get an ack.
              if (canComplete().test(response)) {
                extendLock(externalTask, externalTaskService, worker, response);
              } else {
                completeAction(externalTask, externalTaskService, response);
              }
            },
            error -> executeFailure(externalTask, externalTaskService, error));
  }

  /**
   * filters out external task which is not required to be marked as completed {@code
   * filter( x -> canComplete().test(x))}
   * for eventing model external tasks will be completed by kafka consumer
   *
   * @return Predicate
   */
  protected Predicate<Map<String, Object>> canComplete() {
    return x -> x.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName());
  }

  /**
   * logs the execute task details
   *
   * @param externalTask
   */
  private void logStartEvent(ExternalTask externalTask) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "WorkerExecutor started for topic=%s processInstanceId=%s activityId=%s taskId=%s entityId=%s",
                    externalTask.getTopicName(),
                    externalTask.getProcessInstanceId(),
                    externalTask.getActivityId(),
                    externalTask.getId(),
                    ExternalTaskUtil.getEntityId(externalTask))
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                .className(this.getClass().getSimpleName()));
  }

  /**
   * log and execute external task failure
   *
   * @param externalTask
   * @param externalTaskService
   * @param error
   */
  public void executeFailure(
      ExternalTask externalTask, ExternalTaskService externalTaskService, Throwable error) {
    if(error instanceof WorkflowNonRetriableException)
      executeFailure(externalTask, externalTaskService, 0 , error);
    else
      executeFailure(externalTask, externalTaskService, workerBaseExecutorHelper.getNumberOfRetries(externalTask), error);
  }

  /**
   * log and execute external task failure
   *
   * @param externalTask
   * @param externalTaskService
   * @param error
   */
  private void executeFailure(
      ExternalTask externalTask,
      ExternalTaskService externalTaskService,
      int noOfretries,
      Throwable error) {

    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "WorkerExecutor error invoked for Activity=%s taskId=%s and processInstanceId=%s.Initiating failure with retry : %s.",
                    externalTask.getActivityId(),
                    externalTask.getId(),
                    externalTask.getProcessInstanceId(),
                    externalTask.getRetries(),
                    externalTask.getActivityId())
                .stackTrace(error)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                .className(WorkerBaseExecutor.class.getSimpleName()));

    long retryTimer = workerBaseExecutorHelper.getRetryTimer(externalTask);

    //Default error is same as previous handling - WORKER_EXECUTE_FAILURE
    WorkflowError workflowError = WorkflowError.WORKER_EXECUTE_FAILURE;
    if(error instanceof WorkflowGeneralException
    		&& null != ((WorkflowGeneralException)error).getWorkflowError()) {
    	/**
    	 * Flow actual error message to handle DB status update in FailureCommand of WorkflowTaskManager.
    	 */
    	workflowError = ((WorkflowGeneralException)error).getWorkflowError();
    }

    // execute failure with retry
    externalTaskService.handleFailure(
        externalTask,
        workflowError.name(),
        String.format(
        	workflowError.getErrorDescription(),
            ExceptionUtils.getStackTrace(error),
            contextHandler.get(WASContextEnums.INTUIT_TID)),
        noOfretries,
        retryTimer);

    // Metrics for incident creation
    if (noOfretries == 0) {
      metricLogger.logErrorMetric(
          MetricName.CAMUNDA_INCIDENT,
          Type.EVENT_METRIC,
          new WorkflowGeneralException(WorkflowError.CAMUNDA_INCIDENT));
    }
  }

  /**
   * log and extend external task Lock In case of  any exceptions during ExtendLock, No need to
   * retry, Catching the exception.
   * Exceptions can be NotFoundException, where the task is already marked completed before extend lock.
   * Throw a Exception for any other types of exceptions. It will be retried in executeFailure
   *
   * @param externalTask
   * @param externalTaskService
   * @param worker
   */
  private void extendLock(
      ExternalTask externalTask,
      ExternalTaskService externalTaskService,
      Worker worker, Map<String, Object> response) {

    try {
      /**
       * As response map with pendingExtendLock is checked to make call to this extendLock method,
       * null check is not required here.
       */
      addOrUpdateExecutionVariables(externalTask, new HashMap<>(response));
//    Base condition, No need to extend the lock when the duration is 0.
      if (worker.getExtendedLockDuration() == 0) {
        return;
      }
      externalTaskService.extendLock(externalTask, worker.getExtendedLockDuration());
      WorkflowLogger.info(() ->  WorkflowLoggerRequest.builder()
          .message("WorkerExecutor Task completed for step=extendLock activityId=%s taskId=%s processId=%s entityId=%s topic=%s",
              externalTask.getActivityId(),
              externalTask.getId(),
              externalTask.getProcessInstanceId(),
              ExternalTaskUtil.getEntityId(externalTask),
              externalTask.getTopicName())
          .downstreamComponentName(DownstreamComponentName.WAS)
          .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
          .className(this.getClass().getSimpleName()));
    } catch (NotFoundException e) {
//    The task is completed . No need to retry it in case of any failures.
      WorkflowLogger.error(() ->  WorkflowLoggerRequest.builder()
          .message("WorkerExecutor Task completed for step=extendLockFailed activityId=%s taskId=%s processId=%s entityId=%s topic=%s error=%s",
              externalTask.getActivityId(),
              externalTask.getId(),
              externalTask.getProcessInstanceId(),
              ExternalTaskUtil.getEntityId(externalTask),
              externalTask.getTopicName(),
              e)
          .downstreamComponentName(DownstreamComponentName.WAS)
          .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
          .className(this.getClass().getSimpleName()));
    }
  }

  /**
   * log and execute external task completion
   *
   * @param externalTask
   * @param externalTaskService
   * @param response
   */
  private void completeAction(
      ExternalTask externalTask,
      ExternalTaskService externalTaskService,
      Map<String, Object> response) {
    // complete the task if everything goes fine
    externalTaskService.complete(externalTask, response);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "WorkerExecutor Task completed for activityId=%s taskId=%s processId=%s entityId=%s topic=%s",
                    externalTask.getActivityId(),
                    externalTask.getId(),
                    externalTask.getProcessInstanceId(),
                    ExternalTaskUtil.getEntityId(externalTask),
                    externalTask.getTopicName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                .className(this.getClass().getSimpleName()));
  }

  /**
   * log and execute action
   *
   * @param externalTask
   * @param externalTaskService
   * @return
   */
  Map<String, Object> executeAction(
      ExternalTask externalTask, ExternalTaskService externalTaskService) {

    // execute the task
    Map<String, Object> response = executeTask(externalTask);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "WorkerExecutor finished for activityId=%s taskId=%s processInstanceId=%s entityId=%s topic=%s",
                    externalTask.getActivityId(),
                    externalTask.getId(),
                    externalTask.getProcessInstanceId(),
                    ExternalTaskUtil.getEntityId(externalTask),
                    externalTask.getTopicName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
                .className(this.getClass().getSimpleName()));

    return response;
  }

  /**
   * Adds/Update ExternalTask scoped variables.
   *
   * @param externalTask
   * @param response
   */
  private void addOrUpdateExecutionVariables(ExternalTask externalTask,
      Map<String, Object> response) {
    try {
     /**
      * All variables except pendingExtendLock need to be saved in Camunda scoped to ExternalTask.
      */
      response.remove(WorkFlowVariables.PENDING_EXTEND_LOCK.getName());
      if (MapUtils.isEmpty(response)) {
	   /**
	    * If there are no other attributes to be saved return.
	    */
        return;
      }
      CamundaUpdateRequest request = CamundaServiceHelper
          .prepareExecutionUpdateRequest(externalTask.getExecutionId(),
              response);
      camundaRunTimeServiceRest.updateExecutionVariables(request);
      WorkflowLogger.logInfo("ExternalTask variable update done. id=%s", externalTask.getId());
    } catch (Exception ex) {
      WorkflowLogger.logError(ex,
          "ExternalTask variable update failed. id=%s", externalTask.getId());
    }
  }

}