package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.WorkerRetryStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.task.ExternalTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static java.util.Objects.isNull;

/*
* Helper functions for WorkerBaseExecutor
* <AUTHOR>
* */
@Component
@AllArgsConstructor
public class WorkerBaseExecutorHelper {

    @Autowired
    private ExternalTaskConfiguration externalTaskConfig;

    @Autowired
    private WorkerRetryHelper workerRetryHelper;

    private static final int MAX_RETRIES = 20;

    /*
     * Gets the taskDetails from externalTask
     * @param externalTask
     * */
    public Optional<HandlerDetails.TaskDetails> getTaskDetailsFromExternalTask(ExternalTask externalTask) {
        String taskDetails = (String)Optional.ofNullable(
            externalTask.getVariable(WorkFlowVariables.TASK_DETAILS_KEY.getName()))
            .orElse(externalTask.getExtensionProperty(WorkFlowVariables.TASK_DETAILS_KEY.getName()));
        return StringUtils.isBlank(taskDetails)
                ? Optional.empty()
                : Optional.ofNullable(ObjectConverter.fromJson(taskDetails, HandlerDetails.TaskDetails.class));
    }

    /**
     * if failure occurred for first time return number of retries as {@code
     * externalTaskConfig.getClient().getRetryCount() or retryCount in taskDetails} else returns left retries
     *
     * @param externalTask
     * @return number of retries
     */
    public int getNumberOfRetries(ExternalTask externalTask) {
        return (isNull(externalTask.getRetries()))
                ? getNumberOfRetriesFromTemplateOrConfig(externalTask)
                : externalTask.getRetries() - 1;
    }

    /*
     * Gets the max number of retries from taskDetails or from externalTaskConfig
     * @param externalTask
     * */
    public int getNumberOfRetriesFromTemplateOrConfig(ExternalTask externalTask) {
        final Optional<HandlerDetails.TaskDetails> taskDetailsOptional = getTaskDetailsFromExternalTask(externalTask);

        return getNumberOfRetriesFromTemplateOrConfigOrTask(taskDetailsOptional, externalTask);

    }

    /*
     * Gets the max number of retries from taskDetails or from taskDetails
     * @param externalTask
     * */
    public int getNumberOfRetriesFromTemplateOrConfigOrTask(Optional<HandlerDetails.TaskDetails> taskDetailsOptional, ExternalTask externalTask) {
        int retryCount = externalTaskConfig.getClient().getRetryCount();

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(
                        externalTask.getProcessDefinitionKey(),
                        externalTask.getActivityId(),
                        externalTask.getBusinessKey()
                );

        if(Objects.nonNull(externalTaskRetryConfig)
                && Objects.nonNull(externalTaskRetryConfig.getRetryCount())) {
            retryCount = externalTaskRetryConfig.getRetryCount();
        }

        //retryCount is optional field.
        retryCount = taskDetailsOptional.map(HandlerDetails.TaskDetails::getRetryCount).orElse(retryCount);

        return Math.min(retryCount, MAX_RETRIES);
    }

    /*
     * Gets the retry timer
     * @param externalTask
     * */
    public long getRetryTimer(ExternalTask externalTask) {
        //Get the taskDetails value from template
        final Optional<HandlerDetails.TaskDetails> taskDetailsOptional = getTaskDetailsFromExternalTask(externalTask);

        //Identify the retry strategy
        RetryStrategyName retryStrategyName = getRetryStrategyName(taskDetailsOptional, externalTask);
        RetryStrategy retryStrategy = WorkerRetryStrategy.getStrategy(retryStrategyName);

        //Obtain retry counts
        int retryLimit = getNumberOfRetriesFromTemplateOrConfigOrTask(taskDetailsOptional, externalTask);
        int retryCount = getNumberOfRetries(externalTask);

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(
                        externalTask.getProcessDefinitionKey(),
                        externalTask.getActivityId(),
                        externalTask.getBusinessKey()
                );
        long retryTime = retryStrategy.computeRetryTimer(
                retryLimit, retryCount,
                Objects.nonNull(externalTaskRetryConfig) && Objects.nonNull(externalTaskRetryConfig.getBackOffStepSize())
                        ? externalTaskRetryConfig.getBackOffStepSize()
                        : workerRetryHelper.getDefaultBackOffStepSize());
        WorkflowLogger.logInfo("Retrying external task workflow=%s activityId=%s retryStrategy=%s retryTime=%s", externalTask.getProcessDefinitionKey(), externalTask.getActivityId(), retryStrategyName.toString(),retryTime);
        return retryTime;
    }

    public RetryStrategyName getRetryStrategyName(Optional<HandlerDetails.TaskDetails> taskDetailsOptional, ExternalTask externalTask) {
        RetryStrategyName retryStrategyName = RetryStrategyName.DEFAULT;

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(
                        externalTask.getProcessDefinitionKey(),
                        externalTask.getActivityId(),
                        externalTask.getBusinessKey()
                );

        if(Objects.nonNull(externalTaskRetryConfig)
                && Objects.nonNull(externalTaskRetryConfig.getRetryStrategyName())) {
            retryStrategyName = externalTaskRetryConfig.getRetryStrategyName();
        }

        //Obtain the retry strategy name
        try {
            String strategyName =  taskDetailsOptional.map(HandlerDetails.TaskDetails::getRetryStrategyName).orElse(null);
            retryStrategyName = RetryStrategyName.valueOf(strategyName);
        } catch (Exception ex) {
            // In case the retry Strategy name obtained from template is not one of the RetryStrategyName enums, then
            // it can throw IllegalArgumentException
            // Also, in case the retry strategy isn't present in the taskDetailsOptional, it would through a NPE
            return retryStrategyName;
        }

        return retryStrategyName;
    }

}
