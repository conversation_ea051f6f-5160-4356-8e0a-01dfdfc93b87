package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_ACTION_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_TASK_HANDLER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_NAME;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.experimentation.ExperienceDecisionConstants;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest.WorkerActionRequestBuilder;
import java.util.AbstractMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors.WorkerExecutorHelper;
import lombok.AllArgsConstructor;
import org.camunda.bpm.client.task.ExternalTask;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class  WorkerExecutor extends WorkerBaseExecutor {

  private CustomWorkflowConfig customWorkflowConfig;
  private ProcessDetailsRepoService processDetailsRepoService;
  /**
   * execute task by first fetching action handler details and if success full mark it complete.
   *
   * @param externalTask
   * @return
   */
  public Map<String, Object> executeTask(ExternalTask externalTask) {

    Map<String, String> inputVariablesMap = WorkerExecutorHelper.createInputVariablesMap(externalTask.getAllVariables());
    HandlerDetails handlerDetails = extractHandlerDetails(inputVariablesMap, externalTask);
    TaskHandlerName handlerName = TaskHandlerName.getActionFromName(WorkerExecutorHelper.prepareHandlerName(handlerDetails));
    extractExperienceDecisionDetails(handlerName, inputVariablesMap, externalTask);

    WorkerActionRequestBuilder workerActionRequestBuilder = WorkerActionRequest.builder();

    /** verify if handler name exists or not */
    WorkflowVerfiy.verify(!WorkflowTaskHandlers.contains(handlerName), UNSUPPORTED_HANDLER_NAME);

    workerActionRequestBuilder.handlerId(handlerDetails.getHandlerId());
    /** Add handlerScope in the workerActionRequest (expected in case of test else null) */
    workerActionRequestBuilder.handlerScope(handlerDetails.getHandlerScope());
    /** prepare WorkerAction Request */
    WorkerActionRequest workerActionRequest =
        workerActionRequestBuilder
            .activityId(externalTask.getActivityId())
            .processDefinitionId(externalTask.getProcessDefinitionId())
            .processInstanceId(externalTask.getProcessInstanceId())
            .inputVariables(inputVariablesMap)
            .taskId(externalTask.getId())
            .workerId(externalTask.getWorkerId())
            .variableMap(externalTask.getAllVariablesTyped())
            .extensionProperties(externalTask.getExtensionProperties())
            .handlerDetails(handlerDetails)
            .definitionKey(externalTask.getProcessDefinitionKey())
            .retries(externalTask.getRetries())
            .businessKey(externalTask.getBusinessKey())
            .build();

    /** call handler and execute action */
    return WorkflowTaskHandlers.getHandler(handlerName).execute(workerActionRequest);
  }

  /**
   * Get handler details from extension attributes if present else get from input
   * variables. If not present in both, try to get it from the config.
   *
   * @param externalTask
   * @param inputVariablesMap
   * @return
   */
  private HandlerDetails getHandlerDetails(ExternalTask externalTask, Map<String, String> inputVariablesMap) {
    return WorkerExecutorHelper.fetchHandlerDetails(WorkerExecutorHelper.getExternalTaskEvent(externalTask), inputVariablesMap,
            customWorkflowConfig, processDetailsRepoService);
  }

  public HandlerDetails extractHandlerDetails(Map<String, String> inputVariablesMap, ExternalTask externalTask) {
    /** get handler details for the task */
    HandlerDetails handlerDetails = getHandlerDetails(externalTask, inputVariablesMap);
    WorkflowVerfiy.verify(isEmpty(handlerDetails.getTaskHandler()), INVALID_TASK_HANDLER_DETAILS);
    WorkflowVerfiy.verify(isEmpty(handlerDetails.getActionName()), INVALID_ACTION_DETAILS);
    return handlerDetails;
  }

  /**
   * Special Handling to get Maps Key,Value for ExperienceDecisionTask
   * @param taskHandler
   * @param inputVariablesMap
   * @param externalTask
   */
  private void extractExperienceDecisionDetails(TaskHandlerName taskHandler,
                                                Map<String, String> inputVariablesMap, ExternalTask externalTask) {
    if(TaskHandlerName.EXPERIENCE_DECISION_HANDLER
            .equals(TaskHandlerName.getActionFromName(taskHandler.getTaskHandlerName()))) {
      Map<String, Object> inputVariables = externalTask.getAllVariables();
      inputVariablesMap.putAll(inputVariables.entrySet().stream()
              .filter(entry -> entry.getValue() != null)
              .filter(entry -> ExperienceDecisionConstants.DECISION_DETAILS.equals(entry.getKey()))
              .collect(
                      Collectors.toMap(
                              Entry::getKey,
                              entry ->
                                      // in case of collection type process variable, convert to JSON string
                                      entry.getValue() instanceof AbstractMap
                                              ? ObjectConverter.toJson(entry.getValue())
                                              : entry.getValue().toString())));
    }
  }

}
