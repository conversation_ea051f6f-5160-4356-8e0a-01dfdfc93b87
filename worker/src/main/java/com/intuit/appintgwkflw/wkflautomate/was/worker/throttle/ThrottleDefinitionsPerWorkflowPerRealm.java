package com.intuit.appintgwkflw.wkflautomate.was.worker.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ThrottleDefinitionsPerWorkflowPerRealm extends ThrottleService {
  private final DefinitionDetailsRepository definitionDetailsRepository;

  @Override
  public ThrottleAttribute getAttribute() {
    return ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM;
  }

  /**
   * Checks scenarios in which throttling is not performed
   * Doesn't throttle if definition throttling is not enabled, if thresholds values
   * are not present or if throttling has been disabled for the workflow
   *
   * @param joinPoint
   * @return
   */
  @Override
  public boolean isThrottlingEnabled(final ProceedingJoinPoint joinPoint) {
    DefinitionInstance definitionInstance = ((DefinitionInstance) joinPoint.getArgs()[0]);

    return throttleConfigs.isDefinitionsPerWorkflowPerRealm() &&
        throttleHelper.isThrottlingEnabledForWorkflow(
            getWorkflowName(definitionInstance),
            ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM);
  }

  /**
   * Return number of definitions created and updated for the workflow
   * Both bpmns and dmns of all status are counted
   *
   * @param joinPoint
   * @return
   */
  @Override
  public Pair<Integer, String> getExecutionCountAndWorkflow(final ProceedingJoinPoint joinPoint) {
    DefinitionInstance definitionInstance = ((DefinitionInstance) joinPoint.getArgs()[0]);
    String realmId = (String) joinPoint.getArgs()[1];

    String workflowName = getWorkflowName(definitionInstance);
    Integer count = definitionDetailsRepository.getCountOfDefinitionsPerWorkflowPerRealm(workflowName,
        Long.valueOf(realmId)) + 1;

    return new ImmutablePair<>(count, workflowName);
  }

  @Override
  public Integer getWarnDiff() {
    return Optional.ofNullable(throttleConfigs).map(ThrottleConfigs::getWarnDiffCount)
        .map(y -> y.get(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)).orElse(0);
  }

  /**
   * On breach of threshold, throw throttling exception
   *
   * @param joinPoint
   */
  @Override
  public void executeFailure(final ProceedingJoinPoint joinPoint, Integer executionCount) {
    DefinitionInstance definitionInstance = (DefinitionInstance) joinPoint.getArgs()[0];
    String realmId = (String) joinPoint.getArgs()[1];

    WorkflowLogger.logError(ResiliencyConstants.THROTTLE_DEFINITION_PER_WORKFLOW_PER_REALM_PREFIX + "Definition threshold per Workflow for Realm has been breached for workflow=" + getWorkflowName(definitionInstance) + " by the realm=" + realmId + "with numDefnsCreated=" + executionCount);
    throw new WorkflowGeneralException(WorkflowError.DEFINITIONS_PER_WORKFLOW_PER_REALM_THRESHOLD_BREACHED, getWorkflowName(definitionInstance), realmId);
  }

  @Override
  public void executeWarn(final ProceedingJoinPoint joinPoint, Integer executionCount) {
    DefinitionInstance definitionInstance = (DefinitionInstance) joinPoint.getArgs()[0];
    String realmId = (String) joinPoint.getArgs()[1];

    WorkflowLogger.logWarn(ResiliencyConstants.THROTTLE_DEFINITION_PER_WORKFLOW_PER_REALM_PREFIX + "Definitions per Workflow for Realm threshold about to be breached for workflow=" + getWorkflowName(definitionInstance) + " by the realm=" + realmId + "with numDefnsCreated=" + executionCount);
  }

  /**
   * Extract workflow name
   *
   * @param definitionInstance
   * @return
   */
  private String getWorkflowName(DefinitionInstance definitionInstance) {
    return definitionInstance.getTemplateDetails().getTemplateName(); // create definition case
  }
}
