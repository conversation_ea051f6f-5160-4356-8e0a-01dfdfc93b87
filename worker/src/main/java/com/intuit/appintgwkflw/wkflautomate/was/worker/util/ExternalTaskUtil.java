package com.intuit.appintgwkflw.wkflautomate.was.worker.util;
import lombok.experimental.UtilityClass;
import org.camunda.bpm.client.task.ExternalTask;

import java.util.Optional;

/** A Utility Class */
@UtilityClass
public class ExternalTaskUtil {
    private static final String ENTITY_ID = "Id";
    /*
     * Gets the entityId from externalTask
     */

    public static String getEntityId(ExternalTask externalTask) {
        return Optional.ofNullable(externalTask.getAllVariables())
                .map(variables -> (String) variables.get(ENTITY_ID))
                .orElse(null);
    }
}