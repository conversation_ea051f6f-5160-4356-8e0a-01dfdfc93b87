package com.intuit.appintgwkflw.wkflautomate.was.worker;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExtensionAttributesConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import org.camunda.bpm.client.ExternalTaskClient;
import org.camunda.bpm.client.topic.TopicSubscription;
import org.camunda.bpm.client.topic.TopicSubscriptionBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

public class ExternalTaskWorkerTest {

  @Mock private ExternalTaskClient externalTaskClient;

  @Mock private Worker worker;

  @Mock private WorkerExecutor workerExecutor;

  @Mock private ThreadPoolExecutor sharedExecutor;

  @Mock private ThreadPoolExecutor reservedExecutor;

  @Mock private WASContextHandler contextHandler;

  @Mock private ExtensionAttributesConfig extensionAttributesConfig;

  @InjectMocks private ExternalTaskWorker externalTaskWorker;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void initialize() {
    Mockito.when(worker.getTopicName()).thenReturn("topic");
    Mockito.when(worker.getLockDuration()).thenReturn((long) 200);

    TopicSubscriptionBuilder builder = Mockito.mock(TopicSubscriptionBuilder.class);

    Mockito.when(externalTaskClient.subscribe(Mockito.anyString())).thenReturn(builder);
    Mockito.when(builder.businessKey(Mockito.anyString())).thenReturn(builder);
    Mockito.when(builder.lockDuration(Mockito.anyLong())).thenReturn(builder);
    Mockito.when(builder.includeExtensionProperties(Mockito.anyBoolean())).thenReturn(builder);
    Mockito.when(builder.handler(Mockito.any())).thenReturn(builder);

    externalTaskWorker.initialize("key");
    verify(builder, never()).open();
    verify(externalTaskClient).stop();
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUnintializedStart() {
    externalTaskWorker.start();
  }

  @Test
  public void testStart() {
    TopicSubscriptionBuilder topicSubscriptionBuilder = getBuilder();
    ReflectionTestUtils.setField(
        externalTaskWorker, "topicSubscriptionBuilder", topicSubscriptionBuilder);

    Mockito.when(externalTaskClient.isActive()).thenReturn(false);
    externalTaskWorker.start();
    Mockito.verify(externalTaskClient).start();
    Mockito.verify(topicSubscriptionBuilder).open();
  }

  @Test
  public void testStartActive() {
    TopicSubscriptionBuilder topicSubscriptionBuilder = getBuilder();
    ReflectionTestUtils.setField(
        externalTaskWorker, "topicSubscriptionBuilder", topicSubscriptionBuilder);

    Mockito.when(externalTaskClient.isActive()).thenReturn(true);
    externalTaskWorker.start();
    Mockito.verify(externalTaskClient, times(0)).start();
    Mockito.verify(topicSubscriptionBuilder, times(0)).open();
  }

  @Test
  public void testStop() {
    Mockito.when(externalTaskClient.isActive()).thenReturn(true);
    TopicSubscription topicSubscription = Mockito.mock(TopicSubscription.class);
    ReflectionTestUtils.setField(externalTaskWorker, "topicSubscription", topicSubscription);

    externalTaskWorker.stop();
    Mockito.verify(externalTaskClient).stop();
    Mockito.verify(topicSubscription).close();
  }

  @Test
  public void testStopActiveNotInit() {
    Mockito.when(externalTaskClient.isActive()).thenReturn(true);
    externalTaskWorker.stop();
    Mockito.verify(externalTaskClient).stop();
  }

  @Test
  public void testStopInactive() {
    Mockito.when(externalTaskClient.isActive()).thenReturn(false);
    externalTaskWorker.stop();
    Mockito.verify(externalTaskClient, times(0)).stop();
  }

  private TopicSubscriptionBuilder getBuilder() {
    return Mockito.mock(TopicSubscriptionBuilder.class);
  }
}
