package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.request.State;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CustomWorkflowConfigHandlerDetailsExtractorTest {

  public static String DICTIONARY_PATH = "config.yaml";
  public static String YAML_KEY = "templateConfig";

  public static String readResourceAsString(String path) {
    try (InputStream stream = Resources.getResource(path).openStream()) {
      return IOUtils.toString(stream, Charsets.UTF_8);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static CustomWorkflowConfig loadCustomConfig() {

    try {
      OldCustomWorkflowConfig oldCustomWorkflowConfig =
          new ObjectMapper(new YAMLFactory())
              .readValue(
                  readResourceAsString(DICTIONARY_PATH),
                  new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
              .get(YAML_KEY);
      oldCustomWorkflowConfig.afterPropertiesSet();
      CustomConfigV2 customConfigV2 = new CustomConfigV2();
      CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
      customWorkflowConfig.setCustomWorkflowConfigFactory(
          new CustomWorkflowConfigFactory(
              oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
      customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
      customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
      return customWorkflowConfig;
    } catch (Exception e) {
    }
    return null;
  }

  CustomWorkflowConfig customWorkflowConfig = loadCustomConfig();

  @Test
  public void testHandlerDetailsFromCustomWorkflowConfig() {
    State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("entityType", "invoice");
    ExternalTaskEvent externalTask = new ExternalTaskEvent();
    externalTask.setActivityId("sendPushNotification");
    externalTask.setProcessDefinitionKey("customReminder");

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);
    CustomWorkflowConfigHandlerDetailsExtractor customWorkflowConfigHandlerDetailsExtractor =
        new CustomWorkflowConfigHandlerDetailsExtractor();

    State inputRequestResponse = customWorkflowConfigHandlerDetailsExtractor.execute(inputRequest);
    Assert.assertNotNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
    HandlerDetails handlerDetails =
        inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS);
    Assert.assertEquals("appconnect", handlerDetails.getTaskHandler());
    Assert.assertEquals("executeWorkflowAction", handlerDetails.getActionName());
  }

  @Test
  public void testUnsupportedHandlerDetailsFromCustomWorkflowConfig() {
    State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("entityType", null);
    ExternalTaskEvent externalTask = new ExternalTaskEvent();
    externalTask.setActivityId("sendPushNotification");
    externalTask.setProcessDefinitionKey("customReminder");

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);
    CustomWorkflowConfigHandlerDetailsExtractor customWorkflowConfigHandlerDetailsExtractor =
        new CustomWorkflowConfigHandlerDetailsExtractor();
    try {
      customWorkflowConfigHandlerDetailsExtractor.execute(inputRequest);
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.UNSUPPORTED_HANDLER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Mock ExternalTaskEvent externalTask;

  @Test
  public void testHandlerDetailsNotDirectlyFromConfigForChildProcess() {
    State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("entityType", "invoice");

    Mockito.doReturn(
        Map.of(
            ROOT_PROCESS_INSTANCE_ID, "sampleRootProcessId"
        )
    ).when(externalTask).getVariables();

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);
    CustomWorkflowConfigHandlerDetailsExtractor customWorkflowConfigHandlerDetailsExtractor =
        new CustomWorkflowConfigHandlerDetailsExtractor();

    State inputRequestResponse = customWorkflowConfigHandlerDetailsExtractor.execute(inputRequest);
    Assert.assertNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
  }
}
