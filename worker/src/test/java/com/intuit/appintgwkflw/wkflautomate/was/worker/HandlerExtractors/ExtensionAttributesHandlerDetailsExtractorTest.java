package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class ExtensionAttributesHandlerDetailsExtractorTest {

  @Test
  public void testHandlerDetailsFromExtensionAttributes() {
    final State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, String> extensionAttributes = new HashMap<>();
    extensionAttributes.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    ExternalTaskEvent externalTask = new ExternalTaskEvent();
    externalTask.setExtensionProperties(extensionAttributes);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    ExtensionAttributesHandlerDetailsExtractor extensionAttributesHandlerDetailsExtractor =
        new ExtensionAttributesHandlerDetailsExtractor();
    State inputRequestResponse = extensionAttributesHandlerDetailsExtractor.execute(inputRequest);
    Assert.assertNotNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
    HandlerDetails handlerDetails =
        inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS);
    Assert.assertEquals("was", handlerDetails.getTaskHandler());
    Assert.assertEquals("publishEvent", handlerDetails.getActionName());
  }

  @Test
  public void testUnsupportedHandlerDetailsFromExtensionAttributes() {
    final State inputRequest = new State();
    Map<String, String> extensionAttributes = new HashMap<>();
    extensionAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), null);
    ExternalTaskEvent externalTask = new ExternalTaskEvent();
    externalTask.setExtensionProperties(extensionAttributes);
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, externalTask);
    ExtensionAttributesHandlerDetailsExtractor extensionAttributesHandlerDetailsExtractor =
        new ExtensionAttributesHandlerDetailsExtractor();
    try {
      extensionAttributesHandlerDetailsExtractor.execute(inputRequest);
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.UNSUPPORTED_HANDLER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }
}
