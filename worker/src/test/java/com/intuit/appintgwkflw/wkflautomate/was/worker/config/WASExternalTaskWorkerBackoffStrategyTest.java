package com.intuit.appintgwkflw.wkflautomate.was.worker.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.config.WASExternalTaskWorkerBackoffStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Client;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.camunda.bpm.client.exception.BadRequestException;
import org.camunda.bpm.client.exception.ExternalTaskClientException;
import org.camunda.bpm.client.exception.RestException;
import org.camunda.bpm.client.task.ExternalTask;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;


public class WASExternalTaskWorkerBackoffStrategyTest {

  WASExternalTaskWorkerBackoffStrategy wasExternalTaskWorkerBackoffStrategy;

  @Before
  public void init() {
    Client client = new Client();
    client.setBackOffFactor(Long.valueOf(2));
    client.setBackOffInitTime(Long.valueOf(1000));
    client.setBackOffMaxTime(Long.valueOf(16000));
    client.setErrorBackOffFactor(Float.valueOf(2));
    client.setErrorBackOffInitTime(Long.valueOf(4000));
    client.setErrorBackOffMaxTime(Long.valueOf(32000));
    client.setSlidingWindow(10);
    client.setBackOffStatusCodes(List.of(502, 503, 504));

    wasExternalTaskWorkerBackoffStrategy =
        new WASExternalTaskWorkerBackoffStrategy(new Worker(),new ImmutablePair<>(client, Optional.ofNullable(null)));
  }

  /**
   * Empty response is received in Fetch And lock
   * Only emptyTaskDetailsLevel increments
   */
  @Test
  public void testConfigure_EmptyResponse(){
    wasExternalTaskWorkerBackoffStrategy.errorLevel = 4;
    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), null);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(1000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), null);
    Assert.assertEquals(2, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(2000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), null);
    Assert.assertEquals(3, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(4000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(
        Collections.singletonList(Mockito.mock(ExternalTask.class)), null);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

  }

  /**
   * Only Error response is received.
   * errorLevel increments..
   * Doesn't not increment for non backoff httpCode like 400
   * Empty response backoff increments on non backoff http code.
   */
  @Test
  public void testConfigure_Error(){
    wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel = 4;
    RestException gatewayTimeoutException = new RestException(HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase(),
    		HttpStatus.GATEWAY_TIMEOUT.name(),
    		HttpStatus.GATEWAY_TIMEOUT.value());
    gatewayTimeoutException.setHttpStatusCode(HttpStatus.GATEWAY_TIMEOUT.value());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), gatewayTimeoutException);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(4000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), gatewayTimeoutException);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(3, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(16000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    RestException badRequestRestException = new RestException(HttpStatus.BAD_REQUEST.getReasonPhrase(),
    		HttpStatus.BAD_REQUEST.name(),
    		HttpStatus.BAD_REQUEST.value());
    badRequestRestException.setHttpStatusCode(HttpStatus.BAD_REQUEST.value());
    BadRequestException badRequestException = new BadRequestException("Test Exception",
    		badRequestRestException);

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), badRequestException);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(1000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), gatewayTimeoutException);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(3, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(16000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());
  }

  /**
   * Neither empty response nor error. Backoff time and levels  = 0
   */
  @Test
  public void testConfigure_Neither_Error_Nor_Empty_Response(){
    wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel = 4;
    wasExternalTaskWorkerBackoffStrategy.reconfigure(Collections.singletonList(Mockito.mock(
        ExternalTask.class)), null);

    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.errorLevel = 4;
    wasExternalTaskWorkerBackoffStrategy.reconfigure(Collections.singletonList(Mockito.mock(
        ExternalTask.class)), null);

    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());
  }

  /**
   * Combination od empty response and errors.
   */
  @Test
  public void testConfigure_EmptyResponse_And_Error(){
	  RestException exception =
        new RestException(HttpStatus.GATEWAY_TIMEOUT.getReasonPhrase(),
        		HttpStatus.GATEWAY_TIMEOUT.name(),
        		HttpStatus.GATEWAY_TIMEOUT.value());
	  exception.setHttpStatusCode(HttpStatus.GATEWAY_TIMEOUT.value());

    wasExternalTaskWorkerBackoffStrategy.errorLevel = 4;
    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), null);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(1000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), exception);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(4000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), exception);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(3, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(16000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), exception);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(3, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(16000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), exception);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(5, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(32000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), null);
    Assert.assertEquals(1, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(1000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

    RestException badRequestRestException = new RestException(HttpStatus.BAD_REQUEST.getReasonPhrase(), HttpStatus.BAD_REQUEST.name(),
			HttpStatus.BAD_REQUEST.value());
    badRequestRestException.setHttpStatusCode(HttpStatus.BAD_REQUEST.value());
	ExternalTaskClientException exceptionNonRetry = new BadRequestException("Test Exception",
        badRequestRestException);

    wasExternalTaskWorkerBackoffStrategy.reconfigure(new ArrayList<>(), exceptionNonRetry);
    Assert.assertEquals(2, wasExternalTaskWorkerBackoffStrategy.emptyTaskDetailsLevel);
    Assert.assertEquals(0, wasExternalTaskWorkerBackoffStrategy.errorLevel);
    Assert.assertEquals(2000, wasExternalTaskWorkerBackoffStrategy.calculateBackoffTime());

  }

}