package com.intuit.appintgwkflw.wkflautomate.was.worker.config;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExecutorConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExtensionAttributesConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.worker.ExternalTaskWorker;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ThreadPool;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import java.util.Arrays;
import java.util.HashSet;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.test.util.ReflectionTestUtils;

public class WorkerGeneratorTest {

  @Mock private OfflineTicketClient offlineTicketClient;

  @Mock private WorkerExecutor workerExecutor;

  @Mock private WASContextHandler contextHandler;

  @Mock private ExternalTaskConfiguration externalTaskConfiguration;

  @Mock private CamundaRestUtil camundaRestUtil;

  @Mock private ExtensionAttributesConfig extensionAttributesConfig;

  @Mock private ExecutorConfig executorConfig;

  private ConfigurableBeanFactory beanFactory;

  @InjectMocks private WorkerGenerator workerGenerator;

  private static final String TEST_WORKER = "test-worker";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(externalTaskConfiguration.getThreadPool()).thenReturn(getThreadPool());
    beanFactory = Mockito.mock(ConfigurableBeanFactory.class);
    workerGenerator.setBeanFactory(beanFactory);
  }

  @Test
  public void testHttpConnection() {
    ReflectionTestUtils.setField(workerGenerator, "maxHttpConnections", 50);
    workerGenerator.setHttpMaxConnections();
    Assert.assertEquals("50", System.getProperty("http.maxConnections"));
  }
  @Test
  public void testStart() {
    ExternalTaskWorker worker = getWorker();
    ReflectionTestUtils.setField(
        workerGenerator,
        "workerSet",
        new HashSet<>(Arrays.asList(new ExternalTaskWorker[] {worker})));
    workerGenerator.startWorkers();
    Mockito.verify(worker).start();
  }

  @Test
  public void testStop() {
    ExternalTaskWorker worker = getWorker();
    ReflectionTestUtils.setField(
        workerGenerator,
        "workerSet",
        new HashSet<>(Arrays.asList(new ExternalTaskWorker[] {worker})));
    workerGenerator.stopWorkers();
    Mockito.verify(worker).stop();
  }

  private ExternalTaskWorker getWorker() {
    ExternalTaskWorker mockedWorker = Mockito.mock(ExternalTaskWorker.class);
    Worker testWorker = getTestWorker();
    Mockito.when(mockedWorker.getWorker()).thenReturn(testWorker);
    return mockedWorker;
  }

  private Worker getTestWorker() {
    Worker mockedWorker = Mockito.mock(Worker.class);
    Mockito.when(mockedWorker.getTopicName()).thenReturn(TEST_WORKER);
    return mockedWorker;
  }

  private ThreadPool getThreadPool() {
    ThreadPool threadPool = new ThreadPool();
    threadPool.setMaxQueueSize(1);
    threadPool.setIndividualMinThreads(1);
    threadPool.setIndividualMinThreads(1);
    threadPool.setIdleThreadTimeoutSec(1);
    return threadPool;
  }
}
