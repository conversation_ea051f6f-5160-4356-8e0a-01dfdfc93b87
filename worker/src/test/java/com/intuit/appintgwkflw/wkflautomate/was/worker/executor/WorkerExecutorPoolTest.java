package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExecutorConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.stubbing.answers.AnswersWithDelay;

public class WorkerExecutorPoolTest {

  @Mock private ExecutorConfig executorConfig;

  @Mock private ExternalTask externalTask;

  @Mock private ExternalTaskService externalTaskService;

  @Mock private WASContextHandler contextHandler;

  @Mock private WorkerBaseExecutor workerBaseExecutor;

  @Mock private Worker worker;

  @InjectMocks private ExecuteActionAsync executeActionAsync;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testOfferingId() throws InterruptedException {
	Mockito.when(externalTask.getProcessDefinitionKey()).thenReturn("def_5556_6787878");
	Mockito.when(externalTask.getBusinessKey()).thenReturn("5556");

    // Test with actual offeringId
    Mockito.when(worker.getOfferingId()).thenReturn("test");
    executeActionAsync.run();
    Assert.assertEquals("test", WASContext.getOfferingId().get());

    // Test with null offeringId for default tenant
    Mockito.when(worker.getOfferingId()).thenReturn(null);
    executeActionAsync.run();
    Assert.assertEquals(Optional.empty(), WASContext.getOfferingId());
  }


  @Test
  public void testDefinitionKey_templateName() throws InterruptedException {
    Map<String, Object> variables = Map.of(CorrelationKeysEnum.TEMPLATE_NAME.getName(), "def1");
    Mockito.when(externalTask.getAllVariables()).thenReturn(variables);
    // Test with null offeringId for default tenant
    Mockito.when(worker.getOfferingId()).thenReturn(null);
    executeActionAsync.run();
    Assert.assertEquals(Optional.empty(), WASContext.getOfferingId());
    Mockito.verify(contextHandler, Mockito.times(1))
    	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.eq("def1"));
  }

  @Test
  public void testDefinitionKey_businessKey() throws InterruptedException {
	  Mockito.when(externalTask.getProcessDefinitionKey()).thenReturn("def2_5556_6787878");
		Mockito.when(externalTask.getBusinessKey()).thenReturn("5556");

    // Test with null offeringId for default tenant
    Mockito.when(worker.getOfferingId()).thenReturn(null);
    executeActionAsync.run();
    Assert.assertEquals(Optional.empty(), WASContext.getOfferingId());
    Mockito.verify(contextHandler, Mockito.times(1))
    	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.eq("def2"));
  }

  @Test
  public void testDefinitionKey_NobusinessKey() throws InterruptedException {
	  Mockito.when(externalTask.getProcessDefinitionKey()).thenReturn("def3");
		Mockito.when(externalTask.getBusinessKey()).thenReturn("5556");

    // Test with null offeringId for default tenant
    Mockito.when(worker.getOfferingId()).thenReturn(null);
    executeActionAsync.run();
    Assert.assertEquals(Optional.empty(), WASContext.getOfferingId());
    Mockito.verify(contextHandler, Mockito.times(1))
    	.addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.eq("def3"));
  }


  @Test
  public void testExternalWorkerPool() throws InterruptedException {
    int timePerTask = 1000;
    int threads = 2;
    int queueSize = 2;
    int totalTasks = 10;

    long startTime = System.currentTimeMillis();


    ThreadPoolExecutor executor =
        new ThreadPoolExecutor(
            threads,
            threads,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<Runnable>(queueSize),
            new ThreadPoolExecutor.CallerRunsPolicy());

    Mockito.when(worker.getOfferingId()).thenReturn("test");

    Mockito.doAnswer(new AnswersWithDelay(timePerTask, null))
        .when(workerBaseExecutor)
        .execute(Mockito.any(), Mockito.any(), Mockito.any());

    Mockito.when(externalTask.getProcessDefinitionKey()).thenReturn("def_5556_6787878");
	Mockito.when(externalTask.getBusinessKey()).thenReturn("5556");


    // Submit 10 tasks
    for (int i = 0; i < totalTasks; i++) {
      executor.submit(executeActionAsync);
    }

    // Check if the executor has any pending tasks
    while (executor.getActiveCount() > 0 || executor.getQueue().size() > 0) {
      Thread.sleep(1);
    }

    long timeTaken = (System.currentTimeMillis() - startTime);

    int parellTasks = (threads + 1); // (+1) for main thread
    int noOfSteps = (int) Math.ceil((double) totalTasks / (double) parellTasks);
    int minTime = timePerTask * (noOfSteps - 1);

    // Assert that the time taken is proportional to the number of steps
    Assert.assertTrue(timeTaken >= minTime);
    executor.shutdown();
  }
}
