package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName.WAS_PUBLISH_EVENT_HANDLER;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.AppConnectWorkflowTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.client.exception.NotFoundException;
import org.camunda.bpm.client.exception.RestException;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


/** <AUTHOR> */
public class WorkerExecutorTest {

  private static final Map<String, Object> schema = new HashMap<>();
  public static String DICTIONARY_PATH = "config.yaml";
  public static String YAML_KEY = "templateConfig";

  public static String readResourceAsString(String path) {
    try (InputStream stream = Resources.getResource(path).openStream()) {
      return IOUtils.toString(stream, Charsets.UTF_8);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static CustomWorkflowConfig loadCustomConfig() {

    try {
      OldCustomWorkflowConfig oldCustomWorkflowConfig =
          new ObjectMapper(new YAMLFactory())
              .readValue(
                  readResourceAsString(DICTIONARY_PATH),
                  new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
              .get(YAML_KEY);
      oldCustomWorkflowConfig.afterPropertiesSet();
      CustomConfigV2 customConfigV2 = new CustomConfigV2();
      CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
      customWorkflowConfig.setCustomWorkflowConfigFactory(
          new CustomWorkflowConfigFactory(
              oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
      customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
      customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
      return customWorkflowConfig;
    } catch (Exception e) {
    }
    return null;
  }

  static {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "appconnect");
    parametersSchema.put("actionName", "executeWorkflowAction");
    schema.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
  }

  @Mock private ExternalTask externalTask;
  @Mock private ExternalTaskService externalTaskService;
  @Mock private AppConnectWorkflowTaskHandler appConnectActionHandler;
  @Mock private PublishEventHandler publishEventHandler;
  @Mock private WASContextHandler contextHandler;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private WorkerUtil workerUtil;
  @Mock private ExternalTaskConfiguration externalTaskConfig;
  @Mock private Worker worker;
  @Mock private WorkerBaseExecutorHelper workerBaseExecutorHelper;
  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  CustomWorkflowConfig customWorkflowConfig = loadCustomConfig();
  @InjectMocks private WorkerExecutor workerExecutor= new WorkerExecutor(customWorkflowConfig, processDetailsRepoService);;
  @Mock private CamundaRunTimeServiceRest camundaRunTimeServiceRest;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DISABLE);
    Optional<DefinitionDetails> value = Optional.of(definitionDetails);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(Mockito.anyString()))
        .thenReturn(value);
    doReturn(1l).when(worker).getExtendedLockDuration();
  }

  @Test
  public void testUnsupportedHandlerDetails() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(new HashMap<>());
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");

    Mockito.when(workerBaseExecutorHelper.getNumberOfRetries(Mockito.any())).thenReturn(1);
    Mockito.when(workerBaseExecutorHelper.getRetryTimer(Mockito.any())).thenReturn(1l);
    workerExecutor.execute(externalTask, externalTaskService, worker);

    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testSuccess() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());
    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1)).complete(Mockito.any(), Mockito.any());
  }

  @Test
  public void testSuccessCompleteFailure() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());

    // throw execution in calling failure api
    Mockito.doThrow(new RuntimeException("Timeout occured in complete"))
        .when(externalTaskService)
        .complete(Mockito.any(), Mockito.any());

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testSuccessCompleteFailure_NonRetryableException() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());

    // throw execution in calling failure api
    Mockito.doThrow(new WorkflowNonRetriableException(WorkflowError.PROCESS_IN_ERROR_STATE))
        .when(externalTaskService)
        .complete(Mockito.any(), Mockito.any());

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.eq(WorkflowError.PROCESS_IN_ERROR_STATE.name()),
            Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testSuccessCompleteFailure_WorkflowGeneralException_noWorkflowError() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());

    // throw execution in calling failure api
    Mockito.doThrow(new WorkflowGeneralException(new RuntimeException("")))
        .when(externalTaskService)
        .complete(Mockito.any(), Mockito.any());

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.eq(WorkflowError.WORKER_EXECUTE_FAILURE.name()),
            Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testSuccessCompleteFailureHandleFailure() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());

    // throw execution in calling failure api
    Mockito.doThrow(new RuntimeException("Timeout occured in complete"))
        .when(externalTaskService)
        .complete(Mockito.any(), Mockito.any());

    // throw execution in calling failure api
    Mockito.doThrow(new RuntimeException("Timeout occured in failure"))
        .when(externalTaskService)
        .handleFailure(
            externalTask,
            WorkflowError.WORKER_EXECUTE_FAILURE.getErrorMessage(),
            WorkflowError.WORKER_EXECUTE_FAILURE.getErrorDescription(),
            0,
            0);

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testFailure() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any()))
        .thenThrow(new RuntimeException());
    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1)).complete(Mockito.any(), Mockito.any());
  }

  @Test
  public void testFailureHandleFailure() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any()))
        .thenThrow(new RuntimeException());

    // throw execution in calling failure api
    Mockito.doThrow(new RuntimeException("Timeout occured in failure"))
        .when(externalTaskService)
        .handleFailure(
            externalTask,
            WorkflowError.WORKER_EXECUTE_FAILURE.getErrorMessage(),
            WorkflowError.WORKER_EXECUTE_FAILURE.getErrorDescription(),
            0,
            0);
    try {
      workerExecutor.execute(externalTask, externalTaskService, worker);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testFailureForDisabledDefinition() {

    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DISABLE);
    Optional<DefinitionDetails> value = Optional.of(definitionDetails);
    Mockito.when(definitionDetailsRepository.findByDefinitionId(Mockito.any())).thenReturn(value);

    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenCallRealMethod();
    Mockito.doCallRealMethod().when(workerUtil).validate(Mockito.any());

    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");

    workerExecutor.execute(externalTask, externalTaskService, worker);
  }

  @Test
  public void testExternalTaskFilterForPublishEvent() {
    Map<String, Object> response =
        ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    try {
      // external task having "PENDING" key should be evaluated as false and filtered out
      Assert.assertTrue(workerExecutor.canComplete().test(response));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExternalTaskFilter() {
    Map<String, Object> response =
        ImmutableMap.of(WorkFlowVariables.RESPONSE.getName(), Boolean.TRUE);
    try {
      // external task not having "PENDING" key should be evaluated as true and continue execution
      Assert.assertFalse(workerExecutor.canComplete().test(response));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testPublishEventUseCase() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.executeAction(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));
    Assert.assertNotNull(publishEventHandler.executeAction(Mockito.any()));
    workerExecutor.execute(externalTask, externalTaskService, worker);
  }

  @Test
  public void testEventCompleteUseCase() {
    Mockito.when(externalTask.getAllVariables()).thenReturn(schema);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(
        APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());
    Assert.assertNotNull(appConnectActionHandler.executeAction(Mockito.any()));
    workerExecutor.execute(externalTask, externalTaskService, worker);
  }

  @Test
  public void testEventFailureUseCase() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenThrow(new WorkflowEventException("aa"));
    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testExtendLock() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
  }

  @Test
  public void testExtendLockThrowException() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    doThrow(new WorkflowGeneralException("aa"))
        .when(externalTaskService)
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
    Mockito.when(workerBaseExecutorHelper.getNumberOfRetries(externalTask)).thenReturn(1);
    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(1))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
    Mockito.verify(externalTaskService, Mockito.times(1))
        .handleFailure(
            Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
  }

  @Test
  public void testExtendLockThrowExceptionNotFoundException() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    doThrow(new NotFoundException("aa", new RestException("aa", new RuntimeException("aa"))))
        .when(externalTaskService)
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
    try {
      workerExecutor.execute(externalTask, externalTaskService, worker);
      Mockito.verify(externalTaskService, Mockito.times(1))
          .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
      Mockito.verify(externalTaskService, Mockito.times(0))
          .handleFailure(
              Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyLong());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExtendLockWithZeroLockDuration() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    doReturn(0l).when(worker).getExtendedLockDuration();
    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(externalTaskService, Mockito.times(0))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
  }

  @Test
  public void testPublishEventHandler() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);

    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    Map<String, Object> taskExecutor = workerExecutor.executeTask(externalTask);
    Assert.assertTrue((Boolean) taskExecutor.get(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));
  }

  @Test
  public void testTaskHandlerNotPresentException() {
    Map<String, String> parametersSchema = new HashMap<>();
    Map<String, Object> variable = new HashMap<>();
    parametersSchema.put("actionName", "publishEvent");
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(workerBaseExecutorHelper.getNumberOfRetries(externalTask)).thenReturn(1);
    try {
      workerExecutor.executeTask(externalTask);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(WorkflowError.UNSUPPORTED_HANDLER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testUnSupportedActionNameNotFoundTest() {
    Map<String, String> parametersSchema = new HashMap<>();
    Map<String, Object> variable = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(workerBaseExecutorHelper.getNumberOfRetries(externalTask)).thenReturn(1);
    try {
      workerExecutor.executeTask(externalTask);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(WorkflowError.INVALID_ACTION_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testUnSupportedHandlerDetailsNotFoundTest() {
    Map<String, Object> variable = new HashMap<>();
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Mockito.when(workerBaseExecutorHelper.getNumberOfRetries(externalTask)).thenReturn(1);
    try {
      workerExecutor.executeTask(externalTask);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.UNSUPPORTED_HANDLER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }


  @Test
  public void testHandlerDetailsFromExtensionAttributes(){
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, String> extensionAttributes = new HashMap<>();
    extensionAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getExtensionProperties()).thenReturn(extensionAttributes);
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    Map<String, Object> taskExecutor = workerExecutor.executeTask(externalTask);
    Assert.assertNotNull(externalTask.getExtensionProperties());
    Assert.assertNotNull(externalTask.getExtensionProperties().get(WorkFlowVariables.HANDLER_DETAILS_KEY.getName()));
  }

  @Test
  public void testHandlerDetailsNotFoundInExtensionAttributesTest() {
    Map<String, String> extensionAttributes = new HashMap<>();
    extensionAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),null);
    Mockito.when(externalTask.getExtensionProperties()).thenReturn(extensionAttributes);
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);
    try {
      workerExecutor.executeTask(externalTask);
      Assert.fail();
    } catch (Exception e) {
      Assert.assertEquals(
          WorkflowError.UNSUPPORTED_HANDLER_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testHandlerDetailsFromConfig() {
    Map<String, Object> parametersSchema = new HashMap<>();
    parametersSchema.put("entityType", "invoice");
    WorkflowTaskHandlers.addHandler(
            APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER, appConnectActionHandler);
    Mockito.when(externalTask.getAllVariables()).thenReturn(parametersSchema);
    Mockito.when(externalTask.getProcessDefinitionKey()).thenReturn("customReminder");
    Mockito.when(externalTask.getActivityId()).thenReturn("sendPushNotification");
    Mockito.when(appConnectActionHandler.executeAction(Mockito.any())).thenReturn(new HashMap<>());
    Map<String, Object> taskExecutor = workerExecutor.executeTask(externalTask);
    Assert.assertEquals(true, taskExecutor.isEmpty());
  }

  @Test
  public void testExtensionProps() {
    Map<String, String> extensionProps = new HashMap<>();
    Map<String, String> parametersSchema = new HashMap<>();
    extensionProps.put("test", "11");
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    parametersSchema.put("handlerId", "test");

    extensionProps.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),ObjectConverter.toJson(parametersSchema));
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);

    Mockito.when(externalTask.getAllVariables()).thenReturn(new HashMap<>());
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    Mockito.when(externalTask.getExtensionProperties()).thenReturn(extensionProps);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE));

    Map<String, Object> taskExecutor = workerExecutor.executeTask(externalTask);
//  Ensure No Exception is thrown
    Assert.assertEquals(Boolean.TRUE, taskExecutor.get(WorkFlowVariables.PENDING_EXTEND_LOCK.getName()));
  }

  @Test
  public void testExtendLock_ProcessVariableUpdate_Success() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);

    Map<String, Object> response = new HashMap<>();
    response.put(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    response.put("txnId", "txn1");

    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(response);

    Mockito.doNothing()
        .when(camundaRunTimeServiceRest).updateExecutionVariables(Mockito.any());

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(camundaRunTimeServiceRest, Mockito.times(1))
        .updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));
    Mockito.verify(externalTaskService, Mockito.times(1))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
  }

  @Test
  public void testExtendLock_ExternalTaskVariableUpdate_ThrowException() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);

    Map<String, Object> response = new HashMap<>();
    response.put(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    response.put("txnId", "txn1");

    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(response);

    Mockito.doThrow(new RuntimeException(""))
        .when(camundaRunTimeServiceRest).updateExecutionVariables(Mockito.any());

    Mockito.doNothing().when(externalTaskService).handleFailure(
        Mockito.any(ExternalTask.class), Mockito.any(), Mockito.any(), Mockito.anyInt(),
        Mockito.anyLong());

    workerExecutor.execute(externalTask, externalTaskService, worker);
    Mockito.verify(camundaRunTimeServiceRest, Mockito.times(1))
        .updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));
    Mockito.verify(externalTaskService, Mockito.times(1))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
  }

  @Test
  public void testExtendLock_ExternalTaskVariableUpdate_NoResponse() {
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("taskHandler", "was");
    parametersSchema.put("actionName", "publishEvent");
    Map<String, Object> variable = new HashMap<>();
    variable.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(), ObjectConverter.toJson(parametersSchema));
    Mockito.when(externalTask.getAllVariables()).thenReturn(variable);
    Mockito.when(externalTask.getProcessInstanceId()).thenReturn("pId");
    WorkflowTaskHandlers.addHandler(WAS_PUBLISH_EVENT_HANDLER, publishEventHandler);

    Map<String, Object> response = new HashMap<>();
    response.put(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE);
    Mockito.when(publishEventHandler.execute(Mockito.any()))
        .thenReturn(response);

    Mockito.doNothing().when(externalTaskService)
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());

    workerExecutor.execute(externalTask, externalTaskService, worker);

    Mockito.verify(camundaRunTimeServiceRest, Mockito.never())
        .updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

    Mockito.verify(externalTaskService, Mockito.times(1))
        .extendLock(Mockito.any(ExternalTask.class), Mockito.anyLong());
  }

}
