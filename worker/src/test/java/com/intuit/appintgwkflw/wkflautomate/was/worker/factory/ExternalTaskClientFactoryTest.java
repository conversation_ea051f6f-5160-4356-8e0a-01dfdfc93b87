package com.intuit.appintgwkflw.wkflautomate.was.worker.factory;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.BackoffStrategyName;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Client;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import org.camunda.bpm.client.ExternalTaskClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskClientFactoryTest {

  @Mock private Client client;

  @Mock private Worker worker;

  @Mock private OfflineTicketClient offlineTicketClient;

  @Mock private WASContextHandler contextHandler;

  private static String END_POINT = "http://localhost:8080/rest";

  private static final long TIME = 2l;

  @Before
  public void setUp() {
    doReturn(1).when(worker).getMaxTasks();
    doReturn(BackoffStrategyName.ERROR_EXPONENTIAL).when(client).getBackoffStrategyName();
    doReturn(10).when(client).getSlidingWindow();
    worker.setTopicName("test-topic");
    worker.setWorkerId("test-topic-id");
    worker.setLockDuration(2000l);
  }

  @Test
  public void whenCreateClient_thenSuccess() {

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client, worker, offlineTicketClient, END_POINT, contextHandler);

    assertTrue(externalTaskClient.isActive());
  }

  @Test
  public void whenCreateClient_withBackoffDisabled_thenSuccess() {

    doReturn(true).when(client).getBackOffDisable();
    doReturn(100L).when(worker).getAsyncResponseTimeout();

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client, worker, offlineTicketClient, END_POINT, contextHandler);

    assertTrue(externalTaskClient.isActive());
    verify(worker, times(2)).getAsyncResponseTimeout();
  }

  @Test
  public void testWorkerWithExtendedLocks() {

    doReturn(true).when(client).getBackOffDisable();
    doReturn(100L).when(worker).getAsyncResponseTimeout();
    doReturn("test").when(worker).getWorkerId();

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client, worker, offlineTicketClient, END_POINT, contextHandler);

    assertTrue(externalTaskClient.isActive());
    verify(worker, times(2)).getWorkerId();
  }

  @Test
  public void testWorkerWithNullWorkerId() {

    doReturn(true).when(client).getBackOffDisable();
    doReturn(100L).when(worker).getAsyncResponseTimeout();
    doReturn(null).when(worker).getWorkerId();

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client, worker, offlineTicketClient, END_POINT, contextHandler);

    assertTrue(externalTaskClient.isActive());
  }

  @Test
  public void testWorkerWithEmptyWorkerId() {

    doReturn(true).when(client).getBackOffDisable();
    doReturn(100L).when(worker).getAsyncResponseTimeout();
    doReturn("").when(worker).getWorkerId();

    final ExternalTaskClient externalTaskClient =
        ExternalTaskClientFactory.createExternalTaskClient(
            client, worker, offlineTicketClient, END_POINT, contextHandler);

    assertTrue(externalTaskClient.isActive());
  }

  @Test
  public void testWorkerClass() {

    Worker worker = new Worker();
    worker.setAsyncResponseTimeout(TIME);
    worker.setExtendedLockDuration(TIME);

    assertEquals(worker.getAsyncResponseTimeout(), TIME);
    assertEquals(worker.getExtendedLockDuration(), TIME);
  }
}
