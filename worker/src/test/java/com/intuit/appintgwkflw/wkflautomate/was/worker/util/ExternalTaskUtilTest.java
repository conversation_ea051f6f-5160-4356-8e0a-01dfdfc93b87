package com.intuit.appintgwkflw.wkflautomate.was.worker.util;

import org.camunda.bpm.client.task.ExternalTask;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskUtilTest {

    @Test
    public void testGetEntityIdWithValidId() {
        ExternalTask externalTask = mock(ExternalTask.class);
        Map<String, Object> variables = new HashMap<>();
        variables.put("Id", "12345");
        when(externalTask.getAllVariables()).thenReturn(variables);
        String entityId = ExternalTaskUtil.getEntityId(externalTask);
        Assert.assertEquals("12345", entityId);
    }

    @Test
    public void testGetEntityIdWithNullVariables() {
        ExternalTask externalTask = mock(ExternalTask.class);
        when(externalTask.getAllVariables()).thenReturn(null);
        String entityId = ExternalTaskUtil.getEntityId(externalTask);
        Assert.assertNull(entityId);
    }

    @Test
    public void testGetEntityIdWithEmptyVariables() {
        ExternalTask externalTask = mock(ExternalTask.class);
        Map<String, Object> variables = new HashMap<>();
        when(externalTask.getAllVariables()).thenReturn(variables);
        String entityId = ExternalTaskUtil.getEntityId(externalTask);
        Assert.assertNull(entityId);
    }

    @Test
    public void testGetEntityIdWithMissingId() {
        ExternalTask externalTask = mock(ExternalTask.class);
        Map<String, Object> variables = new HashMap<>();
        variables.put("SomeOtherKey", "value");
        when(externalTask.getAllVariables()).thenReturn(variables);
        String entityId = ExternalTaskUtil.getEntityId(externalTask);
        Assert.assertNull(entityId);
    }
}